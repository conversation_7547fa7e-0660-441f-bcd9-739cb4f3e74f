<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto Polyfill Test</title>
</head>
<body>
    <h1>Crypto Polyfill Test</h1>
    <div id="results"></div>
    
    <script>
        const results = document.getElementById('results');
        
        function addResult(test, result, details = '') {
            const div = document.createElement('div');
            div.innerHTML = `<strong>${test}:</strong> ${result ? '✅ PASS' : '❌ FAIL'} ${details}`;
            div.style.margin = '10px 0';
            div.style.color = result ? 'green' : 'red';
            results.appendChild(div);
        }
        
        // Test 1: Check if crypto object exists
        addResult('crypto object exists', typeof crypto !== 'undefined', `(type: ${typeof crypto})`);
        
        // Test 2: Check if crypto.randomUUID exists
        addResult('crypto.randomUUID exists', typeof crypto?.randomUUID === 'function', `(type: ${typeof crypto?.randomUUID})`);
        
        // Test 3: Try to call crypto.randomUUID()
        try {
            const uuid = crypto.randomUUID();
            addResult('crypto.randomUUID() call', true, `Generated: ${uuid}`);
            
            // Test 4: Validate UUID format
            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
            addResult('UUID format valid', uuidRegex.test(uuid), `Pattern: ${uuid}`);
            
        } catch (error) {
            addResult('crypto.randomUUID() call', false, `Error: ${error.message}`);
        }
        
        // Test 5: Check globalThis.crypto
        addResult('globalThis.crypto exists', typeof globalThis?.crypto !== 'undefined');
        addResult('globalThis.crypto.randomUUID exists', typeof globalThis?.crypto?.randomUUID === 'function');
        
        // Test 6: Check window.crypto
        if (typeof window !== 'undefined') {
            addResult('window.crypto exists', typeof window.crypto !== 'undefined');
            addResult('window.crypto.randomUUID exists', typeof window.crypto?.randomUUID === 'function');
        }
    </script>
</body>
</html>
