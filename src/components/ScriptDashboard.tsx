import { useState } from "react";
import * as React from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";
import { Badge } from "./ui/badge";
import { ScrollArea } from "./ui/scroll-area";
import {
  Plus,
  Search,
  Grid3X3,
  List,
  Film,
  Users,
  Clock,
  Crown,
  Eye,
  Edit,
  Filter,
  SortAsc,
  SortDesc,
  Calendar
} from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "./ui/dialog";
import { Textarea } from "./ui/textarea";
import { useToast } from "../hooks/use-toast";
import { Id } from "../../convex/_generated/dataModel";
import { ScriptCard } from "./ScriptCard";

interface ScriptDashboardProps {
  onSelectScript: (scriptId: Id<"scripts">) => void;
  onScriptCountChange?: (count: number) => void;
}

type ViewMode = "grid" | "list";
type SortOption = "recent" | "title" | "oldest";

export function ScriptDashboard({ onSelectScript, onScriptCountChange }: ScriptDashboardProps) {
  const scripts = useQuery(api.scripts.getUserScripts);
  const canCreateResult = useQuery(api.scripts.canCreateScripts);
  const createScript = useMutation(api.scripts.createScript);
  const { toast } = useToast();

  // UI State
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<SortOption>("recent");
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  // Create Script Form State
  const [newScriptTitle, setNewScriptTitle] = useState("");
  const [newScriptDescription, setNewScriptDescription] = useState("");

  // Extract canCreate boolean for backward compatibility
  const canCreate = canCreateResult?.canCreate ?? false;

  // Update script count when scripts change
  React.useEffect(() => {
    if (scripts && onScriptCountChange) {
      onScriptCountChange(scripts.length);
    }
  }, [scripts, onScriptCountChange]);

  // Filter and sort scripts
  const filteredAndSortedScripts = React.useMemo(() => {
    if (!scripts) return [];

    let filtered = scripts;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = scripts.filter(
        (script) =>
          script.title.toLowerCase().includes(query) ||
          script.description?.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case "title":
          return a.title.localeCompare(b.title);
        case "oldest":
          return a._creationTime - b._creationTime;
        case "recent":
        default:
          return b._creationTime - a._creationTime;
      }
    });

    return sorted;
  }, [scripts, searchQuery, sortBy]);

  const handleCreateScript = async () => {
    if (!newScriptTitle.trim()) {
      toast({
        title: "Error",
        description: "Please enter a script title",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);
    try {
      const scriptId = await createScript({
        title: newScriptTitle.trim(),
        description: newScriptDescription.trim() || undefined,
      });

      toast({
        title: "Success",
        description: "Script created successfully",
      });

      // Reset form
      setNewScriptTitle("");
      setNewScriptDescription("");
      setShowCreateDialog(false);

      // Navigate to the new script
      onSelectScript(scriptId);
    } catch (error) {
      console.error("Failed to create script:", error);
      toast({
        title: "Error",
        description: "Failed to create script. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleShowCreateDialog = () => {
    setNewScriptTitle("");
    setNewScriptDescription("");
    setShowCreateDialog(true);
  };

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case "owner":
        return <Crown className="h-3 w-3" />;
      case "write":
        return <Edit className="h-3 w-3" />;
      case "read":
        return <Eye className="h-3 w-3" />;
      default:
        return <Eye className="h-3 w-3" />;
    }
  };

  const getPermissionColor = (permission: string) => {
    switch (permission) {
      case "owner":
        return "default";
      case "write":
        return "secondary";
      case "read":
        return "outline";
      default:
        return "outline";
    }
  };

  if (!scripts) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col bg-gray-50 h-full">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-xl font-bold text-gray-900">Your Scripts</h1>
            <p className="text-gray-600 text-sm">
              {scripts.length === 0
                ? "Create your first script to get started"
                : `${scripts.length} script${scripts.length === 1 ? "" : "s"} available`}
            </p>
          </div>
          {canCreate && (
            <Button
              onClick={handleShowCreateDialog}
              disabled={isCreating}
              className="gap-2 bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="h-4 w-4" />
              New Script
            </Button>
          )}
        </div>

        {/* Search and Controls */}
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search scripts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <div className="flex items-center gap-2 justify-between sm:justify-start">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSortBy(sortBy === "recent" ? "oldest" : "recent")}
              className="gap-2 flex-1 sm:flex-none"
            >
              {sortBy === "recent" ? <SortDesc className="h-4 w-4" /> : <SortAsc className="h-4 w-4" />}
              <span className="hidden sm:inline">{sortBy === "recent" ? "Newest" : "Oldest"}</span>
            </Button>

            <div className="flex border rounded-md">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="rounded-r-none"
                title="Grid view"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="rounded-l-none"
                title="List view"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <ScrollArea className="flex-1">
        <div className="p-6">
          {!canCreate && (
            <Card className="border-amber-200 bg-amber-50 mb-6">
              <CardContent className="p-4">
                <div className="flex items-start gap-3 text-amber-800">
                  <Film className="h-5 w-5 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium">Limited Access</p>
                    <p className="text-sm">
                      {canCreateResult?.reason === "anonymous_user"
                        ? "Anonymous users can only view shared scripts."
                        : canCreateResult?.reason === "not_authenticated"
                          ? "Please sign in to create and manage scripts."
                          : "You can view shared scripts but cannot create new ones."
                      }
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {filteredAndSortedScripts.length === 0 ? (
            <div className="text-center py-16">
              {searchQuery ? (
                <div className="text-gray-500">
                  <Search size={48} className="mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-medium mb-2">No scripts found</h3>
                  <p className="text-sm">Try adjusting your search terms</p>
                </div>
              ) : (
                <div className="text-gray-500">
                  <Film size={64} className="mx-auto mb-6 opacity-50" />
                  <h3 className="text-xl font-semibold mb-3 text-gray-900">No scripts yet</h3>
                  <p className="text-gray-600 mb-6">
                    {canCreate
                      ? "Create your first script to get started with collaborative screenwriting"
                      : "You don't have access to any scripts yet"}
                  </p>
                  {canCreate && (
                    <Button onClick={handleShowCreateDialog} className="gap-2">
                      <Plus className="h-4 w-4" />
                      Create Your First Script
                    </Button>
                  )}
                </div>
              )}
            </div>
          ) : (
            <div
              className={
                viewMode === "grid"
                  ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 sm:gap-6"
                  : "space-y-3 sm:space-y-4"
              }
            >
              {filteredAndSortedScripts.map((script) => (
                <ScriptCard
                  key={script._id}
                  script={script}
                  viewMode={viewMode}
                  onSelect={() => onSelectScript(script._id)}
                  getPermissionIcon={getPermissionIcon}
                  getPermissionColor={getPermissionColor}
                />
              ))}
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Create Script Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Script</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label htmlFor="script-title" className="text-sm font-medium">
                Title *
              </label>
              <Input
                id="script-title"
                value={newScriptTitle}
                onChange={(e) => setNewScriptTitle(e.target.value)}
                placeholder="Enter script title..."
                className="mt-1"
              />
            </div>
            <div>
              <label htmlFor="script-description" className="text-sm font-medium">
                Description
              </label>
              <Textarea
                id="script-description"
                value={newScriptDescription}
                onChange={(e) => setNewScriptDescription(e.target.value)}
                placeholder="Enter script description..."
                className="mt-1"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
              disabled={isCreating}
            >
              Cancel
            </Button>
            <Button onClick={handleCreateScript} disabled={isCreating}>
              {isCreating ? "Creating..." : "Create Script"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
