{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/rehype-remark/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast-util-to-mdast').Options} Options\n */\n\nexport {default} from './lib/index.js'\n"], "names": [], "mappings": "AAAA;;CAEC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40ungap/structured-clone/esm/types.js"], "sourcesContent": ["export const VOID       = -1;\nexport const PRIMITIVE  = 0;\nexport const ARRAY      = 1;\nexport const OBJECT     = 2;\nexport const DATE       = 3;\nexport const REGEXP     = 4;\nexport const MAP        = 5;\nexport const SET        = 6;\nexport const ERROR      = 7;\nexport const BIGINT     = 8;\n// export const SYMBOL = 9;\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAO,MAAM,OAAa,CAAC;AACpB,MAAM,YAAa;AACnB,MAAM,QAAa;AACnB,MAAM,SAAa;AACnB,MAAM,OAAa;AACnB,MAAM,SAAa;AACnB,MAAM,MAAa;AACnB,MAAM,MAAa;AACnB,MAAM,QAAa;AACnB,MAAM,SAAa,GAC1B,2BAA2B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40ungap/structured-clone/esm/deserialize.js"], "sourcesContent": ["import {\n  VOID, PRIMITIVE,\n  ARRAY, OBJECT,\n  DATE, REGEXP, MAP, SET,\n  ERROR, BIGINT\n} from './types.js';\n\nconst env = typeof self === 'object' ? self : globalThis;\n\nconst deserializer = ($, _) => {\n  const as = (out, index) => {\n    $.set(index, out);\n    return out;\n  };\n\n  const unpair = index => {\n    if ($.has(index))\n      return $.get(index);\n\n    const [type, value] = _[index];\n    switch (type) {\n      case PRIMITIVE:\n      case VOID:\n        return as(value, index);\n      case ARRAY: {\n        const arr = as([], index);\n        for (const index of value)\n          arr.push(unpair(index));\n        return arr;\n      }\n      case OBJECT: {\n        const object = as({}, index);\n        for (const [key, index] of value)\n          object[unpair(key)] = unpair(index);\n        return object;\n      }\n      case DATE:\n        return as(new Date(value), index);\n      case REGEXP: {\n        const {source, flags} = value;\n        return as(new RegExp(source, flags), index);\n      }\n      case MAP: {\n        const map = as(new Map, index);\n        for (const [key, index] of value)\n          map.set(unpair(key), unpair(index));\n        return map;\n      }\n      case SET: {\n        const set = as(new Set, index);\n        for (const index of value)\n          set.add(unpair(index));\n        return set;\n      }\n      case ERROR: {\n        const {name, message} = value;\n        return as(new env[name](message), index);\n      }\n      case BIGINT:\n        return as(BigInt(value), index);\n      case 'BigInt':\n        return as(Object(BigInt(value)), index);\n      case 'ArrayBuffer':\n        return as(new Uint8Array(value).buffer, value);\n      case 'DataView': {\n        const { buffer } = new Uint8Array(value);\n        return as(new DataView(buffer), value);\n      }\n    }\n    return as(new env[type](value), index);\n  };\n\n  return unpair;\n};\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns a deserialized value from a serialized array of Records.\n * @param {Record[]} serialized a previously serialized value.\n * @returns {any}\n */\nexport const deserialize = serialized => deserializer(new Map, serialized)(0);\n"], "names": [], "mappings": ";;;AAAA;;AAOA,MAAM,MAAM,OAAO,SAAS,WAAW,OAAO;AAE9C,MAAM,eAAe,CAAC,GAAG;IACvB,MAAM,KAAK,CAAC,KAAK;QACf,EAAE,GAAG,CAAC,OAAO;QACb,OAAO;IACT;IAEA,MAAM,SAAS,CAAA;QACb,IAAI,EAAE,GAAG,CAAC,QACR,OAAO,EAAE,GAAG,CAAC;QAEf,MAAM,CAAC,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM;QAC9B,OAAQ;YACN,KAAK,8JAAA,CAAA,YAAS;YACd,KAAK,8JAAA,CAAA,OAAI;gBACP,OAAO,GAAG,OAAO;YACnB,KAAK,8JAAA,CAAA,QAAK;gBAAE;oBACV,MAAM,MAAM,GAAG,EAAE,EAAE;oBACnB,KAAK,MAAM,SAAS,MAClB,IAAI,IAAI,CAAC,OAAO;oBAClB,OAAO;gBACT;YACA,KAAK,8JAAA,CAAA,SAAM;gBAAE;oBACX,MAAM,SAAS,GAAG,CAAC,GAAG;oBACtB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,MACzB,MAAM,CAAC,OAAO,KAAK,GAAG,OAAO;oBAC/B,OAAO;gBACT;YACA,KAAK,8JAAA,CAAA,OAAI;gBACP,OAAO,GAAG,IAAI,KAAK,QAAQ;YAC7B,KAAK,8JAAA,CAAA,SAAM;gBAAE;oBACX,MAAM,EAAC,MAAM,EAAE,KAAK,EAAC,GAAG;oBACxB,OAAO,GAAG,IAAI,OAAO,QAAQ,QAAQ;gBACvC;YACA,KAAK,8JAAA,CAAA,MAAG;gBAAE;oBACR,MAAM,MAAM,GAAG,IAAI,KAAK;oBACxB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,MACzB,IAAI,GAAG,CAAC,OAAO,MAAM,OAAO;oBAC9B,OAAO;gBACT;YACA,KAAK,8JAAA,CAAA,MAAG;gBAAE;oBACR,MAAM,MAAM,GAAG,IAAI,KAAK;oBACxB,KAAK,MAAM,SAAS,MAClB,IAAI,GAAG,CAAC,OAAO;oBACjB,OAAO;gBACT;YACA,KAAK,8JAAA,CAAA,QAAK;gBAAE;oBACV,MAAM,EAAC,IAAI,EAAE,OAAO,EAAC,GAAG;oBACxB,OAAO,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,UAAU;gBACpC;YACA,KAAK,8JAAA,CAAA,SAAM;gBACT,OAAO,GAAG,OAAO,QAAQ;YAC3B,KAAK;gBACH,OAAO,GAAG,OAAO,OAAO,SAAS;YACnC,KAAK;gBACH,OAAO,GAAG,IAAI,WAAW,OAAO,MAAM,EAAE;YAC1C,KAAK;gBAAY;oBACf,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,WAAW;oBAClC,OAAO,GAAG,IAAI,SAAS,SAAS;gBAClC;QACF;QACA,OAAO,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,QAAQ;IAClC;IAEA,OAAO;AACT;AAWO,MAAM,cAAc,CAAA,aAAc,aAAa,IAAI,KAAK,YAAY", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40ungap/structured-clone/esm/serialize.js"], "sourcesContent": ["import {\n  VOID, PRIMITIVE,\n  ARRAY, OBJECT,\n  DATE, REGEXP, MAP, SET,\n  ERROR, BIGINT\n} from './types.js';\n\nconst EMPTY = '';\n\nconst {toString} = {};\nconst {keys} = Object;\n\nconst typeOf = value => {\n  const type = typeof value;\n  if (type !== 'object' || !value)\n    return [PRIMITIVE, type];\n\n  const asString = toString.call(value).slice(8, -1);\n  switch (asString) {\n    case 'Array':\n      return [ARRAY, EMPTY];\n    case 'Object':\n      return [OBJECT, EMPTY];\n    case 'Date':\n      return [DATE, EMPTY];\n    case 'RegExp':\n      return [REGEXP, EMPTY];\n    case 'Map':\n      return [MAP, EMPTY];\n    case 'Set':\n      return [SET, EMPTY];\n    case 'DataView':\n      return [ARRAY, asString];\n  }\n\n  if (asString.includes('Array'))\n    return [ARRAY, asString];\n\n  if (asString.includes('Error'))\n    return [ERROR, asString];\n\n  return [OBJECT, asString];\n};\n\nconst shouldSkip = ([TYPE, type]) => (\n  TYPE === PRIMITIVE &&\n  (type === 'function' || type === 'symbol')\n);\n\nconst serializer = (strict, json, $, _) => {\n\n  const as = (out, value) => {\n    const index = _.push(out) - 1;\n    $.set(value, index);\n    return index;\n  };\n\n  const pair = value => {\n    if ($.has(value))\n      return $.get(value);\n\n    let [TYPE, type] = typeOf(value);\n    switch (TYPE) {\n      case PRIMITIVE: {\n        let entry = value;\n        switch (type) {\n          case 'bigint':\n            TYPE = BIGINT;\n            entry = value.toString();\n            break;\n          case 'function':\n          case 'symbol':\n            if (strict)\n              throw new TypeError('unable to serialize ' + type);\n            entry = null;\n            break;\n          case 'undefined':\n            return as([VOID], value);\n        }\n        return as([TYPE, entry], value);\n      }\n      case ARRAY: {\n        if (type) {\n          let spread = value;\n          if (type === 'DataView') {\n            spread = new Uint8Array(value.buffer);\n          }\n          else if (type === 'ArrayBuffer') {\n            spread = new Uint8Array(value);\n          }\n          return as([type, [...spread]], value);\n        }\n\n        const arr = [];\n        const index = as([TYPE, arr], value);\n        for (const entry of value)\n          arr.push(pair(entry));\n        return index;\n      }\n      case OBJECT: {\n        if (type) {\n          switch (type) {\n            case 'BigInt':\n              return as([type, value.toString()], value);\n            case 'Boolean':\n            case 'Number':\n            case 'String':\n              return as([type, value.valueOf()], value);\n          }\n        }\n\n        if (json && ('toJSON' in value))\n          return pair(value.toJSON());\n\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const key of keys(value)) {\n          if (strict || !shouldSkip(typeOf(value[key])))\n            entries.push([pair(key), pair(value[key])]);\n        }\n        return index;\n      }\n      case DATE:\n        return as([TYPE, value.toISOString()], value);\n      case REGEXP: {\n        const {source, flags} = value;\n        return as([TYPE, {source, flags}], value);\n      }\n      case MAP: {\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const [key, entry] of value) {\n          if (strict || !(shouldSkip(typeOf(key)) || shouldSkip(typeOf(entry))))\n            entries.push([pair(key), pair(entry)]);\n        }\n        return index;\n      }\n      case SET: {\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const entry of value) {\n          if (strict || !shouldSkip(typeOf(entry)))\n            entries.push(pair(entry));\n        }\n        return index;\n      }\n    }\n\n    const {message} = value;\n    return as([TYPE, {name: type, message}], value);\n  };\n\n  return pair;\n};\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns an array of serialized Records.\n * @param {any} value a serializable value.\n * @param {{json?: boolean, lossy?: boolean}?} options an object with a `lossy` or `json` property that,\n *  if `true`, will not throw errors on incompatible types, and behave more\n *  like JSON stringify would behave. Symbol and Function will be discarded.\n * @returns {Record[]}\n */\n export const serialize = (value, {json, lossy} = {}) => {\n  const _ = [];\n  return serializer(!(json || lossy), !!json, new Map, _)(value), _;\n};\n"], "names": [], "mappings": ";;;AAAA;;AAOA,MAAM,QAAQ;AAEd,MAAM,EAAC,QAAQ,EAAC,GAAG,CAAC;AACpB,MAAM,EAAC,IAAI,EAAC,GAAG;AAEf,MAAM,SAAS,CAAA;IACb,MAAM,OAAO,OAAO;IACpB,IAAI,SAAS,YAAY,CAAC,OACxB,OAAO;QAAC,8JAAA,CAAA,YAAS;QAAE;KAAK;IAE1B,MAAM,WAAW,SAAS,IAAI,CAAC,OAAO,KAAK,CAAC,GAAG,CAAC;IAChD,OAAQ;QACN,KAAK;YACH,OAAO;gBAAC,8JAAA,CAAA,QAAK;gBAAE;aAAM;QACvB,KAAK;YACH,OAAO;gBAAC,8JAAA,CAAA,SAAM;gBAAE;aAAM;QACxB,KAAK;YACH,OAAO;gBAAC,8JAAA,CAAA,OAAI;gBAAE;aAAM;QACtB,KAAK;YACH,OAAO;gBAAC,8JAAA,CAAA,SAAM;gBAAE;aAAM;QACxB,KAAK;YACH,OAAO;gBAAC,8JAAA,CAAA,MAAG;gBAAE;aAAM;QACrB,KAAK;YACH,OAAO;gBAAC,8JAAA,CAAA,MAAG;gBAAE;aAAM;QACrB,KAAK;YACH,OAAO;gBAAC,8JAAA,CAAA,QAAK;gBAAE;aAAS;IAC5B;IAEA,IAAI,SAAS,QAAQ,CAAC,UACpB,OAAO;QAAC,8JAAA,CAAA,QAAK;QAAE;KAAS;IAE1B,IAAI,SAAS,QAAQ,CAAC,UACpB,OAAO;QAAC,8JAAA,CAAA,QAAK;QAAE;KAAS;IAE1B,OAAO;QAAC,8JAAA,CAAA,SAAM;QAAE;KAAS;AAC3B;AAEA,MAAM,aAAa,CAAC,CAAC,MAAM,KAAK,GAC9B,SAAS,8JAAA,CAAA,YAAS,IAClB,CAAC,SAAS,cAAc,SAAS,QAAQ;AAG3C,MAAM,aAAa,CAAC,QAAQ,MAAM,GAAG;IAEnC,MAAM,KAAK,CAAC,KAAK;QACf,MAAM,QAAQ,EAAE,IAAI,CAAC,OAAO;QAC5B,EAAE,GAAG,CAAC,OAAO;QACb,OAAO;IACT;IAEA,MAAM,OAAO,CAAA;QACX,IAAI,EAAE,GAAG,CAAC,QACR,OAAO,EAAE,GAAG,CAAC;QAEf,IAAI,CAAC,MAAM,KAAK,GAAG,OAAO;QAC1B,OAAQ;YACN,KAAK,8JAAA,CAAA,YAAS;gBAAE;oBACd,IAAI,QAAQ;oBACZ,OAAQ;wBACN,KAAK;4BACH,OAAO,8JAAA,CAAA,SAAM;4BACb,QAAQ,MAAM,QAAQ;4BACtB;wBACF,KAAK;wBACL,KAAK;4BACH,IAAI,QACF,MAAM,IAAI,UAAU,yBAAyB;4BAC/C,QAAQ;4BACR;wBACF,KAAK;4BACH,OAAO,GAAG;gCAAC,8JAAA,CAAA,OAAI;6BAAC,EAAE;oBACtB;oBACA,OAAO,GAAG;wBAAC;wBAAM;qBAAM,EAAE;gBAC3B;YACA,KAAK,8JAAA,CAAA,QAAK;gBAAE;oBACV,IAAI,MAAM;wBACR,IAAI,SAAS;wBACb,IAAI,SAAS,YAAY;4BACvB,SAAS,IAAI,WAAW,MAAM,MAAM;wBACtC,OACK,IAAI,SAAS,eAAe;4BAC/B,SAAS,IAAI,WAAW;wBAC1B;wBACA,OAAO,GAAG;4BAAC;4BAAM;mCAAI;6BAAO;yBAAC,EAAE;oBACjC;oBAEA,MAAM,MAAM,EAAE;oBACd,MAAM,QAAQ,GAAG;wBAAC;wBAAM;qBAAI,EAAE;oBAC9B,KAAK,MAAM,SAAS,MAClB,IAAI,IAAI,CAAC,KAAK;oBAChB,OAAO;gBACT;YACA,KAAK,8JAAA,CAAA,SAAM;gBAAE;oBACX,IAAI,MAAM;wBACR,OAAQ;4BACN,KAAK;gCACH,OAAO,GAAG;oCAAC;oCAAM,MAAM,QAAQ;iCAAG,EAAE;4BACtC,KAAK;4BACL,KAAK;4BACL,KAAK;gCACH,OAAO,GAAG;oCAAC;oCAAM,MAAM,OAAO;iCAAG,EAAE;wBACvC;oBACF;oBAEA,IAAI,QAAS,YAAY,OACvB,OAAO,KAAK,MAAM,MAAM;oBAE1B,MAAM,UAAU,EAAE;oBAClB,MAAM,QAAQ,GAAG;wBAAC;wBAAM;qBAAQ,EAAE;oBAClC,KAAK,MAAM,OAAO,KAAK,OAAQ;wBAC7B,IAAI,UAAU,CAAC,WAAW,OAAO,KAAK,CAAC,IAAI,IACzC,QAAQ,IAAI,CAAC;4BAAC,KAAK;4BAAM,KAAK,KAAK,CAAC,IAAI;yBAAE;oBAC9C;oBACA,OAAO;gBACT;YACA,KAAK,8JAAA,CAAA,OAAI;gBACP,OAAO,GAAG;oBAAC;oBAAM,MAAM,WAAW;iBAAG,EAAE;YACzC,KAAK,8JAAA,CAAA,SAAM;gBAAE;oBACX,MAAM,EAAC,MAAM,EAAE,KAAK,EAAC,GAAG;oBACxB,OAAO,GAAG;wBAAC;wBAAM;4BAAC;4BAAQ;wBAAK;qBAAE,EAAE;gBACrC;YACA,KAAK,8JAAA,CAAA,MAAG;gBAAE;oBACR,MAAM,UAAU,EAAE;oBAClB,MAAM,QAAQ,GAAG;wBAAC;wBAAM;qBAAQ,EAAE;oBAClC,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,MAAO;wBAChC,IAAI,UAAU,CAAC,CAAC,WAAW,OAAO,SAAS,WAAW,OAAO,OAAO,GAClE,QAAQ,IAAI,CAAC;4BAAC,KAAK;4BAAM,KAAK;yBAAO;oBACzC;oBACA,OAAO;gBACT;YACA,KAAK,8JAAA,CAAA,MAAG;gBAAE;oBACR,MAAM,UAAU,EAAE;oBAClB,MAAM,QAAQ,GAAG;wBAAC;wBAAM;qBAAQ,EAAE;oBAClC,KAAK,MAAM,SAAS,MAAO;wBACzB,IAAI,UAAU,CAAC,WAAW,OAAO,SAC/B,QAAQ,IAAI,CAAC,KAAK;oBACtB;oBACA,OAAO;gBACT;QACF;QAEA,MAAM,EAAC,OAAO,EAAC,GAAG;QAClB,OAAO,GAAG;YAAC;YAAM;gBAAC,MAAM;gBAAM;YAAO;SAAE,EAAE;IAC3C;IAEA,OAAO;AACT;AAcQ,MAAM,YAAY,CAAC,OAAO,EAAC,IAAI,EAAE,KAAK,EAAC,GAAG,CAAC,CAAC;IAClD,MAAM,IAAI,EAAE;IACZ,OAAO,WAAW,CAAC,CAAC,QAAQ,KAAK,GAAG,CAAC,CAAC,MAAM,IAAI,KAAK,GAAG,QAAQ;AAClE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40ungap/structured-clone/esm/index.js"], "sourcesContent": ["import {deserialize} from './deserialize.js';\nimport {serialize} from './serialize.js';\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns an array of serialized Records.\n * @param {any} any a serializable value.\n * @param {{transfer?: any[], json?: boolean, lossy?: boolean}?} options an object with\n * a transfer option (ignored when polyfilled) and/or non standard fields that\n * fallback to the polyfill if present.\n * @returns {Record[]}\n */\nexport default typeof structuredClone === \"function\" ?\n  /* c8 ignore start */\n  (any, options) => (\n    options && ('json' in options || 'lossy' in options) ?\n      deserialize(serialize(any, options)) : structuredClone(any)\n  ) :\n  (any, options) => deserialize(serialize(any, options));\n  /* c8 ignore stop */\n\nexport {deserialize, serialize};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAce,OAAO,oBAAoB,aACxC,mBAAmB,GACnB,CAAC,KAAK,UACJ,WAAW,CAAC,UAAU,WAAW,WAAW,OAAO,IACjD,CAAA,GAAA,oKAAA,CAAA,cAAW,AAAD,EAAE,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE,KAAK,YAAY,gBAAgB,OAE3D,CAAC,KAAK,UAAY,CAAA,GAAA,oKAAA,CAAA,cAAW,AAAD,EAAE,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 365, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-is-element/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Parents} Parents\n */\n\n/**\n * @template Fn\n * @template Fallback\n * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate\n */\n\n/**\n * @callback Check\n *   Check that an arbitrary value is an element.\n * @param {unknown} this\n *   Context object (`this`) to call `test` with\n * @param {unknown} [element]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   Position of `element` in its parent.\n * @param {Parents | null | undefined} [parent]\n *   Parent of `element`.\n * @returns {boolean}\n *   Whether this is an element and passes a test.\n *\n * @typedef {Array<TestFunction | string> | TestFunction | string | null | undefined} Test\n *   Check for an arbitrary element.\n *\n *   * when `string`, checks that the element has that tag name\n *   * when `function`, see `TestFunction`\n *   * when `Array`, checks if one of the subtests pass\n *\n * @callback TestFunction\n *   Check if an element passes a test.\n * @param {unknown} this\n *   The given context.\n * @param {Element} element\n *   An element.\n * @param {number | undefined} [index]\n *   Position of `element` in its parent.\n * @param {Parents | undefined} [parent]\n *   Parent of `element`.\n * @returns {boolean | undefined | void}\n *   Whether this element passes the test.\n *\n *   Note: `void` is included until TS sees no return as `undefined`.\n */\n\n/**\n * Check if `element` is an `Element` and whether it passes the given test.\n *\n * @param element\n *   Thing to check, typically `element`.\n * @param test\n *   Check for a specific element.\n * @param index\n *   Position of `element` in its parent.\n * @param parent\n *   Parent of `element`.\n * @param context\n *   Context object (`this`) to call `test` with.\n * @returns\n *   Whether `element` is an `Element` and passes a test.\n * @throws\n *   When an incorrect `test`, `index`, or `parent` is given; there is no error\n *   thrown when `element` is not a node or not an element.\n */\nexport const isElement =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends TestFunction>(element: unknown, test: Condition, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & Predicate<Condition, Element>) &\n   *   (<Condition extends string>(element: unknown, test: Condition, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & {tagName: Condition}) &\n   *   ((element?: null | undefined) => false) &\n   *   ((element: unknown, test?: null | undefined, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element) &\n   *   ((element: unknown, test?: Test, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [element]\n     * @param {Test | undefined} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parents | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function (element, test, index, parent, context) {\n      const check = convertElement(test)\n\n      if (\n        index !== null &&\n        index !== undefined &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite `index`')\n      }\n\n      if (\n        parent !== null &&\n        parent !== undefined &&\n        (!parent.type || !parent.children)\n      ) {\n        throw new Error('Expected valid `parent`')\n      }\n\n      if (\n        (index === null || index === undefined) !==\n        (parent === null || parent === undefined)\n      ) {\n        throw new Error('Expected both `index` and `parent`')\n      }\n\n      return looksLikeAnElement(element)\n        ? check.call(context, element, index, parent)\n        : false\n    }\n  )\n\n/**\n * Generate a check from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * an `element`, `index`, and `parent`.\n *\n * @param test\n *   A test for a specific element.\n * @returns\n *   A check.\n */\nexport const convertElement =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends TestFunction>(test: Condition) => (element: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & Predicate<Condition, Element>) &\n   *   (<Condition extends string>(test: Condition) => (element: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & {tagName: Condition}) &\n   *   ((test?: null | undefined) => (element?: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element) &\n   *   ((test?: Test) => Check)\n   * )}\n   */\n  (\n    /**\n     * @param {Test | null | undefined} [test]\n     * @returns {Check}\n     */\n    function (test) {\n      if (test === null || test === undefined) {\n        return element\n      }\n\n      if (typeof test === 'string') {\n        return tagNameFactory(test)\n      }\n\n      // Assume array.\n      if (typeof test === 'object') {\n        return anyFactory(test)\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      throw new Error('Expected function, string, or array as `test`')\n    }\n  )\n\n/**\n * Handle multiple tests.\n *\n * @param {Array<TestFunction | string>} tests\n * @returns {Check}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<Check>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convertElement(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @type {TestFunction}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].apply(this, parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn a string into a test for an element with a certain type.\n *\n * @param {string} check\n * @returns {Check}\n */\nfunction tagNameFactory(check) {\n  return castFactory(tagName)\n\n  /**\n   * @param {Element} element\n   * @returns {boolean}\n   */\n  function tagName(element) {\n    return element.tagName === check\n  }\n}\n\n/**\n * Turn a custom test into a test for an element that passes that test.\n *\n * @param {TestFunction} testFunction\n * @returns {Check}\n */\nfunction castFactory(testFunction) {\n  return check\n\n  /**\n   * @this {unknown}\n   * @type {Check}\n   */\n  function check(value, index, parent) {\n    return Boolean(\n      looksLikeAnElement(value) &&\n        testFunction.call(\n          this,\n          value,\n          typeof index === 'number' ? index : undefined,\n          parent || undefined\n        )\n    )\n  }\n}\n\n/**\n * Make sure something is an element.\n *\n * @param {unknown} element\n * @returns {element is Element}\n */\nfunction element(element) {\n  return Boolean(\n    element &&\n      typeof element === 'object' &&\n      'type' in element &&\n      element.type === 'element' &&\n      'tagName' in element &&\n      typeof element.tagName === 'string'\n  )\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Element}\n */\nfunction looksLikeAnElement(value) {\n  return (\n    value !== null &&\n    typeof value === 'object' &&\n    'type' in value &&\n    'tagName' in value\n  )\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;CAIC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmCC,GAED;;;;;;;;;;;;;;;;;;CAkBC;;;;AACM,MAAM,YAYT;;;;;;;KAOC,GACD,sCAAsC;AACtC,SAAU,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO;IAC7C,MAAM,QAAQ,eAAe;IAE7B,IACE,UAAU,QACV,UAAU,aACV,CAAC,OAAO,UAAU,YAChB,QAAQ,KACR,UAAU,OAAO,iBAAiB,GACpC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,IACE,WAAW,QACX,WAAW,aACX,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,QAAQ,GACjC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,IACE,CAAC,UAAU,QAAQ,UAAU,SAAS,MACtC,CAAC,WAAW,QAAQ,WAAW,SAAS,GACxC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,mBAAmB,WACtB,MAAM,IAAI,CAAC,SAAS,SAAS,OAAO,UACpC;AACN;AAiBG,MAAM,iBAWT;;;KAGC,GACD,SAAU,IAAI;IACZ,IAAI,SAAS,QAAQ,SAAS,WAAW;QACvC,OAAO;IACT;IAEA,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,eAAe;IACxB;IAEA,gBAAgB;IAChB,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,WAAW;IACpB;IAEA,IAAI,OAAO,SAAS,YAAY;QAC9B,OAAO,YAAY;IACrB;IAEA,MAAM,IAAI,MAAM;AAClB;AAGJ;;;;;CAKC,GACD,SAAS,WAAW,KAAK;IACvB,yBAAyB,GACzB,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,MAAM,CAAC,MAAM,GAAG,eAAe,KAAK,CAAC,MAAM;IAC7C;IAEA,OAAO,YAAY;;IAEnB;;;GAGC,GACD,SAAS,IAAI,GAAG,UAAU;QACxB,IAAI,QAAQ,CAAC;QAEb,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;YAC9B,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,aAAa,OAAO;QACpD;QAEA,OAAO;IACT;AACF;AAEA;;;;;CAKC,GACD,SAAS,eAAe,KAAK;IAC3B,OAAO,YAAY;;IAEnB;;;GAGC,GACD,SAAS,QAAQ,OAAO;QACtB,OAAO,QAAQ,OAAO,KAAK;IAC7B;AACF;AAEA;;;;;CAKC,GACD,SAAS,YAAY,YAAY;IAC/B,OAAO;;IAEP;;;GAGC,GACD,SAAS,MAAM,KAAK,EAAE,KAAK,EAAE,MAAM;QACjC,OAAO,QACL,mBAAmB,UACjB,aAAa,IAAI,CACf,IAAI,EACJ,OACA,OAAO,UAAU,WAAW,QAAQ,WACpC,UAAU;IAGlB;AACF;AAEA;;;;;CAKC,GACD,SAAS,QAAQ,OAAO;IACtB,OAAO,QACL,WACE,OAAO,YAAY,YACnB,UAAU,WACV,QAAQ,IAAI,KAAK,aACjB,aAAa,WACb,OAAO,QAAQ,OAAO,KAAK;AAEjC;AAEA;;;CAGC,GACD,SAAS,mBAAmB,KAAK;IAC/B,OACE,UAAU,QACV,OAAO,UAAU,YACjB,UAAU,SACV,aAAa;AAEjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 543, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-embedded/lib/index.js"], "sourcesContent": ["import {convertElement} from 'hast-util-is-element'\n\n/**\n * Check if a node is a *embedded content*.\n *\n * @param value\n *   Thing to check (typically `Node`).\n * @returns\n *   Whether `value` is an element considered embedded content.\n *\n *   The elements `audio`, `canvas`, `embed`, `iframe`, `img`, `math`,\n *   `object`, `picture`, `svg`, and `video` are embedded content.\n */\nexport const embedded = convertElement(\n  /**\n   * @param element\n   * @returns {element is {tagName: 'audio' | 'canvas' | 'embed' | 'iframe' | 'img' | 'math' | 'object' | 'picture' | 'svg' | 'video'}}\n   */\n  function (element) {\n    return (\n      element.tagName === 'audio' ||\n      element.tagName === 'canvas' ||\n      element.tagName === 'embed' ||\n      element.tagName === 'iframe' ||\n      element.tagName === 'img' ||\n      element.tagName === 'math' ||\n      element.tagName === 'object' ||\n      element.tagName === 'picture' ||\n      element.tagName === 'svg' ||\n      element.tagName === 'video'\n    )\n  }\n)\n"], "names": [], "mappings": ";;;AAAA;;AAaO,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EACnC;;;GAGC,GACD,SAAU,OAAO;IACf,OACE,QAAQ,OAAO,KAAK,WACpB,QAAQ,OAAO,KAAK,YACpB,QAAQ,OAAO,KAAK,WACpB,QAAQ,OAAO,KAAK,YACpB,QAAQ,OAAO,KAAK,SACpB,QAAQ,OAAO,KAAK,UACpB,QAAQ,OAAO,KAAK,YACpB,QAAQ,OAAO,KAAK,aACpB,QAAQ,OAAO,KAAK,SACpB,QAAQ,OAAO,KAAK;AAExB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 560, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-whitespace/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Nodes} Nodes\n */\n\n// HTML whitespace expression.\n// See <https://infra.spec.whatwg.org/#ascii-whitespace>.\nconst re = /[ \\t\\n\\f\\r]/g\n\n/**\n * Check if the given value is *inter-element whitespace*.\n *\n * @param {Nodes | string} thing\n *   Thing to check (`Node` or `string`).\n * @returns {boolean}\n *   Whether the `value` is inter-element whitespace (`boolean`): consisting of\n *   zero or more of space, tab (`\\t`), line feed (`\\n`), carriage return\n *   (`\\r`), or form feed (`\\f`); if a node is passed it must be a `Text` node,\n *   whose `value` field is checked.\n */\nexport function whitespace(thing) {\n  return typeof thing === 'object'\n    ? thing.type === 'text'\n      ? empty(thing.value)\n      : false\n    : empty(thing)\n}\n\n/**\n * @param {string} value\n * @returns {boolean}\n */\nfunction empty(value) {\n  return value.replace(re, '') === ''\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,8BAA8B;AAC9B,yDAAyD;;;;AACzD,MAAM,KAAK;AAaJ,SAAS,WAAW,KAAK;IAC9B,OAAO,OAAO,UAAU,WACpB,MAAM,IAAI,KAAK,SACb,MAAM,MAAM,KAAK,IACjB,QACF,MAAM;AACZ;AAEA;;;CAGC,GACD,SAAS,MAAM,KAAK;IAClB,OAAO,MAAM,OAAO,CAAC,IAAI,QAAQ;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 583, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/unist-util-is/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n */\n\n/**\n * @template Fn\n * @template Fallback\n * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate\n */\n\n/**\n * @callback Check\n *   Check that an arbitrary value is a node.\n * @param {unknown} this\n *   The given context.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is a node and passes a test.\n *\n * @typedef {Record<string, unknown> | Node} Props\n *   Object to check for equivalence.\n *\n *   Note: `Node` is included as it is common but is not indexable.\n *\n * @typedef {Array<Props | TestFunction | string> | Props | TestFunction | string | null | undefined} Test\n *   Check for an arbitrary node.\n *\n * @callback TestFunction\n *   Check if a node passes a test.\n * @param {unknown} this\n *   The given context.\n * @param {Node} node\n *   A node.\n * @param {number | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean | undefined | void}\n *   Whether this node passes the test.\n *\n *   Note: `void` is included until TS sees no return as `undefined`.\n */\n\n/**\n * Check if `node` is a `Node` and whether it passes the given test.\n *\n * @param {unknown} node\n *   Thing to check, typically `Node`.\n * @param {Test} test\n *   A check for a specific node.\n * @param {number | null | undefined} index\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} parent\n *   The node’s parent.\n * @param {unknown} context\n *   Context object (`this`) to pass to `test` functions.\n * @returns {boolean}\n *   Whether `node` is a node and passes a test.\n */\nexport const is =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends string>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n   *   (<Condition extends Props>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n   *   (<Condition extends TestFunction>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n   *   ((node?: null | undefined) => false) &\n   *   ((node: unknown, test?: null | undefined, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n   *   ((node: unknown, test?: Test, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function (node, test, index, parent, context) {\n      const check = convert(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!is(parent) || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      return looksLikeANode(node)\n        ? check.call(context, node, index, parent)\n        : false\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param {Test} test\n *   *   when nullish, checks if `node` is a `Node`.\n *   *   when `string`, works like passing `(node) => node.type === test`.\n *   *   when `function` checks if function passed the node is true.\n *   *   when `object`, checks that all keys in test are in node, and that they have (strictly) equal values.\n *   *   when `array`, checks if any one of the subtests pass.\n * @returns {Check}\n *   An assertion.\n */\nexport const convert =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends string>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n   *   (<Condition extends Props>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n   *   (<Condition extends TestFunction>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n   *   ((test?: null | undefined) => (node?: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n   *   ((test?: Test) => Check)\n   * )}\n   */\n  (\n    /**\n     * @param {Test} [test]\n     * @returns {Check}\n     */\n    function (test) {\n      if (test === null || test === undefined) {\n        return ok\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return Array.isArray(test) ? anyFactory(test) : propsFactory(test)\n      }\n\n      if (typeof test === 'string') {\n        return typeFactory(test)\n      }\n\n      throw new Error('Expected function, string, or object as test')\n    }\n  )\n\n/**\n * @param {Array<Props | TestFunction | string>} tests\n * @returns {Check}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<Check>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convert(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @type {TestFunction}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].apply(this, parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn an object into a test for a node with a certain fields.\n *\n * @param {Props} check\n * @returns {Check}\n */\nfunction propsFactory(check) {\n  const checkAsRecord = /** @type {Record<string, unknown>} */ (check)\n\n  return castFactory(all)\n\n  /**\n   * @param {Node} node\n   * @returns {boolean}\n   */\n  function all(node) {\n    const nodeAsRecord = /** @type {Record<string, unknown>} */ (\n      /** @type {unknown} */ (node)\n    )\n\n    /** @type {string} */\n    let key\n\n    for (key in check) {\n      if (nodeAsRecord[key] !== checkAsRecord[key]) return false\n    }\n\n    return true\n  }\n}\n\n/**\n * Turn a string into a test for a node with a certain type.\n *\n * @param {string} check\n * @returns {Check}\n */\nfunction typeFactory(check) {\n  return castFactory(type)\n\n  /**\n   * @param {Node} node\n   */\n  function type(node) {\n    return node && node.type === check\n  }\n}\n\n/**\n * Turn a custom test into a test for a node that passes that test.\n *\n * @param {TestFunction} testFunction\n * @returns {Check}\n */\nfunction castFactory(testFunction) {\n  return check\n\n  /**\n   * @this {unknown}\n   * @type {Check}\n   */\n  function check(value, index, parent) {\n    return Boolean(\n      looksLikeANode(value) &&\n        testFunction.call(\n          this,\n          value,\n          typeof index === 'number' ? index : undefined,\n          parent || undefined\n        )\n    )\n  }\n}\n\nfunction ok() {\n  return true\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Node}\n */\nfunction looksLikeANode(value) {\n  return value !== null && typeof value === 'object' && 'type' in value\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;CAIC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoCC,GAED;;;;;;;;;;;;;;;CAeC;;;;AACM,MAAM,KAaT;;;;;;;KAOC,GACD,sCAAsC;AACtC,SAAU,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO;IAC1C,MAAM,QAAQ,QAAQ;IAEtB,IACE,UAAU,aACV,UAAU,QACV,CAAC,OAAO,UAAU,YAChB,QAAQ,KACR,UAAU,OAAO,iBAAiB,GACpC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,IACE,WAAW,aACX,WAAW,QACX,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,QAAQ,GAChC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,IACE,CAAC,WAAW,aAAa,WAAW,IAAI,MACxC,CAAC,UAAU,aAAa,UAAU,IAAI,GACtC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,eAAe,QAClB,MAAM,IAAI,CAAC,SAAS,MAAM,OAAO,UACjC;AACN;AAqBG,MAAM,UAYT;;;KAGC,GACD,SAAU,IAAI;IACZ,IAAI,SAAS,QAAQ,SAAS,WAAW;QACvC,OAAO;IACT;IAEA,IAAI,OAAO,SAAS,YAAY;QAC9B,OAAO,YAAY;IACrB;IAEA,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,MAAM,OAAO,CAAC,QAAQ,WAAW,QAAQ,aAAa;IAC/D;IAEA,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,YAAY;IACrB;IAEA,MAAM,IAAI,MAAM;AAClB;AAGJ;;;CAGC,GACD,SAAS,WAAW,KAAK;IACvB,yBAAyB,GACzB,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,MAAM,CAAC,MAAM,GAAG,QAAQ,KAAK,CAAC,MAAM;IACtC;IAEA,OAAO,YAAY;;IAEnB;;;GAGC,GACD,SAAS,IAAI,GAAG,UAAU;QACxB,IAAI,QAAQ,CAAC;QAEb,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;YAC9B,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,aAAa,OAAO;QACpD;QAEA,OAAO;IACT;AACF;AAEA;;;;;CAKC,GACD,SAAS,aAAa,KAAK;IACzB,MAAM,gBAAwD;IAE9D,OAAO,YAAY;;IAEnB;;;GAGC,GACD,SAAS,IAAI,IAAI;QACf,MAAM,eACoB;QAG1B,mBAAmB,GACnB,IAAI;QAEJ,IAAK,OAAO,MAAO;YACjB,IAAI,YAAY,CAAC,IAAI,KAAK,aAAa,CAAC,IAAI,EAAE,OAAO;QACvD;QAEA,OAAO;IACT;AACF;AAEA;;;;;CAKC,GACD,SAAS,YAAY,KAAK;IACxB,OAAO,YAAY;;IAEnB;;GAEC,GACD,SAAS,KAAK,IAAI;QAChB,OAAO,QAAQ,KAAK,IAAI,KAAK;IAC/B;AACF;AAEA;;;;;CAKC,GACD,SAAS,YAAY,YAAY;IAC/B,OAAO;;IAEP;;;GAGC,GACD,SAAS,MAAM,KAAK,EAAE,KAAK,EAAE,MAAM;QACjC,OAAO,QACL,eAAe,UACb,aAAa,IAAI,CACf,IAAI,EACJ,OACA,OAAO,UAAU,WAAW,QAAQ,WACpC,UAAU;IAGlB;AACF;AAEA,SAAS;IACP,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,eAAe,KAAK;IAC3B,OAAO,UAAU,QAAQ,OAAO,UAAU,YAAY,UAAU;AAClE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 771, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-minify-whitespace/lib/block.js"], "sourcesContent": ["// See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\nexport const blocks = [\n  'address', // Flow content.\n  'article', // Sections and headings.\n  'aside', // Sections and headings.\n  'blockquote', // Flow content.\n  'body', // Page.\n  'br', // Contribute whitespace intrinsically.\n  'caption', // Similar to block.\n  'center', // Flow content, legacy.\n  'col', // Similar to block.\n  'colgroup', // Similar to block.\n  'dd', // Lists.\n  'dialog', // Flow content.\n  'dir', // Lists, legacy.\n  'div', // Flow content.\n  'dl', // Lists.\n  'dt', // Lists.\n  'figcaption', // Flow content.\n  'figure', // Flow content.\n  'footer', // Flow content.\n  'form', // Flow content.\n  'h1', // Sections and headings.\n  'h2', // Sections and headings.\n  'h3', // Sections and headings.\n  'h4', // Sections and headings.\n  'h5', // Sections and headings.\n  'h6', // Sections and headings.\n  'head', // Page.\n  'header', // Flow content.\n  'hgroup', // Sections and headings.\n  'hr', // Flow content.\n  'html', // Page.\n  'legend', // Flow content.\n  'li', // Block-like.\n  'li', // Similar to block.\n  'listing', // Flow content, legacy\n  'main', // Flow content.\n  'menu', // Lists.\n  'nav', // Sections and headings.\n  'ol', // Lists.\n  'optgroup', // Similar to block.\n  'option', // Similar to block.\n  'p', // Flow content.\n  'plaintext', // Flow content, legacy\n  'pre', // Flow content.\n  'section', // Sections and headings.\n  'summary', // Similar to block.\n  'table', // Similar to block.\n  'tbody', // Similar to block.\n  'td', // Block-like.\n  'td', // Similar to block.\n  'tfoot', // Similar to block.\n  'th', // Block-like.\n  'th', // Similar to block.\n  'thead', // Similar to block.\n  'tr', // Similar to block.\n  'ul', // Lists.\n  'wbr', // Contribute whitespace intrinsically.\n  'xmp' // Flow content, legacy\n]\n"], "names": [], "mappings": "AAAA,+FAA+F;;;;AACxF,MAAM,SAAS;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM,uBAAuB;CAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 841, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-minify-whitespace/lib/content.js"], "sourcesContent": ["export const content = [\n  // Form.\n  'button',\n  'input',\n  'select',\n  'textarea'\n]\n"], "names": [], "mappings": ";;;AAAO,MAAM,UAAU;IACrB,QAAQ;IACR;IACA;IACA;IACA;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 857, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-minify-whitespace/lib/skippable.js"], "sourcesContent": ["export const skippable = [\n  'area',\n  'base',\n  'basefont',\n  'dialog',\n  'datalist',\n  'head',\n  'link',\n  'meta',\n  'noembed',\n  'noframes',\n  'param',\n  'rp',\n  'script',\n  'source',\n  'style',\n  'template',\n  'track',\n  'title'\n]\n"], "names": [], "mappings": ";;;AAAO,MAAM,YAAY;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 886, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-minify-whitespace/lib/index.js"], "sourcesContent": ["/**\n * @import {Nodes, Parents, Text} from 'hast'\n */\n\n/**\n * @callback Collapse\n *   Collapse a string.\n * @param {string} value\n *   Value to collapse.\n * @returns {string}\n *   Collapsed value.\n *\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [newlines=false]\n *   Collapse whitespace containing newlines to `'\\n'` instead of `' '`\n *   (default: `false`); the default is to collapse to a single space.\n *\n * @typedef Result\n *   Result.\n * @property {boolean} remove\n *   Whether to remove.\n * @property {boolean} ignore\n *   Whether to ignore.\n * @property {boolean} stripAtStart\n *   Whether to strip at the start.\n *\n * @typedef State\n *   Info passed around.\n * @property {Collapse} collapse\n *   Collapse.\n * @property {Whitespace} whitespace\n *   Current whitespace.\n * @property {boolean | undefined} [before]\n *   Whether there is a break before (default: `false`).\n * @property {boolean | undefined} [after]\n *   Whether there is a break after (default: `false`).\n *\n * @typedef {'normal' | 'nowrap' | 'pre' | 'pre-wrap'} Whitespace\n *   Whitespace setting.\n */\n\nimport {embedded} from 'hast-util-embedded'\nimport {isElement} from 'hast-util-is-element'\nimport {whitespace} from 'hast-util-whitespace'\nimport {convert} from 'unist-util-is'\nimport {blocks} from './block.js'\nimport {content as contents} from './content.js'\nimport {skippable as skippables} from './skippable.js'\n\n/** @type {Options} */\nconst emptyOptions = {}\nconst ignorableNode = convert(['comment', 'doctype'])\n\n/**\n * Minify whitespace.\n *\n * @param {Nodes} tree\n *   Tree.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nexport function minifyWhitespace(tree, options) {\n  const settings = options || emptyOptions\n\n  minify(tree, {\n    collapse: collapseFactory(\n      settings.newlines ? replaceNewlines : replaceWhitespace\n    ),\n    whitespace: 'normal'\n  })\n}\n\n/**\n * @param {Nodes} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction minify(node, state) {\n  if ('children' in node) {\n    const settings = {...state}\n\n    if (node.type === 'root' || blocklike(node)) {\n      settings.before = true\n      settings.after = true\n    }\n\n    settings.whitespace = inferWhiteSpace(node, state)\n\n    return all(node, settings)\n  }\n\n  if (node.type === 'text') {\n    if (state.whitespace === 'normal') {\n      return minifyText(node, state)\n    }\n\n    // Naïve collapse, but no trimming:\n    if (state.whitespace === 'nowrap') {\n      node.value = state.collapse(node.value)\n    }\n\n    // The `pre-wrap` or `pre` whitespace settings are neither collapsed nor\n    // trimmed.\n  }\n\n  return {ignore: ignorableNode(node), stripAtStart: false, remove: false}\n}\n\n/**\n * @param {Text} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction minifyText(node, state) {\n  const value = state.collapse(node.value)\n  const result = {ignore: false, stripAtStart: false, remove: false}\n  let start = 0\n  let end = value.length\n\n  if (state.before && removable(value.charAt(0))) {\n    start++\n  }\n\n  if (start !== end && removable(value.charAt(end - 1))) {\n    if (state.after) {\n      end--\n    } else {\n      result.stripAtStart = true\n    }\n  }\n\n  if (start === end) {\n    result.remove = true\n  } else {\n    node.value = value.slice(start, end)\n  }\n\n  return result\n}\n\n/**\n * @param {Parents} parent\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction all(parent, state) {\n  let before = state.before\n  const after = state.after\n  const children = parent.children\n  let length = children.length\n  let index = -1\n\n  while (++index < length) {\n    const result = minify(children[index], {\n      ...state,\n      after: collapsableAfter(children, index, after),\n      before\n    })\n\n    if (result.remove) {\n      children.splice(index, 1)\n      index--\n      length--\n    } else if (!result.ignore) {\n      before = result.stripAtStart\n    }\n\n    // If this element, such as a `<select>` or `<img>`, contributes content\n    // somehow, allow whitespace again.\n    if (content(children[index])) {\n      before = false\n    }\n  }\n\n  return {ignore: false, stripAtStart: Boolean(before || after), remove: false}\n}\n\n/**\n * @param {Array<Nodes>} nodes\n *   Nodes.\n * @param {number} index\n *   Index.\n * @param {boolean | undefined} [after]\n *   Whether there is a break after `nodes` (default: `false`).\n * @returns {boolean | undefined}\n *   Whether there is a break after the node at `index`.\n */\nfunction collapsableAfter(nodes, index, after) {\n  while (++index < nodes.length) {\n    const node = nodes[index]\n    let result = inferBoundary(node)\n\n    if (result === undefined && 'children' in node && !skippable(node)) {\n      result = collapsableAfter(node.children, -1)\n    }\n\n    if (typeof result === 'boolean') {\n      return result\n    }\n  }\n\n  return after\n}\n\n/**\n * Infer two types of boundaries:\n *\n * 1. `true` — boundary for which whitespace around it does not contribute\n *    anything\n * 2. `false` — boundary for which whitespace around it *does* contribute\n *\n * No result (`undefined`) is returned if it is unknown.\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean | undefined}\n *   Boundary.\n */\nfunction inferBoundary(node) {\n  if (node.type === 'element') {\n    if (content(node)) {\n      return false\n    }\n\n    if (blocklike(node)) {\n      return true\n    }\n\n    // Unknown: either depends on siblings if embedded or metadata, or on\n    // children.\n  } else if (node.type === 'text') {\n    if (!whitespace(node)) {\n      return false\n    }\n  } else if (!ignorableNode(node)) {\n    return false\n  }\n}\n\n/**\n * Infer whether a node is skippable.\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is skippable.\n */\nfunction content(node) {\n  return embedded(node) || isElement(node, contents)\n}\n\n/**\n * See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is block-like.\n */\nfunction blocklike(node) {\n  return isElement(node, blocks)\n}\n\n/**\n * @param {Parents} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is skippable.\n */\nfunction skippable(node) {\n  return (\n    Boolean(node.type === 'element' && node.properties.hidden) ||\n    ignorableNode(node) ||\n    isElement(node, skippables)\n  )\n}\n\n/**\n * @param {string} character\n *   Character.\n * @returns {boolean}\n *   Whether `character` is removable.\n */\nfunction removable(character) {\n  return character === ' ' || character === '\\n'\n}\n\n/**\n * @type {Collapse}\n */\nfunction replaceNewlines(value) {\n  const match = /\\r?\\n|\\r/.exec(value)\n  return match ? match[0] : ' '\n}\n\n/**\n * @type {Collapse}\n */\nfunction replaceWhitespace() {\n  return ' '\n}\n\n/**\n * @param {Collapse} replace\n * @returns {Collapse}\n *   Collapse.\n */\nfunction collapseFactory(replace) {\n  return collapse\n\n  /**\n   * @type {Collapse}\n   */\n  function collapse(value) {\n    return String(value).replace(/[\\t\\n\\v\\f\\r ]+/g, replace)\n  }\n}\n\n/**\n * We don’t need to support void elements here (so `nobr wbr` -> `normal` is\n * ignored).\n *\n * @param {Parents} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Whitespace}\n *   Whitespace.\n */\nfunction inferWhiteSpace(node, state) {\n  if ('tagName' in node && node.properties) {\n    switch (node.tagName) {\n      // Whitespace in script/style, while not displayed by CSS as significant,\n      // could have some meaning in JS/CSS, so we can’t touch them.\n      case 'listing':\n      case 'plaintext':\n      case 'script':\n      case 'style':\n      case 'xmp': {\n        return 'pre'\n      }\n\n      case 'nobr': {\n        return 'nowrap'\n      }\n\n      case 'pre': {\n        return node.properties.wrap ? 'pre-wrap' : 'pre'\n      }\n\n      case 'td':\n      case 'th': {\n        return node.properties.noWrap ? 'nowrap' : state.whitespace\n      }\n\n      case 'textarea': {\n        return 'pre-wrap'\n      }\n\n      default:\n    }\n  }\n\n  return state.whitespace\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoCC;;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,oBAAoB,GACpB,MAAM,eAAe,CAAC;AACtB,MAAM,gBAAgB,CAAA,GAAA,mJAAA,CAAA,UAAO,AAAD,EAAE;IAAC;IAAW;CAAU;AAY7C,SAAS,iBAAiB,IAAI,EAAE,OAAO;IAC5C,MAAM,WAAW,WAAW;IAE5B,OAAO,MAAM;QACX,UAAU,gBACR,SAAS,QAAQ,GAAG,kBAAkB;QAExC,YAAY;IACd;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,OAAO,IAAI,EAAE,KAAK;IACzB,IAAI,cAAc,MAAM;QACtB,MAAM,WAAW;YAAC,GAAG,KAAK;QAAA;QAE1B,IAAI,KAAK,IAAI,KAAK,UAAU,UAAU,OAAO;YAC3C,SAAS,MAAM,GAAG;YAClB,SAAS,KAAK,GAAG;QACnB;QAEA,SAAS,UAAU,GAAG,gBAAgB,MAAM;QAE5C,OAAO,IAAI,MAAM;IACnB;IAEA,IAAI,KAAK,IAAI,KAAK,QAAQ;QACxB,IAAI,MAAM,UAAU,KAAK,UAAU;YACjC,OAAO,WAAW,MAAM;QAC1B;QAEA,mCAAmC;QACnC,IAAI,MAAM,UAAU,KAAK,UAAU;YACjC,KAAK,KAAK,GAAG,MAAM,QAAQ,CAAC,KAAK,KAAK;QACxC;IAEA,wEAAwE;IACxE,WAAW;IACb;IAEA,OAAO;QAAC,QAAQ,cAAc;QAAO,cAAc;QAAO,QAAQ;IAAK;AACzE;AAEA;;;;;;;CAOC,GACD,SAAS,WAAW,IAAI,EAAE,KAAK;IAC7B,MAAM,QAAQ,MAAM,QAAQ,CAAC,KAAK,KAAK;IACvC,MAAM,SAAS;QAAC,QAAQ;QAAO,cAAc;QAAO,QAAQ;IAAK;IACjE,IAAI,QAAQ;IACZ,IAAI,MAAM,MAAM,MAAM;IAEtB,IAAI,MAAM,MAAM,IAAI,UAAU,MAAM,MAAM,CAAC,KAAK;QAC9C;IACF;IAEA,IAAI,UAAU,OAAO,UAAU,MAAM,MAAM,CAAC,MAAM,KAAK;QACrD,IAAI,MAAM,KAAK,EAAE;YACf;QACF,OAAO;YACL,OAAO,YAAY,GAAG;QACxB;IACF;IAEA,IAAI,UAAU,KAAK;QACjB,OAAO,MAAM,GAAG;IAClB,OAAO;QACL,KAAK,KAAK,GAAG,MAAM,KAAK,CAAC,OAAO;IAClC;IAEA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,IAAI,MAAM,EAAE,KAAK;IACxB,IAAI,SAAS,MAAM,MAAM;IACzB,MAAM,QAAQ,MAAM,KAAK;IACzB,MAAM,WAAW,OAAO,QAAQ;IAChC,IAAI,SAAS,SAAS,MAAM;IAC5B,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,OAAQ;QACvB,MAAM,SAAS,OAAO,QAAQ,CAAC,MAAM,EAAE;YACrC,GAAG,KAAK;YACR,OAAO,iBAAiB,UAAU,OAAO;YACzC;QACF;QAEA,IAAI,OAAO,MAAM,EAAE;YACjB,SAAS,MAAM,CAAC,OAAO;YACvB;YACA;QACF,OAAO,IAAI,CAAC,OAAO,MAAM,EAAE;YACzB,SAAS,OAAO,YAAY;QAC9B;QAEA,wEAAwE;QACxE,mCAAmC;QACnC,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG;YAC5B,SAAS;QACX;IACF;IAEA,OAAO;QAAC,QAAQ;QAAO,cAAc,QAAQ,UAAU;QAAQ,QAAQ;IAAK;AAC9E;AAEA;;;;;;;;;CASC,GACD,SAAS,iBAAiB,KAAK,EAAE,KAAK,EAAE,KAAK;IAC3C,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,MAAM,OAAO,KAAK,CAAC,MAAM;QACzB,IAAI,SAAS,cAAc;QAE3B,IAAI,WAAW,aAAa,cAAc,QAAQ,CAAC,UAAU,OAAO;YAClE,SAAS,iBAAiB,KAAK,QAAQ,EAAE,CAAC;QAC5C;QAEA,IAAI,OAAO,WAAW,WAAW;YAC/B,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA;;;;;;;;;;;;;CAaC,GACD,SAAS,cAAc,IAAI;IACzB,IAAI,KAAK,IAAI,KAAK,WAAW;QAC3B,IAAI,QAAQ,OAAO;YACjB,OAAO;QACT;QAEA,IAAI,UAAU,OAAO;YACnB,OAAO;QACT;IAEA,qEAAqE;IACrE,YAAY;IACd,OAAO,IAAI,KAAK,IAAI,KAAK,QAAQ;QAC/B,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE,OAAO;YACrB,OAAO;QACT;IACF,OAAO,IAAI,CAAC,cAAc,OAAO;QAC/B,OAAO;IACT;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,QAAQ,IAAI;IACnB,OAAO,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,MAAM,sKAAA,CAAA,UAAQ;AACnD;AAEA;;;;;;;CAOC,GACD,SAAS,UAAU,IAAI;IACrB,OAAO,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,MAAM,oKAAA,CAAA,SAAM;AAC/B;AAEA;;;;;CAKC,GACD,SAAS,UAAU,IAAI;IACrB,OACE,QAAQ,KAAK,IAAI,KAAK,aAAa,KAAK,UAAU,CAAC,MAAM,KACzD,cAAc,SACd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,MAAM,wKAAA,CAAA,YAAU;AAE9B;AAEA;;;;;CAKC,GACD,SAAS,UAAU,SAAS;IAC1B,OAAO,cAAc,OAAO,cAAc;AAC5C;AAEA;;CAEC,GACD,SAAS,gBAAgB,KAAK;IAC5B,MAAM,QAAQ,WAAW,IAAI,CAAC;IAC9B,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;AAC5B;AAEA;;CAEC,GACD,SAAS;IACP,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,gBAAgB,OAAO;IAC9B,OAAO;;IAEP;;GAEC,GACD,SAAS,SAAS,KAAK;QACrB,OAAO,OAAO,OAAO,OAAO,CAAC,mBAAmB;IAClD;AACF;AAEA;;;;;;;;;;CAUC,GACD,SAAS,gBAAgB,IAAI,EAAE,KAAK;IAClC,IAAI,aAAa,QAAQ,KAAK,UAAU,EAAE;QACxC,OAAQ,KAAK,OAAO;YAClB,yEAAyE;YACzE,6DAA6D;YAC7D,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAO;oBACV,OAAO;gBACT;YAEA,KAAK;gBAAQ;oBACX,OAAO;gBACT;YAEA,KAAK;gBAAO;oBACV,OAAO,KAAK,UAAU,CAAC,IAAI,GAAG,aAAa;gBAC7C;YAEA,KAAK;YACL,KAAK;gBAAM;oBACT,OAAO,KAAK,UAAU,CAAC,MAAM,GAAG,WAAW,MAAM,UAAU;gBAC7D;YAEA,KAAK;gBAAY;oBACf,OAAO;gBACT;YAEA;QACF;IACF;IAEA,OAAO,MAAM,UAAU;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1224, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/rehype-minify-whitespace/lib/index.js"], "sourcesContent": ["/**\n * @import {Options} from 'hast-util-minify-whitespace'\n * @import {Root} from 'hast'\n */\n\nimport {minifyWhitespace} from 'hast-util-minify-whitespace'\n\n/**\n * Minify whitespace.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns\n *   Transform.\n */\nexport default function rehypeMinifyWhitespace(options) {\n  /**\n   * @param {Root} tree\n   *   Tree.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  return function (tree) {\n    minifyWhitespace(tree, options)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AAUe,SAAS,uBAAuB,OAAO;IACpD;;;;;GAKC,GACD,OAAO,SAAU,IAAI;QACnB,CAAA,GAAA,oKAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;IACzB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1248, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/unist-util-visit-parents/lib/color.node.js"], "sourcesContent": ["/**\n * @param {string} d\n * @returns {string}\n */\nexport function color(d) {\n  return '\\u001B[33m' + d + '\\u001B[39m'\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AACM,SAAS,MAAM,CAAC;IACrB,OAAO,eAAe,IAAI;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1263, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/unist-util-visit-parents/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} UnistNode\n * @typedef {import('unist').Parent} UnistParent\n */\n\n/**\n * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test\n *   Test from `unist-util-is`.\n *\n *   Note: we have remove and add `undefined`, because otherwise when generating\n *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,\n *   which doesn’t work when publishing on npm.\n */\n\n/**\n * @typedef {(\n *   Fn extends (value: any) => value is infer Thing\n *   ? Thing\n *   : Fallback\n * )} Predicate\n *   Get the value of a type guard `Fn`.\n * @template Fn\n *   Value; typically function that is a type guard (such as `(x): x is Y`).\n * @template Fallback\n *   Value to yield if `Fn` is not a type guard.\n */\n\n/**\n * @typedef {(\n *   Check extends null | undefined // No test.\n *   ? Value\n *   : Value extends {type: Check} // String (type) test.\n *   ? Value\n *   : Value extends Check // Partial test.\n *   ? Value\n *   : Check extends Function // Function test.\n *   ? Predicate<Check, Value> extends Value\n *     ? Predicate<Check, Value>\n *     : never\n *   : never // Some other test?\n * )} MatchesOne\n *   Check whether a node matches a primitive check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test, but not arrays.\n */\n\n/**\n * @typedef {(\n *   Check extends Array<any>\n *   ? MatchesOne<Value, Check[keyof Check]>\n *   : MatchesOne<Value, Check>\n * )} Matches\n *   Check whether a node matches a check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test.\n */\n\n/**\n * @typedef {0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10} Uint\n *   Number; capped reasonably.\n */\n\n/**\n * @typedef {I extends 0 ? 1 : I extends 1 ? 2 : I extends 2 ? 3 : I extends 3 ? 4 : I extends 4 ? 5 : I extends 5 ? 6 : I extends 6 ? 7 : I extends 7 ? 8 : I extends 8 ? 9 : 10} Increment\n *   Increment a number in the type system.\n * @template {Uint} [I=0]\n *   Index.\n */\n\n/**\n * @typedef {(\n *   Node extends UnistParent\n *   ? Node extends {children: Array<infer Children>}\n *     ? Child extends Children ? Node : never\n *     : never\n *   : never\n * )} InternalParent\n *   Collect nodes that can be parents of `Child`.\n * @template {UnistNode} Node\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {InternalParent<InclusiveDescendant<Tree>, Child>} Parent\n *   Collect nodes in `Tree` that can be parents of `Child`.\n * @template {UnistNode} Tree\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {(\n *   Depth extends Max\n *   ? never\n *   :\n *     | InternalParent<Node, Child>\n *     | InternalAncestor<Node, InternalParent<Node, Child>, Max, Increment<Depth>>\n * )} InternalAncestor\n *   Collect nodes in `Tree` that can be ancestors of `Child`.\n * @template {UnistNode} Node\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n * @template {Uint} [Max=10]\n *   Max; searches up to this depth.\n * @template {Uint} [Depth=0]\n *   Current depth.\n */\n\n/**\n * @typedef {InternalAncestor<InclusiveDescendant<Tree>, Child>} Ancestor\n *   Collect nodes in `Tree` that can be ancestors of `Child`.\n * @template {UnistNode} Tree\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {(\n *   Tree extends UnistParent\n *     ? Depth extends Max\n *       ? Tree\n *       : Tree | InclusiveDescendant<Tree['children'][number], Max, Increment<Depth>>\n *     : Tree\n * )} InclusiveDescendant\n *   Collect all (inclusive) descendants of `Tree`.\n *\n *   > 👉 **Note**: for performance reasons, this seems to be the fastest way to\n *   > recurse without actually running into an infinite loop, which the\n *   > previous version did.\n *   >\n *   > Practically, a max of `2` is typically enough assuming a `Root` is\n *   > passed, but it doesn’t improve performance.\n *   > It gets higher with `List > ListItem > Table > TableRow > TableCell`.\n *   > Using up to `10` doesn’t hurt or help either.\n * @template {UnistNode} Tree\n *   Tree type.\n * @template {Uint} [Max=10]\n *   Max; searches up to this depth.\n * @template {Uint} [Depth=0]\n *   Current depth.\n */\n\n/**\n * @typedef {'skip' | boolean} Action\n *   Union of the action types.\n *\n * @typedef {number} Index\n *   Move to the sibling at `index` next (after node itself is completely\n *   traversed).\n *\n *   Useful if mutating the tree, such as removing the node the visitor is\n *   currently on, or any of its previous siblings.\n *   Results less than 0 or greater than or equal to `children.length` stop\n *   traversing the parent.\n *\n * @typedef {[(Action | null | undefined | void)?, (Index | null | undefined)?]} ActionTuple\n *   List with one or two values, the first an action, the second an index.\n *\n * @typedef {Action | ActionTuple | Index | null | undefined | void} VisitorResult\n *   Any value that can be returned from a visitor.\n */\n\n/**\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform the parent of node (the last of `ancestors`).\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of an ancestor still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Array<VisitedParents>} ancestors\n *   Ancestors of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n * @template {UnistNode} [Visited=UnistNode]\n *   Visited node type.\n * @template {UnistParent} [VisitedParents=UnistParent]\n *   Ancestor type.\n */\n\n/**\n * @typedef {Visitor<Matches<InclusiveDescendant<Tree>, Check>, Ancestor<Tree, Matches<InclusiveDescendant<Tree>, Check>>>} BuildVisitor\n *   Build a typed `Visitor` function from a tree and a test.\n *\n *   It will infer which values are passed as `node` and which as `parents`.\n * @template {UnistNode} [Tree=UnistNode]\n *   Tree type.\n * @template {Test} [Check=Test]\n *   Test type.\n */\n\nimport {convert} from 'unist-util-is'\nimport {color} from 'unist-util-visit-parents/do-not-use-color'\n\n/** @type {Readonly<ActionTuple>} */\nconst empty = []\n\n/**\n * Continue traversing as normal.\n */\nexport const CONTINUE = true\n\n/**\n * Stop traversing immediately.\n */\nexport const EXIT = false\n\n/**\n * Do not traverse this node’s children.\n */\nexport const SKIP = 'skip'\n\n/**\n * Visit nodes, with ancestral information.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @overload\n * @param {Tree} tree\n * @param {Check} check\n * @param {BuildVisitor<Tree, Check>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {undefined}\n *\n * @overload\n * @param {Tree} tree\n * @param {BuildVisitor<Tree>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {undefined}\n *\n * @param {UnistNode} tree\n *   Tree to traverse.\n * @param {Visitor | Test} test\n *   `unist-util-is`-compatible test\n * @param {Visitor | boolean | null | undefined} [visitor]\n *   Handle each node.\n * @param {boolean | null | undefined} [reverse]\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns {undefined}\n *   Nothing.\n *\n * @template {UnistNode} Tree\n *   Node type.\n * @template {Test} Check\n *   `unist-util-is`-compatible test.\n */\nexport function visitParents(tree, test, visitor, reverse) {\n  /** @type {Test} */\n  let check\n\n  if (typeof test === 'function' && typeof visitor !== 'function') {\n    reverse = visitor\n    // @ts-expect-error no visitor given, so `visitor` is test.\n    visitor = test\n  } else {\n    // @ts-expect-error visitor given, so `test` isn’t a visitor.\n    check = test\n  }\n\n  const is = convert(check)\n  const step = reverse ? -1 : 1\n\n  factory(tree, undefined, [])()\n\n  /**\n   * @param {UnistNode} node\n   * @param {number | undefined} index\n   * @param {Array<UnistParent>} parents\n   */\n  function factory(node, index, parents) {\n    const value = /** @type {Record<string, unknown>} */ (\n      node && typeof node === 'object' ? node : {}\n    )\n\n    if (typeof value.type === 'string') {\n      const name =\n        // `hast`\n        typeof value.tagName === 'string'\n          ? value.tagName\n          : // `xast`\n          typeof value.name === 'string'\n          ? value.name\n          : undefined\n\n      Object.defineProperty(visit, 'name', {\n        value:\n          'node (' + color(node.type + (name ? '<' + name + '>' : '')) + ')'\n      })\n    }\n\n    return visit\n\n    function visit() {\n      /** @type {Readonly<ActionTuple>} */\n      let result = empty\n      /** @type {Readonly<ActionTuple>} */\n      let subresult\n      /** @type {number} */\n      let offset\n      /** @type {Array<UnistParent>} */\n      let grandparents\n\n      if (!test || is(node, index, parents[parents.length - 1] || undefined)) {\n        // @ts-expect-error: `visitor` is now a visitor.\n        result = toResult(visitor(node, parents))\n\n        if (result[0] === EXIT) {\n          return result\n        }\n      }\n\n      if ('children' in node && node.children) {\n        const nodeAsParent = /** @type {UnistParent} */ (node)\n\n        if (nodeAsParent.children && result[0] !== SKIP) {\n          offset = (reverse ? nodeAsParent.children.length : -1) + step\n          grandparents = parents.concat(nodeAsParent)\n\n          while (offset > -1 && offset < nodeAsParent.children.length) {\n            const child = nodeAsParent.children[offset]\n\n            subresult = factory(child, offset, grandparents)()\n\n            if (subresult[0] === EXIT) {\n              return subresult\n            }\n\n            offset =\n              typeof subresult[1] === 'number' ? subresult[1] : offset + step\n          }\n        }\n      }\n\n      return result\n    }\n  }\n}\n\n/**\n * Turn a return value into a clean result.\n *\n * @param {VisitorResult} value\n *   Valid return values from visitors.\n * @returns {Readonly<ActionTuple>}\n *   Clean result.\n */\nfunction toResult(value) {\n  if (Array.isArray(value)) {\n    return value\n  }\n\n  if (typeof value === 'number') {\n    return [CONTINUE, value]\n  }\n\n  return value === null || value === undefined ? empty : [value]\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;CAWC,GAED;;;;;;;;;;;;;;;;;;;CAmBC,GAED;;;;;;;;;;;CAWC,GAED;;;CAGC,GAED;;;;;CAKC,GAED;;;;;;;;;;;;;CAaC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;;;;;;;CAiBC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GAED;;;;;;;;;;;;;;;;;;CAkBC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmCC,GAED;;;;;;;;;CASC;;;;;;AAED;AACA;;;AAEA,kCAAkC,GAClC,MAAM,QAAQ,EAAE;AAKT,MAAM,WAAW;AAKjB,MAAM,OAAO;AAKb,MAAM,OAAO;AAiDb,SAAS,aAAa,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO;IACvD,iBAAiB,GACjB,IAAI;IAEJ,IAAI,OAAO,SAAS,cAAc,OAAO,YAAY,YAAY;QAC/D,UAAU;QACV,2DAA2D;QAC3D,UAAU;IACZ,OAAO;QACL,6DAA6D;QAC7D,QAAQ;IACV;IAEA,MAAM,KAAK,CAAA,GAAA,mJAAA,CAAA,UAAO,AAAD,EAAE;IACnB,MAAM,OAAO,UAAU,CAAC,IAAI;IAE5B,QAAQ,MAAM,WAAW,EAAE;IAE3B;;;;GAIC,GACD,SAAS,QAAQ,IAAI,EAAE,KAAK,EAAE,OAAO;QACnC,MAAM,QACJ,QAAQ,OAAO,SAAS,WAAW,OAAO,CAAC;QAG7C,IAAI,OAAO,MAAM,IAAI,KAAK,UAAU;YAClC,MAAM,OACJ,SAAS;YACT,OAAO,MAAM,OAAO,KAAK,WACrB,MAAM,OAAO,GAEf,OAAO,MAAM,IAAI,KAAK,WACpB,MAAM,IAAI,GACV;YAEN,OAAO,cAAc,CAAC,OAAO,QAAQ;gBACnC,OACE,WAAW,CAAA,GAAA,yKAAA,CAAA,QAAK,AAAD,EAAE,KAAK,IAAI,GAAG,CAAC,OAAO,MAAM,OAAO,MAAM,EAAE,KAAK;YACnE;QACF;QAEA,OAAO;;QAEP,SAAS;YACP,kCAAkC,GAClC,IAAI,SAAS;YACb,kCAAkC,GAClC,IAAI;YACJ,mBAAmB,GACnB,IAAI;YACJ,+BAA+B,GAC/B,IAAI;YAEJ,IAAI,CAAC,QAAQ,GAAG,MAAM,OAAO,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,IAAI,YAAY;gBACtE,gDAAgD;gBAChD,SAAS,SAAS,QAAQ,MAAM;gBAEhC,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM;oBACtB,OAAO;gBACT;YACF;YAEA,IAAI,cAAc,QAAQ,KAAK,QAAQ,EAAE;gBACvC,MAAM,eAA2C;gBAEjD,IAAI,aAAa,QAAQ,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM;oBAC/C,SAAS,CAAC,UAAU,aAAa,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI;oBACzD,eAAe,QAAQ,MAAM,CAAC;oBAE9B,MAAO,SAAS,CAAC,KAAK,SAAS,aAAa,QAAQ,CAAC,MAAM,CAAE;wBAC3D,MAAM,QAAQ,aAAa,QAAQ,CAAC,OAAO;wBAE3C,YAAY,QAAQ,OAAO,QAAQ;wBAEnC,IAAI,SAAS,CAAC,EAAE,KAAK,MAAM;4BACzB,OAAO;wBACT;wBAEA,SACE,OAAO,SAAS,CAAC,EAAE,KAAK,WAAW,SAAS,CAAC,EAAE,GAAG,SAAS;oBAC/D;gBACF;YACF;YAEA,OAAO;QACT;IACF;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,SAAS,KAAK;IACrB,IAAI,MAAM,OAAO,CAAC,QAAQ;QACxB,OAAO;IACT;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;YAAC;YAAU;SAAM;IAC1B;IAEA,OAAO,UAAU,QAAQ,UAAU,YAAY,QAAQ;QAAC;KAAM;AAChE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1552, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/unist-util-visit/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} UnistNode\n * @typedef {import('unist').Parent} UnistParent\n * @typedef {import('unist-util-visit-parents').VisitorResult} VisitorResult\n */\n\n/**\n * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test\n *   Test from `unist-util-is`.\n *\n *   Note: we have remove and add `undefined`, because otherwise when generating\n *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,\n *   which doesn’t work when publishing on npm.\n */\n\n// To do: use types from `unist-util-visit-parents` when it’s released.\n\n/**\n * @typedef {(\n *   Fn extends (value: any) => value is infer Thing\n *   ? Thing\n *   : Fallback\n * )} Predicate\n *   Get the value of a type guard `Fn`.\n * @template Fn\n *   Value; typically function that is a type guard (such as `(x): x is Y`).\n * @template Fallback\n *   Value to yield if `Fn` is not a type guard.\n */\n\n/**\n * @typedef {(\n *   Check extends null | undefined // No test.\n *   ? Value\n *   : Value extends {type: Check} // String (type) test.\n *   ? Value\n *   : Value extends Check // Partial test.\n *   ? Value\n *   : Check extends Function // Function test.\n *   ? Predicate<Check, Value> extends Value\n *     ? Predicate<Check, Value>\n *     : never\n *   : never // Some other test?\n * )} MatchesOne\n *   Check whether a node matches a primitive check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test, but not arrays.\n */\n\n/**\n * @typedef {(\n *   Check extends Array<any>\n *   ? MatchesOne<Value, Check[keyof Check]>\n *   : MatchesOne<Value, Check>\n * )} Matches\n *   Check whether a node matches a check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test.\n */\n\n/**\n * @typedef {0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10} Uint\n *   Number; capped reasonably.\n */\n\n/**\n * @typedef {I extends 0 ? 1 : I extends 1 ? 2 : I extends 2 ? 3 : I extends 3 ? 4 : I extends 4 ? 5 : I extends 5 ? 6 : I extends 6 ? 7 : I extends 7 ? 8 : I extends 8 ? 9 : 10} Increment\n *   Increment a number in the type system.\n * @template {Uint} [I=0]\n *   Index.\n */\n\n/**\n * @typedef {(\n *   Node extends UnistParent\n *   ? Node extends {children: Array<infer Children>}\n *     ? Child extends Children ? Node : never\n *     : never\n *   : never\n * )} InternalParent\n *   Collect nodes that can be parents of `Child`.\n * @template {UnistNode} Node\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {InternalParent<InclusiveDescendant<Tree>, Child>} Parent\n *   Collect nodes in `Tree` that can be parents of `Child`.\n * @template {UnistNode} Tree\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {(\n *   Depth extends Max\n *   ? never\n *   :\n *     | InternalParent<Node, Child>\n *     | InternalAncestor<Node, InternalParent<Node, Child>, Max, Increment<Depth>>\n * )} InternalAncestor\n *   Collect nodes in `Tree` that can be ancestors of `Child`.\n * @template {UnistNode} Node\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n * @template {Uint} [Max=10]\n *   Max; searches up to this depth.\n * @template {Uint} [Depth=0]\n *   Current depth.\n */\n\n/**\n * @typedef {(\n *   Tree extends UnistParent\n *     ? Depth extends Max\n *       ? Tree\n *       : Tree | InclusiveDescendant<Tree['children'][number], Max, Increment<Depth>>\n *     : Tree\n * )} InclusiveDescendant\n *   Collect all (inclusive) descendants of `Tree`.\n *\n *   > 👉 **Note**: for performance reasons, this seems to be the fastest way to\n *   > recurse without actually running into an infinite loop, which the\n *   > previous version did.\n *   >\n *   > Practically, a max of `2` is typically enough assuming a `Root` is\n *   > passed, but it doesn’t improve performance.\n *   > It gets higher with `List > ListItem > Table > TableRow > TableCell`.\n *   > Using up to `10` doesn’t hurt or help either.\n * @template {UnistNode} Tree\n *   Tree type.\n * @template {Uint} [Max=10]\n *   Max; searches up to this depth.\n * @template {Uint} [Depth=0]\n *   Current depth.\n */\n\n/**\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform `parent`.\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of `parent` still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Visited extends UnistNode ? number | undefined : never} index\n *   Index of `node` in `parent`.\n * @param {Ancestor extends UnistParent ? Ancestor | undefined : never} parent\n *   Parent of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n * @template {UnistNode} [Visited=UnistNode]\n *   Visited node type.\n * @template {UnistParent} [Ancestor=UnistParent]\n *   Ancestor type.\n */\n\n/**\n * @typedef {Visitor<Visited, Parent<Ancestor, Visited>>} BuildVisitorFromMatch\n *   Build a typed `Visitor` function from a node and all possible parents.\n *\n *   It will infer which values are passed as `node` and which as `parent`.\n * @template {UnistNode} Visited\n *   Node type.\n * @template {UnistParent} Ancestor\n *   Parent type.\n */\n\n/**\n * @typedef {(\n *   BuildVisitorFromMatch<\n *     Matches<Descendant, Check>,\n *     Extract<Descendant, UnistParent>\n *   >\n * )} BuildVisitorFromDescendants\n *   Build a typed `Visitor` function from a list of descendants and a test.\n *\n *   It will infer which values are passed as `node` and which as `parent`.\n * @template {UnistNode} Descendant\n *   Node type.\n * @template {Test} Check\n *   Test type.\n */\n\n/**\n * @typedef {(\n *   BuildVisitorFromDescendants<\n *     InclusiveDescendant<Tree>,\n *     Check\n *   >\n * )} BuildVisitor\n *   Build a typed `Visitor` function from a tree and a test.\n *\n *   It will infer which values are passed as `node` and which as `parent`.\n * @template {UnistNode} [Tree=UnistNode]\n *   Node type.\n * @template {Test} [Check=Test]\n *   Test type.\n */\n\nimport {visitParents} from 'unist-util-visit-parents'\n\nexport {CONTINUE, EXIT, SKIP} from 'unist-util-visit-parents'\n\n/**\n * Visit nodes.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @overload\n * @param {Tree} tree\n * @param {Check} check\n * @param {BuildVisitor<Tree, Check>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {undefined}\n *\n * @overload\n * @param {Tree} tree\n * @param {BuildVisitor<Tree>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {undefined}\n *\n * @param {UnistNode} tree\n *   Tree to traverse.\n * @param {Visitor | Test} testOrVisitor\n *   `unist-util-is`-compatible test (optional, omit to pass a visitor).\n * @param {Visitor | boolean | null | undefined} [visitorOrReverse]\n *   Handle each node (when test is omitted, pass `reverse`).\n * @param {boolean | null | undefined} [maybeReverse=false]\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns {undefined}\n *   Nothing.\n *\n * @template {UnistNode} Tree\n *   Node type.\n * @template {Test} Check\n *   `unist-util-is`-compatible test.\n */\nexport function visit(tree, testOrVisitor, visitorOrReverse, maybeReverse) {\n  /** @type {boolean | null | undefined} */\n  let reverse\n  /** @type {Test} */\n  let test\n  /** @type {Visitor} */\n  let visitor\n\n  if (\n    typeof testOrVisitor === 'function' &&\n    typeof visitorOrReverse !== 'function'\n  ) {\n    test = undefined\n    visitor = testOrVisitor\n    reverse = visitorOrReverse\n  } else {\n    // @ts-expect-error: assume the overload with test was given.\n    test = testOrVisitor\n    // @ts-expect-error: assume the overload with test was given.\n    visitor = visitorOrReverse\n    reverse = maybeReverse\n  }\n\n  visitParents(tree, test, overload, reverse)\n\n  /**\n   * @param {UnistNode} node\n   * @param {Array<UnistParent>} parents\n   */\n  function overload(node, parents) {\n    const parent = parents[parents.length - 1]\n    const index = parent ? parent.children.indexOf(node) : undefined\n    return visitor(node, index, parent)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC,GAED,uEAAuE;AAEvE;;;;;;;;;;;CAWC,GAED;;;;;;;;;;;;;;;;;;;CAmBC,GAED;;;;;;;;;;;CAWC,GAED;;;CAGC,GAED;;;;;CAKC,GAED;;;;;;;;;;;;;CAaC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;;;;;;;CAiBC,GAED;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqCC,GAED;;;;;;;;;CASC,GAED;;;;;;;;;;;;;;CAcC,GAED;;;;;;;;;;;;;;CAcC;;;AAED;;;AAmDO,SAAS,MAAM,IAAI,EAAE,aAAa,EAAE,gBAAgB,EAAE,YAAY;IACvE,uCAAuC,GACvC,IAAI;IACJ,iBAAiB,GACjB,IAAI;IACJ,oBAAoB,GACpB,IAAI;IAEJ,IACE,OAAO,kBAAkB,cACzB,OAAO,qBAAqB,YAC5B;QACA,OAAO;QACP,UAAU;QACV,UAAU;IACZ,OAAO;QACL,6DAA6D;QAC7D,OAAO;QACP,6DAA6D;QAC7D,UAAU;QACV,UAAU;IACZ;IAEA,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE,MAAM,MAAM,UAAU;IAEnC;;;GAGC,GACD,SAAS,SAAS,IAAI,EAAE,OAAO;QAC7B,MAAM,SAAS,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;QAC1C,MAAM,QAAQ,SAAS,OAAO,QAAQ,CAAC,OAAO,CAAC,QAAQ;QACvD,OAAO,QAAQ,MAAM,OAAO;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1785, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/unist-util-position/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n */\n\n/**\n * Get the ending point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nexport const pointEnd = point('end')\n\n/**\n * Get the starting point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nexport const pointStart = point('start')\n\n/**\n * Get the positional info of `node`.\n *\n * @param {'end' | 'start'} type\n *   Side.\n * @returns\n *   Getter.\n */\nfunction point(type) {\n  return point\n\n  /**\n   * Get the point info of `node` at a bound side.\n   *\n   * @param {Node | NodeLike | null | undefined} [node]\n   * @returns {Point | undefined}\n   */\n  function point(node) {\n    const point = (node && node.position && node.position[type]) || {}\n\n    if (\n      typeof point.line === 'number' &&\n      point.line > 0 &&\n      typeof point.column === 'number' &&\n      point.column > 0\n    ) {\n      return {\n        line: point.line,\n        column: point.column,\n        offset:\n          typeof point.offset === 'number' && point.offset > -1\n            ? point.offset\n            : undefined\n      }\n    }\n  }\n}\n\n/**\n * Get the positional info of `node`.\n *\n * @param {Node | NodeLike | null | undefined} [node]\n *   Node.\n * @returns {Position | undefined}\n *   Position.\n */\nexport function position(node) {\n  const start = pointStart(node)\n  const end = pointEnd(node)\n\n  if (start && end) {\n    return {start, end}\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;;;;;;;CAaC,GAED;;;;;;;CAOC;;;;;AACM,MAAM,WAAW,MAAM;AAUvB,MAAM,aAAa,MAAM;AAEhC;;;;;;;CAOC,GACD,SAAS,MAAM,IAAI;IACjB,OAAO;;IAEP;;;;;GAKC,GACD,SAAS,MAAM,IAAI;QACjB,MAAM,QAAQ,AAAC,QAAQ,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,KAAK,IAAK,CAAC;QAEjE,IACE,OAAO,MAAM,IAAI,KAAK,YACtB,MAAM,IAAI,GAAG,KACb,OAAO,MAAM,MAAM,KAAK,YACxB,MAAM,MAAM,GAAG,GACf;YACA,OAAO;gBACL,MAAM,MAAM,IAAI;gBAChB,QAAQ,MAAM,MAAM;gBACpB,QACE,OAAO,MAAM,MAAM,KAAK,YAAY,MAAM,MAAM,GAAG,CAAC,IAChD,MAAM,MAAM,GACZ;YACR;QACF;IACF;AACF;AAUO,SAAS,SAAS,IAAI;IAC3B,MAAM,QAAQ,WAAW;IACzB,MAAM,MAAM,SAAS;IAErB,IAAI,SAAS,KAAK;QAChB,OAAO;YAAC;YAAO;QAAG;IACpB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1858, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/a.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Link, PhrasingContent} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Link}\n *   mdast node.\n */\nexport function a(state, node) {\n  const properties = node.properties || {}\n  // Allow potentially “invalid” nodes, they might be unknown.\n  // We also support straddling later.\n  const children = /** @type {Array<PhrasingContent>} */ (state.all(node))\n\n  /** @type {Link} */\n  const result = {\n    type: 'link',\n    url: state.resolve(String(properties.href || '') || null),\n    title: properties.title ? String(properties.title) : null,\n    children\n  }\n  state.patch(node, result)\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC;;;AACM,SAAS,EAAE,KAAK,EAAE,IAAI;IAC3B,MAAM,aAAa,KAAK,UAAU,IAAI,CAAC;IACvC,4DAA4D;IAC5D,oCAAoC;IACpC,MAAM,WAAkD,MAAM,GAAG,CAAC;IAElE,iBAAiB,GACjB,MAAM,SAAS;QACb,MAAM;QACN,KAAK,MAAM,OAAO,CAAC,OAAO,WAAW,IAAI,IAAI,OAAO;QACpD,OAAO,WAAW,KAAK,GAAG,OAAO,WAAW,KAAK,IAAI;QACrD;IACF;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1892, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/base.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {undefined}\n *   Nothing.\n */\nexport function base(state, node) {\n  if (!state.baseFound) {\n    state.frozenBaseUrl =\n      String((node.properties && node.properties.href) || '') || undefined\n    state.baseFound = true\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;;;;CAOC;;;AACM,SAAS,KAAK,KAAK,EAAE,IAAI;IAC9B,IAAI,CAAC,MAAM,SAAS,EAAE;QACpB,MAAM,aAAa,GACjB,OAAO,AAAC,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,IAAI,IAAK,OAAO;QAC7D,MAAM,SAAS,GAAG;IACpB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1917, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/blockquote.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Blockquote} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Blockquote}\n *   mdast node.\n */\nexport function blockquote(state, node) {\n  /** @type {Blockquote} */\n  const result = {type: 'blockquote', children: state.toFlow(state.all(node))}\n  state.patch(node, result)\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC;;;AACM,SAAS,WAAW,KAAK,EAAE,IAAI;IACpC,uBAAuB,GACvB,MAAM,SAAS;QAAC,MAAM;QAAc,UAAU,MAAM,MAAM,CAAC,MAAM,GAAG,CAAC;IAAM;IAC3E,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1945, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/br.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Break} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Break}\n *   mdast node.\n */\nexport function br(state, node) {\n  /** @type {Break} */\n  const result = {type: 'break'}\n  state.patch(node, result)\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC;;;AACM,SAAS,GAAG,KAAK,EAAE,IAAI;IAC5B,kBAAkB,GAClB,MAAM,SAAS;QAAC,MAAM;IAAO;IAC7B,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1972, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/unist-util-find-after/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} UnistNode\n * @typedef {import('unist').Parent} UnistParent\n */\n\n/**\n * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test\n *   Test from `unist-util-is`.\n *\n *   Note: we have remove and add `undefined`, because otherwise when generating\n *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,\n *   which doesn’t work when publishing on npm.\n */\n\n/**\n * @typedef {(\n *   Fn extends (value: any) => value is infer Thing\n *   ? Thing\n *   : Fallback\n * )} Predicate\n *   Get the value of a type guard `Fn`.\n * @template Fn\n *   Value; typically function that is a type guard (such as `(x): x is Y`).\n * @template Fallback\n *   Value to yield if `Fn` is not a type guard.\n */\n\n/**\n * @typedef {(\n *   Check extends null | undefined // No test.\n *   ? Value\n *   : Value extends {type: Check} // String (type) test.\n *   ? Value\n *   : Value extends Check // Partial test.\n *   ? Value\n *   : Check extends Function // Function test.\n *   ? Predicate<Check, Value> extends Value\n *     ? Predicate<Check, Value>\n *     : never\n *   : never // Some other test?\n * )} MatchesOne\n *   Check whether a node matches a primitive check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test, but not arrays.\n */\n\n/**\n * @typedef {(\n *   Check extends Array<any>\n *   ? MatchesOne<Value, Check[keyof Check]>\n *   : MatchesOne<Value, Check>\n * )} Matches\n *   Check whether a node matches a check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test.\n */\n\n/**\n * @typedef {(\n *   Kind extends {children: Array<infer Child>}\n *   ? Child\n *   : never\n * )} Child\n *   Collect nodes that can be parents of `Child`.\n * @template {UnistNode} Kind\n *   All node types.\n */\n\nimport {convert} from 'unist-util-is'\n\n/**\n * Find the first node in `parent` after another `node` or after an index,\n * that passes `test`.\n *\n * @param parent\n *   Parent node.\n * @param index\n *   Child node or index.\n * @param [test=undefined]\n *   Test for child to look for (optional).\n * @returns\n *   A child (matching `test`, if given) or `undefined`.\n */\nexport const findAfter =\n  // Note: overloads like this are needed to support optional generics.\n  /**\n   * @type {(\n   *   (<Kind extends UnistParent, Check extends Test>(parent: Kind, index: Child<Kind> | number, test: Check) => Matches<Child<Kind>, Check> | undefined) &\n   *   (<Kind extends UnistParent>(parent: Kind, index: Child<Kind> | number, test?: null | undefined) => Child<Kind> | undefined)\n   * )}\n   */\n  (\n    /**\n     * @param {UnistParent} parent\n     * @param {UnistNode | number} index\n     * @param {Test} [test]\n     * @returns {UnistNode | undefined}\n     */\n    function (parent, index, test) {\n      const is = convert(test)\n\n      if (!parent || !parent.type || !parent.children) {\n        throw new Error('Expected parent node')\n      }\n\n      if (typeof index === 'number') {\n        if (index < 0 || index === Number.POSITIVE_INFINITY) {\n          throw new Error('Expected positive finite number as index')\n        }\n      } else {\n        index = parent.children.indexOf(index)\n\n        if (index < 0) {\n          throw new Error('Expected child node or index')\n        }\n      }\n\n      while (++index < parent.children.length) {\n        if (is(parent.children[index], index, parent)) {\n          return parent.children[index]\n        }\n      }\n\n      return undefined\n    }\n  )\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;CAWC,GAED;;;;;;;;;;;;;;;;;;;CAmBC,GAED;;;;;;;;;;;CAWC,GAED;;;;;;;;;CASC;;;AAED;;AAeO,MAAM,YAST;;;;;KAKC,GACD,SAAU,MAAM,EAAE,KAAK,EAAE,IAAI;IAC3B,MAAM,KAAK,CAAA,GAAA,mJAAA,CAAA,UAAO,AAAD,EAAE;IAEnB,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,QAAQ,EAAE;QAC/C,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,IAAI,QAAQ,KAAK,UAAU,OAAO,iBAAiB,EAAE;YACnD,MAAM,IAAI,MAAM;QAClB;IACF,OAAO;QACL,QAAQ,OAAO,QAAQ,CAAC,OAAO,CAAC;QAEhC,IAAI,QAAQ,GAAG;YACb,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAO,EAAE,QAAQ,OAAO,QAAQ,CAAC,MAAM,CAAE;QACvC,IAAI,GAAG,OAAO,QAAQ,CAAC,MAAM,EAAE,OAAO,SAAS;YAC7C,OAAO,OAAO,QAAQ,CAAC,MAAM;QAC/B;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2070, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-text/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Comment} Comment\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Nodes} Nodes\n * @typedef {import('hast').Parents} Parents\n * @typedef {import('hast').Text} Text\n * @typedef {import('hast-util-is-element').TestFunction} TestFunction\n */\n\n/**\n * @typedef {'normal' | 'nowrap' | 'pre' | 'pre-wrap'} Whitespace\n *   Valid and useful whitespace values (from CSS).\n *\n * @typedef {0 | 1 | 2} BreakNumber\n *   Specific break:\n *\n *   *   `0` — space\n *   *   `1` — line ending\n *   *   `2` — blank line\n *\n * @typedef {'\\n'} BreakForce\n *   Forced break.\n *\n * @typedef {boolean} BreakValue\n *   Whether there was a break.\n *\n * @typedef {BreakNumber | BreakValue | undefined} BreakBefore\n *   Any value for a break before.\n *\n * @typedef {BreakForce | BreakNumber | BreakValue | undefined} BreakAfter\n *   Any value for a break after.\n *\n * @typedef CollectionInfo\n *   Info on current collection.\n * @property {BreakAfter} breakAfter\n *   Whether there was a break after.\n * @property {BreakBefore} breakBefore\n *   Whether there was a break before.\n * @property {Whitespace} whitespace\n *   Current whitespace setting.\n *\n * @typedef Options\n *   Configuration.\n * @property {Whitespace | null | undefined} [whitespace='normal']\n *   Initial CSS whitespace setting to use (default: `'normal'`).\n */\n\nimport {findAfter} from 'unist-util-find-after'\nimport {convertElement} from 'hast-util-is-element'\n\nconst searchLineFeeds = /\\n/g\nconst searchTabOrSpaces = /[\\t ]+/g\n\nconst br = convertElement('br')\nconst cell = convertElement(isCell)\nconst p = convertElement('p')\nconst row = convertElement('tr')\n\n// Note that we don’t need to include void elements here as they don’t have text.\n// See: <https://github.com/wooorm/html-void-elements>\nconst notRendered = convertElement([\n  // List from: <https://html.spec.whatwg.org/multipage/rendering.html#hidden-elements>\n  'datalist',\n  'head',\n  'noembed',\n  'noframes',\n  'noscript', // Act as if we support scripting.\n  'rp',\n  'script',\n  'style',\n  'template',\n  'title',\n  // Hidden attribute.\n  hidden,\n  // From: <https://html.spec.whatwg.org/multipage/rendering.html#flow-content-3>\n  closedDialog\n])\n\n// See: <https://html.spec.whatwg.org/multipage/rendering.html#the-css-user-agent-style-sheet-and-presentational-hints>\nconst blockOrCaption = convertElement([\n  'address', // Flow content\n  'article', // Sections and headings\n  'aside', // Sections and headings\n  'blockquote', // Flow content\n  'body', // Page\n  'caption', // `table-caption`\n  'center', // Flow content (legacy)\n  'dd', // Lists\n  'dialog', // Flow content\n  'dir', // Lists (legacy)\n  'dl', // Lists\n  'dt', // Lists\n  'div', // Flow content\n  'figure', // Flow content\n  'figcaption', // Flow content\n  'footer', // Flow content\n  'form,', // Flow content\n  'h1', // Sections and headings\n  'h2', // Sections and headings\n  'h3', // Sections and headings\n  'h4', // Sections and headings\n  'h5', // Sections and headings\n  'h6', // Sections and headings\n  'header', // Flow content\n  'hgroup', // Sections and headings\n  'hr', // Flow content\n  'html', // Page\n  'legend', // Flow content\n  'li', // Lists (as `display: list-item`)\n  'listing', // Flow content (legacy)\n  'main', // Flow content\n  'menu', // Lists\n  'nav', // Sections and headings\n  'ol', // Lists\n  'p', // Flow content\n  'plaintext', // Flow content (legacy)\n  'pre', // Flow content\n  'section', // Sections and headings\n  'ul', // Lists\n  'xmp' // Flow content (legacy)\n])\n\n/**\n * Get the plain-text value of a node.\n *\n * ###### Algorithm\n *\n * *   if `tree` is a comment, returns its `value`\n * *   if `tree` is a text, applies normal whitespace collapsing to its\n *     `value`, as defined by the CSS Text spec\n * *   if `tree` is a root or element, applies an algorithm similar to the\n *     `innerText` getter as defined by HTML\n *\n * ###### Notes\n *\n * > 👉 **Note**: the algorithm acts as if `tree` is being rendered, and as if\n * > we’re a CSS-supporting user agent, with scripting enabled.\n *\n * *   if `tree` is an element that is not displayed (such as a `head`), we’ll\n *     still use the `innerText` algorithm instead of switching to `textContent`\n * *   if descendants of `tree` are elements that are not displayed, they are\n *     ignored\n * *   CSS is not considered, except for the default user agent style sheet\n * *   a line feed is collapsed instead of ignored in cases where Fullwidth, Wide,\n *     or Halfwidth East Asian Width characters are used, the same goes for a case\n *     with Chinese, Japanese, or Yi writing systems\n * *   replaced elements (such as `audio`) are treated like non-replaced elements\n *\n * @param {Nodes} tree\n *   Tree to turn into text.\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `tree`.\n */\nexport function toText(tree, options) {\n  const options_ = options || {}\n  const children = 'children' in tree ? tree.children : []\n  const block = blockOrCaption(tree)\n  const whitespace = inferWhitespace(tree, {\n    whitespace: options_.whitespace || 'normal',\n    breakBefore: false,\n    breakAfter: false\n  })\n\n  /** @type {Array<BreakNumber | string>} */\n  const results = []\n\n  // Treat `text` and `comment` as having normal white-space.\n  // This deviates from the spec as in the DOM the node’s `.data` has to be\n  // returned.\n  // If you want that behavior use `hast-util-to-string`.\n  // All other nodes are later handled as if they are `element`s (so the\n  // algorithm also works on a `root`).\n  // Nodes without children are treated as a void element, so `doctype` is thus\n  // ignored.\n  if (tree.type === 'text' || tree.type === 'comment') {\n    results.push(\n      ...collectText(tree, {\n        whitespace,\n        breakBefore: true,\n        breakAfter: true\n      })\n    )\n  }\n\n  // 1.  If this element is not being rendered, or if the user agent is a\n  //     non-CSS user agent, then return the same value as the textContent IDL\n  //     attribute on this element.\n  //\n  //     Note: we’re not supporting stylesheets so we’re acting as if the node\n  //     is rendered.\n  //\n  //     If you want that behavior use `hast-util-to-string`.\n  //     Important: we’ll have to account for this later though.\n\n  // 2.  Let results be a new empty list.\n  let index = -1\n\n  // 3.  For each child node node of this element:\n  while (++index < children.length) {\n    // 3.1. Let current be the list resulting in running the inner text\n    //      collection steps with node.\n    //      Each item in results will either be a JavaScript string or a\n    //      positive integer (a required line break count).\n    // 3.2. For each item item in current, append item to results.\n    results.push(\n      ...renderedTextCollection(\n        children[index],\n        // @ts-expect-error: `tree` is a parent if we’re here.\n        tree,\n        {\n          whitespace,\n          breakBefore: index ? undefined : block,\n          breakAfter:\n            index < children.length - 1 ? br(children[index + 1]) : block\n        }\n      )\n    )\n  }\n\n  // 4.  Remove any items from results that are the empty string.\n  // 5.  Remove any runs of consecutive required line break count items at the\n  //     start or end of results.\n  // 6.  Replace each remaining run of consecutive required line break count\n  //     items with a string consisting of as many U+000A LINE FEED (LF)\n  //     characters as the maximum of the values in the required line break\n  //     count items.\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {number | undefined} */\n  let count\n\n  index = -1\n\n  while (++index < results.length) {\n    const value = results[index]\n\n    if (typeof value === 'number') {\n      if (count !== undefined && value > count) count = value\n    } else if (value) {\n      if (count !== undefined && count > -1) {\n        result.push('\\n'.repeat(count) || ' ')\n      }\n\n      count = -1\n      result.push(value)\n    }\n  }\n\n  // 7.  Return the concatenation of the string items in results.\n  return result.join('')\n}\n\n/**\n * <https://html.spec.whatwg.org/multipage/dom.html#rendered-text-collection-steps>\n *\n * @param {Nodes} node\n * @param {Parents} parent\n * @param {CollectionInfo} info\n * @returns {Array<BreakNumber | string>}\n */\nfunction renderedTextCollection(node, parent, info) {\n  if (node.type === 'element') {\n    return collectElement(node, parent, info)\n  }\n\n  if (node.type === 'text') {\n    return info.whitespace === 'normal'\n      ? collectText(node, info)\n      : collectPreText(node)\n  }\n\n  return []\n}\n\n/**\n * Collect an element.\n *\n * @param {Element} node\n *   Element node.\n * @param {Parents} parent\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Array<BreakNumber | string>}\n */\nfunction collectElement(node, parent, info) {\n  // First we infer the `white-space` property.\n  const whitespace = inferWhitespace(node, info)\n  const children = node.children || []\n  let index = -1\n  /** @type {Array<BreakNumber | string>} */\n  let items = []\n\n  // We’re ignoring point 3, and exiting without any content here, because we\n  // deviated from the spec in `toText` at step 3.\n  if (notRendered(node)) {\n    return items\n  }\n\n  /** @type {BreakNumber | undefined} */\n  let prefix\n  /** @type {BreakForce | BreakNumber | undefined} */\n  let suffix\n  // Note: we first detect if there is going to be a break before or after the\n  // contents, as that changes the white-space handling.\n\n  // 2.  If node’s computed value of `visibility` is not `visible`, then return\n  //     items.\n  //\n  //     Note: Ignored, as everything is visible by default user agent styles.\n\n  // 3.  If node is not being rendered, then return items. [...]\n  //\n  //     Note: We already did this above.\n\n  // See `collectText` for step 4.\n\n  // 5.  If node is a `<br>` element, then append a string containing a single\n  //     U+000A LINE FEED (LF) character to items.\n  if (br(node)) {\n    suffix = '\\n'\n  }\n\n  // 7.  If node’s computed value of `display` is `table-row`, and node’s CSS\n  //     box is not the last `table-row` box of the nearest ancestor `table`\n  //     box, then append a string containing a single U+000A LINE FEED (LF)\n  //     character to items.\n  //\n  //     See: <https://html.spec.whatwg.org/multipage/rendering.html#tables-2>\n  //     Note: needs further investigation as this does not account for implicit\n  //     rows.\n  else if (\n    row(node) &&\n    // @ts-expect-error: something up with types of parents.\n    findAfter(parent, node, row)\n  ) {\n    suffix = '\\n'\n  }\n\n  // 8.  If node is a `<p>` element, then append 2 (a required line break count)\n  //     at the beginning and end of items.\n  else if (p(node)) {\n    prefix = 2\n    suffix = 2\n  }\n\n  // 9.  If node’s used value of `display` is block-level or `table-caption`,\n  //     then append 1 (a required line break count) at the beginning and end of\n  //     items.\n  else if (blockOrCaption(node)) {\n    prefix = 1\n    suffix = 1\n  }\n\n  // 1.  Let items be the result of running the inner text collection steps with\n  //     each child node of node in tree order, and then concatenating the\n  //     results to a single list.\n  while (++index < children.length) {\n    items = items.concat(\n      renderedTextCollection(children[index], node, {\n        whitespace,\n        breakBefore: index ? undefined : prefix,\n        breakAfter:\n          index < children.length - 1 ? br(children[index + 1]) : suffix\n      })\n    )\n  }\n\n  // 6.  If node’s computed value of `display` is `table-cell`, and node’s CSS\n  //     box is not the last `table-cell` box of its enclosing `table-row` box,\n  //     then append a string containing a single U+0009 CHARACTER TABULATION\n  //     (tab) character to items.\n  //\n  //     See: <https://html.spec.whatwg.org/multipage/rendering.html#tables-2>\n  if (\n    cell(node) &&\n    // @ts-expect-error: something up with types of parents.\n    findAfter(parent, node, cell)\n  ) {\n    items.push('\\t')\n  }\n\n  // Add the pre- and suffix.\n  if (prefix) items.unshift(prefix)\n  if (suffix) items.push(suffix)\n\n  return items\n}\n\n/**\n * 4.  If node is a Text node, then for each CSS text box produced by node,\n *     in content order, compute the text of the box after application of the\n *     CSS `white-space` processing rules and `text-transform` rules, set\n *     items to the list of the resulting strings, and return items.\n *     The CSS `white-space` processing rules are slightly modified:\n *     collapsible spaces at the end of lines are always collapsed, but they\n *     are only removed if the line is the last line of the block, or it ends\n *     with a br element.\n *     Soft hyphens should be preserved.\n *\n *     Note: See `collectText` and `collectPreText`.\n *     Note: we don’t deal with `text-transform`, no element has that by\n *     default.\n *\n * See: <https://drafts.csswg.org/css-text/#white-space-phase-1>\n *\n * @param {Comment | Text} node\n *   Text node.\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Array<BreakNumber | string>}\n *   Result.\n */\nfunction collectText(node, info) {\n  const value = String(node.value)\n  /** @type {Array<string>} */\n  const lines = []\n  /** @type {Array<BreakNumber | string>} */\n  const result = []\n  let start = 0\n\n  while (start <= value.length) {\n    searchLineFeeds.lastIndex = start\n\n    const match = searchLineFeeds.exec(value)\n    const end = match && 'index' in match ? match.index : value.length\n\n    lines.push(\n      // Any sequence of collapsible spaces and tabs immediately preceding or\n      // following a segment break is removed.\n      trimAndCollapseSpacesAndTabs(\n        // […] ignoring bidi formatting characters (characters with the\n        // Bidi_Control property [UAX9]: ALM, LTR, RTL, LRE-RLO, LRI-PDI) as if\n        // they were not there.\n        value\n          .slice(start, end)\n          .replace(/[\\u061C\\u200E\\u200F\\u202A-\\u202E\\u2066-\\u2069]/g, ''),\n        start === 0 ? info.breakBefore : true,\n        end === value.length ? info.breakAfter : true\n      )\n    )\n\n    start = end + 1\n  }\n\n  // Collapsible segment breaks are transformed for rendering according to the\n  // segment break transformation rules.\n  // So here we jump to 4.1.2 of [CSSTEXT]:\n  // Any collapsible segment break immediately following another collapsible\n  // segment break is removed\n  let index = -1\n  /** @type {BreakNumber | undefined} */\n  let join\n\n  while (++index < lines.length) {\n    // *   If the character immediately before or immediately after the segment\n    //     break is the zero-width space character (U+200B), then the break is\n    //     removed, leaving behind the zero-width space.\n    if (\n      lines[index].charCodeAt(lines[index].length - 1) === 0x20_0b /* ZWSP */ ||\n      (index < lines.length - 1 &&\n        lines[index + 1].charCodeAt(0) === 0x20_0b) /* ZWSP */\n    ) {\n      result.push(lines[index])\n      join = undefined\n    }\n\n    // *   Otherwise, if the East Asian Width property [UAX11] of both the\n    //     character before and after the segment break is Fullwidth, Wide, or\n    //     Halfwidth (not Ambiguous), and neither side is Hangul, then the\n    //     segment break is removed.\n    //\n    //     Note: ignored.\n    // *   Otherwise, if the writing system of the segment break is Chinese,\n    //     Japanese, or Yi, and the character before or after the segment break\n    //     is punctuation or a symbol (Unicode general category P* or S*) and\n    //     has an East Asian Width property of Ambiguous, and the character on\n    //     the other side of the segment break is Fullwidth, Wide, or Halfwidth,\n    //     and not Hangul, then the segment break is removed.\n    //\n    //     Note: ignored.\n\n    // *   Otherwise, the segment break is converted to a space (U+0020).\n    else if (lines[index]) {\n      if (typeof join === 'number') result.push(join)\n      result.push(lines[index])\n      join = 0\n    } else if (index === 0 || index === lines.length - 1) {\n      // If this line is empty, and it’s the first or last, add a space.\n      // Note that this function is only called in normal whitespace, so we\n      // don’t worry about `pre`.\n      result.push(0)\n    }\n  }\n\n  return result\n}\n\n/**\n * Collect a text node as “pre” whitespace.\n *\n * @param {Text} node\n *   Text node.\n * @returns {Array<BreakNumber | string>}\n *   Result.\n */\nfunction collectPreText(node) {\n  return [String(node.value)]\n}\n\n/**\n * 3.  Every collapsible tab is converted to a collapsible space (U+0020).\n * 4.  Any collapsible space immediately following another collapsible\n *     space—even one outside the boundary of the inline containing that\n *     space, provided both spaces are within the same inline formatting\n *     context—is collapsed to have zero advance width. (It is invisible,\n *     but retains its soft wrap opportunity, if any.)\n *\n * @param {string} value\n *   Value to collapse.\n * @param {BreakBefore} breakBefore\n *   Whether there was a break before.\n * @param {BreakAfter} breakAfter\n *   Whether there was a break after.\n * @returns {string}\n *   Result.\n */\nfunction trimAndCollapseSpacesAndTabs(value, breakBefore, breakAfter) {\n  /** @type {Array<string>} */\n  const result = []\n  let start = 0\n  /** @type {number | undefined} */\n  let end\n\n  while (start < value.length) {\n    searchTabOrSpaces.lastIndex = start\n    const match = searchTabOrSpaces.exec(value)\n    end = match ? match.index : value.length\n\n    // If we’re not directly after a segment break, but there was white space,\n    // add an empty value that will be turned into a space.\n    if (!start && !end && match && !breakBefore) {\n      result.push('')\n    }\n\n    if (start !== end) {\n      result.push(value.slice(start, end))\n    }\n\n    start = match ? end + match[0].length : end\n  }\n\n  // If we reached the end, there was trailing white space, and there’s no\n  // segment break after this node, add an empty value that will be turned\n  // into a space.\n  if (start !== end && !breakAfter) {\n    result.push('')\n  }\n\n  return result.join(' ')\n}\n\n/**\n * Figure out the whitespace of a node.\n *\n * We don’t support void elements here (so `nobr wbr` -> `normal` is ignored).\n *\n * @param {Nodes} node\n *   Node (typically `Element`).\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Whitespace}\n *   Applied whitespace.\n */\nfunction inferWhitespace(node, info) {\n  if (node.type === 'element') {\n    const properties = node.properties || {}\n    switch (node.tagName) {\n      case 'listing':\n      case 'plaintext':\n      case 'xmp': {\n        return 'pre'\n      }\n\n      case 'nobr': {\n        return 'nowrap'\n      }\n\n      case 'pre': {\n        return properties.wrap ? 'pre-wrap' : 'pre'\n      }\n\n      case 'td':\n      case 'th': {\n        return properties.noWrap ? 'nowrap' : info.whitespace\n      }\n\n      case 'textarea': {\n        return 'pre-wrap'\n      }\n\n      default:\n    }\n  }\n\n  return info.whitespace\n}\n\n/**\n * @type {TestFunction}\n * @param {Element} node\n * @returns {node is {properties: {hidden: true}}}\n */\nfunction hidden(node) {\n  return Boolean((node.properties || {}).hidden)\n}\n\n/**\n * @type {TestFunction}\n * @param {Element} node\n * @returns {node is {tagName: 'td' | 'th'}}\n */\nfunction isCell(node) {\n  return node.tagName === 'td' || node.tagName === 'th'\n}\n\n/**\n * @type {TestFunction}\n */\nfunction closedDialog(node) {\n  return node.tagName === 'dialog' && !(node.properties || {}).open\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoCC;;;AAED;AACA;;;AAEA,MAAM,kBAAkB;AACxB,MAAM,oBAAoB;AAE1B,MAAM,KAAK,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EAAE;AAC1B,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EAAE;AAC5B,MAAM,IAAI,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EAAE;AACzB,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EAAE;AAE3B,iFAAiF;AACjF,sDAAsD;AACtD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EAAE;IACjC,qFAAqF;IACrF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,oBAAoB;IACpB;IACA,+EAA+E;IAC/E;CACD;AAED,uHAAuH;AACvH,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EAAE;IACpC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM,wBAAwB;CAC/B;AAmCM,SAAS,OAAO,IAAI,EAAE,OAAO;IAClC,MAAM,WAAW,WAAW,CAAC;IAC7B,MAAM,WAAW,cAAc,OAAO,KAAK,QAAQ,GAAG,EAAE;IACxD,MAAM,QAAQ,eAAe;IAC7B,MAAM,aAAa,gBAAgB,MAAM;QACvC,YAAY,SAAS,UAAU,IAAI;QACnC,aAAa;QACb,YAAY;IACd;IAEA,wCAAwC,GACxC,MAAM,UAAU,EAAE;IAElB,2DAA2D;IAC3D,yEAAyE;IACzE,YAAY;IACZ,uDAAuD;IACvD,sEAAsE;IACtE,qCAAqC;IACrC,6EAA6E;IAC7E,WAAW;IACX,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI,KAAK,WAAW;QACnD,QAAQ,IAAI,IACP,YAAY,MAAM;YACnB;YACA,aAAa;YACb,YAAY;QACd;IAEJ;IAEA,uEAAuE;IACvE,4EAA4E;IAC5E,iCAAiC;IACjC,EAAE;IACF,4EAA4E;IAC5E,mBAAmB;IACnB,EAAE;IACF,2DAA2D;IAC3D,8DAA8D;IAE9D,uCAAuC;IACvC,IAAI,QAAQ,CAAC;IAEb,gDAAgD;IAChD,MAAO,EAAE,QAAQ,SAAS,MAAM,CAAE;QAChC,mEAAmE;QACnE,mCAAmC;QACnC,oEAAoE;QACpE,uDAAuD;QACvD,8DAA8D;QAC9D,QAAQ,IAAI,IACP,uBACD,QAAQ,CAAC,MAAM,EACf,sDAAsD;QACtD,MACA;YACE;YACA,aAAa,QAAQ,YAAY;YACjC,YACE,QAAQ,SAAS,MAAM,GAAG,IAAI,GAAG,QAAQ,CAAC,QAAQ,EAAE,IAAI;QAC5D;IAGN;IAEA,+DAA+D;IAC/D,4EAA4E;IAC5E,+BAA+B;IAC/B,0EAA0E;IAC1E,sEAAsE;IACtE,yEAAyE;IACzE,mBAAmB;IACnB,0BAA0B,GAC1B,MAAM,SAAS,EAAE;IACjB,+BAA+B,GAC/B,IAAI;IAEJ,QAAQ,CAAC;IAET,MAAO,EAAE,QAAQ,QAAQ,MAAM,CAAE;QAC/B,MAAM,QAAQ,OAAO,CAAC,MAAM;QAE5B,IAAI,OAAO,UAAU,UAAU;YAC7B,IAAI,UAAU,aAAa,QAAQ,OAAO,QAAQ;QACpD,OAAO,IAAI,OAAO;YAChB,IAAI,UAAU,aAAa,QAAQ,CAAC,GAAG;gBACrC,OAAO,IAAI,CAAC,KAAK,MAAM,CAAC,UAAU;YACpC;YAEA,QAAQ,CAAC;YACT,OAAO,IAAI,CAAC;QACd;IACF;IAEA,+DAA+D;IAC/D,OAAO,OAAO,IAAI,CAAC;AACrB;AAEA;;;;;;;CAOC,GACD,SAAS,uBAAuB,IAAI,EAAE,MAAM,EAAE,IAAI;IAChD,IAAI,KAAK,IAAI,KAAK,WAAW;QAC3B,OAAO,eAAe,MAAM,QAAQ;IACtC;IAEA,IAAI,KAAK,IAAI,KAAK,QAAQ;QACxB,OAAO,KAAK,UAAU,KAAK,WACvB,YAAY,MAAM,QAClB,eAAe;IACrB;IAEA,OAAO,EAAE;AACX;AAEA;;;;;;;;;CASC,GACD,SAAS,eAAe,IAAI,EAAE,MAAM,EAAE,IAAI;IACxC,6CAA6C;IAC7C,MAAM,aAAa,gBAAgB,MAAM;IACzC,MAAM,WAAW,KAAK,QAAQ,IAAI,EAAE;IACpC,IAAI,QAAQ,CAAC;IACb,wCAAwC,GACxC,IAAI,QAAQ,EAAE;IAEd,2EAA2E;IAC3E,gDAAgD;IAChD,IAAI,YAAY,OAAO;QACrB,OAAO;IACT;IAEA,oCAAoC,GACpC,IAAI;IACJ,iDAAiD,GACjD,IAAI;IACJ,4EAA4E;IAC5E,sDAAsD;IAEtD,6EAA6E;IAC7E,aAAa;IACb,EAAE;IACF,4EAA4E;IAE5E,8DAA8D;IAC9D,EAAE;IACF,uCAAuC;IAEvC,gCAAgC;IAEhC,4EAA4E;IAC5E,gDAAgD;IAChD,IAAI,GAAG,OAAO;QACZ,SAAS;IACX,OAUK,IACH,IAAI,SACJ,wDAAwD;IACxD,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,MAAM,MACxB;QACA,SAAS;IACX,OAIK,IAAI,EAAE,OAAO;QAChB,SAAS;QACT,SAAS;IACX,OAKK,IAAI,eAAe,OAAO;QAC7B,SAAS;QACT,SAAS;IACX;IAEA,8EAA8E;IAC9E,wEAAwE;IACxE,gCAAgC;IAChC,MAAO,EAAE,QAAQ,SAAS,MAAM,CAAE;QAChC,QAAQ,MAAM,MAAM,CAClB,uBAAuB,QAAQ,CAAC,MAAM,EAAE,MAAM;YAC5C;YACA,aAAa,QAAQ,YAAY;YACjC,YACE,QAAQ,SAAS,MAAM,GAAG,IAAI,GAAG,QAAQ,CAAC,QAAQ,EAAE,IAAI;QAC5D;IAEJ;IAEA,4EAA4E;IAC5E,6EAA6E;IAC7E,2EAA2E;IAC3E,gCAAgC;IAChC,EAAE;IACF,4EAA4E;IAC5E,IACE,KAAK,SACL,wDAAwD;IACxD,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,MAAM,OACxB;QACA,MAAM,IAAI,CAAC;IACb;IAEA,2BAA2B;IAC3B,IAAI,QAAQ,MAAM,OAAO,CAAC;IAC1B,IAAI,QAAQ,MAAM,IAAI,CAAC;IAEvB,OAAO;AACT;AAEA;;;;;;;;;;;;;;;;;;;;;;;CAuBC,GACD,SAAS,YAAY,IAAI,EAAE,IAAI;IAC7B,MAAM,QAAQ,OAAO,KAAK,KAAK;IAC/B,0BAA0B,GAC1B,MAAM,QAAQ,EAAE;IAChB,wCAAwC,GACxC,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ;IAEZ,MAAO,SAAS,MAAM,MAAM,CAAE;QAC5B,gBAAgB,SAAS,GAAG;QAE5B,MAAM,QAAQ,gBAAgB,IAAI,CAAC;QACnC,MAAM,MAAM,SAAS,WAAW,QAAQ,MAAM,KAAK,GAAG,MAAM,MAAM;QAElE,MAAM,IAAI,CACR,uEAAuE;QACvE,wCAAwC;QACxC,6BACE,+DAA+D;QAC/D,uEAAuE;QACvE,uBAAuB;QACvB,MACG,KAAK,CAAC,OAAO,KACb,OAAO,CAAC,mDAAmD,KAC9D,UAAU,IAAI,KAAK,WAAW,GAAG,MACjC,QAAQ,MAAM,MAAM,GAAG,KAAK,UAAU,GAAG;QAI7C,QAAQ,MAAM;IAChB;IAEA,4EAA4E;IAC5E,sCAAsC;IACtC,yCAAyC;IACzC,0EAA0E;IAC1E,2BAA2B;IAC3B,IAAI,QAAQ,CAAC;IACb,oCAAoC,GACpC,IAAI;IAEJ,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,2EAA2E;QAC3E,0EAA0E;QAC1E,oDAAoD;QACpD,IACE,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,OAAO,QAAQ,QAAQ,OACpE,QAAQ,MAAM,MAAM,GAAG,KACtB,KAAK,CAAC,QAAQ,EAAE,CAAC,UAAU,CAAC,OAAO,SACrC;YACA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;YACxB,OAAO;QACT,OAkBK,IAAI,KAAK,CAAC,MAAM,EAAE;YACrB,IAAI,OAAO,SAAS,UAAU,OAAO,IAAI,CAAC;YAC1C,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;YACxB,OAAO;QACT,OAAO,IAAI,UAAU,KAAK,UAAU,MAAM,MAAM,GAAG,GAAG;YACpD,kEAAkE;YAClE,qEAAqE;YACrE,2BAA2B;YAC3B,OAAO,IAAI,CAAC;QACd;IACF;IAEA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,eAAe,IAAI;IAC1B,OAAO;QAAC,OAAO,KAAK,KAAK;KAAE;AAC7B;AAEA;;;;;;;;;;;;;;;;CAgBC,GACD,SAAS,6BAA6B,KAAK,EAAE,WAAW,EAAE,UAAU;IAClE,0BAA0B,GAC1B,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ;IACZ,+BAA+B,GAC/B,IAAI;IAEJ,MAAO,QAAQ,MAAM,MAAM,CAAE;QAC3B,kBAAkB,SAAS,GAAG;QAC9B,MAAM,QAAQ,kBAAkB,IAAI,CAAC;QACrC,MAAM,QAAQ,MAAM,KAAK,GAAG,MAAM,MAAM;QAExC,0EAA0E;QAC1E,uDAAuD;QACvD,IAAI,CAAC,SAAS,CAAC,OAAO,SAAS,CAAC,aAAa;YAC3C,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,UAAU,KAAK;YACjB,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,OAAO;QACjC;QAEA,QAAQ,QAAQ,MAAM,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG;IAC1C;IAEA,wEAAwE;IACxE,wEAAwE;IACxE,gBAAgB;IAChB,IAAI,UAAU,OAAO,CAAC,YAAY;QAChC,OAAO,IAAI,CAAC;IACd;IAEA,OAAO,OAAO,IAAI,CAAC;AACrB;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,gBAAgB,IAAI,EAAE,IAAI;IACjC,IAAI,KAAK,IAAI,KAAK,WAAW;QAC3B,MAAM,aAAa,KAAK,UAAU,IAAI,CAAC;QACvC,OAAQ,KAAK,OAAO;YAClB,KAAK;YACL,KAAK;YACL,KAAK;gBAAO;oBACV,OAAO;gBACT;YAEA,KAAK;gBAAQ;oBACX,OAAO;gBACT;YAEA,KAAK;gBAAO;oBACV,OAAO,WAAW,IAAI,GAAG,aAAa;gBACxC;YAEA,KAAK;YACL,KAAK;gBAAM;oBACT,OAAO,WAAW,MAAM,GAAG,WAAW,KAAK,UAAU;gBACvD;YAEA,KAAK;gBAAY;oBACf,OAAO;gBACT;YAEA;QACF;IACF;IAEA,OAAO,KAAK,UAAU;AACxB;AAEA;;;;CAIC,GACD,SAAS,OAAO,IAAI;IAClB,OAAO,QAAQ,CAAC,KAAK,UAAU,IAAI,CAAC,CAAC,EAAE,MAAM;AAC/C;AAEA;;;;CAIC,GACD,SAAS,OAAO,IAAI;IAClB,OAAO,KAAK,OAAO,KAAK,QAAQ,KAAK,OAAO,KAAK;AACnD;AAEA;;CAEC,GACD,SAAS,aAAa,IAAI;IACxB,OAAO,KAAK,OAAO,KAAK,YAAY,CAAC,CAAC,KAAK,UAAU,IAAI,CAAC,CAAC,EAAE,IAAI;AACnE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2539, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/trim-trailing-lines/index.js"], "sourcesContent": ["/**\n * Remove final line endings from `value`\n *\n * @param {unknown} value\n *   Value with trailing line endings, coerced to string.\n * @return {string}\n *   Value without trailing line endings.\n */\nexport function trimTrailingLines(value) {\n  const input = String(value)\n  let end = input.length\n\n  while (end > 0) {\n    const code = input.codePointAt(end - 1)\n    if (code !== undefined && (code === 10 || code === 13)) {\n      end--\n    } else {\n      break\n    }\n  }\n\n  return input.slice(0, end)\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACM,SAAS,kBAAkB,KAAK;IACrC,MAAM,QAAQ,OAAO;IACrB,IAAI,MAAM,MAAM,MAAM;IAEtB,MAAO,MAAM,EAAG;QACd,MAAM,OAAO,MAAM,WAAW,CAAC,MAAM;QACrC,IAAI,SAAS,aAAa,CAAC,SAAS,MAAM,SAAS,EAAE,GAAG;YACtD;QACF,OAAO;YACL;QACF;IACF;IAEA,OAAO,MAAM,KAAK,CAAC,GAAG;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2568, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/code.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Code} from 'mdast'\n */\n\nimport {toText} from 'hast-util-to-text'\nimport {trimTrailingLines} from 'trim-trailing-lines'\n\nconst prefix = 'language-'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Code}\n *   mdast node.\n */\nexport function code(state, node) {\n  const children = node.children\n  let index = -1\n  /** @type {Array<number | string> | undefined} */\n  let classList\n  /** @type {string | undefined} */\n  let lang\n\n  if (node.tagName === 'pre') {\n    while (++index < children.length) {\n      const child = children[index]\n\n      if (\n        child.type === 'element' &&\n        child.tagName === 'code' &&\n        child.properties &&\n        child.properties.className &&\n        Array.isArray(child.properties.className)\n      ) {\n        classList = child.properties.className\n        break\n      }\n    }\n  }\n\n  if (classList) {\n    index = -1\n\n    while (++index < classList.length) {\n      if (String(classList[index]).slice(0, prefix.length) === prefix) {\n        lang = String(classList[index]).slice(prefix.length)\n        break\n      }\n    }\n  }\n\n  /** @type {Code} */\n  const result = {\n    type: 'code',\n    lang: lang || null,\n    meta: null,\n    value: trimTrailingLines(toText(node))\n  }\n  state.patch(node, result)\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;AACA;;;AAEA,MAAM,SAAS;AAUR,SAAS,KAAK,KAAK,EAAE,IAAI;IAC9B,MAAM,WAAW,KAAK,QAAQ;IAC9B,IAAI,QAAQ,CAAC;IACb,+CAA+C,GAC/C,IAAI;IACJ,+BAA+B,GAC/B,IAAI;IAEJ,IAAI,KAAK,OAAO,KAAK,OAAO;QAC1B,MAAO,EAAE,QAAQ,SAAS,MAAM,CAAE;YAChC,MAAM,QAAQ,QAAQ,CAAC,MAAM;YAE7B,IACE,MAAM,IAAI,KAAK,aACf,MAAM,OAAO,KAAK,UAClB,MAAM,UAAU,IAChB,MAAM,UAAU,CAAC,SAAS,IAC1B,MAAM,OAAO,CAAC,MAAM,UAAU,CAAC,SAAS,GACxC;gBACA,YAAY,MAAM,UAAU,CAAC,SAAS;gBACtC;YACF;QACF;IACF;IAEA,IAAI,WAAW;QACb,QAAQ,CAAC;QAET,MAAO,EAAE,QAAQ,UAAU,MAAM,CAAE;YACjC,IAAI,OAAO,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,OAAO,MAAM,MAAM,QAAQ;gBAC/D,OAAO,OAAO,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,MAAM;gBACnD;YACF;QACF;IACF;IAEA,iBAAiB,GACjB,MAAM,SAAS;QACb,MAAM;QACN,MAAM,QAAQ;QACd,MAAM;QACN,OAAO,CAAA,GAAA,kJAAA,CAAA,oBAAiB,AAAD,EAAE,CAAA,GAAA,0JAAA,CAAA,SAAM,AAAD,EAAE;IAClC;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2618, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/comment.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Comment} from 'hast'\n * @import {Html} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Comment>} node\n *   hast element to transform.\n * @returns {Html}\n *   mdast node.\n */\nexport function comment(state, node) {\n  /** @type {Html} */\n  const result = {\n    type: 'html',\n    value: '<!--' + node.value + '-->'\n  }\n  state.patch(node, result)\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC;;;AACM,SAAS,QAAQ,KAAK,EAAE,IAAI;IACjC,iBAAiB,GACjB,MAAM,SAAS;QACb,MAAM;QACN,OAAO,SAAS,KAAK,KAAK,GAAG;IAC/B;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2646, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/del.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Delete, PhrasingContent} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Delete}\n *   mdast node.\n */\nexport function del(state, node) {\n  // Allow potentially “invalid” nodes, they might be unknown.\n  // We also support straddling later.\n  const children = /** @type {Array<PhrasingContent>} */ (state.all(node))\n  /** @type {Delete} */\n  const result = {type: 'delete', children}\n  state.patch(node, result)\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC;;;AACM,SAAS,IAAI,KAAK,EAAE,IAAI;IAC7B,4DAA4D;IAC5D,oCAAoC;IACpC,MAAM,WAAkD,MAAM,GAAG,CAAC;IAClE,mBAAmB,GACnB,MAAM,SAAS;QAAC,MAAM;QAAU;IAAQ;IACxC,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2677, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/util/list-items-spread.js"], "sourcesContent": ["/**\n * @import {ListContent} from 'mdast'\n */\n\n/**\n * Infer whether list items are spread.\n *\n * @param {Readonly<Array<Readonly<ListContent>>>} children\n *   List items.\n * @returns {boolean}\n *   Whether one or more list items are spread.\n */\nexport function listItemsSpread(children) {\n  let index = -1\n\n  if (children.length > 1) {\n    while (++index < children.length) {\n      if (children[index].spread) {\n        return true\n      }\n    }\n  }\n\n  return false\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;;;;CAOC;;;AACM,SAAS,gBAAgB,QAAQ;IACtC,IAAI,QAAQ,CAAC;IAEb,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,MAAO,EAAE,QAAQ,SAAS,MAAM,CAAE;YAChC,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE;gBAC1B,OAAO;YACT;QACF;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2706, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/dl.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {ElementContent, Element} from 'hast'\n * @import {BlockContent, DefinitionContent, ListContent, ListItem, List} from 'mdast'\n */\n\n/**\n * @typedef Group\n *   Title/definition group.\n * @property {Array<Element>} titles\n *   One or more titles.\n * @property {Array<ElementContent>} definitions\n *   One or more definitions.\n */\n\nimport {listItemsSpread} from '../util/list-items-spread.js'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {List | undefined}\n *   mdast node.\n */\nexport function dl(state, node) {\n  /** @type {Array<ElementContent>} */\n  const clean = []\n  /** @type {Array<Group>} */\n  const groups = []\n  let index = -1\n\n  // Unwrap `<div>`s\n  while (++index < node.children.length) {\n    const child = node.children[index]\n\n    if (child.type === 'element' && child.tagName === 'div') {\n      clean.push(...child.children)\n    } else {\n      clean.push(child)\n    }\n  }\n\n  /** @type {Group} */\n  let group = {definitions: [], titles: []}\n  index = -1\n\n  // Group titles and definitions.\n  while (++index < clean.length) {\n    const child = clean[index]\n\n    if (child.type === 'element' && child.tagName === 'dt') {\n      const previous = clean[index - 1]\n\n      if (\n        previous &&\n        previous.type === 'element' &&\n        previous.tagName === 'dd'\n      ) {\n        groups.push(group)\n        group = {definitions: [], titles: []}\n      }\n\n      group.titles.push(child)\n    } else {\n      group.definitions.push(child)\n    }\n  }\n\n  groups.push(group)\n\n  // Create items.\n  index = -1\n  /** @type {Array<ListContent>} */\n  const content = []\n\n  while (++index < groups.length) {\n    const result = [\n      ...handle(state, groups[index].titles),\n      ...handle(state, groups[index].definitions)\n    ]\n\n    if (result.length > 0) {\n      content.push({\n        type: 'listItem',\n        spread: result.length > 1,\n        checked: null,\n        children: result\n      })\n    }\n  }\n\n  // Create a list if there are items.\n  if (content.length > 0) {\n    /** @type {List} */\n    const result = {\n      type: 'list',\n      ordered: false,\n      start: null,\n      spread: listItemsSpread(content),\n      children: content\n    }\n    state.patch(node, result)\n    return result\n  }\n}\n\n/**\n * @param {State} state\n *   State.\n * @param {Array<ElementContent>} children\n *   hast element children to transform.\n * @returns {Array<BlockContent | DefinitionContent>}\n *   mdast nodes.\n */\nfunction handle(state, children) {\n  const nodes = state.all({type: 'root', children})\n  const listItems = state.toSpecificContent(nodes, create)\n\n  if (listItems.length === 0) {\n    return []\n  }\n\n  if (listItems.length === 1) {\n    return listItems[0].children\n  }\n\n  return [\n    {\n      type: 'list',\n      ordered: false,\n      start: null,\n      spread: listItemsSpread(listItems),\n      children: listItems\n    }\n  ]\n}\n\n/**\n * @returns {ListItem}\n */\nfunction create() {\n  return {type: 'listItem', spread: false, checked: null, children: []}\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC;;;AAED;;AAUO,SAAS,GAAG,KAAK,EAAE,IAAI;IAC5B,kCAAkC,GAClC,MAAM,QAAQ,EAAE;IAChB,yBAAyB,GACzB,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,CAAC;IAEb,kBAAkB;IAClB,MAAO,EAAE,QAAQ,KAAK,QAAQ,CAAC,MAAM,CAAE;QACrC,MAAM,QAAQ,KAAK,QAAQ,CAAC,MAAM;QAElC,IAAI,MAAM,IAAI,KAAK,aAAa,MAAM,OAAO,KAAK,OAAO;YACvD,MAAM,IAAI,IAAI,MAAM,QAAQ;QAC9B,OAAO;YACL,MAAM,IAAI,CAAC;QACb;IACF;IAEA,kBAAkB,GAClB,IAAI,QAAQ;QAAC,aAAa,EAAE;QAAE,QAAQ,EAAE;IAAA;IACxC,QAAQ,CAAC;IAET,gCAAgC;IAChC,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,MAAM,QAAQ,KAAK,CAAC,MAAM;QAE1B,IAAI,MAAM,IAAI,KAAK,aAAa,MAAM,OAAO,KAAK,MAAM;YACtD,MAAM,WAAW,KAAK,CAAC,QAAQ,EAAE;YAEjC,IACE,YACA,SAAS,IAAI,KAAK,aAClB,SAAS,OAAO,KAAK,MACrB;gBACA,OAAO,IAAI,CAAC;gBACZ,QAAQ;oBAAC,aAAa,EAAE;oBAAE,QAAQ,EAAE;gBAAA;YACtC;YAEA,MAAM,MAAM,CAAC,IAAI,CAAC;QACpB,OAAO;YACL,MAAM,WAAW,CAAC,IAAI,CAAC;QACzB;IACF;IAEA,OAAO,IAAI,CAAC;IAEZ,gBAAgB;IAChB,QAAQ,CAAC;IACT,+BAA+B,GAC/B,MAAM,UAAU,EAAE;IAElB,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;QAC9B,MAAM,SAAS;eACV,OAAO,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM;eAClC,OAAO,OAAO,MAAM,CAAC,MAAM,CAAC,WAAW;SAC3C;QAED,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,QAAQ,IAAI,CAAC;gBACX,MAAM;gBACN,QAAQ,OAAO,MAAM,GAAG;gBACxB,SAAS;gBACT,UAAU;YACZ;QACF;IACF;IAEA,oCAAoC;IACpC,IAAI,QAAQ,MAAM,GAAG,GAAG;QACtB,iBAAiB,GACjB,MAAM,SAAS;YACb,MAAM;YACN,SAAS;YACT,OAAO;YACP,QAAQ,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE;YACxB,UAAU;QACZ;QACA,MAAM,KAAK,CAAC,MAAM;QAClB,OAAO;IACT;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,OAAO,KAAK,EAAE,QAAQ;IAC7B,MAAM,QAAQ,MAAM,GAAG,CAAC;QAAC,MAAM;QAAQ;IAAQ;IAC/C,MAAM,YAAY,MAAM,iBAAiB,CAAC,OAAO;IAEjD,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,OAAO,EAAE;IACX;IAEA,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,OAAO,SAAS,CAAC,EAAE,CAAC,QAAQ;IAC9B;IAEA,OAAO;QACL;YACE,MAAM;YACN,SAAS;YACT,OAAO;YACP,QAAQ,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE;YACxB,UAAU;QACZ;KACD;AACH;AAEA;;CAEC,GACD,SAAS;IACP,OAAO;QAAC,MAAM;QAAY,QAAQ;QAAO,SAAS;QAAM,UAAU,EAAE;IAAA;AACtE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2833, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/em.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Emphasis, PhrasingContent} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Emphasis}\n *   mdast node.\n */\nexport function em(state, node) {\n  // Allow potentially “invalid” nodes, they might be unknown.\n  // We also support straddling later.\n  const children = /** @type {Array<PhrasingContent>} */ (state.all(node))\n\n  /** @type {Emphasis} */\n  const result = {type: 'emphasis', children}\n  state.patch(node, result)\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC;;;AACM,SAAS,GAAG,KAAK,EAAE,IAAI;IAC5B,4DAA4D;IAC5D,oCAAoC;IACpC,MAAM,WAAkD,MAAM,GAAG,CAAC;IAElE,qBAAqB,GACrB,MAAM,SAAS;QAAC,MAAM;QAAY;IAAQ;IAC1C,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2864, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/util/drop-surrounding-breaks.js"], "sourcesContent": ["/**\n * @import {Nodes} from 'mdast'\n */\n\n/**\n * Drop trailing initial and final `br`s.\n *\n * @template {Nodes} Node\n *   Node type.\n * @param {Array<Node>} nodes\n *   List of nodes.\n * @returns {Array<Node>}\n *   List of nodes w/o `break`s.\n */\nexport function dropSurroundingBreaks(nodes) {\n  let start = 0\n  let end = nodes.length\n\n  while (start < end && nodes[start].type === 'break') start++\n  while (end > start && nodes[end - 1].type === 'break') end--\n\n  return start === 0 && end === nodes.length ? nodes : nodes.slice(start, end)\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;;;;;;CASC;;;AACM,SAAS,sBAAsB,KAAK;IACzC,IAAI,QAAQ;IACZ,IAAI,MAAM,MAAM,MAAM;IAEtB,MAAO,QAAQ,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,QAAS;IACrD,MAAO,MAAM,SAAS,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,KAAK,QAAS;IAEvD,OAAO,UAAU,KAAK,QAAQ,MAAM,MAAM,GAAG,QAAQ,MAAM,KAAK,CAAC,OAAO;AAC1E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2891, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/heading.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Heading, PhrasingContent} from 'mdast'\n */\n\nimport {dropSurroundingBreaks} from '../util/drop-surrounding-breaks.js'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Heading}\n *   mdast node.\n */\nexport function heading(state, node) {\n  const depth = /** @type {Heading['depth']} */ (\n    /* c8 ignore next */\n    Number(node.tagName.charAt(1)) || 1\n  )\n  const children = dropSurroundingBreaks(\n    /** @type {Array<PhrasingContent>} */ (state.all(node))\n  )\n\n  /** @type {Heading} */\n  const result = {type: 'heading', depth, children}\n  state.patch(node, result)\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;;AAUO,SAAS,QAAQ,KAAK,EAAE,IAAI;IACjC,MAAM,QACJ,kBAAkB,GAClB,OAAO,KAAK,OAAO,CAAC,MAAM,CAAC,OAAO;IAEpC,MAAM,WAAW,CAAA,GAAA,2LAAA,CAAA,wBAAqB,AAAD,EACI,MAAM,GAAG,CAAC;IAGnD,oBAAoB,GACpB,MAAM,SAAS;QAAC,MAAM;QAAW;QAAO;IAAQ;IAChD,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2917, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/hr.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {ThematicBreak} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {ThematicBreak}\n *   mdast node.\n */\nexport function hr(state, node) {\n  /** @type {ThematicBreak} */\n  const result = {type: 'thematicBreak'}\n  state.patch(node, result)\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC;;;AACM,SAAS,GAAG,KAAK,EAAE,IAAI;IAC5B,0BAA0B,GAC1B,MAAM,SAAS;QAAC,MAAM;IAAe;IACrC,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2944, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/iframe.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Link} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Link | undefined}\n *   mdast node.\n */\nexport function iframe(state, node) {\n  const properties = node.properties || {}\n  const source = String(properties.src || '')\n  const title = String(properties.title || '')\n\n  // Only create a link if there is a title.\n  // We can’t use the content of the frame because conforming HTML parsers treat\n  // it as text, whereas legacy parsers treat it as HTML, so it will likely\n  // contain tags that will show up in text.\n  if (source && title) {\n    /** @type {Link} */\n    const result = {\n      type: 'link',\n      title: null,\n      url: state.resolve(source),\n      children: [{type: 'text', value: title}]\n    }\n    state.patch(node, result)\n    return result\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC;;;AACM,SAAS,OAAO,KAAK,EAAE,IAAI;IAChC,MAAM,aAAa,KAAK,UAAU,IAAI,CAAC;IACvC,MAAM,SAAS,OAAO,WAAW,GAAG,IAAI;IACxC,MAAM,QAAQ,OAAO,WAAW,KAAK,IAAI;IAEzC,0CAA0C;IAC1C,8EAA8E;IAC9E,yEAAyE;IACzE,0CAA0C;IAC1C,IAAI,UAAU,OAAO;QACnB,iBAAiB,GACjB,MAAM,SAAS;YACb,MAAM;YACN,OAAO;YACP,KAAK,MAAM,OAAO,CAAC;YACnB,UAAU;gBAAC;oBAAC,MAAM;oBAAQ,OAAO;gBAAK;aAAE;QAC1C;QACA,MAAM,KAAK,CAAC,MAAM;QAClB,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2988, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/img.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Image} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Image}\n *   mdast node.\n */\nexport function img(state, node) {\n  const properties = node.properties || {}\n\n  /** @type {Image} */\n  const result = {\n    type: 'image',\n    url: state.resolve(String(properties.src || '') || null),\n    title: properties.title ? String(properties.title) : null,\n    alt: properties.alt ? String(properties.alt) : ''\n  }\n  state.patch(node, result)\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC;;;AACM,SAAS,IAAI,KAAK,EAAE,IAAI;IAC7B,MAAM,aAAa,KAAK,UAAU,IAAI,CAAC;IAEvC,kBAAkB,GAClB,MAAM,SAAS;QACb,MAAM;QACN,KAAK,MAAM,OAAO,CAAC,OAAO,WAAW,GAAG,IAAI,OAAO;QACnD,OAAO,WAAW,KAAK,GAAG,OAAO,WAAW,KAAK,IAAI;QACrD,KAAK,WAAW,GAAG,GAAG,OAAO,WAAW,GAAG,IAAI;IACjD;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3019, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/inline-code.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {InlineCode} from 'mdast'\n */\n\nimport {toText} from 'hast-util-to-text'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {InlineCode}\n *   mdast node.\n */\nexport function inlineCode(state, node) {\n  /** @type {InlineCode} */\n  const result = {type: 'inlineCode', value: toText(node)}\n  state.patch(node, result)\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;;AAUO,SAAS,WAAW,KAAK,EAAE,IAAI;IACpC,uBAAuB,GACvB,MAAM,SAAS;QAAC,MAAM;QAAc,OAAO,CAAA,GAAA,0JAAA,CAAA,SAAM,AAAD,EAAE;IAAK;IACvD,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3042, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/util/find-selected-options.js"], "sourcesContent": ["/**\n * @import {Element, Properties} from 'hast'\n */\n\n/**\n * @typedef {[string, Value]} Option\n *   Option, where the item at `0` is the label, the item at `1` the value.\n *\n * @typedef {Array<Option>} Options\n *   List of options.\n *\n * @typedef {string | undefined} Value\n *   `value` field of option.\n */\n\nimport {toText} from 'hast-util-to-text'\n\n/**\n * @param {Readonly<Element>} node\n *   hast element to inspect.\n * @param {Properties | undefined} [explicitProperties]\n *   Properties to use, normally taken from `node`, but can be changed.\n * @returns {Options}\n *   Options.\n */\nexport function findSelectedOptions(node, explicitProperties) {\n  /** @type {Array<Element>} */\n  const selectedOptions = []\n  /** @type {Options} */\n  const values = []\n  const properties = explicitProperties || node.properties || {}\n  const options = findOptions(node)\n  const size =\n    Math.min(Number.parseInt(String(properties.size), 10), 0) ||\n    (properties.multiple ? 4 : 1)\n  let index = -1\n\n  while (++index < options.length) {\n    const option = options[index]\n\n    if (option && option.properties && option.properties.selected) {\n      selectedOptions.push(option)\n    }\n  }\n\n  const list = selectedOptions.length > 0 ? selectedOptions : options\n  const max = Math.min(list.length, size)\n  index = -1\n\n  while (++index < max) {\n    const option = list[index]\n    const properties = option.properties || {}\n    const content = toText(option)\n    const label = content || String(properties.label || '')\n    const value = String(properties.value || '') || content\n    values.push([value, label === value ? undefined : label])\n  }\n\n  return values\n}\n\n/**\n * @param {Element} node\n *   Parent to find in.\n * @returns {Array<Element>}\n *   Option elements.\n */\nfunction findOptions(node) {\n  /** @type {Array<Element>} */\n  const results = []\n  let index = -1\n\n  while (++index < node.children.length) {\n    const child = node.children[index]\n\n    if ('children' in child && Array.isArray(child.children)) {\n      results.push(...findOptions(child))\n    }\n\n    if (\n      child.type === 'element' &&\n      child.tagName === 'option' &&\n      (!child.properties || !child.properties.disabled)\n    ) {\n      results.push(child)\n    }\n  }\n\n  return results\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;;;;;;CASC;;;AAED;;AAUO,SAAS,oBAAoB,IAAI,EAAE,kBAAkB;IAC1D,2BAA2B,GAC3B,MAAM,kBAAkB,EAAE;IAC1B,oBAAoB,GACpB,MAAM,SAAS,EAAE;IACjB,MAAM,aAAa,sBAAsB,KAAK,UAAU,IAAI,CAAC;IAC7D,MAAM,UAAU,YAAY;IAC5B,MAAM,OACJ,KAAK,GAAG,CAAC,OAAO,QAAQ,CAAC,OAAO,WAAW,IAAI,GAAG,KAAK,MACvD,CAAC,WAAW,QAAQ,GAAG,IAAI,CAAC;IAC9B,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,QAAQ,MAAM,CAAE;QAC/B,MAAM,SAAS,OAAO,CAAC,MAAM;QAE7B,IAAI,UAAU,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,QAAQ,EAAE;YAC7D,gBAAgB,IAAI,CAAC;QACvB;IACF;IAEA,MAAM,OAAO,gBAAgB,MAAM,GAAG,IAAI,kBAAkB;IAC5D,MAAM,MAAM,KAAK,GAAG,CAAC,KAAK,MAAM,EAAE;IAClC,QAAQ,CAAC;IAET,MAAO,EAAE,QAAQ,IAAK;QACpB,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,MAAM,aAAa,OAAO,UAAU,IAAI,CAAC;QACzC,MAAM,UAAU,CAAA,GAAA,0JAAA,CAAA,SAAM,AAAD,EAAE;QACvB,MAAM,QAAQ,WAAW,OAAO,WAAW,KAAK,IAAI;QACpD,MAAM,QAAQ,OAAO,WAAW,KAAK,IAAI,OAAO;QAChD,OAAO,IAAI,CAAC;YAAC;YAAO,UAAU,QAAQ,YAAY;SAAM;IAC1D;IAEA,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,YAAY,IAAI;IACvB,2BAA2B,GAC3B,MAAM,UAAU,EAAE;IAClB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,KAAK,QAAQ,CAAC,MAAM,CAAE;QACrC,MAAM,QAAQ,KAAK,QAAQ,CAAC,MAAM;QAElC,IAAI,cAAc,SAAS,MAAM,OAAO,CAAC,MAAM,QAAQ,GAAG;YACxD,QAAQ,IAAI,IAAI,YAAY;QAC9B;QAEA,IACE,MAAM,IAAI,KAAK,aACf,MAAM,OAAO,KAAK,YAClB,CAAC,CAAC,MAAM,UAAU,IAAI,CAAC,MAAM,UAAU,CAAC,QAAQ,GAChD;YACA,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3112, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/input.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Image, Link, Text} from 'mdast'\n * @import {Options} from '../util/find-selected-options.js'\n */\n\nimport {findSelectedOptions} from '../util/find-selected-options.js'\n\nconst defaultChecked = '[x]'\nconst defaultUnchecked = '[ ]'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Array<Link | Text> | Image | Text | undefined}\n *   mdast node.\n */\n// eslint-disable-next-line complexity\nexport function input(state, node) {\n  const properties = node.properties || {}\n  const value = String(properties.value || properties.placeholder || '')\n\n  if (\n    properties.disabled ||\n    properties.type === 'hidden' ||\n    properties.type === 'file'\n  ) {\n    return\n  }\n\n  if (properties.type === 'checkbox' || properties.type === 'radio') {\n    /** @type {Text} */\n    const result = {\n      type: 'text',\n      value: properties.checked\n        ? state.options.checked || defaultChecked\n        : state.options.unchecked || defaultUnchecked\n    }\n    state.patch(node, result)\n    return result\n  }\n\n  if (properties.type === 'image') {\n    const alt = properties.alt || value\n\n    if (alt) {\n      /** @type {Image} */\n      const result = {\n        type: 'image',\n        url: state.resolve(String(properties.src || '') || null),\n        title: String(properties.title || '') || null,\n        alt: String(alt)\n      }\n      state.patch(node, result)\n      return result\n    }\n\n    return\n  }\n\n  /** @type {Options} */\n  let values = []\n\n  if (value) {\n    values = [[value, undefined]]\n  } else if (\n    // `list` is not supported on these types:\n    properties.type !== 'button' &&\n    properties.type !== 'file' &&\n    properties.type !== 'password' &&\n    properties.type !== 'reset' &&\n    properties.type !== 'submit' &&\n    properties.list\n  ) {\n    const list = String(properties.list)\n    const datalist = state.elementById.get(list)\n\n    if (datalist && datalist.tagName === 'datalist') {\n      values = findSelectedOptions(datalist, properties)\n    }\n  }\n\n  if (values.length === 0) {\n    return\n  }\n\n  // Hide password value.\n  if (properties.type === 'password') {\n    // Passwords don’t support `list`.\n    values[0] = ['•'.repeat(values[0][0].length), undefined]\n  }\n\n  if (properties.type === 'email' || properties.type === 'url') {\n    /** @type {Array<Link | Text>} */\n    const results = []\n    let index = -1\n\n    while (++index < values.length) {\n      const value = state.resolve(values[index][0])\n      /** @type {Link} */\n      const result = {\n        type: 'link',\n        title: null,\n        url: properties.type === 'email' ? 'mailto:' + value : value,\n        children: [{type: 'text', value: values[index][1] || value}]\n      }\n\n      results.push(result)\n\n      if (index !== values.length - 1) {\n        results.push({type: 'text', value: ', '})\n      }\n    }\n\n    return results\n  }\n\n  /** @type {Array<string>} */\n  const texts = []\n  let index = -1\n\n  while (++index < values.length) {\n    texts.push(\n      values[index][1]\n        ? values[index][1] + ' (' + values[index][0] + ')'\n        : values[index][0]\n    )\n  }\n\n  /** @type {Text} */\n  const result = {type: 'text', value: texts.join(', ')}\n  state.patch(node, result)\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAED;;AAEA,MAAM,iBAAiB;AACvB,MAAM,mBAAmB;AAWlB,SAAS,MAAM,KAAK,EAAE,IAAI;IAC/B,MAAM,aAAa,KAAK,UAAU,IAAI,CAAC;IACvC,MAAM,QAAQ,OAAO,WAAW,KAAK,IAAI,WAAW,WAAW,IAAI;IAEnE,IACE,WAAW,QAAQ,IACnB,WAAW,IAAI,KAAK,YACpB,WAAW,IAAI,KAAK,QACpB;QACA;IACF;IAEA,IAAI,WAAW,IAAI,KAAK,cAAc,WAAW,IAAI,KAAK,SAAS;QACjE,iBAAiB,GACjB,MAAM,SAAS;YACb,MAAM;YACN,OAAO,WAAW,OAAO,GACrB,MAAM,OAAO,CAAC,OAAO,IAAI,iBACzB,MAAM,OAAO,CAAC,SAAS,IAAI;QACjC;QACA,MAAM,KAAK,CAAC,MAAM;QAClB,OAAO;IACT;IAEA,IAAI,WAAW,IAAI,KAAK,SAAS;QAC/B,MAAM,MAAM,WAAW,GAAG,IAAI;QAE9B,IAAI,KAAK;YACP,kBAAkB,GAClB,MAAM,SAAS;gBACb,MAAM;gBACN,KAAK,MAAM,OAAO,CAAC,OAAO,WAAW,GAAG,IAAI,OAAO;gBACnD,OAAO,OAAO,WAAW,KAAK,IAAI,OAAO;gBACzC,KAAK,OAAO;YACd;YACA,MAAM,KAAK,CAAC,MAAM;YAClB,OAAO;QACT;QAEA;IACF;IAEA,oBAAoB,GACpB,IAAI,SAAS,EAAE;IAEf,IAAI,OAAO;QACT,SAAS;YAAC;gBAAC;gBAAO;aAAU;SAAC;IAC/B,OAAO,IACL,0CAA0C;IAC1C,WAAW,IAAI,KAAK,YACpB,WAAW,IAAI,KAAK,UACpB,WAAW,IAAI,KAAK,cACpB,WAAW,IAAI,KAAK,WACpB,WAAW,IAAI,KAAK,YACpB,WAAW,IAAI,EACf;QACA,MAAM,OAAO,OAAO,WAAW,IAAI;QACnC,MAAM,WAAW,MAAM,WAAW,CAAC,GAAG,CAAC;QAEvC,IAAI,YAAY,SAAS,OAAO,KAAK,YAAY;YAC/C,SAAS,CAAA,GAAA,yLAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU;QACzC;IACF;IAEA,IAAI,OAAO,MAAM,KAAK,GAAG;QACvB;IACF;IAEA,uBAAuB;IACvB,IAAI,WAAW,IAAI,KAAK,YAAY;QAClC,kCAAkC;QAClC,MAAM,CAAC,EAAE,GAAG;YAAC,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM;YAAG;SAAU;IAC1D;IAEA,IAAI,WAAW,IAAI,KAAK,WAAW,WAAW,IAAI,KAAK,OAAO;QAC5D,+BAA+B,GAC/B,MAAM,UAAU,EAAE;QAClB,IAAI,QAAQ,CAAC;QAEb,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;YAC9B,MAAM,QAAQ,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC5C,iBAAiB,GACjB,MAAM,SAAS;gBACb,MAAM;gBACN,OAAO;gBACP,KAAK,WAAW,IAAI,KAAK,UAAU,YAAY,QAAQ;gBACvD,UAAU;oBAAC;wBAAC,MAAM;wBAAQ,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,IAAI;oBAAK;iBAAE;YAC9D;YAEA,QAAQ,IAAI,CAAC;YAEb,IAAI,UAAU,OAAO,MAAM,GAAG,GAAG;gBAC/B,QAAQ,IAAI,CAAC;oBAAC,MAAM;oBAAQ,OAAO;gBAAI;YACzC;QACF;QAEA,OAAO;IACT;IAEA,0BAA0B,GAC1B,MAAM,QAAQ,EAAE;IAChB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;QAC9B,MAAM,IAAI,CACR,MAAM,CAAC,MAAM,CAAC,EAAE,GACZ,MAAM,CAAC,MAAM,CAAC,EAAE,GAAG,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,GAAG,MAC7C,MAAM,CAAC,MAAM,CAAC,EAAE;IAExB;IAEA,iBAAiB,GACjB,MAAM,SAAS;QAAC,MAAM;QAAQ,OAAO,MAAM,IAAI,CAAC;IAAK;IACrD,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3223, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-has-property/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Nodes} Nodes\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Check if `node` is an element and has a `name` property.\n *\n * @template {string} Key\n *   Type of key.\n * @param {Nodes} node\n *   Node to check (typically `Element`).\n * @param {Key} name\n *   Property name to check.\n * @returns {node is Element & {properties: Record<Key, Array<number | string> | number | string | true>}}}\n *   Whether `node` is an element that has a `name` property.\n *\n *   Note: see <https://github.com/DefinitelyTyped/DefinitelyTyped/blob/27c9274/types/hast/index.d.ts#L37C29-L37C98>.\n */\nexport function hasProperty(node, name) {\n  const value =\n    node.type === 'element' &&\n    own.call(node.properties, name) &&\n    node.properties[name]\n\n  return value !== null && value !== undefined && value !== false\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED,MAAM,MAAM,CAAC,EAAE,cAAc;AAgBtB,SAAS,YAAY,IAAI,EAAE,IAAI;IACpC,MAAM,QACJ,KAAK,IAAI,KAAK,aACd,IAAI,IAAI,CAAC,KAAK,UAAU,EAAE,SAC1B,KAAK,UAAU,CAAC,KAAK;IAEvB,OAAO,UAAU,QAAQ,UAAU,aAAa,UAAU;AAC5D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3240, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-is-body-ok-link/lib/index.js"], "sourcesContent": ["/**\n * @import {Nodes} from 'hast'\n */\n\nconst list = new Set(['pingback', 'prefetch', 'stylesheet'])\n\n/**\n * Checks whether a node is a “body OK” link.\n *\n * @param {Nodes} node\n *   Node to check.\n * @returns {boolean}\n *   Whether `node` is a “body OK” link.\n */\nexport function isBodyOkLink(node) {\n  if (node.type !== 'element' || node.tagName !== 'link') {\n    return false\n  }\n\n  if (node.properties.itemProp) {\n    return true\n  }\n\n  const value = node.properties.rel\n  let index = -1\n\n  if (!Array.isArray(value) || value.length === 0) {\n    return false\n  }\n\n  while (++index < value.length) {\n    if (!list.has(String(value[index]))) {\n      return false\n    }\n  }\n\n  return true\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED,MAAM,OAAO,IAAI,IAAI;IAAC;IAAY;IAAY;CAAa;AAUpD,SAAS,aAAa,IAAI;IAC/B,IAAI,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK,QAAQ;QACtD,OAAO;IACT;IAEA,IAAI,KAAK,UAAU,CAAC,QAAQ,EAAE;QAC5B,OAAO;IACT;IAEA,MAAM,QAAQ,KAAK,UAAU,CAAC,GAAG;IACjC,IAAI,QAAQ,CAAC;IAEb,IAAI,CAAC,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK,GAAG;QAC/C,OAAO;IACT;IAEA,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO,KAAK,CAAC,MAAM,IAAI;YACnC,OAAO;QACT;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3275, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-phrasing/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Nodes} Nodes\n */\n\nimport {embedded} from 'hast-util-embedded'\nimport {hasProperty} from 'hast-util-has-property'\nimport {isBodyOkLink} from 'hast-util-is-body-ok-link'\nimport {convertElement} from 'hast-util-is-element'\n\nconst basic = convertElement([\n  'a',\n  'abbr',\n  // `area` is in fact only phrasing if it is inside a `map` element.\n  // However, since `area`s are required to be inside a `map` element, and it’s\n  // a rather involved check, it’s ignored here for now.\n  'area',\n  'b',\n  'bdi',\n  'bdo',\n  'br',\n  'button',\n  'cite',\n  'code',\n  'data',\n  'datalist',\n  'del',\n  'dfn',\n  'em',\n  'i',\n  'input',\n  'ins',\n  'kbd',\n  'keygen',\n  'label',\n  'map',\n  'mark',\n  'meter',\n  'noscript',\n  'output',\n  'progress',\n  'q',\n  'ruby',\n  's',\n  'samp',\n  'script',\n  'select',\n  'small',\n  'span',\n  'strong',\n  'sub',\n  'sup',\n  'template',\n  'textarea',\n  'time',\n  'u',\n  'var',\n  'wbr'\n])\n\nconst meta = convertElement('meta')\n\n/**\n * Check if the given value is *phrasing* content.\n *\n * @param {Nodes} value\n *   Node to check.\n * @returns {boolean}\n *   Whether `value` is phrasing content.\n */\nexport function phrasing(value) {\n  return Boolean(\n    value.type === 'text' ||\n      basic(value) ||\n      embedded(value) ||\n      isBodyOkLink(value) ||\n      (meta(value) && hasProperty(value, 'itemProp'))\n  )\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;AACA;AACA;AACA;;;;;AAEA,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EAAE;IAC3B;IACA;IACA,mEAAmE;IACnE,6EAA6E;IAC7E,sDAAsD;IACtD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EAAE;AAUrB,SAAS,SAAS,KAAK;IAC5B,OAAO,QACL,MAAM,IAAI,KAAK,UACb,MAAM,UACN,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,UACT,CAAA,GAAA,wKAAA,CAAA,eAAY,AAAD,EAAE,UACZ,KAAK,UAAU,CAAA,GAAA,+JAAA,CAAA,cAAW,AAAD,EAAE,OAAO;AAEzC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3347, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/li.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {ListItem} from 'mdast'\n */\n\n/**\n * @typedef ExtractResult\n *   Result of extracting a leading checkbox.\n * @property {Element | undefined} checkbox\n *   The checkbox that was removed, if any.\n * @property {Element} rest\n *   If there was a leading checkbox, a deep clone of the node w/o the leading\n *   checkbox; otherwise a reference to the given, untouched, node.\n */\n\nimport {phrasing} from 'hast-util-phrasing'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {ListItem}\n *   mdast node.\n */\nexport function li(state, node) {\n  // If the list item starts with a checkbox, remove the checkbox and mark the\n  // list item as a GFM task list item.\n  const {rest, checkbox} = extractLeadingCheckbox(node)\n  const checked = checkbox ? Boolean(checkbox.properties.checked) : null\n  const spread = spreadout(rest)\n  const children = state.toFlow(state.all(rest))\n\n  /** @type {ListItem} */\n  const result = {type: 'listItem', spread, checked, children}\n  state.patch(node, result)\n  return result\n}\n\n/**\n * Check if an element should spread out.\n *\n * The reason to spread out a markdown list item is primarily whether writing\n * the equivalent in markdown, would yield a spread out item.\n *\n * A spread out item results in `<p>` and `</p>` tags.\n * Otherwise, the phrasing would be output directly.\n * We can check for that: if there’s a `<p>` element, spread it out.\n *\n * But what if there are no paragraphs?\n * In that case, we can also assume that if two “block” things were written in\n * an item, that it is spread out, because blocks are typically joined by blank\n * lines, which also means a spread item.\n *\n * Lastly, because in HTML things can be wrapped in a `<div>` or similar, we\n * delve into non-phrasing elements here to figure out if they themselves\n * contain paragraphs or 2 or more flow non-phrasing elements.\n *\n * @param {Readonly<Element>} node\n * @returns {boolean}\n */\nfunction spreadout(node) {\n  let index = -1\n  let seenFlow = false\n\n  while (++index < node.children.length) {\n    const child = node.children[index]\n\n    if (child.type === 'element') {\n      if (phrasing(child)) continue\n\n      if (child.tagName === 'p' || seenFlow || spreadout(child)) {\n        return true\n      }\n\n      seenFlow = true\n    }\n  }\n\n  return false\n}\n\n/**\n * Extract a leading checkbox from a list item.\n *\n * If there was a leading checkbox, makes a deep clone of the node w/o the\n * leading checkbox; otherwise a reference to the given, untouched, node is\n * given back.\n *\n * So for example:\n *\n * ```html\n * <li><input type=\"checkbox\">Text</li>\n * ```\n *\n * …becomes:\n *\n * ```html\n * <li>Text</li>\n * ```\n *\n * ```html\n * <li><p><input type=\"checkbox\">Text</p></li>\n * ```\n *\n * …becomes:\n *\n * ```html\n * <li><p>Text</p></li>\n * ```\n *\n * @param {Readonly<Element>} node\n * @returns {ExtractResult}\n */\nfunction extractLeadingCheckbox(node) {\n  const head = node.children[0]\n\n  if (\n    head &&\n    head.type === 'element' &&\n    head.tagName === 'input' &&\n    head.properties &&\n    (head.properties.type === 'checkbox' || head.properties.type === 'radio')\n  ) {\n    const rest = {...node, children: node.children.slice(1)}\n    return {checkbox: head, rest}\n  }\n\n  // The checkbox may be nested in another element.\n  // If the first element has children, look for a leading checkbox inside it.\n  //\n  // This only handles nesting in `<p>` elements, which is most common.\n  // It’s possible a leading checkbox might be nested in other types of flow or\n  // phrasing elements (and *deeply* nested, which is not possible with `<p>`).\n  // Limiting things to `<p>` elements keeps this simpler for now.\n  if (head && head.type === 'element' && head.tagName === 'p') {\n    const {checkbox, rest: restHead} = extractLeadingCheckbox(head)\n\n    if (checkbox) {\n      const rest = {...node, children: [restHead, ...node.children.slice(1)]}\n      return {checkbox, rest}\n    }\n  }\n\n  return {checkbox: undefined, rest: node}\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;;CAQC;;;AAED;;AAUO,SAAS,GAAG,KAAK,EAAE,IAAI;IAC5B,4EAA4E;IAC5E,qCAAqC;IACrC,MAAM,EAAC,IAAI,EAAE,QAAQ,EAAC,GAAG,uBAAuB;IAChD,MAAM,UAAU,WAAW,QAAQ,SAAS,UAAU,CAAC,OAAO,IAAI;IAClE,MAAM,SAAS,UAAU;IACzB,MAAM,WAAW,MAAM,MAAM,CAAC,MAAM,GAAG,CAAC;IAExC,qBAAqB,GACrB,MAAM,SAAS;QAAC,MAAM;QAAY;QAAQ;QAAS;IAAQ;IAC3D,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT;AAEA;;;;;;;;;;;;;;;;;;;;;CAqBC,GACD,SAAS,UAAU,IAAI;IACrB,IAAI,QAAQ,CAAC;IACb,IAAI,WAAW;IAEf,MAAO,EAAE,QAAQ,KAAK,QAAQ,CAAC,MAAM,CAAE;QACrC,MAAM,QAAQ,KAAK,QAAQ,CAAC,MAAM;QAElC,IAAI,MAAM,IAAI,KAAK,WAAW;YAC5B,IAAI,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;YAErB,IAAI,MAAM,OAAO,KAAK,OAAO,YAAY,UAAU,QAAQ;gBACzD,OAAO;YACT;YAEA,WAAW;QACb;IACF;IAEA,OAAO;AACT;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+BC,GACD,SAAS,uBAAuB,IAAI;IAClC,MAAM,OAAO,KAAK,QAAQ,CAAC,EAAE;IAE7B,IACE,QACA,KAAK,IAAI,KAAK,aACd,KAAK,OAAO,KAAK,WACjB,KAAK,UAAU,IACf,CAAC,KAAK,UAAU,CAAC,IAAI,KAAK,cAAc,KAAK,UAAU,CAAC,IAAI,KAAK,OAAO,GACxE;QACA,MAAM,OAAO;YAAC,GAAG,IAAI;YAAE,UAAU,KAAK,QAAQ,CAAC,KAAK,CAAC;QAAE;QACvD,OAAO;YAAC,UAAU;YAAM;QAAI;IAC9B;IAEA,iDAAiD;IACjD,4EAA4E;IAC5E,EAAE;IACF,qEAAqE;IACrE,6EAA6E;IAC7E,6EAA6E;IAC7E,gEAAgE;IAChE,IAAI,QAAQ,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK,KAAK;QAC3D,MAAM,EAAC,QAAQ,EAAE,MAAM,QAAQ,EAAC,GAAG,uBAAuB;QAE1D,IAAI,UAAU;YACZ,MAAM,OAAO;gBAAC,GAAG,IAAI;gBAAE,UAAU;oBAAC;uBAAa,KAAK,QAAQ,CAAC,KAAK,CAAC;iBAAG;YAAA;YACtE,OAAO;gBAAC;gBAAU;YAAI;QACxB;IACF;IAEA,OAAO;QAAC,UAAU;QAAW,MAAM;IAAI;AACzC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3493, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/list.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {ListItem, List} from 'mdast'\n */\n\nimport {listItemsSpread} from '../util/list-items-spread.js'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {List}\n *   mdast node.\n */\nexport function list(state, node) {\n  const ordered = node.tagName === 'ol'\n  const children = state.toSpecificContent(state.all(node), create)\n  /** @type {number | null} */\n  let start = null\n\n  if (ordered) {\n    start =\n      node.properties && node.properties.start\n        ? Number.parseInt(String(node.properties.start), 10)\n        : 1\n  }\n\n  /** @type {List} */\n  const result = {\n    type: 'list',\n    ordered,\n    start,\n    spread: listItemsSpread(children),\n    children\n  }\n  state.patch(node, result)\n  return result\n}\n\n/**\n * @returns {ListItem}\n */\nfunction create() {\n  return {type: 'listItem', spread: false, checked: null, children: []}\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;;AAUO,SAAS,KAAK,KAAK,EAAE,IAAI;IAC9B,MAAM,UAAU,KAAK,OAAO,KAAK;IACjC,MAAM,WAAW,MAAM,iBAAiB,CAAC,MAAM,GAAG,CAAC,OAAO;IAC1D,0BAA0B,GAC1B,IAAI,QAAQ;IAEZ,IAAI,SAAS;QACX,QACE,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,KAAK,GACpC,OAAO,QAAQ,CAAC,OAAO,KAAK,UAAU,CAAC,KAAK,GAAG,MAC/C;IACR;IAEA,iBAAiB,GACjB,MAAM,SAAS;QACb,MAAM;QACN;QACA;QACA,QAAQ,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE;QACxB;IACF;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT;AAEA;;CAEC,GACD,SAAS;IACP,OAAO;QAAC,MAAM;QAAY,QAAQ;QAAO,SAAS;QAAM,UAAU,EAAE;IAAA;AACtE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3535, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-string/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('mdast').Nodes} Nodes\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [includeImageAlt=true]\n *   Whether to use `alt` for `image`s (default: `true`).\n * @property {boolean | null | undefined} [includeHtml=true]\n *   Whether to use `value` of HTML (default: `true`).\n */\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Get the text content of a node or list of nodes.\n *\n * Prefers the node’s plain-text fields, otherwise serializes its children,\n * and if the given value is an array, serialize the nodes in it.\n *\n * @param {unknown} [value]\n *   Thing to serialize, typically `Node`.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `value`.\n */\nexport function toString(value, options) {\n  const settings = options || emptyOptions\n  const includeImageAlt =\n    typeof settings.includeImageAlt === 'boolean'\n      ? settings.includeImageAlt\n      : true\n  const includeHtml =\n    typeof settings.includeHtml === 'boolean' ? settings.includeHtml : true\n\n  return one(value, includeImageAlt, includeHtml)\n}\n\n/**\n * One node or several nodes.\n *\n * @param {unknown} value\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(value, includeImageAlt, includeHtml) {\n  if (node(value)) {\n    if ('value' in value) {\n      return value.type === 'html' && !includeHtml ? '' : value.value\n    }\n\n    if (includeImageAlt && 'alt' in value && value.alt) {\n      return value.alt\n    }\n\n    if ('children' in value) {\n      return all(value.children, includeImageAlt, includeHtml)\n    }\n  }\n\n  if (Array.isArray(value)) {\n    return all(value, includeImageAlt, includeHtml)\n  }\n\n  return ''\n}\n\n/**\n * Serialize a list of nodes.\n *\n * @param {Array<unknown>} values\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized nodes.\n */\nfunction all(values, includeImageAlt, includeHtml) {\n  /** @type {Array<string>} */\n  const result = []\n  let index = -1\n\n  while (++index < values.length) {\n    result[index] = one(values[index], includeImageAlt, includeHtml)\n  }\n\n  return result.join('')\n}\n\n/**\n * Check if `value` looks like a node.\n *\n * @param {unknown} value\n *   Thing.\n * @returns {value is Nodes}\n *   Whether `value` is a node.\n */\nfunction node(value) {\n  return Boolean(value && typeof value === 'object')\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC,GAED,oBAAoB;;;AACpB,MAAM,eAAe,CAAC;AAef,SAAS,SAAS,KAAK,EAAE,OAAO;IACrC,MAAM,WAAW,WAAW;IAC5B,MAAM,kBACJ,OAAO,SAAS,eAAe,KAAK,YAChC,SAAS,eAAe,GACxB;IACN,MAAM,cACJ,OAAO,SAAS,WAAW,KAAK,YAAY,SAAS,WAAW,GAAG;IAErE,OAAO,IAAI,OAAO,iBAAiB;AACrC;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,IAAI,KAAK,EAAE,eAAe,EAAE,WAAW;IAC9C,IAAI,KAAK,QAAQ;QACf,IAAI,WAAW,OAAO;YACpB,OAAO,MAAM,IAAI,KAAK,UAAU,CAAC,cAAc,KAAK,MAAM,KAAK;QACjE;QAEA,IAAI,mBAAmB,SAAS,SAAS,MAAM,GAAG,EAAE;YAClD,OAAO,MAAM,GAAG;QAClB;QAEA,IAAI,cAAc,OAAO;YACvB,OAAO,IAAI,MAAM,QAAQ,EAAE,iBAAiB;QAC9C;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,QAAQ;QACxB,OAAO,IAAI,OAAO,iBAAiB;IACrC;IAEA,OAAO;AACT;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,IAAI,MAAM,EAAE,eAAe,EAAE,WAAW;IAC/C,0BAA0B,GAC1B,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;QAC9B,MAAM,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,MAAM,EAAE,iBAAiB;IACtD;IAEA,OAAO,OAAO,IAAI,CAAC;AACrB;AAEA;;;;;;;CAOC,GACD,SAAS,KAAK,KAAK;IACjB,OAAO,QAAQ,SAAS,OAAO,UAAU;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3617, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-phrasing/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('mdast').Html} Html\n * @typedef {import('mdast').PhrasingContent} PhrasingContent\n */\n\nimport {convert} from 'unist-util-is'\n\n/**\n * Check if the given value is *phrasing content*.\n *\n * > 👉 **Note**: Excludes `html`, which can be both phrasing or flow.\n *\n * @param node\n *   Thing to check, typically `Node`.\n * @returns\n *   Whether `value` is phrasing content.\n */\n\nexport const phrasing =\n  /** @type {(node?: unknown) => node is Exclude<PhrasingContent, Html>} */\n  (\n    convert([\n      'break',\n      'delete',\n      'emphasis',\n      // To do: next major: removed since footnotes were added to GFM.\n      'footnote',\n      'footnoteReference',\n      'image',\n      'imageReference',\n      'inlineCode',\n      // Enabled by `mdast-util-math`:\n      'inlineMath',\n      'link',\n      'linkReference',\n      // Enabled by `mdast-util-mdx`:\n      'mdxJsxTextElement',\n      // Enabled by `mdast-util-mdx`:\n      'mdxTextExpression',\n      'strong',\n      'text',\n      // Enabled by `mdast-util-directive`:\n      'textDirective'\n    ])\n  )\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AAaO,MAAM,WAGT,CAAA,GAAA,mJAAA,CAAA,UAAO,AAAD,EAAE;IACN;IACA;IACA;IACA,gEAAgE;IAChE;IACA;IACA;IACA;IACA;IACA,gCAAgC;IAChC;IACA;IACA;IACA,+BAA+B;IAC/B;IACA,+BAA+B;IAC/B;IACA;IACA;IACA,qCAAqC;IACrC;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3654, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/util/wrap.js"], "sourcesContent": ["/**\n * @import {} from 'mdast-util-to-hast'\n * @import {\n *   BlockContent,\n *   Delete,\n *   Link,\n *   Nodes,\n *   Paragraph,\n *   Parents,\n *   PhrasingContent,\n *   RootContent\n * } from 'mdast'\n */\n\nimport structuredClone from '@ungap/structured-clone'\nimport {phrasing as hastPhrasing} from 'hast-util-phrasing'\nimport {whitespace} from 'hast-util-whitespace'\nimport {phrasing as mdastPhrasing} from 'mdast-util-phrasing'\nimport {dropSurroundingBreaks} from './drop-surrounding-breaks.js'\n\n/**\n * Check if there are phrasing mdast nodes.\n *\n * This is needed if a fragment is given, which could just be a sentence, and\n * doesn’t need a wrapper paragraph.\n *\n * @param {Array<Nodes>} nodes\n * @returns {boolean}\n */\nexport function wrapNeeded(nodes) {\n  let index = -1\n\n  while (++index < nodes.length) {\n    const node = nodes[index]\n\n    if (!phrasing(node) || ('children' in node && wrapNeeded(node.children))) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * Wrap runs of phrasing content into paragraphs, leaving the non-phrasing\n * content as-is.\n *\n * @param {Array<RootContent>} nodes\n *   Content.\n * @returns {Array<BlockContent>}\n *   Content where phrasing is wrapped in paragraphs.\n */\nexport function wrap(nodes) {\n  return runs(nodes, onphrasing, function (d) {\n    return d\n  })\n\n  /**\n   * @param {Array<PhrasingContent>} nodes\n   * @returns {Array<Paragraph>}\n   */\n  function onphrasing(nodes) {\n    return nodes.every(function (d) {\n      return d.type === 'text' ? whitespace(d.value) : false\n    })\n      ? []\n      : [{type: 'paragraph', children: dropSurroundingBreaks(nodes)}]\n  }\n}\n\n/**\n * @param {Delete | Link} node\n * @returns {Array<BlockContent>}\n */\nfunction split(node) {\n  return runs(node.children, onphrasing, onnonphrasing)\n\n  /**\n   * Use `parent`, put the phrasing run inside it.\n   *\n   * @param {Array<PhrasingContent>} nodes\n   * @returns {Array<BlockContent>}\n   */\n  function onphrasing(nodes) {\n    const newParent = cloneWithoutChildren(node)\n    newParent.children = nodes\n    // @ts-expect-error Assume fine.\n    return [newParent]\n  }\n\n  /**\n   * Use `child`, add `parent` as its first child, put the original children\n   * into `parent`.\n   * If `child` is not a parent, `parent` will not be added.\n   *\n   * @param {BlockContent} child\n   * @returns {BlockContent}\n   */\n  function onnonphrasing(child) {\n    if ('children' in child && 'children' in node) {\n      const newParent = cloneWithoutChildren(node)\n      const newChild = cloneWithoutChildren(child)\n      // @ts-expect-error Assume fine.\n      newParent.children = child.children\n      // @ts-expect-error Assume fine.\n      newChild.children.push(newParent)\n      return newChild\n    }\n\n    return {...child}\n  }\n}\n\n/**\n * Wrap all runs of mdast phrasing content in `paragraph` nodes.\n *\n * @param {Array<RootContent>} nodes\n *   List of input nodes.\n * @param {(nodes: Array<PhrasingContent>) => Array<BlockContent>} onphrasing\n *   Turn phrasing content into block content.\n * @param {(node: BlockContent) => BlockContent} onnonphrasing\n *   Map block content (defaults to keeping them as-is).\n * @returns {Array<BlockContent>}\n */\nfunction runs(nodes, onphrasing, onnonphrasing) {\n  const flattened = flatten(nodes)\n  /** @type {Array<BlockContent>} */\n  const result = []\n  /** @type {Array<PhrasingContent>} */\n  let queue = []\n  let index = -1\n\n  while (++index < flattened.length) {\n    const node = flattened[index]\n\n    if (phrasing(node)) {\n      queue.push(node)\n    } else {\n      if (queue.length > 0) {\n        result.push(...onphrasing(queue))\n        queue = []\n      }\n\n      // @ts-expect-error Assume non-phrasing.\n      result.push(onnonphrasing(node))\n    }\n  }\n\n  if (queue.length > 0) {\n    result.push(...onphrasing(queue))\n    queue = []\n  }\n\n  return result\n}\n\n/**\n * Flatten a list of nodes.\n *\n * @param {Array<RootContent>} nodes\n *   List of nodes, will unravel `delete` and `link`.\n * @returns {Array<RootContent>}\n *   Unraveled nodes.\n */\nfunction flatten(nodes) {\n  /** @type {Array<RootContent>} */\n  const flattened = []\n  let index = -1\n\n  while (++index < nodes.length) {\n    const node = nodes[index]\n\n    // Straddling: some elements are *weird*.\n    // Namely: `map`, `ins`, `del`, and `a`, as they are hybrid elements.\n    // See: <https://html.spec.whatwg.org/#paragraphs>.\n    // Paragraphs are the weirdest of them all.\n    // See the straddling fixture for more info!\n    // `ins` is ignored in mdast, so we don’t need to worry about that.\n    // `map` maps to its content, so we don’t need to worry about that either.\n    // `del` maps to `delete` and `a` to `link`, so we do handle those.\n    // What we’ll do is split `node` over each of its children.\n    if (\n      (node.type === 'delete' || node.type === 'link') &&\n      wrapNeeded(node.children)\n    ) {\n      flattened.push(...split(node))\n    } else {\n      flattened.push(node)\n    }\n  }\n\n  return flattened\n}\n\n/**\n * Check if an mdast node is phrasing.\n *\n * Also supports checking embedded hast fields.\n *\n * @param {Nodes} node\n *   mdast node to check.\n * @returns {node is PhrasingContent}\n *   Whether `node` is phrasing content (includes nodes with `hName` fields\n *   set to phrasing hast element names).\n */\nfunction phrasing(node) {\n  const tagName = node.data && node.data.hName\n  return tagName\n    ? hastPhrasing({type: 'element', tagName, properties: {}, children: []})\n    : mdastPhrasing(node)\n}\n\n/**\n * @template {Parents} ParentType\n *   Parent type.\n * @param {ParentType} node\n *   Node to clone.\n * @returns {ParentType}\n *   Cloned node, without children.\n */\nfunction cloneWithoutChildren(node) {\n  return structuredClone({...node, children: []})\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;CAYC;;;;AAED;AACA;AACA;AACA;AACA;;;;;;AAWO,SAAS,WAAW,KAAK;IAC9B,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,MAAM,OAAO,KAAK,CAAC,MAAM;QAEzB,IAAI,CAAC,SAAS,SAAU,cAAc,QAAQ,WAAW,KAAK,QAAQ,GAAI;YACxE,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAWO,SAAS,KAAK,KAAK;IACxB,OAAO,KAAK,OAAO,YAAY,SAAU,CAAC;QACxC,OAAO;IACT;;IAEA;;;GAGC,GACD,SAAS,WAAW,KAAK;QACvB,OAAO,MAAM,KAAK,CAAC,SAAU,CAAC;YAC5B,OAAO,EAAE,IAAI,KAAK,SAAS,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE,EAAE,KAAK,IAAI;QACnD,KACI,EAAE,GACF;YAAC;gBAAC,MAAM;gBAAa,UAAU,CAAA,GAAA,2LAAA,CAAA,wBAAqB,AAAD,EAAE;YAAM;SAAE;IACnE;AACF;AAEA;;;CAGC,GACD,SAAS,MAAM,IAAI;IACjB,OAAO,KAAK,KAAK,QAAQ,EAAE,YAAY;;IAEvC;;;;;GAKC,GACD,SAAS,WAAW,KAAK;QACvB,MAAM,YAAY,qBAAqB;QACvC,UAAU,QAAQ,GAAG;QACrB,gCAAgC;QAChC,OAAO;YAAC;SAAU;IACpB;IAEA;;;;;;;GAOC,GACD,SAAS,cAAc,KAAK;QAC1B,IAAI,cAAc,SAAS,cAAc,MAAM;YAC7C,MAAM,YAAY,qBAAqB;YACvC,MAAM,WAAW,qBAAqB;YACtC,gCAAgC;YAChC,UAAU,QAAQ,GAAG,MAAM,QAAQ;YACnC,gCAAgC;YAChC,SAAS,QAAQ,CAAC,IAAI,CAAC;YACvB,OAAO;QACT;QAEA,OAAO;YAAC,GAAG,KAAK;QAAA;IAClB;AACF;AAEA;;;;;;;;;;CAUC,GACD,SAAS,KAAK,KAAK,EAAE,UAAU,EAAE,aAAa;IAC5C,MAAM,YAAY,QAAQ;IAC1B,gCAAgC,GAChC,MAAM,SAAS,EAAE;IACjB,mCAAmC,GACnC,IAAI,QAAQ,EAAE;IACd,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,UAAU,MAAM,CAAE;QACjC,MAAM,OAAO,SAAS,CAAC,MAAM;QAE7B,IAAI,SAAS,OAAO;YAClB,MAAM,IAAI,CAAC;QACb,OAAO;YACL,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,OAAO,IAAI,IAAI,WAAW;gBAC1B,QAAQ,EAAE;YACZ;YAEA,wCAAwC;YACxC,OAAO,IAAI,CAAC,cAAc;QAC5B;IACF;IAEA,IAAI,MAAM,MAAM,GAAG,GAAG;QACpB,OAAO,IAAI,IAAI,WAAW;QAC1B,QAAQ,EAAE;IACZ;IAEA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,QAAQ,KAAK;IACpB,+BAA+B,GAC/B,MAAM,YAAY,EAAE;IACpB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,MAAM,OAAO,KAAK,CAAC,MAAM;QAEzB,yCAAyC;QACzC,qEAAqE;QACrE,mDAAmD;QACnD,2CAA2C;QAC3C,4CAA4C;QAC5C,mEAAmE;QACnE,0EAA0E;QAC1E,mEAAmE;QACnE,2DAA2D;QAC3D,IACE,CAAC,KAAK,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,MAAM,KAC/C,WAAW,KAAK,QAAQ,GACxB;YACA,UAAU,IAAI,IAAI,MAAM;QAC1B,OAAO;YACL,UAAU,IAAI,CAAC;QACjB;IACF;IAEA,OAAO;AACT;AAEA;;;;;;;;;;CAUC,GACD,SAAS,SAAS,IAAI;IACpB,MAAM,UAAU,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK;IAC5C,OAAO,UACH,CAAA,GAAA,wJAAA,CAAA,WAAY,AAAD,EAAE;QAAC,MAAM;QAAW;QAAS,YAAY,CAAC;QAAG,UAAU,EAAE;IAAA,KACpE,CAAA,GAAA,yJAAA,CAAA,WAAa,AAAD,EAAE;AACpB;AAEA;;;;;;;CAOC,GACD,SAAS,qBAAqB,IAAI;IAChC,OAAO,CAAA,GAAA,8KAAA,CAAA,UAAe,AAAD,EAAE;QAAC,GAAG,IAAI;QAAE,UAAU,EAAE;IAAA;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3851, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/media.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Image, Link, PhrasingContent, RootContent as MdastRootContent, Root} from 'mdast'\n */\n\nimport {toString} from 'mdast-util-to-string'\nimport {EXIT, visit} from 'unist-util-visit'\nimport {wrapNeeded} from '../util/wrap.js'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Array<MdastRootContent> | Link}\n *   mdast node.\n */\nexport function media(state, node) {\n  const properties = node.properties || {}\n  const poster = node.tagName === 'video' ? String(properties.poster || '') : ''\n  let source = String(properties.src || '')\n  let index = -1\n  let linkInFallbackContent = false\n  let nodes = state.all(node)\n\n  /** @type {Root} */\n  const fragment = {type: 'root', children: nodes}\n\n  visit(fragment, function (node) {\n    if (node.type === 'link') {\n      linkInFallbackContent = true\n      return EXIT\n    }\n  })\n\n  // If the content links to something, or if it’s not phrasing…\n  if (linkInFallbackContent || wrapNeeded(nodes)) {\n    return nodes\n  }\n\n  // Find the source.\n  while (!source && ++index < node.children.length) {\n    const child = node.children[index]\n\n    if (\n      child.type === 'element' &&\n      child.tagName === 'source' &&\n      child.properties\n    ) {\n      source = String(child.properties.src || '')\n    }\n  }\n\n  // If there’s a poster defined on the video, create an image.\n  if (poster) {\n    /** @type {Image} */\n    const image = {\n      type: 'image',\n      title: null,\n      url: state.resolve(poster),\n      alt: toString(nodes)\n    }\n    state.patch(node, image)\n    nodes = [image]\n  }\n\n  // Allow potentially “invalid” nodes, they might be unknown.\n  // We also support straddling later.\n  const children = /** @type {Array<PhrasingContent>} */ (nodes)\n\n  // Link to the media resource.\n  /** @type {Link} */\n  const result = {\n    type: 'link',\n    title: properties.title ? String(properties.title) : null,\n    url: state.resolve(source),\n    children\n  }\n  state.patch(node, result)\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;AACA;AAAA;AACA;;;;AAUO,SAAS,MAAM,KAAK,EAAE,IAAI;IAC/B,MAAM,aAAa,KAAK,UAAU,IAAI,CAAC;IACvC,MAAM,SAAS,KAAK,OAAO,KAAK,UAAU,OAAO,WAAW,MAAM,IAAI,MAAM;IAC5E,IAAI,SAAS,OAAO,WAAW,GAAG,IAAI;IACtC,IAAI,QAAQ,CAAC;IACb,IAAI,wBAAwB;IAC5B,IAAI,QAAQ,MAAM,GAAG,CAAC;IAEtB,iBAAiB,GACjB,MAAM,WAAW;QAAC,MAAM;QAAQ,UAAU;IAAK;IAE/C,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,SAAU,IAAI;QAC5B,IAAI,KAAK,IAAI,KAAK,QAAQ;YACxB,wBAAwB;YACxB,OAAO,iKAAA,CAAA,OAAI;QACb;IACF;IAEA,8DAA8D;IAC9D,IAAI,yBAAyB,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE,QAAQ;QAC9C,OAAO;IACT;IAEA,mBAAmB;IACnB,MAAO,CAAC,UAAU,EAAE,QAAQ,KAAK,QAAQ,CAAC,MAAM,CAAE;QAChD,MAAM,QAAQ,KAAK,QAAQ,CAAC,MAAM;QAElC,IACE,MAAM,IAAI,KAAK,aACf,MAAM,OAAO,KAAK,YAClB,MAAM,UAAU,EAChB;YACA,SAAS,OAAO,MAAM,UAAU,CAAC,GAAG,IAAI;QAC1C;IACF;IAEA,6DAA6D;IAC7D,IAAI,QAAQ;QACV,kBAAkB,GAClB,MAAM,QAAQ;YACZ,MAAM;YACN,OAAO;YACP,KAAK,MAAM,OAAO,CAAC;YACnB,KAAK,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAChB;QACA,MAAM,KAAK,CAAC,MAAM;QAClB,QAAQ;YAAC;SAAM;IACjB;IAEA,4DAA4D;IAC5D,oCAAoC;IACpC,MAAM,WAAkD;IAExD,8BAA8B;IAC9B,iBAAiB,GACjB,MAAM,SAAS;QACb,MAAM;QACN,OAAO,WAAW,KAAK,GAAG,OAAO,WAAW,KAAK,IAAI;QACrD,KAAK,MAAM,OAAO,CAAC;QACnB;IACF;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3925, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/p.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Paragraph, PhrasingContent} from 'mdast'\n */\n\nimport {dropSurroundingBreaks} from '../util/drop-surrounding-breaks.js'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Paragraph | undefined}\n *   mdast node.\n */\nexport function p(state, node) {\n  const children = dropSurroundingBreaks(\n    // Allow potentially “invalid” nodes, they might be unknown.\n    // We also support straddling later.\n    /** @type {Array<PhrasingContent>} */ (state.all(node))\n  )\n\n  if (children.length > 0) {\n    /** @type {Paragraph} */\n    const result = {type: 'paragraph', children}\n    state.patch(node, result)\n    return result\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;;AAUO,SAAS,EAAE,KAAK,EAAE,IAAI;IAC3B,MAAM,WAAW,CAAA,GAAA,2LAAA,CAAA,wBAAqB,AAAD,EAGI,MAAM,GAAG,CAAC;IAGnD,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,sBAAsB,GACtB,MAAM,SAAS;YAAC,MAAM;YAAa;QAAQ;QAC3C,MAAM,KAAK,CAAC,MAAM;QAClB,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3951, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/q.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {RootContent as MdastRootContent} from 'mdast'\n */\n\nconst defaultQuotes = ['\"']\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Array<MdastRootContent>}\n *   mdast nodes.\n */\nexport function q(state, node) {\n  const quotes = state.options.quotes || defaultQuotes\n\n  state.qNesting++\n  const contents = state.all(node)\n  state.qNesting--\n\n  const quote = quotes[state.qNesting % quotes.length]\n  const head = contents[0]\n  const tail = contents[contents.length - 1]\n  const open = quote.charAt(0)\n  const close = quote.length > 1 ? quote.charAt(1) : quote\n\n  if (head && head.type === 'text') {\n    head.value = open + head.value\n  } else {\n    contents.unshift({type: 'text', value: open})\n  }\n\n  if (tail && tail.type === 'text') {\n    tail.value += close\n  } else {\n    contents.push({type: 'text', value: close})\n  }\n\n  return contents\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED,MAAM,gBAAgB;IAAC;CAAI;AAUpB,SAAS,EAAE,KAAK,EAAE,IAAI;IAC3B,MAAM,SAAS,MAAM,OAAO,CAAC,MAAM,IAAI;IAEvC,MAAM,QAAQ;IACd,MAAM,WAAW,MAAM,GAAG,CAAC;IAC3B,MAAM,QAAQ;IAEd,MAAM,QAAQ,MAAM,CAAC,MAAM,QAAQ,GAAG,OAAO,MAAM,CAAC;IACpD,MAAM,OAAO,QAAQ,CAAC,EAAE;IACxB,MAAM,OAAO,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;IAC1C,MAAM,OAAO,MAAM,MAAM,CAAC;IAC1B,MAAM,QAAQ,MAAM,MAAM,GAAG,IAAI,MAAM,MAAM,CAAC,KAAK;IAEnD,IAAI,QAAQ,KAAK,IAAI,KAAK,QAAQ;QAChC,KAAK,KAAK,GAAG,OAAO,KAAK,KAAK;IAChC,OAAO;QACL,SAAS,OAAO,CAAC;YAAC,MAAM;YAAQ,OAAO;QAAI;IAC7C;IAEA,IAAI,QAAQ,KAAK,IAAI,KAAK,QAAQ;QAChC,KAAK,KAAK,IAAI;IAChB,OAAO;QACL,SAAS,IAAI,CAAC;YAAC,MAAM;YAAQ,OAAO;QAAK;IAC3C;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3995, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/root.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Root as HastRoot} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n */\n\nimport {wrap, wrapNeeded} from '../util/wrap.js'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<HastRoot>} node\n *   hast root to transform.\n * @returns {MdastRoot}\n *   mdast node.\n */\nexport function root(state, node) {\n  let children = state.all(node)\n\n  if (state.options.document || wrapNeeded(children)) {\n    children = wrap(children)\n  }\n\n  /** @type {MdastRoot} */\n  const result = {type: 'root', children}\n  state.patch(node, result)\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;;AAUO,SAAS,KAAK,KAAK,EAAE,IAAI;IAC9B,IAAI,WAAW,MAAM,GAAG,CAAC;IAEzB,IAAI,MAAM,OAAO,CAAC,QAAQ,IAAI,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE,WAAW;QAClD,WAAW,CAAA,GAAA,kKAAA,CAAA,OAAI,AAAD,EAAE;IAClB;IAEA,sBAAsB,GACtB,MAAM,SAAS;QAAC,MAAM;QAAQ;IAAQ;IACtC,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4022, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/select.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Text} from 'mdast'\n */\n\nimport {findSelectedOptions} from '../util/find-selected-options.js'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Text | undefined}\n *   mdast node.\n */\nexport function select(state, node) {\n  const values = findSelectedOptions(node)\n  let index = -1\n  /** @type {Array<string>} */\n  const results = []\n\n  while (++index < values.length) {\n    const value = values[index]\n    results.push(value[1] ? value[1] + ' (' + value[0] + ')' : value[0])\n  }\n\n  if (results.length > 0) {\n    /** @type {Text} */\n    const result = {type: 'text', value: results.join(', ')}\n    state.patch(node, result)\n    return result\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;;AAUO,SAAS,OAAO,KAAK,EAAE,IAAI;IAChC,MAAM,SAAS,CAAA,GAAA,yLAAA,CAAA,sBAAmB,AAAD,EAAE;IACnC,IAAI,QAAQ,CAAC;IACb,0BAA0B,GAC1B,MAAM,UAAU,EAAE;IAElB,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;QAC9B,MAAM,QAAQ,MAAM,CAAC,MAAM;QAC3B,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,KAAK,CAAC,EAAE,GAAG,MAAM,KAAK,CAAC,EAAE;IACrE;IAEA,IAAI,QAAQ,MAAM,GAAG,GAAG;QACtB,iBAAiB,GACjB,MAAM,SAAS;YAAC,MAAM;YAAQ,OAAO,QAAQ,IAAI,CAAC;QAAK;QACvD,MAAM,KAAK,CAAC,MAAM;QAClB,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4054, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/strong.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {PhrasingContent, Strong} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Strong}\n *   mdast node.\n */\nexport function strong(state, node) {\n  // Allow potentially “invalid” nodes, they might be unknown.\n  // We also support straddling later.\n  const children = /** @type {Array<PhrasingContent>} */ (state.all(node))\n\n  /** @type {Strong} */\n  const result = {type: 'strong', children}\n  state.patch(node, result)\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC;;;AACM,SAAS,OAAO,KAAK,EAAE,IAAI;IAChC,4DAA4D;IAC5D,oCAAoC;IACpC,MAAM,WAAkD,MAAM,GAAG,CAAC;IAElE,mBAAmB,GACnB,MAAM,SAAS;QAAC,MAAM;QAAU;IAAQ;IACxC,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4085, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/table-cell.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {PhrasingContent, TableCell} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {TableCell}\n *   mdast node.\n */\nexport function tableCell(state, node) {\n  // Allow potentially “invalid” nodes, they might be unknown.\n  // We also support straddling later.\n  const children = /** @type {Array<PhrasingContent>} */ (state.all(node))\n\n  /** @type {TableCell} */\n  const result = {type: 'tableCell', children}\n  state.patch(node, result)\n\n  if (node.properties) {\n    const rowSpan = node.properties.rowSpan\n    const colSpan = node.properties.colSpan\n\n    if (rowSpan || colSpan) {\n      const data = /** @type {Record<string, unknown>} */ (\n        result.data || (result.data = {})\n      )\n      if (rowSpan) data.hastUtilToMdastTemporaryRowSpan = rowSpan\n      if (colSpan) data.hastUtilToMdastTemporaryColSpan = colSpan\n    }\n  }\n\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC;;;AACM,SAAS,UAAU,KAAK,EAAE,IAAI;IACnC,4DAA4D;IAC5D,oCAAoC;IACpC,MAAM,WAAkD,MAAM,GAAG,CAAC;IAElE,sBAAsB,GACtB,MAAM,SAAS;QAAC,MAAM;QAAa;IAAQ;IAC3C,MAAM,KAAK,CAAC,MAAM;IAElB,IAAI,KAAK,UAAU,EAAE;QACnB,MAAM,UAAU,KAAK,UAAU,CAAC,OAAO;QACvC,MAAM,UAAU,KAAK,UAAU,CAAC,OAAO;QAEvC,IAAI,WAAW,SAAS;YACtB,MAAM,OACJ,OAAO,IAAI,IAAI,CAAC,OAAO,IAAI,GAAG,CAAC,CAAC;YAElC,IAAI,SAAS,KAAK,+BAA+B,GAAG;YACpD,IAAI,SAAS,KAAK,+BAA+B,GAAG;QACtD;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4125, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/table-row.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {RowContent, TableRow} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {TableRow}\n *   mdast node.\n */\nexport function tableRow(state, node) {\n  const children = state.toSpecificContent(state.all(node), create)\n\n  /** @type {TableRow} */\n  const result = {type: 'tableRow', children}\n  state.patch(node, result)\n  return result\n}\n\n/**\n * @returns {RowContent}\n */\nfunction create() {\n  return {type: 'tableCell', children: []}\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC;;;AACM,SAAS,SAAS,KAAK,EAAE,IAAI;IAClC,MAAM,WAAW,MAAM,iBAAiB,CAAC,MAAM,GAAG,CAAC,OAAO;IAE1D,qBAAqB,GACrB,MAAM,SAAS;QAAC,MAAM;QAAY;IAAQ;IAC1C,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT;AAEA;;CAEC,GACD,SAAS;IACP,OAAO;QAAC,MAAM;QAAa,UAAU,EAAE;IAAA;AACzC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4162, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/table.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {AlignType, RowContent, TableContent, Table, Text} from 'mdast'\n */\n\n/**\n * @typedef Info\n *   Inferred info on a table.\n * @property {Array<AlignType>} align\n *   Alignment.\n * @property {boolean} headless\n *   Whether a `thead` is missing.\n */\n\nimport {toText} from 'hast-util-to-text'\nimport {SKIP, visit} from 'unist-util-visit'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Table | Text}\n *   mdast node.\n */\n// eslint-disable-next-line complexity\nexport function table(state, node) {\n  // Ignore nested tables.\n  if (state.inTable) {\n    /** @type {Text} */\n    const result = {type: 'text', value: toText(node)}\n    state.patch(node, result)\n    return result\n  }\n\n  state.inTable = true\n\n  const {align, headless} = inspect(node)\n  const rows = state.toSpecificContent(state.all(node), createRow)\n\n  // Add an empty header row.\n  if (headless) {\n    rows.unshift(createRow())\n  }\n\n  let rowIndex = -1\n\n  while (++rowIndex < rows.length) {\n    const row = rows[rowIndex]\n    const cells = state.toSpecificContent(row.children, createCell)\n    row.children = cells\n  }\n\n  let columns = 1\n  rowIndex = -1\n\n  while (++rowIndex < rows.length) {\n    const cells = rows[rowIndex].children\n    let cellIndex = -1\n\n    while (++cellIndex < cells.length) {\n      const cell = cells[cellIndex]\n\n      if (cell.data) {\n        const data = /** @type {Record<string, unknown>} */ (cell.data)\n        const colSpan =\n          Number.parseInt(String(data.hastUtilToMdastTemporaryColSpan), 10) || 1\n        const rowSpan =\n          Number.parseInt(String(data.hastUtilToMdastTemporaryRowSpan), 10) || 1\n\n        if (colSpan > 1 || rowSpan > 1) {\n          let otherRowIndex = rowIndex - 1\n\n          while (++otherRowIndex < rowIndex + rowSpan) {\n            let colIndex = cellIndex - 1\n\n            while (++colIndex < cellIndex + colSpan) {\n              if (!rows[otherRowIndex]) {\n                // Don’t add rows that don’t exist.\n                // Browsers don’t render them either.\n                break\n              }\n\n              /** @type {Array<RowContent>} */\n              const newCells = []\n\n              if (otherRowIndex !== rowIndex || colIndex !== cellIndex) {\n                newCells.push({type: 'tableCell', children: []})\n              }\n\n              rows[otherRowIndex].children.splice(colIndex, 0, ...newCells)\n            }\n          }\n        }\n\n        // Clean the data fields.\n        if ('hastUtilToMdastTemporaryColSpan' in cell.data)\n          delete cell.data.hastUtilToMdastTemporaryColSpan\n        if ('hastUtilToMdastTemporaryRowSpan' in cell.data)\n          delete cell.data.hastUtilToMdastTemporaryRowSpan\n        if (Object.keys(cell.data).length === 0) delete cell.data\n      }\n    }\n\n    if (cells.length > columns) columns = cells.length\n  }\n\n  // Add extra empty cells.\n  rowIndex = -1\n\n  while (++rowIndex < rows.length) {\n    const cells = rows[rowIndex].children\n    let cellIndex = cells.length - 1\n    while (++cellIndex < columns) {\n      cells.push({type: 'tableCell', children: []})\n    }\n  }\n\n  let alignIndex = align.length - 1\n  while (++alignIndex < columns) {\n    align.push(null)\n  }\n\n  state.inTable = false\n\n  /** @type {Table} */\n  const result = {type: 'table', align, children: rows}\n  state.patch(node, result)\n  return result\n}\n\n/**\n * Infer whether the HTML table has a head and how it aligns.\n *\n * @param {Readonly<Element>} node\n *   Table element to check.\n * @returns {Info}\n *   Info.\n */\nfunction inspect(node) {\n  /** @type {Info} */\n  const info = {align: [null], headless: true}\n  let rowIndex = 0\n  let cellIndex = 0\n\n  visit(node, function (child) {\n    if (child.type === 'element') {\n      // Don’t enter nested tables.\n      if (child.tagName === 'table' && node !== child) {\n        return SKIP\n      }\n\n      if (\n        (child.tagName === 'th' || child.tagName === 'td') &&\n        child.properties\n      ) {\n        if (!info.align[cellIndex]) {\n          const value = String(child.properties.align || '') || null\n\n          if (\n            value === 'center' ||\n            value === 'left' ||\n            value === 'right' ||\n            value === null\n          ) {\n            info.align[cellIndex] = value\n          }\n        }\n\n        // If there is a `th` in the first row, assume there is a header row.\n        if (info.headless && rowIndex < 2 && child.tagName === 'th') {\n          info.headless = false\n        }\n\n        cellIndex++\n      }\n      // If there is a `thead`, assume there is a header row.\n      else if (child.tagName === 'thead') {\n        info.headless = false\n      } else if (child.tagName === 'tr') {\n        rowIndex++\n        cellIndex = 0\n      }\n    }\n  })\n\n  return info\n}\n\n/**\n * @returns {RowContent}\n */\nfunction createCell() {\n  return {type: 'tableCell', children: []}\n}\n\n/**\n * @returns {TableContent}\n */\nfunction createRow() {\n  return {type: 'tableRow', children: []}\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC;;;AAED;AACA;AAAA;;;AAWO,SAAS,MAAM,KAAK,EAAE,IAAI;IAC/B,wBAAwB;IACxB,IAAI,MAAM,OAAO,EAAE;QACjB,iBAAiB,GACjB,MAAM,SAAS;YAAC,MAAM;YAAQ,OAAO,CAAA,GAAA,0JAAA,CAAA,SAAM,AAAD,EAAE;QAAK;QACjD,MAAM,KAAK,CAAC,MAAM;QAClB,OAAO;IACT;IAEA,MAAM,OAAO,GAAG;IAEhB,MAAM,EAAC,KAAK,EAAE,QAAQ,EAAC,GAAG,QAAQ;IAClC,MAAM,OAAO,MAAM,iBAAiB,CAAC,MAAM,GAAG,CAAC,OAAO;IAEtD,2BAA2B;IAC3B,IAAI,UAAU;QACZ,KAAK,OAAO,CAAC;IACf;IAEA,IAAI,WAAW,CAAC;IAEhB,MAAO,EAAE,WAAW,KAAK,MAAM,CAAE;QAC/B,MAAM,MAAM,IAAI,CAAC,SAAS;QAC1B,MAAM,QAAQ,MAAM,iBAAiB,CAAC,IAAI,QAAQ,EAAE;QACpD,IAAI,QAAQ,GAAG;IACjB;IAEA,IAAI,UAAU;IACd,WAAW,CAAC;IAEZ,MAAO,EAAE,WAAW,KAAK,MAAM,CAAE;QAC/B,MAAM,QAAQ,IAAI,CAAC,SAAS,CAAC,QAAQ;QACrC,IAAI,YAAY,CAAC;QAEjB,MAAO,EAAE,YAAY,MAAM,MAAM,CAAE;YACjC,MAAM,OAAO,KAAK,CAAC,UAAU;YAE7B,IAAI,KAAK,IAAI,EAAE;gBACb,MAAM,OAA+C,KAAK,IAAI;gBAC9D,MAAM,UACJ,OAAO,QAAQ,CAAC,OAAO,KAAK,+BAA+B,GAAG,OAAO;gBACvE,MAAM,UACJ,OAAO,QAAQ,CAAC,OAAO,KAAK,+BAA+B,GAAG,OAAO;gBAEvE,IAAI,UAAU,KAAK,UAAU,GAAG;oBAC9B,IAAI,gBAAgB,WAAW;oBAE/B,MAAO,EAAE,gBAAgB,WAAW,QAAS;wBAC3C,IAAI,WAAW,YAAY;wBAE3B,MAAO,EAAE,WAAW,YAAY,QAAS;4BACvC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gCAGxB;4BACF;4BAEA,8BAA8B,GAC9B,MAAM,WAAW,EAAE;4BAEnB,IAAI,kBAAkB,YAAY,aAAa,WAAW;gCACxD,SAAS,IAAI,CAAC;oCAAC,MAAM;oCAAa,UAAU,EAAE;gCAAA;4BAChD;4BAEA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,MAAM;wBACtD;oBACF;gBACF;gBAEA,yBAAyB;gBACzB,IAAI,qCAAqC,KAAK,IAAI,EAChD,OAAO,KAAK,IAAI,CAAC,+BAA+B;gBAClD,IAAI,qCAAqC,KAAK,IAAI,EAChD,OAAO,KAAK,IAAI,CAAC,+BAA+B;gBAClD,IAAI,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,KAAK,GAAG,OAAO,KAAK,IAAI;YAC3D;QACF;QAEA,IAAI,MAAM,MAAM,GAAG,SAAS,UAAU,MAAM,MAAM;IACpD;IAEA,yBAAyB;IACzB,WAAW,CAAC;IAEZ,MAAO,EAAE,WAAW,KAAK,MAAM,CAAE;QAC/B,MAAM,QAAQ,IAAI,CAAC,SAAS,CAAC,QAAQ;QACrC,IAAI,YAAY,MAAM,MAAM,GAAG;QAC/B,MAAO,EAAE,YAAY,QAAS;YAC5B,MAAM,IAAI,CAAC;gBAAC,MAAM;gBAAa,UAAU,EAAE;YAAA;QAC7C;IACF;IAEA,IAAI,aAAa,MAAM,MAAM,GAAG;IAChC,MAAO,EAAE,aAAa,QAAS;QAC7B,MAAM,IAAI,CAAC;IACb;IAEA,MAAM,OAAO,GAAG;IAEhB,kBAAkB,GAClB,MAAM,SAAS;QAAC,MAAM;QAAS;QAAO,UAAU;IAAI;IACpD,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,QAAQ,IAAI;IACnB,iBAAiB,GACjB,MAAM,OAAO;QAAC,OAAO;YAAC;SAAK;QAAE,UAAU;IAAI;IAC3C,IAAI,WAAW;IACf,IAAI,YAAY;IAEhB,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,MAAM,SAAU,KAAK;QACzB,IAAI,MAAM,IAAI,KAAK,WAAW;YAC5B,6BAA6B;YAC7B,IAAI,MAAM,OAAO,KAAK,WAAW,SAAS,OAAO;gBAC/C,OAAO,iKAAA,CAAA,OAAI;YACb;YAEA,IACE,CAAC,MAAM,OAAO,KAAK,QAAQ,MAAM,OAAO,KAAK,IAAI,KACjD,MAAM,UAAU,EAChB;gBACA,IAAI,CAAC,KAAK,KAAK,CAAC,UAAU,EAAE;oBAC1B,MAAM,QAAQ,OAAO,MAAM,UAAU,CAAC,KAAK,IAAI,OAAO;oBAEtD,IACE,UAAU,YACV,UAAU,UACV,UAAU,WACV,UAAU,MACV;wBACA,KAAK,KAAK,CAAC,UAAU,GAAG;oBAC1B;gBACF;gBAEA,qEAAqE;gBACrE,IAAI,KAAK,QAAQ,IAAI,WAAW,KAAK,MAAM,OAAO,KAAK,MAAM;oBAC3D,KAAK,QAAQ,GAAG;gBAClB;gBAEA;YACF,OAEK,IAAI,MAAM,OAAO,KAAK,SAAS;gBAClC,KAAK,QAAQ,GAAG;YAClB,OAAO,IAAI,MAAM,OAAO,KAAK,MAAM;gBACjC;gBACA,YAAY;YACd;QACF;IACF;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS;IACP,OAAO;QAAC,MAAM;QAAa,UAAU,EAAE;IAAA;AACzC;AAEA;;CAEC,GACD,SAAS;IACP,OAAO;QAAC,MAAM;QAAY,UAAU,EAAE;IAAA;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4333, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/text.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Text as HastText} from 'hast'\n * @import {Text as MdastText} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<HastText>} node\n *   hast element to transform.\n * @returns {MdastText}\n *   mdast node.\n */\nexport function text(state, node) {\n  /** @type {MdastText} */\n  const result = {type: 'text', value: node.value}\n  state.patch(node, result)\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC;;;AACM,SAAS,KAAK,KAAK,EAAE,IAAI;IAC9B,sBAAsB,GACtB,MAAM,SAAS;QAAC,MAAM;QAAQ,OAAO,KAAK,KAAK;IAAA;IAC/C,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4361, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/textarea.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Text} from 'mdast'\n */\n\nimport {toText} from 'hast-util-to-text'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Text}\n *   mdast node.\n */\nexport function textarea(state, node) {\n  /** @type {Text} */\n  const result = {type: 'text', value: toText(node)}\n  state.patch(node, result)\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;;AAUO,SAAS,SAAS,KAAK,EAAE,IAAI;IAClC,iBAAiB,GACjB,MAAM,SAAS;QAAC,MAAM;QAAQ,OAAO,CAAA,GAAA,0JAAA,CAAA,SAAM,AAAD,EAAE;IAAK;IACjD,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4384, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/wbr.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Text} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Text}\n *   mdast node.\n */\nexport function wbr(state, node) {\n  /** @type {Text} */\n  const result = {type: 'text', value: '\\u200B'}\n  state.patch(node, result)\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC;;;AACM,SAAS,IAAI,KAAK,EAAE,IAAI;IAC7B,iBAAiB,GACjB,MAAM,SAAS;QAAC,MAAM;QAAQ,OAAO;IAAQ;IAC7C,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4412, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/handlers/index.js"], "sourcesContent": ["/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Parents} from 'hast'\n */\n\nimport {a} from './a.js'\nimport {base} from './base.js'\nimport {blockquote} from './blockquote.js'\nimport {br} from './br.js'\nimport {code} from './code.js'\nimport {comment} from './comment.js'\nimport {del} from './del.js'\nimport {dl} from './dl.js'\nimport {em} from './em.js'\nimport {heading} from './heading.js'\nimport {hr} from './hr.js'\nimport {iframe} from './iframe.js'\nimport {img} from './img.js'\nimport {inlineCode} from './inline-code.js'\nimport {input} from './input.js'\nimport {li} from './li.js'\nimport {list} from './list.js'\nimport {media} from './media.js'\nimport {p} from './p.js'\nimport {q} from './q.js'\nimport {root} from './root.js'\nimport {select} from './select.js'\nimport {strong} from './strong.js'\nimport {tableCell} from './table-cell.js'\nimport {tableRow} from './table-row.js'\nimport {table} from './table.js'\nimport {text} from './text.js'\nimport {textarea} from './textarea.js'\nimport {wbr} from './wbr.js'\n\n/**\n * Default handlers for nodes.\n *\n * Each key is a node type, each value is a `NodeHandler`.\n */\nexport const nodeHandlers = {\n  comment,\n  doctype: ignore,\n  root,\n  text\n}\n\n/**\n * Default handlers for elements.\n *\n * Each key is an element name, each value is a `Handler`.\n */\nexport const handlers = {\n  // Ignore:\n  applet: ignore,\n  area: ignore,\n  basefont: ignore,\n  bgsound: ignore,\n  caption: ignore,\n  col: ignore,\n  colgroup: ignore,\n  command: ignore,\n  content: ignore,\n  datalist: ignore,\n  dialog: ignore,\n  element: ignore,\n  embed: ignore,\n  frame: ignore,\n  frameset: ignore,\n  isindex: ignore,\n  keygen: ignore,\n  link: ignore,\n  math: ignore,\n  menu: ignore,\n  menuitem: ignore,\n  meta: ignore,\n  nextid: ignore,\n  noembed: ignore,\n  noframes: ignore,\n  optgroup: ignore,\n  option: ignore,\n  param: ignore,\n  script: ignore,\n  shadow: ignore,\n  source: ignore,\n  spacer: ignore,\n  style: ignore,\n  svg: ignore,\n  template: ignore,\n  title: ignore,\n  track: ignore,\n\n  // Use children:\n  abbr: all,\n  acronym: all,\n  bdi: all,\n  bdo: all,\n  big: all,\n  blink: all,\n  button: all,\n  canvas: all,\n  cite: all,\n  data: all,\n  details: all,\n  dfn: all,\n  font: all,\n  ins: all,\n  label: all,\n  map: all,\n  marquee: all,\n  meter: all,\n  nobr: all,\n  noscript: all,\n  object: all,\n  output: all,\n  progress: all,\n  rb: all,\n  rbc: all,\n  rp: all,\n  rt: all,\n  rtc: all,\n  ruby: all,\n  slot: all,\n  small: all,\n  span: all,\n  sup: all,\n  sub: all,\n  tbody: all,\n  tfoot: all,\n  thead: all,\n  time: all,\n\n  // Use children as flow.\n  address: flow,\n  article: flow,\n  aside: flow,\n  body: flow,\n  center: flow,\n  div: flow,\n  fieldset: flow,\n  figcaption: flow,\n  figure: flow,\n  form: flow,\n  footer: flow,\n  header: flow,\n  hgroup: flow,\n  html: flow,\n  legend: flow,\n  main: flow,\n  multicol: flow,\n  nav: flow,\n  picture: flow,\n  section: flow,\n\n  // Handle.\n  a,\n  audio: media,\n  b: strong,\n  base,\n  blockquote,\n  br,\n  code: inlineCode,\n  dir: list,\n  dl,\n  dt: li,\n  dd: li,\n  del,\n  em,\n  h1: heading,\n  h2: heading,\n  h3: heading,\n  h4: heading,\n  h5: heading,\n  h6: heading,\n  hr,\n  i: em,\n  iframe,\n  img,\n  image: img,\n  input,\n  kbd: inlineCode,\n  li,\n  listing: code,\n  mark: em,\n  ol: list,\n  p,\n  plaintext: code,\n  pre: code,\n  q,\n  s: del,\n  samp: inlineCode,\n  select,\n  strike: del,\n  strong,\n  summary: p,\n  table,\n  td: tableCell,\n  textarea,\n  th: tableCell,\n  tr: tableRow,\n  tt: inlineCode,\n  u: em,\n  ul: list,\n  var: inlineCode,\n  video: media,\n  wbr,\n  xmp: code\n}\n\n/**\n * @param {State} state\n *   State.\n * @param {Parents} node\n *   Parent to transform.\n */\nfunction all(state, node) {\n  return state.all(node)\n}\n\n/**\n * @param {State} state\n *   State.\n * @param {Parents} node\n *   Parent to transform.\n */\nfunction flow(state, node) {\n  return state.toFlow(state.all(node))\n}\n\n/**\n * @returns {undefined}\n */\nfunction ignore() {}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOO,MAAM,eAAe;IAC1B,SAAA,yKAAA,CAAA,UAAO;IACP,SAAS;IACT,MAAA,sKAAA,CAAA,OAAI;IACJ,MAAA,sKAAA,CAAA,OAAI;AACN;AAOO,MAAM,WAAW;IACtB,UAAU;IACV,QAAQ;IACR,MAAM;IACN,UAAU;IACV,SAAS;IACT,SAAS;IACT,KAAK;IACL,UAAU;IACV,SAAS;IACT,SAAS;IACT,UAAU;IACV,QAAQ;IACR,SAAS;IACT,OAAO;IACP,OAAO;IACP,UAAU;IACV,SAAS;IACT,QAAQ;IACR,MAAM;IACN,MAAM;IACN,MAAM;IACN,UAAU;IACV,MAAM;IACN,QAAQ;IACR,SAAS;IACT,UAAU;IACV,UAAU;IACV,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,KAAK;IACL,UAAU;IACV,OAAO;IACP,OAAO;IAEP,gBAAgB;IAChB,MAAM;IACN,SAAS;IACT,KAAK;IACL,KAAK;IACL,KAAK;IACL,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,SAAS;IACT,KAAK;IACL,MAAM;IACN,KAAK;IACL,OAAO;IACP,KAAK;IACL,SAAS;IACT,OAAO;IACP,MAAM;IACN,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,KAAK;IACL,KAAK;IACL,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IAEN,wBAAwB;IACxB,SAAS;IACT,SAAS;IACT,OAAO;IACP,MAAM;IACN,QAAQ;IACR,KAAK;IACL,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,MAAM;IACN,UAAU;IACV,KAAK;IACL,SAAS;IACT,SAAS;IAET,UAAU;IACV,GAAA,mKAAA,CAAA,IAAC;IACD,OAAO,uKAAA,CAAA,QAAK;IACZ,GAAG,wKAAA,CAAA,SAAM;IACT,MAAA,sKAAA,CAAA,OAAI;IACJ,YAAA,4KAAA,CAAA,aAAU;IACV,IAAA,oKAAA,CAAA,KAAE;IACF,MAAM,gLAAA,CAAA,aAAU;IAChB,KAAK,sKAAA,CAAA,OAAI;IACT,IAAA,oKAAA,CAAA,KAAE;IACF,IAAI,oKAAA,CAAA,KAAE;IACN,IAAI,oKAAA,CAAA,KAAE;IACN,KAAA,qKAAA,CAAA,MAAG;IACH,IAAA,oKAAA,CAAA,KAAE;IACF,IAAI,yKAAA,CAAA,UAAO;IACX,IAAI,yKAAA,CAAA,UAAO;IACX,IAAI,yKAAA,CAAA,UAAO;IACX,IAAI,yKAAA,CAAA,UAAO;IACX,IAAI,yKAAA,CAAA,UAAO;IACX,IAAI,yKAAA,CAAA,UAAO;IACX,IAAA,oKAAA,CAAA,KAAE;IACF,GAAG,oKAAA,CAAA,KAAE;IACL,QAAA,wKAAA,CAAA,SAAM;IACN,KAAA,qKAAA,CAAA,MAAG;IACH,OAAO,qKAAA,CAAA,MAAG;IACV,OAAA,uKAAA,CAAA,QAAK;IACL,KAAK,gLAAA,CAAA,aAAU;IACf,IAAA,oKAAA,CAAA,KAAE;IACF,SAAS,sKAAA,CAAA,OAAI;IACb,MAAM,oKAAA,CAAA,KAAE;IACR,IAAI,sKAAA,CAAA,OAAI;IACR,GAAA,mKAAA,CAAA,IAAC;IACD,WAAW,sKAAA,CAAA,OAAI;IACf,KAAK,sKAAA,CAAA,OAAI;IACT,GAAA,mKAAA,CAAA,IAAC;IACD,GAAG,qKAAA,CAAA,MAAG;IACN,MAAM,gLAAA,CAAA,aAAU;IAChB,QAAA,wKAAA,CAAA,SAAM;IACN,QAAQ,qKAAA,CAAA,MAAG;IACX,QAAA,wKAAA,CAAA,SAAM;IACN,SAAS,mKAAA,CAAA,IAAC;IACV,OAAA,uKAAA,CAAA,QAAK;IACL,IAAI,+KAAA,CAAA,YAAS;IACb,UAAA,0KAAA,CAAA,WAAQ;IACR,IAAI,+KAAA,CAAA,YAAS;IACb,IAAI,8KAAA,CAAA,WAAQ;IACZ,IAAI,gLAAA,CAAA,aAAU;IACd,GAAG,oKAAA,CAAA,KAAE;IACL,IAAI,sKAAA,CAAA,OAAI;IACR,KAAK,gLAAA,CAAA,aAAU;IACf,OAAO,uKAAA,CAAA,QAAK;IACZ,KAAA,qKAAA,CAAA,MAAG;IACH,KAAK,sKAAA,CAAA,OAAI;AACX;AAEA;;;;;CAKC,GACD,SAAS,IAAI,KAAK,EAAE,IAAI;IACtB,OAAO,MAAM,GAAG,CAAC;AACnB;AAEA;;;;;CAKC,GACD,SAAS,KAAK,KAAK,EAAE,IAAI;IACvB,OAAO,MAAM,MAAM,CAAC,MAAM,GAAG,CAAC;AAChC;AAEA;;CAEC,GACD,SAAS,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4661, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/state.js"], "sourcesContent": ["/**\n * @import {Element, Nodes, Parents} from 'hast'\n * @import {\n *   BlockContent as MdastBlockContent,\n *   DefinitionContent as MdastDefinitionContent,\n *   Nodes as MdastNodes,\n *   Parents as MdastParents,\n *   RootContent as <PERSON><PERSON><PERSON>RootContent\n * } from 'mdast'\n */\n\n/**\n * @typedef {MdastBlockContent | MdastDefinitionContent} MdastFlowContent\n */\n\n/**\n * @callback All\n *   Transform the children of a hast parent to mdast.\n * @param {Parents} parent\n *   Parent.\n * @returns {Array<MdastRootContent>}\n *   mdast children.\n *\n * @callback Handle\n *   Handle a particular element.\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Element} element\n *   Element to transform.\n * @param {Parents | undefined} parent\n *   Parent of `element`.\n * @returns {Array<MdastNodes> | MdastNodes | undefined | void}\n *   mdast node or nodes.\n *\n *   Note: `void` is included until TS nicely infers `undefined`.\n *\n * @callback NodeHandle\n *   Handle a particular node.\n * @param {State} state\n *   Info passed around about the current state.\n * @param {any} node\n *   Node to transform.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Array<MdastNodes> | MdastNodes | undefined | void}\n *   mdast node or nodes.\n *\n *   Note: `void` is included until TS nicely infers `undefined`.\n *\n * @callback One\n *   Transform a hast node to mdast.\n * @param {Nodes} node\n *   Expected hast node.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Array<MdastNodes> | MdastNodes | undefined}\n *   mdast result.\n *\n * @typedef Options\n *   Configuration.\n * @property {string | null | undefined} [checked='[x]']\n *   Value to use for a checked checkbox or radio input (default: `'[x]'`)\n * @property {boolean | null | undefined} [document]\n *   Whether the given tree represents a complete document (optional).\n *\n *   Applies when the `tree` is a `root` node.\n *   When the tree represents a complete document, then things are wrapped in\n *   paragraphs when needed, and otherwise they’re left as-is.\n *   The default checks for whether there’s mixed content: some phrasing nodes\n *   *and* some non-phrasing nodes.\n * @property {Record<string, Handle | null | undefined> | null | undefined} [handlers]\n *   Object mapping tag names to functions handling the corresponding elements\n *   (optional).\n *\n *   Merged into the defaults.\n * @property {boolean | null | undefined} [newlines=false]\n *   Keep line endings when collapsing whitespace (default: `false`).\n *\n *   The default collapses to a single space.\n * @property {Record<string, NodeHandle | null | undefined> | null | undefined} [nodeHandlers]\n *   Object mapping node types to functions handling the corresponding nodes\n *   (optional).\n *\n *   Merged into the defaults.\n * @property {Array<string> | null | undefined} [quotes=['\"']]\n *   List of quotes to use (default: `['\"']`).\n *\n *   Each value can be one or two characters.\n *   When two, the first character determines the opening quote and the second\n *   the closing quote at that level.\n *   When one, both the opening and closing quote are that character.\n *\n *   The order in which the preferred quotes appear determines which quotes to\n *   use at which level of nesting.\n *   So, to prefer `‘’` at the first level of nesting, and `“”` at the second,\n *   pass `['‘’', '“”']`.\n *   If `<q>`s are nested deeper than the given amount of quotes, the markers\n *   wrap around: a third level of nesting when using `['«»', '‹›']` should\n *   have double guillemets, a fourth single, a fifth double again, etc.\n * @property {string | null | undefined} [unchecked='[ ]']\n *   Value to use for an unchecked checkbox or radio input (default: `'[ ]'`).\n *\n * @callback Patch\n *   Copy a node’s positional info.\n * @param {Nodes} from\n *   hast node to copy from.\n * @param {MdastNodes} to\n *   mdast node to copy into.\n * @returns {undefined}\n *   Nothing.\n *\n * @callback Resolve\n *   Resolve a URL relative to a base.\n * @param {string | null | undefined} url\n *   Possible URL value.\n * @returns {string}\n *   URL, resolved to a `base` element, if any.\n *\n * @typedef State\n *   Info passed around about the current state.\n * @property {All} all\n *   Transform the children of a hast parent to mdast.\n * @property {boolean} baseFound\n *   Whether a `<base>` element was seen.\n * @property {Map<string, Element>} elementById\n *   Elements by their `id`.\n * @property {string | undefined} frozenBaseUrl\n *   `href` of `<base>`, if any.\n * @property {Record<string, Handle>} handlers\n *   Applied element handlers.\n * @property {boolean} inTable\n *   Whether we’re in a table.\n * @property {Record<string, NodeHandle>} nodeHandlers\n *   Applied node handlers.\n * @property {One} one\n *   Transform a hast node to mdast.\n * @property {Options} options\n *   User configuration.\n * @property {Patch} patch\n *   Copy a node’s positional info.\n * @property {number} qNesting\n *   Non-negative finite integer representing how deep we’re in `<q>`s.\n * @property {Resolve} resolve\n *   Resolve a URL relative to a base.\n * @property {ToFlow} toFlow\n *   Transform a list of mdast nodes to flow.\n * @property {<ChildType extends MdastNodes, ParentType extends MdastParents & {'children': Array<ChildType>}>(nodes: Array<MdastRootContent>, build: (() => ParentType)) => Array<ParentType>} toSpecificContent\n *   Turn arbitrary content into a list of a particular node type.\n *\n *   This is useful for example for lists, which must have list items as\n *   content.\n *   in this example, when non-items are found, they will be queued, and\n *   inserted into an adjacent item.\n *   When no actual items exist, one will be made with `build`.\n *\n * @callback ToFlow\n *   Transform a list of mdast nodes to flow.\n * @param {Array<MdastRootContent>} nodes\n *   mdast nodes.\n * @returns {Array<MdastFlowContent>}\n *   mdast flow children.\n */\n\nimport {position} from 'unist-util-position'\nimport {handlers, nodeHandlers} from './handlers/index.js'\nimport {wrap} from './util/wrap.js'\n\nconst own = {}.hasOwnProperty\n\n/**\n * Create a state.\n *\n * @param {Readonly<Options>} options\n *   User configuration.\n * @returns {State}\n *   State.\n */\nexport function createState(options) {\n  return {\n    all,\n    baseFound: false,\n    elementById: new Map(),\n    frozenBaseUrl: undefined,\n    handlers: {...handlers, ...options.handlers},\n    inTable: false,\n    nodeHandlers: {...nodeHandlers, ...options.nodeHandlers},\n    one,\n    options,\n    patch,\n    qNesting: 0,\n    resolve,\n    toFlow,\n    toSpecificContent\n  }\n}\n\n/**\n * Transform the children of a hast parent to mdast.\n *\n * You might want to combine this with `toFlow` or `toSpecificContent`.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Parents} parent\n *   Parent.\n * @returns {Array<MdastRootContent>}\n *   mdast children.\n */\nfunction all(parent) {\n  const children = parent.children || []\n  /** @type {Array<MdastRootContent>} */\n  const results = []\n  let index = -1\n\n  while (++index < children.length) {\n    const child = children[index]\n    // Content -> content.\n    const result =\n      /** @type {Array<MdastRootContent> | MdastRootContent | undefined} */ (\n        this.one(child, parent)\n      )\n\n    if (Array.isArray(result)) {\n      results.push(...result)\n    } else if (result) {\n      results.push(result)\n    }\n  }\n\n  return results\n}\n\n/**\n * Transform a hast node to mdast.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Nodes} node\n *   hast node to transform.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Array<MdastNodes> | MdastNodes | undefined}\n *   mdast result.\n */\nfunction one(node, parent) {\n  if (node.type === 'element') {\n    if (node.properties && node.properties.dataMdast === 'ignore') {\n      return\n    }\n\n    if (own.call(this.handlers, node.tagName)) {\n      return this.handlers[node.tagName](this, node, parent) || undefined\n    }\n  } else if (own.call(this.nodeHandlers, node.type)) {\n    return this.nodeHandlers[node.type](this, node, parent) || undefined\n  }\n\n  // Unknown literal.\n  if ('value' in node && typeof node.value === 'string') {\n    /** @type {MdastRootContent} */\n    const result = {type: 'text', value: node.value}\n    this.patch(node, result)\n    return result\n  }\n\n  // Unknown parent.\n  if ('children' in node) {\n    return this.all(node)\n  }\n}\n\n/**\n * Copy a node’s positional info.\n *\n * @param {Nodes} origin\n *   hast node to copy from.\n * @param {MdastNodes} node\n *   mdast node to copy into.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(origin, node) {\n  if (origin.position) node.position = position(origin)\n}\n\n/**\n * @this {State}\n *   Info passed around about the current state.\n * @param {string | null | undefined} url\n *   Possible URL value.\n * @returns {string}\n *   URL, resolved to a `base` element, if any.\n */\nfunction resolve(url) {\n  const base = this.frozenBaseUrl\n\n  if (url === null || url === undefined) {\n    return ''\n  }\n\n  if (base) {\n    return String(new URL(url, base))\n  }\n\n  return url\n}\n\n/**\n * Transform a list of mdast nodes to flow.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Array<MdastRootContent>} nodes\n *   Parent.\n * @returns {Array<MdastFlowContent>}\n *   mdast flow children.\n */\nfunction toFlow(nodes) {\n  return wrap(nodes)\n}\n\n/**\n * Turn arbitrary content into a particular node type.\n *\n * This is useful for example for lists, which must have list items as content.\n * in this example, when non-items are found, they will be queued, and\n * inserted into an adjacent item.\n * When no actual items exist, one will be made with `build`.\n *\n * @template {MdastNodes} ChildType\n *   Node type of children.\n * @template {MdastParents & {'children': Array<ChildType>}} ParentType\n *   Node type of parent.\n * @param {Array<MdastRootContent>} nodes\n *   Nodes, which are either `ParentType`, or will be wrapped in one.\n * @param {() => ParentType} build\n *   Build a parent if needed (must have empty `children`).\n * @returns {Array<ParentType>}\n *   List of parents.\n */\nfunction toSpecificContent(nodes, build) {\n  const reference = build()\n  /** @type {Array<ParentType>} */\n  const results = []\n  /** @type {Array<ChildType>} */\n  let queue = []\n  let index = -1\n\n  while (++index < nodes.length) {\n    const node = nodes[index]\n\n    if (expectedParent(node)) {\n      if (queue.length > 0) {\n        node.children.unshift(...queue)\n        queue = []\n      }\n\n      results.push(node)\n    } else {\n      // Assume `node` can be a child of `ParentType`.\n      // If we start checking nodes, we’d run into problems with unknown nodes,\n      // which we do want to support.\n      const child = /** @type {ChildType} */ (node)\n      queue.push(child)\n    }\n  }\n\n  if (queue.length > 0) {\n    let node = results[results.length - 1]\n\n    if (!node) {\n      node = build()\n      results.push(node)\n    }\n\n    node.children.push(...queue)\n    queue = []\n  }\n\n  return results\n\n  /**\n   * @param {MdastNodes} node\n   * @returns {node is ParentType}\n   */\n  function expectedParent(node) {\n    return node.type === reference.type\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC,GAED;;CAEC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAkJC;;;AAED;AACA;AACA;;;;AAEA,MAAM,MAAM,CAAC,EAAE,cAAc;AAUtB,SAAS,YAAY,OAAO;IACjC,OAAO;QACL;QACA,WAAW;QACX,aAAa,IAAI;QACjB,eAAe;QACf,UAAU;YAAC,GAAG,uKAAA,CAAA,WAAQ;YAAE,GAAG,QAAQ,QAAQ;QAAA;QAC3C,SAAS;QACT,cAAc;YAAC,GAAG,uKAAA,CAAA,eAAY;YAAE,GAAG,QAAQ,YAAY;QAAA;QACvD;QACA;QACA;QACA,UAAU;QACV;QACA;QACA;IACF;AACF;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,IAAI,MAAM;IACjB,MAAM,WAAW,OAAO,QAAQ,IAAI,EAAE;IACtC,oCAAoC,GACpC,MAAM,UAAU,EAAE;IAClB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,SAAS,MAAM,CAAE;QAChC,MAAM,QAAQ,QAAQ,CAAC,MAAM;QAC7B,sBAAsB;QACtB,MAAM,SAEF,IAAI,CAAC,GAAG,CAAC,OAAO;QAGpB,IAAI,MAAM,OAAO,CAAC,SAAS;YACzB,QAAQ,IAAI,IAAI;QAClB,OAAO,IAAI,QAAQ;YACjB,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,OAAO;AACT;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,IAAI,IAAI,EAAE,MAAM;IACvB,IAAI,KAAK,IAAI,KAAK,WAAW;QAC3B,IAAI,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,SAAS,KAAK,UAAU;YAC7D;QACF;QAEA,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,OAAO,GAAG;YACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,OAAO,CAAC,CAAC,IAAI,EAAE,MAAM,WAAW;QAC5D;IACF,OAAO,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,IAAI,GAAG;QACjD,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,IAAI,CAAC,CAAC,IAAI,EAAE,MAAM,WAAW;IAC7D;IAEA,mBAAmB;IACnB,IAAI,WAAW,QAAQ,OAAO,KAAK,KAAK,KAAK,UAAU;QACrD,6BAA6B,GAC7B,MAAM,SAAS;YAAC,MAAM;YAAQ,OAAO,KAAK,KAAK;QAAA;QAC/C,IAAI,CAAC,KAAK,CAAC,MAAM;QACjB,OAAO;IACT;IAEA,kBAAkB;IAClB,IAAI,cAAc,MAAM;QACtB,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB;AACF;AAEA;;;;;;;;;CASC,GACD,SAAS,MAAM,MAAM,EAAE,IAAI;IACzB,IAAI,OAAO,QAAQ,EAAE,KAAK,QAAQ,GAAG,CAAA,GAAA,yJAAA,CAAA,WAAQ,AAAD,EAAE;AAChD;AAEA;;;;;;;CAOC,GACD,SAAS,QAAQ,GAAG;IAClB,MAAM,OAAO,IAAI,CAAC,aAAa;IAE/B,IAAI,QAAQ,QAAQ,QAAQ,WAAW;QACrC,OAAO;IACT;IAEA,IAAI,MAAM;QACR,OAAO,OAAO,IAAI,IAAI,KAAK;IAC7B;IAEA,OAAO;AACT;AAEA;;;;;;;;;CASC,GACD,SAAS,OAAO,KAAK;IACnB,OAAO,CAAA,GAAA,kKAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEA;;;;;;;;;;;;;;;;;;CAkBC,GACD,SAAS,kBAAkB,KAAK,EAAE,KAAK;IACrC,MAAM,YAAY;IAClB,8BAA8B,GAC9B,MAAM,UAAU,EAAE;IAClB,6BAA6B,GAC7B,IAAI,QAAQ,EAAE;IACd,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,MAAM,OAAO,KAAK,CAAC,MAAM;QAEzB,IAAI,eAAe,OAAO;YACxB,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,KAAK,QAAQ,CAAC,OAAO,IAAI;gBACzB,QAAQ,EAAE;YACZ;YAEA,QAAQ,IAAI,CAAC;QACf,OAAO;YACL,gDAAgD;YAChD,yEAAyE;YACzE,+BAA+B;YAC/B,MAAM,QAAkC;YACxC,MAAM,IAAI,CAAC;QACb;IACF;IAEA,IAAI,MAAM,MAAM,GAAG,GAAG;QACpB,IAAI,OAAO,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;QAEtC,IAAI,CAAC,MAAM;YACT,OAAO;YACP,QAAQ,IAAI,CAAC;QACf;QAEA,KAAK,QAAQ,CAAC,IAAI,IAAI;QACtB,QAAQ,EAAE;IACZ;IAEA,OAAO;;IAEP;;;GAGC,GACD,SAAS,eAAe,IAAI;QAC1B,OAAO,KAAK,IAAI,KAAK,UAAU,IAAI;IACrC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5019, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-mdast/lib/index.js"], "sourcesContent": ["/**\n * @import {Options} from 'hast-util-to-mdast'\n * @import {Nodes} from 'hast'\n * @import {Nodes as MdastNodes, RootContent as MdastRootContent} from 'mdast'\n */\n\nimport structuredClone from '@ungap/structured-clone'\nimport rehypeMinifyWhitespace from 'rehype-minify-whitespace'\nimport {visit} from 'unist-util-visit'\nimport {createState} from './state.js'\n\n/** @type {Readonly<Options>} */\nconst emptyOptions = {}\n\n/**\n * Transform hast to mdast.\n *\n * @param {Readonly<Nodes>} tree\n *   hast tree to transform.\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {MdastNodes}\n *   mdast tree.\n */\nexport function toMdast(tree, options) {\n  // We have to clone, cause we’ll use `rehype-minify-whitespace` on the tree,\n  // which modifies.\n  const cleanTree = structuredClone(tree)\n  const settings = options || emptyOptions\n  const transformWhitespace = rehypeMinifyWhitespace({\n    newlines: settings.newlines === true\n  })\n  const state = createState(settings)\n  /** @type {MdastNodes} */\n  let mdast\n\n  // @ts-expect-error: fine to pass an arbitrary node.\n  transformWhitespace(cleanTree)\n\n  visit(cleanTree, function (node) {\n    if (node && node.type === 'element' && node.properties) {\n      const id = String(node.properties.id || '') || undefined\n\n      if (id && !state.elementById.has(id)) {\n        state.elementById.set(id, node)\n      }\n    }\n  })\n\n  const result = state.one(cleanTree, undefined)\n\n  if (!result) {\n    mdast = {type: 'root', children: []}\n  } else if (Array.isArray(result)) {\n    // Assume content.\n    const children = /** @type {Array<MdastRootContent>} */ (result)\n    mdast = {type: 'root', children}\n  } else {\n    mdast = result\n  }\n\n  // Collapse text nodes, and fix whitespace.\n  //\n  // Most of this is taken care of by `rehype-minify-whitespace`, but\n  // we’re generating some whitespace too, and some nodes are in the end\n  // ignored.\n  // So clean up.\n  visit(mdast, function (node, index, parent) {\n    if (node.type === 'text' && index !== undefined && parent) {\n      const previous = parent.children[index - 1]\n\n      if (previous && previous.type === node.type) {\n        previous.value += node.value\n        parent.children.splice(index, 1)\n\n        if (previous.position && node.position) {\n          previous.position.end = node.position.end\n        }\n\n        // Iterate over the previous node again, to handle its total value.\n        return index - 1\n      }\n\n      node.value = node.value.replace(/[\\t ]*(\\r?\\n|\\r)[\\t ]*/, '$1')\n\n      // We don’t care about other phrasing nodes in between (e.g., `[ asd ]()`),\n      // as there the whitespace matters.\n      if (\n        parent &&\n        (parent.type === 'heading' ||\n          parent.type === 'paragraph' ||\n          parent.type === 'root')\n      ) {\n        if (!index) {\n          node.value = node.value.replace(/^[\\t ]+/, '')\n        }\n\n        if (index === parent.children.length - 1) {\n          node.value = node.value.replace(/[\\t ]+$/, '')\n        }\n      }\n\n      if (!node.value) {\n        parent.children.splice(index, 1)\n        return index\n      }\n    }\n  })\n\n  return mdast\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;AACA;AACA;AACA;;;;;AAEA,8BAA8B,GAC9B,MAAM,eAAe,CAAC;AAYf,SAAS,QAAQ,IAAI,EAAE,OAAO;IACnC,4EAA4E;IAC5E,kBAAkB;IAClB,MAAM,YAAY,CAAA,GAAA,8KAAA,CAAA,UAAe,AAAD,EAAE;IAClC,MAAM,WAAW,WAAW;IAC5B,MAAM,sBAAsB,CAAA,GAAA,8JAAA,CAAA,UAAsB,AAAD,EAAE;QACjD,UAAU,SAAS,QAAQ,KAAK;IAClC;IACA,MAAM,QAAQ,CAAA,GAAA,2JAAA,CAAA,cAAW,AAAD,EAAE;IAC1B,uBAAuB,GACvB,IAAI;IAEJ,oDAAoD;IACpD,oBAAoB;IAEpB,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,WAAW,SAAU,IAAI;QAC7B,IAAI,QAAQ,KAAK,IAAI,KAAK,aAAa,KAAK,UAAU,EAAE;YACtD,MAAM,KAAK,OAAO,KAAK,UAAU,CAAC,EAAE,IAAI,OAAO;YAE/C,IAAI,MAAM,CAAC,MAAM,WAAW,CAAC,GAAG,CAAC,KAAK;gBACpC,MAAM,WAAW,CAAC,GAAG,CAAC,IAAI;YAC5B;QACF;IACF;IAEA,MAAM,SAAS,MAAM,GAAG,CAAC,WAAW;IAEpC,IAAI,CAAC,QAAQ;QACX,QAAQ;YAAC,MAAM;YAAQ,UAAU,EAAE;QAAA;IACrC,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS;QAChC,kBAAkB;QAClB,MAAM,WAAmD;QACzD,QAAQ;YAAC,MAAM;YAAQ;QAAQ;IACjC,OAAO;QACL,QAAQ;IACV;IAEA,2CAA2C;IAC3C,EAAE;IACF,mEAAmE;IACnE,sEAAsE;IACtE,WAAW;IACX,eAAe;IACf,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,OAAO,SAAU,IAAI,EAAE,KAAK,EAAE,MAAM;QACxC,IAAI,KAAK,IAAI,KAAK,UAAU,UAAU,aAAa,QAAQ;YACzD,MAAM,WAAW,OAAO,QAAQ,CAAC,QAAQ,EAAE;YAE3C,IAAI,YAAY,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE;gBAC3C,SAAS,KAAK,IAAI,KAAK,KAAK;gBAC5B,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO;gBAE9B,IAAI,SAAS,QAAQ,IAAI,KAAK,QAAQ,EAAE;oBACtC,SAAS,QAAQ,CAAC,GAAG,GAAG,KAAK,QAAQ,CAAC,GAAG;gBAC3C;gBAEA,mEAAmE;gBACnE,OAAO,QAAQ;YACjB;YAEA,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,0BAA0B;YAE1D,2EAA2E;YAC3E,mCAAmC;YACnC,IACE,UACA,CAAC,OAAO,IAAI,KAAK,aACf,OAAO,IAAI,KAAK,eAChB,OAAO,IAAI,KAAK,MAAM,GACxB;gBACA,IAAI,CAAC,OAAO;oBACV,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,WAAW;gBAC7C;gBAEA,IAAI,UAAU,OAAO,QAAQ,CAAC,MAAM,GAAG,GAAG;oBACxC,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,WAAW;gBAC7C;YACF;YAEA,IAAI,CAAC,KAAK,KAAK,EAAE;gBACf,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO;gBAC9B,OAAO;YACT;QACF;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5114, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/rehype-remark/lib/index.js"], "sourcesContent": ["/**\n * @import {Root as HastRoot} from 'hast'\n * @import {Options} from 'hast-util-to-mdast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {Processor} from 'unified'\n * @import {VFile} from 'vfile'\n */\n\n/**\n * @callback TransformBridge\n *   Bridge-mode.\n *\n *   Runs the destination with the new mdast tree.\n *   Discards result.\n * @param {HastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {Promise<undefined>}\n *   Nothing.\n *\n * @callback TransformMutate\n *  Mutate-mode.\n *\n *  Further transformers run on the mdast tree.\n * @param {HastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {MdastRoot}\n *   Tree (mdast).\n */\n\nimport {toMdast} from 'hast-util-to-mdast'\n\n/** @satisfies {Options} */\nconst defaults = {document: true}\n\n/**\n * Turn HTML into markdown.\n *\n * ###### Notes\n *\n * *   if a processor is given, runs the (remark) plugins used on it with an\n *     mdast tree, then discards the result (*bridge mode*)\n * *   otherwise, returns an mdast tree, the plugins used after `rehypeRemark`\n *     are remark plugins (*mutate mode*)\n *\n * > 👉 **Note**: It’s highly unlikely that you want to pass a `processor`.\n *\n * @overload\n * @param {Processor} processor\n * @param {Options | null | undefined} [options]\n * @returns {TransformBridge}\n *\n * @overload\n * @param {Options | null | undefined} [options]\n * @returns {TransformMutate}\n *\n * @overload\n * @param {Options | Processor | null | undefined} [destination]\n * @param {Options | null | undefined} [options]\n * @returns {TransformBridge | TransformMutate}\n *\n * @param {Options | Processor | null | undefined} [destination]\n *   Processor or configuration (optional).\n * @param {Options | null | undefined} [options]\n *   When a processor was given, configuration (optional).\n * @returns {TransformBridge | TransformMutate}\n *   Transform.\n */\nexport default function rehypeRemark(destination, options) {\n  if (destination && 'run' in destination) {\n    /**\n     * @type {TransformBridge}\n     */\n    return async function (tree, file) {\n      const mdastTree = toMdast(tree, {...defaults, ...options})\n      await destination.run(mdastTree, file)\n    }\n  }\n\n  /**\n   * @type {TransformMutate}\n   */\n  return function (tree) {\n    return /** @type {MdastRoot} */ (\n      toMdast(tree, {...defaults, ...destination})\n    )\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED;;;;;;;;;;;;;;;;;;;;;;;CAuBC;;;AAED;;AAEA,yBAAyB,GACzB,MAAM,WAAW;IAAC,UAAU;AAAI;AAmCjB,SAAS,aAAa,WAAW,EAAE,OAAO;IACvD,IAAI,eAAe,SAAS,aAAa;QACvC;;KAEC,GACD,OAAO,eAAgB,IAAI,EAAE,IAAI;YAC/B,MAAM,YAAY,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,MAAM;gBAAC,GAAG,QAAQ;gBAAE,GAAG,OAAO;YAAA;YACxD,MAAM,YAAY,GAAG,CAAC,WAAW;QACnC;IACF;IAEA;;GAEC,GACD,OAAO,SAAU,IAAI;QACnB,OACE,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,MAAM;YAAC,GAAG,QAAQ;YAAE,GAAG,WAAW;QAAA;IAE9C;AACF", "ignoreList": [0], "debugId": null}}]}