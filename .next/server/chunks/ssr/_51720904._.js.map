{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/convex/_generated/api.js"], "sourcesContent": ["/* eslint-disable */\n/**\n * Generated `api` utility.\n *\n * THIS CODE IS AUTOMATICALLY GENERATED.\n *\n * To regenerate, run `npx convex dev`.\n * @module\n */\n\nimport { anyApi, componentsGeneric } from \"convex/server\";\n\n/**\n * A utility for referencing Convex functions in your app's API.\n *\n * Usage:\n * ```js\n * const myFunctionReference = api.myModule.myFunction;\n * ```\n */\nexport const api = anyApi;\nexport const internal = anyApi;\nexport const components = componentsGeneric();\n"], "names": [], "mappings": "AAAA,kBAAkB,GAClB;;;;;;;CAOC;;;;;AAED;AAAA;AAAA;;AAUO,MAAM,MAAM,sJAAA,CAAA,SAAM;AAClB,MAAM,WAAW,sJAAA,CAAA,SAAM;AACvB,MAAM,aAAa,CAAA,GAAA,sLAAA,CAAA,oBAAiB,AAAD", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport { \n  Home, \n  FileText, \n  Clock, \n  Users, \n  Settings, \n  ChevronLeft,\n  ChevronRight,\n  Film\n} from 'lucide-react';\nimport { Button } from '../ui/button';\nimport { cn } from '@/lib/utils';\n\ninterface SidebarProps {\n  className?: string;\n}\n\nconst navigationItems = [\n  {\n    title: 'Dashboard',\n    href: '/',\n    icon: Home,\n  },\n  {\n    title: 'Scripts',\n    href: '/scripts',\n    icon: Film,\n  },\n  {\n    title: 'Recent',\n    href: '/recent',\n    icon: Clock,\n  },\n  {\n    title: 'Shared with me',\n    href: '/shared',\n    icon: Users,\n  },\n  {\n    title: 'Settings',\n    href: '/settings',\n    icon: Settings,\n  },\n];\n\nexport function Sidebar({ className }: SidebarProps) {\n  const [isCollapsed, setIsCollapsed] = useState(false);\n  const pathname = usePathname();\n\n  return (\n    <div\n      className={cn(\n        'relative flex flex-col bg-white border-r border-gray-200 transition-all duration-300',\n        isCollapsed ? 'w-16' : 'w-64',\n        className\n      )}\n    >\n      {/* Header */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n        {!isCollapsed && (\n          <div className=\"flex items-center gap-2\">\n            <Film className=\"text-blue-600\" size={24} />\n            <h2 className=\"text-xl font-semibold text-gray-900\">Scripty</h2>\n          </div>\n        )}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => setIsCollapsed(!isCollapsed)}\n          className=\"h-8 w-8 p-0\"\n        >\n          {isCollapsed ? (\n            <ChevronRight className=\"h-4 w-4\" />\n          ) : (\n            <ChevronLeft className=\"h-4 w-4\" />\n          )}\n        </Button>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 p-4\">\n        <ul className=\"space-y-2\">\n          {navigationItems.map((item) => {\n            const isActive = pathname === item.href || \n              (item.href !== '/' && pathname.startsWith(item.href));\n            \n            return (\n              <li key={item.href}>\n                <Link\n                  href={item.href}\n                  className={cn(\n                    'flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors',\n                    isActive\n                      ? 'bg-blue-50 text-blue-700 border border-blue-200'\n                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900',\n                    isCollapsed && 'justify-center px-2'\n                  )}\n                >\n                  <item.icon className=\"h-5 w-5 flex-shrink-0\" />\n                  {!isCollapsed && <span>{item.title}</span>}\n                </Link>\n              </li>\n            );\n          })}\n        </ul>\n      </nav>\n\n      {/* User section at bottom */}\n      <div className=\"p-4 border-t border-gray-200\">\n        {!isCollapsed ? (\n          <div className=\"flex items-center gap-3 px-3 py-2\">\n            <div className=\"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center\">\n              <span className=\"text-sm font-medium text-blue-700\">U</span>\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-sm font-medium text-gray-900 truncate\">User</p>\n              <p className=\"text-xs text-gray-500 truncate\"><EMAIL></p>\n            </div>\n          </div>\n        ) : (\n          <div className=\"flex justify-center\">\n            <div className=\"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center\">\n              <span className=\"text-sm font-medium text-blue-700\">U</span>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAhBA;;;;;;;;AAsBA,MAAM,kBAAkB;IACtB;QACE,OAAO;QACP,MAAM;QACN,MAAM,mMAAA,CAAA,OAAI;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,kMAAA,CAAA,OAAI;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;IACb;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;IACb;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;IAChB;CACD;AAEM,SAAS,QAAQ,EAAE,SAAS,EAAgB;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wFACA,cAAc,SAAS,QACvB;;0BAIF,8OAAC;gBAAI,WAAU;;oBACZ,CAAC,6BACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;gCAAgB,MAAM;;;;;;0CACtC,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAGxD,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,eAAe,CAAC;wBAC/B,WAAU;kCAET,4BACC,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;iDAExB,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAM7B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BACX,gBAAgB,GAAG,CAAC,CAAC;wBACpB,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;wBAErD,qBACE,8OAAC;sCACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sFACA,WACI,oDACA,uDACJ,eAAe;;kDAGjB,8OAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;oCACpB,CAAC,6BAAe,8OAAC;kDAAM,KAAK,KAAK;;;;;;;;;;;;2BAZ7B,KAAK,IAAI;;;;;oBAgBtB;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;0BACZ,CAAC,4BACA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAAoC;;;;;;;;;;;sCAEtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAC1D,8OAAC;oCAAE,WAAU;8CAAiC;;;;;;;;;;;;;;;;;yCAIlD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlE", "debugId": null}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/auth/SignOutButton.tsx"], "sourcesContent": ["\"use client\";\nimport { useAuthActions } from \"@convex-dev/auth/react\";\nimport { useConvexAuth } from \"convex/react\";\nimport { Button } from '../ui/button';\n\nexport function SignOutButton() {\n  const { isAuthenticated } = useConvexAuth();\n  const { signOut } = useAuthActions();\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <Button\n      variant={\"default\"}\n      size={\"sm\"}\n      onClick={() => void signOut()}\n    >\n      Sign out\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AAHA;;;;;AAKO,SAAS;IACd,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD;IACxC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD;IAEjC,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAS;QACT,MAAM;QACN,SAAS,IAAM,KAAK;kBACrB;;;;;;AAIL", "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { Film } from 'lucide-react';\nimport { SignOutButton } from '../auth/SignOutButton';\n\ninterface HeaderProps {\n  title?: string;\n  showSidebar?: boolean;\n}\n\nexport function Header({ title = 'Scripty', showSidebar = true }: HeaderProps) {\n  return (\n    <header className=\"sticky top-0 z-10 py-3 bg-white/80 backdrop-blur-sm h-16 flex justify-between items-center border-b shadow-sm px-4\">\n      <div className=\"flex items-center gap-2\">\n        {!showSidebar && (\n          <>\n            <Film className=\"text-blue-600\" size={24} />\n            <h2 className=\"text-xl font-semibold text-gray-900\">{title}</h2>\n          </>\n        )}\n      </div>\n      <SignOutButton />\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUO,SAAS,OAAO,EAAE,QAAQ,SAAS,EAAE,cAAc,IAAI,EAAe;IAC3E,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;0BACZ,CAAC,6BACA;;sCACE,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;4BAAgB,MAAM;;;;;;sCACtC,8OAAC;4BAAG,WAAU;sCAAuC;;;;;;;;;;;;;0BAI3D,8OAAC,2IAAA,CAAA,gBAAa;;;;;;;;;;;AAGpB", "debugId": null}}, {"offset": {"line": 454, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/auth/SignInForm.tsx"], "sourcesContent": ["\"use client\";\nimport { useAuthActions } from \"@convex-dev/auth/react\";\nimport { useState } from \"react\";\nimport { toast } from \"sonner\";\n\nexport function SignInForm() {\n  const { signIn } = useAuthActions();\n  const [flow, setFlow] = useState<\"signIn\" | \"signUp\">(\"signIn\");\n  const [submitting, setSubmitting] = useState(false);\n\n  return (\n    <div className=\"w-full\">\n      <form\n        className=\"flex flex-col gap-2\"\n        onSubmit={(e) => {\n          e.preventDefault();\n          setSubmitting(true);\n          const formData = new FormData(e.target as HTMLFormElement);\n          formData.set(\"flow\", flow);\n          void signIn(\"password\", formData).catch((error) => {\n            let toastTitle = \"\";\n            if (error.message.includes(\"Invalid password\")) {\n              toastTitle = \"Invalid password. Please try again.\";\n            } else {\n              toastTitle =\n                flow === \"signIn\"\n                  ? \"Could not sign in, did you mean to sign up?\"\n                  : \"Could not sign up, did you mean to sign in?\";\n            }\n            toast.error(toastTitle);\n            setSubmitting(false);\n          });\n        }}\n      >\n        <input\n          className=\"auth-input-field\"\n          type=\"email\"\n          name=\"email\"\n          placeholder=\"Email\"\n          required\n        />\n        <input\n          className=\"auth-input-field\"\n          type=\"password\"\n          name=\"password\"\n          placeholder=\"Password\"\n          required\n        />\n        <button className=\"auth-button\" type=\"submit\" disabled={submitting}>\n          {flow === \"signIn\" ? \"Sign in\" : \"Sign up\"}\n        </button>\n        <div className=\"text-center text-sm text-secondary\">\n          <span className=\"text-slate-400\">\n            {flow === \"signIn\"\n              ? \"Don't have an account? \"\n              : \"Already have an account? \"}\n          </span>\n          <button\n            type=\"button\"\n            className=\"text-primary text-right hover:text-primary-hover hover:underline font-medium cursor-pointer\"\n            onClick={() => setFlow(flow === \"signIn\" ? \"signUp\" : \"signIn\")}\n          >\n            {flow === \"signIn\" ? \"Sign up instead\" : \"Sign in instead\"}\n          </button>\n        </div>\n      </form>\n      <div className=\"flex items-center justify-center my-3\">\n        <hr className=\"my-4 grow border-gray-200\" />\n        <span className=\"mx-4 text-secondary\">or</span>\n        <hr className=\"my-4 grow border-gray-200\" />\n      </div>\n      <button className=\"auth-button\" onClick={() => void signIn(\"anonymous\")}>\n        Sign in anonymously\n      </button>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAKO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD;IAChC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAU;gBACV,UAAU,CAAC;oBACT,EAAE,cAAc;oBAChB,cAAc;oBACd,MAAM,WAAW,IAAI,SAAS,EAAE,MAAM;oBACtC,SAAS,GAAG,CAAC,QAAQ;oBACrB,KAAK,OAAO,YAAY,UAAU,KAAK,CAAC,CAAC;wBACvC,IAAI,aAAa;wBACjB,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,qBAAqB;4BAC9C,aAAa;wBACf,OAAO;4BACL,aACE,SAAS,WACL,gDACA;wBACR;wBACA,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACZ,cAAc;oBAChB;gBACF;;kCAEA,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,MAAK;wBACL,aAAY;wBACZ,QAAQ;;;;;;kCAEV,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,MAAK;wBACL,aAAY;wBACZ,QAAQ;;;;;;kCAEV,8OAAC;wBAAO,WAAU;wBAAc,MAAK;wBAAS,UAAU;kCACrD,SAAS,WAAW,YAAY;;;;;;kCAEnC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CACb,SAAS,WACN,4BACA;;;;;;0CAEN,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,QAAQ,SAAS,WAAW,WAAW;0CAErD,SAAS,WAAW,oBAAoB;;;;;;;;;;;;;;;;;;0BAI/C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;;;;;kCACd,8OAAC;wBAAK,WAAU;kCAAsB;;;;;;kCACtC,8OAAC;wBAAG,WAAU;;;;;;;;;;;;0BAEhB,8OAAC;gBAAO,WAAU;gBAAc,SAAS,IAAM,KAAK,OAAO;0BAAc;;;;;;;;;;;;AAK/E", "debugId": null}}, {"offset": {"line": 610, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/layout/AppLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport { Authenticated, Unauthenticated } from 'convex/react';\nimport { Sidebar } from './Sidebar';\nimport { Header } from './Header';\nimport { SignInForm } from '../auth/SignInForm';\nimport { FileText } from 'lucide-react';\n\ninterface AppLayoutProps {\n  children: ReactNode;\n  showSidebar?: boolean;\n}\n\nexport function AppLayout({ children, showSidebar = true }: AppLayoutProps) {\n  return (\n    <div className=\"h-screen flex flex-col bg-gray-50 overflow-hidden\" data-testid=\"app-loaded\">\n      <Unauthenticated>\n        <div className=\"flex-1 flex items-center justify-center p-8\">\n          <div className=\"w-full max-w-md mx-auto text-center\">\n            <div className=\"mb-8\">\n              <FileText size={64} className=\"mx-auto mb-4 text-blue-600\" />\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                Scripty\n              </h1>\n              <p className=\"text-gray-600\">\n                Create and edit scripts and screenplays together in real-time\n              </p>\n            </div>\n            <SignInForm />\n          </div>\n        </div>\n      </Unauthenticated>\n\n      <Authenticated>\n        <div className=\"flex-1 flex h-full overflow-hidden\">\n          {showSidebar && <Sidebar />}\n          <div className=\"flex-1 flex flex-col\">\n            <Header showSidebar={showSidebar} />\n            <main className=\"flex-1 overflow-hidden\">\n              {children}\n            </main>\n          </div>\n        </div>\n      </Authenticated>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AACA;AACA;AAPA;;;;;;;AAcO,SAAS,UAAU,EAAE,QAAQ,EAAE,cAAc,IAAI,EAAkB;IACxE,qBACE,8OAAC;QAAI,WAAU;QAAoD,eAAY;;0BAC7E,8OAAC,8JAAA,CAAA,kBAAe;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC9B,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAI/B,8OAAC,wIAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;0BAKjB,8OAAC,8JAAA,CAAA,gBAAa;0BACZ,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,6BAAe,8OAAC,uIAAA,CAAA,UAAO;;;;;sCACxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sIAAA,CAAA,SAAM;oCAAC,aAAa;;;;;;8CACrB,8OAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 749, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 778, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 859, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/scroll-area.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,0KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,0KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 926, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/dialog.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1057, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1085, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/hooks/use-toast.ts"], "sourcesContent": ["import * as React from \"react\"\n\nimport type {\n  ToastActionElement,\n  ToastProps,\n} from \"../components/ui/toast\"\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: \"ADD_TOAST\",\n  UPDATE_TOAST: \"UPDATE_TOAST\",\n  DISMISS_TOAST: \"DISMISS_TOAST\",\n  REMOVE_TOAST: \"REMOVE_TOAST\",\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType[\"ADD_TOAST\"]\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType[\"UPDATE_TOAST\"]\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType[\"DISMISS_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n  | {\n      type: ActionType[\"REMOVE_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: \"REMOVE_TOAST\",\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case \"ADD_TOAST\":\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case \"UPDATE_TOAST\":\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      }\n\n    case \"DISMISS_TOAST\": {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case \"REMOVE_TOAST\":\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach((listener) => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, \"id\">\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: \"UPDATE_TOAST\",\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\n\n  dispatch({\n    type: \"ADD_TOAST\",\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\n  }\n}\n\nexport { useToast, toast }\n"], "names": [], "mappings": ";;;;;AAAA;;AAOA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAS3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAc;IACpC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAA2C,EAAE;AAEnD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS,MAAM,EAAE,GAAG,OAAc;IAChC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAS;IAEhD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,UAAU,IAAI,CAAC;QACf,OAAO;YACL,MAAM,QAAQ,UAAU,OAAO,CAAC;YAChC,IAAI,QAAQ,CAAC,GAAG;gBACd,UAAU,MAAM,CAAC,OAAO;YAC1B;QACF;IACF,GAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAC3E;AACF", "debugId": null}}, {"offset": {"line": 1239, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 1281, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ScriptCard.tsx"], "sourcesContent": ["import { useState } from \"react\";\nimport { useMutation } from \"convex/react\";\nimport { api } from \"../../convex/_generated/api\";\nimport { Button } from \"./ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"./ui/card\";\nimport { Badge } from \"./ui/badge\";\nimport {\n  Trash2,\n  MoreVertical,\n  Calendar,\n  Users,\n  FileText,\n  Film\n} from \"lucide-react\";\nimport { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from \"./ui/dialog\";\nimport { useToast } from \"../hooks/use-toast\";\nimport { Id } from \"../../convex/_generated/dataModel\";\n\ninterface Script {\n  _id: Id<\"scripts\">;\n  _creationTime: number;\n  title: string;\n  description?: string;\n  permission: \"owner\" | \"read\" | \"write\";\n  sharedBy?: Id<\"users\">;\n}\n\ninterface ScriptCardProps {\n  script: Script;\n  viewMode: \"grid\" | \"list\";\n  onSelect: () => void;\n  getPermissionIcon: (permission: string) => React.ReactNode;\n  getPermissionColor: (permission: string) => string;\n}\n\nexport function ScriptCard({\n  script,\n  viewMode,\n  onSelect,\n  getPermissionIcon,\n  getPermissionColor,\n}: ScriptCardProps) {\n  const deleteScript = useMutation(api.scripts.deleteScript);\n  const { toast } = useToast();\n\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\n  const [isDeleting, setIsDeleting] = useState(false);\n\n  const handleDeleteScript = async () => {\n    setIsDeleting(true);\n    try {\n      await deleteScript({ id: script._id });\n      toast({\n        title: \"Success\",\n        description: \"Script deleted successfully\",\n      });\n      setShowDeleteDialog(false);\n    } catch (error) {\n      console.error(\"Failed to delete script:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to delete script. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n\n  const formatDate = (timestamp: number) => {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));\n\n    if (diffInDays === 0) {\n      return \"Today\";\n    } else if (diffInDays === 1) {\n      return \"Yesterday\";\n    } else if (diffInDays < 7) {\n      return `${diffInDays} days ago`;\n    } else {\n      return date.toLocaleDateString();\n    }\n  };\n\n  if (viewMode === \"list\") {\n    return (\n      <>\n        <Card className=\"hover:shadow-md transition-all duration-200 cursor-pointer group\">\n          <CardContent className=\"p-3 sm:p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3 sm:gap-4 flex-1 min-w-0\" onClick={onSelect}>\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n                    <Film className=\"h-5 w-5 sm:h-6 sm:w-6 text-blue-600\" />\n                  </div>\n                </div>\n                \n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-center gap-2 mb-1\">\n                    <h3 className=\"font-semibold text-gray-900 truncate\">{script.title}</h3>\n                    <Badge\n                      variant={getPermissionColor(script.permission) as any}\n                      className=\"gap-1 text-xs flex-shrink-0\"\n                    >\n                      {getPermissionIcon(script.permission)}\n                      {script.permission}\n                    </Badge>\n                  </div>\n                  \n                  {script.description && (\n                    <p className=\"text-sm text-gray-600 line-clamp-1 mb-1\">\n                      {script.description}\n                    </p>\n                  )}\n                  \n                  <div className=\"flex items-center gap-4 text-xs text-gray-500\">\n                    <div className=\"flex items-center gap-1\">\n                      <Calendar className=\"h-3 w-3\" />\n                      {formatDate(script._creationTime)}\n                    </div>\n                    <div className=\"flex items-center gap-1\">\n                      <Users className=\"h-3 w-3\" />\n                      Collaborative\n                    </div>\n                  </div>\n                </div>\n              </div>\n              \n              {script.permission === \"owner\" && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    setShowDeleteDialog(true);\n                  }}\n                  className=\"opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0 text-gray-400 hover:text-red-600 flex-shrink-0\"\n                >\n                  <Trash2 className=\"h-4 w-4\" />\n                </Button>\n              )}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Delete Dialog */}\n        <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\n          <DialogContent>\n            <DialogHeader>\n              <DialogTitle>Delete Script</DialogTitle>\n            </DialogHeader>\n            <p className=\"text-sm text-gray-600\">\n              Are you sure you want to delete \"{script.title}\"? This will also delete all documents within the script. This action cannot be undone.\n            </p>\n            <DialogFooter>\n              <Button\n                variant=\"outline\"\n                onClick={() => setShowDeleteDialog(false)}\n                disabled={isDeleting}\n              >\n                Cancel\n              </Button>\n              <Button\n                variant=\"destructive\"\n                onClick={handleDeleteScript}\n                disabled={isDeleting}\n              >\n                {isDeleting ? \"Deleting...\" : \"Delete Script\"}\n              </Button>\n            </DialogFooter>\n          </DialogContent>\n        </Dialog>\n      </>\n    );\n  }\n\n  // Grid view\n  return (\n    <>\n      <Card className=\"hover:shadow-lg transition-all duration-200 cursor-pointer group h-full\">\n        <CardHeader className=\"pb-2 sm:pb-3 p-4 sm:p-6\">\n          <div className=\"flex items-start justify-between\">\n            <div className=\"flex-1 min-w-0\" onClick={onSelect}>\n              <div className=\"w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-2 sm:mb-3\">\n                <Film className=\"h-5 w-5 sm:h-6 sm:w-6 text-blue-600\" />\n              </div>\n              <CardTitle className=\"text-base sm:text-lg line-clamp-2 mb-2\">{script.title}</CardTitle>\n            </div>\n            {script.permission === \"owner\" && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={(e) => {\n                  e.stopPropagation();\n                  setShowDeleteDialog(true);\n                }}\n                className=\"opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0 text-gray-400 hover:text-red-600 flex-shrink-0\"\n              >\n                <Trash2 className=\"h-4 w-4\" />\n              </Button>\n            )}\n          </div>\n        </CardHeader>\n\n        <CardContent className=\"pt-0 p-4 sm:p-6\" onClick={onSelect}>\n          {script.description && (\n            <p className=\"text-sm text-gray-600 line-clamp-3 mb-4\">\n              {script.description}\n            </p>\n          )}\n          \n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between\">\n              <Badge\n                variant={getPermissionColor(script.permission) as any}\n                className=\"gap-1 text-xs\"\n              >\n                {getPermissionIcon(script.permission)}\n                {script.permission}\n              </Badge>\n              <div className=\"flex items-center gap-1 text-xs text-gray-500\">\n                <Users className=\"h-3 w-3\" />\n                <span>Collaborative</span>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center gap-1 text-xs text-gray-500\">\n              <Calendar className=\"h-3 w-3\" />\n              <span>{formatDate(script._creationTime)}</span>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Delete Dialog */}\n      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>Delete Script</DialogTitle>\n          </DialogHeader>\n          <p className=\"text-sm text-gray-600\">\n            Are you sure you want to delete \"{script.title}\"? This will also delete all documents within the script. This action cannot be undone.\n          </p>\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowDeleteDialog(false)}\n              disabled={isDeleting}\n            >\n              Cancel\n            </Button>\n            <Button\n              variant=\"destructive\"\n              onClick={handleDeleteScript}\n              disabled={isDeleting}\n            >\n              {isDeleting ? \"Deleting...\" : \"Delete Script\"}\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAQA;AACA;;;;;;;;;;;AAoBO,SAAS,WAAW,EACzB,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,iBAAiB,EACjB,kBAAkB,EACF;IAChB,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,YAAY;IACzD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,qBAAqB;QACzB,cAAc;QACd,IAAI;YACF,MAAM,aAAa;gBAAE,IAAI,OAAO,GAAG;YAAC;YACpC,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YACA,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,aAAa,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;QAErF,IAAI,eAAe,GAAG;YACpB,OAAO;QACT,OAAO,IAAI,eAAe,GAAG;YAC3B,OAAO;QACT,OAAO,IAAI,aAAa,GAAG;YACzB,OAAO,GAAG,WAAW,SAAS,CAAC;QACjC,OAAO;YACL,OAAO,KAAK,kBAAkB;QAChC;IACF;IAEA,IAAI,aAAa,QAAQ;QACvB,qBACE;;8BACE,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAAkD,SAAS;;sDACxE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAIpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAwC,OAAO,KAAK;;;;;;sEAClE,8OAAC,iIAAA,CAAA,QAAK;4DACJ,SAAS,mBAAmB,OAAO,UAAU;4DAC7C,WAAU;;gEAET,kBAAkB,OAAO,UAAU;gEACnC,OAAO,UAAU;;;;;;;;;;;;;gDAIrB,OAAO,WAAW,kBACjB,8OAAC;oDAAE,WAAU;8DACV,OAAO,WAAW;;;;;;8DAIvB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,WAAW,OAAO,aAAa;;;;;;;sEAElC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;;;;;;;;;;;;;;gCAOpC,OAAO,UAAU,KAAK,yBACrB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,CAAC;wCACR,EAAE,eAAe;wCACjB,oBAAoB;oCACtB;oCACA,WAAU;8CAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ5B,8OAAC,kIAAA,CAAA,SAAM;oBAAC,MAAM;oBAAkB,cAAc;8BAC5C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;;0CACZ,8OAAC,kIAAA,CAAA,eAAY;0CACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;8CAAC;;;;;;;;;;;0CAEf,8OAAC;gCAAE,WAAU;;oCAAwB;oCACD,OAAO,KAAK;oCAAC;;;;;;;0CAEjD,8OAAC,kIAAA,CAAA,eAAY;;kDACX,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,oBAAoB;wCACnC,UAAU;kDACX;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;wCACT,UAAU;kDAET,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;IAO5C;IAEA,YAAY;IACZ,qBACE;;0BACE,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAAiB,SAAS;;sDACvC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAA0C,OAAO,KAAK;;;;;;;;;;;;gCAE5E,OAAO,UAAU,KAAK,yBACrB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,CAAC;wCACR,EAAE,eAAe;wCACjB,oBAAoB;oCACtB;oCACA,WAAU;8CAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAM1B,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;wBAAkB,SAAS;;4BAC/C,OAAO,WAAW,kBACjB,8OAAC;gCAAE,WAAU;0CACV,OAAO,WAAW;;;;;;0CAIvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAS,mBAAmB,OAAO,UAAU;gDAC7C,WAAU;;oDAET,kBAAkB,OAAO,UAAU;oDACnC,OAAO,UAAU;;;;;;;0DAEpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;kDAIV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAM,WAAW,OAAO,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9C,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;;sCACZ,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;0CAAC;;;;;;;;;;;sCAEf,8OAAC;4BAAE,WAAU;;gCAAwB;gCACD,OAAO,KAAK;gCAAC;;;;;;;sCAEjD,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,oBAAoB;oCACnC,UAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;8CAET,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C", "debugId": null}}, {"offset": {"line": 1839, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ScriptDashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from \"react\";\nimport * as React from \"react\";\nimport { useQuery, useMutation } from \"convex/react\";\nimport { api } from \"../../convex/_generated/api\";\nimport { But<PERSON> } from \"./ui/button\";\nimport { Input } from \"./ui/input\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"./ui/card\";\nimport { Badge } from \"./ui/badge\";\nimport { ScrollArea } from \"./ui/scroll-area\";\nimport {\n  Plus,\n  Search,\n  Grid3X3,\n  List,\n  Film,\n  Users,\n  Clock,\n  Crown,\n  Eye,\n  Edit,\n  Filter,\n  SortAsc,\n  SortDesc,\n  Calendar\n} from \"lucide-react\";\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from \"./ui/dialog\";\nimport { Textarea } from \"./ui/textarea\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { Id } from \"../../convex/_generated/dataModel\";\nimport { ScriptCard } from \"./ScriptCard\";\n\ninterface ScriptDashboardProps {\n  onSelectScript: (scriptId: Id<\"scripts\">) => void;\n  onScriptCountChange?: (count: number) => void;\n}\n\ntype ViewMode = \"grid\" | \"list\";\ntype SortOption = \"recent\" | \"title\" | \"oldest\";\n\nexport function ScriptDashboard({ onSelectScript, onScriptCountChange }: ScriptDashboardProps) {\n  const scripts = useQuery(api.scripts.getUserScripts);\n  const canCreateResult = useQuery(api.scripts.canCreateScripts);\n  const createScript = useMutation(api.scripts.createScript);\n  const { toast } = useToast();\n\n  // UI State\n  const [viewMode, setViewMode] = useState<ViewMode>(\"list\");\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [sortBy, setSortBy] = useState<SortOption>(\"recent\");\n  const [showCreateDialog, setShowCreateDialog] = useState(false);\n  const [isCreating, setIsCreating] = useState(false);\n\n  // Create Script Form State\n  const [newScriptTitle, setNewScriptTitle] = useState(\"\");\n  const [newScriptDescription, setNewScriptDescription] = useState(\"\");\n\n  // Extract canCreate boolean for backward compatibility\n  const canCreate = canCreateResult?.canCreate ?? false;\n\n  // Update script count when scripts change\n  React.useEffect(() => {\n    if (scripts && onScriptCountChange) {\n      onScriptCountChange(scripts.length);\n    }\n  }, [scripts, onScriptCountChange]);\n\n  // Filter and sort scripts\n  const filteredAndSortedScripts = React.useMemo(() => {\n    if (!scripts) return [];\n\n    let filtered = scripts;\n\n    // Apply search filter\n    if (searchQuery.trim()) {\n      const query = searchQuery.toLowerCase();\n      filtered = scripts.filter(\n        (script) =>\n          script.title.toLowerCase().includes(query) ||\n          script.description?.toLowerCase().includes(query)\n      );\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"title\":\n          return a.title.localeCompare(b.title);\n        case \"oldest\":\n          return a._creationTime - b._creationTime;\n        case \"recent\":\n        default:\n          return b._creationTime - a._creationTime;\n      }\n    });\n\n    return sorted;\n  }, [scripts, searchQuery, sortBy]);\n\n  const handleCreateScript = async () => {\n    if (!newScriptTitle.trim()) {\n      toast({\n        title: \"Error\",\n        description: \"Please enter a script title\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    setIsCreating(true);\n    try {\n      const scriptId = await createScript({\n        title: newScriptTitle.trim(),\n        description: newScriptDescription.trim() || undefined,\n      });\n\n      toast({\n        title: \"Success\",\n        description: \"Script created successfully\",\n      });\n\n      // Reset form\n      setNewScriptTitle(\"\");\n      setNewScriptDescription(\"\");\n      setShowCreateDialog(false);\n\n      // Navigate to the new script\n      onSelectScript(scriptId);\n    } catch (error) {\n      console.error(\"Failed to create script:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to create script. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsCreating(false);\n    }\n  };\n\n  const handleShowCreateDialog = () => {\n    setNewScriptTitle(\"\");\n    setNewScriptDescription(\"\");\n    setShowCreateDialog(true);\n  };\n\n  const getPermissionIcon = (permission: string) => {\n    switch (permission) {\n      case \"owner\":\n        return <Crown className=\"h-3 w-3\" />;\n      case \"write\":\n        return <Edit className=\"h-3 w-3\" />;\n      case \"read\":\n        return <Eye className=\"h-3 w-3\" />;\n      default:\n        return <Eye className=\"h-3 w-3\" />;\n    }\n  };\n\n  const getPermissionColor = (permission: string) => {\n    switch (permission) {\n      case \"owner\":\n        return \"default\";\n      case \"write\":\n        return \"secondary\";\n      case \"read\":\n        return \"outline\";\n      default:\n        return \"outline\";\n    }\n  };\n\n  if (!scripts) {\n    return (\n      <div className=\"flex-1 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex-1 flex flex-col bg-gray-50 h-full\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200 px-6 py-4\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div>\n            <h1 className=\"text-xl font-bold text-gray-900\">Your Scripts</h1>\n            <p className=\"text-gray-600 text-sm\">\n              {scripts.length === 0\n                ? \"Create your first script to get started\"\n                : `${scripts.length} script${scripts.length === 1 ? \"\" : \"s\"} available`}\n            </p>\n          </div>\n          {canCreate && (\n            <Button\n              onClick={handleShowCreateDialog}\n              disabled={isCreating}\n              className=\"gap-2 bg-blue-600 hover:bg-blue-700\"\n            >\n              <Plus className=\"h-4 w-4\" />\n              New Script\n            </Button>\n          )}\n        </div>\n\n        {/* Search and Controls */}\n        <div className=\"flex flex-col sm:flex-row items-stretch sm:items-center gap-4\">\n          <div className=\"relative flex-1 max-w-md\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <Input\n              placeholder=\"Search scripts...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"pl-10\"\n            />\n          </div>\n\n          <div className=\"flex items-center gap-2 justify-between sm:justify-start\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => setSortBy(sortBy === \"recent\" ? \"oldest\" : \"recent\")}\n              className=\"gap-2 flex-1 sm:flex-none\"\n            >\n              {sortBy === \"recent\" ? <SortDesc className=\"h-4 w-4\" /> : <SortAsc className=\"h-4 w-4\" />}\n              <span className=\"hidden sm:inline\">{sortBy === \"recent\" ? \"Newest\" : \"Oldest\"}</span>\n            </Button>\n\n            <div className=\"flex border rounded-md\">\n              <Button\n                variant={viewMode === \"grid\" ? \"default\" : \"ghost\"}\n                size=\"sm\"\n                onClick={() => setViewMode(\"grid\")}\n                className=\"rounded-r-none\"\n                title=\"Grid view\"\n              >\n                <Grid3X3 className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant={viewMode === \"list\" ? \"default\" : \"ghost\"}\n                size=\"sm\"\n                onClick={() => setViewMode(\"list\")}\n                className=\"rounded-l-none\"\n                title=\"List view\"\n              >\n                <List className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <ScrollArea className=\"flex-1\">\n        <div className=\"p-6\">\n          {!canCreate && (\n            <Card className=\"border-amber-200 bg-amber-50 mb-6\">\n              <CardContent className=\"p-4\">\n                <div className=\"flex items-start gap-3 text-amber-800\">\n                  <Film className=\"h-5 w-5 mt-0.5 flex-shrink-0\" />\n                  <div>\n                    <p className=\"font-medium\">Limited Access</p>\n                    <p className=\"text-sm\">\n                      {canCreateResult?.reason === \"anonymous_user\"\n                        ? \"Anonymous users can only view shared scripts.\"\n                        : canCreateResult?.reason === \"not_authenticated\"\n                          ? \"Please sign in to create and manage scripts.\"\n                          : \"You can view shared scripts but cannot create new ones.\"\n                      }\n                    </p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {filteredAndSortedScripts.length === 0 ? (\n            <div className=\"text-center py-16\">\n              {searchQuery ? (\n                <div className=\"text-gray-500\">\n                  <Search size={48} className=\"mx-auto mb-4 opacity-50\" />\n                  <h3 className=\"text-lg font-medium mb-2\">No scripts found</h3>\n                  <p className=\"text-sm\">Try adjusting your search terms</p>\n                </div>\n              ) : (\n                <div className=\"text-gray-500\">\n                  <Film size={64} className=\"mx-auto mb-6 opacity-50\" />\n                  <h3 className=\"text-xl font-semibold mb-3 text-gray-900\">No scripts yet</h3>\n                  <p className=\"text-gray-600 mb-6\">\n                    {canCreate\n                      ? \"Create your first script to get started with collaborative screenwriting\"\n                      : \"You don't have access to any scripts yet\"}\n                  </p>\n                  {canCreate && (\n                    <Button onClick={handleShowCreateDialog} className=\"gap-2\">\n                      <Plus className=\"h-4 w-4\" />\n                      Create Your First Script\n                    </Button>\n                  )}\n                </div>\n              )}\n            </div>\n          ) : (\n            <div\n              className={\n                viewMode === \"grid\"\n                  ? \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 sm:gap-6\"\n                  : \"space-y-3 sm:space-y-4\"\n              }\n            >\n              {filteredAndSortedScripts.map((script) => (\n                <ScriptCard\n                  key={script._id}\n                  script={script}\n                  viewMode={viewMode}\n                  onSelect={() => onSelectScript(script._id)}\n                  getPermissionIcon={getPermissionIcon}\n                  getPermissionColor={getPermissionColor}\n                />\n              ))}\n            </div>\n          )}\n        </div>\n      </ScrollArea>\n\n      {/* Create Script Dialog */}\n      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>Create New Script</DialogTitle>\n          </DialogHeader>\n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"script-title\" className=\"text-sm font-medium\">\n                Title *\n              </label>\n              <Input\n                id=\"script-title\"\n                value={newScriptTitle}\n                onChange={(e) => setNewScriptTitle(e.target.value)}\n                placeholder=\"Enter script title...\"\n                className=\"mt-1\"\n              />\n            </div>\n            <div>\n              <label htmlFor=\"script-description\" className=\"text-sm font-medium\">\n                Description\n              </label>\n              <Textarea\n                id=\"script-description\"\n                value={newScriptDescription}\n                onChange={(e) => setNewScriptDescription(e.target.value)}\n                placeholder=\"Enter script description...\"\n                className=\"mt-1\"\n                rows={3}\n              />\n            </div>\n          </div>\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowCreateDialog(false)}\n              disabled={isCreating}\n            >\n              Cancel\n            </Button>\n            <Button onClick={handleCreateScript} disabled={isCreating}>\n              {isCreating ? \"Creating...\" : \"Create Script\"}\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AACA;AAEA;AA/BA;;;;;;;;;;;;;;;AAyCO,SAAS,gBAAgB,EAAE,cAAc,EAAE,mBAAmB,EAAwB;IAC3F,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,cAAc;IACnD,MAAM,kBAAkB,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,gBAAgB;IAC7D,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,YAAY;IACzD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAEzB,WAAW;IACX,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,2BAA2B;IAC3B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjE,uDAAuD;IACvD,MAAM,YAAY,iBAAiB,aAAa;IAEhD,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,WAAW,qBAAqB;YAClC,oBAAoB,QAAQ,MAAM;QACpC;IACF,GAAG;QAAC;QAAS;KAAoB;IAEjC,0BAA0B;IAC1B,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC7C,IAAI,CAAC,SAAS,OAAO,EAAE;QAEvB,IAAI,WAAW;QAEf,sBAAsB;QACtB,IAAI,YAAY,IAAI,IAAI;YACtB,MAAM,QAAQ,YAAY,WAAW;YACrC,WAAW,QAAQ,MAAM,CACvB,CAAC,SACC,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,UACpC,OAAO,WAAW,EAAE,cAAc,SAAS;QAEjD;QAEA,gBAAgB;QAChB,MAAM,SAAS;eAAI;SAAS,CAAC,IAAI,CAAC,CAAC,GAAG;YACpC,OAAQ;gBACN,KAAK;oBACH,OAAO,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE,KAAK;gBACtC,KAAK;oBACH,OAAO,EAAE,aAAa,GAAG,EAAE,aAAa;gBAC1C,KAAK;gBACL;oBACE,OAAO,EAAE,aAAa,GAAG,EAAE,aAAa;YAC5C;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAS;QAAa;KAAO;IAEjC,MAAM,qBAAqB;QACzB,IAAI,CAAC,eAAe,IAAI,IAAI;YAC1B,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,cAAc;QACd,IAAI;YACF,MAAM,WAAW,MAAM,aAAa;gBAClC,OAAO,eAAe,IAAI;gBAC1B,aAAa,qBAAqB,IAAI,MAAM;YAC9C;YAEA,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,aAAa;YACb,kBAAkB;YAClB,wBAAwB;YACxB,oBAAoB;YAEpB,6BAA6B;YAC7B,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,yBAAyB;QAC7B,kBAAkB;QAClB,wBAAwB;QACxB,oBAAoB;IACtB;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,2MAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB;gBACE,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;QAC1B;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,8OAAC;wCAAE,WAAU;kDACV,QAAQ,MAAM,KAAK,IAChB,4CACA,GAAG,QAAQ,MAAM,CAAC,OAAO,EAAE,QAAQ,MAAM,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC;;;;;;;;;;;;4BAG7E,2BACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;kCAOlC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAId,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,UAAU,WAAW,WAAW,WAAW;wCAC1D,WAAU;;4CAET,WAAW,yBAAW,8OAAC,iOAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;qEAAe,8OAAC,8NAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DAC7E,8OAAC;gDAAK,WAAU;0DAAoB,WAAW,WAAW,WAAW;;;;;;;;;;;;kDAGvE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;gDACV,OAAM;0DAEN,cAAA,8OAAC,4MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;gDACV,OAAM;0DAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1B,8OAAC,0IAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,CAAC,2BACA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,8OAAC;oDAAE,WAAU;8DACV,iBAAiB,WAAW,mBACzB,kDACA,iBAAiB,WAAW,sBAC1B,iDACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBASjB,yBAAyB,MAAM,KAAK,kBACnC,8OAAC;4BAAI,WAAU;sCACZ,4BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC5B,8OAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,8OAAC;wCAAE,WAAU;kDAAU;;;;;;;;;;;qDAGzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC1B,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAE,WAAU;kDACV,YACG,6EACA;;;;;;oCAEL,2BACC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;wCAAwB,WAAU;;0DACjD,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;iDAQtC,8OAAC;4BACC,WACE,aAAa,SACT,iGACA;sCAGL,yBAAyB,GAAG,CAAC,CAAC,uBAC7B,8OAAC,gIAAA,CAAA,aAAU;oCAET,QAAQ;oCACR,UAAU;oCACV,UAAU,IAAM,eAAe,OAAO,GAAG;oCACzC,mBAAmB;oCACnB,oBAAoB;mCALf,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;0BAc3B,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;;sCACZ,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;0CAAC;;;;;;;;;;;sCAEf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAe,WAAU;sDAAsB;;;;;;sDAG9D,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CACjD,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAGd,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAqB,WAAU;sDAAsB;;;;;;sDAGpE,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,wBAAwB,EAAE,MAAM,CAAC,KAAK;4CACvD,aAAY;4CACZ,WAAU;4CACV,MAAM;;;;;;;;;;;;;;;;;;sCAIZ,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,oBAAoB;oCACnC,UAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAoB,UAAU;8CAC5C,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C", "debugId": null}}, {"offset": {"line": 2537, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/scripts/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useQuery } from \"convex/react\";\nimport { api } from \"../../convex/_generated/api\";\nimport { AppLayout } from \"../components/layout/AppLayout\";\nimport { ScriptDashboard } from \"../components/ScriptDashboard\";\nimport { useRouter } from \"next/navigation\";\nimport { useState } from \"react\";\n\nexport default function ScriptsPage() {\n  const router = useRouter();\n  const [scriptCount, setScriptCount] = useState<number>(0);\n  const loggedInUser = useQuery(api.auth.loggedInUser);\n\n  const navigateToScript = (scriptId: string) => {\n    router.push(`/script/${scriptId}`);\n  };\n\n  if (loggedInUser === undefined) {\n    return (\n      <AppLayout>\n        <div className=\"flex-1 flex justify-center items-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n        </div>\n      </AppLayout>\n    );\n  }\n\n  return (\n    <AppLayout>\n      <ScriptDashboard\n        onSelectScript={navigateToScript}\n        onScriptCountChange={setScriptCount}\n      />\n    </AppLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,IAAI,CAAC,YAAY;IAEnD,MAAM,mBAAmB,CAAC;QACxB,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU;IACnC;IAEA,IAAI,iBAAiB,WAAW;QAC9B,qBACE,8OAAC,yIAAA,CAAA,YAAS;sBACR,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC,yIAAA,CAAA,YAAS;kBACR,cAAA,8OAAC,qIAAA,CAAA,kBAAe;YACd,gBAAgB;YAChB,qBAAqB;;;;;;;;;;;AAI7B", "debugId": null}}]}