{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/rehype-format/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast-util-format').Options} Options\n */\n\nexport {default} from './lib/index.js'\n"], "names": [], "mappings": "AAAA;;CAEC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-is-element/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Parents} Parents\n */\n\n/**\n * @template Fn\n * @template Fallback\n * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate\n */\n\n/**\n * @callback Check\n *   Check that an arbitrary value is an element.\n * @param {unknown} this\n *   Context object (`this`) to call `test` with\n * @param {unknown} [element]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   Position of `element` in its parent.\n * @param {Parents | null | undefined} [parent]\n *   Parent of `element`.\n * @returns {boolean}\n *   Whether this is an element and passes a test.\n *\n * @typedef {Array<TestFunction | string> | TestFunction | string | null | undefined} Test\n *   Check for an arbitrary element.\n *\n *   * when `string`, checks that the element has that tag name\n *   * when `function`, see `TestFunction`\n *   * when `Array`, checks if one of the subtests pass\n *\n * @callback TestFunction\n *   Check if an element passes a test.\n * @param {unknown} this\n *   The given context.\n * @param {Element} element\n *   An element.\n * @param {number | undefined} [index]\n *   Position of `element` in its parent.\n * @param {Parents | undefined} [parent]\n *   Parent of `element`.\n * @returns {boolean | undefined | void}\n *   Whether this element passes the test.\n *\n *   Note: `void` is included until TS sees no return as `undefined`.\n */\n\n/**\n * Check if `element` is an `Element` and whether it passes the given test.\n *\n * @param element\n *   Thing to check, typically `element`.\n * @param test\n *   Check for a specific element.\n * @param index\n *   Position of `element` in its parent.\n * @param parent\n *   Parent of `element`.\n * @param context\n *   Context object (`this`) to call `test` with.\n * @returns\n *   Whether `element` is an `Element` and passes a test.\n * @throws\n *   When an incorrect `test`, `index`, or `parent` is given; there is no error\n *   thrown when `element` is not a node or not an element.\n */\nexport const isElement =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends TestFunction>(element: unknown, test: Condition, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & Predicate<Condition, Element>) &\n   *   (<Condition extends string>(element: unknown, test: Condition, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & {tagName: Condition}) &\n   *   ((element?: null | undefined) => false) &\n   *   ((element: unknown, test?: null | undefined, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element) &\n   *   ((element: unknown, test?: Test, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [element]\n     * @param {Test | undefined} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parents | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function (element, test, index, parent, context) {\n      const check = convertElement(test)\n\n      if (\n        index !== null &&\n        index !== undefined &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite `index`')\n      }\n\n      if (\n        parent !== null &&\n        parent !== undefined &&\n        (!parent.type || !parent.children)\n      ) {\n        throw new Error('Expected valid `parent`')\n      }\n\n      if (\n        (index === null || index === undefined) !==\n        (parent === null || parent === undefined)\n      ) {\n        throw new Error('Expected both `index` and `parent`')\n      }\n\n      return looksLikeAnElement(element)\n        ? check.call(context, element, index, parent)\n        : false\n    }\n  )\n\n/**\n * Generate a check from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * an `element`, `index`, and `parent`.\n *\n * @param test\n *   A test for a specific element.\n * @returns\n *   A check.\n */\nexport const convertElement =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends TestFunction>(test: Condition) => (element: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & Predicate<Condition, Element>) &\n   *   (<Condition extends string>(test: Condition) => (element: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & {tagName: Condition}) &\n   *   ((test?: null | undefined) => (element?: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element) &\n   *   ((test?: Test) => Check)\n   * )}\n   */\n  (\n    /**\n     * @param {Test | null | undefined} [test]\n     * @returns {Check}\n     */\n    function (test) {\n      if (test === null || test === undefined) {\n        return element\n      }\n\n      if (typeof test === 'string') {\n        return tagNameFactory(test)\n      }\n\n      // Assume array.\n      if (typeof test === 'object') {\n        return anyFactory(test)\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      throw new Error('Expected function, string, or array as `test`')\n    }\n  )\n\n/**\n * Handle multiple tests.\n *\n * @param {Array<TestFunction | string>} tests\n * @returns {Check}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<Check>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convertElement(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @type {TestFunction}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].apply(this, parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn a string into a test for an element with a certain type.\n *\n * @param {string} check\n * @returns {Check}\n */\nfunction tagNameFactory(check) {\n  return castFactory(tagName)\n\n  /**\n   * @param {Element} element\n   * @returns {boolean}\n   */\n  function tagName(element) {\n    return element.tagName === check\n  }\n}\n\n/**\n * Turn a custom test into a test for an element that passes that test.\n *\n * @param {TestFunction} testFunction\n * @returns {Check}\n */\nfunction castFactory(testFunction) {\n  return check\n\n  /**\n   * @this {unknown}\n   * @type {Check}\n   */\n  function check(value, index, parent) {\n    return Boolean(\n      looksLikeAnElement(value) &&\n        testFunction.call(\n          this,\n          value,\n          typeof index === 'number' ? index : undefined,\n          parent || undefined\n        )\n    )\n  }\n}\n\n/**\n * Make sure something is an element.\n *\n * @param {unknown} element\n * @returns {element is Element}\n */\nfunction element(element) {\n  return Boolean(\n    element &&\n      typeof element === 'object' &&\n      'type' in element &&\n      element.type === 'element' &&\n      'tagName' in element &&\n      typeof element.tagName === 'string'\n  )\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Element}\n */\nfunction looksLikeAnElement(value) {\n  return (\n    value !== null &&\n    typeof value === 'object' &&\n    'type' in value &&\n    'tagName' in value\n  )\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;CAIC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmCC,GAED;;;;;;;;;;;;;;;;;;CAkBC;;;;AACM,MAAM,YAYT;;;;;;;KAOC,GACD,sCAAsC;AACtC,SAAU,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO;IAC7C,MAAM,QAAQ,eAAe;IAE7B,IACE,UAAU,QACV,UAAU,aACV,CAAC,OAAO,UAAU,YAChB,QAAQ,KACR,UAAU,OAAO,iBAAiB,GACpC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,IACE,WAAW,QACX,WAAW,aACX,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,QAAQ,GACjC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,IACE,CAAC,UAAU,QAAQ,UAAU,SAAS,MACtC,CAAC,WAAW,QAAQ,WAAW,SAAS,GACxC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,mBAAmB,WACtB,MAAM,IAAI,CAAC,SAAS,SAAS,OAAO,UACpC;AACN;AAiBG,MAAM,iBAWT;;;KAGC,GACD,SAAU,IAAI;IACZ,IAAI,SAAS,QAAQ,SAAS,WAAW;QACvC,OAAO;IACT;IAEA,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,eAAe;IACxB;IAEA,gBAAgB;IAChB,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,WAAW;IACpB;IAEA,IAAI,OAAO,SAAS,YAAY;QAC9B,OAAO,YAAY;IACrB;IAEA,MAAM,IAAI,MAAM;AAClB;AAGJ;;;;;CAKC,GACD,SAAS,WAAW,KAAK;IACvB,yBAAyB,GACzB,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,MAAM,CAAC,MAAM,GAAG,eAAe,KAAK,CAAC,MAAM;IAC7C;IAEA,OAAO,YAAY;;IAEnB;;;GAGC,GACD,SAAS,IAAI,GAAG,UAAU;QACxB,IAAI,QAAQ,CAAC;QAEb,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;YAC9B,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,aAAa,OAAO;QACpD;QAEA,OAAO;IACT;AACF;AAEA;;;;;CAKC,GACD,SAAS,eAAe,KAAK;IAC3B,OAAO,YAAY;;IAEnB;;;GAGC,GACD,SAAS,QAAQ,OAAO;QACtB,OAAO,QAAQ,OAAO,KAAK;IAC7B;AACF;AAEA;;;;;CAKC,GACD,SAAS,YAAY,YAAY;IAC/B,OAAO;;IAEP;;;GAGC,GACD,SAAS,MAAM,KAAK,EAAE,KAAK,EAAE,MAAM;QACjC,OAAO,QACL,mBAAmB,UACjB,aAAa,IAAI,CACf,IAAI,EACJ,OACA,OAAO,UAAU,WAAW,QAAQ,WACpC,UAAU;IAGlB;AACF;AAEA;;;;;CAKC,GACD,SAAS,QAAQ,OAAO;IACtB,OAAO,QACL,WACE,OAAO,YAAY,YACnB,UAAU,WACV,QAAQ,IAAI,KAAK,aACjB,aAAa,WACb,OAAO,QAAQ,OAAO,KAAK;AAEjC;AAEA;;;CAGC,GACD,SAAS,mBAAmB,KAAK;IAC/B,OACE,UAAU,QACV,OAAO,UAAU,YACjB,UAAU,SACV,aAAa;AAEjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-embedded/lib/index.js"], "sourcesContent": ["import {convertElement} from 'hast-util-is-element'\n\n/**\n * Check if a node is a *embedded content*.\n *\n * @param value\n *   Thing to check (typically `Node`).\n * @returns\n *   Whether `value` is an element considered embedded content.\n *\n *   The elements `audio`, `canvas`, `embed`, `iframe`, `img`, `math`,\n *   `object`, `picture`, `svg`, and `video` are embedded content.\n */\nexport const embedded = convertElement(\n  /**\n   * @param element\n   * @returns {element is {tagName: 'audio' | 'canvas' | 'embed' | 'iframe' | 'img' | 'math' | 'object' | 'picture' | 'svg' | 'video'}}\n   */\n  function (element) {\n    return (\n      element.tagName === 'audio' ||\n      element.tagName === 'canvas' ||\n      element.tagName === 'embed' ||\n      element.tagName === 'iframe' ||\n      element.tagName === 'img' ||\n      element.tagName === 'math' ||\n      element.tagName === 'object' ||\n      element.tagName === 'picture' ||\n      element.tagName === 'svg' ||\n      element.tagName === 'video'\n    )\n  }\n)\n"], "names": [], "mappings": ";;;AAAA;;AAaO,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EACnC;;;GAGC,GACD,SAAU,OAAO;IACf,OACE,QAAQ,OAAO,KAAK,WACpB,QAAQ,OAAO,KAAK,YACpB,QAAQ,OAAO,KAAK,WACpB,QAAQ,OAAO,KAAK,YACpB,QAAQ,OAAO,KAAK,SACpB,QAAQ,OAAO,KAAK,UACpB,QAAQ,OAAO,KAAK,YACpB,QAAQ,OAAO,KAAK,aACpB,QAAQ,OAAO,KAAK,SACpB,QAAQ,OAAO,KAAK;AAExB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-whitespace/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Nodes} Nodes\n */\n\n// HTML whitespace expression.\n// See <https://infra.spec.whatwg.org/#ascii-whitespace>.\nconst re = /[ \\t\\n\\f\\r]/g\n\n/**\n * Check if the given value is *inter-element whitespace*.\n *\n * @param {Nodes | string} thing\n *   Thing to check (`Node` or `string`).\n * @returns {boolean}\n *   Whether the `value` is inter-element whitespace (`boolean`): consisting of\n *   zero or more of space, tab (`\\t`), line feed (`\\n`), carriage return\n *   (`\\r`), or form feed (`\\f`); if a node is passed it must be a `Text` node,\n *   whose `value` field is checked.\n */\nexport function whitespace(thing) {\n  return typeof thing === 'object'\n    ? thing.type === 'text'\n      ? empty(thing.value)\n      : false\n    : empty(thing)\n}\n\n/**\n * @param {string} value\n * @returns {boolean}\n */\nfunction empty(value) {\n  return value.replace(re, '') === ''\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,8BAA8B;AAC9B,yDAAyD;;;;AACzD,MAAM,KAAK;AAaJ,SAAS,WAAW,KAAK;IAC9B,OAAO,OAAO,UAAU,WACpB,MAAM,IAAI,KAAK,SACb,MAAM,MAAM,KAAK,IACjB,QACF,MAAM;AACZ;AAEA;;;CAGC,GACD,SAAS,MAAM,KAAK;IAClB,OAAO,MAAM,OAAO,CAAC,IAAI,QAAQ;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/unist-util-is/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n */\n\n/**\n * @template Fn\n * @template Fallback\n * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate\n */\n\n/**\n * @callback Check\n *   Check that an arbitrary value is a node.\n * @param {unknown} this\n *   The given context.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is a node and passes a test.\n *\n * @typedef {Record<string, unknown> | Node} Props\n *   Object to check for equivalence.\n *\n *   Note: `Node` is included as it is common but is not indexable.\n *\n * @typedef {Array<Props | TestFunction | string> | Props | TestFunction | string | null | undefined} Test\n *   Check for an arbitrary node.\n *\n * @callback TestFunction\n *   Check if a node passes a test.\n * @param {unknown} this\n *   The given context.\n * @param {Node} node\n *   A node.\n * @param {number | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean | undefined | void}\n *   Whether this node passes the test.\n *\n *   Note: `void` is included until TS sees no return as `undefined`.\n */\n\n/**\n * Check if `node` is a `Node` and whether it passes the given test.\n *\n * @param {unknown} node\n *   Thing to check, typically `Node`.\n * @param {Test} test\n *   A check for a specific node.\n * @param {number | null | undefined} index\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} parent\n *   The node’s parent.\n * @param {unknown} context\n *   Context object (`this`) to pass to `test` functions.\n * @returns {boolean}\n *   Whether `node` is a node and passes a test.\n */\nexport const is =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends string>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n   *   (<Condition extends Props>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n   *   (<Condition extends TestFunction>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n   *   ((node?: null | undefined) => false) &\n   *   ((node: unknown, test?: null | undefined, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n   *   ((node: unknown, test?: Test, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function (node, test, index, parent, context) {\n      const check = convert(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!is(parent) || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      return looksLikeANode(node)\n        ? check.call(context, node, index, parent)\n        : false\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param {Test} test\n *   *   when nullish, checks if `node` is a `Node`.\n *   *   when `string`, works like passing `(node) => node.type === test`.\n *   *   when `function` checks if function passed the node is true.\n *   *   when `object`, checks that all keys in test are in node, and that they have (strictly) equal values.\n *   *   when `array`, checks if any one of the subtests pass.\n * @returns {Check}\n *   An assertion.\n */\nexport const convert =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends string>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n   *   (<Condition extends Props>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n   *   (<Condition extends TestFunction>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n   *   ((test?: null | undefined) => (node?: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n   *   ((test?: Test) => Check)\n   * )}\n   */\n  (\n    /**\n     * @param {Test} [test]\n     * @returns {Check}\n     */\n    function (test) {\n      if (test === null || test === undefined) {\n        return ok\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return Array.isArray(test) ? anyFactory(test) : propsFactory(test)\n      }\n\n      if (typeof test === 'string') {\n        return typeFactory(test)\n      }\n\n      throw new Error('Expected function, string, or object as test')\n    }\n  )\n\n/**\n * @param {Array<Props | TestFunction | string>} tests\n * @returns {Check}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<Check>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convert(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @type {TestFunction}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].apply(this, parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn an object into a test for a node with a certain fields.\n *\n * @param {Props} check\n * @returns {Check}\n */\nfunction propsFactory(check) {\n  const checkAsRecord = /** @type {Record<string, unknown>} */ (check)\n\n  return castFactory(all)\n\n  /**\n   * @param {Node} node\n   * @returns {boolean}\n   */\n  function all(node) {\n    const nodeAsRecord = /** @type {Record<string, unknown>} */ (\n      /** @type {unknown} */ (node)\n    )\n\n    /** @type {string} */\n    let key\n\n    for (key in check) {\n      if (nodeAsRecord[key] !== checkAsRecord[key]) return false\n    }\n\n    return true\n  }\n}\n\n/**\n * Turn a string into a test for a node with a certain type.\n *\n * @param {string} check\n * @returns {Check}\n */\nfunction typeFactory(check) {\n  return castFactory(type)\n\n  /**\n   * @param {Node} node\n   */\n  function type(node) {\n    return node && node.type === check\n  }\n}\n\n/**\n * Turn a custom test into a test for a node that passes that test.\n *\n * @param {TestFunction} testFunction\n * @returns {Check}\n */\nfunction castFactory(testFunction) {\n  return check\n\n  /**\n   * @this {unknown}\n   * @type {Check}\n   */\n  function check(value, index, parent) {\n    return Boolean(\n      looksLikeANode(value) &&\n        testFunction.call(\n          this,\n          value,\n          typeof index === 'number' ? index : undefined,\n          parent || undefined\n        )\n    )\n  }\n}\n\nfunction ok() {\n  return true\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Node}\n */\nfunction looksLikeANode(value) {\n  return value !== null && typeof value === 'object' && 'type' in value\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;CAIC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoCC,GAED;;;;;;;;;;;;;;;CAeC;;;;AACM,MAAM,KAaT;;;;;;;KAOC,GACD,sCAAsC;AACtC,SAAU,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO;IAC1C,MAAM,QAAQ,QAAQ;IAEtB,IACE,UAAU,aACV,UAAU,QACV,CAAC,OAAO,UAAU,YAChB,QAAQ,KACR,UAAU,OAAO,iBAAiB,GACpC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,IACE,WAAW,aACX,WAAW,QACX,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,QAAQ,GAChC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,IACE,CAAC,WAAW,aAAa,WAAW,IAAI,MACxC,CAAC,UAAU,aAAa,UAAU,IAAI,GACtC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,eAAe,QAClB,MAAM,IAAI,CAAC,SAAS,MAAM,OAAO,UACjC;AACN;AAqBG,MAAM,UAYT;;;KAGC,GACD,SAAU,IAAI;IACZ,IAAI,SAAS,QAAQ,SAAS,WAAW;QACvC,OAAO;IACT;IAEA,IAAI,OAAO,SAAS,YAAY;QAC9B,OAAO,YAAY;IACrB;IAEA,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,MAAM,OAAO,CAAC,QAAQ,WAAW,QAAQ,aAAa;IAC/D;IAEA,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,YAAY;IACrB;IAEA,MAAM,IAAI,MAAM;AAClB;AAGJ;;;CAGC,GACD,SAAS,WAAW,KAAK;IACvB,yBAAyB,GACzB,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,MAAM,CAAC,MAAM,GAAG,QAAQ,KAAK,CAAC,MAAM;IACtC;IAEA,OAAO,YAAY;;IAEnB;;;GAGC,GACD,SAAS,IAAI,GAAG,UAAU;QACxB,IAAI,QAAQ,CAAC;QAEb,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;YAC9B,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,aAAa,OAAO;QACpD;QAEA,OAAO;IACT;AACF;AAEA;;;;;CAKC,GACD,SAAS,aAAa,KAAK;IACzB,MAAM,gBAAwD;IAE9D,OAAO,YAAY;;IAEnB;;;GAGC,GACD,SAAS,IAAI,IAAI;QACf,MAAM,eACoB;QAG1B,mBAAmB,GACnB,IAAI;QAEJ,IAAK,OAAO,MAAO;YACjB,IAAI,YAAY,CAAC,IAAI,KAAK,aAAa,CAAC,IAAI,EAAE,OAAO;QACvD;QAEA,OAAO;IACT;AACF;AAEA;;;;;CAKC,GACD,SAAS,YAAY,KAAK;IACxB,OAAO,YAAY;;IAEnB;;GAEC,GACD,SAAS,KAAK,IAAI;QAChB,OAAO,QAAQ,KAAK,IAAI,KAAK;IAC/B;AACF;AAEA;;;;;CAKC,GACD,SAAS,YAAY,YAAY;IAC/B,OAAO;;IAEP;;;GAGC,GACD,SAAS,MAAM,KAAK,EAAE,KAAK,EAAE,MAAM;QACjC,OAAO,QACL,eAAe,UACb,aAAa,IAAI,CACf,IAAI,EACJ,OACA,OAAO,UAAU,WAAW,QAAQ,WACpC,UAAU;IAGlB;AACF;AAEA,SAAS;IACP,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,eAAe,KAAK;IAC3B,OAAO,UAAU,QAAQ,OAAO,UAAU,YAAY,UAAU;AAClE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-minify-whitespace/lib/block.js"], "sourcesContent": ["// See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\nexport const blocks = [\n  'address', // Flow content.\n  'article', // Sections and headings.\n  'aside', // Sections and headings.\n  'blockquote', // Flow content.\n  'body', // Page.\n  'br', // Contribute whitespace intrinsically.\n  'caption', // Similar to block.\n  'center', // Flow content, legacy.\n  'col', // Similar to block.\n  'colgroup', // Similar to block.\n  'dd', // Lists.\n  'dialog', // Flow content.\n  'dir', // Lists, legacy.\n  'div', // Flow content.\n  'dl', // Lists.\n  'dt', // Lists.\n  'figcaption', // Flow content.\n  'figure', // Flow content.\n  'footer', // Flow content.\n  'form', // Flow content.\n  'h1', // Sections and headings.\n  'h2', // Sections and headings.\n  'h3', // Sections and headings.\n  'h4', // Sections and headings.\n  'h5', // Sections and headings.\n  'h6', // Sections and headings.\n  'head', // Page.\n  'header', // Flow content.\n  'hgroup', // Sections and headings.\n  'hr', // Flow content.\n  'html', // Page.\n  'legend', // Flow content.\n  'li', // Block-like.\n  'li', // Similar to block.\n  'listing', // Flow content, legacy\n  'main', // Flow content.\n  'menu', // Lists.\n  'nav', // Sections and headings.\n  'ol', // Lists.\n  'optgroup', // Similar to block.\n  'option', // Similar to block.\n  'p', // Flow content.\n  'plaintext', // Flow content, legacy\n  'pre', // Flow content.\n  'section', // Sections and headings.\n  'summary', // Similar to block.\n  'table', // Similar to block.\n  'tbody', // Similar to block.\n  'td', // Block-like.\n  'td', // Similar to block.\n  'tfoot', // Similar to block.\n  'th', // Block-like.\n  'th', // Similar to block.\n  'thead', // Similar to block.\n  'tr', // Similar to block.\n  'ul', // Lists.\n  'wbr', // Contribute whitespace intrinsically.\n  'xmp' // Flow content, legacy\n]\n"], "names": [], "mappings": "AAAA,+FAA+F;;;;AACxF,MAAM,SAAS;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM,uBAAuB;CAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-minify-whitespace/lib/content.js"], "sourcesContent": ["export const content = [\n  // Form.\n  'button',\n  'input',\n  'select',\n  'textarea'\n]\n"], "names": [], "mappings": ";;;AAAO,MAAM,UAAU;IACrB,QAAQ;IACR;IACA;IACA;IACA;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 517, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-minify-whitespace/lib/skippable.js"], "sourcesContent": ["export const skippable = [\n  'area',\n  'base',\n  'basefont',\n  'dialog',\n  'datalist',\n  'head',\n  'link',\n  'meta',\n  'noembed',\n  'noframes',\n  'param',\n  'rp',\n  'script',\n  'source',\n  'style',\n  'template',\n  'track',\n  'title'\n]\n"], "names": [], "mappings": ";;;AAAO,MAAM,YAAY;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-minify-whitespace/lib/index.js"], "sourcesContent": ["/**\n * @import {Nodes, Parents, Text} from 'hast'\n */\n\n/**\n * @callback Collapse\n *   Collapse a string.\n * @param {string} value\n *   Value to collapse.\n * @returns {string}\n *   Collapsed value.\n *\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [newlines=false]\n *   Collapse whitespace containing newlines to `'\\n'` instead of `' '`\n *   (default: `false`); the default is to collapse to a single space.\n *\n * @typedef Result\n *   Result.\n * @property {boolean} remove\n *   Whether to remove.\n * @property {boolean} ignore\n *   Whether to ignore.\n * @property {boolean} stripAtStart\n *   Whether to strip at the start.\n *\n * @typedef State\n *   Info passed around.\n * @property {Collapse} collapse\n *   Collapse.\n * @property {Whitespace} whitespace\n *   Current whitespace.\n * @property {boolean | undefined} [before]\n *   Whether there is a break before (default: `false`).\n * @property {boolean | undefined} [after]\n *   Whether there is a break after (default: `false`).\n *\n * @typedef {'normal' | 'nowrap' | 'pre' | 'pre-wrap'} Whitespace\n *   Whitespace setting.\n */\n\nimport {embedded} from 'hast-util-embedded'\nimport {isElement} from 'hast-util-is-element'\nimport {whitespace} from 'hast-util-whitespace'\nimport {convert} from 'unist-util-is'\nimport {blocks} from './block.js'\nimport {content as contents} from './content.js'\nimport {skippable as skippables} from './skippable.js'\n\n/** @type {Options} */\nconst emptyOptions = {}\nconst ignorableNode = convert(['comment', 'doctype'])\n\n/**\n * Minify whitespace.\n *\n * @param {Nodes} tree\n *   Tree.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nexport function minifyWhitespace(tree, options) {\n  const settings = options || emptyOptions\n\n  minify(tree, {\n    collapse: collapseFactory(\n      settings.newlines ? replaceNewlines : replaceWhitespace\n    ),\n    whitespace: 'normal'\n  })\n}\n\n/**\n * @param {Nodes} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction minify(node, state) {\n  if ('children' in node) {\n    const settings = {...state}\n\n    if (node.type === 'root' || blocklike(node)) {\n      settings.before = true\n      settings.after = true\n    }\n\n    settings.whitespace = inferWhiteSpace(node, state)\n\n    return all(node, settings)\n  }\n\n  if (node.type === 'text') {\n    if (state.whitespace === 'normal') {\n      return minifyText(node, state)\n    }\n\n    // Naïve collapse, but no trimming:\n    if (state.whitespace === 'nowrap') {\n      node.value = state.collapse(node.value)\n    }\n\n    // The `pre-wrap` or `pre` whitespace settings are neither collapsed nor\n    // trimmed.\n  }\n\n  return {ignore: ignorableNode(node), stripAtStart: false, remove: false}\n}\n\n/**\n * @param {Text} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction minifyText(node, state) {\n  const value = state.collapse(node.value)\n  const result = {ignore: false, stripAtStart: false, remove: false}\n  let start = 0\n  let end = value.length\n\n  if (state.before && removable(value.charAt(0))) {\n    start++\n  }\n\n  if (start !== end && removable(value.charAt(end - 1))) {\n    if (state.after) {\n      end--\n    } else {\n      result.stripAtStart = true\n    }\n  }\n\n  if (start === end) {\n    result.remove = true\n  } else {\n    node.value = value.slice(start, end)\n  }\n\n  return result\n}\n\n/**\n * @param {Parents} parent\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction all(parent, state) {\n  let before = state.before\n  const after = state.after\n  const children = parent.children\n  let length = children.length\n  let index = -1\n\n  while (++index < length) {\n    const result = minify(children[index], {\n      ...state,\n      after: collapsableAfter(children, index, after),\n      before\n    })\n\n    if (result.remove) {\n      children.splice(index, 1)\n      index--\n      length--\n    } else if (!result.ignore) {\n      before = result.stripAtStart\n    }\n\n    // If this element, such as a `<select>` or `<img>`, contributes content\n    // somehow, allow whitespace again.\n    if (content(children[index])) {\n      before = false\n    }\n  }\n\n  return {ignore: false, stripAtStart: Boolean(before || after), remove: false}\n}\n\n/**\n * @param {Array<Nodes>} nodes\n *   Nodes.\n * @param {number} index\n *   Index.\n * @param {boolean | undefined} [after]\n *   Whether there is a break after `nodes` (default: `false`).\n * @returns {boolean | undefined}\n *   Whether there is a break after the node at `index`.\n */\nfunction collapsableAfter(nodes, index, after) {\n  while (++index < nodes.length) {\n    const node = nodes[index]\n    let result = inferBoundary(node)\n\n    if (result === undefined && 'children' in node && !skippable(node)) {\n      result = collapsableAfter(node.children, -1)\n    }\n\n    if (typeof result === 'boolean') {\n      return result\n    }\n  }\n\n  return after\n}\n\n/**\n * Infer two types of boundaries:\n *\n * 1. `true` — boundary for which whitespace around it does not contribute\n *    anything\n * 2. `false` — boundary for which whitespace around it *does* contribute\n *\n * No result (`undefined`) is returned if it is unknown.\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean | undefined}\n *   Boundary.\n */\nfunction inferBoundary(node) {\n  if (node.type === 'element') {\n    if (content(node)) {\n      return false\n    }\n\n    if (blocklike(node)) {\n      return true\n    }\n\n    // Unknown: either depends on siblings if embedded or metadata, or on\n    // children.\n  } else if (node.type === 'text') {\n    if (!whitespace(node)) {\n      return false\n    }\n  } else if (!ignorableNode(node)) {\n    return false\n  }\n}\n\n/**\n * Infer whether a node is skippable.\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is skippable.\n */\nfunction content(node) {\n  return embedded(node) || isElement(node, contents)\n}\n\n/**\n * See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is block-like.\n */\nfunction blocklike(node) {\n  return isElement(node, blocks)\n}\n\n/**\n * @param {Parents} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is skippable.\n */\nfunction skippable(node) {\n  return (\n    Boolean(node.type === 'element' && node.properties.hidden) ||\n    ignorableNode(node) ||\n    isElement(node, skippables)\n  )\n}\n\n/**\n * @param {string} character\n *   Character.\n * @returns {boolean}\n *   Whether `character` is removable.\n */\nfunction removable(character) {\n  return character === ' ' || character === '\\n'\n}\n\n/**\n * @type {Collapse}\n */\nfunction replaceNewlines(value) {\n  const match = /\\r?\\n|\\r/.exec(value)\n  return match ? match[0] : ' '\n}\n\n/**\n * @type {Collapse}\n */\nfunction replaceWhitespace() {\n  return ' '\n}\n\n/**\n * @param {Collapse} replace\n * @returns {Collapse}\n *   Collapse.\n */\nfunction collapseFactory(replace) {\n  return collapse\n\n  /**\n   * @type {Collapse}\n   */\n  function collapse(value) {\n    return String(value).replace(/[\\t\\n\\v\\f\\r ]+/g, replace)\n  }\n}\n\n/**\n * We don’t need to support void elements here (so `nobr wbr` -> `normal` is\n * ignored).\n *\n * @param {Parents} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Whitespace}\n *   Whitespace.\n */\nfunction inferWhiteSpace(node, state) {\n  if ('tagName' in node && node.properties) {\n    switch (node.tagName) {\n      // Whitespace in script/style, while not displayed by CSS as significant,\n      // could have some meaning in JS/CSS, so we can’t touch them.\n      case 'listing':\n      case 'plaintext':\n      case 'script':\n      case 'style':\n      case 'xmp': {\n        return 'pre'\n      }\n\n      case 'nobr': {\n        return 'nowrap'\n      }\n\n      case 'pre': {\n        return node.properties.wrap ? 'pre-wrap' : 'pre'\n      }\n\n      case 'td':\n      case 'th': {\n        return node.properties.noWrap ? 'nowrap' : state.whitespace\n      }\n\n      case 'textarea': {\n        return 'pre-wrap'\n      }\n\n      default:\n    }\n  }\n\n  return state.whitespace\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoCC;;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,oBAAoB,GACpB,MAAM,eAAe,CAAC;AACtB,MAAM,gBAAgB,CAAA,GAAA,mJAAA,CAAA,UAAO,AAAD,EAAE;IAAC;IAAW;CAAU;AAY7C,SAAS,iBAAiB,IAAI,EAAE,OAAO;IAC5C,MAAM,WAAW,WAAW;IAE5B,OAAO,MAAM;QACX,UAAU,gBACR,SAAS,QAAQ,GAAG,kBAAkB;QAExC,YAAY;IACd;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,OAAO,IAAI,EAAE,KAAK;IACzB,IAAI,cAAc,MAAM;QACtB,MAAM,WAAW;YAAC,GAAG,KAAK;QAAA;QAE1B,IAAI,KAAK,IAAI,KAAK,UAAU,UAAU,OAAO;YAC3C,SAAS,MAAM,GAAG;YAClB,SAAS,KAAK,GAAG;QACnB;QAEA,SAAS,UAAU,GAAG,gBAAgB,MAAM;QAE5C,OAAO,IAAI,MAAM;IACnB;IAEA,IAAI,KAAK,IAAI,KAAK,QAAQ;QACxB,IAAI,MAAM,UAAU,KAAK,UAAU;YACjC,OAAO,WAAW,MAAM;QAC1B;QAEA,mCAAmC;QACnC,IAAI,MAAM,UAAU,KAAK,UAAU;YACjC,KAAK,KAAK,GAAG,MAAM,QAAQ,CAAC,KAAK,KAAK;QACxC;IAEA,wEAAwE;IACxE,WAAW;IACb;IAEA,OAAO;QAAC,QAAQ,cAAc;QAAO,cAAc;QAAO,QAAQ;IAAK;AACzE;AAEA;;;;;;;CAOC,GACD,SAAS,WAAW,IAAI,EAAE,KAAK;IAC7B,MAAM,QAAQ,MAAM,QAAQ,CAAC,KAAK,KAAK;IACvC,MAAM,SAAS;QAAC,QAAQ;QAAO,cAAc;QAAO,QAAQ;IAAK;IACjE,IAAI,QAAQ;IACZ,IAAI,MAAM,MAAM,MAAM;IAEtB,IAAI,MAAM,MAAM,IAAI,UAAU,MAAM,MAAM,CAAC,KAAK;QAC9C;IACF;IAEA,IAAI,UAAU,OAAO,UAAU,MAAM,MAAM,CAAC,MAAM,KAAK;QACrD,IAAI,MAAM,KAAK,EAAE;YACf;QACF,OAAO;YACL,OAAO,YAAY,GAAG;QACxB;IACF;IAEA,IAAI,UAAU,KAAK;QACjB,OAAO,MAAM,GAAG;IAClB,OAAO;QACL,KAAK,KAAK,GAAG,MAAM,KAAK,CAAC,OAAO;IAClC;IAEA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,IAAI,MAAM,EAAE,KAAK;IACxB,IAAI,SAAS,MAAM,MAAM;IACzB,MAAM,QAAQ,MAAM,KAAK;IACzB,MAAM,WAAW,OAAO,QAAQ;IAChC,IAAI,SAAS,SAAS,MAAM;IAC5B,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,OAAQ;QACvB,MAAM,SAAS,OAAO,QAAQ,CAAC,MAAM,EAAE;YACrC,GAAG,KAAK;YACR,OAAO,iBAAiB,UAAU,OAAO;YACzC;QACF;QAEA,IAAI,OAAO,MAAM,EAAE;YACjB,SAAS,MAAM,CAAC,OAAO;YACvB;YACA;QACF,OAAO,IAAI,CAAC,OAAO,MAAM,EAAE;YACzB,SAAS,OAAO,YAAY;QAC9B;QAEA,wEAAwE;QACxE,mCAAmC;QACnC,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG;YAC5B,SAAS;QACX;IACF;IAEA,OAAO;QAAC,QAAQ;QAAO,cAAc,QAAQ,UAAU;QAAQ,QAAQ;IAAK;AAC9E;AAEA;;;;;;;;;CASC,GACD,SAAS,iBAAiB,KAAK,EAAE,KAAK,EAAE,KAAK;IAC3C,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,MAAM,OAAO,KAAK,CAAC,MAAM;QACzB,IAAI,SAAS,cAAc;QAE3B,IAAI,WAAW,aAAa,cAAc,QAAQ,CAAC,UAAU,OAAO;YAClE,SAAS,iBAAiB,KAAK,QAAQ,EAAE,CAAC;QAC5C;QAEA,IAAI,OAAO,WAAW,WAAW;YAC/B,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA;;;;;;;;;;;;;CAaC,GACD,SAAS,cAAc,IAAI;IACzB,IAAI,KAAK,IAAI,KAAK,WAAW;QAC3B,IAAI,QAAQ,OAAO;YACjB,OAAO;QACT;QAEA,IAAI,UAAU,OAAO;YACnB,OAAO;QACT;IAEA,qEAAqE;IACrE,YAAY;IACd,OAAO,IAAI,KAAK,IAAI,KAAK,QAAQ;QAC/B,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE,OAAO;YACrB,OAAO;QACT;IACF,OAAO,IAAI,CAAC,cAAc,OAAO;QAC/B,OAAO;IACT;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,QAAQ,IAAI;IACnB,OAAO,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,MAAM,sKAAA,CAAA,UAAQ;AACnD;AAEA;;;;;;;CAOC,GACD,SAAS,UAAU,IAAI;IACrB,OAAO,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,MAAM,oKAAA,CAAA,SAAM;AAC/B;AAEA;;;;;CAKC,GACD,SAAS,UAAU,IAAI;IACrB,OACE,QAAQ,KAAK,IAAI,KAAK,aAAa,KAAK,UAAU,CAAC,MAAM,KACzD,cAAc,SACd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,MAAM,wKAAA,CAAA,YAAU;AAE9B;AAEA;;;;;CAKC,GACD,SAAS,UAAU,SAAS;IAC1B,OAAO,cAAc,OAAO,cAAc;AAC5C;AAEA;;CAEC,GACD,SAAS,gBAAgB,KAAK;IAC5B,MAAM,QAAQ,WAAW,IAAI,CAAC;IAC9B,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;AAC5B;AAEA;;CAEC,GACD,SAAS;IACP,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,gBAAgB,OAAO;IAC9B,OAAO;;IAEP;;GAEC,GACD,SAAS,SAAS,KAAK;QACrB,OAAO,OAAO,OAAO,OAAO,CAAC,mBAAmB;IAClD;AACF;AAEA;;;;;;;;;;CAUC,GACD,SAAS,gBAAgB,IAAI,EAAE,KAAK;IAClC,IAAI,aAAa,QAAQ,KAAK,UAAU,EAAE;QACxC,OAAQ,KAAK,OAAO;YAClB,yEAAyE;YACzE,6DAA6D;YAC7D,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAO;oBACV,OAAO;gBACT;YAEA,KAAK;gBAAQ;oBACX,OAAO;gBACT;YAEA,KAAK;gBAAO;oBACV,OAAO,KAAK,UAAU,CAAC,IAAI,GAAG,aAAa;gBAC7C;YAEA,KAAK;YACL,KAAK;gBAAM;oBACT,OAAO,KAAK,UAAU,CAAC,MAAM,GAAG,WAAW,MAAM,UAAU;gBAC7D;YAEA,KAAK;gBAAY;oBACf,OAAO;gBACT;YAEA;QACF;IACF;IAEA,OAAO,MAAM,UAAU;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 884, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-has-property/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Nodes} Nodes\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Check if `node` is an element and has a `name` property.\n *\n * @template {string} Key\n *   Type of key.\n * @param {Nodes} node\n *   Node to check (typically `Element`).\n * @param {Key} name\n *   Property name to check.\n * @returns {node is Element & {properties: Record<Key, Array<number | string> | number | string | true>}}}\n *   Whether `node` is an element that has a `name` property.\n *\n *   Note: see <https://github.com/DefinitelyTyped/DefinitelyTyped/blob/27c9274/types/hast/index.d.ts#L37C29-L37C98>.\n */\nexport function hasProperty(node, name) {\n  const value =\n    node.type === 'element' &&\n    own.call(node.properties, name) &&\n    node.properties[name]\n\n  return value !== null && value !== undefined && value !== false\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED,MAAM,MAAM,CAAC,EAAE,cAAc;AAgBtB,SAAS,YAAY,IAAI,EAAE,IAAI;IACpC,MAAM,QACJ,KAAK,IAAI,KAAK,aACd,IAAI,IAAI,CAAC,KAAK,UAAU,EAAE,SAC1B,KAAK,UAAU,CAAC,KAAK;IAEvB,OAAO,UAAU,QAAQ,UAAU,aAAa,UAAU;AAC5D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 901, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-is-body-ok-link/lib/index.js"], "sourcesContent": ["/**\n * @import {Nodes} from 'hast'\n */\n\nconst list = new Set(['pingback', 'prefetch', 'stylesheet'])\n\n/**\n * Checks whether a node is a “body OK” link.\n *\n * @param {Nodes} node\n *   Node to check.\n * @returns {boolean}\n *   Whether `node` is a “body OK” link.\n */\nexport function isBodyOkLink(node) {\n  if (node.type !== 'element' || node.tagName !== 'link') {\n    return false\n  }\n\n  if (node.properties.itemProp) {\n    return true\n  }\n\n  const value = node.properties.rel\n  let index = -1\n\n  if (!Array.isArray(value) || value.length === 0) {\n    return false\n  }\n\n  while (++index < value.length) {\n    if (!list.has(String(value[index]))) {\n      return false\n    }\n  }\n\n  return true\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED,MAAM,OAAO,IAAI,IAAI;IAAC;IAAY;IAAY;CAAa;AAUpD,SAAS,aAAa,IAAI;IAC/B,IAAI,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK,QAAQ;QACtD,OAAO;IACT;IAEA,IAAI,KAAK,UAAU,CAAC,QAAQ,EAAE;QAC5B,OAAO;IACT;IAEA,MAAM,QAAQ,KAAK,UAAU,CAAC,GAAG;IACjC,IAAI,QAAQ,CAAC;IAEb,IAAI,CAAC,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK,GAAG;QAC/C,OAAO;IACT;IAEA,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO,KAAK,CAAC,MAAM,IAAI;YACnC,OAAO;QACT;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 936, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-phrasing/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Nodes} Nodes\n */\n\nimport {embedded} from 'hast-util-embedded'\nimport {hasProperty} from 'hast-util-has-property'\nimport {isBodyOkLink} from 'hast-util-is-body-ok-link'\nimport {convertElement} from 'hast-util-is-element'\n\nconst basic = convertElement([\n  'a',\n  'abbr',\n  // `area` is in fact only phrasing if it is inside a `map` element.\n  // However, since `area`s are required to be inside a `map` element, and it’s\n  // a rather involved check, it’s ignored here for now.\n  'area',\n  'b',\n  'bdi',\n  'bdo',\n  'br',\n  'button',\n  'cite',\n  'code',\n  'data',\n  'datalist',\n  'del',\n  'dfn',\n  'em',\n  'i',\n  'input',\n  'ins',\n  'kbd',\n  'keygen',\n  'label',\n  'map',\n  'mark',\n  'meter',\n  'noscript',\n  'output',\n  'progress',\n  'q',\n  'ruby',\n  's',\n  'samp',\n  'script',\n  'select',\n  'small',\n  'span',\n  'strong',\n  'sub',\n  'sup',\n  'template',\n  'textarea',\n  'time',\n  'u',\n  'var',\n  'wbr'\n])\n\nconst meta = convertElement('meta')\n\n/**\n * Check if the given value is *phrasing* content.\n *\n * @param {Nodes} value\n *   Node to check.\n * @returns {boolean}\n *   Whether `value` is phrasing content.\n */\nexport function phrasing(value) {\n  return Boolean(\n    value.type === 'text' ||\n      basic(value) ||\n      embedded(value) ||\n      isBodyOkLink(value) ||\n      (meta(value) && hasProperty(value, 'itemProp'))\n  )\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;AACA;AACA;AACA;;;;;AAEA,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EAAE;IAC3B;IACA;IACA,mEAAmE;IACnE,6EAA6E;IAC7E,sDAAsD;IACtD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EAAE;AAUrB,SAAS,SAAS,KAAK;IAC5B,OAAO,QACL,MAAM,IAAI,KAAK,UACb,MAAM,UACN,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,UACT,CAAA,GAAA,wKAAA,CAAA,eAAY,AAAD,EAAE,UACZ,KAAK,UAAU,CAAA,GAAA,+JAAA,CAAA,cAAW,AAAD,EAAE,OAAO;AAEzC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/html-whitespace-sensitive-tag-names/lib/index.js"], "sourcesContent": ["/**\n * List of HTML tag names that are whitespace sensitive.\n */\nexport const whitespaceSensitiveTagNames = [\n  'pre',\n  'script',\n  'style',\n  'textarea'\n]\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACM,MAAM,8BAA8B;IACzC;IACA;IACA;IACA;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1025, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/unist-util-visit-parents/lib/color.node.js"], "sourcesContent": ["/**\n * @param {string} d\n * @returns {string}\n */\nexport function color(d) {\n  return '\\u001B[33m' + d + '\\u001B[39m'\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AACM,SAAS,MAAM,CAAC;IACrB,OAAO,eAAe,IAAI;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1040, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/unist-util-visit-parents/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} UnistNode\n * @typedef {import('unist').Parent} UnistParent\n */\n\n/**\n * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test\n *   Test from `unist-util-is`.\n *\n *   Note: we have remove and add `undefined`, because otherwise when generating\n *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,\n *   which doesn’t work when publishing on npm.\n */\n\n/**\n * @typedef {(\n *   Fn extends (value: any) => value is infer Thing\n *   ? Thing\n *   : Fallback\n * )} Predicate\n *   Get the value of a type guard `Fn`.\n * @template Fn\n *   Value; typically function that is a type guard (such as `(x): x is Y`).\n * @template Fallback\n *   Value to yield if `Fn` is not a type guard.\n */\n\n/**\n * @typedef {(\n *   Check extends null | undefined // No test.\n *   ? Value\n *   : Value extends {type: Check} // String (type) test.\n *   ? Value\n *   : Value extends Check // Partial test.\n *   ? Value\n *   : Check extends Function // Function test.\n *   ? Predicate<Check, Value> extends Value\n *     ? Predicate<Check, Value>\n *     : never\n *   : never // Some other test?\n * )} MatchesOne\n *   Check whether a node matches a primitive check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test, but not arrays.\n */\n\n/**\n * @typedef {(\n *   Check extends Array<any>\n *   ? MatchesOne<Value, Check[keyof Check]>\n *   : MatchesOne<Value, Check>\n * )} Matches\n *   Check whether a node matches a check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test.\n */\n\n/**\n * @typedef {0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10} Uint\n *   Number; capped reasonably.\n */\n\n/**\n * @typedef {I extends 0 ? 1 : I extends 1 ? 2 : I extends 2 ? 3 : I extends 3 ? 4 : I extends 4 ? 5 : I extends 5 ? 6 : I extends 6 ? 7 : I extends 7 ? 8 : I extends 8 ? 9 : 10} Increment\n *   Increment a number in the type system.\n * @template {Uint} [I=0]\n *   Index.\n */\n\n/**\n * @typedef {(\n *   Node extends UnistParent\n *   ? Node extends {children: Array<infer Children>}\n *     ? Child extends Children ? Node : never\n *     : never\n *   : never\n * )} InternalParent\n *   Collect nodes that can be parents of `Child`.\n * @template {UnistNode} Node\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {InternalParent<InclusiveDescendant<Tree>, Child>} Parent\n *   Collect nodes in `Tree` that can be parents of `Child`.\n * @template {UnistNode} Tree\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {(\n *   Depth extends Max\n *   ? never\n *   :\n *     | InternalParent<Node, Child>\n *     | InternalAncestor<Node, InternalParent<Node, Child>, Max, Increment<Depth>>\n * )} InternalAncestor\n *   Collect nodes in `Tree` that can be ancestors of `Child`.\n * @template {UnistNode} Node\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n * @template {Uint} [Max=10]\n *   Max; searches up to this depth.\n * @template {Uint} [Depth=0]\n *   Current depth.\n */\n\n/**\n * @typedef {InternalAncestor<InclusiveDescendant<Tree>, Child>} Ancestor\n *   Collect nodes in `Tree` that can be ancestors of `Child`.\n * @template {UnistNode} Tree\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {(\n *   Tree extends UnistParent\n *     ? Depth extends Max\n *       ? Tree\n *       : Tree | InclusiveDescendant<Tree['children'][number], Max, Increment<Depth>>\n *     : Tree\n * )} InclusiveDescendant\n *   Collect all (inclusive) descendants of `Tree`.\n *\n *   > 👉 **Note**: for performance reasons, this seems to be the fastest way to\n *   > recurse without actually running into an infinite loop, which the\n *   > previous version did.\n *   >\n *   > Practically, a max of `2` is typically enough assuming a `Root` is\n *   > passed, but it doesn’t improve performance.\n *   > It gets higher with `List > ListItem > Table > TableRow > TableCell`.\n *   > Using up to `10` doesn’t hurt or help either.\n * @template {UnistNode} Tree\n *   Tree type.\n * @template {Uint} [Max=10]\n *   Max; searches up to this depth.\n * @template {Uint} [Depth=0]\n *   Current depth.\n */\n\n/**\n * @typedef {'skip' | boolean} Action\n *   Union of the action types.\n *\n * @typedef {number} Index\n *   Move to the sibling at `index` next (after node itself is completely\n *   traversed).\n *\n *   Useful if mutating the tree, such as removing the node the visitor is\n *   currently on, or any of its previous siblings.\n *   Results less than 0 or greater than or equal to `children.length` stop\n *   traversing the parent.\n *\n * @typedef {[(Action | null | undefined | void)?, (Index | null | undefined)?]} ActionTuple\n *   List with one or two values, the first an action, the second an index.\n *\n * @typedef {Action | ActionTuple | Index | null | undefined | void} VisitorResult\n *   Any value that can be returned from a visitor.\n */\n\n/**\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform the parent of node (the last of `ancestors`).\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of an ancestor still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Array<VisitedParents>} ancestors\n *   Ancestors of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n * @template {UnistNode} [Visited=UnistNode]\n *   Visited node type.\n * @template {UnistParent} [VisitedParents=UnistParent]\n *   Ancestor type.\n */\n\n/**\n * @typedef {Visitor<Matches<InclusiveDescendant<Tree>, Check>, Ancestor<Tree, Matches<InclusiveDescendant<Tree>, Check>>>} BuildVisitor\n *   Build a typed `Visitor` function from a tree and a test.\n *\n *   It will infer which values are passed as `node` and which as `parents`.\n * @template {UnistNode} [Tree=UnistNode]\n *   Tree type.\n * @template {Test} [Check=Test]\n *   Test type.\n */\n\nimport {convert} from 'unist-util-is'\nimport {color} from 'unist-util-visit-parents/do-not-use-color'\n\n/** @type {Readonly<ActionTuple>} */\nconst empty = []\n\n/**\n * Continue traversing as normal.\n */\nexport const CONTINUE = true\n\n/**\n * Stop traversing immediately.\n */\nexport const EXIT = false\n\n/**\n * Do not traverse this node’s children.\n */\nexport const SKIP = 'skip'\n\n/**\n * Visit nodes, with ancestral information.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @overload\n * @param {Tree} tree\n * @param {Check} check\n * @param {BuildVisitor<Tree, Check>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {undefined}\n *\n * @overload\n * @param {Tree} tree\n * @param {BuildVisitor<Tree>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {undefined}\n *\n * @param {UnistNode} tree\n *   Tree to traverse.\n * @param {Visitor | Test} test\n *   `unist-util-is`-compatible test\n * @param {Visitor | boolean | null | undefined} [visitor]\n *   Handle each node.\n * @param {boolean | null | undefined} [reverse]\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns {undefined}\n *   Nothing.\n *\n * @template {UnistNode} Tree\n *   Node type.\n * @template {Test} Check\n *   `unist-util-is`-compatible test.\n */\nexport function visitParents(tree, test, visitor, reverse) {\n  /** @type {Test} */\n  let check\n\n  if (typeof test === 'function' && typeof visitor !== 'function') {\n    reverse = visitor\n    // @ts-expect-error no visitor given, so `visitor` is test.\n    visitor = test\n  } else {\n    // @ts-expect-error visitor given, so `test` isn’t a visitor.\n    check = test\n  }\n\n  const is = convert(check)\n  const step = reverse ? -1 : 1\n\n  factory(tree, undefined, [])()\n\n  /**\n   * @param {UnistNode} node\n   * @param {number | undefined} index\n   * @param {Array<UnistParent>} parents\n   */\n  function factory(node, index, parents) {\n    const value = /** @type {Record<string, unknown>} */ (\n      node && typeof node === 'object' ? node : {}\n    )\n\n    if (typeof value.type === 'string') {\n      const name =\n        // `hast`\n        typeof value.tagName === 'string'\n          ? value.tagName\n          : // `xast`\n          typeof value.name === 'string'\n          ? value.name\n          : undefined\n\n      Object.defineProperty(visit, 'name', {\n        value:\n          'node (' + color(node.type + (name ? '<' + name + '>' : '')) + ')'\n      })\n    }\n\n    return visit\n\n    function visit() {\n      /** @type {Readonly<ActionTuple>} */\n      let result = empty\n      /** @type {Readonly<ActionTuple>} */\n      let subresult\n      /** @type {number} */\n      let offset\n      /** @type {Array<UnistParent>} */\n      let grandparents\n\n      if (!test || is(node, index, parents[parents.length - 1] || undefined)) {\n        // @ts-expect-error: `visitor` is now a visitor.\n        result = toResult(visitor(node, parents))\n\n        if (result[0] === EXIT) {\n          return result\n        }\n      }\n\n      if ('children' in node && node.children) {\n        const nodeAsParent = /** @type {UnistParent} */ (node)\n\n        if (nodeAsParent.children && result[0] !== SKIP) {\n          offset = (reverse ? nodeAsParent.children.length : -1) + step\n          grandparents = parents.concat(nodeAsParent)\n\n          while (offset > -1 && offset < nodeAsParent.children.length) {\n            const child = nodeAsParent.children[offset]\n\n            subresult = factory(child, offset, grandparents)()\n\n            if (subresult[0] === EXIT) {\n              return subresult\n            }\n\n            offset =\n              typeof subresult[1] === 'number' ? subresult[1] : offset + step\n          }\n        }\n      }\n\n      return result\n    }\n  }\n}\n\n/**\n * Turn a return value into a clean result.\n *\n * @param {VisitorResult} value\n *   Valid return values from visitors.\n * @returns {Readonly<ActionTuple>}\n *   Clean result.\n */\nfunction toResult(value) {\n  if (Array.isArray(value)) {\n    return value\n  }\n\n  if (typeof value === 'number') {\n    return [CONTINUE, value]\n  }\n\n  return value === null || value === undefined ? empty : [value]\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;CAWC,GAED;;;;;;;;;;;;;;;;;;;CAmBC,GAED;;;;;;;;;;;CAWC,GAED;;;CAGC,GAED;;;;;CAKC,GAED;;;;;;;;;;;;;CAaC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;;;;;;;CAiBC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GAED;;;;;;;;;;;;;;;;;;CAkBC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmCC,GAED;;;;;;;;;CASC;;;;;;AAED;AACA;;;AAEA,kCAAkC,GAClC,MAAM,QAAQ,EAAE;AAKT,MAAM,WAAW;AAKjB,MAAM,OAAO;AAKb,MAAM,OAAO;AAiDb,SAAS,aAAa,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO;IACvD,iBAAiB,GACjB,IAAI;IAEJ,IAAI,OAAO,SAAS,cAAc,OAAO,YAAY,YAAY;QAC/D,UAAU;QACV,2DAA2D;QAC3D,UAAU;IACZ,OAAO;QACL,6DAA6D;QAC7D,QAAQ;IACV;IAEA,MAAM,KAAK,CAAA,GAAA,mJAAA,CAAA,UAAO,AAAD,EAAE;IACnB,MAAM,OAAO,UAAU,CAAC,IAAI;IAE5B,QAAQ,MAAM,WAAW,EAAE;IAE3B;;;;GAIC,GACD,SAAS,QAAQ,IAAI,EAAE,KAAK,EAAE,OAAO;QACnC,MAAM,QACJ,QAAQ,OAAO,SAAS,WAAW,OAAO,CAAC;QAG7C,IAAI,OAAO,MAAM,IAAI,KAAK,UAAU;YAClC,MAAM,OACJ,SAAS;YACT,OAAO,MAAM,OAAO,KAAK,WACrB,MAAM,OAAO,GAEf,OAAO,MAAM,IAAI,KAAK,WACpB,MAAM,IAAI,GACV;YAEN,OAAO,cAAc,CAAC,OAAO,QAAQ;gBACnC,OACE,WAAW,CAAA,GAAA,yKAAA,CAAA,QAAK,AAAD,EAAE,KAAK,IAAI,GAAG,CAAC,OAAO,MAAM,OAAO,MAAM,EAAE,KAAK;YACnE;QACF;QAEA,OAAO;;QAEP,SAAS;YACP,kCAAkC,GAClC,IAAI,SAAS;YACb,kCAAkC,GAClC,IAAI;YACJ,mBAAmB,GACnB,IAAI;YACJ,+BAA+B,GAC/B,IAAI;YAEJ,IAAI,CAAC,QAAQ,GAAG,MAAM,OAAO,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,IAAI,YAAY;gBACtE,gDAAgD;gBAChD,SAAS,SAAS,QAAQ,MAAM;gBAEhC,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM;oBACtB,OAAO;gBACT;YACF;YAEA,IAAI,cAAc,QAAQ,KAAK,QAAQ,EAAE;gBACvC,MAAM,eAA2C;gBAEjD,IAAI,aAAa,QAAQ,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM;oBAC/C,SAAS,CAAC,UAAU,aAAa,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI;oBACzD,eAAe,QAAQ,MAAM,CAAC;oBAE9B,MAAO,SAAS,CAAC,KAAK,SAAS,aAAa,QAAQ,CAAC,MAAM,CAAE;wBAC3D,MAAM,QAAQ,aAAa,QAAQ,CAAC,OAAO;wBAE3C,YAAY,QAAQ,OAAO,QAAQ;wBAEnC,IAAI,SAAS,CAAC,EAAE,KAAK,MAAM;4BACzB,OAAO;wBACT;wBAEA,SACE,OAAO,SAAS,CAAC,EAAE,KAAK,WAAW,SAAS,CAAC,EAAE,GAAG,SAAS;oBAC/D;gBACF;YACF;YAEA,OAAO;QACT;IACF;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,SAAS,KAAK;IACrB,IAAI,MAAM,OAAO,CAAC,QAAQ;QACxB,OAAO;IACT;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;YAAC;YAAU;SAAM;IAC1B;IAEA,OAAO,UAAU,QAAQ,UAAU,YAAY,QAAQ;QAAC;KAAM;AAChE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1329, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-format/lib/index.js"], "sourcesContent": ["/**\n * @import {Nodes, RootContent, Root} from 'hast'\n * @import {BuildVisitor} from 'unist-util-visit-parents'\n * @import {Options, State} from './types.js'\n */\n\nimport {embedded} from 'hast-util-embedded'\nimport {minifyWhitespace} from 'hast-util-minify-whitespace'\nimport {phrasing} from 'hast-util-phrasing'\nimport {whitespace} from 'hast-util-whitespace'\nimport {whitespaceSensitiveTagNames} from 'html-whitespace-sensitive-tag-names'\nimport {SKIP, visitParents} from 'unist-util-visit-parents'\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Format whitespace in HTML.\n *\n * @param {Root} tree\n *   Tree.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nexport function format(tree, options) {\n  const settings = options || emptyOptions\n\n  /** @type {State} */\n  const state = {\n    blanks: settings.blanks || [],\n    head: false,\n    indentInitial: settings.indentInitial !== false,\n    indent:\n      typeof settings.indent === 'number'\n        ? ' '.repeat(settings.indent)\n        : typeof settings.indent === 'string'\n          ? settings.indent\n          : '  '\n  }\n\n  minifyWhitespace(tree, {newlines: true})\n\n  visitParents(tree, visitor)\n\n  /**\n   * @type {BuildVisitor<Root>}\n   */\n  function visitor(node, parents) {\n    if (!('children' in node)) {\n      return\n    }\n\n    if (node.type === 'element' && node.tagName === 'head') {\n      state.head = true\n    }\n\n    if (state.head && node.type === 'element' && node.tagName === 'body') {\n      state.head = false\n    }\n\n    if (\n      node.type === 'element' &&\n      whitespaceSensitiveTagNames.includes(node.tagName)\n    ) {\n      return SKIP\n    }\n\n    // Don’t indent content of whitespace-sensitive nodes / inlines.\n    if (node.children.length === 0 || !padding(state, node)) {\n      return\n    }\n\n    let level = parents.length\n\n    if (!state.indentInitial) {\n      level--\n    }\n\n    let eol = false\n\n    // Indent newlines in `text`.\n    for (const child of node.children) {\n      if (child.type === 'comment' || child.type === 'text') {\n        if (child.value.includes('\\n')) {\n          eol = true\n        }\n\n        child.value = child.value.replace(\n          / *\\n/g,\n          '$&' + state.indent.repeat(level)\n        )\n      }\n    }\n\n    /** @type {Array<RootContent>} */\n    const result = []\n    /** @type {RootContent | undefined} */\n    let previous\n\n    for (const child of node.children) {\n      if (padding(state, child) || (eol && !previous)) {\n        addBreak(result, level, child)\n        eol = true\n      }\n\n      previous = child\n      result.push(child)\n    }\n\n    if (previous && (eol || padding(state, previous))) {\n      // Ignore trailing whitespace (if that already existed), as we’ll add\n      // properly indented whitespace.\n      if (whitespace(previous)) {\n        result.pop()\n        previous = result[result.length - 1]\n      }\n\n      addBreak(result, level - 1)\n    }\n\n    node.children = result\n  }\n\n  /**\n   * @param {Array<RootContent>} list\n   *   Nodes.\n   * @param {number} level\n   *   Indentation level.\n   * @param {RootContent | undefined} [next]\n   *   Next node.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function addBreak(list, level, next) {\n    const tail = list[list.length - 1]\n    const previous = tail && whitespace(tail) ? list[list.length - 2] : tail\n    const replace =\n      (blank(state, previous) && blank(state, next) ? '\\n\\n' : '\\n') +\n      state.indent.repeat(Math.max(level, 0))\n\n    if (tail && tail.type === 'text') {\n      tail.value = whitespace(tail) ? replace : tail.value + replace\n    } else {\n      list.push({type: 'text', value: replace})\n    }\n  }\n}\n\n/**\n * @param {State} state\n *   Info passed around.\n * @param {Nodes | undefined} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is a blank.\n */\nfunction blank(state, node) {\n  return Boolean(\n    node &&\n      node.type === 'element' &&\n      state.blanks.length > 0 &&\n      state.blanks.includes(node.tagName)\n  )\n}\n\n/**\n * @param {State} state\n *   Info passed around.\n * @param {Nodes} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` should be padded.\n */\nfunction padding(state, node) {\n  return (\n    node.type === 'root' ||\n    (node.type === 'element'\n      ? state.head ||\n        node.tagName === 'script' ||\n        embedded(node) ||\n        !phrasing(node)\n      : false)\n  )\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,oBAAoB,GACpB,MAAM,eAAe,CAAC;AAYf,SAAS,OAAO,IAAI,EAAE,OAAO;IAClC,MAAM,WAAW,WAAW;IAE5B,kBAAkB,GAClB,MAAM,QAAQ;QACZ,QAAQ,SAAS,MAAM,IAAI,EAAE;QAC7B,MAAM;QACN,eAAe,SAAS,aAAa,KAAK;QAC1C,QACE,OAAO,SAAS,MAAM,KAAK,WACvB,IAAI,MAAM,CAAC,SAAS,MAAM,IAC1B,OAAO,SAAS,MAAM,KAAK,WACzB,SAAS,MAAM,GACf;IACV;IAEA,CAAA,GAAA,oKAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;QAAC,UAAU;IAAI;IAEtC,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE,MAAM;IAEnB;;GAEC,GACD,SAAS,QAAQ,IAAI,EAAE,OAAO;QAC5B,IAAI,CAAC,CAAC,cAAc,IAAI,GAAG;YACzB;QACF;QAEA,IAAI,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK,QAAQ;YACtD,MAAM,IAAI,GAAG;QACf;QAEA,IAAI,MAAM,IAAI,IAAI,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK,QAAQ;YACpE,MAAM,IAAI,GAAG;QACf;QAEA,IACE,KAAK,IAAI,KAAK,aACd,+KAAA,CAAA,8BAA2B,CAAC,QAAQ,CAAC,KAAK,OAAO,GACjD;YACA,OAAO,iKAAA,CAAA,OAAI;QACb;QAEA,gEAAgE;QAChE,IAAI,KAAK,QAAQ,CAAC,MAAM,KAAK,KAAK,CAAC,QAAQ,OAAO,OAAO;YACvD;QACF;QAEA,IAAI,QAAQ,QAAQ,MAAM;QAE1B,IAAI,CAAC,MAAM,aAAa,EAAE;YACxB;QACF;QAEA,IAAI,MAAM;QAEV,6BAA6B;QAC7B,KAAK,MAAM,SAAS,KAAK,QAAQ,CAAE;YACjC,IAAI,MAAM,IAAI,KAAK,aAAa,MAAM,IAAI,KAAK,QAAQ;gBACrD,IAAI,MAAM,KAAK,CAAC,QAAQ,CAAC,OAAO;oBAC9B,MAAM;gBACR;gBAEA,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,OAAO,CAC/B,SACA,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC;YAE/B;QACF;QAEA,+BAA+B,GAC/B,MAAM,SAAS,EAAE;QACjB,oCAAoC,GACpC,IAAI;QAEJ,KAAK,MAAM,SAAS,KAAK,QAAQ,CAAE;YACjC,IAAI,QAAQ,OAAO,UAAW,OAAO,CAAC,UAAW;gBAC/C,SAAS,QAAQ,OAAO;gBACxB,MAAM;YACR;YAEA,WAAW;YACX,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,YAAY,CAAC,OAAO,QAAQ,OAAO,SAAS,GAAG;YACjD,qEAAqE;YACrE,gCAAgC;YAChC,IAAI,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE,WAAW;gBACxB,OAAO,GAAG;gBACV,WAAW,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;YACtC;YAEA,SAAS,QAAQ,QAAQ;QAC3B;QAEA,KAAK,QAAQ,GAAG;IAClB;IAEA;;;;;;;;;GASC,GACD,SAAS,SAAS,IAAI,EAAE,KAAK,EAAE,IAAI;QACjC,MAAM,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QAClC,MAAM,WAAW,QAAQ,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,GAAG;QACpE,MAAM,UACJ,CAAC,MAAM,OAAO,aAAa,MAAM,OAAO,QAAQ,SAAS,IAAI,IAC7D,MAAM,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,OAAO;QAEtC,IAAI,QAAQ,KAAK,IAAI,KAAK,QAAQ;YAChC,KAAK,KAAK,GAAG,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,UAAU,KAAK,KAAK,GAAG;QACzD,OAAO;YACL,KAAK,IAAI,CAAC;gBAAC,MAAM;gBAAQ,OAAO;YAAO;QACzC;IACF;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,MAAM,KAAK,EAAE,IAAI;IACxB,OAAO,QACL,QACE,KAAK,IAAI,KAAK,aACd,MAAM,MAAM,CAAC,MAAM,GAAG,KACtB,MAAM,MAAM,CAAC,QAAQ,CAAC,KAAK,OAAO;AAExC;AAEA;;;;;;;CAOC,GACD,SAAS,QAAQ,KAAK,EAAE,IAAI;IAC1B,OACE,KAAK,IAAI,KAAK,UACd,CAAC,KAAK,IAAI,KAAK,YACX,MAAM,IAAI,IACV,KAAK,OAAO,KAAK,YACjB,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,SACT,CAAC,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,QACV,KAAK;AAEb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1464, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/rehype-format/lib/index.js"], "sourcesContent": ["/**\n * @import {Options} from 'hast-util-format'\n * @import {Root} from 'hast'\n */\n\nimport {format} from 'hast-util-format'\n\n/**\n * Format whitespace in HTML.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns\n *   Transform.\n */\nexport default function rehypeFormat(options) {\n  /**\n   * Transform.\n   *\n   * @param {Root} tree\n   *   Tree.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  return function (tree) {\n    format(tree, options)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AAUe,SAAS,aAAa,OAAO;IAC1C;;;;;;;GAOC,GACD,OAAO,SAAU,IAAI;QACnB,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;IACf;AACF", "ignoreList": [0], "debugId": null}}]}