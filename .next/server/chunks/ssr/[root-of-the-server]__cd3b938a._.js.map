{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_59dee874-module__9CtR0q__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/lib/crypto-polyfill.ts"], "sourcesContent": ["/**\n * Comprehensive crypto polyfill for both server-side and client-side compatibility\n * Provides crypto.randomUUID() and crypto.getRandomValues() functionality when not available\n */\n\n// UUID v4 implementation\nfunction generateUUID(): string {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\n    return v.toString(16);\n  });\n}\n\n// getRandomValues polyfill implementation\nfunction getRandomValuesPolyfill(array: any): any {\n  if (array instanceof Uint8Array) {\n    for (let i = 0; i < array.length; i++) {\n      array[i] = Math.floor(Math.random() * 256);\n    }\n  } else if (array instanceof Uint16Array) {\n    for (let i = 0; i < array.length; i++) {\n      array[i] = Math.floor(Math.random() * 65536);\n    }\n  } else if (array instanceof Uint32Array) {\n    for (let i = 0; i < array.length; i++) {\n      array[i] = Math.floor(Math.random() * 4294967296);\n    }\n  } else if (array instanceof Int8Array) {\n    for (let i = 0; i < array.length; i++) {\n      array[i] = Math.floor(Math.random() * 256) - 128;\n    }\n  } else if (array instanceof Int16Array) {\n    for (let i = 0; i < array.length; i++) {\n      array[i] = Math.floor(Math.random() * 65536) - 32768;\n    }\n  } else if (array instanceof Int32Array) {\n    for (let i = 0; i < array.length; i++) {\n      array[i] = Math.floor(Math.random() * 4294967296) - 2147483648;\n    }\n  }\n  return array;\n}\n\n// Client-side polyfill (browser environment)\nif (typeof window !== 'undefined') {\n  // Ensure crypto object exists\n  if (!window.crypto) {\n    (window as any).crypto = {};\n  }\n\n  // Add randomUUID if it doesn't exist\n  if (!window.crypto.randomUUID) {\n    (window.crypto as any).randomUUID = generateUUID;\n  }\n\n  // Add getRandomValues if it doesn't exist\n  if (!window.crypto.getRandomValues) {\n    (window.crypto as any).getRandomValues = getRandomValuesPolyfill;\n  }\n\n  // Also polyfill the global crypto if it exists but lacks functions\n  if (typeof crypto !== 'undefined') {\n    if (!crypto.randomUUID) {\n      (crypto as any).randomUUID = generateUUID;\n    }\n    if (!crypto.getRandomValues) {\n      (crypto as any).getRandomValues = getRandomValuesPolyfill;\n    }\n  }\n}\n\n// Server-side polyfill (Node.js environment)\nif (typeof globalThis !== 'undefined') {\n  // Ensure crypto object exists\n  if (!globalThis.crypto) {\n    globalThis.crypto = {} as Crypto;\n  }\n\n  // Add randomUUID if it doesn't exist\n  if (!globalThis.crypto.randomUUID) {\n    (globalThis.crypto as any).randomUUID = generateUUID;\n  }\n\n  // Add getRandomValues if it doesn't exist\n  if (!globalThis.crypto.getRandomValues) {\n    (globalThis.crypto as any).getRandomValues = getRandomValuesPolyfill;\n  }\n}\n\n// Additional fallback for environments where crypto exists but functions don't\nif (typeof crypto !== 'undefined') {\n  if (!crypto.randomUUID) {\n    (crypto as any).randomUUID = generateUUID;\n  }\n  if (!crypto.getRandomValues) {\n    (crypto as any).getRandomValues = getRandomValuesPolyfill;\n  }\n}\n\nexport {};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,yBAAyB;;AACzB,SAAS;IACP,OAAO,uCAAuC,OAAO,CAAC,SAAS,SAAS,CAAC;QACvE,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK;QAC/B,MAAM,IAAI,MAAM,MAAM,IAAK,IAAI,MAAM;QACrC,OAAO,EAAE,QAAQ,CAAC;IACpB;AACF;AAEA,0CAA0C;AAC1C,SAAS,wBAAwB,KAAU;IACzC,IAAI,iBAAiB,YAAY;QAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,KAAK,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QACxC;IACF,OAAO,IAAI,iBAAiB,aAAa;QACvC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,KAAK,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QACxC;IACF,OAAO,IAAI,iBAAiB,aAAa;QACvC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,KAAK,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QACxC;IACF,OAAO,IAAI,iBAAiB,WAAW;QACrC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,KAAK,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;QAC/C;IACF,OAAO,IAAI,iBAAiB,YAAY;QACtC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,KAAK,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS;QACjD;IACF,OAAO,IAAI,iBAAiB,YAAY;QACtC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,KAAK,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,cAAc;QACtD;IACF;IACA,OAAO;AACT;AAEA,6CAA6C;AAC7C,uCAAmC;;AAyBnC;AAEA,6CAA6C;AAC7C,IAAI,OAAO,eAAe,aAAa;IACrC,8BAA8B;IAC9B,IAAI,CAAC,WAAW,MAAM,EAAE;QACtB,WAAW,MAAM,GAAG,CAAC;IACvB;IAEA,qCAAqC;IACrC,IAAI,CAAC,WAAW,MAAM,CAAC,UAAU,EAAE;QAChC,WAAW,MAAM,CAAS,UAAU,GAAG;IAC1C;IAEA,0CAA0C;IAC1C,IAAI,CAAC,WAAW,MAAM,CAAC,eAAe,EAAE;QACrC,WAAW,MAAM,CAAS,eAAe,GAAG;IAC/C;AACF;AAEA,+EAA+E;AAC/E,IAAI,OAAO,WAAW,aAAa;IACjC,IAAI,CAAC,OAAO,UAAU,EAAE;QACrB,OAAe,UAAU,GAAG;IAC/B;IACA,IAAI,CAAC,OAAO,eAAe,EAAE;QAC1B,OAAe,eAAe,GAAG;IACpC;AACF", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/providers/ConvexProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ConvexClientProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ConvexClientProvider() from the server but ConvexClientProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/providers/ConvexProvider.tsx <module evaluation>\",\n    \"ConvexClientProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,6EACA", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/providers/ConvexProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ConvexClientProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ConvexClientProvider() from the server but ConvexClientProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/providers/ConvexProvider.tsx\",\n    \"ConvexClientProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,yDACA", "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from 'next';\nimport { Inter } from 'next/font/google';\nimport './globals.css';\nimport './lib/crypto-polyfill';\nimport { ConvexClientProvider } from './components/providers/ConvexProvider';\nimport { Toaster } from 'sonner';\n\nconst inter = Inter({ subsets: ['latin'] });\n\nexport const metadata: Metadata = {\n  title: 'Scripty - Collaborative Content Editor',\n  description: 'Create and edit scripts and screenplays together in real-time',\n};\n\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return (\n    <html lang=\"en\">\n      <body className={inter.className}>\n        <ConvexClientProvider>\n          {children}\n          <Toaster />\n        </ConvexClientProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAGA;AACA;AACA;;;;;;;AAIO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YAAK,WAAW,yIAAA,CAAA,UAAK,CAAC,SAAS;sBAC9B,cAAA,8OAAC,iJAAA,CAAA,uBAAoB;;oBAClB;kCACD,8OAAC,wIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;AAKlB", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/sonner/dist/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs <module evaluation>\",\n    \"Toaster\",\n);\nexport const toast = registerClientReference(\n    function() { throw new Error(\"Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs <module evaluation>\",\n    \"toast\",\n);\nexport const useSonner = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSonner() from the server but useSonner is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs <module evaluation>\",\n    \"useSonner\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,oEACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,oEACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,oEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/sonner/dist/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs\",\n    \"Toaster\",\n);\nexport const toast = registerClientReference(\n    function() { throw new Error(\"Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs\",\n    \"toast\",\n);\nexport const useSonner = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSonner() from the server but useSonner is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs\",\n    \"useSonner\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,gDACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,gDACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,gDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}