{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/rehype-stringify/index.js"], "sourcesContent": ["// Note: types exposed from `index.d.ts`.\nexport {default} from './lib/index.js'\n"], "names": [], "mappings": "AAAA,yCAAyC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/html-void-elements/index.js"], "sourcesContent": ["/**\n * List of HTML void tag names.\n *\n * @type {Array<string>}\n */\nexport const htmlVoidElements = [\n  'area',\n  'base',\n  'basefont',\n  'bgsound',\n  'br',\n  'col',\n  'command',\n  'embed',\n  'frame',\n  'hr',\n  'image',\n  'img',\n  'input',\n  'keygen',\n  'link',\n  'meta',\n  'param',\n  'source',\n  'track',\n  'wbr'\n]\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACM,MAAM,mBAAmB;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/property-information/lib/util/schema.js"], "sourcesContent": ["/**\n * @import {Schema as SchemaType, Space} from 'property-information'\n */\n\n/** @type {SchemaType} */\nexport class Schema {\n  /**\n   * @param {SchemaType['property']} property\n   *   Property.\n   * @param {SchemaType['normal']} normal\n   *   Normal.\n   * @param {Space | undefined} [space]\n   *   Space.\n   * @returns\n   *   Schema.\n   */\n  constructor(property, normal, space) {\n    this.normal = normal\n    this.property = property\n\n    if (space) {\n      this.space = space\n    }\n  }\n}\n\nSchema.prototype.normal = {}\nSchema.prototype.property = {}\nSchema.prototype.space = undefined\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,uBAAuB;;;AAChB,MAAM;IACX;;;;;;;;;GASC,GACD,YAAY,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAE;QACnC,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,QAAQ,GAAG;QAEhB,IAAI,OAAO;YACT,IAAI,CAAC,KAAK,GAAG;QACf;IACF;AACF;AAEA,OAAO,SAAS,CAAC,MAAM,GAAG,CAAC;AAC3B,OAAO,SAAS,CAAC,QAAQ,GAAG,CAAC;AAC7B,OAAO,SAAS,CAAC,KAAK,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/property-information/lib/util/merge.js"], "sourcesContent": ["/**\n * @import {Info, Space} from 'property-information'\n */\n\nimport {Schema} from './schema.js'\n\n/**\n * @param {ReadonlyArray<Schema>} definitions\n *   Definitions.\n * @param {Space | undefined} [space]\n *   Space.\n * @returns {Schema}\n *   Schema.\n */\nexport function merge(definitions, space) {\n  /** @type {Record<string, Info>} */\n  const property = {}\n  /** @type {Record<string, string>} */\n  const normal = {}\n\n  for (const definition of definitions) {\n    Object.assign(property, definition.property)\n    Object.assign(normal, definition.normal)\n  }\n\n  return new Schema(property, normal, space)\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;;AAUO,SAAS,MAAM,WAAW,EAAE,KAAK;IACtC,iCAAiC,GACjC,MAAM,WAAW,CAAC;IAClB,mCAAmC,GACnC,MAAM,SAAS,CAAC;IAEhB,KAAK,MAAM,cAAc,YAAa;QACpC,OAAO,MAAM,CAAC,UAAU,WAAW,QAAQ;QAC3C,OAAO,MAAM,CAAC,QAAQ,WAAW,MAAM;IACzC;IAEA,OAAO,IAAI,gKAAA,CAAA,SAAM,CAAC,UAAU,QAAQ;AACtC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/property-information/lib/normalize.js"], "sourcesContent": ["/**\n * Get the cleaned case insensitive form of an attribute or property.\n *\n * @param {string} value\n *   An attribute-like or property-like name.\n * @returns {string}\n *   Value that can be used to look up the properly cased property on a\n *   `Schema`.\n */\nexport function normalize(value) {\n  return value.toLowerCase()\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;AACM,SAAS,UAAU,KAAK;IAC7B,OAAO,MAAM,WAAW;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/property-information/lib/util/info.js"], "sourcesContent": ["/**\n * @import {Info as InfoType} from 'property-information'\n */\n\n/** @type {InfoType} */\nexport class Info {\n  /**\n   * @param {string} property\n   *   Property.\n   * @param {string} attribute\n   *   Attribute.\n   * @returns\n   *   Info.\n   */\n  constructor(property, attribute) {\n    this.attribute = attribute\n    this.property = property\n  }\n}\n\nInfo.prototype.attribute = ''\nInfo.prototype.booleanish = false\nInfo.prototype.boolean = false\nInfo.prototype.commaOrSpaceSeparated = false\nInfo.prototype.commaSeparated = false\nInfo.prototype.defined = false\nInfo.prototype.mustUseProperty = false\nInfo.prototype.number = false\nInfo.prototype.overloadedBoolean = false\nInfo.prototype.property = ''\nInfo.prototype.spaceSeparated = false\nInfo.prototype.space = undefined\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,qBAAqB;;;AACd,MAAM;IACX;;;;;;;GAOC,GACD,YAAY,QAAQ,EAAE,SAAS,CAAE;QAC/B,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,QAAQ,GAAG;IAClB;AACF;AAEA,KAAK,SAAS,CAAC,SAAS,GAAG;AAC3B,KAAK,SAAS,CAAC,UAAU,GAAG;AAC5B,KAAK,SAAS,CAAC,OAAO,GAAG;AACzB,KAAK,SAAS,CAAC,qBAAqB,GAAG;AACvC,KAAK,SAAS,CAAC,cAAc,GAAG;AAChC,KAAK,SAAS,CAAC,OAAO,GAAG;AACzB,KAAK,SAAS,CAAC,eAAe,GAAG;AACjC,KAAK,SAAS,CAAC,MAAM,GAAG;AACxB,KAAK,SAAS,CAAC,iBAAiB,GAAG;AACnC,KAAK,SAAS,CAAC,QAAQ,GAAG;AAC1B,KAAK,SAAS,CAAC,cAAc,GAAG;AAChC,KAAK,SAAS,CAAC,KAAK,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/property-information/lib/util/types.js"], "sourcesContent": ["let powers = 0\n\nexport const boolean = increment()\nexport const booleanish = increment()\nexport const overloadedBoolean = increment()\nexport const number = increment()\nexport const spaceSeparated = increment()\nexport const commaSeparated = increment()\nexport const commaOrSpaceSeparated = increment()\n\nfunction increment() {\n  return 2 ** ++powers\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA,IAAI,SAAS;AAEN,MAAM,UAAU;AAChB,MAAM,aAAa;AACnB,MAAM,oBAAoB;AAC1B,MAAM,SAAS;AACf,MAAM,iBAAiB;AACvB,MAAM,iBAAiB;AACvB,MAAM,wBAAwB;AAErC,SAAS;IACP,OAAO,KAAK,EAAE;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/property-information/lib/util/defined-info.js"], "sourcesContent": ["/**\n * @import {Space} from 'property-information'\n */\n\nimport {Info} from './info.js'\nimport * as types from './types.js'\n\nconst checks = /** @type {ReadonlyArray<keyof typeof types>} */ (\n  Object.keys(types)\n)\n\nexport class DefinedInfo extends Info {\n  /**\n   * @constructor\n   * @param {string} property\n   *   Property.\n   * @param {string} attribute\n   *   Attribute.\n   * @param {number | null | undefined} [mask]\n   *   Mask.\n   * @param {Space | undefined} [space]\n   *   Space.\n   * @returns\n   *   Info.\n   */\n  constructor(property, attribute, mask, space) {\n    let index = -1\n\n    super(property, attribute)\n\n    mark(this, 'space', space)\n\n    if (typeof mask === 'number') {\n      while (++index < checks.length) {\n        const check = checks[index]\n        mark(this, checks[index], (mask & types[check]) === types[check])\n      }\n    }\n  }\n}\n\nDefinedInfo.prototype.defined = true\n\n/**\n * @template {keyof DefinedInfo} Key\n *   Key type.\n * @param {DefinedInfo} values\n *   Info.\n * @param {Key} key\n *   Key.\n * @param {DefinedInfo[Key]} value\n *   Value.\n * @returns {undefined}\n *   Nothing.\n */\nfunction mark(values, key, value) {\n  if (value) {\n    values[key] = value\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;AACA;;;AAEA,MAAM,SACJ,OAAO,IAAI,CAAC;AAGP,MAAM,oBAAoB,8JAAA,CAAA,OAAI;IACnC;;;;;;;;;;;;GAYC,GACD,YAAY,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAE;QAC5C,IAAI,QAAQ,CAAC;QAEb,KAAK,CAAC,UAAU;QAEhB,KAAK,IAAI,EAAE,SAAS;QAEpB,IAAI,OAAO,SAAS,UAAU;YAC5B,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;gBAC9B,MAAM,QAAQ,MAAM,CAAC,MAAM;gBAC3B,KAAK,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,OAAO,+JAAK,CAAC,MAAM,MAAM,+JAAK,CAAC,MAAM;YAClE;QACF;IACF;AACF;AAEA,YAAY,SAAS,CAAC,OAAO,GAAG;AAEhC;;;;;;;;;;;CAWC,GACD,SAAS,KAAK,MAAM,EAAE,GAAG,EAAE,KAAK;IAC9B,IAAI,OAAO;QACT,MAAM,CAAC,IAAI,GAAG;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/property-information/lib/util/create.js"], "sourcesContent": ["/**\n * @import {Info, Space} from 'property-information'\n */\n\n/**\n * @typedef Definition\n *   Definition of a schema.\n * @property {Record<string, string> | undefined} [attributes]\n *   Normalzed names to special attribute case.\n * @property {ReadonlyArray<string> | undefined} [mustUseProperty]\n *   Normalized names that must be set as properties.\n * @property {Record<string, number | null>} properties\n *   Property names to their types.\n * @property {Space | undefined} [space]\n *   Space.\n * @property {Transform} transform\n *   Transform a property name.\n */\n\n/**\n * @callback Transform\n *   Transform.\n * @param {Record<string, string>} attributes\n *   Attributes.\n * @param {string} property\n *   Property.\n * @returns {string}\n *   Attribute.\n */\n\nimport {normalize} from '../normalize.js'\nimport {DefinedInfo} from './defined-info.js'\nimport {Schema} from './schema.js'\n\n/**\n * @param {Definition} definition\n *   Definition.\n * @returns {Schema}\n *   Schema.\n */\nexport function create(definition) {\n  /** @type {Record<string, Info>} */\n  const properties = {}\n  /** @type {Record<string, string>} */\n  const normals = {}\n\n  for (const [property, value] of Object.entries(definition.properties)) {\n    const info = new DefinedInfo(\n      property,\n      definition.transform(definition.attributes || {}, property),\n      value,\n      definition.space\n    )\n\n    if (\n      definition.mustUseProperty &&\n      definition.mustUseProperty.includes(property)\n    ) {\n      info.mustUseProperty = true\n    }\n\n    properties[property] = info\n\n    normals[normalize(property)] = property\n    normals[normalize(info.attribute)] = property\n  }\n\n  return new Schema(properties, normals, definition.space)\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;;;;;;;;;;CAaC,GAED;;;;;;;;;CASC;;;AAED;AACA;AACA;;;;AAQO,SAAS,OAAO,UAAU;IAC/B,iCAAiC,GACjC,MAAM,aAAa,CAAC;IACpB,mCAAmC,GACnC,MAAM,UAAU,CAAC;IAEjB,KAAK,MAAM,CAAC,UAAU,MAAM,IAAI,OAAO,OAAO,CAAC,WAAW,UAAU,EAAG;QACrE,MAAM,OAAO,IAAI,yKAAA,CAAA,cAAW,CAC1B,UACA,WAAW,SAAS,CAAC,WAAW,UAAU,IAAI,CAAC,GAAG,WAClD,OACA,WAAW,KAAK;QAGlB,IACE,WAAW,eAAe,IAC1B,WAAW,eAAe,CAAC,QAAQ,CAAC,WACpC;YACA,KAAK,eAAe,GAAG;QACzB;QAEA,UAAU,CAAC,SAAS,GAAG;QAEvB,OAAO,CAAC,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,UAAU,GAAG;QAC/B,OAAO,CAAC,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,KAAK,SAAS,EAAE,GAAG;IACvC;IAEA,OAAO,IAAI,gKAAA,CAAA,SAAM,CAAC,YAAY,SAAS,WAAW,KAAK;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/property-information/lib/aria.js"], "sourcesContent": ["import {create} from './util/create.js'\nimport {booleanish, number, spaceSeparated} from './util/types.js'\n\nexport const aria = create({\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: booleanish,\n    ariaChecked: booleanish,\n    ariaColCount: number,\n    ariaColIndex: number,\n    ariaColSpan: number,\n    ariaControls: spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: booleanish,\n    ariaDropEffect: spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: booleanish,\n    ariaFlowTo: spaceSeparated,\n    ariaGrabbed: booleanish,\n    ariaHasPopup: null,\n    ariaHidden: booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: spaceSeparated,\n    ariaLevel: number,\n    ariaLive: null,\n    ariaModal: booleanish,\n    ariaMultiLine: booleanish,\n    ariaMultiSelectable: booleanish,\n    ariaOrientation: null,\n    ariaOwns: spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: number,\n    ariaPressed: booleanish,\n    ariaReadOnly: booleanish,\n    ariaRelevant: null,\n    ariaRequired: booleanish,\n    ariaRoleDescription: spaceSeparated,\n    ariaRowCount: number,\n    ariaRowIndex: number,\n    ariaRowSpan: number,\n    ariaSelected: booleanish,\n    ariaSetSize: number,\n    ariaSort: null,\n    ariaValueMax: number,\n    ariaValueMin: number,\n    ariaValueNow: number,\n    ariaValueText: null,\n    role: null\n  },\n  transform(_, property) {\n    return property === 'role'\n      ? property\n      : 'aria-' + property.slice(4).toLowerCase()\n  }\n})\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,OAAO,CAAA,GAAA,gKAAA,CAAA,SAAM,AAAD,EAAE;IACzB,YAAY;QACV,sBAAsB;QACtB,YAAY,+JAAA,CAAA,aAAU;QACtB,kBAAkB;QAClB,UAAU,+JAAA,CAAA,aAAU;QACpB,aAAa,+JAAA,CAAA,aAAU;QACvB,cAAc,+JAAA,CAAA,SAAM;QACpB,cAAc,+JAAA,CAAA,SAAM;QACpB,aAAa,+JAAA,CAAA,SAAM;QACnB,cAAc,+JAAA,CAAA,iBAAc;QAC5B,aAAa;QACb,iBAAiB,+JAAA,CAAA,iBAAc;QAC/B,aAAa;QACb,cAAc,+JAAA,CAAA,aAAU;QACxB,gBAAgB,+JAAA,CAAA,iBAAc;QAC9B,kBAAkB;QAClB,cAAc,+JAA<PERSON>,CAAA,aAAU;QACxB,YAAY,+JAAA,CAAA,iBAAc;QAC1B,aAAa,+JAAA,CAAA,aAAU;QACvB,cAAc;QACd,YAAY,+JAAA,CAAA,aAAU;QACtB,aAAa;QACb,kBAAkB;QAClB,WAAW;QACX,gBAAgB,+JAAA,CAAA,iBAAc;QAC9B,WAAW,+JAAA,CAAA,SAAM;QACjB,UAAU;QACV,WAAW,+JAAA,CAAA,aAAU;QACrB,eAAe,+JAAA,CAAA,aAAU;QACzB,qBAAqB,+JAAA,CAAA,aAAU;QAC/B,iBAAiB;QACjB,UAAU,+JAAA,CAAA,iBAAc;QACxB,iBAAiB;QACjB,cAAc,+JAAA,CAAA,SAAM;QACpB,aAAa,+JAAA,CAAA,aAAU;QACvB,cAAc,+JAAA,CAAA,aAAU;QACxB,cAAc;QACd,cAAc,+JAAA,CAAA,aAAU;QACxB,qBAAqB,+JAAA,CAAA,iBAAc;QACnC,cAAc,+JAAA,CAAA,SAAM;QACpB,cAAc,+JAAA,CAAA,SAAM;QACpB,aAAa,+JAAA,CAAA,SAAM;QACnB,cAAc,+JAAA,CAAA,aAAU;QACxB,aAAa,+JAAA,CAAA,SAAM;QACnB,UAAU;QACV,cAAc,+JAAA,CAAA,SAAM;QACpB,cAAc,+JAAA,CAAA,SAAM;QACpB,cAAc,+JAAA,CAAA,SAAM;QACpB,eAAe;QACf,MAAM;IACR;IACA,WAAU,CAAC,EAAE,QAAQ;QACnB,OAAO,aAAa,SAChB,WACA,UAAU,SAAS,KAAK,CAAC,GAAG,WAAW;IAC7C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/property-information/lib/util/case-sensitive-transform.js"], "sourcesContent": ["/**\n * @param {Record<string, string>} attributes\n *   Attributes.\n * @param {string} attribute\n *   Attribute.\n * @returns {string}\n *   Transformed attribute.\n */\nexport function caseSensitiveTransform(attributes, attribute) {\n  return attribute in attributes ? attributes[attribute] : attribute\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACM,SAAS,uBAAuB,UAAU,EAAE,SAAS;IAC1D,OAAO,aAAa,aAAa,UAAU,CAAC,UAAU,GAAG;AAC3D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/property-information/lib/util/case-insensitive-transform.js"], "sourcesContent": ["import {caseSensitiveTransform} from './case-sensitive-transform.js'\n\n/**\n * @param {Record<string, string>} attributes\n *   Attributes.\n * @param {string} property\n *   Property.\n * @returns {string}\n *   Transformed property.\n */\nexport function caseInsensitiveTransform(attributes, property) {\n  return caseSensitiveTransform(attributes, property.toLowerCase())\n}\n"], "names": [], "mappings": ";;;AAAA;;AAUO,SAAS,yBAAyB,UAAU,EAAE,QAAQ;IAC3D,OAAO,CAAA,GAAA,wLAAA,CAAA,yBAAsB,AAAD,EAAE,YAAY,SAAS,WAAW;AAChE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/property-information/lib/html.js"], "sourcesContent": ["import {caseInsensitiveTransform} from './util/case-insensitive-transform.js'\nimport {create} from './util/create.js'\nimport {\n  booleanish,\n  boolean,\n  commaSeparated,\n  number,\n  overloadedBoolean,\n  spaceSeparated\n} from './util/types.js'\n\nexport const html = create({\n  attributes: {\n    acceptcharset: 'accept-charset',\n    classname: 'class',\n    htmlfor: 'for',\n    httpequiv: 'http-equiv'\n  },\n  mustUseProperty: ['checked', 'multiple', 'muted', 'selected'],\n  properties: {\n    // Standard Properties.\n    abbr: null,\n    accept: commaSeparated,\n    acceptCharset: spaceSeparated,\n    accessKey: spaceSeparated,\n    action: null,\n    allow: null,\n    allowFullScreen: boolean,\n    allowPaymentRequest: boolean,\n    allowUserMedia: boolean,\n    alt: null,\n    as: null,\n    async: boolean,\n    autoCapitalize: null,\n    autoComplete: spaceSeparated,\n    autoFocus: boolean,\n    autoPlay: boolean,\n    blocking: spaceSeparated,\n    capture: null,\n    charSet: null,\n    checked: boolean,\n    cite: null,\n    className: spaceSeparated,\n    cols: number,\n    colSpan: null,\n    content: null,\n    contentEditable: booleanish,\n    controls: boolean,\n    controlsList: spaceSeparated,\n    coords: number | commaSeparated,\n    crossOrigin: null,\n    data: null,\n    dateTime: null,\n    decoding: null,\n    default: boolean,\n    defer: boolean,\n    dir: null,\n    dirName: null,\n    disabled: boolean,\n    download: overloadedBoolean,\n    draggable: booleanish,\n    encType: null,\n    enterKeyHint: null,\n    fetchPriority: null,\n    form: null,\n    formAction: null,\n    formEncType: null,\n    formMethod: null,\n    formNoValidate: boolean,\n    formTarget: null,\n    headers: spaceSeparated,\n    height: number,\n    hidden: overloadedBoolean,\n    high: number,\n    href: null,\n    hrefLang: null,\n    htmlFor: spaceSeparated,\n    httpEquiv: spaceSeparated,\n    id: null,\n    imageSizes: null,\n    imageSrcSet: null,\n    inert: boolean,\n    inputMode: null,\n    integrity: null,\n    is: null,\n    isMap: boolean,\n    itemId: null,\n    itemProp: spaceSeparated,\n    itemRef: spaceSeparated,\n    itemScope: boolean,\n    itemType: spaceSeparated,\n    kind: null,\n    label: null,\n    lang: null,\n    language: null,\n    list: null,\n    loading: null,\n    loop: boolean,\n    low: number,\n    manifest: null,\n    max: null,\n    maxLength: number,\n    media: null,\n    method: null,\n    min: null,\n    minLength: number,\n    multiple: boolean,\n    muted: boolean,\n    name: null,\n    nonce: null,\n    noModule: boolean,\n    noValidate: boolean,\n    onAbort: null,\n    onAfterPrint: null,\n    onAuxClick: null,\n    onBeforeMatch: null,\n    onBeforePrint: null,\n    onBeforeToggle: null,\n    onBeforeUnload: null,\n    onBlur: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onContextLost: null,\n    onContextMenu: null,\n    onContextRestored: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFormData: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLanguageChange: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadEnd: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMessageError: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRejectionHandled: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onScrollEnd: null,\n    onSecurityPolicyViolation: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onSlotChange: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnhandledRejection: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onWheel: null,\n    open: boolean,\n    optimum: number,\n    pattern: null,\n    ping: spaceSeparated,\n    placeholder: null,\n    playsInline: boolean,\n    popover: null,\n    popoverTarget: null,\n    popoverTargetAction: null,\n    poster: null,\n    preload: null,\n    readOnly: boolean,\n    referrerPolicy: null,\n    rel: spaceSeparated,\n    required: boolean,\n    reversed: boolean,\n    rows: number,\n    rowSpan: number,\n    sandbox: spaceSeparated,\n    scope: null,\n    scoped: boolean,\n    seamless: boolean,\n    selected: boolean,\n    shadowRootClonable: boolean,\n    shadowRootDelegatesFocus: boolean,\n    shadowRootMode: null,\n    shape: null,\n    size: number,\n    sizes: null,\n    slot: null,\n    span: number,\n    spellCheck: booleanish,\n    src: null,\n    srcDoc: null,\n    srcLang: null,\n    srcSet: null,\n    start: number,\n    step: null,\n    style: null,\n    tabIndex: number,\n    target: null,\n    title: null,\n    translate: null,\n    type: null,\n    typeMustMatch: boolean,\n    useMap: null,\n    value: booleanish,\n    width: number,\n    wrap: null,\n    writingSuggestions: null,\n\n    // Legacy.\n    // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis\n    align: null, // Several. Use CSS `text-align` instead,\n    aLink: null, // `<body>`. Use CSS `a:active {color}` instead\n    archive: spaceSeparated, // `<object>`. List of URIs to archives\n    axis: null, // `<td>` and `<th>`. Use `scope` on `<th>`\n    background: null, // `<body>`. Use CSS `background-image` instead\n    bgColor: null, // `<body>` and table elements. Use CSS `background-color` instead\n    border: number, // `<table>`. Use CSS `border-width` instead,\n    borderColor: null, // `<table>`. Use CSS `border-color` instead,\n    bottomMargin: number, // `<body>`\n    cellPadding: null, // `<table>`\n    cellSpacing: null, // `<table>`\n    char: null, // Several table elements. When `align=char`, sets the character to align on\n    charOff: null, // Several table elements. When `char`, offsets the alignment\n    classId: null, // `<object>`\n    clear: null, // `<br>`. Use CSS `clear` instead\n    code: null, // `<object>`\n    codeBase: null, // `<object>`\n    codeType: null, // `<object>`\n    color: null, // `<font>` and `<hr>`. Use CSS instead\n    compact: boolean, // Lists. Use CSS to reduce space between items instead\n    declare: boolean, // `<object>`\n    event: null, // `<script>`\n    face: null, // `<font>`. Use CSS instead\n    frame: null, // `<table>`\n    frameBorder: null, // `<iframe>`. Use CSS `border` instead\n    hSpace: number, // `<img>` and `<object>`\n    leftMargin: number, // `<body>`\n    link: null, // `<body>`. Use CSS `a:link {color: *}` instead\n    longDesc: null, // `<frame>`, `<iframe>`, and `<img>`. Use an `<a>`\n    lowSrc: null, // `<img>`. Use a `<picture>`\n    marginHeight: number, // `<body>`\n    marginWidth: number, // `<body>`\n    noResize: boolean, // `<frame>`\n    noHref: boolean, // `<area>`. Use no href instead of an explicit `nohref`\n    noShade: boolean, // `<hr>`. Use background-color and height instead of borders\n    noWrap: boolean, // `<td>` and `<th>`\n    object: null, // `<applet>`\n    profile: null, // `<head>`\n    prompt: null, // `<isindex>`\n    rev: null, // `<link>`\n    rightMargin: number, // `<body>`\n    rules: null, // `<table>`\n    scheme: null, // `<meta>`\n    scrolling: booleanish, // `<frame>`. Use overflow in the child context\n    standby: null, // `<object>`\n    summary: null, // `<table>`\n    text: null, // `<body>`. Use CSS `color` instead\n    topMargin: number, // `<body>`\n    valueType: null, // `<param>`\n    version: null, // `<html>`. Use a doctype.\n    vAlign: null, // Several. Use CSS `vertical-align` instead\n    vLink: null, // `<body>`. Use CSS `a:visited {color}` instead\n    vSpace: number, // `<img>` and `<object>`\n\n    // Non-standard Properties.\n    allowTransparency: null,\n    autoCorrect: null,\n    autoSave: null,\n    disablePictureInPicture: boolean,\n    disableRemotePlayback: boolean,\n    prefix: null,\n    property: null,\n    results: number,\n    security: null,\n    unselectable: null\n  },\n  space: 'html',\n  transform: caseInsensitiveTransform\n})\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AASO,MAAM,OAAO,CAAA,GAAA,gKAAA,CAAA,SAAM,AAAD,EAAE;IACzB,YAAY;QACV,eAAe;QACf,WAAW;QACX,SAAS;QACT,WAAW;IACb;IACA,iBAAiB;QAAC;QAAW;QAAY;QAAS;KAAW;IAC7D,YAAY;QACV,uBAAuB;QACvB,MAAM;QACN,QAAQ,+JAAA,CAAA,iBAAc;QACtB,eAAe,+JAAA,CAAA,iBAAc;QAC7B,WAAW,+JAAA,CAAA,iBAAc;QACzB,QAAQ;QACR,OAAO;QACP,iBAAiB,+JAAA,CAAA,UAAO;QACxB,qBAAqB,+JAAA,CAAA,UAAO;QAC5B,gBAAgB,+JAAA,CAAA,UAAO;QACvB,KAAK;QACL,IAAI;QACJ,OAAO,+JAA<PERSON>,CAAA,UAAO;QACd,gBAAgB;QAChB,cAAc,+JAAA,CAAA,iBAAc;QAC5B,WAAW,+JAAA,CAAA,UAAO;QAClB,UAAU,+JAAA,CAAA,UAAO;QACjB,UAAU,+JAAA,CAAA,iBAAc;QACxB,SAAS;QACT,SAAS;QACT,SAAS,+JAAA,CAAA,UAAO;QAChB,MAAM;QACN,WAAW,+JAAA,CAAA,iBAAc;QACzB,MAAM,+JAAA,CAAA,SAAM;QACZ,SAAS;QACT,SAAS;QACT,iBAAiB,+JAAA,CAAA,aAAU;QAC3B,UAAU,+JAAA,CAAA,UAAO;QACjB,cAAc,+JAAA,CAAA,iBAAc;QAC5B,QAAQ,+JAAA,CAAA,SAAM,GAAG,+JAAA,CAAA,iBAAc;QAC/B,aAAa;QACb,MAAM;QACN,UAAU;QACV,UAAU;QACV,SAAS,+JAAA,CAAA,UAAO;QAChB,OAAO,+JAAA,CAAA,UAAO;QACd,KAAK;QACL,SAAS;QACT,UAAU,+JAAA,CAAA,UAAO;QACjB,UAAU,+JAAA,CAAA,oBAAiB;QAC3B,WAAW,+JAAA,CAAA,aAAU;QACrB,SAAS;QACT,cAAc;QACd,eAAe;QACf,MAAM;QACN,YAAY;QACZ,aAAa;QACb,YAAY;QACZ,gBAAgB,+JAAA,CAAA,UAAO;QACvB,YAAY;QACZ,SAAS,+JAAA,CAAA,iBAAc;QACvB,QAAQ,+JAAA,CAAA,SAAM;QACd,QAAQ,+JAAA,CAAA,oBAAiB;QACzB,MAAM,+JAAA,CAAA,SAAM;QACZ,MAAM;QACN,UAAU;QACV,SAAS,+JAAA,CAAA,iBAAc;QACvB,WAAW,+JAAA,CAAA,iBAAc;QACzB,IAAI;QACJ,YAAY;QACZ,aAAa;QACb,OAAO,+JAAA,CAAA,UAAO;QACd,WAAW;QACX,WAAW;QACX,IAAI;QACJ,OAAO,+JAAA,CAAA,UAAO;QACd,QAAQ;QACR,UAAU,+JAAA,CAAA,iBAAc;QACxB,SAAS,+JAAA,CAAA,iBAAc;QACvB,WAAW,+JAAA,CAAA,UAAO;QAClB,UAAU,+JAAA,CAAA,iBAAc;QACxB,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,MAAM;QACN,SAAS;QACT,MAAM,+JAAA,CAAA,UAAO;QACb,KAAK,+JAAA,CAAA,SAAM;QACX,UAAU;QACV,KAAK;QACL,WAAW,+JAAA,CAAA,SAAM;QACjB,OAAO;QACP,QAAQ;QACR,KAAK;QACL,WAAW,+JAAA,CAAA,SAAM;QACjB,UAAU,+JAAA,CAAA,UAAO;QACjB,OAAO,+JAAA,CAAA,UAAO;QACd,MAAM;QACN,OAAO;QACP,UAAU,+JAAA,CAAA,UAAO;QACjB,YAAY,+JAAA,CAAA,UAAO;QACnB,SAAS;QACT,cAAc;QACd,YAAY;QACZ,eAAe;QACf,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,QAAQ;QACR,UAAU;QACV,WAAW;QACX,kBAAkB;QAClB,UAAU;QACV,SAAS;QACT,SAAS;QACT,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,QAAQ;QACR,aAAa;QACb,OAAO;QACP,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,aAAa;QACb,YAAY;QACZ,aAAa;QACb,YAAY;QACZ,aAAa;QACb,QAAQ;QACR,kBAAkB;QAClB,WAAW;QACX,SAAS;QACT,SAAS;QACT,SAAS;QACT,YAAY;QACZ,cAAc;QACd,SAAS;QACT,WAAW;QACX,WAAW;QACX,YAAY;QACZ,SAAS;QACT,kBAAkB;QAClB,QAAQ;QACR,cAAc;QACd,kBAAkB;QAClB,WAAW;QACX,aAAa;QACb,WAAW;QACX,gBAAgB;QAChB,aAAa;QACb,cAAc;QACd,cAAc;QACd,aAAa;QACb,YAAY;QACZ,aAAa;QACb,WAAW;QACX,WAAW;QACX,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,SAAS;QACT,SAAS;QACT,QAAQ;QACR,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,oBAAoB;QACpB,SAAS;QACT,UAAU;QACV,UAAU;QACV,aAAa;QACb,2BAA2B;QAC3B,UAAU;QACV,WAAW;QACX,UAAU;QACV,cAAc;QACd,WAAW;QACX,WAAW;QACX,UAAU;QACV,WAAW;QACX,cAAc;QACd,UAAU;QACV,sBAAsB;QACtB,UAAU;QACV,gBAAgB;QAChB,WAAW;QACX,SAAS;QACT,MAAM,+JAAA,CAAA,UAAO;QACb,SAAS,+JAAA,CAAA,SAAM;QACf,SAAS;QACT,MAAM,+JAAA,CAAA,iBAAc;QACpB,aAAa;QACb,aAAa,+JAAA,CAAA,UAAO;QACpB,SAAS;QACT,eAAe;QACf,qBAAqB;QACrB,QAAQ;QACR,SAAS;QACT,UAAU,+JAAA,CAAA,UAAO;QACjB,gBAAgB;QAChB,KAAK,+JAAA,CAAA,iBAAc;QACnB,UAAU,+JAAA,CAAA,UAAO;QACjB,UAAU,+JAAA,CAAA,UAAO;QACjB,MAAM,+JAAA,CAAA,SAAM;QACZ,SAAS,+JAAA,CAAA,SAAM;QACf,SAAS,+JAAA,CAAA,iBAAc;QACvB,OAAO;QACP,QAAQ,+JAAA,CAAA,UAAO;QACf,UAAU,+JAAA,CAAA,UAAO;QACjB,UAAU,+JAAA,CAAA,UAAO;QACjB,oBAAoB,+JAAA,CAAA,UAAO;QAC3B,0BAA0B,+JAAA,CAAA,UAAO;QACjC,gBAAgB;QAChB,OAAO;QACP,MAAM,+JAAA,CAAA,SAAM;QACZ,OAAO;QACP,MAAM;QACN,MAAM,+JAAA,CAAA,SAAM;QACZ,YAAY,+JAAA,CAAA,aAAU;QACtB,KAAK;QACL,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,OAAO,+JAAA,CAAA,SAAM;QACb,MAAM;QACN,OAAO;QACP,UAAU,+JAAA,CAAA,SAAM;QAChB,QAAQ;QACR,OAAO;QACP,WAAW;QACX,MAAM;QACN,eAAe,+JAAA,CAAA,UAAO;QACtB,QAAQ;QACR,OAAO,+JAAA,CAAA,aAAU;QACjB,OAAO,+JAAA,CAAA,SAAM;QACb,MAAM;QACN,oBAAoB;QAEpB,UAAU;QACV,yEAAyE;QACzE,OAAO;QACP,OAAO;QACP,SAAS,+JAAA,CAAA,iBAAc;QACvB,MAAM;QACN,YAAY;QACZ,SAAS;QACT,QAAQ,+JAAA,CAAA,SAAM;QACd,aAAa;QACb,cAAc,+JAAA,CAAA,SAAM;QACpB,aAAa;QACb,aAAa;QACb,MAAM;QACN,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;QACV,OAAO;QACP,SAAS,+JAAA,CAAA,UAAO;QAChB,SAAS,+JAAA,CAAA,UAAO;QAChB,OAAO;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ,+JAAA,CAAA,SAAM;QACd,YAAY,+JAAA,CAAA,SAAM;QAClB,MAAM;QACN,UAAU;QACV,QAAQ;QACR,cAAc,+JAAA,CAAA,SAAM;QACpB,aAAa,+JAAA,CAAA,SAAM;QACnB,UAAU,+JAAA,CAAA,UAAO;QACjB,QAAQ,+JAAA,CAAA,UAAO;QACf,SAAS,+JAAA,CAAA,UAAO;QAChB,QAAQ,+JAAA,CAAA,UAAO;QACf,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,KAAK;QACL,aAAa,+JAAA,CAAA,SAAM;QACnB,OAAO;QACP,QAAQ;QACR,WAAW,+JAAA,CAAA,aAAU;QACrB,SAAS;QACT,SAAS;QACT,MAAM;QACN,WAAW,+JAAA,CAAA,SAAM;QACjB,WAAW;QACX,SAAS;QACT,QAAQ;QACR,OAAO;QACP,QAAQ,+JAAA,CAAA,SAAM;QAEd,2BAA2B;QAC3B,mBAAmB;QACnB,aAAa;QACb,UAAU;QACV,yBAAyB,+JAAA,CAAA,UAAO;QAChC,uBAAuB,+JAAA,CAAA,UAAO;QAC9B,QAAQ;QACR,UAAU;QACV,SAAS,+JAAA,CAAA,SAAM;QACf,UAAU;QACV,cAAc;IAChB;IACA,OAAO;IACP,WAAW,0LAAA,CAAA,2BAAwB;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 737, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/property-information/lib/svg.js"], "sourcesContent": ["import {caseSensitiveTransform} from './util/case-sensitive-transform.js'\nimport {create} from './util/create.js'\nimport {\n  boolean,\n  commaOrSpaceSeparated,\n  commaSeparated,\n  number,\n  spaceSeparated\n} from './util/types.js'\n\nexport const svg = create({\n  attributes: {\n    accentHeight: 'accent-height',\n    alignmentBaseline: 'alignment-baseline',\n    arabicForm: 'arabic-form',\n    baselineShift: 'baseline-shift',\n    capHeight: 'cap-height',\n    className: 'class',\n    clipPath: 'clip-path',\n    clipRule: 'clip-rule',\n    colorInterpolation: 'color-interpolation',\n    colorInterpolationFilters: 'color-interpolation-filters',\n    colorProfile: 'color-profile',\n    colorRendering: 'color-rendering',\n    crossOrigin: 'crossorigin',\n    dataType: 'datatype',\n    dominantBaseline: 'dominant-baseline',\n    enableBackground: 'enable-background',\n    fillOpacity: 'fill-opacity',\n    fillRule: 'fill-rule',\n    floodColor: 'flood-color',\n    floodOpacity: 'flood-opacity',\n    fontFamily: 'font-family',\n    fontSize: 'font-size',\n    fontSizeAdjust: 'font-size-adjust',\n    fontStretch: 'font-stretch',\n    fontStyle: 'font-style',\n    fontVariant: 'font-variant',\n    fontWeight: 'font-weight',\n    glyphName: 'glyph-name',\n    glyphOrientationHorizontal: 'glyph-orientation-horizontal',\n    glyphOrientationVertical: 'glyph-orientation-vertical',\n    hrefLang: 'hreflang',\n    horizAdvX: 'horiz-adv-x',\n    horizOriginX: 'horiz-origin-x',\n    horizOriginY: 'horiz-origin-y',\n    imageRendering: 'image-rendering',\n    letterSpacing: 'letter-spacing',\n    lightingColor: 'lighting-color',\n    markerEnd: 'marker-end',\n    markerMid: 'marker-mid',\n    markerStart: 'marker-start',\n    navDown: 'nav-down',\n    navDownLeft: 'nav-down-left',\n    navDownRight: 'nav-down-right',\n    navLeft: 'nav-left',\n    navNext: 'nav-next',\n    navPrev: 'nav-prev',\n    navRight: 'nav-right',\n    navUp: 'nav-up',\n    navUpLeft: 'nav-up-left',\n    navUpRight: 'nav-up-right',\n    onAbort: 'onabort',\n    onActivate: 'onactivate',\n    onAfterPrint: 'onafterprint',\n    onBeforePrint: 'onbeforeprint',\n    onBegin: 'onbegin',\n    onCancel: 'oncancel',\n    onCanPlay: 'oncanplay',\n    onCanPlayThrough: 'oncanplaythrough',\n    onChange: 'onchange',\n    onClick: 'onclick',\n    onClose: 'onclose',\n    onCopy: 'oncopy',\n    onCueChange: 'oncuechange',\n    onCut: 'oncut',\n    onDblClick: 'ondblclick',\n    onDrag: 'ondrag',\n    onDragEnd: 'ondragend',\n    onDragEnter: 'ondragenter',\n    onDragExit: 'ondragexit',\n    onDragLeave: 'ondragleave',\n    onDragOver: 'ondragover',\n    onDragStart: 'ondragstart',\n    onDrop: 'ondrop',\n    onDurationChange: 'ondurationchange',\n    onEmptied: 'onemptied',\n    onEnd: 'onend',\n    onEnded: 'onended',\n    onError: 'onerror',\n    onFocus: 'onfocus',\n    onFocusIn: 'onfocusin',\n    onFocusOut: 'onfocusout',\n    onHashChange: 'onhashchange',\n    onInput: 'oninput',\n    onInvalid: 'oninvalid',\n    onKeyDown: 'onkeydown',\n    onKeyPress: 'onkeypress',\n    onKeyUp: 'onkeyup',\n    onLoad: 'onload',\n    onLoadedData: 'onloadeddata',\n    onLoadedMetadata: 'onloadedmetadata',\n    onLoadStart: 'onloadstart',\n    onMessage: 'onmessage',\n    onMouseDown: 'onmousedown',\n    onMouseEnter: 'onmouseenter',\n    onMouseLeave: 'onmouseleave',\n    onMouseMove: 'onmousemove',\n    onMouseOut: 'onmouseout',\n    onMouseOver: 'onmouseover',\n    onMouseUp: 'onmouseup',\n    onMouseWheel: 'onmousewheel',\n    onOffline: 'onoffline',\n    onOnline: 'ononline',\n    onPageHide: 'onpagehide',\n    onPageShow: 'onpageshow',\n    onPaste: 'onpaste',\n    onPause: 'onpause',\n    onPlay: 'onplay',\n    onPlaying: 'onplaying',\n    onPopState: 'onpopstate',\n    onProgress: 'onprogress',\n    onRateChange: 'onratechange',\n    onRepeat: 'onrepeat',\n    onReset: 'onreset',\n    onResize: 'onresize',\n    onScroll: 'onscroll',\n    onSeeked: 'onseeked',\n    onSeeking: 'onseeking',\n    onSelect: 'onselect',\n    onShow: 'onshow',\n    onStalled: 'onstalled',\n    onStorage: 'onstorage',\n    onSubmit: 'onsubmit',\n    onSuspend: 'onsuspend',\n    onTimeUpdate: 'ontimeupdate',\n    onToggle: 'ontoggle',\n    onUnload: 'onunload',\n    onVolumeChange: 'onvolumechange',\n    onWaiting: 'onwaiting',\n    onZoom: 'onzoom',\n    overlinePosition: 'overline-position',\n    overlineThickness: 'overline-thickness',\n    paintOrder: 'paint-order',\n    panose1: 'panose-1',\n    pointerEvents: 'pointer-events',\n    referrerPolicy: 'referrerpolicy',\n    renderingIntent: 'rendering-intent',\n    shapeRendering: 'shape-rendering',\n    stopColor: 'stop-color',\n    stopOpacity: 'stop-opacity',\n    strikethroughPosition: 'strikethrough-position',\n    strikethroughThickness: 'strikethrough-thickness',\n    strokeDashArray: 'stroke-dasharray',\n    strokeDashOffset: 'stroke-dashoffset',\n    strokeLineCap: 'stroke-linecap',\n    strokeLineJoin: 'stroke-linejoin',\n    strokeMiterLimit: 'stroke-miterlimit',\n    strokeOpacity: 'stroke-opacity',\n    strokeWidth: 'stroke-width',\n    tabIndex: 'tabindex',\n    textAnchor: 'text-anchor',\n    textDecoration: 'text-decoration',\n    textRendering: 'text-rendering',\n    transformOrigin: 'transform-origin',\n    typeOf: 'typeof',\n    underlinePosition: 'underline-position',\n    underlineThickness: 'underline-thickness',\n    unicodeBidi: 'unicode-bidi',\n    unicodeRange: 'unicode-range',\n    unitsPerEm: 'units-per-em',\n    vAlphabetic: 'v-alphabetic',\n    vHanging: 'v-hanging',\n    vIdeographic: 'v-ideographic',\n    vMathematical: 'v-mathematical',\n    vectorEffect: 'vector-effect',\n    vertAdvY: 'vert-adv-y',\n    vertOriginX: 'vert-origin-x',\n    vertOriginY: 'vert-origin-y',\n    wordSpacing: 'word-spacing',\n    writingMode: 'writing-mode',\n    xHeight: 'x-height',\n    // These were camelcased in Tiny. Now lowercased in SVG 2\n    playbackOrder: 'playbackorder',\n    timelineBegin: 'timelinebegin'\n  },\n  properties: {\n    about: commaOrSpaceSeparated,\n    accentHeight: number,\n    accumulate: null,\n    additive: null,\n    alignmentBaseline: null,\n    alphabetic: number,\n    amplitude: number,\n    arabicForm: null,\n    ascent: number,\n    attributeName: null,\n    attributeType: null,\n    azimuth: number,\n    bandwidth: null,\n    baselineShift: null,\n    baseFrequency: null,\n    baseProfile: null,\n    bbox: null,\n    begin: null,\n    bias: number,\n    by: null,\n    calcMode: null,\n    capHeight: number,\n    className: spaceSeparated,\n    clip: null,\n    clipPath: null,\n    clipPathUnits: null,\n    clipRule: null,\n    color: null,\n    colorInterpolation: null,\n    colorInterpolationFilters: null,\n    colorProfile: null,\n    colorRendering: null,\n    content: null,\n    contentScriptType: null,\n    contentStyleType: null,\n    crossOrigin: null,\n    cursor: null,\n    cx: null,\n    cy: null,\n    d: null,\n    dataType: null,\n    defaultAction: null,\n    descent: number,\n    diffuseConstant: number,\n    direction: null,\n    display: null,\n    dur: null,\n    divisor: number,\n    dominantBaseline: null,\n    download: boolean,\n    dx: null,\n    dy: null,\n    edgeMode: null,\n    editable: null,\n    elevation: number,\n    enableBackground: null,\n    end: null,\n    event: null,\n    exponent: number,\n    externalResourcesRequired: null,\n    fill: null,\n    fillOpacity: number,\n    fillRule: null,\n    filter: null,\n    filterRes: null,\n    filterUnits: null,\n    floodColor: null,\n    floodOpacity: null,\n    focusable: null,\n    focusHighlight: null,\n    fontFamily: null,\n    fontSize: null,\n    fontSizeAdjust: null,\n    fontStretch: null,\n    fontStyle: null,\n    fontVariant: null,\n    fontWeight: null,\n    format: null,\n    fr: null,\n    from: null,\n    fx: null,\n    fy: null,\n    g1: commaSeparated,\n    g2: commaSeparated,\n    glyphName: commaSeparated,\n    glyphOrientationHorizontal: null,\n    glyphOrientationVertical: null,\n    glyphRef: null,\n    gradientTransform: null,\n    gradientUnits: null,\n    handler: null,\n    hanging: number,\n    hatchContentUnits: null,\n    hatchUnits: null,\n    height: null,\n    href: null,\n    hrefLang: null,\n    horizAdvX: number,\n    horizOriginX: number,\n    horizOriginY: number,\n    id: null,\n    ideographic: number,\n    imageRendering: null,\n    initialVisibility: null,\n    in: null,\n    in2: null,\n    intercept: number,\n    k: number,\n    k1: number,\n    k2: number,\n    k3: number,\n    k4: number,\n    kernelMatrix: commaOrSpaceSeparated,\n    kernelUnitLength: null,\n    keyPoints: null, // SEMI_COLON_SEPARATED\n    keySplines: null, // SEMI_COLON_SEPARATED\n    keyTimes: null, // SEMI_COLON_SEPARATED\n    kerning: null,\n    lang: null,\n    lengthAdjust: null,\n    letterSpacing: null,\n    lightingColor: null,\n    limitingConeAngle: number,\n    local: null,\n    markerEnd: null,\n    markerMid: null,\n    markerStart: null,\n    markerHeight: null,\n    markerUnits: null,\n    markerWidth: null,\n    mask: null,\n    maskContentUnits: null,\n    maskUnits: null,\n    mathematical: null,\n    max: null,\n    media: null,\n    mediaCharacterEncoding: null,\n    mediaContentEncodings: null,\n    mediaSize: number,\n    mediaTime: null,\n    method: null,\n    min: null,\n    mode: null,\n    name: null,\n    navDown: null,\n    navDownLeft: null,\n    navDownRight: null,\n    navLeft: null,\n    navNext: null,\n    navPrev: null,\n    navRight: null,\n    navUp: null,\n    navUpLeft: null,\n    navUpRight: null,\n    numOctaves: null,\n    observer: null,\n    offset: null,\n    onAbort: null,\n    onActivate: null,\n    onAfterPrint: null,\n    onBeforePrint: null,\n    onBegin: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnd: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFocusIn: null,\n    onFocusOut: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onMouseWheel: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRepeat: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onShow: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onZoom: null,\n    opacity: null,\n    operator: null,\n    order: null,\n    orient: null,\n    orientation: null,\n    origin: null,\n    overflow: null,\n    overlay: null,\n    overlinePosition: number,\n    overlineThickness: number,\n    paintOrder: null,\n    panose1: null,\n    path: null,\n    pathLength: number,\n    patternContentUnits: null,\n    patternTransform: null,\n    patternUnits: null,\n    phase: null,\n    ping: spaceSeparated,\n    pitch: null,\n    playbackOrder: null,\n    pointerEvents: null,\n    points: null,\n    pointsAtX: number,\n    pointsAtY: number,\n    pointsAtZ: number,\n    preserveAlpha: null,\n    preserveAspectRatio: null,\n    primitiveUnits: null,\n    propagate: null,\n    property: commaOrSpaceSeparated,\n    r: null,\n    radius: null,\n    referrerPolicy: null,\n    refX: null,\n    refY: null,\n    rel: commaOrSpaceSeparated,\n    rev: commaOrSpaceSeparated,\n    renderingIntent: null,\n    repeatCount: null,\n    repeatDur: null,\n    requiredExtensions: commaOrSpaceSeparated,\n    requiredFeatures: commaOrSpaceSeparated,\n    requiredFonts: commaOrSpaceSeparated,\n    requiredFormats: commaOrSpaceSeparated,\n    resource: null,\n    restart: null,\n    result: null,\n    rotate: null,\n    rx: null,\n    ry: null,\n    scale: null,\n    seed: null,\n    shapeRendering: null,\n    side: null,\n    slope: null,\n    snapshotTime: null,\n    specularConstant: number,\n    specularExponent: number,\n    spreadMethod: null,\n    spacing: null,\n    startOffset: null,\n    stdDeviation: null,\n    stemh: null,\n    stemv: null,\n    stitchTiles: null,\n    stopColor: null,\n    stopOpacity: null,\n    strikethroughPosition: number,\n    strikethroughThickness: number,\n    string: null,\n    stroke: null,\n    strokeDashArray: commaOrSpaceSeparated,\n    strokeDashOffset: null,\n    strokeLineCap: null,\n    strokeLineJoin: null,\n    strokeMiterLimit: number,\n    strokeOpacity: number,\n    strokeWidth: null,\n    style: null,\n    surfaceScale: number,\n    syncBehavior: null,\n    syncBehaviorDefault: null,\n    syncMaster: null,\n    syncTolerance: null,\n    syncToleranceDefault: null,\n    systemLanguage: commaOrSpaceSeparated,\n    tabIndex: number,\n    tableValues: null,\n    target: null,\n    targetX: number,\n    targetY: number,\n    textAnchor: null,\n    textDecoration: null,\n    textRendering: null,\n    textLength: null,\n    timelineBegin: null,\n    title: null,\n    transformBehavior: null,\n    type: null,\n    typeOf: commaOrSpaceSeparated,\n    to: null,\n    transform: null,\n    transformOrigin: null,\n    u1: null,\n    u2: null,\n    underlinePosition: number,\n    underlineThickness: number,\n    unicode: null,\n    unicodeBidi: null,\n    unicodeRange: null,\n    unitsPerEm: number,\n    values: null,\n    vAlphabetic: number,\n    vMathematical: number,\n    vectorEffect: null,\n    vHanging: number,\n    vIdeographic: number,\n    version: null,\n    vertAdvY: number,\n    vertOriginX: number,\n    vertOriginY: number,\n    viewBox: null,\n    viewTarget: null,\n    visibility: null,\n    width: null,\n    widths: null,\n    wordSpacing: null,\n    writingMode: null,\n    x: null,\n    x1: null,\n    x2: null,\n    xChannelSelector: null,\n    xHeight: number,\n    y: null,\n    y1: null,\n    y2: null,\n    yChannelSelector: null,\n    z: null,\n    zoomAndPan: null\n  },\n  space: 'svg',\n  transform: caseSensitiveTransform\n})\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAQO,MAAM,MAAM,CAAA,GAAA,gKAAA,CAAA,SAAM,AAAD,EAAE;IACxB,YAAY;QACV,cAAc;QACd,mBAAmB;QACnB,YAAY;QACZ,eAAe;QACf,WAAW;QACX,WAAW;QACX,UAAU;QACV,UAAU;QACV,oBAAoB;QACpB,2BAA2B;QAC3B,cAAc;QACd,gBAAgB;QAChB,aAAa;QACb,UAAU;QACV,kBAAkB;QAClB,kBAAkB;QAClB,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;QACd,YAAY;QACZ,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,WAAW;QACX,4BAA4B;QAC5B,0BAA0B;QAC1B,UAAU;QACV,WAAW;QACX,cAAc;QACd,cAAc;QACd,gBAAgB;QAChB,eAAe;QACf,eAAe;QACf,WAAW;QACX,WAAW;QACX,aAAa;QACb,SAAS;QACT,aAAa;QACb,cAAc;QACd,SAAS;QACT,SAAS;QACT,SAAS;QACT,UAAU;QACV,OAAO;QACP,WAAW;QACX,YAAY;QACZ,SAAS;QACT,YAAY;QACZ,cAAc;QACd,eAAe;QACf,SAAS;QACT,UAAU;QACV,WAAW;QACX,kBAAkB;QAClB,UAAU;QACV,SAAS;QACT,SAAS;QACT,QAAQ;QACR,aAAa;QACb,OAAO;QACP,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,aAAa;QACb,YAAY;QACZ,aAAa;QACb,YAAY;QACZ,aAAa;QACb,QAAQ;QACR,kBAAkB;QAClB,WAAW;QACX,OAAO;QACP,SAAS;QACT,SAAS;QACT,SAAS;QACT,WAAW;QACX,YAAY;QACZ,cAAc;QACd,SAAS;QACT,WAAW;QACX,WAAW;QACX,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,cAAc;QACd,kBAAkB;QAClB,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,cAAc;QACd,aAAa;QACb,YAAY;QACZ,aAAa;QACb,WAAW;QACX,cAAc;QACd,WAAW;QACX,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,SAAS;QACT,SAAS;QACT,QAAQ;QACR,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,UAAU;QACV,SAAS;QACT,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QACX,UAAU;QACV,QAAQ;QACR,WAAW;QACX,WAAW;QACX,UAAU;QACV,WAAW;QACX,cAAc;QACd,UAAU;QACV,UAAU;QACV,gBAAgB;QAChB,WAAW;QACX,QAAQ;QACR,kBAAkB;QAClB,mBAAmB;QACnB,YAAY;QACZ,SAAS;QACT,eAAe;QACf,gBAAgB;QAChB,iBAAiB;QACjB,gBAAgB;QAChB,WAAW;QACX,aAAa;QACb,uBAAuB;QACvB,wBAAwB;QACxB,iBAAiB;QACjB,kBAAkB;QAClB,eAAe;QACf,gBAAgB;QAChB,kBAAkB;QAClB,eAAe;QACf,aAAa;QACb,UAAU;QACV,YAAY;QACZ,gBAAgB;QAChB,eAAe;QACf,iBAAiB;QACjB,QAAQ;QACR,mBAAmB;QACnB,oBAAoB;QACpB,aAAa;QACb,cAAc;QACd,YAAY;QACZ,aAAa;QACb,UAAU;QACV,cAAc;QACd,eAAe;QACf,cAAc;QACd,UAAU;QACV,aAAa;QACb,aAAa;QACb,aAAa;QACb,aAAa;QACb,SAAS;QACT,yDAAyD;QACzD,eAAe;QACf,eAAe;IACjB;IACA,YAAY;QACV,OAAO,+JAAA,CAAA,wBAAqB;QAC5B,cAAc,+JAAA,CAAA,SAAM;QACpB,YAAY;QACZ,UAAU;QACV,mBAAmB;QACnB,YAAY,+JAAA,CAAA,SAAM;QAClB,WAAW,+JAAA,CAAA,SAAM;QACjB,YAAY;QACZ,QAAQ,+JAAA,CAAA,SAAM;QACd,eAAe;QACf,eAAe;QACf,SAAS,+JAAA,CAAA,SAAM;QACf,WAAW;QACX,eAAe;QACf,eAAe;QACf,aAAa;QACb,MAAM;QACN,OAAO;QACP,MAAM,+JAAA,CAAA,SAAM;QACZ,IAAI;QACJ,UAAU;QACV,WAAW,+JAAA,CAAA,SAAM;QACjB,WAAW,+JAAA,CAAA,iBAAc;QACzB,MAAM;QACN,UAAU;QACV,eAAe;QACf,UAAU;QACV,OAAO;QACP,oBAAoB;QACpB,2BAA2B;QAC3B,cAAc;QACd,gBAAgB;QAChB,SAAS;QACT,mBAAmB;QACnB,kBAAkB;QAClB,aAAa;QACb,QAAQ;QACR,IAAI;QACJ,IAAI;QACJ,GAAG;QACH,UAAU;QACV,eAAe;QACf,SAAS,+JAAA,CAAA,SAAM;QACf,iBAAiB,+JAAA,CAAA,SAAM;QACvB,WAAW;QACX,SAAS;QACT,KAAK;QACL,SAAS,+JAAA,CAAA,SAAM;QACf,kBAAkB;QAClB,UAAU,+JAAA,CAAA,UAAO;QACjB,IAAI;QACJ,IAAI;QACJ,UAAU;QACV,UAAU;QACV,WAAW,+JAAA,CAAA,SAAM;QACjB,kBAAkB;QAClB,KAAK;QACL,OAAO;QACP,UAAU,+JAAA,CAAA,SAAM;QAChB,2BAA2B;QAC3B,MAAM;QACN,aAAa,+JAAA,CAAA,SAAM;QACnB,UAAU;QACV,QAAQ;QACR,WAAW;QACX,aAAa;QACb,YAAY;QACZ,cAAc;QACd,WAAW;QACX,gBAAgB;QAChB,YAAY;QACZ,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,QAAQ;QACR,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI,+JAAA,CAAA,iBAAc;QAClB,IAAI,+JAAA,CAAA,iBAAc;QAClB,WAAW,+JAAA,CAAA,iBAAc;QACzB,4BAA4B;QAC5B,0BAA0B;QAC1B,UAAU;QACV,mBAAmB;QACnB,eAAe;QACf,SAAS;QACT,SAAS,+JAAA,CAAA,SAAM;QACf,mBAAmB;QACnB,YAAY;QACZ,QAAQ;QACR,MAAM;QACN,UAAU;QACV,WAAW,+JAAA,CAAA,SAAM;QACjB,cAAc,+JAAA,CAAA,SAAM;QACpB,cAAc,+JAAA,CAAA,SAAM;QACpB,IAAI;QACJ,aAAa,+JAAA,CAAA,SAAM;QACnB,gBAAgB;QAChB,mBAAmB;QACnB,IAAI;QACJ,KAAK;QACL,WAAW,+JAAA,CAAA,SAAM;QACjB,GAAG,+JAAA,CAAA,SAAM;QACT,IAAI,+JAAA,CAAA,SAAM;QACV,IAAI,+JAAA,CAAA,SAAM;QACV,IAAI,+JAAA,CAAA,SAAM;QACV,IAAI,+JAAA,CAAA,SAAM;QACV,cAAc,+JAAA,CAAA,wBAAqB;QACnC,kBAAkB;QAClB,WAAW;QACX,YAAY;QACZ,UAAU;QACV,SAAS;QACT,MAAM;QACN,cAAc;QACd,eAAe;QACf,eAAe;QACf,mBAAmB,+JAAA,CAAA,SAAM;QACzB,OAAO;QACP,WAAW;QACX,WAAW;QACX,aAAa;QACb,cAAc;QACd,aAAa;QACb,aAAa;QACb,MAAM;QACN,kBAAkB;QAClB,WAAW;QACX,cAAc;QACd,KAAK;QACL,OAAO;QACP,wBAAwB;QACxB,uBAAuB;QACvB,WAAW,+JAAA,CAAA,SAAM;QACjB,WAAW;QACX,QAAQ;QACR,KAAK;QACL,MAAM;QACN,MAAM;QACN,SAAS;QACT,aAAa;QACb,cAAc;QACd,SAAS;QACT,SAAS;QACT,SAAS;QACT,UAAU;QACV,OAAO;QACP,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,YAAY;QACZ,cAAc;QACd,eAAe;QACf,SAAS;QACT,UAAU;QACV,WAAW;QACX,kBAAkB;QAClB,UAAU;QACV,SAAS;QACT,SAAS;QACT,QAAQ;QACR,aAAa;QACb,OAAO;QACP,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,aAAa;QACb,YAAY;QACZ,aAAa;QACb,YAAY;QACZ,aAAa;QACb,QAAQ;QACR,kBAAkB;QAClB,WAAW;QACX,OAAO;QACP,SAAS;QACT,SAAS;QACT,SAAS;QACT,WAAW;QACX,YAAY;QACZ,cAAc;QACd,SAAS;QACT,WAAW;QACX,WAAW;QACX,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,cAAc;QACd,kBAAkB;QAClB,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,cAAc;QACd,aAAa;QACb,YAAY;QACZ,aAAa;QACb,WAAW;QACX,cAAc;QACd,WAAW;QACX,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,SAAS;QACT,SAAS;QACT,QAAQ;QACR,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,UAAU;QACV,SAAS;QACT,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QACX,UAAU;QACV,QAAQ;QACR,WAAW;QACX,WAAW;QACX,UAAU;QACV,WAAW;QACX,cAAc;QACd,UAAU;QACV,UAAU;QACV,gBAAgB;QAChB,WAAW;QACX,QAAQ;QACR,SAAS;QACT,UAAU;QACV,OAAO;QACP,QAAQ;QACR,aAAa;QACb,QAAQ;QACR,UAAU;QACV,SAAS;QACT,kBAAkB,+JAAA,CAAA,SAAM;QACxB,mBAAmB,+JAAA,CAAA,SAAM;QACzB,YAAY;QACZ,SAAS;QACT,MAAM;QACN,YAAY,+JAAA,CAAA,SAAM;QAClB,qBAAqB;QACrB,kBAAkB;QAClB,cAAc;QACd,OAAO;QACP,MAAM,+JAAA,CAAA,iBAAc;QACpB,OAAO;QACP,eAAe;QACf,eAAe;QACf,QAAQ;QACR,WAAW,+JAAA,CAAA,SAAM;QACjB,WAAW,+JAAA,CAAA,SAAM;QACjB,WAAW,+JAAA,CAAA,SAAM;QACjB,eAAe;QACf,qBAAqB;QACrB,gBAAgB;QAChB,WAAW;QACX,UAAU,+JAAA,CAAA,wBAAqB;QAC/B,GAAG;QACH,QAAQ;QACR,gBAAgB;QAChB,MAAM;QACN,MAAM;QACN,KAAK,+JAAA,CAAA,wBAAqB;QAC1B,KAAK,+JAAA,CAAA,wBAAqB;QAC1B,iBAAiB;QACjB,aAAa;QACb,WAAW;QACX,oBAAoB,+JAAA,CAAA,wBAAqB;QACzC,kBAAkB,+JAAA,CAAA,wBAAqB;QACvC,eAAe,+JAAA,CAAA,wBAAqB;QACpC,iBAAiB,+JAAA,CAAA,wBAAqB;QACtC,UAAU;QACV,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,IAAI;QACJ,IAAI;QACJ,OAAO;QACP,MAAM;QACN,gBAAgB;QAChB,MAAM;QACN,OAAO;QACP,cAAc;QACd,kBAAkB,+JAAA,CAAA,SAAM;QACxB,kBAAkB,+JAAA,CAAA,SAAM;QACxB,cAAc;QACd,SAAS;QACT,aAAa;QACb,cAAc;QACd,OAAO;QACP,OAAO;QACP,aAAa;QACb,WAAW;QACX,aAAa;QACb,uBAAuB,+JAAA,CAAA,SAAM;QAC7B,wBAAwB,+JAAA,CAAA,SAAM;QAC9B,QAAQ;QACR,QAAQ;QACR,iBAAiB,+JAAA,CAAA,wBAAqB;QACtC,kBAAkB;QAClB,eAAe;QACf,gBAAgB;QAChB,kBAAkB,+JAAA,CAAA,SAAM;QACxB,eAAe,+JAAA,CAAA,SAAM;QACrB,aAAa;QACb,OAAO;QACP,cAAc,+JAAA,CAAA,SAAM;QACpB,cAAc;QACd,qBAAqB;QACrB,YAAY;QACZ,eAAe;QACf,sBAAsB;QACtB,gBAAgB,+JAAA,CAAA,wBAAqB;QACrC,UAAU,+JAAA,CAAA,SAAM;QAChB,aAAa;QACb,QAAQ;QACR,SAAS,+JAAA,CAAA,SAAM;QACf,SAAS,+JAAA,CAAA,SAAM;QACf,YAAY;QACZ,gBAAgB;QAChB,eAAe;QACf,YAAY;QACZ,eAAe;QACf,OAAO;QACP,mBAAmB;QACnB,MAAM;QACN,QAAQ,+JAAA,CAAA,wBAAqB;QAC7B,IAAI;QACJ,WAAW;QACX,iBAAiB;QACjB,IAAI;QACJ,IAAI;QACJ,mBAAmB,+JAAA,CAAA,SAAM;QACzB,oBAAoB,+JAAA,CAAA,SAAM;QAC1B,SAAS;QACT,aAAa;QACb,cAAc;QACd,YAAY,+JAAA,CAAA,SAAM;QAClB,QAAQ;QACR,aAAa,+JAAA,CAAA,SAAM;QACnB,eAAe,+JAAA,CAAA,SAAM;QACrB,cAAc;QACd,UAAU,+JAAA,CAAA,SAAM;QAChB,cAAc,+JAAA,CAAA,SAAM;QACpB,SAAS;QACT,UAAU,+JAAA,CAAA,SAAM;QAChB,aAAa,+JAAA,CAAA,SAAM;QACnB,aAAa,+JAAA,CAAA,SAAM;QACnB,SAAS;QACT,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,QAAQ;QACR,aAAa;QACb,aAAa;QACb,GAAG;QACH,IAAI;QACJ,IAAI;QACJ,kBAAkB;QAClB,SAAS,+JAAA,CAAA,SAAM;QACf,GAAG;QACH,IAAI;QACJ,IAAI;QACJ,kBAAkB;QAClB,GAAG;QACH,YAAY;IACd;IACA,OAAO;IACP,WAAW,wLAAA,CAAA,yBAAsB;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1309, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/property-information/lib/xlink.js"], "sourcesContent": ["import {create} from './util/create.js'\n\nexport const xlink = create({\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  },\n  space: 'xlink',\n  transform(_, property) {\n    return 'xlink:' + property.slice(5).toLowerCase()\n  }\n})\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,QAAQ,CAAA,GAAA,gKAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,YAAY;QACV,cAAc;QACd,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,WAAW;IACb;IACA,OAAO;IACP,WAAU,CAAC,EAAE,QAAQ;QACnB,OAAO,WAAW,SAAS,KAAK,CAAC,GAAG,WAAW;IACjD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1335, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/property-information/lib/xmlns.js"], "sourcesContent": ["import {create} from './util/create.js'\nimport {caseInsensitiveTransform} from './util/case-insensitive-transform.js'\n\nexport const xmlns = create({\n  attributes: {xmlnsxlink: 'xmlns:xlink'},\n  properties: {xmlnsXLink: null, xmlns: null},\n  space: 'xmlns',\n  transform: caseInsensitiveTransform\n})\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,QAAQ,CAAA,GAAA,gKAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,YAAY;QAAC,YAAY;IAAa;IACtC,YAAY;QAAC,YAAY;QAAM,OAAO;IAAI;IAC1C,OAAO;IACP,WAAW,0LAAA,CAAA,2BAAwB;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1359, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/property-information/lib/xml.js"], "sourcesContent": ["import {create} from './util/create.js'\n\nexport const xml = create({\n  properties: {xmlBase: null, xmlLang: null, xmlSpace: null},\n  space: 'xml',\n  transform(_, property) {\n    return 'xml:' + property.slice(3).toLowerCase()\n  }\n})\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,MAAM,CAAA,GAAA,gKAAA,CAAA,SAAM,AAAD,EAAE;IACxB,YAAY;QAAC,SAAS;QAAM,SAAS;QAAM,UAAU;IAAI;IACzD,OAAO;IACP,WAAU,CAAC,EAAE,QAAQ;QACnB,OAAO,SAAS,SAAS,KAAK,CAAC,GAAG,WAAW;IAC/C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1381, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/property-information/index.js"], "sourcesContent": ["// Note: types exposed from `index.d.ts`.\nimport {merge} from './lib/util/merge.js'\nimport {aria} from './lib/aria.js'\nimport {html as htmlBase} from './lib/html.js'\nimport {svg as svgBase} from './lib/svg.js'\nimport {xlink} from './lib/xlink.js'\nimport {xmlns} from './lib/xmlns.js'\nimport {xml} from './lib/xml.js'\n\nexport {hastToReact} from './lib/hast-to-react.js'\n\nexport const html = merge([aria, htmlBase, xlink, xmlns, xml], 'html')\n\nexport {find} from './lib/find.js'\nexport {normalize} from './lib/normalize.js'\n\nexport const svg = merge([aria, svgBase, xlink, xmlns, xml], 'svg')\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAIO,MAAM,OAAO,CAAA,GAAA,+JAAA,CAAA,QAAK,AAAD,EAAE;IAAC,sJAAA,CAAA,OAAI;IAAE,sJAAA,CAAA,OAAQ;IAAE,uJAAA,CAAA,QAAK;IAAE,uJAAA,CAAA,QAAK;IAAE,qJAAA,CAAA,MAAG;CAAC,EAAE;;;AAKxD,MAAM,MAAM,CAAA,GAAA,+JAAA,CAAA,QAAK,AAAD,EAAE;IAAC,sJAAA,CAAA,OAAI;IAAE,qJAAA,CAAA,MAAO;IAAE,uJAAA,CAAA,QAAK;IAAE,uJAAA,CAAA,QAAK;IAAE,qJAAA,CAAA,MAAG;CAAC,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1423, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/zwitch/index.js"], "sourcesContent": ["/**\n * @callback Handler\n *   Handle a value, with a certain ID field set to a certain value.\n *   The ID field is passed to `zwitch`, and it’s value is this function’s\n *   place on the `handlers` record.\n * @param {...any} parameters\n *   Arbitrary parameters passed to the zwitch.\n *   The first will be an object with a certain ID field set to a certain value.\n * @returns {any}\n *   Anything!\n */\n\n/**\n * @callback UnknownHandler\n *   Handle values that do have a certain ID field, but it’s set to a value\n *   that is not listed in the `handlers` record.\n * @param {unknown} value\n *   An object with a certain ID field set to an unknown value.\n * @param {...any} rest\n *   Arbitrary parameters passed to the zwitch.\n * @returns {any}\n *   Anything!\n */\n\n/**\n * @callback InvalidHandler\n *   Handle values that do not have a certain ID field.\n * @param {unknown} value\n *   Any unknown value.\n * @param {...any} rest\n *   Arbitrary parameters passed to the zwitch.\n * @returns {void|null|undefined|never}\n *   This should crash or return nothing.\n */\n\n/**\n * @template {InvalidHandler} [Invalid=InvalidHandler]\n * @template {UnknownHandler} [Unknown=UnknownHandler]\n * @template {Record<string, Handler>} [Handlers=Record<string, Handler>]\n * @typedef Options\n *   Configuration (required).\n * @property {Invalid} [invalid]\n *   Handler to use for invalid values.\n * @property {Unknown} [unknown]\n *   Handler to use for unknown values.\n * @property {Handlers} [handlers]\n *   Handlers to use.\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Handle values based on a field.\n *\n * @template {InvalidHandler} [Invalid=InvalidHandler]\n * @template {UnknownHandler} [Unknown=UnknownHandler]\n * @template {Record<string, Handler>} [Handlers=Record<string, Handler>]\n * @param {string} key\n *   Field to switch on.\n * @param {Options<Invalid, Unknown, Handlers>} [options]\n *   Configuration (required).\n * @returns {{unknown: Unknown, invalid: Invalid, handlers: Handlers, (...parameters: Parameters<Handlers[keyof Handlers]>): ReturnType<Handlers[keyof Handlers]>, (...parameters: Parameters<Unknown>): ReturnType<Unknown>}}\n */\nexport function zwitch(key, options) {\n  const settings = options || {}\n\n  /**\n   * Handle one value.\n   *\n   * Based on the bound `key`, a respective handler will be called.\n   * If `value` is not an object, or doesn’t have a `key` property, the special\n   * “invalid” handler will be called.\n   * If `value` has an unknown `key`, the special “unknown” handler will be\n   * called.\n   *\n   * All arguments, and the context object, are passed through to the handler,\n   * and it’s result is returned.\n   *\n   * @this {unknown}\n   *   Any context object.\n   * @param {unknown} [value]\n   *   Any value.\n   * @param {...unknown} parameters\n   *   Arbitrary parameters passed to the zwitch.\n   * @property {Handler} invalid\n   *   Handle for values that do not have a certain ID field.\n   * @property {Handler} unknown\n   *   Handle values that do have a certain ID field, but it’s set to a value\n   *   that is not listed in the `handlers` record.\n   * @property {Handlers} handlers\n   *   Record of handlers.\n   * @returns {unknown}\n   *   Anything.\n   */\n  function one(value, ...parameters) {\n    /** @type {Handler|undefined} */\n    let fn = one.invalid\n    const handlers = one.handlers\n\n    if (value && own.call(value, key)) {\n      // @ts-expect-error Indexable.\n      const id = String(value[key])\n      // @ts-expect-error Indexable.\n      fn = own.call(handlers, id) ? handlers[id] : one.unknown\n    }\n\n    if (fn) {\n      return fn.call(this, value, ...parameters)\n    }\n  }\n\n  one.handlers = settings.handlers || {}\n  one.invalid = settings.invalid\n  one.unknown = settings.unknown\n\n  // @ts-expect-error: matches!\n  return one\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC,GAED;;;;;;;;;;CAUC,GAED;;;;;;;;;CASC,GAED;;;;;;;;;;;;CAYC;;;AAED,MAAM,MAAM,CAAC,EAAE,cAAc;AActB,SAAS,OAAO,GAAG,EAAE,OAAO;IACjC,MAAM,WAAW,WAAW,CAAC;IAE7B;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BC,GACD,SAAS,IAAI,KAAK,EAAE,GAAG,UAAU;QAC/B,8BAA8B,GAC9B,IAAI,KAAK,IAAI,OAAO;QACpB,MAAM,WAAW,IAAI,QAAQ;QAE7B,IAAI,SAAS,IAAI,IAAI,CAAC,OAAO,MAAM;YACjC,8BAA8B;YAC9B,MAAM,KAAK,OAAO,KAAK,CAAC,IAAI;YAC5B,8BAA8B;YAC9B,KAAK,IAAI,IAAI,CAAC,UAAU,MAAM,QAAQ,CAAC,GAAG,GAAG,IAAI,OAAO;QAC1D;QAEA,IAAI,IAAI;YACN,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,UAAU;QACjC;IACF;IAEA,IAAI,QAAQ,GAAG,SAAS,QAAQ,IAAI,CAAC;IACrC,IAAI,OAAO,GAAG,SAAS,OAAO;IAC9B,IAAI,OAAO,GAAG,SAAS,OAAO;IAE9B,6BAA6B;IAC7B,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1522, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/stringify-entities/lib/core.js"], "sourcesContent": ["/**\n * @typedef CoreOptions\n * @property {ReadonlyArray<string>} [subset=[]]\n *   Whether to only escape the given subset of characters.\n * @property {boolean} [escapeOnly=false]\n *   Whether to only escape possibly dangerous characters.\n *   Those characters are `\"`, `&`, `'`, `<`, `>`, and `` ` ``.\n *\n * @typedef FormatOptions\n * @property {(code: number, next: number, options: CoreWithFormatOptions) => string} format\n *   Format strategy.\n *\n * @typedef {CoreOptions & FormatOptions & import('./util/format-smart.js').FormatSmartOptions} CoreWithFormatOptions\n */\n\nconst defaultSubsetRegex = /[\"&'<>`]/g\nconst surrogatePairsRegex = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g\nconst controlCharactersRegex =\n  // eslint-disable-next-line no-control-regex, unicorn/no-hex-escape\n  /[\\x01-\\t\\v\\f\\x0E-\\x1F\\x7F\\x81\\x8D\\x8F\\x90\\x9D\\xA0-\\uFFFF]/g\nconst regexEscapeRegex = /[|\\\\{}()[\\]^$+*?.]/g\n\n/** @type {WeakMap<ReadonlyArray<string>, RegExp>} */\nconst subsetToRegexCache = new WeakMap()\n\n/**\n * Encode certain characters in `value`.\n *\n * @param {string} value\n * @param {CoreWithFormatOptions} options\n * @returns {string}\n */\nexport function core(value, options) {\n  value = value.replace(\n    options.subset\n      ? charactersToExpressionCached(options.subset)\n      : defaultSubsetRegex,\n    basic\n  )\n\n  if (options.subset || options.escapeOnly) {\n    return value\n  }\n\n  return (\n    value\n      // Surrogate pairs.\n      .replace(surrogatePairsRegex, surrogate)\n      // BMP control characters (C0 except for LF, CR, SP; DEL; and some more\n      // non-ASCII ones).\n      .replace(controlCharactersRegex, basic)\n  )\n\n  /**\n   * @param {string} pair\n   * @param {number} index\n   * @param {string} all\n   */\n  function surrogate(pair, index, all) {\n    return options.format(\n      (pair.charCodeAt(0) - 0xd800) * 0x400 +\n        pair.charCodeAt(1) -\n        0xdc00 +\n        0x10000,\n      all.charCodeAt(index + 2),\n      options\n    )\n  }\n\n  /**\n   * @param {string} character\n   * @param {number} index\n   * @param {string} all\n   */\n  function basic(character, index, all) {\n    return options.format(\n      character.charCodeAt(0),\n      all.charCodeAt(index + 1),\n      options\n    )\n  }\n}\n\n/**\n * A wrapper function that caches the result of `charactersToExpression` with a WeakMap.\n * This can improve performance when tooling calls `charactersToExpression` repeatedly\n * with the same subset.\n *\n * @param {ReadonlyArray<string>} subset\n * @returns {RegExp}\n */\nfunction charactersToExpressionCached(subset) {\n  let cached = subsetToRegexCache.get(subset)\n\n  if (!cached) {\n    cached = charactersToExpression(subset)\n    subsetToRegexCache.set(subset, cached)\n  }\n\n  return cached\n}\n\n/**\n * @param {ReadonlyArray<string>} subset\n * @returns {RegExp}\n */\nfunction charactersToExpression(subset) {\n  /** @type {Array<string>} */\n  const groups = []\n  let index = -1\n\n  while (++index < subset.length) {\n    groups.push(subset[index].replace(regexEscapeRegex, '\\\\$&'))\n  }\n\n  return new RegExp('(?:' + groups.join('|') + ')', 'g')\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;CAaC;;;AAED,MAAM,qBAAqB;AAC3B,MAAM,sBAAsB;AAC5B,MAAM,yBACJ,mEAAmE;AACnE;AACF,MAAM,mBAAmB;AAEzB,mDAAmD,GACnD,MAAM,qBAAqB,IAAI;AASxB,SAAS,KAAK,KAAK,EAAE,OAAO;IACjC,QAAQ,MAAM,OAAO,CACnB,QAAQ,MAAM,GACV,6BAA6B,QAAQ,MAAM,IAC3C,oBACJ;IAGF,IAAI,QAAQ,MAAM,IAAI,QAAQ,UAAU,EAAE;QACxC,OAAO;IACT;IAEA,OACE,KACE,mBAAmB;KAClB,OAAO,CAAC,qBAAqB,UAC9B,uEAAuE;IACvE,mBAAmB;KAClB,OAAO,CAAC,wBAAwB;;IAGrC;;;;GAIC,GACD,SAAS,UAAU,IAAI,EAAE,KAAK,EAAE,GAAG;QACjC,OAAO,QAAQ,MAAM,CACnB,CAAC,KAAK,UAAU,CAAC,KAAK,MAAM,IAAI,QAC9B,KAAK,UAAU,CAAC,KAChB,SACA,SACF,IAAI,UAAU,CAAC,QAAQ,IACvB;IAEJ;IAEA;;;;GAIC,GACD,SAAS,MAAM,SAAS,EAAE,KAAK,EAAE,GAAG;QAClC,OAAO,QAAQ,MAAM,CACnB,UAAU,UAAU,CAAC,IACrB,IAAI,UAAU,CAAC,QAAQ,IACvB;IAEJ;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,6BAA6B,MAAM;IAC1C,IAAI,SAAS,mBAAmB,GAAG,CAAC;IAEpC,IAAI,CAAC,QAAQ;QACX,SAAS,uBAAuB;QAChC,mBAAmB,GAAG,CAAC,QAAQ;IACjC;IAEA,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,uBAAuB,MAAM;IACpC,0BAA0B,GAC1B,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;QAC9B,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB;IACtD;IAEA,OAAO,IAAI,OAAO,QAAQ,OAAO,IAAI,CAAC,OAAO,KAAK;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1601, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/stringify-entities/lib/util/to-hexadecimal.js"], "sourcesContent": ["const hexadecimalRegex = /[\\dA-Fa-f]/\n\n/**\n * Configurable ways to encode characters as hexadecimal references.\n *\n * @param {number} code\n * @param {number} next\n * @param {boolean|undefined} omit\n * @returns {string}\n */\nexport function toHexadecimal(code, next, omit) {\n  const value = '&#x' + code.toString(16).toUpperCase()\n  return omit && next && !hexadecimalRegex.test(String.fromCharCode(next))\n    ? value\n    : value + ';'\n}\n"], "names": [], "mappings": ";;;AAAA,MAAM,mBAAmB;AAUlB,SAAS,cAAc,IAAI,EAAE,IAAI,EAAE,IAAI;IAC5C,MAAM,QAAQ,QAAQ,KAAK,QAAQ,CAAC,IAAI,WAAW;IACnD,OAAO,QAAQ,QAAQ,CAAC,iBAAiB,IAAI,CAAC,OAAO,YAAY,CAAC,SAC9D,QACA,QAAQ;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1615, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/stringify-entities/lib/util/to-decimal.js"], "sourcesContent": ["const decimalRegex = /\\d/\n\n/**\n * Configurable ways to encode characters as decimal references.\n *\n * @param {number} code\n * @param {number} next\n * @param {boolean|undefined} omit\n * @returns {string}\n */\nexport function toDecimal(code, next, omit) {\n  const value = '&#' + String(code)\n  return omit && next && !decimalRegex.test(String.fromCharCode(next))\n    ? value\n    : value + ';'\n}\n"], "names": [], "mappings": ";;;AAAA,MAAM,eAAe;AAUd,SAAS,UAAU,IAAI,EAAE,IAAI,EAAE,IAAI;IACxC,MAAM,QAAQ,OAAO,OAAO;IAC5B,OAAO,QAAQ,QAAQ,CAAC,aAAa,IAAI,CAAC,OAAO,YAAY,CAAC,SAC1D,QACA,QAAQ;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1629, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/character-entities-legacy/index.js"], "sourcesContent": ["/**\n * List of legacy HTML named character references that don’t need a trailing semicolon.\n *\n * @type {Array<string>}\n */\nexport const characterEntitiesLegacy = [\n  'AElig',\n  'AMP',\n  'Aacute',\n  'Acirc',\n  '<PERSON>grave',\n  'Aring',\n  'Atilde',\n  'Auml',\n  'COPY',\n  'C<PERSON><PERSON>',\n  'ETH',\n  'Eacute',\n  'Ecirc',\n  'Egrave',\n  'Euml',\n  'GT',\n  'Iacute',\n  'Icirc',\n  'Igrave',\n  'Iuml',\n  'LT',\n  'Ntilde',\n  'Oacute',\n  'Ocirc',\n  'Ograve',\n  'Oslash',\n  'Otilde',\n  'Ouml',\n  'QUOT',\n  'REG',\n  'THORN',\n  'Uacute',\n  'Ucirc',\n  'Ugrave',\n  'Uuml',\n  'Yacute',\n  'aacute',\n  'acirc',\n  'acute',\n  'aelig',\n  'agrave',\n  'amp',\n  'aring',\n  'atilde',\n  'auml',\n  'brvbar',\n  'ccedil',\n  'cedil',\n  'cent',\n  'copy',\n  'curren',\n  'deg',\n  'divide',\n  'eacute',\n  'ecirc',\n  'egrave',\n  'eth',\n  'euml',\n  'frac12',\n  'frac14',\n  'frac34',\n  'gt',\n  'iacute',\n  'icirc',\n  'iexcl',\n  'igrave',\n  'iquest',\n  'iuml',\n  'laquo',\n  'lt',\n  'macr',\n  'micro',\n  'middot',\n  'nbsp',\n  'not',\n  'ntilde',\n  'oacute',\n  'ocirc',\n  'ograve',\n  'ordf',\n  'ordm',\n  'oslash',\n  'otilde',\n  'ouml',\n  'para',\n  'plusmn',\n  'pound',\n  'quot',\n  'raquo',\n  'reg',\n  'sect',\n  'shy',\n  'sup1',\n  'sup2',\n  'sup3',\n  'szlig',\n  'thorn',\n  'times',\n  'uacute',\n  'ucirc',\n  'ugrave',\n  'uml',\n  'uuml',\n  'yacute',\n  'yen',\n  'yuml'\n]\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACM,MAAM,0BAA0B;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1750, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/character-entities-html4/index.js"], "sourcesContent": ["/**\n * Map of named character references from HTML 4.\n *\n * @type {Record<string, string>}\n */\nexport const characterEntitiesHtml4 = {\n  nbsp: ' ',\n  iexcl: '¡',\n  cent: '¢',\n  pound: '£',\n  curren: '¤',\n  yen: '¥',\n  brvbar: '¦',\n  sect: '§',\n  uml: '¨',\n  copy: '©',\n  ordf: 'ª',\n  laquo: '«',\n  not: '¬',\n  shy: '­',\n  reg: '®',\n  macr: '¯',\n  deg: '°',\n  plusmn: '±',\n  sup2: '²',\n  sup3: '³',\n  acute: '´',\n  micro: 'µ',\n  para: '¶',\n  middot: '·',\n  cedil: '¸',\n  sup1: '¹',\n  ordm: 'º',\n  raquo: '»',\n  frac14: '¼',\n  frac12: '½',\n  frac34: '¾',\n  iquest: '¿',\n  Agrave: 'À',\n  Aacute: 'Á',\n  Acirc: 'Â',\n  Atilde: 'Ã',\n  Auml: 'Ä',\n  Aring: 'Å',\n  AElig: 'Æ',\n  Ccedil: 'Ç',\n  Egrave: 'È',\n  Eacute: 'É',\n  Ecirc: 'Ê',\n  Euml: 'Ë',\n  Igrave: 'Ì',\n  Iacute: 'Í',\n  Icirc: 'Î',\n  Iuml: 'Ï',\n  ETH: 'Ð',\n  Ntilde: 'Ñ',\n  Ograve: 'Ò',\n  Oacute: 'Ó',\n  Ocirc: 'Ô',\n  Otilde: 'Õ',\n  Ouml: 'Ö',\n  times: '×',\n  Oslash: 'Ø',\n  Ugrave: 'Ù',\n  Uacute: 'Ú',\n  Ucirc: 'Û',\n  Uuml: 'Ü',\n  Yacute: 'Ý',\n  THORN: 'Þ',\n  szlig: 'ß',\n  agrave: 'à',\n  aacute: 'á',\n  acirc: 'â',\n  atilde: 'ã',\n  auml: 'ä',\n  aring: 'å',\n  aelig: 'æ',\n  ccedil: 'ç',\n  egrave: 'è',\n  eacute: 'é',\n  ecirc: 'ê',\n  euml: 'ë',\n  igrave: 'ì',\n  iacute: 'í',\n  icirc: 'î',\n  iuml: 'ï',\n  eth: 'ð',\n  ntilde: 'ñ',\n  ograve: 'ò',\n  oacute: 'ó',\n  ocirc: 'ô',\n  otilde: 'õ',\n  ouml: 'ö',\n  divide: '÷',\n  oslash: 'ø',\n  ugrave: 'ù',\n  uacute: 'ú',\n  ucirc: 'û',\n  uuml: 'ü',\n  yacute: 'ý',\n  thorn: 'þ',\n  yuml: 'ÿ',\n  fnof: 'ƒ',\n  Alpha: 'Α',\n  Beta: 'Β',\n  Gamma: 'Γ',\n  Delta: 'Δ',\n  Epsilon: 'Ε',\n  Zeta: 'Ζ',\n  Eta: 'Η',\n  Theta: 'Θ',\n  Iota: 'Ι',\n  Kappa: 'Κ',\n  Lambda: 'Λ',\n  Mu: 'Μ',\n  Nu: 'Ν',\n  Xi: 'Ξ',\n  Omicron: 'Ο',\n  Pi: 'Π',\n  Rho: 'Ρ',\n  Sigma: 'Σ',\n  Tau: 'Τ',\n  Upsilon: 'Υ',\n  Phi: 'Φ',\n  Chi: 'Χ',\n  Psi: 'Ψ',\n  Omega: 'Ω',\n  alpha: 'α',\n  beta: 'β',\n  gamma: 'γ',\n  delta: 'δ',\n  epsilon: 'ε',\n  zeta: 'ζ',\n  eta: 'η',\n  theta: 'θ',\n  iota: 'ι',\n  kappa: 'κ',\n  lambda: 'λ',\n  mu: 'μ',\n  nu: 'ν',\n  xi: 'ξ',\n  omicron: 'ο',\n  pi: 'π',\n  rho: 'ρ',\n  sigmaf: 'ς',\n  sigma: 'σ',\n  tau: 'τ',\n  upsilon: 'υ',\n  phi: 'φ',\n  chi: 'χ',\n  psi: 'ψ',\n  omega: 'ω',\n  thetasym: 'ϑ',\n  upsih: 'ϒ',\n  piv: 'ϖ',\n  bull: '•',\n  hellip: '…',\n  prime: '′',\n  Prime: '″',\n  oline: '‾',\n  frasl: '⁄',\n  weierp: '℘',\n  image: 'ℑ',\n  real: 'ℜ',\n  trade: '™',\n  alefsym: 'ℵ',\n  larr: '←',\n  uarr: '↑',\n  rarr: '→',\n  darr: '↓',\n  harr: '↔',\n  crarr: '↵',\n  lArr: '⇐',\n  uArr: '⇑',\n  rArr: '⇒',\n  dArr: '⇓',\n  hArr: '⇔',\n  forall: '∀',\n  part: '∂',\n  exist: '∃',\n  empty: '∅',\n  nabla: '∇',\n  isin: '∈',\n  notin: '∉',\n  ni: '∋',\n  prod: '∏',\n  sum: '∑',\n  minus: '−',\n  lowast: '∗',\n  radic: '√',\n  prop: '∝',\n  infin: '∞',\n  ang: '∠',\n  and: '∧',\n  or: '∨',\n  cap: '∩',\n  cup: '∪',\n  int: '∫',\n  there4: '∴',\n  sim: '∼',\n  cong: '≅',\n  asymp: '≈',\n  ne: '≠',\n  equiv: '≡',\n  le: '≤',\n  ge: '≥',\n  sub: '⊂',\n  sup: '⊃',\n  nsub: '⊄',\n  sube: '⊆',\n  supe: '⊇',\n  oplus: '⊕',\n  otimes: '⊗',\n  perp: '⊥',\n  sdot: '⋅',\n  lceil: '⌈',\n  rceil: '⌉',\n  lfloor: '⌊',\n  rfloor: '⌋',\n  lang: '〈',\n  rang: '〉',\n  loz: '◊',\n  spades: '♠',\n  clubs: '♣',\n  hearts: '♥',\n  diams: '♦',\n  quot: '\"',\n  amp: '&',\n  lt: '<',\n  gt: '>',\n  OElig: 'Œ',\n  oelig: 'œ',\n  Scaron: 'Š',\n  scaron: 'š',\n  Yuml: 'Ÿ',\n  circ: 'ˆ',\n  tilde: '˜',\n  ensp: ' ',\n  emsp: ' ',\n  thinsp: ' ',\n  zwnj: '‌',\n  zwj: '‍',\n  lrm: '‎',\n  rlm: '‏',\n  ndash: '–',\n  mdash: '—',\n  lsquo: '‘',\n  rsquo: '’',\n  sbquo: '‚',\n  ldquo: '“',\n  rdquo: '”',\n  bdquo: '„',\n  dagger: '†',\n  Dagger: '‡',\n  permil: '‰',\n  lsaquo: '‹',\n  rsaquo: '›',\n  euro: '€'\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACM,MAAM,yBAAyB;IACpC,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,MAAM;IACN,KAAK;IACL,MAAM;IACN,MAAM;IACN,OAAO;IACP,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,KAAK;IACL,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,QAAQ;IACR,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,MAAM;IACN,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,QAAQ;IACR,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,OAAO;IACP,SAAS;IACT,MAAM;IACN,KAAK;IACL,OAAO;IACP,MAAM;IACN,OAAO;IACP,QAAQ;IACR,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,KAAK;IACL,OAAO;IACP,KAAK;IACL,SAAS;IACT,KAAK;IACL,KAAK;IACL,KAAK;IACL,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,OAAO;IACP,SAAS;IACT,MAAM;IACN,KAAK;IACL,OAAO;IACP,MAAM;IACN,OAAO;IACP,QAAQ;IACR,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,KAAK;IACL,QAAQ;IACR,OAAO;IACP,KAAK;IACL,SAAS;IACT,KAAK;IACL,KAAK;IACL,KAAK;IACL,OAAO;IACP,UAAU;IACV,OAAO;IACP,KAAK;IACL,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,MAAM;IACN,OAAO;IACP,SAAS;IACT,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,QAAQ;IACR,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,IAAI;IACJ,MAAM;IACN,KAAK;IACL,OAAO;IACP,QAAQ;IACR,OAAO;IACP,MAAM;IACN,OAAO;IACP,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,QAAQ;IACR,KAAK;IACL,MAAM;IACN,OAAO;IACP,IAAI;IACJ,OAAO;IACP,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,KAAK;IACL,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,OAAO;IACP,MAAM;IACN,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,MAAM;IACN,QAAQ;IACR,MAAM;IACN,KAAK;IACL,KAAK;IACL,KAAK;IACL,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2017, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/stringify-entities/lib/constant/dangerous.js"], "sourcesContent": ["/**\n * List of legacy (that don’t need a trailing `;`) named references which could,\n * depending on what follows them, turn into a different meaning\n *\n * @type {Array<string>}\n */\nexport const dangerous = [\n  'cent',\n  'copy',\n  'divide',\n  'gt',\n  'lt',\n  'not',\n  'para',\n  'times'\n]\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AACM,MAAM,YAAY;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2041, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/stringify-entities/lib/util/to-named.js"], "sourcesContent": ["import {characterEntitiesLegacy} from 'character-entities-legacy'\nimport {characterEntitiesHtml4} from 'character-entities-html4'\nimport {dangerous} from '../constant/dangerous.js'\n\nconst own = {}.hasOwnProperty\n\n/**\n * `characterEntitiesHtml4` but inverted.\n *\n * @type {Record<string, string>}\n */\nconst characters = {}\n\n/** @type {string} */\nlet key\n\nfor (key in characterEntitiesHtml4) {\n  if (own.call(characterEntitiesHtml4, key)) {\n    characters[characterEntitiesHtml4[key]] = key\n  }\n}\n\nconst notAlphanumericRegex = /[^\\dA-Za-z]/\n\n/**\n * Configurable ways to encode characters as named references.\n *\n * @param {number} code\n * @param {number} next\n * @param {boolean|undefined} omit\n * @param {boolean|undefined} attribute\n * @returns {string}\n */\nexport function toNamed(code, next, omit, attribute) {\n  const character = String.fromCharCode(code)\n\n  if (own.call(characters, character)) {\n    const name = characters[character]\n    const value = '&' + name\n\n    if (\n      omit &&\n      characterEntitiesLegacy.includes(name) &&\n      !dangerous.includes(name) &&\n      (!attribute ||\n        (next &&\n          next !== 61 /* `=` */ &&\n          notAlphanumericRegex.test(String.fromCharCode(next))))\n    ) {\n      return value\n    }\n\n    return value + ';'\n  }\n\n  return ''\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,MAAM,CAAC,EAAE,cAAc;AAE7B;;;;CAIC,GACD,MAAM,aAAa,CAAC;AAEpB,mBAAmB,GACnB,IAAI;AAEJ,IAAK,OAAO,uJAAA,CAAA,yBAAsB,CAAE;IAClC,IAAI,IAAI,IAAI,CAAC,uJAAA,CAAA,yBAAsB,EAAE,MAAM;QACzC,UAAU,CAAC,uJAAA,CAAA,yBAAsB,CAAC,IAAI,CAAC,GAAG;IAC5C;AACF;AAEA,MAAM,uBAAuB;AAWtB,SAAS,QAAQ,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS;IACjD,MAAM,YAAY,OAAO,YAAY,CAAC;IAEtC,IAAI,IAAI,IAAI,CAAC,YAAY,YAAY;QACnC,MAAM,OAAO,UAAU,CAAC,UAAU;QAClC,MAAM,QAAQ,MAAM;QAEpB,IACE,QACA,wJAAA,CAAA,0BAAuB,CAAC,QAAQ,CAAC,SACjC,CAAC,qKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,SACpB,CAAC,CAAC,aACC,QACC,SAAS,GAAG,OAAO,OACnB,qBAAqB,IAAI,CAAC,OAAO,YAAY,CAAC,MAAO,GACzD;YACA,OAAO;QACT;QAEA,OAAO,QAAQ;IACjB;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2081, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/stringify-entities/lib/util/format-smart.js"], "sourcesContent": ["/**\n * @typedef FormatSmartOptions\n * @property {boolean} [useNamedReferences=false]\n *   Prefer named character references (`&amp;`) where possible.\n * @property {boolean} [useShortestReferences=false]\n *   Prefer the shortest possible reference, if that results in less bytes.\n *   **Note**: `useNamedReferences` can be omitted when using `useShortestReferences`.\n * @property {boolean} [omitOptionalSemicolons=false]\n *   Whether to omit semicolons when possible.\n *   **Note**: This creates what HTML calls “parse errors” but is otherwise still valid HTML — don’t use this except when building a minifier.\n *   Omitting semicolons is possible for certain named and numeric references in some cases.\n * @property {boolean} [attribute=false]\n *   Create character references which don’t fail in attributes.\n *   **Note**: `attribute` only applies when operating dangerously with\n *   `omitOptionalSemicolons: true`.\n */\n\nimport {toHexadecimal} from './to-hexadecimal.js'\nimport {toDecimal} from './to-decimal.js'\nimport {toNamed} from './to-named.js'\n\n/**\n * Configurable ways to encode a character yielding pretty or small results.\n *\n * @param {number} code\n * @param {number} next\n * @param {FormatSmartOptions} options\n * @returns {string}\n */\nexport function formatSmart(code, next, options) {\n  let numeric = toHexadecimal(code, next, options.omitOptionalSemicolons)\n  /** @type {string|undefined} */\n  let named\n\n  if (options.useNamedReferences || options.useShortestReferences) {\n    named = toNamed(\n      code,\n      next,\n      options.omitOptionalSemicolons,\n      options.attribute\n    )\n  }\n\n  // Use the shortest numeric reference when requested.\n  // A simple algorithm would use decimal for all code points under 100, as\n  // those are shorter than hexadecimal:\n  //\n  // * `&#99;` vs `&#x63;` (decimal shorter)\n  // * `&#100;` vs `&#x64;` (equal)\n  //\n  // However, because we take `next` into consideration when `omit` is used,\n  // And it would be possible that decimals are shorter on bigger values as\n  // well if `next` is hexadecimal but not decimal, we instead compare both.\n  if (\n    (options.useShortestReferences || !named) &&\n    options.useShortestReferences\n  ) {\n    const decimal = toDecimal(code, next, options.omitOptionalSemicolons)\n\n    if (decimal.length < numeric.length) {\n      numeric = decimal\n    }\n  }\n\n  return named &&\n    (!options.useShortestReferences || named.length < numeric.length)\n    ? named\n    : numeric\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;CAeC;;;AAED;AACA;AACA;;;;AAUO,SAAS,YAAY,IAAI,EAAE,IAAI,EAAE,OAAO;IAC7C,IAAI,UAAU,CAAA,GAAA,yKAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,MAAM,QAAQ,sBAAsB;IACtE,6BAA6B,GAC7B,IAAI;IAEJ,IAAI,QAAQ,kBAAkB,IAAI,QAAQ,qBAAqB,EAAE;QAC/D,QAAQ,CAAA,GAAA,mKAAA,CAAA,UAAO,AAAD,EACZ,MACA,MACA,QAAQ,sBAAsB,EAC9B,QAAQ,SAAS;IAErB;IAEA,qDAAqD;IACrD,yEAAyE;IACzE,sCAAsC;IACtC,EAAE;IACF,0CAA0C;IAC1C,iCAAiC;IACjC,EAAE;IACF,0EAA0E;IAC1E,yEAAyE;IACzE,0EAA0E;IAC1E,IACE,CAAC,QAAQ,qBAAqB,IAAI,CAAC,KAAK,KACxC,QAAQ,qBAAqB,EAC7B;QACA,MAAM,UAAU,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD,EAAE,MAAM,MAAM,QAAQ,sBAAsB;QAEpE,IAAI,QAAQ,MAAM,GAAG,QAAQ,MAAM,EAAE;YACnC,UAAU;QACZ;IACF;IAEA,OAAO,SACL,CAAC,CAAC,QAAQ,qBAAqB,IAAI,MAAM,MAAM,GAAG,QAAQ,MAAM,IAC9D,QACA;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2135, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/stringify-entities/lib/util/format-basic.js"], "sourcesContent": ["/**\n * The smallest way to encode a character.\n *\n * @param {number} code\n * @returns {string}\n */\nexport function formatBasic(code) {\n  return '&#x' + code.toString(16).toUpperCase() + ';'\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AACM,SAAS,YAAY,IAAI;IAC9B,OAAO,QAAQ,KAAK,QAAQ,CAAC,IAAI,WAAW,KAAK;AACnD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2152, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/stringify-entities/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('./core.js').CoreOptions & import('./util/format-smart.js').FormatSmartOptions} Options\n * @typedef {import('./core.js').CoreOptions} LightOptions\n */\n\nimport {core} from './core.js'\nimport {formatSmart} from './util/format-smart.js'\nimport {formatBasic} from './util/format-basic.js'\n\n/**\n * Encode special characters in `value`.\n *\n * @param {string} value\n *   Value to encode.\n * @param {Options} [options]\n *   Configuration.\n * @returns {string}\n *   Encoded value.\n */\nexport function stringifyEntities(value, options) {\n  return core(value, Object.assign({format: formatSmart}, options))\n}\n\n/**\n * Encode special characters in `value` as hexadecimals.\n *\n * @param {string} value\n *   Value to encode.\n * @param {LightOptions} [options]\n *   Configuration.\n * @returns {string}\n *   Encoded value.\n */\nexport function stringifyEntitiesLight(value, options) {\n  return core(value, Object.assign({format: formatBasic}, options))\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;AACA;;;;AAYO,SAAS,kBAAkB,KAAK,EAAE,OAAO;IAC9C,OAAO,CAAA,GAAA,oJAAA,CAAA,OAAI,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAAC,QAAQ,uKAAA,CAAA,cAAW;IAAA,GAAG;AAC1D;AAYO,SAAS,uBAAuB,KAAK,EAAE,OAAO;IACnD,OAAO,CAAA,GAAA,oJAAA,CAAA,OAAI,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAAC,QAAQ,uKAAA,CAAA,cAAW;IAAA,GAAG;AAC1D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2181, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-html/lib/handle/comment.js"], "sourcesContent": ["/**\n * @import {Comment, Parents} from 'hast'\n * @import {State} from '../index.js'\n */\n\nimport {stringifyEntities} from 'stringify-entities'\n\nconst htmlCommentRegex = /^>|^->|<!--|-->|--!>|<!-$/g\n\n// Declare arrays as variables so it can be cached by `stringifyEntities`\nconst bogusCommentEntitySubset = ['>']\nconst commentEntitySubset = ['<', '>']\n\n/**\n * Serialize a comment.\n *\n * @param {Comment} node\n *   Node to handle.\n * @param {number | undefined} _1\n *   Index of `node` in `parent.\n * @param {Parents | undefined} _2\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nexport function comment(node, _1, _2, state) {\n  // See: <https://html.spec.whatwg.org/multipage/syntax.html#comments>\n  return state.settings.bogusComments\n    ? '<?' +\n        stringifyEntities(\n          node.value,\n          Object.assign({}, state.settings.characterReferences, {\n            subset: bogusCommentEntitySubset\n          })\n        ) +\n        '>'\n    : '<!--' + node.value.replace(htmlCommentRegex, encode) + '-->'\n\n  /**\n   * @param {string} $0\n   */\n  function encode($0) {\n    return stringifyEntities(\n      $0,\n      Object.assign({}, state.settings.characterReferences, {\n        subset: commentEntitySubset\n      })\n    )\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AAEA,MAAM,mBAAmB;AAEzB,yEAAyE;AACzE,MAAM,2BAA2B;IAAC;CAAI;AACtC,MAAM,sBAAsB;IAAC;IAAK;CAAI;AAgB/B,SAAS,QAAQ,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK;IACzC,qEAAqE;IACrE,OAAO,MAAM,QAAQ,CAAC,aAAa,GAC/B,OACE,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EACd,KAAK,KAAK,EACV,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,QAAQ,CAAC,mBAAmB,EAAE;QACpD,QAAQ;IACV,MAEF,MACF,SAAS,KAAK,KAAK,CAAC,OAAO,CAAC,kBAAkB,UAAU;;IAE5D;;GAEC,GACD,SAAS,OAAO,EAAE;QAChB,OAAO,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EACrB,IACA,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,QAAQ,CAAC,mBAAmB,EAAE;YACpD,QAAQ;QACV;IAEJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2218, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-html/lib/handle/doctype.js"], "sourcesContent": ["/**\n * @import {Doctype, Parents} from 'hast'\n * @import {State} from '../index.js'\n */\n\n/**\n * Serialize a doctype.\n *\n * @param {Doctype} _1\n *   Node to handle.\n * @param {number | undefined} _2\n *   Index of `node` in `parent.\n * @param {Parents | undefined} _3\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nexport function doctype(_1, _2, _3, state) {\n  return (\n    '<!' +\n    (state.settings.upperDoctype ? 'DOCTYPE' : 'doctype') +\n    (state.settings.tightDoctype ? '' : ' ') +\n    'html>'\n  )\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;;;;;;;;;;CAaC;;;AACM,SAAS,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK;IACvC,OACE,OACA,CAAC,MAAM,QAAQ,CAAC,YAAY,GAAG,YAAY,SAAS,IACpD,CAAC,MAAM,QAAQ,CAAC,YAAY,GAAG,KAAK,GAAG,IACvC;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2246, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/ccount/index.js"], "sourcesContent": ["/**\n * Count how often a character (or substring) is used in a string.\n *\n * @param {string} value\n *   Value to search in.\n * @param {string} character\n *   Character (or substring) to look for.\n * @return {number}\n *   Number of times `character` occurred in `value`.\n */\nexport function ccount(value, character) {\n  const source = String(value)\n\n  if (typeof character !== 'string') {\n    throw new TypeError('Expected character')\n  }\n\n  let count = 0\n  let index = source.indexOf(character)\n\n  while (index !== -1) {\n    count++\n    index = source.indexOf(character, index + character.length)\n  }\n\n  return count\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;AACM,SAAS,OAAO,KAAK,EAAE,SAAS;IACrC,MAAM,SAAS,OAAO;IAEtB,IAAI,OAAO,cAAc,UAAU;QACjC,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,QAAQ;IACZ,IAAI,QAAQ,OAAO,OAAO,CAAC;IAE3B,MAAO,UAAU,CAAC,EAAG;QACnB;QACA,QAAQ,OAAO,OAAO,CAAC,WAAW,QAAQ,UAAU,MAAM;IAC5D;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2277, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/comma-separated-tokens/index.js"], "sourcesContent": ["/**\n * @typedef Options\n *   Configuration for `stringify`.\n * @property {boolean} [padLeft=true]\n *   Whether to pad a space before a token.\n * @property {boolean} [padRight=false]\n *   Whether to pad a space after a token.\n */\n\n/**\n * @typedef {Options} StringifyOptions\n *   Please use `StringifyOptions` instead.\n */\n\n/**\n * Parse comma-separated tokens to an array.\n *\n * @param {string} value\n *   Comma-separated tokens.\n * @returns {Array<string>}\n *   List of tokens.\n */\nexport function parse(value) {\n  /** @type {Array<string>} */\n  const tokens = []\n  const input = String(value || '')\n  let index = input.indexOf(',')\n  let start = 0\n  /** @type {boolean} */\n  let end = false\n\n  while (!end) {\n    if (index === -1) {\n      index = input.length\n      end = true\n    }\n\n    const token = input.slice(start, index).trim()\n\n    if (token || !end) {\n      tokens.push(token)\n    }\n\n    start = index + 1\n    index = input.indexOf(',', start)\n  }\n\n  return tokens\n}\n\n/**\n * Serialize an array of strings or numbers to comma-separated tokens.\n *\n * @param {Array<string|number>} values\n *   List of tokens.\n * @param {Options} [options]\n *   Configuration for `stringify` (optional).\n * @returns {string}\n *   Comma-separated tokens.\n */\nexport function stringify(values, options) {\n  const settings = options || {}\n\n  // Ensure the last empty entry is seen.\n  const input = values[values.length - 1] === '' ? [...values, ''] : values\n\n  return input\n    .join(\n      (settings.padRight ? ' ' : '') +\n        ',' +\n        (settings.padLeft === false ? '' : ' ')\n    )\n    .trim()\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED;;;CAGC,GAED;;;;;;;CAOC;;;;AACM,SAAS,MAAM,KAAK;IACzB,0BAA0B,GAC1B,MAAM,SAAS,EAAE;IACjB,MAAM,QAAQ,OAAO,SAAS;IAC9B,IAAI,QAAQ,MAAM,OAAO,CAAC;IAC1B,IAAI,QAAQ;IACZ,oBAAoB,GACpB,IAAI,MAAM;IAEV,MAAO,CAAC,IAAK;QACX,IAAI,UAAU,CAAC,GAAG;YAChB,QAAQ,MAAM,MAAM;YACpB,MAAM;QACR;QAEA,MAAM,QAAQ,MAAM,KAAK,CAAC,OAAO,OAAO,IAAI;QAE5C,IAAI,SAAS,CAAC,KAAK;YACjB,OAAO,IAAI,CAAC;QACd;QAEA,QAAQ,QAAQ;QAChB,QAAQ,MAAM,OAAO,CAAC,KAAK;IAC7B;IAEA,OAAO;AACT;AAYO,SAAS,UAAU,MAAM,EAAE,OAAO;IACvC,MAAM,WAAW,WAAW,CAAC;IAE7B,uCAAuC;IACvC,MAAM,QAAQ,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,KAAK,KAAK;WAAI;QAAQ;KAAG,GAAG;IAEnE,OAAO,MACJ,IAAI,CACH,CAAC,SAAS,QAAQ,GAAG,MAAM,EAAE,IAC3B,MACA,CAAC,SAAS,OAAO,KAAK,QAAQ,KAAK,GAAG,GAEzC,IAAI;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2333, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/property-information/lib/find.js"], "sourcesContent": ["/**\n * @import {Schema} from 'property-information'\n */\n\nimport {DefinedInfo} from './util/defined-info.js'\nimport {Info} from './util/info.js'\nimport {normalize} from './normalize.js'\n\nconst cap = /[A-Z]/g\nconst dash = /-[a-z]/g\nconst valid = /^data[-\\w.:]+$/i\n\n/**\n * Look up info on a property.\n *\n * In most cases the given `schema` contains info on the property.\n * All standard,\n * most legacy,\n * and some non-standard properties are supported.\n * For these cases,\n * the returned `Info` has hints about the value of the property.\n *\n * `name` can also be a valid data attribute or property,\n * in which case an `Info` object with the correctly cased `attribute` and\n * `property` is returned.\n *\n * `name` can be an unknown attribute,\n * in which case an `Info` object with `attribute` and `property` set to the\n * given name is returned.\n * It is not recommended to provide unsupported legacy or recently specced\n * properties.\n *\n *\n * @param {Schema} schema\n *   Schema;\n *   either the `html` or `svg` export.\n * @param {string} value\n *   An attribute-like or property-like name;\n *   it will be passed through `normalize` to hopefully find the correct info.\n * @returns {Info}\n *   Info.\n */\nexport function find(schema, value) {\n  const normal = normalize(value)\n  let property = value\n  let Type = Info\n\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]]\n  }\n\n  if (normal.length > 4 && normal.slice(0, 4) === 'data' && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      // Turn it into a property.\n      const rest = value.slice(5).replace(dash, camelcase)\n      property = 'data' + rest.charAt(0).toUpperCase() + rest.slice(1)\n    } else {\n      // Turn it into an attribute.\n      const rest = value.slice(4)\n\n      if (!dash.test(rest)) {\n        let dashes = rest.replace(cap, kebab)\n\n        if (dashes.charAt(0) !== '-') {\n          dashes = '-' + dashes\n        }\n\n        value = 'data' + dashes\n      }\n    }\n\n    Type = DefinedInfo\n  }\n\n  return new Type(property, value)\n}\n\n/**\n * @param {string} $0\n *   Value.\n * @returns {string}\n *   Kebab.\n */\nfunction kebab($0) {\n  return '-' + $0.toLowerCase()\n}\n\n/**\n * @param {string} $0\n *   Value.\n * @returns {string}\n *   Camel.\n */\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase()\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;AACA;AACA;;;;AAEA,MAAM,MAAM;AACZ,MAAM,OAAO;AACb,MAAM,QAAQ;AAgCP,SAAS,KAAK,MAAM,EAAE,KAAK;IAChC,MAAM,SAAS,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE;IACzB,IAAI,WAAW;IACf,IAAI,OAAO,8JAAA,CAAA,OAAI;IAEf,IAAI,UAAU,OAAO,MAAM,EAAE;QAC3B,OAAO,OAAO,QAAQ,CAAC,OAAO,MAAM,CAAC,OAAO,CAAC;IAC/C;IAEA,IAAI,OAAO,MAAM,GAAG,KAAK,OAAO,KAAK,CAAC,GAAG,OAAO,UAAU,MAAM,IAAI,CAAC,QAAQ;QAC3E,yBAAyB;QACzB,IAAI,MAAM,MAAM,CAAC,OAAO,KAAK;YAC3B,2BAA2B;YAC3B,MAAM,OAAO,MAAM,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;YAC1C,WAAW,SAAS,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC;QAChE,OAAO;YACL,6BAA6B;YAC7B,MAAM,OAAO,MAAM,KAAK,CAAC;YAEzB,IAAI,CAAC,KAAK,IAAI,CAAC,OAAO;gBACpB,IAAI,SAAS,KAAK,OAAO,CAAC,KAAK;gBAE/B,IAAI,OAAO,MAAM,CAAC,OAAO,KAAK;oBAC5B,SAAS,MAAM;gBACjB;gBAEA,QAAQ,SAAS;YACnB;QACF;QAEA,OAAO,yKAAA,CAAA,cAAW;IACpB;IAEA,OAAO,IAAI,KAAK,UAAU;AAC5B;AAEA;;;;;CAKC,GACD,SAAS,MAAM,EAAE;IACf,OAAO,MAAM,GAAG,WAAW;AAC7B;AAEA;;;;;CAKC,GACD,SAAS,UAAU,EAAE;IACnB,OAAO,GAAG,MAAM,CAAC,GAAG,WAAW;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2397, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/space-separated-tokens/index.js"], "sourcesContent": ["/**\n * Parse space-separated tokens to an array of strings.\n *\n * @param {string} value\n *   Space-separated tokens.\n * @returns {Array<string>}\n *   List of tokens.\n */\nexport function parse(value) {\n  const input = String(value || '').trim()\n  return input ? input.split(/[ \\t\\n\\r\\f]+/g) : []\n}\n\n/**\n * Serialize an array of strings as space separated-tokens.\n *\n * @param {Array<string|number>} values\n *   List of tokens.\n * @returns {string}\n *   Space-separated tokens.\n */\nexport function stringify(values) {\n  return values.join(' ').trim()\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACM,SAAS,MAAM,KAAK;IACzB,MAAM,QAAQ,OAAO,SAAS,IAAI,IAAI;IACtC,OAAO,QAAQ,MAAM,KAAK,CAAC,mBAAmB,EAAE;AAClD;AAUO,SAAS,UAAU,MAAM;IAC9B,OAAO,OAAO,IAAI,CAAC,KAAK,IAAI;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2421, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-whitespace/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Nodes} Nodes\n */\n\n// HTML whitespace expression.\n// See <https://infra.spec.whatwg.org/#ascii-whitespace>.\nconst re = /[ \\t\\n\\f\\r]/g\n\n/**\n * Check if the given value is *inter-element whitespace*.\n *\n * @param {Nodes | string} thing\n *   Thing to check (`Node` or `string`).\n * @returns {boolean}\n *   Whether the `value` is inter-element whitespace (`boolean`): consisting of\n *   zero or more of space, tab (`\\t`), line feed (`\\n`), carriage return\n *   (`\\r`), or form feed (`\\f`); if a node is passed it must be a `Text` node,\n *   whose `value` field is checked.\n */\nexport function whitespace(thing) {\n  return typeof thing === 'object'\n    ? thing.type === 'text'\n      ? empty(thing.value)\n      : false\n    : empty(thing)\n}\n\n/**\n * @param {string} value\n * @returns {boolean}\n */\nfunction empty(value) {\n  return value.replace(re, '') === ''\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,8BAA8B;AAC9B,yDAAyD;;;;AACzD,MAAM,KAAK;AAaJ,SAAS,WAAW,KAAK;IAC9B,OAAO,OAAO,UAAU,WACpB,MAAM,IAAI,KAAK,SACb,MAAM,MAAM,KAAK,IACjB,QACF,MAAM;AACZ;AAEA;;;CAGC,GACD,SAAS,MAAM,KAAK;IAClB,OAAO,MAAM,OAAO,CAAC,IAAI,QAAQ;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2444, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-html/lib/omission/util/siblings.js"], "sourcesContent": ["/**\n * @import {Parents, RootContent} from 'hast'\n */\n\nimport {whitespace} from 'hast-util-whitespace'\n\nexport const siblingAfter = siblings(1)\nexport const siblingBefore = siblings(-1)\n\n/** @type {Array<RootContent>} */\nconst emptyChildren = []\n\n/**\n * Factory to check siblings in a direction.\n *\n * @param {number} increment\n */\nfunction siblings(increment) {\n  return sibling\n\n  /**\n   * Find applicable siblings in a direction.\n   *\n   * @template {Parents} Parent\n   *   Parent type.\n   * @param {Parent | undefined} parent\n   *   Parent.\n   * @param {number | undefined} index\n   *   Index of child in `parent`.\n   * @param {boolean | undefined} [includeWhitespace=false]\n   *   Whether to include whitespace (default: `false`).\n   * @returns {Parent extends {children: Array<infer Child>} ? Child | undefined : never}\n   *   Child of parent.\n   */\n  function sibling(parent, index, includeWhitespace) {\n    const siblings = parent ? parent.children : emptyChildren\n    let offset = (index || 0) + increment\n    let next = siblings[offset]\n\n    if (!includeWhitespace) {\n      while (next && whitespace(next)) {\n        offset += increment\n        next = siblings[offset]\n      }\n    }\n\n    // @ts-expect-error: it’s a correct child.\n    return next\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAED;;AAEO,MAAM,eAAe,SAAS;AAC9B,MAAM,gBAAgB,SAAS,CAAC;AAEvC,+BAA+B,GAC/B,MAAM,gBAAgB,EAAE;AAExB;;;;CAIC,GACD,SAAS,SAAS,SAAS;IACzB,OAAO;;IAEP;;;;;;;;;;;;;GAaC,GACD,SAAS,QAAQ,MAAM,EAAE,KAAK,EAAE,iBAAiB;QAC/C,MAAM,WAAW,SAAS,OAAO,QAAQ,GAAG;QAC5C,IAAI,SAAS,CAAC,SAAS,CAAC,IAAI;QAC5B,IAAI,OAAO,QAAQ,CAAC,OAAO;QAE3B,IAAI,CAAC,mBAAmB;YACtB,MAAO,QAAQ,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE,MAAO;gBAC/B,UAAU;gBACV,OAAO,QAAQ,CAAC,OAAO;YACzB;QACF;QAEA,0CAA0C;QAC1C,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2495, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-html/lib/omission/omission.js"], "sourcesContent": ["/**\n * @import {Element, Parents} from 'hast'\n */\n\n/**\n * @callback OmitHandle\n *   Check if a tag can be omitted.\n * @param {Element} element\n *   Element to check.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether to omit a tag.\n *\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Factory to check if a given node can have a tag omitted.\n *\n * @param {Record<string, OmitHandle>} handlers\n *   Omission handlers, where each key is a tag name, and each value is the\n *   corresponding handler.\n * @returns {OmitHandle}\n *   Whether to omit a tag of an element.\n */\nexport function omission(handlers) {\n  return omit\n\n  /**\n   * Check if a given node can have a tag omitted.\n   *\n   * @type {OmitHandle}\n   */\n  function omit(node, index, parent) {\n    return (\n      own.call(handlers, node.tagName) &&\n      handlers[node.tagName](node, index, parent)\n    )\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;;;;;;;;;CAYC;;;AAED,MAAM,MAAM,CAAC,EAAE,cAAc;AAWtB,SAAS,SAAS,QAAQ;IAC/B,OAAO;;IAEP;;;;GAIC,GACD,SAAS,KAAK,IAAI,EAAE,KAAK,EAAE,MAAM;QAC/B,OACE,IAAI,IAAI,CAAC,UAAU,KAAK,OAAO,KAC/B,QAAQ,CAAC,KAAK,OAAO,CAAC,CAAC,MAAM,OAAO;IAExC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2530, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-html/lib/omission/closing.js"], "sourcesContent": ["/**\n * @import {Element, Parents} from 'hast'\n */\n\nimport {whitespace} from 'hast-util-whitespace'\nimport {siblingAfter} from './util/siblings.js'\nimport {omission} from './omission.js'\n\nexport const closing = omission({\n  body,\n  caption: headOrColgroupOrCaption,\n  colgroup: headOrColgroupOrCaption,\n  dd,\n  dt,\n  head: headOrColgroupOrCaption,\n  html,\n  li,\n  optgroup,\n  option,\n  p,\n  rp: rubyElement,\n  rt: rubyElement,\n  tbody,\n  td: cells,\n  tfoot,\n  th: cells,\n  thead,\n  tr\n})\n\n/**\n * Macro for `</head>`, `</colgroup>`, and `</caption>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction headOrColgroupOrCaption(_, index, parent) {\n  const next = siblingAfter(parent, index, true)\n  return (\n    !next ||\n    (next.type !== 'comment' &&\n      !(next.type === 'text' && whitespace(next.value.charAt(0))))\n  )\n}\n\n/**\n * Whether to omit `</html>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction html(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return !next || next.type !== 'comment'\n}\n\n/**\n * Whether to omit `</body>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction body(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return !next || next.type !== 'comment'\n}\n\n/**\n * Whether to omit `</p>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction p(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return next\n    ? next.type === 'element' &&\n        (next.tagName === 'address' ||\n          next.tagName === 'article' ||\n          next.tagName === 'aside' ||\n          next.tagName === 'blockquote' ||\n          next.tagName === 'details' ||\n          next.tagName === 'div' ||\n          next.tagName === 'dl' ||\n          next.tagName === 'fieldset' ||\n          next.tagName === 'figcaption' ||\n          next.tagName === 'figure' ||\n          next.tagName === 'footer' ||\n          next.tagName === 'form' ||\n          next.tagName === 'h1' ||\n          next.tagName === 'h2' ||\n          next.tagName === 'h3' ||\n          next.tagName === 'h4' ||\n          next.tagName === 'h5' ||\n          next.tagName === 'h6' ||\n          next.tagName === 'header' ||\n          next.tagName === 'hgroup' ||\n          next.tagName === 'hr' ||\n          next.tagName === 'main' ||\n          next.tagName === 'menu' ||\n          next.tagName === 'nav' ||\n          next.tagName === 'ol' ||\n          next.tagName === 'p' ||\n          next.tagName === 'pre' ||\n          next.tagName === 'section' ||\n          next.tagName === 'table' ||\n          next.tagName === 'ul')\n    : !parent ||\n        // Confusing parent.\n        !(\n          parent.type === 'element' &&\n          (parent.tagName === 'a' ||\n            parent.tagName === 'audio' ||\n            parent.tagName === 'del' ||\n            parent.tagName === 'ins' ||\n            parent.tagName === 'map' ||\n            parent.tagName === 'noscript' ||\n            parent.tagName === 'video')\n        )\n}\n\n/**\n * Whether to omit `</li>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction li(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'li')\n}\n\n/**\n * Whether to omit `</dt>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction dt(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return Boolean(\n    next &&\n      next.type === 'element' &&\n      (next.tagName === 'dt' || next.tagName === 'dd')\n  )\n}\n\n/**\n * Whether to omit `</dd>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction dd(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'dt' || next.tagName === 'dd'))\n  )\n}\n\n/**\n * Whether to omit `</rt>` or `</rp>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction rubyElement(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'rp' || next.tagName === 'rt'))\n  )\n}\n\n/**\n * Whether to omit `</optgroup>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction optgroup(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'optgroup')\n}\n\n/**\n * Whether to omit `</option>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction option(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'option' || next.tagName === 'optgroup'))\n  )\n}\n\n/**\n * Whether to omit `</thead>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction thead(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return Boolean(\n    next &&\n      next.type === 'element' &&\n      (next.tagName === 'tbody' || next.tagName === 'tfoot')\n  )\n}\n\n/**\n * Whether to omit `</tbody>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tbody(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'tbody' || next.tagName === 'tfoot'))\n  )\n}\n\n/**\n * Whether to omit `</tfoot>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tfoot(_, index, parent) {\n  return !siblingAfter(parent, index)\n}\n\n/**\n * Whether to omit `</tr>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tr(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'tr')\n}\n\n/**\n * Whether to omit `</td>` or `</th>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction cells(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'td' || next.tagName === 'th'))\n  )\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;AACA;AACA;;;;AAEO,MAAM,UAAU,CAAA,GAAA,yKAAA,CAAA,WAAQ,AAAD,EAAE;IAC9B;IACA,SAAS;IACT,UAAU;IACV;IACA;IACA,MAAM;IACN;IACA;IACA;IACA;IACA;IACA,IAAI;IACJ,IAAI;IACJ;IACA,IAAI;IACJ;IACA,IAAI;IACJ;IACA;AACF;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,wBAAwB,CAAC,EAAE,KAAK,EAAE,MAAM;IAC/C,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,OAAO;IACzC,OACE,CAAC,QACA,KAAK,IAAI,KAAK,aACb,CAAC,CAAC,KAAK,IAAI,KAAK,UAAU,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG;AAEhE;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM;IAC5B,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OAAO,CAAC,QAAQ,KAAK,IAAI,KAAK;AAChC;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM;IAC5B,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OAAO,CAAC,QAAQ,KAAK,IAAI,KAAK;AAChC;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM;IACzB,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OAAO,OACH,KAAK,IAAI,KAAK,aACZ,CAAC,KAAK,OAAO,KAAK,aAChB,KAAK,OAAO,KAAK,aACjB,KAAK,OAAO,KAAK,WACjB,KAAK,OAAO,KAAK,gBACjB,KAAK,OAAO,KAAK,aACjB,KAAK,OAAO,KAAK,SACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,cACjB,KAAK,OAAO,KAAK,gBACjB,KAAK,OAAO,KAAK,YACjB,KAAK,OAAO,KAAK,YACjB,KAAK,OAAO,KAAK,UACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,YACjB,KAAK,OAAO,KAAK,YACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,UACjB,KAAK,OAAO,KAAK,UACjB,KAAK,OAAO,KAAK,SACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,OACjB,KAAK,OAAO,KAAK,SACjB,KAAK,OAAO,KAAK,aACjB,KAAK,OAAO,KAAK,WACjB,KAAK,OAAO,KAAK,IAAI,IACzB,CAAC,UACC,oBAAoB;IACpB,CAAC,CACC,OAAO,IAAI,KAAK,aAChB,CAAC,OAAO,OAAO,KAAK,OAClB,OAAO,OAAO,KAAK,WACnB,OAAO,OAAO,KAAK,SACnB,OAAO,OAAO,KAAK,SACnB,OAAO,OAAO,KAAK,SACnB,OAAO,OAAO,KAAK,cACnB,OAAO,OAAO,KAAK,OAAO,CAC9B;AACR;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM;IAC1B,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OAAO,CAAC,QAAS,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK;AAC/D;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM;IAC1B,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OAAO,QACL,QACE,KAAK,IAAI,KAAK,aACd,CAAC,KAAK,OAAO,KAAK,QAAQ,KAAK,OAAO,KAAK,IAAI;AAErD;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM;IAC1B,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OACE,CAAC,QACA,KAAK,IAAI,KAAK,aACb,CAAC,KAAK,OAAO,KAAK,QAAQ,KAAK,OAAO,KAAK,IAAI;AAErD;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,YAAY,CAAC,EAAE,KAAK,EAAE,MAAM;IACnC,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OACE,CAAC,QACA,KAAK,IAAI,KAAK,aACb,CAAC,KAAK,OAAO,KAAK,QAAQ,KAAK,OAAO,KAAK,IAAI;AAErD;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,SAAS,CAAC,EAAE,KAAK,EAAE,MAAM;IAChC,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OAAO,CAAC,QAAS,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK;AAC/D;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,OAAO,CAAC,EAAE,KAAK,EAAE,MAAM;IAC9B,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OACE,CAAC,QACA,KAAK,IAAI,KAAK,aACb,CAAC,KAAK,OAAO,KAAK,YAAY,KAAK,OAAO,KAAK,UAAU;AAE/D;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,MAAM,CAAC,EAAE,KAAK,EAAE,MAAM;IAC7B,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OAAO,QACL,QACE,KAAK,IAAI,KAAK,aACd,CAAC,KAAK,OAAO,KAAK,WAAW,KAAK,OAAO,KAAK,OAAO;AAE3D;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,MAAM,CAAC,EAAE,KAAK,EAAE,MAAM;IAC7B,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OACE,CAAC,QACA,KAAK,IAAI,KAAK,aACb,CAAC,KAAK,OAAO,KAAK,WAAW,KAAK,OAAO,KAAK,OAAO;AAE3D;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,MAAM,CAAC,EAAE,KAAK,EAAE,MAAM;IAC7B,OAAO,CAAC,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;AAC/B;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM;IAC1B,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OAAO,CAAC,QAAS,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK;AAC/D;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,MAAM,CAAC,EAAE,KAAK,EAAE,MAAM;IAC7B,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OACE,CAAC,QACA,KAAK,IAAI,KAAK,aACb,CAAC,KAAK,OAAO,KAAK,QAAQ,KAAK,OAAO,KAAK,IAAI;AAErD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2793, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-html/lib/omission/opening.js"], "sourcesContent": ["/**\n * @import {Element, Parents} from 'hast'\n */\n\nimport {whitespace} from 'hast-util-whitespace'\nimport {siblingAfter, siblingBefore} from './util/siblings.js'\nimport {closing} from './closing.js'\nimport {omission} from './omission.js'\n\nexport const opening = omission({\n  body,\n  colgroup,\n  head,\n  html,\n  tbody\n})\n\n/**\n * Whether to omit `<html>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction html(node) {\n  const head = siblingAfter(node, -1)\n  return !head || head.type !== 'comment'\n}\n\n/**\n * Whether to omit `<head>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction head(node) {\n  /** @type {Set<string>} */\n  const seen = new Set()\n\n  // Whether `srcdoc` or not,\n  // make sure the content model at least doesn’t have too many `base`s/`title`s.\n  for (const child of node.children) {\n    if (\n      child.type === 'element' &&\n      (child.tagName === 'base' || child.tagName === 'title')\n    ) {\n      if (seen.has(child.tagName)) return false\n      seen.add(child.tagName)\n    }\n  }\n\n  // “May be omitted if the element is empty,\n  // or if the first thing inside the head element is an element.”\n  const child = node.children[0]\n  return !child || child.type === 'element'\n}\n\n/**\n * Whether to omit `<body>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction body(node) {\n  const head = siblingAfter(node, -1, true)\n\n  return (\n    !head ||\n    (head.type !== 'comment' &&\n      !(head.type === 'text' && whitespace(head.value.charAt(0))) &&\n      !(\n        head.type === 'element' &&\n        (head.tagName === 'meta' ||\n          head.tagName === 'link' ||\n          head.tagName === 'script' ||\n          head.tagName === 'style' ||\n          head.tagName === 'template')\n      ))\n  )\n}\n\n/**\n * Whether to omit `<colgroup>`.\n * The spec describes some logic for the opening tag, but it’s easier to\n * implement in the closing tag, to the same effect, so we handle it there\n * instead.\n *\n * @param {Element} node\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction colgroup(node, index, parent) {\n  const previous = siblingBefore(parent, index)\n  const head = siblingAfter(node, -1, true)\n\n  // Previous colgroup was already omitted.\n  if (\n    parent &&\n    previous &&\n    previous.type === 'element' &&\n    previous.tagName === 'colgroup' &&\n    closing(previous, parent.children.indexOf(previous), parent)\n  ) {\n    return false\n  }\n\n  return Boolean(head && head.type === 'element' && head.tagName === 'col')\n}\n\n/**\n * Whether to omit `<tbody>`.\n *\n * @param {Element} node\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction tbody(node, index, parent) {\n  const previous = siblingBefore(parent, index)\n  const head = siblingAfter(node, -1)\n\n  // Previous table section was already omitted.\n  if (\n    parent &&\n    previous &&\n    previous.type === 'element' &&\n    (previous.tagName === 'thead' || previous.tagName === 'tbody') &&\n    closing(previous, parent.children.indexOf(previous), parent)\n  ) {\n    return false\n  }\n\n  return Boolean(head && head.type === 'element' && head.tagName === 'tr')\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;AACA;AACA;AACA;;;;;AAEO,MAAM,UAAU,CAAA,GAAA,yKAAA,CAAA,WAAQ,AAAD,EAAE;IAC9B;IACA;IACA;IACA;IACA;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,KAAK,IAAI;IAChB,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,MAAM,CAAC;IACjC,OAAO,CAAC,QAAQ,KAAK,IAAI,KAAK;AAChC;AAEA;;;;;;;CAOC,GACD,SAAS,KAAK,IAAI;IAChB,wBAAwB,GACxB,MAAM,OAAO,IAAI;IAEjB,2BAA2B;IAC3B,+EAA+E;IAC/E,KAAK,MAAM,SAAS,KAAK,QAAQ,CAAE;QACjC,IACE,MAAM,IAAI,KAAK,aACf,CAAC,MAAM,OAAO,KAAK,UAAU,MAAM,OAAO,KAAK,OAAO,GACtD;YACA,IAAI,KAAK,GAAG,CAAC,MAAM,OAAO,GAAG,OAAO;YACpC,KAAK,GAAG,CAAC,MAAM,OAAO;QACxB;IACF;IAEA,2CAA2C;IAC3C,gEAAgE;IAChE,MAAM,QAAQ,KAAK,QAAQ,CAAC,EAAE;IAC9B,OAAO,CAAC,SAAS,MAAM,IAAI,KAAK;AAClC;AAEA;;;;;;;CAOC,GACD,SAAS,KAAK,IAAI;IAChB,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,MAAM,CAAC,GAAG;IAEpC,OACE,CAAC,QACA,KAAK,IAAI,KAAK,aACb,CAAC,CAAC,KAAK,IAAI,KAAK,UAAU,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG,KAC1D,CAAC,CACC,KAAK,IAAI,KAAK,aACd,CAAC,KAAK,OAAO,KAAK,UAChB,KAAK,OAAO,KAAK,UACjB,KAAK,OAAO,KAAK,YACjB,KAAK,OAAO,KAAK,WACjB,KAAK,OAAO,KAAK,UAAU,CAC/B;AAEN;AAEA;;;;;;;;;;;;;;CAcC,GACD,SAAS,SAAS,IAAI,EAAE,KAAK,EAAE,MAAM;IACnC,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;IACvC,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,MAAM,CAAC,GAAG;IAEpC,yCAAyC;IACzC,IACE,UACA,YACA,SAAS,IAAI,KAAK,aAClB,SAAS,OAAO,KAAK,cACrB,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO,QAAQ,CAAC,OAAO,CAAC,WAAW,SACrD;QACA,OAAO;IACT;IAEA,OAAO,QAAQ,QAAQ,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK;AACrE;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,MAAM,IAAI,EAAE,KAAK,EAAE,MAAM;IAChC,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;IACvC,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,MAAM,CAAC;IAEjC,8CAA8C;IAC9C,IACE,UACA,YACA,SAAS,IAAI,KAAK,aAClB,CAAC,SAAS,OAAO,KAAK,WAAW,SAAS,OAAO,KAAK,OAAO,KAC7D,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO,QAAQ,CAAC,OAAO,CAAC,WAAW,SACrD;QACA,OAAO;IACT;IAEA,OAAO,QAAQ,QAAQ,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2906, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-html/lib/handle/element.js"], "sourcesContent": ["/**\n * @import {Element, Parents, Properties} from 'hast'\n * @import {State} from '../index.js'\n */\n\nimport {ccount} from 'ccount'\nimport {stringify as commas} from 'comma-separated-tokens'\nimport {find, svg} from 'property-information'\nimport {stringify as spaces} from 'space-separated-tokens'\nimport {stringifyEntities} from 'stringify-entities'\nimport {closing} from '../omission/closing.js'\nimport {opening} from '../omission/opening.js'\n\n/**\n * Maps of subsets.\n *\n * Each value is a matrix of tuples.\n * The value at `0` causes parse errors, the value at `1` is valid.\n * Of both, the value at `0` is unsafe, and the value at `1` is safe.\n *\n * @type {Record<'double' | 'name' | 'single' | 'unquoted', Array<[Array<string>, Array<string>]>>}\n */\nconst constants = {\n  // See: <https://html.spec.whatwg.org/#attribute-name-state>.\n  name: [\n    ['\\t\\n\\f\\r &/=>'.split(''), '\\t\\n\\f\\r \"&\\'/=>`'.split('')],\n    ['\\0\\t\\n\\f\\r \"&\\'/<=>'.split(''), '\\0\\t\\n\\f\\r \"&\\'/<=>`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(unquoted)-state>.\n  unquoted: [\n    ['\\t\\n\\f\\r &>'.split(''), '\\0\\t\\n\\f\\r \"&\\'<=>`'.split('')],\n    ['\\0\\t\\n\\f\\r \"&\\'<=>`'.split(''), '\\0\\t\\n\\f\\r \"&\\'<=>`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(single-quoted)-state>.\n  single: [\n    [\"&'\".split(''), '\"&\\'`'.split('')],\n    [\"\\0&'\".split(''), '\\0\"&\\'`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(double-quoted)-state>.\n  double: [\n    ['\"&'.split(''), '\"&\\'`'.split('')],\n    ['\\0\"&'.split(''), '\\0\"&\\'`'.split('')]\n  ]\n}\n\n/**\n * Serialize an element node.\n *\n * @param {Element} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nexport function element(node, index, parent, state) {\n  const schema = state.schema\n  const omit = schema.space === 'svg' ? false : state.settings.omitOptionalTags\n  let selfClosing =\n    schema.space === 'svg'\n      ? state.settings.closeEmptyElements\n      : state.settings.voids.includes(node.tagName.toLowerCase())\n  /** @type {Array<string>} */\n  const parts = []\n  /** @type {string} */\n  let last\n\n  if (schema.space === 'html' && node.tagName === 'svg') {\n    state.schema = svg\n  }\n\n  const attributes = serializeAttributes(state, node.properties)\n\n  const content = state.all(\n    schema.space === 'html' && node.tagName === 'template' ? node.content : node\n  )\n\n  state.schema = schema\n\n  // If the node is categorised as void, but it has children, remove the\n  // categorisation.\n  // This enables for example `menuitem`s, which are void in W3C HTML but not\n  // void in WHATWG HTML, to be stringified properly.\n  // Note: `menuitem` has since been removed from the HTML spec, and so is no\n  // longer void.\n  if (content) selfClosing = false\n\n  if (attributes || !omit || !opening(node, index, parent)) {\n    parts.push('<', node.tagName, attributes ? ' ' + attributes : '')\n\n    if (\n      selfClosing &&\n      (schema.space === 'svg' || state.settings.closeSelfClosing)\n    ) {\n      last = attributes.charAt(attributes.length - 1)\n      if (\n        !state.settings.tightSelfClosing ||\n        last === '/' ||\n        (last && last !== '\"' && last !== \"'\")\n      ) {\n        parts.push(' ')\n      }\n\n      parts.push('/')\n    }\n\n    parts.push('>')\n  }\n\n  parts.push(content)\n\n  if (!selfClosing && (!omit || !closing(node, index, parent))) {\n    parts.push('</' + node.tagName + '>')\n  }\n\n  return parts.join('')\n}\n\n/**\n * @param {State} state\n * @param {Properties | null | undefined} properties\n * @returns {string}\n */\nfunction serializeAttributes(state, properties) {\n  /** @type {Array<string>} */\n  const values = []\n  let index = -1\n  /** @type {string} */\n  let key\n\n  if (properties) {\n    for (key in properties) {\n      if (properties[key] !== null && properties[key] !== undefined) {\n        const value = serializeAttribute(state, key, properties[key])\n        if (value) values.push(value)\n      }\n    }\n  }\n\n  while (++index < values.length) {\n    const last = state.settings.tightAttributes\n      ? values[index].charAt(values[index].length - 1)\n      : undefined\n\n    // In tight mode, don’t add a space after quoted attributes.\n    if (index !== values.length - 1 && last !== '\"' && last !== \"'\") {\n      values[index] += ' '\n    }\n  }\n\n  return values.join('')\n}\n\n/**\n * @param {State} state\n * @param {string} key\n * @param {Properties[keyof Properties]} value\n * @returns {string}\n */\nfunction serializeAttribute(state, key, value) {\n  const info = find(state.schema, key)\n  const x =\n    state.settings.allowParseErrors && state.schema.space === 'html' ? 0 : 1\n  const y = state.settings.allowDangerousCharacters ? 0 : 1\n  let quote = state.quote\n  /** @type {string | undefined} */\n  let result\n\n  if (info.overloadedBoolean && (value === info.attribute || value === '')) {\n    value = true\n  } else if (\n    (info.boolean || info.overloadedBoolean) &&\n    (typeof value !== 'string' || value === info.attribute || value === '')\n  ) {\n    value = Boolean(value)\n  }\n\n  if (\n    value === null ||\n    value === undefined ||\n    value === false ||\n    (typeof value === 'number' && Number.isNaN(value))\n  ) {\n    return ''\n  }\n\n  const name = stringifyEntities(\n    info.attribute,\n    Object.assign({}, state.settings.characterReferences, {\n      // Always encode without parse errors in non-HTML.\n      subset: constants.name[x][y]\n    })\n  )\n\n  // No value.\n  // There is currently only one boolean property in SVG: `[download]` on\n  // `<a>`.\n  // This property does not seem to work in browsers (Firefox, Safari, Chrome),\n  // so I can’t test if dropping the value works.\n  // But I assume that it should:\n  //\n  // ```html\n  // <!doctype html>\n  // <svg viewBox=\"0 0 100 100\">\n  //   <a href=https://example.com download>\n  //     <circle cx=50 cy=40 r=35 />\n  //   </a>\n  // </svg>\n  // ```\n  //\n  // See: <https://github.com/wooorm/property-information/blob/main/lib/svg.js>\n  if (value === true) return name\n\n  // `spaces` doesn’t accept a second argument, but it’s given here just to\n  // keep the code cleaner.\n  value = Array.isArray(value)\n    ? (info.commaSeparated ? commas : spaces)(value, {\n        padLeft: !state.settings.tightCommaSeparatedLists\n      })\n    : String(value)\n\n  if (state.settings.collapseEmptyAttributes && !value) return name\n\n  // Check unquoted value.\n  if (state.settings.preferUnquoted) {\n    result = stringifyEntities(\n      value,\n      Object.assign({}, state.settings.characterReferences, {\n        attribute: true,\n        subset: constants.unquoted[x][y]\n      })\n    )\n  }\n\n  // If we don’t want unquoted, or if `value` contains character references when\n  // unquoted…\n  if (result !== value) {\n    // If the alternative is less common than `quote`, switch.\n    if (\n      state.settings.quoteSmart &&\n      ccount(value, quote) > ccount(value, state.alternative)\n    ) {\n      quote = state.alternative\n    }\n\n    result =\n      quote +\n      stringifyEntities(\n        value,\n        Object.assign({}, state.settings.characterReferences, {\n          // Always encode without parse errors in non-HTML.\n          subset: (quote === \"'\" ? constants.single : constants.double)[x][y],\n          attribute: true\n        })\n      ) +\n      quote\n  }\n\n  // Don’t add a `=` for unquoted empties.\n  return name + (result ? '=' + result : result)\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;AAEA;;;;;;;;CAQC,GACD,MAAM,YAAY;IAChB,6DAA6D;IAC7D,MAAM;QACJ;YAAC,gBAAgB,KAAK,CAAC;YAAK,oBAAoB,KAAK,CAAC;SAAI;QAC1D;YAAC,sBAAsB,KAAK,CAAC;YAAK,uBAAuB,KAAK,CAAC;SAAI;KACpE;IACD,yEAAyE;IACzE,UAAU;QACR;YAAC,cAAc,KAAK,CAAC;YAAK,sBAAsB,KAAK,CAAC;SAAI;QAC1D;YAAC,sBAAsB,KAAK,CAAC;YAAK,sBAAsB,KAAK,CAAC;SAAI;KACnE;IACD,8EAA8E;IAC9E,QAAQ;QACN;YAAC,KAAK,KAAK,CAAC;YAAK,QAAQ,KAAK,CAAC;SAAI;QACnC;YAAC,OAAO,KAAK,CAAC;YAAK,UAAU,KAAK,CAAC;SAAI;KACxC;IACD,8EAA8E;IAC9E,QAAQ;QACN;YAAC,KAAK,KAAK,CAAC;YAAK,QAAQ,KAAK,CAAC;SAAI;QACnC;YAAC,OAAO,KAAK,CAAC;YAAK,UAAU,KAAK,CAAC;SAAI;KACxC;AACH;AAgBO,SAAS,QAAQ,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK;IAChD,MAAM,SAAS,MAAM,MAAM;IAC3B,MAAM,OAAO,OAAO,KAAK,KAAK,QAAQ,QAAQ,MAAM,QAAQ,CAAC,gBAAgB;IAC7E,IAAI,cACF,OAAO,KAAK,KAAK,QACb,MAAM,QAAQ,CAAC,kBAAkB,GACjC,MAAM,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,OAAO,CAAC,WAAW;IAC5D,0BAA0B,GAC1B,MAAM,QAAQ,EAAE;IAChB,mBAAmB,GACnB,IAAI;IAEJ,IAAI,OAAO,KAAK,KAAK,UAAU,KAAK,OAAO,KAAK,OAAO;QACrD,MAAM,MAAM,GAAG,gKAAA,CAAA,MAAG;IACpB;IAEA,MAAM,aAAa,oBAAoB,OAAO,KAAK,UAAU;IAE7D,MAAM,UAAU,MAAM,GAAG,CACvB,OAAO,KAAK,KAAK,UAAU,KAAK,OAAO,KAAK,aAAa,KAAK,OAAO,GAAG;IAG1E,MAAM,MAAM,GAAG;IAEf,sEAAsE;IACtE,kBAAkB;IAClB,2EAA2E;IAC3E,mDAAmD;IACnD,2EAA2E;IAC3E,eAAe;IACf,IAAI,SAAS,cAAc;IAE3B,IAAI,cAAc,CAAC,QAAQ,CAAC,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,MAAM,OAAO,SAAS;QACxD,MAAM,IAAI,CAAC,KAAK,KAAK,OAAO,EAAE,aAAa,MAAM,aAAa;QAE9D,IACE,eACA,CAAC,OAAO,KAAK,KAAK,SAAS,MAAM,QAAQ,CAAC,gBAAgB,GAC1D;YACA,OAAO,WAAW,MAAM,CAAC,WAAW,MAAM,GAAG;YAC7C,IACE,CAAC,MAAM,QAAQ,CAAC,gBAAgB,IAChC,SAAS,OACR,QAAQ,SAAS,OAAO,SAAS,KAClC;gBACA,MAAM,IAAI,CAAC;YACb;YAEA,MAAM,IAAI,CAAC;QACb;QAEA,MAAM,IAAI,CAAC;IACb;IAEA,MAAM,IAAI,CAAC;IAEX,IAAI,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,MAAM,OAAO,OAAO,GAAG;QAC5D,MAAM,IAAI,CAAC,OAAO,KAAK,OAAO,GAAG;IACnC;IAEA,OAAO,MAAM,IAAI,CAAC;AACpB;AAEA;;;;CAIC,GACD,SAAS,oBAAoB,KAAK,EAAE,UAAU;IAC5C,0BAA0B,GAC1B,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,CAAC;IACb,mBAAmB,GACnB,IAAI;IAEJ,IAAI,YAAY;QACd,IAAK,OAAO,WAAY;YACtB,IAAI,UAAU,CAAC,IAAI,KAAK,QAAQ,UAAU,CAAC,IAAI,KAAK,WAAW;gBAC7D,MAAM,QAAQ,mBAAmB,OAAO,KAAK,UAAU,CAAC,IAAI;gBAC5D,IAAI,OAAO,OAAO,IAAI,CAAC;YACzB;QACF;IACF;IAEA,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;QAC9B,MAAM,OAAO,MAAM,QAAQ,CAAC,eAAe,GACvC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,KAC5C;QAEJ,4DAA4D;QAC5D,IAAI,UAAU,OAAO,MAAM,GAAG,KAAK,SAAS,OAAO,SAAS,KAAK;YAC/D,MAAM,CAAC,MAAM,IAAI;QACnB;IACF;IAEA,OAAO,OAAO,IAAI,CAAC;AACrB;AAEA;;;;;CAKC,GACD,SAAS,mBAAmB,KAAK,EAAE,GAAG,EAAE,KAAK;IAC3C,MAAM,OAAO,CAAA,GAAA,sJAAA,CAAA,OAAI,AAAD,EAAE,MAAM,MAAM,EAAE;IAChC,MAAM,IACJ,MAAM,QAAQ,CAAC,gBAAgB,IAAI,MAAM,MAAM,CAAC,KAAK,KAAK,SAAS,IAAI;IACzE,MAAM,IAAI,MAAM,QAAQ,CAAC,wBAAwB,GAAG,IAAI;IACxD,IAAI,QAAQ,MAAM,KAAK;IACvB,+BAA+B,GAC/B,IAAI;IAEJ,IAAI,KAAK,iBAAiB,IAAI,CAAC,UAAU,KAAK,SAAS,IAAI,UAAU,EAAE,GAAG;QACxE,QAAQ;IACV,OAAO,IACL,CAAC,KAAK,OAAO,IAAI,KAAK,iBAAiB,KACvC,CAAC,OAAO,UAAU,YAAY,UAAU,KAAK,SAAS,IAAI,UAAU,EAAE,GACtE;QACA,QAAQ,QAAQ;IAClB;IAEA,IACE,UAAU,QACV,UAAU,aACV,UAAU,SACT,OAAO,UAAU,YAAY,OAAO,KAAK,CAAC,QAC3C;QACA,OAAO;IACT;IAEA,MAAM,OAAO,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAC3B,KAAK,SAAS,EACd,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,QAAQ,CAAC,mBAAmB,EAAE;QACpD,kDAAkD;QAClD,QAAQ,UAAU,IAAI,CAAC,EAAE,CAAC,EAAE;IAC9B;IAGF,YAAY;IACZ,uEAAuE;IACvE,SAAS;IACT,6EAA6E;IAC7E,+CAA+C;IAC/C,+BAA+B;IAC/B,EAAE;IACF,UAAU;IACV,kBAAkB;IAClB,8BAA8B;IAC9B,0CAA0C;IAC1C,kCAAkC;IAClC,SAAS;IACT,SAAS;IACT,MAAM;IACN,EAAE;IACF,6EAA6E;IAC7E,IAAI,UAAU,MAAM,OAAO;IAE3B,yEAAyE;IACzE,yBAAyB;IACzB,QAAQ,MAAM,OAAO,CAAC,SAClB,CAAC,KAAK,cAAc,GAAG,qJAAA,CAAA,YAAM,GAAG,qJAAA,CAAA,YAAM,EAAE,OAAO;QAC7C,SAAS,CAAC,MAAM,QAAQ,CAAC,wBAAwB;IACnD,KACA,OAAO;IAEX,IAAI,MAAM,QAAQ,CAAC,uBAAuB,IAAI,CAAC,OAAO,OAAO;IAE7D,wBAAwB;IACxB,IAAI,MAAM,QAAQ,CAAC,cAAc,EAAE;QACjC,SAAS,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EACvB,OACA,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,QAAQ,CAAC,mBAAmB,EAAE;YACpD,WAAW;YACX,QAAQ,UAAU,QAAQ,CAAC,EAAE,CAAC,EAAE;QAClC;IAEJ;IAEA,8EAA8E;IAC9E,YAAY;IACZ,IAAI,WAAW,OAAO;QACpB,0DAA0D;QAC1D,IACE,MAAM,QAAQ,CAAC,UAAU,IACzB,CAAA,GAAA,+HAAA,CAAA,SAAM,AAAD,EAAE,OAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,SAAM,AAAD,EAAE,OAAO,MAAM,WAAW,GACtD;YACA,QAAQ,MAAM,WAAW;QAC3B;QAEA,SACE,QACA,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EACd,OACA,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,QAAQ,CAAC,mBAAmB,EAAE;YACpD,kDAAkD;YAClD,QAAQ,CAAC,UAAU,MAAM,UAAU,MAAM,GAAG,UAAU,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;YACnE,WAAW;QACb,MAEF;IACJ;IAEA,wCAAwC;IACxC,OAAO,OAAO,CAAC,SAAS,MAAM,SAAS,MAAM;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3118, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-html/lib/handle/text.js"], "sourcesContent": ["/**\n * @import {Parents, Text} from 'hast'\n * @import {Raw} from 'mdast-util-to-hast'\n * @import {State} from '../index.js'\n */\n\nimport {stringifyEntities} from 'stringify-entities'\n\n// Declare array as variable so it can be cached by `stringifyEntities`\nconst textEntitySubset = ['<', '&']\n\n/**\n * Serialize a text node.\n *\n * @param {Raw | Text} node\n *   Node to handle.\n * @param {number | undefined} _\n *   Index of `node` in `parent.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nexport function text(node, _, parent, state) {\n  // Check if content of `node` should be escaped.\n  return parent &&\n    parent.type === 'element' &&\n    (parent.tagName === 'script' || parent.tagName === 'style')\n    ? node.value\n    : stringifyEntities(\n        node.value,\n        Object.assign({}, state.settings.characterReferences, {\n          subset: textEntitySubset\n        })\n      )\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;;AAEA,uEAAuE;AACvE,MAAM,mBAAmB;IAAC;IAAK;CAAI;AAgB5B,SAAS,KAAK,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK;IACzC,gDAAgD;IAChD,OAAO,UACL,OAAO,IAAI,KAAK,aAChB,CAAC,OAAO,OAAO,KAAK,YAAY,OAAO,OAAO,KAAK,OAAO,IACxD,KAAK,KAAK,GACV,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EACd,KAAK,KAAK,EACV,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,QAAQ,CAAC,mBAAmB,EAAE;QACpD,QAAQ;IACV;AAER", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3144, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-html/lib/handle/raw.js"], "sourcesContent": ["/**\n * @import {Parents} from 'hast'\n * @import {Raw} from 'mdast-util-to-hast'\n * @import {State} from '../index.js'\n */\n\nimport {text} from './text.js'\n\n/**\n * Serialize a raw node.\n *\n * @param {Raw} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nexport function raw(node, index, parent, state) {\n  return state.settings.allowDangerousHtml\n    ? node.value\n    : text(node, index, parent, state)\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;;AAgBO,SAAS,IAAI,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK;IAC5C,OAAO,MAAM,QAAQ,CAAC,kBAAkB,GACpC,KAAK,KAAK,GACV,CAAA,GAAA,mKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,OAAO,QAAQ;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3162, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-html/lib/handle/root.js"], "sourcesContent": ["/**\n * @import {Parents, Root} from 'hast'\n * @import {State} from '../index.js'\n */\n\n/**\n * Serialize a root.\n *\n * @param {Root} node\n *   Node to handle.\n * @param {number | undefined} _1\n *   Index of `node` in `parent.\n * @param {Parents | undefined} _2\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nexport function root(node, _1, _2, state) {\n  return state.all(node)\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;;;;;;;;;;CAaC;;;AACM,SAAS,KAAK,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK;IACtC,OAAO,MAAM,GAAG,CAAC;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3190, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-html/lib/handle/index.js"], "sourcesContent": ["/**\n * @import {Nodes, Parents} from 'hast'\n * @import {State} from '../index.js'\n */\n\nimport {zwitch} from 'zwitch'\nimport {comment} from './comment.js'\nimport {doctype} from './doctype.js'\nimport {element} from './element.js'\nimport {raw} from './raw.js'\nimport {root} from './root.js'\nimport {text} from './text.js'\n\n/**\n * @type {(node: Nodes, index: number | undefined, parent: Parents | undefined, state: State) => string}\n */\nexport const handle = zwitch('type', {\n  invalid,\n  unknown,\n  handlers: {comment, doctype, element, raw, root, text}\n})\n\n/**\n * Fail when a non-node is found in the tree.\n *\n * @param {unknown} node\n *   Unknown value.\n * @returns {never}\n *   Never.\n */\nfunction invalid(node) {\n  throw new Error('Expected node, not `' + node + '`')\n}\n\n/**\n * Fail when a node with an unknown type is found in the tree.\n *\n * @param {unknown} node_\n *  Unknown node.\n * @returns {never}\n *   Never.\n */\nfunction unknown(node_) {\n  // `type` is guaranteed by runtime JS.\n  const node = /** @type {Nodes} */ (node_)\n  throw new Error('Cannot compile unknown node `' + node.type + '`')\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAKO,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;IACnC;IACA;IACA,UAAU;QAAC,SAAA,sKAAA,CAAA,UAAO;QAAE,SAAA,sKAAA,CAAA,UAAO;QAAE,SAAA,sKAAA,CAAA,UAAO;QAAE,KAAA,kKAAA,CAAA,MAAG;QAAE,MAAA,mKAAA,CAAA,OAAI;QAAE,MAAA,mKAAA,CAAA,OAAI;IAAA;AACvD;AAEA;;;;;;;CAOC,GACD,SAAS,QAAQ,IAAI;IACnB,MAAM,IAAI,MAAM,yBAAyB,OAAO;AAClD;AAEA;;;;;;;CAOC,GACD,SAAS,QAAQ,KAAK;IACpB,sCAAsC;IACtC,MAAM,OAA6B;IACnC,MAAM,IAAI,MAAM,kCAAkC,KAAK,IAAI,GAAG;AAChE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3250, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/hast-util-to-html/lib/index.js"], "sourcesContent": ["/**\n * @import {Nodes, Parents, RootContent} from 'hast'\n * @import {Schema} from 'property-information'\n * @import {Options as StringifyEntitiesOptions} from 'stringify-entities'\n */\n\n/**\n * @typedef {Omit<StringifyEntitiesOptions, 'attribute' | 'escapeOnly' | 'subset'>} CharacterReferences\n *\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [allowDangerousCharacters=false]\n *   Do not encode some characters which cause XSS vulnerabilities in older\n *   browsers (default: `false`).\n *\n *   > ⚠️ **Danger**: only set this if you completely trust the content.\n * @property {boolean | null | undefined} [allowDangerousHtml=false]\n *   Allow `raw` nodes and insert them as raw HTML (default: `false`).\n *\n *   When `false`, `Raw` nodes are encoded.\n *\n *   > ⚠️ **Danger**: only set this if you completely trust the content.\n * @property {boolean | null | undefined} [allowParseErrors=false]\n *   Do not encode characters which cause parse errors (even though they work),\n *   to save bytes (default: `false`).\n *\n *   Not used in the SVG space.\n *\n *   > 👉 **Note**: intentionally creates parse errors in markup (how parse\n *   > errors are handled is well defined, so this works but isn’t pretty).\n * @property {boolean | null | undefined} [bogusComments=false]\n *   Use “bogus comments” instead of comments to save byes: `<?charlie>`\n *   instead of `<!--charlie-->` (default: `false`).\n *\n *   > 👉 **Note**: intentionally creates parse errors in markup (how parse\n *   > errors are handled is well defined, so this works but isn’t pretty).\n * @property {CharacterReferences | null | undefined} [characterReferences]\n *   Configure how to serialize character references (optional).\n * @property {boolean | null | undefined} [closeEmptyElements=false]\n *   Close SVG elements without any content with slash (`/`) on the opening tag\n *   instead of an end tag: `<circle />` instead of `<circle></circle>`\n *   (default: `false`).\n *\n *   See `tightSelfClosing` to control whether a space is used before the\n *   slash.\n *\n *   Not used in the HTML space.\n * @property {boolean | null | undefined} [closeSelfClosing=false]\n *   Close self-closing nodes with an extra slash (`/`): `<img />` instead of\n *   `<img>` (default: `false`).\n *\n *   See `tightSelfClosing` to control whether a space is used before the\n *   slash.\n *\n *   Not used in the SVG space.\n * @property {boolean | null | undefined} [collapseEmptyAttributes=false]\n *   Collapse empty attributes: get `class` instead of `class=\"\"` (default:\n *   `false`).\n *\n *   Not used in the SVG space.\n *\n *   > 👉 **Note**: boolean attributes (such as `hidden`) are always collapsed.\n * @property {boolean | null | undefined} [omitOptionalTags=false]\n *   Omit optional opening and closing tags (default: `false`).\n *\n *   For example, in `<ol><li>one</li><li>two</li></ol>`, both `</li>` closing\n *   tags can be omitted.\n *   The first because it’s followed by another `li`, the last because it’s\n *   followed by nothing.\n *\n *   Not used in the SVG space.\n * @property {boolean | null | undefined} [preferUnquoted=false]\n *   Leave attributes unquoted if that results in less bytes (default: `false`).\n *\n *   Not used in the SVG space.\n * @property {boolean | null | undefined} [quoteSmart=false]\n *   Use the other quote if that results in less bytes (default: `false`).\n * @property {Quote | null | undefined} [quote='\"']\n *   Preferred quote to use (default: `'\"'`).\n * @property {Space | null | undefined} [space='html']\n *   When an `<svg>` element is found in the HTML space, this package already\n *   automatically switches to and from the SVG space when entering and exiting\n *   it (default: `'html'`).\n *\n *   > 👉 **Note**: hast is not XML.\n *   > It supports SVG as embedded in HTML.\n *   > It does not support the features available in XML.\n *   > Passing SVG might break but fragments of modern SVG should be fine.\n *   > Use [`xast`][xast] if you need to support SVG as XML.\n * @property {boolean | null | undefined} [tightAttributes=false]\n *   Join attributes together, without whitespace, if possible: get\n *   `class=\"a b\"title=\"c d\"` instead of `class=\"a b\" title=\"c d\"` to save\n *   bytes (default: `false`).\n *\n *   Not used in the SVG space.\n *\n *   > 👉 **Note**: intentionally creates parse errors in markup (how parse\n *   > errors are handled is well defined, so this works but isn’t pretty).\n * @property {boolean | null | undefined} [tightCommaSeparatedLists=false]\n *   Join known comma-separated attribute values with just a comma (`,`),\n *   instead of padding them on the right as well (`,␠`, where `␠` represents a\n *   space) (default: `false`).\n * @property {boolean | null | undefined} [tightDoctype=false]\n *   Drop unneeded spaces in doctypes: `<!doctypehtml>` instead of\n *   `<!doctype html>` to save bytes (default: `false`).\n *\n *   > 👉 **Note**: intentionally creates parse errors in markup (how parse\n *   > errors are handled is well defined, so this works but isn’t pretty).\n * @property {boolean | null | undefined} [tightSelfClosing=false]\n *   Do not use an extra space when closing self-closing elements: `<img/>`\n *   instead of `<img />` (default: `false`).\n *\n *   > 👉 **Note**: only used if `closeSelfClosing: true` or\n *   > `closeEmptyElements: true`.\n * @property {boolean | null | undefined} [upperDoctype=false]\n *   Use a `<!DOCTYPE…` instead of `<!doctype…` (default: `false`).\n *\n *   Useless except for XHTML.\n * @property {ReadonlyArray<string> | null | undefined} [voids]\n *   Tag names of elements to serialize without closing tag (default: `html-void-elements`).\n *\n *   Not used in the SVG space.\n *\n *   > 👉 **Note**: It’s highly unlikely that you want to pass this, because\n *   > hast is not for XML, and HTML will not add more void elements.\n *\n * @typedef {'\"' | \"'\"} Quote\n *   HTML quotes for attribute values.\n *\n * @typedef {Omit<Required<{[key in keyof Options]: Exclude<Options[key], null | undefined>}>, 'space' | 'quote'>} Settings\n *\n * @typedef {'html' | 'svg'} Space\n *   Namespace.\n *\n * @typedef State\n *   Info passed around about the current state.\n * @property {(node: Parents | undefined) => string} all\n *   Serialize the children of a parent node.\n * @property {Quote} alternative\n *   Alternative quote.\n * @property {(node: Nodes, index: number | undefined, parent: Parents | undefined) => string} one\n *   Serialize one node.\n * @property {Quote} quote\n *   Preferred quote.\n * @property {Schema} schema\n *   Current schema.\n * @property {Settings} settings\n *   User configuration.\n */\n\nimport {htmlVoidElements} from 'html-void-elements'\nimport {html, svg} from 'property-information'\nimport {handle} from './handle/index.js'\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/** @type {CharacterReferences} */\nconst emptyCharacterReferences = {}\n\n/** @type {Array<never>} */\nconst emptyChildren = []\n\n/**\n * Serialize hast as HTML.\n *\n * @param {Array<RootContent> | Nodes} tree\n *   Tree to serialize.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized HTML.\n */\nexport function toHtml(tree, options) {\n  const options_ = options || emptyOptions\n  const quote = options_.quote || '\"'\n  const alternative = quote === '\"' ? \"'\" : '\"'\n\n  if (quote !== '\"' && quote !== \"'\") {\n    throw new Error('Invalid quote `' + quote + '`, expected `\\'` or `\"`')\n  }\n\n  /** @type {State} */\n  const state = {\n    one,\n    all,\n    settings: {\n      omitOptionalTags: options_.omitOptionalTags || false,\n      allowParseErrors: options_.allowParseErrors || false,\n      allowDangerousCharacters: options_.allowDangerousCharacters || false,\n      quoteSmart: options_.quoteSmart || false,\n      preferUnquoted: options_.preferUnquoted || false,\n      tightAttributes: options_.tightAttributes || false,\n      upperDoctype: options_.upperDoctype || false,\n      tightDoctype: options_.tightDoctype || false,\n      bogusComments: options_.bogusComments || false,\n      tightCommaSeparatedLists: options_.tightCommaSeparatedLists || false,\n      tightSelfClosing: options_.tightSelfClosing || false,\n      collapseEmptyAttributes: options_.collapseEmptyAttributes || false,\n      allowDangerousHtml: options_.allowDangerousHtml || false,\n      voids: options_.voids || htmlVoidElements,\n      characterReferences:\n        options_.characterReferences || emptyCharacterReferences,\n      closeSelfClosing: options_.closeSelfClosing || false,\n      closeEmptyElements: options_.closeEmptyElements || false\n    },\n    schema: options_.space === 'svg' ? svg : html,\n    quote,\n    alternative\n  }\n\n  return state.one(\n    Array.isArray(tree) ? {type: 'root', children: tree} : tree,\n    undefined,\n    undefined\n  )\n}\n\n/**\n * Serialize a node.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Nodes} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(node, index, parent) {\n  return handle(node, index, parent, this)\n}\n\n/**\n * Serialize all children of `parent`.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Parents | undefined} parent\n *   Parent whose children to serialize.\n * @returns {string}\n */\nexport function all(parent) {\n  /** @type {Array<string>} */\n  const results = []\n  const children = (parent && parent.children) || emptyChildren\n  let index = -1\n\n  while (++index < children.length) {\n    results[index] = this.one(children[index], index, parent)\n  }\n\n  return results.join('')\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8IC;;;;AAED;AACA;AACA;;;;AAEA,oBAAoB,GACpB,MAAM,eAAe,CAAC;AAEtB,gCAAgC,GAChC,MAAM,2BAA2B,CAAC;AAElC,yBAAyB,GACzB,MAAM,gBAAgB,EAAE;AAYjB,SAAS,OAAO,IAAI,EAAE,OAAO;IAClC,MAAM,WAAW,WAAW;IAC5B,MAAM,QAAQ,SAAS,KAAK,IAAI;IAChC,MAAM,cAAc,UAAU,MAAM,MAAM;IAE1C,IAAI,UAAU,OAAO,UAAU,KAAK;QAClC,MAAM,IAAI,MAAM,oBAAoB,QAAQ;IAC9C;IAEA,kBAAkB,GAClB,MAAM,QAAQ;QACZ;QACA;QACA,UAAU;YACR,kBAAkB,SAAS,gBAAgB,IAAI;YAC/C,kBAAkB,SAAS,gBAAgB,IAAI;YAC/C,0BAA0B,SAAS,wBAAwB,IAAI;YAC/D,YAAY,SAAS,UAAU,IAAI;YACnC,gBAAgB,SAAS,cAAc,IAAI;YAC3C,iBAAiB,SAAS,eAAe,IAAI;YAC7C,cAAc,SAAS,YAAY,IAAI;YACvC,cAAc,SAAS,YAAY,IAAI;YACvC,eAAe,SAAS,aAAa,IAAI;YACzC,0BAA0B,SAAS,wBAAwB,IAAI;YAC/D,kBAAkB,SAAS,gBAAgB,IAAI;YAC/C,yBAAyB,SAAS,uBAAuB,IAAI;YAC7D,oBAAoB,SAAS,kBAAkB,IAAI;YACnD,OAAO,SAAS,KAAK,IAAI,iJAAA,CAAA,mBAAgB;YACzC,qBACE,SAAS,mBAAmB,IAAI;YAClC,kBAAkB,SAAS,gBAAgB,IAAI;YAC/C,oBAAoB,SAAS,kBAAkB,IAAI;QACrD;QACA,QAAQ,SAAS,KAAK,KAAK,QAAQ,gKAAA,CAAA,MAAG,GAAG,gKAAA,CAAA,OAAI;QAC7C;QACA;IACF;IAEA,OAAO,MAAM,GAAG,CACd,MAAM,OAAO,CAAC,QAAQ;QAAC,MAAM;QAAQ,UAAU;IAAI,IAAI,MACvD,WACA;AAEJ;AAEA;;;;;;;;;;;;;CAaC,GACD,SAAS,IAAI,IAAI,EAAE,KAAK,EAAE,MAAM;IAC9B,OAAO,CAAA,GAAA,oKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,OAAO,QAAQ,IAAI;AACzC;AAWO,SAAS,IAAI,MAAM;IACxB,0BAA0B,GAC1B,MAAM,UAAU,EAAE;IAClB,MAAM,WAAW,AAAC,UAAU,OAAO,QAAQ,IAAK;IAChD,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,SAAS,MAAM,CAAE;QAChC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO;IACpD;IAEA,OAAO,QAAQ,IAAI,CAAC;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3478, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/rehype-stringify/lib/index.js"], "sourcesContent": ["/**\n * @import {Root} from 'hast'\n * @import {Options} from 'hast-util-to-html'\n * @import {Compiler, Processor} from 'unified'\n */\n\nimport {toHtml} from 'hast-util-to-html'\n\n/**\n * Plugin to add support for serializing as HTML.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nexport default function rehypeStringify(options) {\n  /** @type {Processor<undefined, undefined, undefined, Root, string>} */\n  // @ts-expect-error: TS in JSDoc generates wrong types if `this` is typed regularly.\n  const self = this\n  const settings = {...self.data('settings'), ...options}\n\n  self.compiler = compiler\n\n  /**\n   * @type {Compiler<Root, string>}\n   */\n  function compiler(tree) {\n    return toHtml(tree, settings)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;;AAUe,SAAS,gBAAgB,OAAO;IAC7C,qEAAqE,GACrE,oFAAoF;IACpF,MAAM,OAAO,IAAI;IACjB,MAAM,WAAW;QAAC,GAAG,KAAK,IAAI,CAAC,WAAW;QAAE,GAAG,OAAO;IAAA;IAEtD,KAAK,QAAQ,GAAG;IAEhB;;GAEC,GACD,SAAS,SAAS,IAAI;QACpB,OAAO,CAAA,GAAA,0JAAA,CAAA,SAAM,AAAD,EAAE,MAAM;IACtB;AACF", "ignoreList": [0], "debugId": null}}]}