{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/pm/state/dist/index.js"], "sourcesContent": ["// state/index.ts\nexport * from \"prosemirror-state\";\n"], "names": [], "mappings": "AAAA,iBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/pm/view/dist/index.js"], "sourcesContent": ["// view/index.ts\nexport * from \"prosemirror-view\";\n"], "names": [], "mappings": "AAAA,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/pm/keymap/dist/index.js"], "sourcesContent": ["// keymap/index.ts\nexport * from \"prosemirror-keymap\";\n"], "names": [], "mappings": "AAAA,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/pm/model/dist/index.js"], "sourcesContent": ["// model/index.ts\nexport * from \"prosemirror-model\";\n"], "names": [], "mappings": "AAAA,iBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/pm/transform/dist/index.js"], "sourcesContent": ["// transform/index.ts\nexport * from \"prosemirror-transform\";\n"], "names": [], "mappings": "AAAA,qBAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/pm/commands/dist/index.js"], "sourcesContent": ["// commands/index.ts\nexport * from \"prosemirror-commands\";\n"], "names": [], "mappings": "AAAA,oBAAoB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/pm/schema-list/dist/index.js"], "sourcesContent": ["// schema-list/index.ts\nexport * from \"prosemirror-schema-list\";\n"], "names": [], "mappings": "AAAA,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/pm/collab/dist/index.js"], "sourcesContent": ["// collab/index.ts\nexport * from \"prosemirror-collab\";\n"], "names": [], "mappings": "AAAA,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/pm/gapcursor/dist/index.js"], "sourcesContent": ["// gapcursor/index.ts\nexport * from \"prosemirror-gapcursor\";\n"], "names": [], "mappings": "AAAA,qBAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/pm/history/dist/index.js"], "sourcesContent": ["// history/index.ts\nexport * from \"prosemirror-history\";\n"], "names": [], "mappings": "AAAA,mBAAmB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/extension-bold/src/bold.ts"], "sourcesContent": ["import {\n  Mark,\n  markInputRule,\n  markPasteRule,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface BoldOptions {\n  /**\n   * HTML attributes to add to the bold element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    bold: {\n      /**\n       * Set a bold mark\n       */\n      setBold: () => ReturnType,\n      /**\n       * Toggle a bold mark\n       */\n      toggleBold: () => ReturnType,\n      /**\n       * Unset a bold mark\n       */\n      unsetBold: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches bold text via `**` as input.\n */\nexport const starInputRegex = /(?:^|\\s)(\\*\\*(?!\\s+\\*\\*)((?:[^*]+))\\*\\*(?!\\s+\\*\\*))$/\n\n/**\n * Matches bold text via `**` while pasting.\n */\nexport const starPasteRegex = /(?:^|\\s)(\\*\\*(?!\\s+\\*\\*)((?:[^*]+))\\*\\*(?!\\s+\\*\\*))/g\n\n/**\n * Matches bold text via `__` as input.\n */\nexport const underscoreInputRegex = /(?:^|\\s)(__(?!\\s+__)((?:[^_]+))__(?!\\s+__))$/\n\n/**\n * Matches bold text via `__` while pasting.\n */\nexport const underscorePasteRegex = /(?:^|\\s)(__(?!\\s+__)((?:[^_]+))__(?!\\s+__))/g\n\n/**\n * This extension allows you to mark text as bold.\n * @see https://tiptap.dev/api/marks/bold\n */\nexport const Bold = Mark.create<BoldOptions>({\n  name: 'bold',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'strong',\n      },\n      {\n        tag: 'b',\n        getAttrs: node => (node as HTMLElement).style.fontWeight !== 'normal' && null,\n      },\n      {\n        style: 'font-weight=400',\n        clearMark: mark => mark.type.name === this.name,\n      },\n      {\n        style: 'font-weight',\n        getAttrs: value => /^(bold(er)?|[5-9]\\d{2,})$/.test(value as string) && null,\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['strong', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setBold: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleBold: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetBold: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-b': () => this.editor.commands.toggleBold(),\n      'Mod-B': () => this.editor.commands.toggleBold(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: starInputRegex,\n        type: this.type,\n      }),\n      markInputRule({\n        find: underscoreInputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: starPasteRegex,\n        type: this.type,\n      }),\n      markPasteRule({\n        find: underscorePasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;;AAmCA;;CAEG,GACI,MAAM,cAAc,GAAG;AAE9B;;CAEG,GACI,MAAM,cAAc,GAAG;AAE9B;;CAEG,GACI,MAAM,oBAAoB,GAAG;AAEpC;;CAEG,GACI,MAAM,oBAAoB,GAAG;AAEpC;;;CAGG,GACU,MAAA,IAAI,oJAAG,QAAI,CAAC,MAAM,CAAc;IAC3C,IAAI,EAAE,MAAM;IAEZ,UAAU,GAAA;QACR,OAAO;YACL,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,SAAS,GAAA;QACP,OAAO;YACL;gBACE,GAAG,EAAE,QAAQ;YACd,CAAA;YACD;gBACE,GAAG,EAAE,GAAG;gBACR,QAAQ,GAAE,IAAI,GAAK,IAAoB,CAAC,KAAK,CAAC,UAAU,KAAK,QAAQ,IAAI,IAAI;YAC9E,CAAA;YACD;gBACE,KAAK,EAAE,iBAAiB;gBACxB,SAAS,EAAE,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;YAChD,CAAA;YACD;gBACE,KAAK,EAAE,aAAa;gBACpB,QAAQ,GAAE,KAAK,GAAI,2BAA2B,CAAC,IAAI,CAAC,KAAe,CAAC,IAAI,IAAI;YAC7E,CAAA;SACF;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,QAAQ;gBAAE,oKAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KACnF;IAED,WAAW,GAAA;QACT,OAAO;YACL,OAAO,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAC9B,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;iBACnC;YACD,UAAU,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACjC,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtC;YACD,SAAS,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAChC,OAAO,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;iBACrC;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE;YAChD,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE;SACjD;KACF;IAED,aAAa,GAAA;QACX,OAAO;kKACL,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;aACF,qKAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;IAED,aAAa,GAAA;QACX,OAAO;kKACL,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;kKACF,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/extension-code/src/code.ts"], "sourcesContent": ["import {\n  Mark,\n  markInputRule,\n  markPasteRule,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface CodeOptions {\n  /**\n   * The HTML attributes applied to the code element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    code: {\n      /**\n       * Set a code mark\n       */\n      setCode: () => ReturnType,\n      /**\n       * Toggle inline code\n       */\n      toggleCode: () => ReturnType,\n      /**\n       * Unset a code mark\n       */\n      unsetCode: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Regular expressions to match inline code blocks enclosed in backticks.\n *  It matches:\n *     - An opening backtick, followed by\n *     - Any text that doesn't include a backtick (captured for marking), followed by\n *     - A closing backtick.\n *  This ensures that any text between backticks is formatted as code,\n *  regardless of the surrounding characters (exception being another backtick).\n */\nexport const inputRegex = /(^|[^`])`([^`]+)`(?!`)/\n\n/**\n * Matches inline code while pasting.\n */\nexport const pasteRegex = /(^|[^`])`([^`]+)`(?!`)/g\n\n/**\n * This extension allows you to mark text as inline code.\n * @see https://tiptap.dev/api/marks/code\n */\nexport const Code = Mark.create<CodeOptions>({\n  name: 'code',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  excludes: '_',\n\n  code: true,\n\n  exitable: true,\n\n  parseHTML() {\n    return [\n      { tag: 'code' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['code', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setCode: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleCode: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetCode: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-e': () => this.editor.commands.toggleCode(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: inputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: pasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;AAmCA;;;;;;;;CAQG,GACI,MAAM,UAAU,GAAG;AAE1B;;CAEG,GACI,MAAM,UAAU,GAAG;AAE1B;;;CAGG,GACU,MAAA,IAAI,qJAAG,OAAI,CAAC,MAAM,CAAc;IAC3C,IAAI,EAAE,MAAM;IAEZ,UAAU,GAAA;QACR,OAAO;YACL,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,QAAQ,EAAE,GAAG;IAEb,IAAI,EAAE,IAAI;IAEV,QAAQ,EAAE,IAAI;IAEd,SAAS,GAAA;QACP,OAAO;YACL;gBAAE,GAAG,EAAE,MAAM;YAAA,CAAE;SAChB;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,MAAM;YAAE,wKAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KACjF;IAED,WAAW,GAAA;QACT,OAAO;YACL,OAAO,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAC9B,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;iBACnC;YACD,UAAU,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACjC,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtC;YACD,SAAS,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAChC,OAAO,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;iBACrC;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE;SACjD;KACF;IAED,aAAa,GAAA;QACX,OAAO;aACL,qKAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;IAED,aAAa,GAAA;QACX,OAAO;kKACL,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/extension-italic/src/italic.ts"], "sourcesContent": ["import {\n  Mark,\n  markInputRule,\n  markPasteRule,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface ItalicOptions {\n  /**\n   * HTML attributes to add to the italic element.\n   * @default {}\n   * @example { class: 'foo' }\n  */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    italic: {\n      /**\n       * Set an italic mark\n       * @example editor.commands.setItalic()\n       */\n      setItalic: () => ReturnType,\n      /**\n       * Toggle an italic mark\n       * @example editor.commands.toggleItalic()\n       */\n      toggleItalic: () => ReturnType,\n      /**\n       * Unset an italic mark\n       * @example editor.commands.unsetItalic()\n       */\n      unsetItalic: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches an italic to a *italic* on input.\n */\nexport const starInputRegex = /(?:^|\\s)(\\*(?!\\s+\\*)((?:[^*]+))\\*(?!\\s+\\*))$/\n\n/**\n * Matches an italic to a *italic* on paste.\n */\nexport const starPasteRegex = /(?:^|\\s)(\\*(?!\\s+\\*)((?:[^*]+))\\*(?!\\s+\\*))/g\n\n/**\n * Matches an italic to a _italic_ on input.\n */\nexport const underscoreInputRegex = /(?:^|\\s)(_(?!\\s+_)((?:[^_]+))_(?!\\s+_))$/\n\n/**\n * Matches an italic to a _italic_ on paste.\n */\nexport const underscorePasteRegex = /(?:^|\\s)(_(?!\\s+_)((?:[^_]+))_(?!\\s+_))/g\n\n/**\n * This extension allows you to create italic text.\n * @see https://www.tiptap.dev/api/marks/italic\n */\nexport const Italic = Mark.create<ItalicOptions>({\n  name: 'italic',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'em',\n      },\n      {\n        tag: 'i',\n        getAttrs: node => (node as HTMLElement).style.fontStyle !== 'normal' && null,\n      },\n      {\n        style: 'font-style=normal',\n        clearMark: mark => mark.type.name === this.name,\n      },\n      {\n        style: 'font-style=italic',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['em', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setItalic: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleItalic: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetItalic: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-i': () => this.editor.commands.toggleItalic(),\n      'Mod-I': () => this.editor.commands.toggleItalic(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: starInputRegex,\n        type: this.type,\n      }),\n      markInputRule({\n        find: underscoreInputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: starPasteRegex,\n        type: this.type,\n      }),\n      markPasteRule({\n        find: underscorePasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;;AAsCA;;CAEG,GACI,MAAM,cAAc,GAAG;AAE9B;;CAEG,GACI,MAAM,cAAc,GAAG;AAE9B;;CAEG,GACI,MAAM,oBAAoB,GAAG;AAEpC;;CAEG,GACI,MAAM,oBAAoB,GAAG;AAEpC;;;CAGG,GACU,MAAA,MAAM,oJAAG,QAAI,CAAC,MAAM,CAAgB;IAC/C,IAAI,EAAE,QAAQ;IAEd,UAAU,GAAA;QACR,OAAO;YACL,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,SAAS,GAAA;QACP,OAAO;YACL;gBACE,GAAG,EAAE,IAAI;YACV,CAAA;YACD;gBACE,GAAG,EAAE,GAAG;gBACR,QAAQ,GAAE,IAAI,GAAK,IAAoB,CAAC,KAAK,CAAC,SAAS,KAAK,QAAQ,IAAI,IAAI;YAC7E,CAAA;YACD;gBACE,KAAK,EAAE,mBAAmB;gBAC1B,SAAS,EAAE,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;YAChD,CAAA;YACD;gBACE,KAAK,EAAE,mBAAmB;YAC3B,CAAA;SACF;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,IAAI;kKAAE,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KAC/E;IAED,WAAW,GAAA;QACT,OAAO;YACL,SAAS,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAChC,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;iBACnC;YACD,YAAY,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACnC,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtC;YACD,WAAW,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAClC,OAAO,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;iBACrC;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;YAClD,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;SACnD;KACF;IAED,aAAa,GAAA;QACX,OAAO;kKACL,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;aACF,qKAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;IAED,aAAa,GAAA;QACX,OAAO;kKACL,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;kKACF,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/extension-strike/src/strike.ts"], "sourcesContent": ["import {\n  Mark,\n  markInputRule,\n  markPasteRule,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface StrikeOptions {\n  /**\n   * HTML attributes to add to the strike element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    strike: {\n      /**\n       * Set a strike mark\n       * @example editor.commands.setStrike()\n       */\n      setStrike: () => ReturnType,\n      /**\n       * Toggle a strike mark\n       * @example editor.commands.toggleStrike()\n       */\n      toggleStrike: () => ReturnType,\n      /**\n       * Unset a strike mark\n       * @example editor.commands.unsetStrike()\n       */\n      unsetStrike: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches a strike to a ~~strike~~ on input.\n */\nexport const inputRegex = /(?:^|\\s)(~~(?!\\s+~~)((?:[^~]+))~~(?!\\s+~~))$/\n\n/**\n * Matches a strike to a ~~strike~~ on paste.\n */\nexport const pasteRegex = /(?:^|\\s)(~~(?!\\s+~~)((?:[^~]+))~~(?!\\s+~~))/g\n\n/**\n * This extension allows you to create strike text.\n * @see https://www.tiptap.dev/api/marks/strike\n */\nexport const Strike = Mark.create<StrikeOptions>({\n  name: 'strike',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 's',\n      },\n      {\n        tag: 'del',\n      },\n      {\n        tag: 'strike',\n      },\n      {\n        style: 'text-decoration',\n        consuming: false,\n        getAttrs: style => ((style as string).includes('line-through') ? {} : false),\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['s', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setStrike: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleStrike: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetStrike: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-s': () => this.editor.commands.toggleStrike(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: inputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: pasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;AAsCA;;CAEG,GACI,MAAM,UAAU,GAAG;AAE1B;;CAEG,GACI,MAAM,UAAU,GAAG;AAE1B;;;CAGG,GACU,MAAA,MAAM,qJAAG,OAAI,CAAC,MAAM,CAAgB;IAC/C,IAAI,EAAE,QAAQ;IAEd,UAAU,GAAA;QACR,OAAO;YACL,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,SAAS,GAAA;QACP,OAAO;YACL;gBACE,GAAG,EAAE,GAAG;YACT,CAAA;YACD;gBACE,GAAG,EAAE,KAAK;YACX,CAAA;YACD;gBACE,GAAG,EAAE,QAAQ;YACd,CAAA;YACD;gBACE,KAAK,EAAE,iBAAiB;gBACxB,SAAS,EAAE,KAAK;gBAChB,QAAQ,GAAE,KAAK,GAAM,KAAgB,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAA,CAAE,GAAG,KAAK,CAAC;YAC7E,CAAA;SACF;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,GAAG;kKAAE,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KAC9E;IAED,WAAW,GAAA;QACT,OAAO;YACL,SAAS,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAChC,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;iBACnC;YACD,YAAY,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACnC,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtC;YACD,WAAW,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBAClC,OAAO,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;iBACrC;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,aAAa,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;SACzD;KACF;IAED,aAAa,GAAA;QACX,OAAO;aACL,qKAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;IAED,aAAa,GAAA;QACX,OAAO;kKACL,gBAAA,AAAa,EAAC;gBACZ,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/extension-underline/src/underline.ts"], "sourcesContent": ["import { Mark, mergeAttributes } from '@tiptap/core'\n\nexport interface UnderlineOptions {\n  /**\n   * HTML attributes to add to the underline element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    underline: {\n      /**\n       * Set an underline mark\n       * @example editor.commands.setUnderline()\n       */\n      setUnderline: () => ReturnType,\n      /**\n       * Toggle an underline mark\n       * @example editor.commands.toggleUnderline()\n       */\n      toggleUnderline: () => ReturnType,\n      /**\n       * Unset an underline mark\n       * @example editor.commands.unsetUnderline()\n       */\n      unsetUnderline: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to create underline text.\n * @see https://www.tiptap.dev/api/marks/underline\n */\nexport const Underline = Mark.create<UnderlineOptions>({\n  name: 'underline',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'u',\n      },\n      {\n        style: 'text-decoration',\n        consuming: false,\n        getAttrs: style => ((style as string).includes('underline') ? {} : false),\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['u', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setUnderline: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleUnderline: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetUnderline: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-u': () => this.editor.commands.toggleUnderline(),\n      'Mod-U': () => this.editor.commands.toggleUnderline(),\n    }\n  },\n})\n"], "names": [], "mappings": ";;;;;;AAiCA;;;CAGG,GACU,MAAA,SAAS,qJAAG,OAAI,CAAC,MAAM,CAAmB;IACrD,IAAI,EAAE,WAAW;IAEjB,UAAU,GAAA;QACR,OAAO;YACL,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,SAAS,GAAA;QACP,OAAO;YACL;gBACE,GAAG,EAAE,GAAG;YACT,CAAA;YACD;gBACE,KAAK,EAAE,iBAAiB;gBACxB,SAAS,EAAE,KAAK;gBAChB,QAAQ,GAAE,KAAK,GAAM,KAAgB,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAA,CAAE,GAAG,KAAK,CAAC;YAC1E,CAAA;SACF;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,GAAG;kKAAE,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KAC9E;IAED,WAAW,GAAA;QACT,OAAO;YACL,YAAY,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACnC,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;iBACnC;YACD,eAAe,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACtC,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtC;YACD,cAAc,EAAE,IAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;oBACrC,OAAO,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;iBACrC;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE;YACrD,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE;SACtD;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/extension-table-cell/src/table-cell.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface TableCellOptions {\n  /**\n   * The HTML attributes for a table cell node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\n/**\n * This extension allows you to create table cells.\n * @see https://www.tiptap.dev/api/nodes/table-cell\n */\nexport const TableCell = Node.create<TableCellOptions>({\n  name: 'tableCell',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'block+',\n\n  addAttributes() {\n    return {\n      colspan: {\n        default: 1,\n      },\n      rowspan: {\n        default: 1,\n      },\n      colwidth: {\n        default: null,\n        parseHTML: element => {\n          const colwidth = element.getAttribute('colwidth')\n          const value = colwidth\n            ? colwidth.split(',').map(width => parseInt(width, 10))\n            : null\n\n          return value\n        },\n      },\n    }\n  },\n\n  tableRole: 'cell',\n\n  isolating: true,\n\n  parseHTML() {\n    return [\n      { tag: 'td' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['td', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n})\n"], "names": [], "mappings": ";;;;;;AAWA;;;CAGG,GACU,MAAA,SAAS,qJAAG,OAAI,CAAC,MAAM,CAAmB;IACrD,IAAI,EAAE,WAAW;IAEjB,UAAU,GAAA;QACR,OAAO;YACL,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,OAAO,EAAE,QAAQ;IAEjB,aAAa,GAAA;QACX,OAAO;YACL,OAAO,EAAE;gBACP,OAAO,EAAE,CAAC;YACX,CAAA;YACD,OAAO,EAAE;gBACP,OAAO,EAAE,CAAC;YACX,CAAA;YACD,QAAQ,EAAE;gBACR,OAAO,EAAE,IAAI;gBACb,SAAS,GAAE,OAAO,IAAG;oBACnB,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC;oBACjD,MAAM,KAAK,GAAG,WACV,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,IACpD,IAAI;oBAER,OAAO,KAAK;iBACb;YACF,CAAA;SACF;KACF;IAED,SAAS,EAAE,MAAM;IAEjB,SAAS,EAAE,IAAI;IAEf,SAAS,GAAA;QACP,OAAO;YACL;gBAAE,GAAG,EAAE,IAAI;YAAA,CAAE;SACd;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,IAAI;kKAAE,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KAC/E;AAEF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 701, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/extension-table-header/src/table-header.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface TableHeaderOptions {\n  /**\n   * The HTML attributes for a table header node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\n/**\n * This extension allows you to create table headers.\n * @see https://www.tiptap.dev/api/nodes/table-header\n */\nexport const TableHeader = Node.create<TableHeaderOptions>({\n  name: 'tableHeader',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'block+',\n\n  addAttributes() {\n    return {\n      colspan: {\n        default: 1,\n      },\n      rowspan: {\n        default: 1,\n      },\n      colwidth: {\n        default: null,\n        parseHTML: element => {\n          const colwidth = element.getAttribute('colwidth')\n          const value = colwidth\n            ? colwidth.split(',').map(width => parseInt(width, 10))\n            : null\n\n          return value\n        },\n      },\n    }\n  },\n\n  tableRole: 'header_cell',\n\n  isolating: true,\n\n  parseHTML() {\n    return [\n      { tag: 'th' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['th', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n})\n"], "names": [], "mappings": ";;;;;;AAWA;;;CAGG,GACU,MAAA,WAAW,qJAAG,OAAI,CAAC,MAAM,CAAqB;IACzD,IAAI,EAAE,aAAa;IAEnB,UAAU,GAAA;QACR,OAAO;YACL,cAAc,EAAE,CAAA,CAAE;SACnB;KACF;IAED,OAAO,EAAE,QAAQ;IAEjB,aAAa,GAAA;QACX,OAAO;YACL,OAAO,EAAE;gBACP,OAAO,EAAE,CAAC;YACX,CAAA;YACD,OAAO,EAAE;gBACP,OAAO,EAAE,CAAC;YACX,CAAA;YACD,QAAQ,EAAE;gBACR,OAAO,EAAE,IAAI;gBACb,SAAS,GAAE,OAAO,IAAG;oBACnB,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC;oBACjD,MAAM,KAAK,GAAG,WACV,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,IACpD,IAAI;oBAER,OAAO,KAAK;iBACb;YACF,CAAA;SACF;KACF;IAED,SAAS,EAAE,aAAa;IAExB,SAAS,EAAE,IAAI;IAEf,SAAS,GAAA;QACP,OAAO;YACL;gBAAE,GAAG,EAAE,IAAI;YAAA,CAAE;SACd;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,OAAO;YAAC,IAAI;kKAAE,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KAC/E;AAEF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/extension-gapcursor/src/gapcursor.ts"], "sourcesContent": ["import {\n  callOrReturn,\n  Extension,\n  getExtensionField,\n  ParentConfig,\n} from '@tiptap/core'\nimport { gapCursor } from '@tiptap/pm/gapcursor'\n\ndeclare module '@tiptap/core' {\n  interface NodeConfig<Options, Storage> {\n    /**\n     * A function to determine whether the gap cursor is allowed at the current position. Must return `true` or `false`.\n     * @default null\n     */\n    allowGapCursor?:\n      | boolean\n      | null\n      | ((this: {\n        name: string,\n        options: Options,\n        storage: Storage,\n        parent: ParentConfig<NodeConfig<Options>>['allowGapCursor'],\n      }) => boolean | null),\n  }\n}\n\n/**\n * This extension allows you to add a gap cursor to your editor.\n * A gap cursor is a cursor that appears when you click on a place\n * where no content is present, for example inbetween nodes.\n * @see https://tiptap.dev/api/extensions/gapcursor\n */\nexport const Gapcursor = Extension.create({\n  name: 'gapCursor',\n\n  addProseMirrorPlugins() {\n    return [\n      gapCursor(),\n    ]\n  },\n\n  extendNodeSchema(extension) {\n    const context = {\n      name: extension.name,\n      options: extension.options,\n      storage: extension.storage,\n    }\n\n    return {\n      allowGapCursor: callOrReturn(getExtensionField(extension, 'allowGapCursor', context)) ?? null,\n    }\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;AA0BA;;;;;CAKG,GACU,MAAA,SAAS,qJAAG,YAAS,CAAC,MAAM,CAAC;IACxC,IAAI,EAAE,WAAW;IAEjB,qBAAqB,GAAA;QACnB,OAAO;aACL,yKAAA,AAAS,EAAE;SACZ;KACF;IAED,gBAAgB,EAAC,SAAS,EAAA;;QACxB,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,OAAO,EAAE,SAAS,CAAC,OAAO;YAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;SAC3B;QAED,OAAO;YACL,cAAc,EAAE,CAAA,EAAA,yJAAA,eAAA,AAAY,wJAAC,oBAAA,AAAiB,EAAC,SAAS,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,IAAI;SAC9F;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 802, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/extension-history/src/history.ts"], "sourcesContent": ["import { Extension } from '@tiptap/core'\nimport { history, redo, undo } from '@tiptap/pm/history'\n\nexport interface HistoryOptions {\n  /**\n   * The amount of history events that are collected before the oldest events are discarded.\n   * @default 100\n   * @example 50\n   */\n  depth: number,\n\n  /**\n   * The delay (in milliseconds) between changes after which a new group should be started.\n   * @default 500\n   * @example 1000\n   */\n  newGroupDelay: number,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    history: {\n      /**\n       * Undo recent changes\n       * @example editor.commands.undo()\n       */\n      undo: () => ReturnType,\n      /**\n       * Reapply reverted changes\n       * @example editor.commands.redo()\n       */\n      redo: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to undo and redo recent changes.\n * @see https://www.tiptap.dev/api/extensions/history\n *\n * **Important**: If the `@tiptap/extension-collaboration` package is used, make sure to remove\n * the `history` extension, as it is not compatible with the `collaboration` extension.\n *\n * `@tiptap/extension-collaboration` uses its own history implementation.\n */\nexport const History = Extension.create<HistoryOptions>({\n  name: 'history',\n\n  addOptions() {\n    return {\n      depth: 100,\n      newGroupDelay: 500,\n    }\n  },\n\n  addCommands() {\n    return {\n      undo: () => ({ state, dispatch }) => {\n        return undo(state, dispatch)\n      },\n      redo: () => ({ state, dispatch }) => {\n        return redo(state, dispatch)\n      },\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      history(this.options),\n    ]\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-z': () => this.editor.commands.undo(),\n      'Shift-Mod-z': () => this.editor.commands.redo(),\n      'Mod-y': () => this.editor.commands.redo(),\n\n      // Russian keyboard layouts\n      'Mod-я': () => this.editor.commands.undo(),\n      'Shift-Mod-я': () => this.editor.commands.redo(),\n    }\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;AAoCA;;;;;;;;CAQG,GACU,MAAA,OAAO,qJAAG,YAAS,CAAC,MAAM,CAAiB;IACtD,IAAI,EAAE,SAAS;IAEf,UAAU,GAAA;QACR,OAAO;YACL,KAAK,EAAE,GAAG;YACV,aAAa,EAAE,GAAG;SACnB;KACF;IAED,WAAW,GAAA;QACT,OAAO;YACL,IAAI,EAAE,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;oBAClC,mKAAO,OAAA,AAAI,EAAC,KAAK,EAAE,QAAQ,CAAC;iBAC7B;YACD,IAAI,EAAE,IAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;oBAClC,mKAAO,OAAA,AAAI,EAAC,KAAK,EAAE,QAAQ,CAAC;iBAC7B;SACF;KACF;IAED,qBAAqB,GAAA;QACnB,OAAO;wKACL,UAAA,AAAO,EAAC,IAAI,CAAC,OAAO,CAAC;SACtB;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;YAC1C,aAAa,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;YAChD,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;;YAG1C,OAAO,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;YAC1C,aAAa,EAAE,IAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;SACjD;KACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 861, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/extension-link/src/helpers/autolink.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/extension-link/src/helpers/clickHandler.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/extension-link/src/helpers/pasteHandler.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/extension-link/src/link.ts"], "sourcesContent": ["import {\n  combineTransactionSteps,\n  findChildrenInRange,\n  getChangedRanges,\n  getMarksBetween,\n  NodeWithPos,\n} from '@tiptap/core'\nimport { MarkType } from '@tiptap/pm/model'\nimport { Plugin, PluginKey } from '@tiptap/pm/state'\nimport { MultiToken, tokenize } from 'linkifyjs'\n\n/**\n * Check if the provided tokens form a valid link structure, which can either be a single link token\n * or a link token surrounded by parentheses or square brackets.\n *\n * This ensures that only complete and valid text is hyperlinked, preventing cases where a valid\n * top-level domain (TLD) is immediately followed by an invalid character, like a number. For\n * example, with the `find` method from Linkify, entering `example.com1` would result in\n * `example.com` being linked and the trailing `1` left as plain text. By using the `tokenize`\n * method, we can perform more comprehensive validation on the input text.\n */\nfunction isValidLinkStructure(tokens: Array<ReturnType<MultiToken['toObject']>>) {\n  if (tokens.length === 1) {\n    return tokens[0].isLink\n  }\n\n  if (tokens.length === 3 && tokens[1].isLink) {\n    return ['()', '[]'].includes(tokens[0].value + tokens[2].value)\n  }\n\n  return false\n}\n\ntype AutolinkOptions = {\n  type: MarkType\n  defaultProtocol: string\n  validate: (url: string) => boolean\n  shouldAutoLink: (url: string) => boolean\n}\n\n/**\n * This plugin allows you to automatically add links to your editor.\n * @param options The plugin options\n * @returns The plugin instance\n */\nexport function autolink(options: AutolinkOptions): Plugin {\n  return new Plugin({\n    key: new PluginKey('autolink'),\n    appendTransaction: (transactions, oldState, newState) => {\n      /**\n       * Does the transaction change the document?\n       */\n      const docChanges = transactions.some(transaction => transaction.docChanged) && !oldState.doc.eq(newState.doc)\n\n      /**\n       * Prevent autolink if the transaction is not a document change or if the transaction has the meta `preventAutolink`.\n       */\n      const preventAutolink = transactions.some(transaction => transaction.getMeta('preventAutolink'))\n\n      /**\n       * Prevent autolink if the transaction is not a document change\n       * or if the transaction has the meta `preventAutolink`.\n       */\n      if (!docChanges || preventAutolink) {\n        return\n      }\n\n      const { tr } = newState\n      const transform = combineTransactionSteps(oldState.doc, [...transactions])\n      const changes = getChangedRanges(transform)\n\n      changes.forEach(({ newRange }) => {\n        // Now let’s see if we can add new links.\n        const nodesInChangedRanges = findChildrenInRange(\n          newState.doc,\n          newRange,\n          node => node.isTextblock,\n        )\n\n        let textBlock: NodeWithPos | undefined\n        let textBeforeWhitespace: string | undefined\n\n        if (nodesInChangedRanges.length > 1) {\n          // Grab the first node within the changed ranges (ex. the first of two paragraphs when hitting enter).\n          textBlock = nodesInChangedRanges[0]\n          textBeforeWhitespace = newState.doc.textBetween(\n            textBlock.pos,\n            textBlock.pos + textBlock.node.nodeSize,\n            undefined,\n            ' ',\n          )\n        } else if (\n          nodesInChangedRanges.length\n          // We want to make sure to include the block seperator argument to treat hard breaks like spaces.\n          && newState.doc.textBetween(newRange.from, newRange.to, ' ', ' ').endsWith(' ')\n        ) {\n          textBlock = nodesInChangedRanges[0]\n          textBeforeWhitespace = newState.doc.textBetween(\n            textBlock.pos,\n            newRange.to,\n            undefined,\n            ' ',\n          )\n        }\n\n        if (textBlock && textBeforeWhitespace) {\n          const wordsBeforeWhitespace = textBeforeWhitespace.split(' ').filter(s => s !== '')\n\n          if (wordsBeforeWhitespace.length <= 0) {\n            return false\n          }\n\n          const lastWordBeforeSpace = wordsBeforeWhitespace[wordsBeforeWhitespace.length - 1]\n          const lastWordAndBlockOffset = textBlock.pos + textBeforeWhitespace.lastIndexOf(lastWordBeforeSpace)\n\n          if (!lastWordBeforeSpace) {\n            return false\n          }\n\n          const linksBeforeSpace = tokenize(lastWordBeforeSpace).map(t => t.toObject(options.defaultProtocol))\n\n          if (!isValidLinkStructure(linksBeforeSpace)) {\n            return false\n          }\n\n          linksBeforeSpace\n            .filter(link => link.isLink)\n            // Calculate link position.\n            .map(link => ({\n              ...link,\n              from: lastWordAndBlockOffset + link.start + 1,\n              to: lastWordAndBlockOffset + link.end + 1,\n            }))\n            // ignore link inside code mark\n            .filter(link => {\n              if (!newState.schema.marks.code) {\n                return true\n              }\n\n              return !newState.doc.rangeHasMark(\n                link.from,\n                link.to,\n                newState.schema.marks.code,\n              )\n            })\n            // validate link\n            .filter(link => options.validate(link.value))\n            // check whether should autolink\n            .filter(link => options.shouldAutoLink(link.value))\n            // Add link mark.\n            .forEach(link => {\n              if (getMarksBetween(link.from, link.to, newState.doc).some(item => item.mark.type === options.type)) {\n                return\n              }\n\n              tr.addMark(\n                link.from,\n                link.to,\n                options.type.create({\n                  href: link.href,\n                }),\n              )\n            })\n        }\n      })\n\n      if (!tr.steps.length) {\n        return\n      }\n\n      return tr\n    },\n  })\n}\n", "import { getAttributes } from '@tiptap/core'\nimport { MarkType } from '@tiptap/pm/model'\nimport { Plugin, PluginKey } from '@tiptap/pm/state'\n\ntype ClickHandlerOptions = {\n  type: MarkType;\n}\n\nexport function clickHandler(options: ClickHandlerOptions): Plugin {\n  return new Plugin({\n    key: new PluginKey('handleClickLink'),\n    props: {\n      handleClick: (view, pos, event) => {\n        if (event.button !== 0) {\n          return false\n        }\n\n        if (!view.editable) {\n          return false\n        }\n\n        let a = event.target as HTMLElement\n        const els = []\n\n        while (a.nodeName !== 'DIV') {\n          els.push(a)\n          a = a.parentNode as HTMLElement\n        }\n\n        if (!els.find(value => value.nodeName === 'A')) {\n          return false\n        }\n\n        const attrs = getAttributes(view.state, options.type.name)\n        const link = (event.target as HTMLAnchorElement)\n\n        const href = link?.href ?? attrs.href\n        const target = link?.target ?? attrs.target\n\n        if (link && href) {\n          window.open(href, target)\n\n          return true\n        }\n\n        return false\n      },\n    },\n  })\n}\n", "import { Editor } from '@tiptap/core'\nimport { MarkType } from '@tiptap/pm/model'\nimport { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\nimport { find } from 'linkifyjs'\n\ntype PasteHandlerOptions = {\n  editor: Editor\n  defaultProtocol: string\n  type: MarkType\n}\n\nexport function pasteHandler(options: PasteHandlerOptions): Plugin {\n  return new Plugin({\n    key: new Plugin<PERSON>ey('handlePasteLink'),\n    props: {\n      handlePaste: (view, event, slice) => {\n        const { state } = view\n        const { selection } = state\n        const { empty } = selection\n\n        if (empty) {\n          return false\n        }\n\n        let textContent = ''\n\n        slice.content.forEach(node => {\n          textContent += node.textContent\n        })\n\n        const link = find(textContent, { defaultProtocol: options.defaultProtocol }).find(item => item.isLink && item.value === textContent)\n\n        if (!textContent || !link) {\n          return false\n        }\n\n        return options.editor.commands.setMark(options.type, {\n          href: link.href,\n        })\n      },\n    },\n  })\n}\n", "import {\n  <PERSON>, mark<PERSON>aste<PERSON><PERSON>, merge<PERSON>ttribut<PERSON>, PasteRuleMatch,\n} from '@tiptap/core'\nimport { Plugin } from '@tiptap/pm/state'\nimport { find, registerCustomProtocol, reset } from 'linkifyjs'\n\nimport { autolink } from './helpers/autolink.js'\nimport { clickHandler } from './helpers/clickHandler.js'\nimport { pasteHandler } from './helpers/pasteHandler.js'\n\nexport interface LinkProtocolOptions {\n  /**\n   * The protocol scheme to be registered.\n   * @default '''\n   * @example 'ftp'\n   * @example 'git'\n   */\n  scheme: string;\n\n  /**\n   * If enabled, it allows optional slashes after the protocol.\n   * @default false\n   * @example true\n   */\n  optionalSlashes?: boolean;\n}\n\nexport const pasteRegex = /https?:\\/\\/(?:www\\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\\.[a-zA-Z]{2,}\\b(?:[-a-zA-Z0-9@:%._+~#=?!&/]*)(?:[-a-zA-Z0-9@:%._+~#=?!&/]*)/gi\n\n/**\n * @deprecated The default behavior is now to open links when the editor is not editable.\n */\ntype DeprecatedOpenWhenNotEditable = 'whenNotEditable';\n\nexport interface LinkOptions {\n  /**\n   * If enabled, the extension will automatically add links as you type.\n   * @default true\n   * @example false\n   */\n  autolink: boolean;\n\n  /**\n   * An array of custom protocols to be registered with linkifyjs.\n   * @default []\n   * @example ['ftp', 'git']\n   */\n  protocols: Array<LinkProtocolOptions | string>;\n\n  /**\n   * Default protocol to use when no protocol is specified.\n   * @default 'http'\n   */\n  defaultProtocol: string;\n  /**\n   * If enabled, links will be opened on click.\n   * @default true\n   * @example false\n   */\n  openOnClick: boolean | DeprecatedOpenWhenNotEditable;\n  /**\n   * Adds a link to the current selection if the pasted content only contains an url.\n   * @default true\n   * @example false\n   */\n  linkOnPaste: boolean;\n\n  /**\n   * HTML attributes to add to the link element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>;\n\n  /**\n   * @deprecated Use the `shouldAutoLink` option instead.\n   * A validation function that modifies link verification for the auto linker.\n   * @param url - The url to be validated.\n   * @returns - True if the url is valid, false otherwise.\n   */\n  validate: (url: string) => boolean;\n\n  /**\n   * A validation function which is used for configuring link verification for preventing XSS attacks.\n   * Only modify this if you know what you're doing.\n   *\n   * @returns {boolean} `true` if the URL is valid, `false` otherwise.\n   *\n   * @example\n   * isAllowedUri: (url, { defaultValidate, protocols, defaultProtocol }) => {\n   * return url.startsWith('./') || defaultValidate(url)\n   * }\n   */\n  isAllowedUri: (\n    /**\n     * The URL to be validated.\n     */\n    url: string,\n    ctx: {\n      /**\n       * The default validation function.\n       */\n      defaultValidate: (url: string) => boolean;\n      /**\n       * An array of allowed protocols for the URL (e.g., \"http\", \"https\"). As defined in the `protocols` option.\n       */\n      protocols: Array<LinkProtocolOptions | string>;\n      /**\n       * A string that represents the default protocol (e.g., 'http'). As defined in the `defaultProtocol` option.\n       */\n      defaultProtocol: string;\n    }\n  ) => boolean;\n\n  /**\n   * Determines whether a valid link should be automatically linked in the content.\n   *\n   * @param {string} url - The URL that has already been validated.\n   * @returns {boolean} - True if the link should be auto-linked; false if it should not be auto-linked.\n   */\n  shouldAutoLink: (url: string) => boolean;\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    link: {\n      /**\n       * Set a link mark\n       * @param attributes The link attributes\n       * @example editor.commands.setLink({ href: 'https://tiptap.dev' })\n       */\n      setLink: (attributes: {\n        href: string;\n        target?: string | null;\n        rel?: string | null;\n        class?: string | null;\n      }) => ReturnType;\n      /**\n       * Toggle a link mark\n       * @param attributes The link attributes\n       * @example editor.commands.toggleLink({ href: 'https://tiptap.dev' })\n       */\n      toggleLink: (attributes: {\n        href: string;\n        target?: string | null;\n        rel?: string | null;\n        class?: string | null;\n      }) => ReturnType;\n      /**\n       * Unset a link mark\n       * @example editor.commands.unsetLink()\n       */\n      unsetLink: () => ReturnType;\n    };\n  }\n}\n\n// From DOMPurify\n// https://github.com/cure53/DOMPurify/blob/main/src/regexp.js\n// eslint-disable-next-line no-control-regex\nconst ATTR_WHITESPACE = /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g\n\nexport function isAllowedUri(uri: string | undefined, protocols?: LinkOptions['protocols']) {\n  const allowedProtocols: string[] = [\n    'http',\n    'https',\n    'ftp',\n    'ftps',\n    'mailto',\n    'tel',\n    'callto',\n    'sms',\n    'cid',\n    'xmpp',\n  ]\n\n  if (protocols) {\n    protocols.forEach(protocol => {\n      const nextProtocol = typeof protocol === 'string' ? protocol : protocol.scheme\n\n      if (nextProtocol) {\n        allowedProtocols.push(nextProtocol)\n      }\n    })\n  }\n\n  return (\n    !uri\n    || uri\n      .replace(ATTR_WHITESPACE, '')\n      .match(\n        new RegExp(\n          // eslint-disable-next-line no-useless-escape\n          `^(?:(?:${allowedProtocols.join('|')}):|[^a-z]|[a-z0-9+.\\-]+(?:[^a-z+.\\-:]|$))`,\n          'i',\n        ),\n      )\n  )\n}\n\n/**\n * This extension allows you to create links.\n * @see https://www.tiptap.dev/api/marks/link\n */\nexport const Link = Mark.create<LinkOptions>({\n  name: 'link',\n\n  priority: 1000,\n\n  keepOnSplit: false,\n\n  exitable: true,\n\n  onCreate() {\n    if (this.options.validate && !this.options.shouldAutoLink) {\n      // Copy the validate function to the shouldAutoLink option\n      this.options.shouldAutoLink = this.options.validate\n      console.warn(\n        'The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.',\n      )\n    }\n    this.options.protocols.forEach(protocol => {\n      if (typeof protocol === 'string') {\n        registerCustomProtocol(protocol)\n        return\n      }\n      registerCustomProtocol(protocol.scheme, protocol.optionalSlashes)\n    })\n  },\n\n  onDestroy() {\n    reset()\n  },\n\n  inclusive() {\n    return this.options.autolink\n  },\n\n  addOptions() {\n    return {\n      openOnClick: true,\n      linkOnPaste: true,\n      autolink: true,\n      protocols: [],\n      defaultProtocol: 'http',\n      HTMLAttributes: {\n        target: '_blank',\n        rel: 'noopener noreferrer nofollow',\n        class: null,\n      },\n      isAllowedUri: (url, ctx) => !!isAllowedUri(url, ctx.protocols),\n      validate: url => !!url,\n      shouldAutoLink: url => !!url,\n    }\n  },\n\n  addAttributes() {\n    return {\n      href: {\n        default: null,\n        parseHTML(element) {\n          return element.getAttribute('href')\n        },\n      },\n      target: {\n        default: this.options.HTMLAttributes.target,\n      },\n      rel: {\n        default: this.options.HTMLAttributes.rel,\n      },\n      class: {\n        default: this.options.HTMLAttributes.class,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'a[href]',\n        getAttrs: dom => {\n          const href = (dom as HTMLElement).getAttribute('href')\n\n          // prevent XSS attacks\n          if (\n            !href\n            || !this.options.isAllowedUri(href, {\n              defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n              protocols: this.options.protocols,\n              defaultProtocol: this.options.defaultProtocol,\n            })\n          ) {\n            return false\n          }\n          return null\n        },\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    // prevent XSS attacks\n    if (\n      !this.options.isAllowedUri(HTMLAttributes.href, {\n        defaultValidate: href => !!isAllowedUri(href, this.options.protocols),\n        protocols: this.options.protocols,\n        defaultProtocol: this.options.defaultProtocol,\n      })\n    ) {\n      // strip out the href\n      return [\n        'a',\n        mergeAttributes(this.options.HTMLAttributes, { ...HTMLAttributes, href: '' }),\n        0,\n      ]\n    }\n\n    return ['a', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setLink:\n        attributes => ({ chain }) => {\n          const { href } = attributes\n\n          if (!this.options.isAllowedUri(href, {\n            defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n            protocols: this.options.protocols,\n            defaultProtocol: this.options.defaultProtocol,\n          })) {\n            return false\n          }\n\n          return chain().setMark(this.name, attributes).setMeta('preventAutolink', true).run()\n        },\n\n      toggleLink:\n        attributes => ({ chain }) => {\n          const { href } = attributes\n\n          if (!this.options.isAllowedUri(href, {\n            defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n            protocols: this.options.protocols,\n            defaultProtocol: this.options.defaultProtocol,\n          })) {\n            return false\n          }\n\n          return chain()\n            .toggleMark(this.name, attributes, { extendEmptyMarkRange: true })\n            .setMeta('preventAutolink', true)\n            .run()\n        },\n\n      unsetLink:\n        () => ({ chain }) => {\n          return chain()\n            .unsetMark(this.name, { extendEmptyMarkRange: true })\n            .setMeta('preventAutolink', true)\n            .run()\n        },\n    }\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: text => {\n          const foundLinks: PasteRuleMatch[] = []\n\n          if (text) {\n            const { protocols, defaultProtocol } = this.options\n            const links = find(text).filter(\n              item => item.isLink\n                && this.options.isAllowedUri(item.value, {\n                  defaultValidate: href => !!isAllowedUri(href, protocols),\n                  protocols,\n                  defaultProtocol,\n                }),\n            )\n\n            if (links.length) {\n              links.forEach(link => foundLinks.push({\n                text: link.value,\n                data: {\n                  href: link.href,\n                },\n                index: link.start,\n              }))\n            }\n          }\n\n          return foundLinks\n        },\n        type: this.type,\n        getAttributes: match => {\n          return {\n            href: match.data?.href,\n          }\n        },\n      }),\n    ]\n  },\n\n  addProseMirrorPlugins() {\n    const plugins: Plugin[] = []\n    const { protocols, defaultProtocol } = this.options\n\n    if (this.options.autolink) {\n      plugins.push(\n        autolink({\n          type: this.type,\n          defaultProtocol: this.options.defaultProtocol,\n          validate: url => this.options.isAllowedUri(url, {\n            defaultValidate: href => !!isAllowedUri(href, protocols),\n            protocols,\n            defaultProtocol,\n          }),\n          shouldAutoLink: this.options.shouldAutoLink,\n        }),\n      )\n    }\n\n    if (this.options.openOnClick === true) {\n      plugins.push(\n        clickHandler({\n          type: this.type,\n        }),\n      )\n    }\n\n    if (this.options.linkOnPaste) {\n      plugins.push(\n        pasteHandler({\n          editor: this.editor,\n          defaultProtocol: this.options.defaultProtocol,\n          type: this.type,\n        }),\n      )\n    }\n\n    return plugins\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAWA;;;;;;;;;CASG,GACH,SAAS,oBAAoB,CAAC,MAAiD,EAAA;IAC7E,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACvB,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM;;IAGzB,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;QAC3C,OAAO;YAAC,IAAI;YAAE,IAAI;SAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;;IAGjE,OAAO,KAAK;AACd;AASA;;;;CAIG,GACG,SAAU,QAAQ,CAAC,OAAwB,EAAA;IAC/C,OAAO,0JAAI,SAAM,CAAC;QAChB,GAAG,EAAE,0JAAI,YAAS,CAAC,UAAU,CAAC;QAC9B,iBAAiB,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,QAAQ,KAAI;YACtD;;aAEG,GACH,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,EAAC,WAAW,GAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC;YAE7G;;aAEG,GACH,MAAM,eAAe,GAAG,YAAY,CAAC,IAAI,EAAC,WAAW,GAAI,WAAW,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAEhG;;;aAGG,GACH,IAAI,CAAC,UAAU,IAAI,eAAe,EAAE;gBAClC;;YAGF,MAAM,EAAE,EAAE,EAAE,GAAG,QAAQ;YACvB,MAAM,SAAS,yJAAG,0BAAA,AAAuB,EAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;mBAAG,YAAY;aAAC,CAAC;YAC1E,MAAM,OAAO,yJAAG,mBAAA,AAAgB,EAAC,SAAS,CAAC;YAE3C,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAI;;gBAE/B,MAAM,oBAAoB,wJAAG,uBAAA,AAAmB,EAC9C,QAAQ,CAAC,GAAG,EACZ,QAAQ,GACR,IAAI,GAAI,IAAI,CAAC,WAAW,CACzB;gBAED,IAAI,SAAkC;gBACtC,IAAI,oBAAwC;gBAE5C,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;;oBAEnC,SAAS,GAAG,oBAAoB,CAAC,CAAC,CAAC;oBACnC,oBAAoB,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAC7C,SAAS,CAAC,GAAG,EACb,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ,EACvC,SAAS,EACT,GAAG,CACJ;uBACI,IACL,oBAAoB,CAAC,MAAA,IAElB,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAC/E;oBACA,SAAS,GAAG,oBAAoB,CAAC,CAAC,CAAC;oBACnC,oBAAoB,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAC7C,SAAS,CAAC,GAAG,EACb,QAAQ,CAAC,EAAE,EACX,SAAS,EACT,GAAG,CACJ;;gBAGH,IAAI,SAAS,IAAI,oBAAoB,EAAE;oBACrC,MAAM,qBAAqB,GAAG,oBAAoB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,EAAC,CAAC,GAAI,CAAC,KAAK,EAAE,CAAC;oBAEnF,IAAI,qBAAqB,CAAC,MAAM,IAAI,CAAC,EAAE;wBACrC,OAAO,KAAK;;oBAGd,MAAM,mBAAmB,GAAG,qBAAqB,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC;oBACnF,MAAM,sBAAsB,GAAG,SAAS,CAAC,GAAG,GAAG,oBAAoB,CAAC,WAAW,CAAC,mBAAmB,CAAC;oBAEpG,IAAI,CAAC,mBAAmB,EAAE;wBACxB,OAAO,KAAK;;oBAGd,MAAM,gBAAgB,GAAG,6JAAA,AAAQ,EAAC,mBAAmB,CAAC,CAAC,GAAG,EAAC,CAAC,GAAI,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;oBAEpG,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,EAAE;wBAC3C,OAAO,KAAK;;oBAGd,iBACG,MAAM,EAAC,IAAI,GAAI,IAAI,CAAC,MAAM;qBAE1B,GAAG,CAAC,IAAI,IAAA,CAAK;4BACZ,GAAG,IAAI;4BACP,IAAI,EAAE,sBAAsB,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC;4BAC7C,EAAE,EAAE,sBAAsB,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;wBAC1C,CAAA,CAAC;qBAED,MAAM,EAAC,IAAI,IAAG;wBACb,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE;4BAC/B,OAAO,IAAI;;wBAGb,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,CAC/B,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,EAAE,EACP,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAC3B;oBACH,CAAC;qBAEA,MAAM,EAAC,IAAI,GAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;qBAE3C,MAAM,EAAC,IAAI,GAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;qBAEjD,OAAO,CAAC,IAAI,IAAG;wBACd,0JAAI,kBAAA,AAAe,EAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,EAAC,IAAI,GAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,EAAE;4BACnG;;wBAGF,EAAE,CAAC,OAAO,CACR,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,EAAE,EACP,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;4BAClB,IAAI,EAAE,IAAI,CAAC,IAAI;wBAChB,CAAA,CAAC,CACH;oBACH,CAAC,CAAC;;YAER,CAAC,CAAC;YAEF,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE;gBACpB;;YAGF,OAAO,EAAE;SACV;IACF,CAAA,CAAC;AACJ;ACrKM,SAAU,YAAY,CAAC,OAA4B,EAAA;IACvD,OAAO,0JAAI,SAAM,CAAC;QAChB,GAAG,EAAE,yJAAI,aAAS,CAAC,iBAAiB,CAAC;QACrC,KAAK,EAAE;YACL,WAAW,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,KAAI;;gBAChC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;oBACtB,OAAO,KAAK;;gBAGd,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;oBAClB,OAAO,KAAK;;gBAGd,IAAI,CAAC,GAAG,KAAK,CAAC,MAAqB;gBACnC,MAAM,GAAG,GAAG,EAAE;gBAEd,MAAO,CAAC,CAAC,QAAQ,KAAK,KAAK,CAAE;oBAC3B,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;oBACX,CAAC,GAAG,CAAC,CAAC,UAAyB;;gBAGjC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAC,KAAK,GAAI,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,EAAE;oBAC9C,OAAO,KAAK;;gBAGd,MAAM,KAAK,yJAAG,gBAAA,AAAa,EAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC1D,MAAM,IAAI,GAAI,KAAK,CAAC,MAA4B;gBAEhD,MAAM,IAAI,GAAG,CAAA,EAAA,GAAA,IAAI,KAAA,QAAJ,IAAI,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAJ,IAAI,CAAE,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,KAAK,CAAC,IAAI;gBACrC,MAAM,MAAM,GAAG,CAAA,EAAA,GAAA,IAAI,KAAA,QAAJ,IAAI,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAJ,IAAI,CAAE,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,KAAK,CAAC,MAAM;gBAE3C,IAAI,IAAI,IAAI,IAAI,EAAE;oBAChB,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;oBAEzB,OAAO,IAAI;;gBAGb,OAAO,KAAK;aACb;QACF,CAAA;IACF,CAAA,CAAC;AACJ;ACtCM,SAAU,YAAY,CAAC,OAA4B,EAAA;IACvD,OAAO,0JAAI,SAAM,CAAC;QAChB,GAAG,EAAE,0JAAI,YAAS,CAAC,iBAAiB,CAAC;QACrC,KAAK,EAAE;YACL,WAAW,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,KAAI;gBAClC,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI;gBACtB,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK;gBAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS;gBAE3B,IAAI,KAAK,EAAE;oBACT,OAAO,KAAK;;gBAGd,IAAI,WAAW,GAAG,EAAE;gBAEpB,KAAK,CAAC,OAAO,CAAC,OAAO,EAAC,IAAI,IAAG;oBAC3B,WAAW,IAAI,IAAI,CAAC,WAAW;gBACjC,CAAC,CAAC;gBAEF,MAAM,IAAI,qJAAG,OAAA,AAAI,EAAC,WAAW,EAAE;oBAAE,eAAe,EAAE,OAAO,CAAC,eAAe;gBAAA,CAAE,CAAC,CAAC,IAAI,EAAC,IAAI,GAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,CAAC;gBAEpI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,EAAE;oBACzB,OAAO,KAAK;;gBAGd,OAAO,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;oBACnD,IAAI,EAAE,IAAI,CAAC,IAAI;gBAChB,CAAA,CAAC;aACH;QACF,CAAA;IACF,CAAA,CAAC;AACJ;ACfO,MAAM,UAAU,GAAG;AAkI1B,iBAAA;AACA,8DAAA;AACA,4CAAA;AACA,MAAM,eAAe,GAAG,6DAA6D;AAErE,SAAA,YAAY,CAAC,GAAuB,EAAE,SAAoC,EAAA;IACxF,MAAM,gBAAgB,GAAa;QACjC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;QACR,KAAK;QACL,QAAQ;QACR,KAAK;QACL,KAAK;QACL,MAAM;KACP;IAED,IAAI,SAAS,EAAE;QACb,SAAS,CAAC,OAAO,EAAC,QAAQ,IAAG;YAC3B,MAAM,YAAY,GAAG,OAAO,QAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,MAAM;YAE9E,IAAI,YAAY,EAAE;gBAChB,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC;;QAEvC,CAAC,CAAC;;IAGJ,OACE,CAAC,OACE,IACA,OAAO,CAAC,eAAe,EAAE,EAAE,EAC3B,KAAK,CACJ,IAAI,MAAM;IAER,CAAA,OAAA,EAAU,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAA2C,yCAAA,CAAA,EAC/E,GAAG,CACJ,CACF;AAEP;AAEA;;;CAGG,GACU,MAAA,IAAI,qJAAG,OAAI,CAAC,MAAM,CAAc;IAC3C,IAAI,EAAE,MAAM;IAEZ,QAAQ,EAAE,IAAI;IAEd,WAAW,EAAE,KAAK;IAElB,QAAQ,EAAE,IAAI;IAEd,QAAQ,GAAA;QACN,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;;YAEzD,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ;YACnD,OAAO,CAAC,IAAI,CACV,qFAAqF,CACtF;;QAEH,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAC,QAAQ,IAAG;YACxC,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;kKAChC,yBAAA,AAAsB,EAAC,QAAQ,CAAC;gBAChC;;aAEF,0KAAA,AAAsB,EAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,eAAe,CAAC;QACnE,CAAC,CAAC;KACH;IAED,SAAS,GAAA;0JACP,QAAA,AAAK,EAAE;KACR;IAED,SAAS,GAAA;QACP,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ;KAC7B;IAED,UAAU,GAAA;QACR,OAAO;YACL,WAAW,EAAE,IAAI;YACjB,WAAW,EAAE,IAAI;YACjB,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE,EAAE;YACb,eAAe,EAAE,MAAM;YACvB,cAAc,EAAE;gBACd,MAAM,EAAE,QAAQ;gBAChB,GAAG,EAAE,8BAA8B;gBACnC,KAAK,EAAE,IAAI;YACZ,CAAA;YACD,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,GAAK,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,SAAS,CAAC;YAC9D,QAAQ,GAAE,GAAG,GAAI,CAAC,CAAC,GAAG;YACtB,cAAc,GAAE,GAAG,GAAI,CAAC,CAAC,GAAG;SAC7B;KACF;IAED,aAAa,GAAA;QACX,OAAO;YACL,IAAI,EAAE;gBACJ,OAAO,EAAE,IAAI;gBACb,SAAS,EAAC,OAAO,EAAA;oBACf,OAAO,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC;iBACpC;YACF,CAAA;YACD,MAAM,EAAE;gBACN,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM;YAC5C,CAAA;YACD,GAAG,EAAE;gBACH,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG;YACzC,CAAA;YACD,KAAK,EAAE;gBACL,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK;YAC3C,CAAA;SACF;KACF;IAED,SAAS,GAAA;QACP,OAAO;YACL;gBACE,GAAG,EAAE,SAAS;gBACd,QAAQ,GAAE,GAAG,IAAG;oBACd,MAAM,IAAI,GAAI,GAAmB,CAAC,YAAY,CAAC,MAAM,CAAC;;oBAGtD,IACE,CAAC,QACE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE;wBAClC,eAAe,GAAE,GAAG,GAAI,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;wBACnE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;wBACjC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;oBAC9C,CAAA,CAAC,EACF;wBACA,OAAO,KAAK;;oBAEd,OAAO,IAAI;iBACZ;YACF,CAAA;SACF;KACF;IAED,UAAU,EAAC,EAAE,cAAc,EAAE,EAAA;;QAE3B,IACE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE;YAC9C,eAAe,GAAE,IAAI,GAAI,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;YACrE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;YACjC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;QAC9C,CAAA,CAAC,EACF;;YAEA,OAAO;gBACL,GAAG;sKACH,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;oBAAE,GAAG,cAAc;oBAAE,IAAI,EAAE,EAAE;gBAAA,CAAE,CAAC;gBAC7E,CAAC;aACF;;QAGH,OAAO;YAAC,GAAG;kKAAE,kBAAA,AAAe,EAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;YAAE,CAAC;SAAC;KAC9E;IAED,WAAW,GAAA;QACT,OAAO;YACL,OAAO,GACL,UAAU,GAAI,CAAC,EAAE,KAAK,EAAE,KAAI;oBAC1B,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU;oBAE3B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE;wBACnC,eAAe,GAAE,GAAG,GAAI,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;wBACnE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;wBACjC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;oBAC9C,CAAA,CAAC,EAAE;wBACF,OAAO,KAAK;;oBAGd,OAAO,KAAK,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE;iBACrF;YAEH,UAAU,GACR,UAAU,GAAI,CAAC,EAAE,KAAK,EAAE,KAAI;oBAC1B,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU;oBAE3B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE;wBACnC,eAAe,EAAE,GAAG,IAAI,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;wBACnE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;wBACjC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;oBAC9C,CAAA,CAAC,EAAE;wBACF,OAAO,KAAK;;oBAGd,OAAO,KAAK,GACT,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE;wBAAE,oBAAoB,EAAE,IAAI;oBAAA,CAAE,EAChE,OAAO,CAAC,iBAAiB,EAAE,IAAI,EAC/B,GAAG,EAAE;iBACT;YAEH,SAAS,EACP,IAAM,CAAC,EAAE,KAAK,EAAE,KAAI;oBAClB,OAAO,KAAK,GACT,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE;wBAAE,oBAAoB,EAAE,IAAI;oBAAA,CAAE,EACnD,OAAO,CAAC,iBAAiB,EAAE,IAAI,EAC/B,GAAG,EAAE;iBACT;SACJ;KACF;IAED,aAAa,GAAA;QACX,OAAO;kKACL,gBAAA,AAAa,EAAC;gBACZ,IAAI,GAAE,IAAI,IAAG;oBACX,MAAM,UAAU,GAAqB,EAAE;oBAEvC,IAAI,IAAI,EAAE;wBACR,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC,OAAO;wBACnD,MAAM,KAAK,qJAAG,OAAA,AAAI,EAAC,IAAI,CAAC,CAAC,MAAM,CAC7B,IAAI,IAAI,IAAI,CAAC,MAAA,IACR,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE;gCACvC,eAAe,GAAE,IAAI,GAAI,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC;gCACxD,SAAS;gCACT,eAAe;4BAChB,CAAA,CAAC,CACL;wBAED,IAAI,KAAK,CAAC,MAAM,EAAE;4BAChB,KAAK,CAAC,OAAO,EAAC,IAAI,GAAI,UAAU,CAAC,IAAI,CAAC;oCACpC,IAAI,EAAE,IAAI,CAAC,KAAK;oCAChB,IAAI,EAAE;wCACJ,IAAI,EAAE,IAAI,CAAC,IAAI;oCAChB,CAAA;oCACD,KAAK,EAAE,IAAI,CAAC,KAAK;gCAClB,CAAA,CAAC,CAAC;;;oBAIP,OAAO,UAAU;iBAClB;gBACD,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,aAAa,GAAE,KAAK,IAAG;;oBACrB,OAAO;wBACL,IAAI,EAAE,CAAA,EAAA,GAAA,KAAK,CAAC,IAAI,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,IAAI;qBACvB;iBACF;aACF,CAAC;SACH;KACF;IAED,qBAAqB,GAAA;QACnB,MAAM,OAAO,GAAa,EAAE;QAC5B,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC,OAAO;QAEnD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YACzB,OAAO,CAAC,IAAI,CACV,QAAQ,CAAC;gBACP,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;gBAC7C,QAAQ,GAAE,GAAG,GAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE;wBAC9C,eAAe,GAAE,IAAI,GAAI,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC;wBACxD,SAAS;wBACT,eAAe;qBAChB,CAAC;gBACF,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;YAC5C,CAAA,CAAC,CACH;;QAGH,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI,EAAE;YACrC,OAAO,CAAC,IAAI,CACV,YAAY,CAAC;gBACX,IAAI,EAAE,IAAI,CAAC,IAAI;YAChB,CAAA,CAAC,CACH;;QAGH,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC5B,OAAO,CAAC,IAAI,CACV,YAAY,CAAC;gBACX,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;gBAC7C,IAAI,EAAE,IAAI,CAAC,IAAI;YAChB,CAAA,CAAC,CACH;;QAGH,OAAO,OAAO;KACf;AACF,CAAA", "ignoreList": [0, 1, 2, 3], "debugId": null}}, {"offset": {"line": 1280, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/extension-text/src/text.ts"], "sourcesContent": ["import { Node } from '@tiptap/core'\n\n/**\n * This extension allows you to create text nodes.\n * @see https://www.tiptap.dev/api/nodes/text\n */\nexport const Text = Node.create({\n  name: 'text',\n  group: 'inline',\n})\n"], "names": [], "mappings": ";;;;;;AAEA;;;CAGG,GACU,MAAA,IAAI,qJAAG,OAAI,CAAC,MAAM,CAAC;IAC9B,IAAI,EAAE,MAAM;IACZ,KAAK,EAAE,QAAQ;AAChB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1301, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/extension-bubble-menu/src/bubble-menu-plugin.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/extension-bubble-menu/src/bubble-menu.ts"], "sourcesContent": ["import {\n  Editor, isNodeSelection, isTextSelection, posToDOMRect,\n} from '@tiptap/core'\nimport { EditorState, Plugin, PluginKey } from '@tiptap/pm/state'\nimport { EditorView } from '@tiptap/pm/view'\nimport tippy, { Instance, Props } from 'tippy.js'\n\nexport interface BubbleMenuPluginProps {\n  /**\n   * The plugin key.\n   * @type {PluginKey | string}\n   * @default 'bubbleMenu'\n   */\n  pluginKey: PluginKey | string\n\n  /**\n   * The editor instance.\n   */\n  editor: Editor\n\n  /**\n   * The DOM element that contains your menu.\n   * @type {HTMLElement}\n   * @default null\n   */\n  element: HTMLElement\n\n  /**\n   * The options for the tippy.js instance.\n   * @see https://atomiks.github.io/tippyjs/v6/all-props/\n   */\n  tippyOptions?: Partial<Props>\n\n  /**\n   * The delay in milliseconds before the menu should be updated.\n   * This can be useful to prevent performance issues.\n   * @type {number}\n   * @default 250\n   */\n  updateDelay?: number\n\n  /**\n   * A function that determines whether the menu should be shown or not.\n   * If this function returns `false`, the menu will be hidden, otherwise it will be shown.\n   */\n  shouldShow?:\n    | ((props: {\n        editor: Editor\n        element: HTMLElement\n        view: EditorView\n        state: EditorState\n        oldState?: EditorState\n        from: number\n        to: number\n      }) => boolean)\n    | null\n}\n\nexport type BubbleMenuViewProps = BubbleMenuPluginProps & {\n  view: EditorView\n}\n\nexport class BubbleMenuView {\n  public editor: Editor\n\n  public element: HTMLElement\n\n  public view: EditorView\n\n  public preventHide = false\n\n  public tippy: Instance | undefined\n\n  public tippyOptions?: Partial<Props>\n\n  public updateDelay: number\n\n  private updateDebounceTimer: number | undefined\n\n  public shouldShow: Exclude<BubbleMenuPluginProps['shouldShow'], null> = ({\n    view,\n    state,\n    from,\n    to,\n  }) => {\n    const { doc, selection } = state\n    const { empty } = selection\n\n    // Sometime check for `empty` is not enough.\n    // Doubleclick an empty paragraph returns a node size of 2.\n    // So we check also for an empty text size.\n    const isEmptyTextBlock = !doc.textBetween(from, to).length && isTextSelection(state.selection)\n\n    // When clicking on a element inside the bubble menu the editor \"blur\" event\n    // is called and the bubble menu item is focussed. In this case we should\n    // consider the menu as part of the editor and keep showing the menu\n    const isChildOfMenu = this.element.contains(document.activeElement)\n\n    const hasEditorFocus = view.hasFocus() || isChildOfMenu\n\n    if (!hasEditorFocus || empty || isEmptyTextBlock || !this.editor.isEditable) {\n      return false\n    }\n\n    return true\n  }\n\n  constructor({\n    editor,\n    element,\n    view,\n    tippyOptions = {},\n    updateDelay = 250,\n    shouldShow,\n  }: BubbleMenuViewProps) {\n    this.editor = editor\n    this.element = element\n    this.view = view\n    this.updateDelay = updateDelay\n\n    if (shouldShow) {\n      this.shouldShow = shouldShow\n    }\n\n    this.element.addEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.view.dom.addEventListener('dragstart', this.dragstartHandler)\n    this.editor.on('focus', this.focusHandler)\n    this.editor.on('blur', this.blurHandler)\n    this.tippyOptions = tippyOptions\n    // Detaches menu content from its current parent\n    this.element.remove()\n    this.element.style.visibility = 'visible'\n  }\n\n  mousedownHandler = () => {\n    this.preventHide = true\n  }\n\n  dragstartHandler = () => {\n    this.hide()\n  }\n\n  focusHandler = () => {\n    // we use `setTimeout` to make sure `selection` is already updated\n    setTimeout(() => this.update(this.editor.view))\n  }\n\n  blurHandler = ({ event }: { event: FocusEvent }) => {\n    if (this.preventHide) {\n      this.preventHide = false\n\n      return\n    }\n\n    if (event?.relatedTarget && this.element.parentNode?.contains(event.relatedTarget as Node)) {\n      return\n    }\n\n    if (\n      event?.relatedTarget === this.editor.view.dom\n    ) {\n      return\n    }\n\n    this.hide()\n  }\n\n  tippyBlurHandler = (event: FocusEvent) => {\n    this.blurHandler({ event })\n  }\n\n  createTooltip() {\n    const { element: editorElement } = this.editor.options\n    const editorIsAttached = !!editorElement.parentElement\n\n    if (this.tippy || !editorIsAttached) {\n      return\n    }\n\n    this.tippy = tippy(editorElement, {\n      duration: 0,\n      getReferenceClientRect: null,\n      content: this.element,\n      interactive: true,\n      trigger: 'manual',\n      placement: 'top',\n      hideOnClick: 'toggle',\n      ...this.tippyOptions,\n    })\n\n    // maybe we have to hide tippy on its own blur event as well\n    if (this.tippy.popper.firstChild) {\n      (this.tippy.popper.firstChild as HTMLElement).addEventListener('blur', this.tippyBlurHandler)\n    }\n  }\n\n  update(view: EditorView, oldState?: EditorState) {\n    const { state } = view\n    const hasValidSelection = state.selection.from !== state.selection.to\n\n    if (this.updateDelay > 0 && hasValidSelection) {\n      this.handleDebouncedUpdate(view, oldState)\n      return\n    }\n\n    const selectionChanged = !oldState?.selection.eq(view.state.selection)\n    const docChanged = !oldState?.doc.eq(view.state.doc)\n\n    this.updateHandler(view, selectionChanged, docChanged, oldState)\n  }\n\n  handleDebouncedUpdate = (view: EditorView, oldState?: EditorState) => {\n    const selectionChanged = !oldState?.selection.eq(view.state.selection)\n    const docChanged = !oldState?.doc.eq(view.state.doc)\n\n    if (!selectionChanged && !docChanged) {\n      return\n    }\n\n    if (this.updateDebounceTimer) {\n      clearTimeout(this.updateDebounceTimer)\n    }\n\n    this.updateDebounceTimer = window.setTimeout(() => {\n      this.updateHandler(view, selectionChanged, docChanged, oldState)\n    }, this.updateDelay)\n  }\n\n  updateHandler = (view: EditorView, selectionChanged: boolean, docChanged: boolean, oldState?: EditorState) => {\n    const { state, composing } = view\n    const { selection } = state\n\n    const isSame = !selectionChanged && !docChanged\n\n    if (composing || isSame) {\n      return\n    }\n\n    this.createTooltip()\n\n    // support for CellSelections\n    const { ranges } = selection\n    const from = Math.min(...ranges.map(range => range.$from.pos))\n    const to = Math.max(...ranges.map(range => range.$to.pos))\n\n    const shouldShow = this.shouldShow?.({\n      editor: this.editor,\n      element: this.element,\n      view,\n      state,\n      oldState,\n      from,\n      to,\n    })\n\n    if (!shouldShow) {\n      this.hide()\n\n      return\n    }\n\n    this.tippy?.setProps({\n      getReferenceClientRect:\n        this.tippyOptions?.getReferenceClientRect\n        || (() => {\n          if (isNodeSelection(state.selection)) {\n            let node = view.nodeDOM(from) as HTMLElement\n\n            if (node) {\n              const nodeViewWrapper = node.dataset.nodeViewWrapper ? node : node.querySelector('[data-node-view-wrapper]')\n\n              if (nodeViewWrapper) {\n                node = nodeViewWrapper.firstChild as HTMLElement\n              }\n\n              if (node) {\n                return node.getBoundingClientRect()\n              }\n            }\n          }\n\n          return posToDOMRect(view, from, to)\n        }),\n    })\n\n    this.show()\n  }\n\n  show() {\n    this.tippy?.show()\n  }\n\n  hide() {\n    this.tippy?.hide()\n  }\n\n  destroy() {\n    if (this.tippy?.popper.firstChild) {\n      (this.tippy.popper.firstChild as HTMLElement).removeEventListener(\n        'blur',\n        this.tippyBlurHandler,\n      )\n    }\n    this.tippy?.destroy()\n    this.element.removeEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.view.dom.removeEventListener('dragstart', this.dragstartHandler)\n    this.editor.off('focus', this.focusHandler)\n    this.editor.off('blur', this.blurHandler)\n  }\n}\n\nexport const BubbleMenuPlugin = (options: BubbleMenuPluginProps) => {\n  return new Plugin({\n    key:\n      typeof options.pluginKey === 'string' ? new PluginKey(options.pluginKey) : options.pluginKey,\n    view: view => new BubbleMenuView({ view, ...options }),\n  })\n}\n", "import { Extension } from '@tiptap/core'\n\nimport { BubbleMenuPlugin, BubbleMenuPluginProps } from './bubble-menu-plugin.js'\n\nexport type BubbleMenuOptions = Omit<BubbleMenuPluginProps, 'editor' | 'element'> & {\n  /**\n   * The DOM element that contains your menu.\n   * @type {HTMLElement}\n   * @default null\n   */\n  element: HTMLElement | null,\n}\n\n/**\n * This extension allows you to create a bubble menu.\n * @see https://tiptap.dev/api/extensions/bubble-menu\n */\nexport const BubbleMenu = Extension.create<BubbleMenuOptions>({\n  name: 'bubbleMenu',\n\n  addOptions() {\n    return {\n      element: null,\n      tippyOptions: {},\n      pluginKey: 'bubbleMenu',\n      updateDelay: undefined,\n      shouldShow: null,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    if (!this.options.element) {\n      return []\n    }\n\n    return [\n      BubbleMenuPlugin({\n        pluginKey: this.options.plugin<PERSON><PERSON>,\n        editor: this.editor,\n        element: this.options.element,\n        tippyOptions: this.options.tippyOptions,\n        updateDelay: this.options.updateDelay,\n        shouldShow: this.options.shouldShow,\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;;;;;MA8Da,cAAc,CAAA;IA6CzB,WAAA,CAAY,EACV,MAAM,EACN,OAAO,EACP,IAAI,EACJ,YAAY,GAAG,CAAA,CAAE,EACjB,WAAW,GAAG,GAAG,EACjB,UAAU,EACU,CAAA;QA7Cf,IAAW,CAAA,WAAA,GAAG,KAAK;QAUnB,IAAA,CAAA,UAAU,GAAuD,CAAC,EACvE,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,EAAE,EACH,KAAI;YACH,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,KAAK;YAChC,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS;;;;YAK3B,MAAM,gBAAgB,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,MAAM,0JAAI,kBAAA,AAAe,EAAC,KAAK,CAAC,SAAS,CAAC;;;;YAK9F,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC;YAEnE,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,aAAa;YAEvD,IAAI,CAAC,cAAc,IAAI,KAAK,IAAI,gBAAgB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;gBAC3E,OAAO,KAAK;;YAGd,OAAO,IAAI;QACb,CAAC;QA6BD,IAAgB,CAAA,gBAAA,GAAG,MAAK;YACtB,IAAI,CAAC,WAAW,GAAG,IAAI;QACzB,CAAC;QAED,IAAgB,CAAA,gBAAA,GAAG,MAAK;YACtB,IAAI,CAAC,IAAI,EAAE;QACb,CAAC;QAED,IAAY,CAAA,YAAA,GAAG,MAAK;;YAElB,UAAU,CAAC,IAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC;QAED,IAAA,CAAA,WAAW,GAAG,CAAC,EAAE,KAAK,EAAyB,KAAI;;YACjD,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,CAAC,WAAW,GAAG,KAAK;gBAExB;;YAGF,IAAI,CAAA,KAAK,KAAA,IAAA,IAAL,KAAK,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAL,KAAK,CAAE,aAAa,KAAA,CAAI,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAQ,CAAC,KAAK,CAAC,aAAqB,CAAC,CAAA,EAAE;gBAC1F;;YAGF,IACE,CAAA,KAAK,KAAA,IAAA,IAAL,KAAK,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAL,KAAK,CAAE,aAAa,MAAK,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAC7C;gBACA;;YAGF,IAAI,CAAC,IAAI,EAAE;QACb,CAAC;QAED,IAAA,CAAA,gBAAgB,GAAG,CAAC,KAAiB,KAAI;YACvC,IAAI,CAAC,WAAW,CAAC;gBAAE,KAAK;YAAA,CAAE,CAAC;QAC7B,CAAC;QA0CD,IAAA,CAAA,qBAAqB,GAAG,CAAC,IAAgB,EAAE,QAAsB,KAAI;YACnE,MAAM,gBAAgB,GAAG,CAAA,CAAC,QAAQ,KAAR,IAAA,IAAA,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CAAE,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;YACtE,MAAM,UAAU,GAAG,CAAA,CAAC,QAAQ,KAAR,IAAA,IAAA,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CAAE,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAEpD,IAAI,CAAC,gBAAgB,IAAI,CAAC,UAAU,EAAE;gBACpC;;YAGF,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC5B,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC;;YAGxC,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,UAAU,CAAC,MAAK;gBAChD,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,gBAAgB,EAAE,UAAU,EAAE,QAAQ,CAAC;YAClE,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC;QACtB,CAAC;QAED,IAAa,CAAA,aAAA,GAAG,CAAC,IAAgB,EAAE,gBAAyB,EAAE,UAAmB,EAAE,QAAsB,KAAI;;YAC3G,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,IAAI;YACjC,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK;YAE3B,MAAM,MAAM,GAAG,CAAC,gBAAgB,IAAI,CAAC,UAAU;YAE/C,IAAI,SAAS,IAAI,MAAM,EAAE;gBACvB;;YAGF,IAAI,CAAC,aAAa,EAAE;;YAGpB,MAAM,EAAE,MAAM,EAAE,GAAG,SAAS;YAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,EAAC,KAAK,GAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC9D,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,EAAC,KAAK,GAAI,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAE1D,MAAM,UAAU,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,IAAA,EAAA;gBACnC,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,IAAI;gBACJ,KAAK;gBACL,QAAQ;gBACR,IAAI;gBACJ,EAAE;YACH,CAAA,CAAC;YAEF,IAAI,CAAC,UAAU,EAAE;gBACf,IAAI,CAAC,IAAI,EAAE;gBAEX;;YAGF,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,QAAQ,CAAC;gBACnB,sBAAsB,EACpB,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,sBAAsB,KACtC,CAAC,MAAK;oBACP,0JAAI,kBAAA,AAAe,EAAC,KAAK,CAAC,SAAS,CAAC,EAAE;wBACpC,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAgB;wBAE5C,IAAI,IAAI,EAAE;4BACR,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,0BAA0B,CAAC;4BAE5G,IAAI,eAAe,EAAE;gCACnB,IAAI,GAAG,eAAe,CAAC,UAAyB;;4BAGlD,IAAI,IAAI,EAAE;gCACR,OAAO,IAAI,CAAC,qBAAqB,EAAE;;;;oBAKzC,WAAO,iKAAA,AAAY,EAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC;gBACrC,CAAC,CAAC;YACL,CAAA,CAAC;YAEF,IAAI,CAAC,IAAI,EAAE;QACb,CAAC;QA3KC,IAAI,CAAC,MAAM,GAAG,MAAM;QACpB,IAAI,CAAC,OAAO,GAAG,OAAO;QACtB,IAAI,CAAC,IAAI,GAAG,IAAI;QAChB,IAAI,CAAC,WAAW,GAAG,WAAW;QAE9B,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,UAAU,GAAG,UAAU;;QAG9B,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,EAAE;YAAE,OAAO,EAAE,IAAI;QAAA,CAAE,CAAC;QACpF,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC;QAClE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC;QAC1C,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,YAAY,GAAG,YAAY;;QAEhC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;QACrB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS;;IAwC3C,aAAa,GAAA;QACX,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO;QACtD,MAAM,gBAAgB,GAAG,CAAC,CAAC,aAAa,CAAC,aAAa;QAEtD,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,gBAAgB,EAAE;YACnC;;QAGF,IAAI,CAAC,KAAK,OAAG,8JAAA,AAAK,EAAC,aAAa,EAAE;YAChC,QAAQ,EAAE,CAAC;YACX,sBAAsB,EAAE,IAAI;YAC5B,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,QAAQ;YACjB,SAAS,EAAE,KAAK;YAChB,WAAW,EAAE,QAAQ;YACrB,GAAG,IAAI,CAAC,YAAY;QACrB,CAAA,CAAC;;QAGF,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE;YAC/B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAA0B,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC;;;IAIjG,MAAM,CAAC,IAAgB,EAAE,QAAsB,EAAA;QAC7C,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI;QACtB,MAAM,iBAAiB,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QAErE,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,iBAAiB,EAAE;YAC7C,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,QAAQ,CAAC;YAC1C;;QAGF,MAAM,gBAAgB,GAAG,CAAA,CAAC,QAAQ,KAAR,IAAA,IAAA,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CAAE,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;QACtE,MAAM,UAAU,GAAG,CAAA,CAAC,QAAQ,KAAR,IAAA,IAAA,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CAAE,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAEpD,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,gBAAgB,EAAE,UAAU,EAAE,QAAQ,CAAC;;IAgFlE,IAAI,GAAA;;QACF,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,EAAE;;IAGpB,IAAI,GAAA;;QACF,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,EAAE;;IAGpB,OAAO,GAAA;;QACL,IAAI,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,MAAM,CAAC,UAAU,EAAE;YAChC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAA0B,CAAC,mBAAmB,CAC/D,MAAM,EACN,IAAI,CAAC,gBAAgB,CACtB;;QAEH,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,EAAE;QACrB,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,EAAE;YAAE,OAAO,EAAE,IAAI;QAAA,CAAE,CAAC;QACvF,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC;QACrE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC;QAC3C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC;;AAE5C;AAEY,MAAA,gBAAgB,GAAG,CAAC,OAA8B,KAAI;IACjE,OAAO,0JAAI,SAAM,CAAC;QAChB,GAAG,EACD,OAAO,OAAO,CAAC,SAAS,KAAK,QAAQ,GAAG,0JAAI,YAAS,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,SAAS;QAC9F,IAAI,GAAE,IAAI,GAAI,IAAI,cAAc,CAAC;gBAAE,IAAI;gBAAE,GAAG,OAAO;YAAA,CAAE,CAAC;IACvD,CAAA,CAAC;AACJ;AChTA;;;CAGG,GACU,MAAA,UAAU,qJAAG,YAAS,CAAC,MAAM,CAAoB;IAC5D,IAAI,EAAE,YAAY;IAElB,UAAU,GAAA;QACR,OAAO;YACL,OAAO,EAAE,IAAI;YACb,YAAY,EAAE,CAAA,CAAE;YAChB,SAAS,EAAE,YAAY;YACvB,WAAW,EAAE,SAAS;YACtB,UAAU,EAAE,IAAI;SACjB;KACF;IAED,qBAAqB,GAAA;QACnB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACzB,OAAO,EAAE;;QAGX,OAAO;YACL,gBAAgB,CAAC;gBACf,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;gBACjC,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;gBAC7B,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;gBACvC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;gBACrC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;aACpC,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0, 1], "debugId": null}}, {"offset": {"line": 1540, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/extension-floating-menu/src/floating-menu-plugin.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/extension-floating-menu/src/floating-menu.ts"], "sourcesContent": ["import {\n  Editor, getText, getTextSerializersFromSchema, posToDOMRect,\n} from '@tiptap/core'\nimport { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { EditorState, Plugin, PluginKey } from '@tiptap/pm/state'\nimport { EditorView } from '@tiptap/pm/view'\nimport tippy, { Instance, Props } from 'tippy.js'\n\nexport interface FloatingMenuPluginProps {\n  /**\n   * The plugin key for the floating menu.\n   * @default 'floatingMenu'\n   */\n  pluginKey: PluginKey | string\n\n  /**\n   * The editor instance.\n   * @default null\n   */\n  editor: Editor\n\n  /**\n   * The DOM element that contains your menu.\n   * @default null\n   */\n  element: HTMLElement\n\n  /**\n   * The options for the tippy instance.\n   * @default {}\n   * @see https://atomiks.github.io/tippyjs/v6/all-props/\n   */\n  tippyOptions?: Partial<Props>\n\n  /**\n   * A function that determines whether the menu should be shown or not.\n   * If this function returns `false`, the menu will be hidden, otherwise it will be shown.\n   * @default null\n   */\n  shouldShow?:\n    | ((props: {\n        editor: Editor\n        view: EditorView\n        state: EditorState\n        oldState?: EditorState\n      }) => boolean)\n    | null\n}\n\nexport type FloatingMenuViewProps = FloatingMenuPluginProps & {\n  /**\n   * The editor view.\n   */\n  view: EditorView\n}\n\nexport class FloatingMenuView {\n  public editor: Editor\n\n  public element: HTMLElement\n\n  public view: EditorView\n\n  public preventHide = false\n\n  public tippy: Instance | undefined\n\n  public tippyOptions?: Partial<Props>\n\n  private getTextContent(node:ProseMirrorNode) {\n    return getText(node, { textSerializers: getTextSerializersFromSchema(this.editor.schema) })\n  }\n\n  public shouldShow: Exclude<FloatingMenuPluginProps['shouldShow'], null> = ({ view, state }) => {\n    const { selection } = state\n    const { $anchor, empty } = selection\n    const isRootDepth = $anchor.depth === 1\n\n    const isEmptyTextBlock = $anchor.parent.isTextblock && !$anchor.parent.type.spec.code && !$anchor.parent.textContent && $anchor.parent.childCount === 0 && !this.getTextContent($anchor.parent)\n\n    if (\n      !view.hasFocus()\n      || !empty\n      || !isRootDepth\n      || !isEmptyTextBlock\n      || !this.editor.isEditable\n    ) {\n      return false\n    }\n\n    return true\n  }\n\n  constructor({\n    editor, element, view, tippyOptions = {}, shouldShow,\n  }: FloatingMenuViewProps) {\n    this.editor = editor\n    this.element = element\n    this.view = view\n\n    if (shouldShow) {\n      this.shouldShow = shouldShow\n    }\n\n    this.element.addEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.editor.on('focus', this.focusHandler)\n    this.editor.on('blur', this.blurHandler)\n    this.tippyOptions = tippyOptions\n    // Detaches menu content from its current parent\n    this.element.remove()\n    this.element.style.visibility = 'visible'\n  }\n\n  mousedownHandler = () => {\n    this.preventHide = true\n  }\n\n  focusHandler = () => {\n    // we use `setTimeout` to make sure `selection` is already updated\n    setTimeout(() => this.update(this.editor.view))\n  }\n\n  blurHandler = ({ event }: { event: FocusEvent }) => {\n    if (this.preventHide) {\n      this.preventHide = false\n\n      return\n    }\n\n    if (event?.relatedTarget && this.element.parentNode?.contains(event.relatedTarget as Node)) {\n      return\n    }\n\n    if (\n      event?.relatedTarget === this.editor.view.dom\n    ) {\n      return\n    }\n\n    this.hide()\n  }\n\n  tippyBlurHandler = (event: FocusEvent) => {\n    this.blurHandler({ event })\n  }\n\n  createTooltip() {\n    const { element: editorElement } = this.editor.options\n    const editorIsAttached = !!editorElement.parentElement\n\n    if (this.tippy || !editorIsAttached) {\n      return\n    }\n\n    this.tippy = tippy(editorElement, {\n      duration: 0,\n      getReferenceClientRect: null,\n      content: this.element,\n      interactive: true,\n      trigger: 'manual',\n      placement: 'right',\n      hideOnClick: 'toggle',\n      ...this.tippyOptions,\n    })\n\n    // maybe we have to hide tippy on its own blur event as well\n    if (this.tippy.popper.firstChild) {\n      (this.tippy.popper.firstChild as HTMLElement).addEventListener('blur', this.tippyBlurHandler)\n    }\n  }\n\n  update(view: EditorView, oldState?: EditorState) {\n    const { state } = view\n    const { doc, selection } = state\n    const { from, to } = selection\n    const isSame = oldState && oldState.doc.eq(doc) && oldState.selection.eq(selection)\n\n    if (isSame) {\n      return\n    }\n\n    this.createTooltip()\n\n    const shouldShow = this.shouldShow?.({\n      editor: this.editor,\n      view,\n      state,\n      oldState,\n    })\n\n    if (!shouldShow) {\n      this.hide()\n\n      return\n    }\n\n    this.tippy?.setProps({\n      getReferenceClientRect:\n        this.tippyOptions?.getReferenceClientRect || (() => posToDOMRect(view, from, to)),\n    })\n\n    this.show()\n  }\n\n  show() {\n    this.tippy?.show()\n  }\n\n  hide() {\n    this.tippy?.hide()\n  }\n\n  destroy() {\n    if (this.tippy?.popper.firstChild) {\n      (this.tippy.popper.firstChild as HTMLElement).removeEventListener(\n        'blur',\n        this.tippyBlurHandler,\n      )\n    }\n    this.tippy?.destroy()\n    this.element.removeEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.editor.off('focus', this.focusHandler)\n    this.editor.off('blur', this.blurHandler)\n  }\n}\n\nexport const FloatingMenuPlugin = (options: FloatingMenuPluginProps) => {\n  return new Plugin({\n    key:\n      typeof options.pluginKey === 'string' ? new PluginKey(options.pluginKey) : options.pluginKey,\n    view: view => new FloatingMenuView({ view, ...options }),\n  })\n}\n", "import { Extension } from '@tiptap/core'\n\nimport { FloatingMenuPlugin, FloatingMenuPluginProps } from './floating-menu-plugin.js'\n\nexport type FloatingMenuOptions = Omit<FloatingMenuPluginProps, 'editor' | 'element'> & {\n  /**\n   * The DOM element that contains your menu.\n   * @type {HTMLElement}\n   * @default null\n   */\n  element: HTMLElement | null,\n}\n\n/**\n * This extension allows you to create a floating menu.\n * @see https://tiptap.dev/api/extensions/floating-menu\n */\nexport const FloatingMenu = Extension.create<FloatingMenuOptions>({\n  name: 'floatingMenu',\n\n  addOptions() {\n    return {\n      element: null,\n      tippyOptions: {},\n      pluginKey: 'floatingMenu',\n      shouldShow: null,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    if (!this.options.element) {\n      return []\n    }\n\n    return [\n      FloatingMenuPlugin({\n        pluginKey: this.options.plugin<PERSON><PERSON>,\n        editor: this.editor,\n        element: this.options.element,\n        tippyOptions: this.options.tippyOptions,\n        shouldShow: this.options.shouldShow,\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;;;;;MAwDa,gBAAgB,CAAA;IAanB,cAAc,CAAC,IAAoB,EAAA;QACzC,6JAAO,UAAA,AAAO,EAAC,IAAI,EAAE;YAAE,eAAe,wJAAE,+BAAA,AAA4B,EAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAAA,CAAE,CAAC;;IAuB7F,WAAA,CAAY,EACV,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,GAAG,CAAA,CAAE,EAAE,UAAU,EAC9B,CAAA;QAhCjB,IAAW,CAAA,WAAA,GAAG,KAAK;QAUnB,IAAU,CAAA,UAAA,GAAyD,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAI;YAC5F,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK;YAC3B,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,SAAS;YACpC,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,KAAK,CAAC;YAEvC,MAAM,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,IAAI,OAAO,CAAC,MAAM,CAAC,UAAU,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC;YAE/L,IACE,CAAC,IAAI,CAAC,QAAQ,MACX,CAAC,SACD,CAAC,eACD,CAAC,oBACD,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAC1B;gBACA,OAAO,KAAK;;YAGd,OAAO,IAAI;QACb,CAAC;QAsBD,IAAgB,CAAA,gBAAA,GAAG,MAAK;YACtB,IAAI,CAAC,WAAW,GAAG,IAAI;QACzB,CAAC;QAED,IAAY,CAAA,YAAA,GAAG,MAAK;;YAElB,UAAU,CAAC,IAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC;QAED,IAAA,CAAA,WAAW,GAAG,CAAC,EAAE,KAAK,EAAyB,KAAI;;YACjD,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,CAAC,WAAW,GAAG,KAAK;gBAExB;;YAGF,IAAI,CAAA,KAAK,KAAA,IAAA,IAAL,KAAK,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAL,KAAK,CAAE,aAAa,KAAA,CAAI,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAQ,CAAC,KAAK,CAAC,aAAqB,CAAC,CAAA,EAAE;gBAC1F;;YAGF,IACE,CAAA,KAAK,KAAA,IAAA,IAAL,KAAK,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAL,KAAK,CAAE,aAAa,MAAK,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAC7C;gBACA;;YAGF,IAAI,CAAC,IAAI,EAAE;QACb,CAAC;QAED,IAAA,CAAA,gBAAgB,GAAG,CAAC,KAAiB,KAAI;YACvC,IAAI,CAAC,WAAW,CAAC;gBAAE,KAAK;YAAA,CAAE,CAAC;QAC7B,CAAC;QAhDC,IAAI,CAAC,MAAM,GAAG,MAAM;QACpB,IAAI,CAAC,OAAO,GAAG,OAAO;QACtB,IAAI,CAAC,IAAI,GAAG,IAAI;QAEhB,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,UAAU,GAAG,UAAU;;QAG9B,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,EAAE;YAAE,OAAO,EAAE,IAAI;QAAA,CAAE,CAAC;QACpF,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC;QAC1C,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,YAAY,GAAG,YAAY;;QAEhC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;QACrB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS;;IAoC3C,aAAa,GAAA;QACX,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO;QACtD,MAAM,gBAAgB,GAAG,CAAC,CAAC,aAAa,CAAC,aAAa;QAEtD,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,gBAAgB,EAAE;YACnC;;QAGF,IAAI,CAAC,KAAK,2JAAG,UAAA,AAAK,EAAC,aAAa,EAAE;YAChC,QAAQ,EAAE,CAAC;YACX,sBAAsB,EAAE,IAAI;YAC5B,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,QAAQ;YACjB,SAAS,EAAE,OAAO;YAClB,WAAW,EAAE,QAAQ;YACrB,GAAG,IAAI,CAAC,YAAY;QACrB,CAAA,CAAC;;QAGF,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE;YAC/B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAA0B,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC;;;IAIjG,MAAM,CAAC,IAAgB,EAAE,QAAsB,EAAA;;QAC7C,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI;QACtB,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,KAAK;QAChC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,SAAS;QAC9B,MAAM,MAAM,GAAG,QAAQ,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC;QAEnF,IAAI,MAAM,EAAE;YACV;;QAGF,IAAI,CAAC,aAAa,EAAE;QAEpB,MAAM,UAAU,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,IAAA,EAAA;YACnC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI;YACJ,KAAK;YACL,QAAQ;QACT,CAAA,CAAC;QAEF,IAAI,CAAC,UAAU,EAAE;YACf,IAAI,CAAC,IAAI,EAAE;YAEX;;QAGF,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,QAAQ,CAAC;YACnB,sBAAsB,EACpB,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,sBAAsB,KAAA,CAAK,0JAAM,eAAA,AAAY,EAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;QACpF,CAAA,CAAC;QAEF,IAAI,CAAC,IAAI,EAAE;;IAGb,IAAI,GAAA;;QACF,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,EAAE;;IAGpB,IAAI,GAAA;;QACF,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,EAAE;;IAGpB,OAAO,GAAA;;QACL,IAAI,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,MAAM,CAAC,UAAU,EAAE;YAChC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAA0B,CAAC,mBAAmB,CAC/D,MAAM,EACN,IAAI,CAAC,gBAAgB,CACtB;;QAEH,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,EAAE;QACrB,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,EAAE;YAAE,OAAO,EAAE,IAAI;QAAA,CAAE,CAAC;QACvF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC;QAC3C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC;;AAE5C;AAEY,MAAA,kBAAkB,GAAG,CAAC,OAAgC,KAAI;IACrE,OAAO,0JAAI,SAAM,CAAC;QAChB,GAAG,EACD,OAAO,OAAO,CAAC,SAAS,KAAK,QAAQ,GAAG,0JAAI,YAAS,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,SAAS;QAC9F,IAAI,GAAE,IAAI,GAAI,IAAI,gBAAgB,CAAC;gBAAE,IAAI;gBAAE,GAAG,OAAO;YAAA,CAAE,CAAC;IACzD,CAAA,CAAC;AACJ;AC3NA;;;CAGG,GACU,MAAA,YAAY,oJAAG,aAAS,CAAC,MAAM,CAAsB;IAChE,IAAI,EAAE,cAAc;IAEpB,UAAU,GAAA;QACR,OAAO;YACL,OAAO,EAAE,IAAI;YACb,YAAY,EAAE,CAAA,CAAE;YAChB,SAAS,EAAE,cAAc;YACzB,UAAU,EAAE,IAAI;SACjB;KACF;IAED,qBAAqB,GAAA;QACnB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACzB,OAAO,EAAE;;QAGX,OAAO;YACL,kBAAkB,CAAC;gBACjB,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;gBACjC,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;gBAC7B,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;gBACvC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;aACpC,CAAC;SACH;KACF;AACF,CAAA", "ignoreList": [0, 1], "debugId": null}}, {"offset": {"line": 1725, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.production.min.js", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/node_modules/use-sync-external-store/shim/index.js", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/react/src/EditorContent.tsx", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/node_modules/fast-deep-equal/es6/react.js", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.production.min.js", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/node_modules/use-sync-external-store/shim/with-selector.js", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/react/src/useEditorState.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/react/src/useEditor.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/react/src/Context.tsx", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/react/src/BubbleMenu.tsx", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/react/src/FloatingMenu.tsx", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/react/src/useReactNodeView.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/react/src/NodeViewContent.tsx", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/react/src/NodeViewWrapper.tsx", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/react/src/ReactRenderer.tsx", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40tiptap/react/src/ReactNodeViewRenderer.tsx"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var e=require(\"react\");function h(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var k=\"function\"===typeof Object.is?Object.is:h,l=e.useState,m=e.useEffect,n=e.useLayoutEffect,p=e.useDebugValue;function q(a,b){var d=b(),f=l({inst:{value:d,getSnapshot:b}}),c=f[0].inst,g=f[1];n(function(){c.value=d;c.getSnapshot=b;r(c)&&g({inst:c})},[a,d,b]);m(function(){r(c)&&g({inst:c});return a(function(){r(c)&&g({inst:c})})},[a]);p(d);return d}\nfunction r(a){var b=a.getSnapshot;a=a.value;try{var d=b();return!k(a,d)}catch(f){return!0}}function t(a,b){return b()}var u=\"undefined\"===typeof window||\"undefined\"===typeof window.document||\"undefined\"===typeof window.document.createElement?t:q;exports.useSyncExternalStore=void 0!==e.useSyncExternalStore?e.useSyncExternalStore:u;\n", "/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var React = require('react');\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction is(x, y) {\n  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n  ;\n}\n\nvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\n// dispatch for CommonJS interop named imports.\n\nvar useState = React.useState,\n    useEffect = React.useEffect,\n    useLayoutEffect = React.useLayoutEffect,\n    useDebugValue = React.useDebugValue;\nvar didWarnOld18Alpha = false;\nvar didWarnUncachedGetSnapshot = false; // Disclaimer: This shim breaks many of the rules of React, and only works\n// because of a very particular set of implementation details and assumptions\n// -- change any one of them and it will break. The most important assumption\n// is that updates are always synchronous, because concurrent rendering is\n// only available in versions of React that also have a built-in\n// useSyncExternalStore API. And we only use this shim when the built-in API\n// does not exist.\n//\n// Do not assume that the clever hacks used by this hook also work in general.\n// The point of this shim is to replace the need for hacks by other libraries.\n\nfunction useSyncExternalStore(subscribe, getSnapshot, // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n// React do not expose a way to check if we're hydrating. So users of the shim\n// will need to track that themselves and return the correct value\n// from `getSnapshot`.\ngetServerSnapshot) {\n  {\n    if (!didWarnOld18Alpha) {\n      if (React.startTransition !== undefined) {\n        didWarnOld18Alpha = true;\n\n        error('You are using an outdated, pre-release alpha of React 18 that ' + 'does not support useSyncExternalStore. The ' + 'use-sync-external-store shim will not work correctly. Upgrade ' + 'to a newer pre-release.');\n      }\n    }\n  } // Read the current snapshot from the store on every render. Again, this\n  // breaks the rules of React, and only works here because of specific\n  // implementation details, most importantly that updates are\n  // always synchronous.\n\n\n  var value = getSnapshot();\n\n  {\n    if (!didWarnUncachedGetSnapshot) {\n      var cachedValue = getSnapshot();\n\n      if (!objectIs(value, cachedValue)) {\n        error('The result of getSnapshot should be cached to avoid an infinite loop');\n\n        didWarnUncachedGetSnapshot = true;\n      }\n    }\n  } // Because updates are synchronous, we don't queue them. Instead we force a\n  // re-render whenever the subscribed state changes by updating an some\n  // arbitrary useState hook. Then, during render, we call getSnapshot to read\n  // the current value.\n  //\n  // Because we don't actually use the state returned by the useState hook, we\n  // can save a bit of memory by storing other stuff in that slot.\n  //\n  // To implement the early bailout, we need to track some things on a mutable\n  // object. Usually, we would put that in a useRef hook, but we can stash it in\n  // our useState hook instead.\n  //\n  // To force a re-render, we call forceUpdate({inst}). That works because the\n  // new object always fails an equality check.\n\n\n  var _useState = useState({\n    inst: {\n      value: value,\n      getSnapshot: getSnapshot\n    }\n  }),\n      inst = _useState[0].inst,\n      forceUpdate = _useState[1]; // Track the latest getSnapshot function with a ref. This needs to be updated\n  // in the layout phase so we can access it during the tearing check that\n  // happens on subscribe.\n\n\n  useLayoutEffect(function () {\n    inst.value = value;\n    inst.getSnapshot = getSnapshot; // Whenever getSnapshot or subscribe changes, we need to check in the\n    // commit phase if there was an interleaved mutation. In concurrent mode\n    // this can happen all the time, but even in synchronous mode, an earlier\n    // effect may have mutated the store.\n\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst: inst\n      });\n    }\n  }, [subscribe, value, getSnapshot]);\n  useEffect(function () {\n    // Check for changes right before subscribing. Subsequent changes will be\n    // detected in the subscription handler.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst: inst\n      });\n    }\n\n    var handleStoreChange = function () {\n      // TODO: Because there is no cross-renderer API for batching updates, it's\n      // up to the consumer of this library to wrap their subscription event\n      // with unstable_batchedUpdates. Should we try to detect when this isn't\n      // the case and print a warning in development?\n      // The store changed. Check if the snapshot changed since the last time we\n      // read from the store.\n      if (checkIfSnapshotChanged(inst)) {\n        // Force a re-render.\n        forceUpdate({\n          inst: inst\n        });\n      }\n    }; // Subscribe to the store and return a clean-up function.\n\n\n    return subscribe(handleStoreChange);\n  }, [subscribe]);\n  useDebugValue(value);\n  return value;\n}\n\nfunction checkIfSnapshotChanged(inst) {\n  var latestGetSnapshot = inst.getSnapshot;\n  var prevValue = inst.value;\n\n  try {\n    var nextValue = latestGetSnapshot();\n    return !objectIs(prevValue, nextValue);\n  } catch (error) {\n    return true;\n  }\n}\n\nfunction useSyncExternalStore$1(subscribe, getSnapshot, getServerSnapshot) {\n  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n  // React do not expose a way to check if we're hydrating. So users of the shim\n  // will need to track that themselves and return the correct value\n  // from `getSnapshot`.\n  return getSnapshot();\n}\n\nvar canUseDOM = !!(typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined');\n\nvar isServerEnvironment = !canUseDOM;\n\nvar shim = isServerEnvironment ? useSyncExternalStore$1 : useSyncExternalStore;\nvar useSyncExternalStore$2 = React.useSyncExternalStore !== undefined ? React.useSyncExternalStore : shim;\n\nexports.useSyncExternalStore = useSyncExternalStore$2;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.min.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "import { Editor } from '@tiptap/core'\nimport React, {\n  ForwardedRef, forwardRef, HTMLProps, LegacyRef, MutableRefObject,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport { useSyncExternalStore } from 'use-sync-external-store/shim'\n\nimport { ContentComponent, EditorWithContentComponent } from './Editor.js'\nimport { ReactRenderer } from './ReactRenderer.js'\n\nconst mergeRefs = <T extends HTMLDivElement>(\n  ...refs: Array<MutableRefObject<T> | LegacyRef<T> | undefined>\n) => {\n  return (node: T) => {\n    refs.forEach(ref => {\n      if (typeof ref === 'function') {\n        ref(node)\n      } else if (ref) {\n        (ref as MutableRefObject<T | null>).current = node\n      }\n    })\n  }\n}\n\n/**\n * This component renders all of the editor's node views.\n */\nconst Portals: React.FC<{ contentComponent: ContentComponent }> = ({\n  contentComponent,\n}) => {\n  // For performance reasons, we render the node view portals on state changes only\n  const renderers = useSyncExternalStore(\n    contentComponent.subscribe,\n    contentComponent.getSnapshot,\n    contentComponent.getServerSnapshot,\n  )\n\n  // This allows us to directly render the portals without any additional wrapper\n  return (\n    <>\n      {Object.values(renderers)}\n    </>\n  )\n}\n\nexport interface EditorContentProps extends HTMLProps<HTMLDivElement> {\n  editor: Editor | null;\n  innerRef?: ForwardedRef<HTMLDivElement | null>;\n}\n\nfunction getInstance(): ContentComponent {\n  const subscribers = new Set<() => void>()\n  let renderers: Record<string, React.ReactPortal> = {}\n\n  return {\n    /**\n     * Subscribe to the editor instance's changes.\n     */\n    subscribe(callback: () => void) {\n      subscribers.add(callback)\n      return () => {\n        subscribers.delete(callback)\n      }\n    },\n    getSnapshot() {\n      return renderers\n    },\n    getServerSnapshot() {\n      return renderers\n    },\n    /**\n     * Adds a new NodeView Renderer to the editor.\n     */\n    setRenderer(id: string, renderer: ReactRenderer) {\n      renderers = {\n        ...renderers,\n        [id]: ReactDOM.createPortal(renderer.reactElement, renderer.element, id),\n      }\n\n      subscribers.forEach(subscriber => subscriber())\n    },\n    /**\n     * Removes a NodeView Renderer from the editor.\n     */\n    removeRenderer(id: string) {\n      const nextRenderers = { ...renderers }\n\n      delete nextRenderers[id]\n      renderers = nextRenderers\n      subscribers.forEach(subscriber => subscriber())\n    },\n  }\n}\n\nexport class PureEditorContent extends React.Component<\n  EditorContentProps,\n  { hasContentComponentInitialized: boolean }\n> {\n  editorContentRef: React.RefObject<any>\n\n  initialized: boolean\n\n  unsubscribeToContentComponent?: () => void\n\n  constructor(props: EditorContentProps) {\n    super(props)\n    this.editorContentRef = React.createRef()\n    this.initialized = false\n\n    this.state = {\n      hasContentComponentInitialized: Boolean((props.editor as EditorWithContentComponent | null)?.contentComponent),\n    }\n  }\n\n  componentDidMount() {\n    this.init()\n  }\n\n  componentDidUpdate() {\n    this.init()\n  }\n\n  init() {\n    const editor = this.props.editor as EditorWithContentComponent | null\n\n    if (editor && !editor.isDestroyed && editor.options.element) {\n      if (editor.contentComponent) {\n        return\n      }\n\n      const element = this.editorContentRef.current\n\n      element.append(...editor.options.element.childNodes)\n\n      editor.setOptions({\n        element,\n      })\n\n      editor.contentComponent = getInstance()\n\n      // Has the content component been initialized?\n      if (!this.state.hasContentComponentInitialized) {\n        // Subscribe to the content component\n        this.unsubscribeToContentComponent = editor.contentComponent.subscribe(() => {\n          this.setState(prevState => {\n            if (!prevState.hasContentComponentInitialized) {\n              return {\n                hasContentComponentInitialized: true,\n              }\n            }\n            return prevState\n          })\n\n          // Unsubscribe to previous content component\n          if (this.unsubscribeToContentComponent) {\n            this.unsubscribeToContentComponent()\n          }\n        })\n      }\n\n      editor.createNodeViews()\n\n      this.initialized = true\n    }\n  }\n\n  componentWillUnmount() {\n    const editor = this.props.editor as EditorWithContentComponent | null\n\n    if (!editor) {\n      return\n    }\n\n    this.initialized = false\n\n    if (!editor.isDestroyed) {\n      editor.view.setProps({\n        nodeViews: {},\n      })\n    }\n\n    if (this.unsubscribeToContentComponent) {\n      this.unsubscribeToContentComponent()\n    }\n\n    editor.contentComponent = null\n\n    if (!editor.options.element.firstChild) {\n      return\n    }\n\n    const newElement = document.createElement('div')\n\n    newElement.append(...editor.options.element.childNodes)\n\n    editor.setOptions({\n      element: newElement,\n    })\n  }\n\n  render() {\n    const { editor, innerRef, ...rest } = this.props\n\n    return (\n      <>\n        <div ref={mergeRefs(innerRef, this.editorContentRef)} {...rest} />\n        {/* @ts-ignore */}\n        {editor?.contentComponent && <Portals contentComponent={editor.contentComponent} />}\n      </>\n    )\n  }\n}\n\n// EditorContent should be re-created whenever the Editor instance changes\nconst EditorContentWithKey = forwardRef<HTMLDivElement, EditorContentProps>(\n  (props: Omit<EditorContentProps, 'innerRef'>, ref) => {\n    const key = React.useMemo(() => {\n      return Math.floor(Math.random() * 0xffffffff).toString()\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [props.editor])\n\n    // Can't use JSX here because it conflicts with the type definition of Vue's JSX, so use createElement\n    return React.createElement(PureEditorContent, {\n      key,\n      innerRef: ref,\n      ...props,\n    })\n  },\n)\n\nexport const EditorContent = React.memo(EditorContentWithKey)\n", "'use strict';\n\n// do not edit .js files directly - edit src/index.jst\n\n\n  var envHasBigInt64Array = typeof BigInt64Array !== 'undefined';\n\n\nmodule.exports = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n\n    if ((a instanceof Map) && (b instanceof Map)) {\n      if (a.size !== b.size) return false;\n      for (i of a.entries())\n        if (!b.has(i[0])) return false;\n      for (i of a.entries())\n        if (!equal(i[1], b.get(i[0]))) return false;\n      return true;\n    }\n\n    if ((a instanceof Set) && (b instanceof Set)) {\n      if (a.size !== b.size) return false;\n      for (i of a.entries())\n        if (!b.has(i[0])) return false;\n      return true;\n    }\n\n    if (ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (a[i] !== b[i]) return false;\n      return true;\n    }\n\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n\n      if (key === '_owner' && a.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner.\n        //  _owner contains circular references\n        // and is not needed when comparing the actual elements (and not their owners)\n        continue;\n      }\n\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a!==a && b!==b;\n};\n", "/**\n * @license React\n * use-sync-external-store-shim/with-selector.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var h=require(\"react\"),n=require(\"use-sync-external-store/shim\");function p(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var q=\"function\"===typeof Object.is?Object.is:p,r=n.useSyncExternalStore,t=h.useRef,u=h.useEffect,v=h.useMemo,w=h.useDebugValue;\nexports.useSyncExternalStoreWithSelector=function(a,b,e,l,g){var c=t(null);if(null===c.current){var f={hasValue:!1,value:null};c.current=f}else f=c.current;c=v(function(){function a(a){if(!c){c=!0;d=a;a=l(a);if(void 0!==g&&f.hasValue){var b=f.value;if(g(b,a))return k=b}return k=a}b=k;if(q(d,a))return b;var e=l(a);if(void 0!==g&&g(b,e))return b;d=a;return k=e}var c=!1,d,k,m=void 0===e?null:e;return[function(){return a(b())},null===m?void 0:function(){return a(m())}]},[b,e,l,g]);var d=r(a,c[0],c[1]);\nu(function(){f.hasValue=!0;f.value=d},[d]);w(d);return d};\n", "/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var React = require('react');\nvar shim = require('use-sync-external-store/shim');\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction is(x, y) {\n  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n  ;\n}\n\nvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\nvar useSyncExternalStore = shim.useSyncExternalStore;\n\n// for CommonJS interop.\n\nvar useRef = React.useRef,\n    useEffect = React.useEffect,\n    useMemo = React.useMemo,\n    useDebugValue = React.useDebugValue; // Same as useSyncExternalStore, but supports selector and isEqual arguments.\n\nfunction useSyncExternalStoreWithSelector(subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {\n  // Use this to track the rendered snapshot.\n  var instRef = useRef(null);\n  var inst;\n\n  if (instRef.current === null) {\n    inst = {\n      hasValue: false,\n      value: null\n    };\n    instRef.current = inst;\n  } else {\n    inst = instRef.current;\n  }\n\n  var _useMemo = useMemo(function () {\n    // Track the memoized state using closure variables that are local to this\n    // memoized instance of a getSnapshot function. Intentionally not using a\n    // useRef hook, because that state would be shared across all concurrent\n    // copies of the hook/component.\n    var hasMemo = false;\n    var memoizedSnapshot;\n    var memoizedSelection;\n\n    var memoizedSelector = function (nextSnapshot) {\n      if (!hasMemo) {\n        // The first time the hook is called, there is no memoized result.\n        hasMemo = true;\n        memoizedSnapshot = nextSnapshot;\n\n        var _nextSelection = selector(nextSnapshot);\n\n        if (isEqual !== undefined) {\n          // Even if the selector has changed, the currently rendered selection\n          // may be equal to the new selection. We should attempt to reuse the\n          // current value if possible, to preserve downstream memoizations.\n          if (inst.hasValue) {\n            var currentSelection = inst.value;\n\n            if (isEqual(currentSelection, _nextSelection)) {\n              memoizedSelection = currentSelection;\n              return currentSelection;\n            }\n          }\n        }\n\n        memoizedSelection = _nextSelection;\n        return _nextSelection;\n      } // We may be able to reuse the previous invocation's result.\n\n\n      // We may be able to reuse the previous invocation's result.\n      var prevSnapshot = memoizedSnapshot;\n      var prevSelection = memoizedSelection;\n\n      if (objectIs(prevSnapshot, nextSnapshot)) {\n        // The snapshot is the same as last time. Reuse the previous selection.\n        return prevSelection;\n      } // The snapshot has changed, so we need to compute a new selection.\n\n\n      // The snapshot has changed, so we need to compute a new selection.\n      var nextSelection = selector(nextSnapshot); // If a custom isEqual function is provided, use that to check if the data\n      // has changed. If it hasn't, return the previous selection. That signals\n      // to React that the selections are conceptually equal, and we can bail\n      // out of rendering.\n\n      // If a custom isEqual function is provided, use that to check if the data\n      // has changed. If it hasn't, return the previous selection. That signals\n      // to React that the selections are conceptually equal, and we can bail\n      // out of rendering.\n      if (isEqual !== undefined && isEqual(prevSelection, nextSelection)) {\n        return prevSelection;\n      }\n\n      memoizedSnapshot = nextSnapshot;\n      memoizedSelection = nextSelection;\n      return nextSelection;\n    }; // Assigning this to a constant so that Flow knows it can't change.\n\n\n    // Assigning this to a constant so that Flow knows it can't change.\n    var maybeGetServerSnapshot = getServerSnapshot === undefined ? null : getServerSnapshot;\n\n    var getSnapshotWithSelector = function () {\n      return memoizedSelector(getSnapshot());\n    };\n\n    var getServerSnapshotWithSelector = maybeGetServerSnapshot === null ? undefined : function () {\n      return memoizedSelector(maybeGetServerSnapshot());\n    };\n    return [getSnapshotWithSelector, getServerSnapshotWithSelector];\n  }, [getSnapshot, getServerSnapshot, selector, isEqual]),\n      getSelection = _useMemo[0],\n      getServerSelection = _useMemo[1];\n\n  var value = useSyncExternalStore(subscribe, getSelection, getServerSelection);\n  useEffect(function () {\n    inst.hasValue = true;\n    inst.value = value;\n  }, [value]);\n  useDebugValue(value);\n  return value;\n}\n\nexports.useSyncExternalStoreWithSelector = useSyncExternalStoreWithSelector;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.min.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n", "import type { Editor } from '@tiptap/core'\nimport deepEqual from 'fast-deep-equal/es6/react'\nimport {\n  useDebugValue, useEffect, useLayoutEffect, useState,\n} from 'react'\nimport { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector'\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect\n\nexport type EditorStateSnapshot<TEditor extends Editor | null = Editor | null> = {\n  editor: TEditor;\n  transactionNumber: number;\n};\n\nexport type UseEditorStateOptions<\n  TSelectorResult,\n  TEditor extends Editor | null = Editor | null,\n> = {\n  /**\n   * The editor instance.\n   */\n  editor: TEditor;\n  /**\n   * A selector function to determine the value to compare for re-rendering.\n   */\n  selector: (context: EditorStateSnapshot<TEditor>) => TSelectorResult;\n  /**\n   * A custom equality function to determine if the editor should re-render.\n   * @default `deepEqual` from `fast-deep-equal`\n   */\n  equalityFn?: (a: TSelectorR<PERSON>ult, b: TSelectorResult | null) => boolean;\n};\n\n/**\n * To synchronize the editor instance with the component state,\n * we need to create a separate instance that is not affected by the component re-renders.\n */\nclass EditorStateManager<TEditor extends Editor | null = Editor | null> {\n  private transactionNumber = 0\n\n  private lastTransactionNumber = 0\n\n  private lastSnapshot: EditorStateSnapshot<TEditor>\n\n  private editor: TEditor\n\n  private subscribers = new Set<() => void>()\n\n  constructor(initialEditor: TEditor) {\n    this.editor = initialEditor\n    this.lastSnapshot = { editor: initialEditor, transactionNumber: 0 }\n\n    this.getSnapshot = this.getSnapshot.bind(this)\n    this.getServerSnapshot = this.getServerSnapshot.bind(this)\n    this.watch = this.watch.bind(this)\n    this.subscribe = this.subscribe.bind(this)\n  }\n\n  /**\n   * Get the current editor instance.\n   */\n  getSnapshot(): EditorStateSnapshot<TEditor> {\n    if (this.transactionNumber === this.lastTransactionNumber) {\n      return this.lastSnapshot\n    }\n    this.lastTransactionNumber = this.transactionNumber\n    this.lastSnapshot = { editor: this.editor, transactionNumber: this.transactionNumber }\n    return this.lastSnapshot\n  }\n\n  /**\n   * Always disable the editor on the server-side.\n   */\n  getServerSnapshot(): EditorStateSnapshot<null> {\n    return { editor: null, transactionNumber: 0 }\n  }\n\n  /**\n   * Subscribe to the editor instance's changes.\n   */\n  subscribe(callback: () => void): () => void {\n    this.subscribers.add(callback)\n    return () => {\n      this.subscribers.delete(callback)\n    }\n  }\n\n  /**\n   * Watch the editor instance for changes.\n   */\n  watch(nextEditor: Editor | null): undefined | (() => void) {\n    this.editor = nextEditor as TEditor\n\n    if (this.editor) {\n      /**\n       * This will force a re-render when the editor state changes.\n       * This is to support things like `editor.can().toggleBold()` in components that `useEditor`.\n       * This could be more efficient, but it's a good trade-off for now.\n       */\n      const fn = () => {\n        this.transactionNumber += 1\n        this.subscribers.forEach(callback => callback())\n      }\n\n      const currentEditor = this.editor\n\n      currentEditor.on('transaction', fn)\n      return () => {\n        currentEditor.off('transaction', fn)\n      }\n    }\n\n    return undefined\n  }\n}\n\n/**\n * This hook allows you to watch for changes on the editor instance.\n * It will allow you to select a part of the editor state and re-render the component when it changes.\n * @example\n * ```tsx\n * const editor = useEditor({...options})\n * const { currentSelection } = useEditorState({\n *  editor,\n *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),\n * })\n */\nexport function useEditorState<TSelectorResult>(\n  options: UseEditorStateOptions<TSelectorResult, Editor>\n): TSelectorResult;\n/**\n * This hook allows you to watch for changes on the editor instance.\n * It will allow you to select a part of the editor state and re-render the component when it changes.\n * @example\n * ```tsx\n * const editor = useEditor({...options})\n * const { currentSelection } = useEditorState({\n *  editor,\n *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),\n * })\n */\nexport function useEditorState<TSelectorResult>(\n  options: UseEditorStateOptions<TSelectorResult, Editor | null>\n): TSelectorResult | null;\n\n/**\n * This hook allows you to watch for changes on the editor instance.\n * It will allow you to select a part of the editor state and re-render the component when it changes.\n * @example\n * ```tsx\n * const editor = useEditor({...options})\n * const { currentSelection } = useEditorState({\n *  editor,\n *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),\n * })\n */\nexport function useEditorState<TSelectorResult>(\n  options: UseEditorStateOptions<TSelectorResult, Editor> | UseEditorStateOptions<TSelectorResult, Editor | null>,\n): TSelectorResult | null {\n  const [editorStateManager] = useState(() => new EditorStateManager(options.editor))\n\n  // Using the `useSyncExternalStore` hook to sync the editor instance with the component state\n  const selectedState = useSyncExternalStoreWithSelector(\n    editorStateManager.subscribe,\n    editorStateManager.getSnapshot,\n    editorStateManager.getServerSnapshot,\n    options.selector as UseEditorStateOptions<TSelectorResult, Editor | null>['selector'],\n    options.equalityFn ?? deepEqual,\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    return editorStateManager.watch(options.editor)\n  }, [options.editor, editorStateManager])\n\n  useDebugValue(selectedState)\n\n  return selectedState\n}\n", "import { type EditorOptions, Editor } from '@tiptap/core'\nimport {\n  DependencyList,\n  MutableRefObject,\n  useDebugValue,\n  useEffect,\n  useRef,\n  useState,\n} from 'react'\nimport { useSyncExternalStore } from 'use-sync-external-store/shim'\n\nimport { useEditorState } from './useEditorState.js'\n\nconst isDev = process.env.NODE_ENV !== 'production'\nconst isSSR = typeof window === 'undefined'\nconst isNext = isSSR || Boolean(typeof window !== 'undefined' && (window as any).next)\n\n/**\n * The options for the `useEditor` hook.\n */\nexport type UseEditorOptions = Partial<EditorOptions> & {\n  /**\n   * Whether to render the editor on the first render.\n   * If client-side rendering, set this to `true`.\n   * If server-side rendering, set this to `false`.\n   * @default true\n   */\n  immediatelyRender?: boolean;\n  /**\n   * Whether to re-render the editor on each transaction.\n   * This is legacy behavior that will be removed in future versions.\n   * @default true\n   */\n  shouldRerenderOnTransaction?: boolean;\n};\n\n/**\n * This class handles the creation, destruction, and re-creation of the editor instance.\n */\nclass EditorInstanceManager {\n  /**\n   * The current editor instance.\n   */\n  private editor: Editor | null = null\n\n  /**\n   * The most recent options to apply to the editor.\n   */\n  private options: MutableRefObject<UseEditorOptions>\n\n  /**\n   * The subscriptions to notify when the editor instance\n   * has been created or destroyed.\n   */\n  private subscriptions = new Set<() => void>()\n\n  /**\n   * A timeout to destroy the editor if it was not mounted within a time frame.\n   */\n  private scheduledDestructionTimeout: ReturnType<typeof setTimeout> | undefined\n\n  /**\n   * Whether the editor has been mounted.\n   */\n  private isComponentMounted = false\n\n  /**\n   * The most recent dependencies array.\n   */\n  private previousDeps: DependencyList | null = null\n\n  /**\n   * The unique instance ID. This is used to identify the editor instance. And will be re-generated for each new instance.\n   */\n  public instanceId = ''\n\n  constructor(options: MutableRefObject<UseEditorOptions>) {\n    this.options = options\n    this.subscriptions = new Set<() => void>()\n    this.setEditor(this.getInitialEditor())\n    this.scheduleDestroy()\n\n    this.getEditor = this.getEditor.bind(this)\n    this.getServerSnapshot = this.getServerSnapshot.bind(this)\n    this.subscribe = this.subscribe.bind(this)\n    this.refreshEditorInstance = this.refreshEditorInstance.bind(this)\n    this.scheduleDestroy = this.scheduleDestroy.bind(this)\n    this.onRender = this.onRender.bind(this)\n    this.createEditor = this.createEditor.bind(this)\n  }\n\n  private setEditor(editor: Editor | null) {\n    this.editor = editor\n    this.instanceId = Math.random().toString(36).slice(2, 9)\n\n    // Notify all subscribers that the editor instance has been created\n    this.subscriptions.forEach(cb => cb())\n  }\n\n  private getInitialEditor() {\n    if (this.options.current.immediatelyRender === undefined) {\n      if (isSSR || isNext) {\n        // TODO in the next major release, we should throw an error here\n        if (isDev) {\n          /**\n           * Throw an error in development, to make sure the developer is aware that tiptap cannot be SSR'd\n           * and that they need to set `immediatelyRender` to `false` to avoid hydration mismatches.\n           */\n          console.warn(\n            'Tiptap Error: SSR has been detected, please set `immediatelyRender` explicitly to `false` to avoid hydration mismatches.',\n          )\n        }\n\n        // Best faith effort in production, run the code in the legacy mode to avoid hydration mismatches and errors in production\n        return null\n      }\n\n      // Default to immediately rendering when client-side rendering\n      return this.createEditor()\n    }\n\n    if (this.options.current.immediatelyRender && isSSR && isDev) {\n      // Warn in development, to make sure the developer is aware that tiptap cannot be SSR'd, set `immediatelyRender` to `false` to avoid hydration mismatches.\n      throw new Error(\n        'Tiptap Error: SSR has been detected, and `immediatelyRender` has been set to `true` this is an unsupported configuration that may result in errors, explicitly set `immediatelyRender` to `false` to avoid hydration mismatches.',\n      )\n    }\n\n    if (this.options.current.immediatelyRender) {\n      return this.createEditor()\n    }\n\n    return null\n  }\n\n  /**\n   * Create a new editor instance. And attach event listeners.\n   */\n  private createEditor(): Editor {\n    const optionsToApply: Partial<EditorOptions> = {\n      ...this.options.current,\n      // Always call the most recent version of the callback function by default\n      onBeforeCreate: (...args) => this.options.current.onBeforeCreate?.(...args),\n      onBlur: (...args) => this.options.current.onBlur?.(...args),\n      onCreate: (...args) => this.options.current.onCreate?.(...args),\n      onDestroy: (...args) => this.options.current.onDestroy?.(...args),\n      onFocus: (...args) => this.options.current.onFocus?.(...args),\n      onSelectionUpdate: (...args) => this.options.current.onSelectionUpdate?.(...args),\n      onTransaction: (...args) => this.options.current.onTransaction?.(...args),\n      onUpdate: (...args) => this.options.current.onUpdate?.(...args),\n      onContentError: (...args) => this.options.current.onContentError?.(...args),\n      onDrop: (...args) => this.options.current.onDrop?.(...args),\n      onPaste: (...args) => this.options.current.onPaste?.(...args),\n    }\n    const editor = new Editor(optionsToApply)\n\n    // no need to keep track of the event listeners, they will be removed when the editor is destroyed\n\n    return editor\n  }\n\n  /**\n   * Get the current editor instance.\n   */\n  getEditor(): Editor | null {\n    return this.editor\n  }\n\n  /**\n   * Always disable the editor on the server-side.\n   */\n  getServerSnapshot(): null {\n    return null\n  }\n\n  /**\n   * Subscribe to the editor instance's changes.\n   */\n  subscribe(onStoreChange: () => void) {\n    this.subscriptions.add(onStoreChange)\n\n    return () => {\n      this.subscriptions.delete(onStoreChange)\n    }\n  }\n\n  static compareOptions(a: UseEditorOptions, b: UseEditorOptions) {\n    return (Object.keys(a) as (keyof UseEditorOptions)[]).every(key => {\n      if (['onCreate', 'onBeforeCreate', 'onDestroy', 'onUpdate', 'onTransaction', 'onFocus', 'onBlur', 'onSelectionUpdate', 'onContentError', 'onDrop', 'onPaste'].includes(key)) {\n        // we don't want to compare callbacks, they are always different and only registered once\n        return true\n      }\n\n      // We often encourage putting extensions inlined in the options object, so we will do a slightly deeper comparison here\n      if (key === 'extensions' && a.extensions && b.extensions) {\n        if (a.extensions.length !== b.extensions.length) {\n          return false\n        }\n        return a.extensions.every((extension, index) => {\n          if (extension !== b.extensions?.[index]) {\n            return false\n          }\n          return true\n        })\n      }\n      if (a[key] !== b[key]) {\n        // if any of the options have changed, we should update the editor options\n        return false\n      }\n      return true\n    })\n  }\n\n  /**\n   * On each render, we will create, update, or destroy the editor instance.\n   * @param deps The dependencies to watch for changes\n   * @returns A cleanup function\n   */\n  onRender(deps: DependencyList) {\n    // The returned callback will run on each render\n    return () => {\n      this.isComponentMounted = true\n      // Cleanup any scheduled destructions, since we are currently rendering\n      clearTimeout(this.scheduledDestructionTimeout)\n\n      if (this.editor && !this.editor.isDestroyed && deps.length === 0) {\n        // if the editor does exist & deps are empty, we don't need to re-initialize the editor generally\n        if (!EditorInstanceManager.compareOptions(this.options.current, this.editor.options)) {\n          // But, the options are different, so we need to update the editor options\n          // Still, this is faster than re-creating the editor\n          this.editor.setOptions({\n            ...this.options.current,\n            editable: this.editor.isEditable,\n          })\n        }\n      } else {\n        // When the editor:\n        // - does not yet exist\n        // - is destroyed\n        // - the deps array changes\n        // We need to destroy the editor instance and re-initialize it\n        this.refreshEditorInstance(deps)\n      }\n\n      return () => {\n        this.isComponentMounted = false\n        this.scheduleDestroy()\n      }\n    }\n  }\n\n  /**\n   * Recreate the editor instance if the dependencies have changed.\n   */\n  private refreshEditorInstance(deps: DependencyList) {\n    if (this.editor && !this.editor.isDestroyed) {\n      // Editor instance already exists\n      if (this.previousDeps === null) {\n        // If lastDeps has not yet been initialized, reuse the current editor instance\n        this.previousDeps = deps\n        return\n      }\n      const depsAreEqual = this.previousDeps.length === deps.length\n        && this.previousDeps.every((dep, index) => dep === deps[index])\n\n      if (depsAreEqual) {\n        // deps exist and are equal, no need to recreate\n        return\n      }\n    }\n\n    if (this.editor && !this.editor.isDestroyed) {\n      // Destroy the editor instance if it exists\n      this.editor.destroy()\n    }\n\n    this.setEditor(this.createEditor())\n\n    // Update the lastDeps to the current deps\n    this.previousDeps = deps\n  }\n\n  /**\n   * Schedule the destruction of the editor instance.\n   * This will only destroy the editor if it was not mounted on the next tick.\n   * This is to avoid destroying the editor instance when it's actually still mounted.\n   */\n  private scheduleDestroy() {\n    const currentInstanceId = this.instanceId\n    const currentEditor = this.editor\n\n    // Wait two ticks to see if the component is still mounted\n    this.scheduledDestructionTimeout = setTimeout(() => {\n      if (this.isComponentMounted && this.instanceId === currentInstanceId) {\n        // If still mounted on the following tick, with the same instanceId, do not destroy the editor\n        if (currentEditor) {\n          // just re-apply options as they might have changed\n          currentEditor.setOptions(this.options.current)\n        }\n        return\n      }\n      if (currentEditor && !currentEditor.isDestroyed) {\n        currentEditor.destroy()\n        if (this.instanceId === currentInstanceId) {\n          this.setEditor(null)\n        }\n      }\n      // This allows the effect to run again between ticks\n      // which may save us from having to re-create the editor\n    }, 1)\n  }\n}\n\n/**\n * This hook allows you to create an editor instance.\n * @param options The editor options\n * @param deps The dependencies to watch for changes\n * @returns The editor instance\n * @example const editor = useEditor({ extensions: [...] })\n */\nexport function useEditor(\n  options: UseEditorOptions & { immediatelyRender: true },\n  deps?: DependencyList\n): Editor;\n\n/**\n * This hook allows you to create an editor instance.\n * @param options The editor options\n * @param deps The dependencies to watch for changes\n * @returns The editor instance\n * @example const editor = useEditor({ extensions: [...] })\n */\nexport function useEditor(options?: UseEditorOptions, deps?: DependencyList): Editor | null;\n\nexport function useEditor(\n  options: UseEditorOptions = {},\n  deps: DependencyList = [],\n): Editor | null {\n  const mostRecentOptions = useRef(options)\n\n  mostRecentOptions.current = options\n\n  const [instanceManager] = useState(() => new EditorInstanceManager(mostRecentOptions))\n\n  const editor = useSyncExternalStore(\n    instanceManager.subscribe,\n    instanceManager.getEditor,\n    instanceManager.getServerSnapshot,\n  )\n\n  useDebugValue(editor)\n\n  // This effect will handle creating/updating the editor instance\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useEffect(instanceManager.onRender(deps))\n\n  // The default behavior is to re-render on each transaction\n  // This is legacy behavior that will be removed in future versions\n  useEditorState({\n    editor,\n    selector: ({ transactionNumber }) => {\n      if (options.shouldRerenderOnTransaction === false) {\n        // This will prevent the editor from re-rendering on each transaction\n        return null\n      }\n\n      // This will avoid re-rendering on the first transaction when `immediatelyRender` is set to `true`\n      if (options.immediatelyRender && transactionNumber === 0) {\n        return 0\n      }\n      return transactionNumber + 1\n    },\n  })\n\n  return editor\n}\n", "import { Editor } from '@tiptap/core'\nimport React, {\n  createContext, HTMLAttributes, ReactNode, useContext,\n} from 'react'\n\nimport { EditorContent } from './EditorContent.js'\nimport { useEditor, UseEditorOptions } from './useEditor.js'\n\nexport type EditorContextValue = {\n  editor: Editor | null;\n}\n\nexport const EditorContext = createContext<EditorContextValue>({\n  editor: null,\n})\n\nexport const EditorConsumer = EditorContext.Consumer\n\n/**\n * A hook to get the current editor instance.\n */\nexport const useCurrentEditor = () => useContext(EditorContext)\n\nexport type EditorProviderProps = {\n  children?: ReactNode;\n  slotBefore?: ReactNode;\n  slotAfter?: ReactNode;\n  editorContainerProps?: HTMLAttributes<HTMLDivElement>;\n} & UseEditorOptions\n\n/**\n * This is the provider component for the editor.\n * It allows the editor to be accessible across the entire component tree\n * with `useCurrentEditor`.\n */\nexport function EditorProvider({\n  children, slotAfter, slotBefore, editorContainerProps = {}, ...editorOptions\n}: EditorProviderProps) {\n  const editor = useEditor(editorOptions)\n\n  if (!editor) {\n    return null\n  }\n\n  return (\n    <EditorContext.Provider value={{ editor }}>\n      {slotBefore}\n      <EditorConsumer>\n        {({ editor: currentEditor }) => (\n          <EditorContent editor={currentEditor} {...editorContainerProps} />\n        )}\n      </EditorConsumer>\n      {children}\n      {slotAfter}\n    </EditorContext.Provider>\n  )\n}\n", "import { BubbleMenuPlugin, BubbleMenuPluginProps } from '@tiptap/extension-bubble-menu'\nimport React, { useEffect, useState } from 'react'\n\nimport { useCurrentEditor } from './Context.js'\n\ntype Optional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>;\n\nexport type BubbleMenuProps = Omit<Optional<BubbleMenuPluginProps, 'pluginKey'>, 'element' | 'editor'> & {\n  editor: BubbleMenuPluginProps['editor'] | null;\n  className?: string;\n  children: React.ReactNode;\n  updateDelay?: number;\n};\n\nexport const BubbleMenu = (props: BubbleMenuProps) => {\n  const [element, setElement] = useState<HTMLDivElement | null>(null)\n  const { editor: currentEditor } = useCurrentEditor()\n\n  useEffect(() => {\n    if (!element) {\n      return\n    }\n\n    if (props.editor?.isDestroyed || currentEditor?.isDestroyed) {\n      return\n    }\n\n    const {\n      pluginKey = 'bubbleMenu', editor, tippyOptions = {}, updateDelay, shouldShow = null,\n    } = props\n\n    const menuEditor = editor || currentEditor\n\n    if (!menuEditor) {\n      console.warn('BubbleMenu component is not rendered inside of an editor component or does not have editor prop.')\n      return\n    }\n\n    const plugin = BubbleMenuPlugin({\n      updateDelay,\n      editor: menuEditor,\n      element,\n      pluginKey,\n      shouldShow,\n      tippyOptions,\n    })\n\n    menuEditor.registerPlugin(plugin)\n    return () => { menuEditor.unregisterPlugin(pluginKey) }\n  }, [props.editor, currentEditor, element])\n\n  return (\n    <div ref={setElement} className={props.className} style={{ visibility: 'hidden' }}>\n      {props.children}\n    </div>\n  )\n}\n", "import { FloatingMenuPlugin, FloatingMenuPluginProps } from '@tiptap/extension-floating-menu'\nimport React, {\n  useEffect, useState,\n} from 'react'\n\nimport { useCurrentEditor } from './Context.js'\n\ntype Optional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>\n\nexport type FloatingMenuProps = Omit<Optional<FloatingMenuPluginProps, 'pluginKey'>, 'element' | 'editor'> & {\n  editor: FloatingMenuPluginProps['editor'] | null;\n  className?: string,\n  children: React.ReactNode\n}\n\nexport const FloatingMenu = (props: FloatingMenuProps) => {\n  const [element, setElement] = useState<HTMLDivElement | null>(null)\n  const { editor: currentEditor } = useCurrentEditor()\n\n  useEffect(() => {\n    if (!element) {\n      return\n    }\n\n    if (props.editor?.isDestroyed || currentEditor?.isDestroyed) {\n      return\n    }\n\n    const {\n      pluginKey = 'floatingMenu',\n      editor,\n      tippyOptions = {},\n      shouldShow = null,\n    } = props\n\n    const menuEditor = editor || currentEditor\n\n    if (!menuEditor) {\n      console.warn('FloatingMenu component is not rendered inside of an editor component or does not have editor prop.')\n      return\n    }\n\n    const plugin = FloatingMenuPlugin({\n      pluginKey,\n      editor: menuEditor,\n      element,\n      tippyOptions,\n      shouldShow,\n    })\n\n    menuEditor.registerPlugin(plugin)\n    return () => { menuEditor.unregisterPlugin(pluginKey) }\n  }, [\n    props.editor,\n    currentEditor,\n    element,\n  ])\n\n  return (\n    <div ref={setElement} className={props.className} style={{ visibility: 'hidden' }}>\n      {props.children}\n    </div>\n  )\n}\n", "import { createContext, useContext } from 'react'\n\nexport interface ReactNodeViewContextProps {\n  onDragStart: (event: DragEvent) => void,\n  nodeViewContentRef: (element: HTMLElement | null) => void,\n}\n\nexport const ReactNodeViewContext = createContext<Partial<ReactNodeViewContextProps>>({\n  onDragStart: undefined,\n})\n\nexport const useReactNodeView = () => useContext(ReactNodeViewContext)\n", "import React from 'react'\n\nimport { useReactNodeView } from './useReactNodeView.js'\n\nexport interface NodeViewContentProps {\n  [key: string]: any,\n  as?: React.ElementType,\n}\n\nexport const NodeViewContent: React.FC<NodeViewContentProps> = props => {\n  const Tag = props.as || 'div'\n  const { nodeViewContentRef } = useReactNodeView()\n\n  return (\n    // @ts-ignore\n    <Tag\n      {...props}\n      ref={nodeViewContentRef}\n      data-node-view-content=\"\"\n      style={{\n        whiteSpace: 'pre-wrap',\n        ...props.style,\n      }}\n    />\n  )\n}\n", "import React from 'react'\n\nimport { useReactNodeView } from './useReactNodeView.js'\n\nexport interface NodeViewWrapperProps {\n  [key: string]: any,\n  as?: React.ElementType,\n}\n\nexport const NodeViewWrapper: React.FC<NodeViewWrapperProps> = React.forwardRef((props, ref) => {\n  const { onDragStart } = useReactNodeView()\n  const Tag = props.as || 'div'\n\n  return (\n    // @ts-ignore\n    <Tag\n      {...props}\n      ref={ref}\n      data-node-view-wrapper=\"\"\n      onDragStart={onDragStart}\n      style={{\n        whiteSpace: 'normal',\n        ...props.style,\n      }}\n    />\n  )\n})\n", "import type { Editor } from '@tiptap/core'\nimport type {\n  ComponentClass,\n  ForwardRefExoticComponent,\n  FunctionComponent,\n  PropsWithoutRef,\n  ReactNode,\n  RefAttributes,\n} from 'react'\nimport React, { version as reactVersion } from 'react'\nimport { flushSync } from 'react-dom'\n\nimport { EditorWithContentComponent } from './Editor.js'\n\n/**\n * Check if a component is a class component.\n * @param Component\n * @returns {boolean}\n */\nfunction isClassComponent(Component: any) {\n  return !!(\n    typeof Component === 'function'\n    && Component.prototype\n    && Component.prototype.isReactComponent\n  )\n}\n\n/**\n * Check if a component is a forward ref component.\n * @param Component\n * @returns {boolean}\n */\nfunction isForwardRefComponent(Component: any) {\n  return !!(\n    typeof Component === 'object'\n    && Component.$$typeof?.toString() === 'Symbol(react.forward_ref)'\n  )\n}\n\n/**\n * Check if we're running React 19+ by detecting if function components support ref props\n * @returns {boolean}\n */\nfunction isReact19Plus(): boolean {\n  // React 19 is detected by checking React version if available\n  // In practice, we'll use a more conservative approach and assume React 18 behavior\n  // unless we can definitively detect React 19\n  try {\n    // @ts-ignore\n    if (reactVersion) {\n      const majorVersion = parseInt(reactVersion.split('.')[0], 10)\n\n      return majorVersion >= 19\n    }\n  } catch {\n    // Fallback to React 18 behavior if we can't determine version\n  }\n  return false\n}\n\nexport interface ReactRendererOptions {\n  /**\n   * The editor instance.\n   * @type {Editor}\n   */\n  editor: Editor,\n\n  /**\n   * The props for the component.\n   * @type {Record<string, any>}\n   * @default {}\n   */\n  props?: Record<string, any>,\n\n  /**\n   * The tag name of the element.\n   * @type {string}\n   * @default 'div'\n   */\n  as?: string,\n\n  /**\n   * The class name of the element.\n   * @type {string}\n   * @default ''\n   * @example 'foo bar'\n   */\n  className?: string,\n}\n\ntype ComponentType<R, P> =\n  | ComponentClass<P>\n  | FunctionComponent<P>\n  | ForwardRefExoticComponent<PropsWithoutRef<P> & RefAttributes<R>>\n\n/**\n * The ReactRenderer class. It's responsible for rendering React components inside the editor.\n * @example\n * new ReactRenderer(MyComponent, {\n *   editor,\n *   props: {\n *     foo: 'bar',\n *   },\n *   as: 'span',\n * })\n*/\nexport class ReactRenderer<R = unknown, P extends Record<string, any> = object> {\n  id: string\n\n  editor: Editor\n\n  component: any\n\n  element: Element\n\n  props: P\n\n  reactElement: ReactNode\n\n  ref: R | null = null\n\n  /**\n   * Immediately creates element and renders the provided React component.\n   */\n  constructor(component: ComponentType<R, P>, {\n    editor,\n    props = {},\n    as = 'div',\n    className = '',\n  }: ReactRendererOptions) {\n    this.id = Math.floor(Math.random() * 0xFFFFFFFF).toString()\n    this.component = component\n    this.editor = editor as EditorWithContentComponent\n    this.props = props as P\n    this.element = document.createElement(as)\n    this.element.classList.add('react-renderer')\n\n    if (className) {\n      this.element.classList.add(...className.split(' '))\n    }\n\n    if (this.editor.isInitialized) {\n      // On first render, we need to flush the render synchronously\n      // Renders afterwards can be async, but this fixes a cursor positioning issue\n      flushSync(() => {\n        this.render()\n      })\n    } else {\n      this.render()\n    }\n  }\n\n  /**\n   * Render the React component.\n   */\n  render(): void {\n    const Component = this.component\n    const props = this.props\n    const editor = this.editor as EditorWithContentComponent\n\n    // Handle ref forwarding with React 18/19 compatibility\n    const isReact19 = isReact19Plus()\n    const isClassComp = isClassComponent(Component)\n    const isForwardRefComp = isForwardRefComponent(Component)\n\n    const elementProps = { ...props }\n\n    if (!elementProps.ref) {\n      if (isReact19) {\n        // React 19: ref is a standard prop for all components\n        // @ts-ignore - Setting ref prop for React 19 compatibility\n        elementProps.ref = (ref: R) => {\n          this.ref = ref\n        }\n      } else if (isClassComp || isForwardRefComp) {\n        // React 18 and prior: only set ref for class components and forwardRef components\n        // @ts-ignore - Setting ref prop for React 18 class/forwardRef components\n        elementProps.ref = (ref: R) => {\n          this.ref = ref\n        }\n      }\n      // For function components in React 18, we can't use ref - the component won't receive it\n      // This is a limitation we have to accept for React 18 function components without forwardRef\n    }\n\n    this.reactElement = <Component {...elementProps} />\n\n    editor?.contentComponent?.setRenderer(this.id, this)\n  }\n\n  /**\n   * Re-renders the React component with new props.\n   */\n  updateProps(props: Record<string, any> = {}): void {\n    this.props = {\n      ...this.props,\n      ...props,\n    }\n\n    this.render()\n  }\n\n  /**\n   * Destroy the React component.\n   */\n  destroy(): void {\n    const editor = this.editor as EditorWithContentComponent\n\n    editor?.contentComponent?.removeRenderer(this.id)\n  }\n\n  /**\n   * Update the attributes of the element that holds the React component.\n   */\n  updateAttributes(attributes: Record<string, string>): void {\n    Object.keys(attributes).forEach(key => {\n      this.element.setAttribute(key, attributes[key])\n    })\n  }\n}\n", "import type {\n  DecorationWithType, Editor, NodeViewRenderer, NodeViewRendererOptions,\n} from '@tiptap/core'\nimport { getRenderedAttributes, NodeView } from '@tiptap/core'\nimport type { Node, Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport type { Decoration, DecorationSource, NodeView as ProseMirrorNodeView } from '@tiptap/pm/view'\nimport type { ComponentType, NamedExoticComponent } from 'react'\nimport React, { createElement, createRef, memo } from 'react'\n\nimport { EditorWithContentComponent } from './Editor.js'\nimport { ReactRenderer } from './ReactRenderer.js'\nimport type { ReactNodeViewProps } from './types.js'\nimport type { ReactNodeViewContextProps } from './useReactNodeView.js'\nimport { ReactNodeViewContext } from './useReactNodeView.js'\n\nexport interface ReactNodeViewRendererOptions extends NodeViewRendererOptions {\n  /**\n   * This function is called when the node view is updated.\n   * It allows you to compare the old node with the new node and decide if the component should update.\n   */\n  update:\n    | ((props: {\n        oldNode: ProseMirrorNode;\n        oldDecorations: readonly Decoration[];\n        oldInnerDecorations: DecorationSource;\n        newNode: ProseMirrorNode;\n        newDecorations: readonly Decoration[];\n        innerDecorations: DecorationSource;\n        updateProps: () => void;\n      }) => boolean)\n    | null;\n  /**\n   * The tag name of the element wrapping the React component.\n   */\n  as?: string;\n  /**\n   * The class name of the element wrapping the React component.\n   */\n  className?: string;\n  /**\n   * Attributes that should be applied to the element wrapping the React component.\n   * If this is a function, it will be called each time the node view is updated.\n   * If this is an object, it will be applied once when the node view is mounted.\n   */\n  attrs?:\n    | Record<string, string>\n    | ((props: {\n        node: ProseMirrorNode;\n        HTMLAttributes: Record<string, any>;\n      }) => Record<string, string>);\n}\n\nexport class ReactNodeView<\n  T = HTMLElement,\n  Component extends ComponentType<ReactNodeViewProps<T>> = ComponentType<ReactNodeViewProps<T>>,\n  NodeEditor extends Editor = Editor,\n  Options extends ReactNodeViewRendererOptions = ReactNodeViewRendererOptions,\n> extends NodeView<Component, NodeEditor, Options> {\n  /**\n   * The renderer instance.\n   */\n  renderer!: ReactRenderer<unknown, ReactNodeViewProps<T>>\n\n  /**\n   * The element that holds the rich-text content of the node.\n   */\n  contentDOMElement!: HTMLElement | null\n\n  /**\n   * Setup the React component.\n   * Called on initialization.\n   */\n  mount() {\n    const props = {\n      editor: this.editor,\n      node: this.node,\n      decorations: this.decorations as DecorationWithType[],\n      innerDecorations: this.innerDecorations,\n      view: this.view,\n      selected: false,\n      extension: this.extension,\n      HTMLAttributes: this.HTMLAttributes,\n      getPos: () => this.getPos(),\n      updateAttributes: (attributes = {}) => this.updateAttributes(attributes),\n      deleteNode: () => this.deleteNode(),\n      ref: createRef<T>(),\n    } satisfies ReactNodeViewProps<T>\n\n    if (!(this.component as any).displayName) {\n      const capitalizeFirstChar = (string: string): string => {\n        return string.charAt(0).toUpperCase() + string.substring(1)\n      }\n\n      this.component.displayName = capitalizeFirstChar(this.extension.name)\n    }\n\n    const onDragStart = this.onDragStart.bind(this)\n    const nodeViewContentRef: ReactNodeViewContextProps['nodeViewContentRef'] = element => {\n      if (element && this.contentDOMElement && element.firstChild !== this.contentDOMElement) {\n        element.appendChild(this.contentDOMElement)\n      }\n    }\n    const context = { onDragStart, nodeViewContentRef }\n    const Component = this.component\n    // For performance reasons, we memoize the provider component\n    // And all of the things it requires are declared outside of the component, so it doesn't need to re-render\n    const ReactNodeViewProvider: NamedExoticComponent<ReactNodeViewProps<T>> = memo(componentProps => {\n      return (\n        <ReactNodeViewContext.Provider value={context}>\n          {createElement(Component, componentProps)}\n        </ReactNodeViewContext.Provider>\n      )\n    })\n\n    ReactNodeViewProvider.displayName = 'ReactNodeView'\n\n    if (this.node.isLeaf) {\n      this.contentDOMElement = null\n    } else if (this.options.contentDOMElementTag) {\n      this.contentDOMElement = document.createElement(this.options.contentDOMElementTag)\n    } else {\n      this.contentDOMElement = document.createElement(this.node.isInline ? 'span' : 'div')\n    }\n\n    if (this.contentDOMElement) {\n      this.contentDOMElement.dataset.nodeViewContentReact = ''\n      // For some reason the whiteSpace prop is not inherited properly in Chrome and Safari\n      // With this fix it seems to work fine\n      // See: https://github.com/ueberdosis/tiptap/issues/1197\n      this.contentDOMElement.style.whiteSpace = 'inherit'\n    }\n\n    let as = this.node.isInline ? 'span' : 'div'\n\n    if (this.options.as) {\n      as = this.options.as\n    }\n\n    const { className = '' } = this.options\n\n    this.handleSelectionUpdate = this.handleSelectionUpdate.bind(this)\n\n    this.renderer = new ReactRenderer(ReactNodeViewProvider, {\n      editor: this.editor,\n      props,\n      as,\n      className: `node-${this.node.type.name} ${className}`.trim(),\n    })\n\n    this.editor.on('selectionUpdate', this.handleSelectionUpdate)\n    this.updateElementAttributes()\n  }\n\n  /**\n   * Return the DOM element.\n   * This is the element that will be used to display the node view.\n   */\n  get dom() {\n    if (\n      this.renderer.element.firstElementChild\n      && !this.renderer.element.firstElementChild?.hasAttribute('data-node-view-wrapper')\n    ) {\n      throw Error('Please use the NodeViewWrapper component for your node view.')\n    }\n\n    return this.renderer.element as HTMLElement\n  }\n\n  /**\n   * Return the content DOM element.\n   * This is the element that will be used to display the rich-text content of the node.\n   */\n  get contentDOM() {\n    if (this.node.isLeaf) {\n      return null\n    }\n\n    return this.contentDOMElement\n  }\n\n  /**\n   * On editor selection update, check if the node is selected.\n   * If it is, call `selectNode`, otherwise call `deselectNode`.\n   */\n  handleSelectionUpdate() {\n    const { from, to } = this.editor.state.selection\n    const pos = this.getPos()\n\n    if (typeof pos !== 'number') {\n      return\n    }\n\n    if (from <= pos && to >= pos + this.node.nodeSize) {\n      if (this.renderer.props.selected) {\n        return\n      }\n\n      this.selectNode()\n    } else {\n      if (!this.renderer.props.selected) {\n        return\n      }\n\n      this.deselectNode()\n    }\n  }\n\n  /**\n   * On update, update the React component.\n   * To prevent unnecessary updates, the `update` option can be used.\n   */\n  update(\n    node: Node,\n    decorations: readonly Decoration[],\n    innerDecorations: DecorationSource,\n  ): boolean {\n    const rerenderComponent = (props?: Record<string, any>) => {\n      this.renderer.updateProps(props)\n      if (typeof this.options.attrs === 'function') {\n        this.updateElementAttributes()\n      }\n    }\n\n    if (node.type !== this.node.type) {\n      return false\n    }\n\n    if (typeof this.options.update === 'function') {\n      const oldNode = this.node\n      const oldDecorations = this.decorations\n      const oldInnerDecorations = this.innerDecorations\n\n      this.node = node\n      this.decorations = decorations\n      this.innerDecorations = innerDecorations\n\n      return this.options.update({\n        oldNode,\n        oldDecorations,\n        newNode: node,\n        newDecorations: decorations,\n        oldInnerDecorations,\n        innerDecorations,\n        updateProps: () => rerenderComponent({ node, decorations, innerDecorations }),\n      })\n    }\n\n    if (\n      node === this.node\n      && this.decorations === decorations\n      && this.innerDecorations === innerDecorations\n    ) {\n      return true\n    }\n\n    this.node = node\n    this.decorations = decorations\n    this.innerDecorations = innerDecorations\n\n    rerenderComponent({ node, decorations, innerDecorations })\n\n    return true\n  }\n\n  /**\n   * Select the node.\n   * Add the `selected` prop and the `ProseMirror-selectednode` class.\n   */\n  selectNode() {\n    this.renderer.updateProps({\n      selected: true,\n    })\n    this.renderer.element.classList.add('ProseMirror-selectednode')\n  }\n\n  /**\n   * Deselect the node.\n   * Remove the `selected` prop and the `ProseMirror-selectednode` class.\n   */\n  deselectNode() {\n    this.renderer.updateProps({\n      selected: false,\n    })\n    this.renderer.element.classList.remove('ProseMirror-selectednode')\n  }\n\n  /**\n   * Destroy the React component instance.\n   */\n  destroy() {\n    this.renderer.destroy()\n    this.editor.off('selectionUpdate', this.handleSelectionUpdate)\n    this.contentDOMElement = null\n  }\n\n  /**\n   * Update the attributes of the top-level element that holds the React component.\n   * Applying the attributes defined in the `attrs` option.\n   */\n  updateElementAttributes() {\n    if (this.options.attrs) {\n      let attrsObj: Record<string, string> = {}\n\n      if (typeof this.options.attrs === 'function') {\n        const extensionAttributes = this.editor.extensionManager.attributes\n        const HTMLAttributes = getRenderedAttributes(this.node, extensionAttributes)\n\n        attrsObj = this.options.attrs({ node: this.node, HTMLAttributes })\n      } else {\n        attrsObj = this.options.attrs\n      }\n\n      this.renderer.updateAttributes(attrsObj)\n    }\n  }\n}\n\n/**\n * Create a React node view renderer.\n */\nexport function ReactNodeViewRenderer<T = HTMLElement>(\n  component: ComponentType<ReactNodeViewProps<T>>,\n  options?: Partial<ReactNodeViewRendererOptions>,\n): NodeViewRenderer {\n  return props => {\n    // try to get the parent component\n    // this is important for vue devtools to show the component hierarchy correctly\n    // maybe it’s `undefined` because <editor-content> isn’t rendered yet\n    if (!(props.editor as EditorWithContentComponent).contentComponent) {\n      return {} as unknown as ProseMirrorNodeView\n    }\n\n    return new ReactNodeView<T>(component, props, options)\n  }\n}\n"], "names": ["require$$0", "React", "shimModule", "require$$1", "useSyncExternalStore", "withSelectorModule", "useSyncExternalStoreWithSelector", "reactVersion"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASa,IAAI,CAAC,yMAACA,UAAgB;IAAC,SAAS,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;QAAC,OAAO,CAAC,KAAG,CAAC,IAAA,CAAG,CAAC,KAAG,CAAC,IAAE,CAAC,GAAC,CAAC,KAAG,CAAC,GAAC,CAAC,CAAC,IAAE,CAAC,KAAG,CAAC,IAAE,CAAC,KAAG;IAAC;IAAC,IAAI,CAAC,GAAC,UAAU,KAAG,OAAO,MAAM,CAAC,EAAE,GAAC,MAAM,CAAC,EAAE,GAAC,CAAC,EAAC,CAAC,GAAC,CAAC,CAAC,QAAQ,EAAC,CAAC,GAAC,CAAC,CAAC,SAAS,EAAC,CAAC,GAAC,CAAC,CAAC,eAAe,EAAC,CAAC,GAAC,CAAC,CAAC,aAAa;IAAC,SAAS,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;QAAC,IAAI,CAAC,GAAC,CAAC,EAAE,EAAC,CAAC,GAAC,CAAC,CAAC;YAAC,IAAI,EAAC;gBAAC,KAAK,EAAC,CAAC;gBAAC,WAAW,EAAC;YAAC;QAAC,CAAC,CAAC,EAAC,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAC,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC;QAAC,CAAC,CAAC,UAAU;YAAC,CAAC,CAAC,KAAK,GAAC,CAAC;YAAC,CAAC,CAAC,WAAW,GAAC,CAAC;YAAC,CAAC,CAAC,CAAC,CAAC,IAAE,CAAC,CAAC;gBAAC,IAAI,EAAC;YAAC,CAAC;QAAC,CAAC,EAAC;YAAC,CAAC;YAAC,CAAC;YAAC,CAAC;SAAC,CAAC;QAAC,CAAC,CAAC,UAAU;YAAC,CAAC,CAAC,CAAC,CAAC,IAAE,CAAC,CAAC;gBAAC,IAAI,EAAC;YAAC,CAAC,CAAC;YAAC,OAAO,CAAC,CAAC,UAAU;gBAAC,CAAC,CAAC,CAAC,CAAC,IAAE,CAAC,CAAC;oBAAC,IAAI,EAAC;gBAAC,CAAC;YAAC,CAAC;QAAC,CAAC,EAAC;YAAC,CAAC;SAAC,CAAC;QAAC,CAAC,CAAC,CAAC,CAAC;QAAC,OAAO;IAAC;IAClc,SAAS,CAAC,CAAC,CAAC,CAAC;QAAC,IAAI,CAAC,GAAC,CAAC,CAAC,WAAW;QAAC,CAAC,GAAC,CAAC,CAAC,KAAK;QAAC,IAAG;YAAC,IAAI,CAAC,GAAC,CAAC,EAAE;YAAC,OAAM,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC;QAAC,EAAC,OAAM,CAAC,EAAC;YAAC,OAAM,CAAC;QAAC;IAAC;IAAC,SAAS,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;QAAC,OAAO,CAAC;IAAE;IAAC,IAAI,CAAC,GAAC,WAAW,KAAG,OAAO,MAAM,IAAE,WAAW,KAAG,OAAO,MAAM,CAAC,QAAQ,IAAE,WAAW,KAAG,OAAO,MAAM,CAAC,QAAQ,CAAC,aAAa,GAAC,CAAC,GAAC,CAAC;IAAC,uCAA4B,CAAA,oBAAA,GAAC,KAAK,CAAC,KAAG,CAAC,CAAC,oBAAoB,GAAC,CAAC,CAAC,oBAAoB,GAAC,CAAC;;;;;;;;;;;;;;;;ICE3U,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,WAAc,CAAF;QACvC,CAAC,WAAW;YAId,yCAAA,GACA,IACE,OAAO,8BAA8B,KAAK,WAAW,IACrD,OAAO,8BAA8B,CAAC,2BAA2B,KAC/D,YACF;gBACA,8BAA8B,CAAC,2BAA2B,CAAC,IAAI,KAAK,EAAE,CAAC;;YAE/D,IAAIC,OAAK,GAAGD,gNAAgB;YAEtC,IAAI,oBAAoB,GAAGC,OAAK,CAAC,kDAAkD;YAEnF,SAAS,KAAK,CAAC,MAAM,EAAE;gBACrB;oBACE;wBACE,IAAK,IAAI,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE,IAAI,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE;4BACjH,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;;wBAGpC,YAAY,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC;;;;YAKzC,SAAS,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE;gBAC3C,mDAAA;gBACA,6CAAA;gBACE;oBACE,IAAI,sBAAsB,GAAG,oBAAoB,CAAC,sBAAsB;oBACxE,IAAI,KAAK,GAAG,sBAAsB,CAAC,gBAAgB,EAAE;oBAErD,IAAI,KAAK,KAAK,EAAE,EAAE;wBAChB,MAAM,IAAI,IAAI;wBACd,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;4BAAC,KAAK;yBAAC,CAAC;qBAC5B,CAAA,+DAAA;oBAGD,IAAI,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,SAAU,IAAI,EAAE;wBAC5C,OAAO,MAAM,CAAC,IAAI,CAAC;oBACzB,CAAK,CAAC,CAAC,CAAA,+CAAA;oBAEH,cAAc,CAAC,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,CAAC,CAAA,oEAAA;oBACjD,6DAAA;oBACA,gEAAA;oBAEI,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,cAAc,CAAC;;;YAI1E;;;EAGA,GACA,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;gBAChB,OAAO,CAAC,KAAK,CAAC,IAAA,CAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA,sCAAA;;;YAItE,IAAI,QAAQ,GAAG,OAAO,MAAM,CAAC,EAAE,KAAK,UAAU,GAAG,MAAM,CAAC,EAAE,GAAG,EAAE;YAE/D,+CAAA;YAEA,IAAI,QAAQ,GAAGA,OAAK,CAAC,QAAQ,EACzB,SAAS,GAAGA,OAAK,CAAC,SAAS,EAC3B,eAAe,GAAGA,OAAK,CAAC,eAAe,EACvC,aAAa,GAAGA,OAAK,CAAC,aAAa;YACvC,IAAI,iBAAiB,GAAG,KAAK;YAC7B,IAAI,0BAA0B,GAAG,KAAK,CAAC,CAAA,0EAAA;YACvC,6EAAA;YACA,6EAAA;YACA,0EAAA;YACA,gEAAA;YACA,4EAAA;YACA,kBAAA;YACA,EAAA;YACA,8EAAA;YACA,8EAAA;YAEA,SAAS,oBAAoB,CAAC,SAAS,EAAE,WAAW,EACpD,8EAAA;YACA,kEAAA;YACA,sBAAA;YACA,iBAAiB,EAAE;gBACjB;oBACE,IAAI,CAAC,iBAAiB,EAAE;wBACtB,IAAIA,OAAK,CAAC,eAAe,KAAK,SAAS,EAAE;4BACvC,iBAAiB,GAAG,IAAI;4BAExB,KAAK,CAAC,gEAAgE,GAAG,6CAA6C,GAAG,gEAAgE,GAAG,yBAAyB,CAAC;;;iBAG3N;gBACH,qEAAA;gBACA,4DAAA;gBACA,sBAAA;gBAGE,IAAI,KAAK,GAAG,WAAW,EAAE;gBAEzB;oBACE,IAAI,CAAC,0BAA0B,EAAE;wBAC/B,IAAI,WAAW,GAAG,WAAW,EAAE;wBAE/B,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,WAAW,CAAC,EAAE;4BACjC,KAAK,CAAC,sEAAsE,CAAC;4BAE7E,0BAA0B,GAAG,IAAI;;;iBAGtC;gBACH,sEAAA;gBACA,4EAAA;gBACA,qBAAA;gBACA,EAAA;gBACA,4EAAA;gBACA,gEAAA;gBACA,EAAA;gBACA,4EAAA;gBACA,8EAAA;gBACA,6BAAA;gBACA,EAAA;gBACA,4EAAA;gBACA,6CAAA;gBAGE,IAAI,SAAS,GAAG,QAAQ,CAAC;oBACvB,IAAI,EAAE;wBACJ,KAAK,EAAE,KAAK;wBACZ,WAAW,EAAE;;gBAEnB,CAAG,CAAC,EACE,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,EACxB,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA,6EAAA;gBACjC,wEAAA;gBACA,wBAAA;gBAGE,eAAe,CAAC,YAAY;oBAC1B,IAAI,CAAC,KAAK,GAAG,KAAK;oBAClB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,CAAA,qEAAA;oBACnC,wEAAA;oBACA,yEAAA;oBACA,qCAAA;oBAEI,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;wBACtC,qBAAA;wBACM,WAAW,CAAC;4BACV,IAAI,EAAE;wBACd,CAAO,CAAC;;iBAEL,EAAE;oBAAC,SAAS;oBAAE,KAAK;oBAAE,WAAW;iBAAC,CAAC;gBACnC,SAAS,CAAC,YAAY;oBACxB,yEAAA;oBACA,wCAAA;oBACI,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;wBACtC,qBAAA;wBACM,WAAW,CAAC;4BACV,IAAI,EAAE;wBACd,CAAO,CAAC;;oBAGJ,IAAI,iBAAiB,GAAG,YAAY;wBACxC,0EAAA;wBACA,sEAAA;wBACA,wEAAA;wBACA,+CAAA;wBACA,0EAAA;wBACA,uBAAA;wBACM,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;4BACxC,qBAAA;4BACQ,WAAW,CAAC;gCACV,IAAI,EAAE;4BAChB,CAAS,CAAC;;oBAEV,CAAK,CAAC,CAAA,yDAAA;oBAGF,OAAO,SAAS,CAAC,iBAAiB,CAAC;gBACvC,CAAG,EAAE;oBAAC,SAAS;iBAAC,CAAC;gBACf,aAAa,CAAC,KAAK,CAAC;gBACpB,OAAO,KAAK;;YAGd,SAAS,sBAAsB,CAAC,IAAI,EAAE;gBACpC,IAAI,iBAAiB,GAAG,IAAI,CAAC,WAAW;gBACxC,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK;gBAE1B,IAAI;oBACF,IAAI,SAAS,GAAG,iBAAiB,EAAE;oBACnC,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC;iBACvC,CAAC,OAAO,KAAK,EAAE;oBACd,OAAO,IAAI;;;YAIf,SAAS,sBAAsB,CAAC,SAAS,EAAE,WAAW,EAAE,iBAAiB,EAAE;gBAC3E,4EAAA;gBACA,8EAAA;gBACA,kEAAA;gBACA,sBAAA;gBACE,OAAO,WAAW,EAAE;;YAGtB,IAAI,SAAS,GAAG,CAAC,CAAA,CAAE,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,QAAQ,CAAC,aAAa,KAAK,WAAW,CAAC;YAEnJ,IAAI,mBAAmB,GAAG,CAAC,SAAS;YAEpC,IAAI,IAAI,GAAG,mBAAmB,GAAG,sBAAsB,GAAG,oBAAoB;YAC9E,IAAI,sBAAsB,GAAGA,OAAK,CAAC,oBAAoB,KAAK,SAAS,GAAGA,OAAK,CAAC,oBAAoB,GAAG,IAAI;YAE7E,oCAAA,CAAA,oBAAA,GAAG,sBAAsB;YACrD,yCAAA,GACA,IACE,OAAO,8BAA8B,KAAK,WAAW,IACrD,OAAO,8BAA8B,CAAC,0BAA0B,KAC9D,YACF;gBACA,8BAA8B,CAAC,0BAA0B,CAAC,IAAI,KAAK,EAAE,CAAC;;QAGxE,CAAG,GAAG;IACN;;;AC5OA,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,UAAc,EAAF;;AAEzC,CAAC,MAAM;IACLC,IAAA,CAAA,OAAc,GAAGC,2CAAA,EAA6D;AAChF;;ACIA,MAAM,SAAS,GAAG,CAChB,GAAG,IAA2D,KAC5D;IACF,OAAO,CAAC,IAAO,KAAI;QACjB,IAAI,CAAC,OAAO,EAAC,GAAG,IAAG;YACjB,IAAI,OAAO,GAAG,KAAK,UAAU,EAAE;gBAC7B,GAAG,CAAC,IAAI,CAAC;mBACJ,IAAI,GAAG,EAAE;gBACb,GAAkC,CAAC,OAAO,GAAG,IAAI;;QAEtD,CAAC,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;CAEG,GACH,MAAM,OAAO,GAAqD,CAAC,EACjE,gBAAgB,EACjB,KAAI;;IAEH,MAAM,SAAS,GAAGC,YAAAA,oBAAoB,CACpC,gBAAgB,CAAC,SAAS,EAC1B,gBAAgB,CAAC,WAAW,EAC5B,gBAAgB,CAAC,iBAAiB,CACnC;;IAGD,6MACE,UACG,CAAA,aAAA,uMAAA,UAAA,CAAA,QAAA,EAAA,IAAA,EAAA,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CACxB;AAEP,CAAC;AAOD,SAAS,WAAW,GAAA;IAClB,MAAM,WAAW,GAAG,IAAI,GAAG,EAAc;IACzC,IAAI,SAAS,GAAsC,CAAA,CAAE;IAErD,OAAO;QACL;;SAEG,GACH,SAAS,EAAC,QAAoB,EAAA;YAC5B,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC;YACzB,OAAO,MAAK;gBACV,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC9B,CAAC;SACF;QACD,WAAW,GAAA;YACT,OAAO,SAAS;SACjB;QACD,iBAAiB,GAAA;YACf,OAAO,SAAS;SACjB;QACD;;SAEG,GACH,WAAW,EAAC,EAAU,EAAE,QAAuB,EAAA;YAC7C,SAAS,GAAG;gBACV,GAAG,SAAS;gBACZ,CAAC,EAAE,CAAA,+MAAG,UAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY,EAAE,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;aACzE;YAED,WAAW,CAAC,OAAO,EAAC,UAAU,GAAI,UAAU,EAAE,CAAC;SAChD;QACD;;SAEG,GACH,cAAc,EAAC,EAAU,EAAA;YACvB,MAAM,aAAa,GAAG;gBAAE,GAAG,SAAS;YAAA,CAAE;YAEtC,OAAO,aAAa,CAAC,EAAE,CAAC;YACxB,SAAS,GAAG,aAAa;YACzB,WAAW,CAAC,OAAO,EAAC,UAAU,GAAI,UAAU,EAAE,CAAC;SAChD;KACF;AACH;AAEa,MAAA,iBAAkB,+MAAQ,UAAK,CAAC,SAG5C,CAAA;IAOC,WAAA,CAAY,KAAyB,CAAA;;QACnC,KAAK,CAAC,KAAK,CAAC;QACZ,IAAI,CAAC,gBAAgB,GAAG,gNAAK,CAAC,SAAS,EAAE;QACzC,IAAI,CAAC,WAAW,GAAG,KAAK;QAExB,IAAI,CAAC,KAAK,GAAG;YACX,8BAA8B,EAAE,OAAO,CAAC,CAAA,EAAA,GAAC,KAAK,CAAC,MAA4C,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,gBAAgB,CAAC;SAC/G;;IAGH,iBAAiB,GAAA;QACf,IAAI,CAAC,IAAI,EAAE;;IAGb,kBAAkB,GAAA;QAChB,IAAI,CAAC,IAAI,EAAE;;IAGb,IAAI,GAAA;QACF,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAA2C;QAErE,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE;YAC3D,IAAI,MAAM,CAAC,gBAAgB,EAAE;gBAC3B;;YAGF,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO;YAE7C,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;YAEpD,MAAM,CAAC,UAAU,CAAC;gBAChB,OAAO;YACR,CAAA,CAAC;YAEF,MAAM,CAAC,gBAAgB,GAAG,WAAW,EAAE;;YAGvC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,8BAA8B,EAAE;;gBAE9C,IAAI,CAAC,6BAA6B,GAAG,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAK;oBAC1E,IAAI,CAAC,QAAQ,EAAC,SAAS,IAAG;wBACxB,IAAI,CAAC,SAAS,CAAC,8BAA8B,EAAE;4BAC7C,OAAO;gCACL,8BAA8B,EAAE,IAAI;6BACrC;;wBAEH,OAAO,SAAS;oBAClB,CAAC,CAAC;;oBAGF,IAAI,IAAI,CAAC,6BAA6B,EAAE;wBACtC,IAAI,CAAC,6BAA6B,EAAE;;gBAExC,CAAC,CAAC;;YAGJ,MAAM,CAAC,eAAe,EAAE;YAExB,IAAI,CAAC,WAAW,GAAG,IAAI;;;IAI3B,oBAAoB,GAAA;QAClB,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAA2C;QAErE,IAAI,CAAC,MAAM,EAAE;YACX;;QAGF,IAAI,CAAC,WAAW,GAAG,KAAK;QAExB,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YACvB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACnB,SAAS,EAAE,CAAA,CAAE;YACd,CAAA,CAAC;;QAGJ,IAAI,IAAI,CAAC,6BAA6B,EAAE;YACtC,IAAI,CAAC,6BAA6B,EAAE;;QAGtC,MAAM,CAAC,gBAAgB,GAAG,IAAI;QAE9B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE;YACtC;;QAGF,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;QAEhD,UAAU,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;QAEvD,MAAM,CAAC,UAAU,CAAC;YAChB,OAAO,EAAE,UAAU;QACpB,CAAA,CAAC;;IAGJ,MAAM,GAAA;QACJ,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK;QAEhD,6MACE,UAAA,CAAA,aAAA,uMAAA,UAAA,CAAA,QAAA,EAAA,IAAA,EACE,gNAAA,CAAA,aAAA,CAAA,KAAA,EAAA;YAAK,GAAG,EAAE,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC;YAAM,GAAA,IAAI;QAAA,CAAI,CAAA,EAEjE,CAAA,MAAM,KAAN,IAAA,IAAA,MAAM,KAAA,KAAA,IAAA,KAAA,IAAN,MAAM,CAAE,gBAAgB,2MAAI,UAAA,CAAA,aAAA,CAAC,OAAO,EAAC;YAAA,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;QAAA,CAAI,CAAA,CAClF;;AAGR;AAED,0EAAA;AACA,MAAM,oBAAoB,6MAAG,aAAA,AAAU,EACrC,CAAC,KAA2C,EAAE,GAAG,KAAI;IACnD,MAAM,GAAG,wMAAG,WAAK,CAAC,OAAO,CAAC,MAAK;QAC7B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,QAAQ,EAAE;;IAE1D,CAAC,EAAE;QAAC,KAAK,CAAC,MAAM;KAAC,CAAC;;IAGlB,6MAAO,UAAK,CAAC,aAAa,CAAC,iBAAiB,EAAE;QAC5C,GAAG;QACH,QAAQ,EAAE,GAAG;QACb,GAAG,KAAK;IACT,CAAA,CAAC;AACJ,CAAC,CACF;AAEY,MAAA,aAAa,yMAAG,UAAK,CAAC,IAAI,CAAC,oBAAoB;AC9N5D,IAAA,KAAc,GAAG,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;IACpC,IAAI,CAAC,KAAK,CAAC,EAAE,OAAO,IAAI;IAExB,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,QAAQ,IAAI,OAAO,CAAC,IAAI,QAAQ,EAAE;QAC1D,IAAI,CAAC,CAAC,WAAW,KAAK,CAAC,CAAC,WAAW,EAAE,OAAO,KAAK;QAEjD,IAAI,MAAM,EAAE,CAAC,EAAE,IAAI;QACnB,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YACpB,MAAM,GAAG,CAAC,CAAC,MAAM;YACjB,IAAI,MAAM,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,KAAK;YACpC,IAAK,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,EACxB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;YACtC,OAAO,IAAI;QACjB;QAGI,IAAI,AAAC,CAAC,YAAY,GAAG,IAAM,CAAC,YAAY,GAAG,CAAC,CAAE;YAC5C,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,OAAO,KAAK;YACnC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CACnB,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;YAChC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CACnB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;YAC7C,OAAO,IAAI;QACjB;QAEI,IAAI,AAAC,CAAC,YAAY,GAAG,IAAM,CAAC,YAAY,GAAG,CAAC,CAAE;YAC5C,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,OAAO,KAAK;YACnC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CACnB,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;YAChC,OAAO,IAAI;QACjB;QAEI,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;YAClD,MAAM,GAAG,CAAC,CAAC,MAAM;YACjB,IAAI,MAAM,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,KAAK;YACpC,IAAK,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,EACxB,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;YACjC,OAAO,IAAI;QACjB;QAGI,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK;QACjF,IAAI,CAAC,CAAC,OAAO,KAAK,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,OAAO,EAAE;QAC9E,IAAI,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE;QAElF,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QACrB,MAAM,GAAG,IAAI,CAAC,MAAM;QACpB,IAAI,MAAM,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,OAAO,KAAK;QAElD,IAAK,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,EACxB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;QAErE,IAAK,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,EAAG;YAC3B,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;YAEjB,IAAI,GAAG,KAAK,QAAQ,IAAI,CAAC,CAAC,QAAQ,EAAE;gBAIlC;YACR;YAEM,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,KAAK;QAC9C;QAEI,OAAO,IAAI;IACf;IAEA,oCAAA;IACE,OAAO,CAAC,KAAG,CAAC,IAAI,CAAC,KAAG,CAAC;AACvB,CAAC;;;;;;;;;;;;;;;;;;ICrEY,IAAI,CAAC,GAACJ,gNAAgB,EAAC,CAAC,GAACG,WAAuC;IAAC,SAAS,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;QAAC,OAAO,CAAC,KAAG,CAAC,IAAA,CAAG,CAAC,KAAG,CAAC,IAAE,CAAC,GAAC,CAAC,KAAG,CAAC,GAAC,CAAC,CAAC,IAAE,CAAC,KAAG,CAAC,IAAE,CAAC,KAAG;IAAC;IAAC,IAAI,CAAC,GAAC,UAAU,KAAG,OAAO,MAAM,CAAC,EAAE,GAAC,MAAM,CAAC,EAAE,GAAC,CAAC,EAAC,CAAC,GAAC,CAAC,CAAC,oBAAoB,EAAC,CAAC,GAAC,CAAC,CAAC,MAAM,EAAC,CAAC,GAAC,CAAC,CAAC,SAAS,EAAC,CAAC,GAAC,CAAC,CAAC,OAAO,EAAC,CAAC,GAAC,CAAC,CAAC,aAAa;IAC5Q,2BAAA,CAAA,gCAAwC,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;QAAC,IAAI,CAAC,GAAC,CAAC,CAAC,IAAI,CAAC;QAAC,IAAG,IAAI,KAAG,CAAC,CAAC,OAAO,EAAC;YAAC,IAAI,CAAC,GAAC;gBAAC,QAAQ,EAAC,CAAC,CAAC;gBAAC,KAAK,EAAC;YAAI,CAAC;YAAC,CAAC,CAAC,OAAO,GAAC;QAAC,CAAC,MAAK,CAAC,GAAC,CAAC,CAAC,OAAO;QAAC,CAAC,GAAC,CAAC,CAAC,UAAU;YAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAAC,IAAG,CAAC,CAAC,EAAC;oBAAC,CAAC,GAAC,CAAC,CAAC;oBAAC,CAAC,GAAC,CAAC;oBAAC,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC;oBAAC,IAAG,KAAK,CAAC,KAAG,CAAC,IAAE,CAAC,CAAC,QAAQ,EAAC;wBAAC,IAAI,CAAC,GAAC,CAAC,CAAC,KAAK;wBAAC,IAAG,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,OAAO,CAAC,GAAC;oBAAC;oBAAC,OAAO,CAAC,GAAC;gBAAC;gBAAC,CAAC,GAAC,CAAC;gBAAC,IAAG,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,OAAO,CAAC;gBAAC,IAAI,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC;gBAAC,IAAG,KAAK,CAAC,KAAG,CAAC,IAAE,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,OAAO,CAAC;gBAAC,CAAC,GAAC,CAAC;gBAAC,OAAO,CAAC,GAAC;YAAC;YAAC,IAAI,CAAC,GAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,GAAC,KAAK,CAAC,KAAG,CAAC,GAAC,IAAI,GAAC,CAAC;YAAC,OAAM;gBAAC,UAAU;oBAAC,OAAO,CAAC,CAAC,CAAC,EAAE;gBAAC,CAAC;gBAAC,IAAI,KAAG,CAAC,GAAC,KAAK,CAAC,GAAC,UAAU;oBAAC,OAAO,CAAC,CAAC,CAAC,EAAE;gBAAC,CAAC;aAAC,CAAC;WAAC;YAAC,CAAC;YAAC,CAAC;YAAC,CAAC;YAAC,CAAC;SAAC,CAAC;QAAC,IAAI,CAAC,GAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtf,CAAC,CAAC,UAAU;YAAC,CAAC,CAAC,QAAQ,GAAC,CAAC,CAAC;YAAC,CAAC,CAAC,KAAK,GAAC;QAAC,CAAC,EAAC;YAAC,CAAC;SAAC,CAAC;QAAC,CAAC,CAAC,CAAC,CAAC;QAAC,OAAO;IAAC,CAAC;;;;;;;;;;;;;;;;ICCzD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,WAAc,CAAF;QACvC,CAAC,WAAW;YAId,yCAAA,GACA,IACE,OAAO,8BAA8B,KAAK,WAAW,IACrD,OAAO,8BAA8B,CAAC,2BAA2B,KAC/D,YACF;gBACA,8BAA8B,CAAC,2BAA2B,CAAC,IAAI,KAAK,EAAE,CAAC;;YAE/D,IAAIF,OAAK,yMAAGD,UAAgB;YACtC,IAAI,IAAI,GAAGG,WAAuC;YAElD;;;EAGA,GACA,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;gBAChB,OAAO,CAAC,KAAK,CAAC,IAAA,CAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA,sCAAA;;;YAItE,IAAI,QAAQ,GAAG,OAAO,MAAM,CAAC,EAAE,KAAK,UAAU,GAAG,MAAM,CAAC,EAAE,GAAG,EAAE;YAE/D,IAAI,oBAAoB,GAAG,IAAI,CAAC,oBAAoB;YAEpD,wBAAA;YAEA,IAAI,MAAM,GAAGF,OAAK,CAAC,MAAM,EACrB,SAAS,GAAGA,OAAK,CAAC,SAAS,EAC3B,OAAO,GAAGA,OAAK,CAAC,OAAO,EACvB,aAAa,GAAGA,OAAK,CAAC,aAAa,CAAC,CAAA,6EAAA;YAExC,SAAS,gCAAgC,CAAC,SAAS,EAAE,WAAW,EAAE,iBAAiB,EAAE,QAAQ,EAAE,OAAO,EAAE;gBACxG,2CAAA;gBACE,IAAI,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC;gBAC1B,IAAI,IAAI;gBAER,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,EAAE;oBAC5B,IAAI,GAAG;wBACL,QAAQ,EAAE,KAAK;wBACf,KAAK,EAAE;oBACb,CAAK;oBACD,OAAO,CAAC,OAAO,GAAG,IAAI;gBAC1B,CAAG,MAAM;oBACL,IAAI,GAAG,OAAO,CAAC,OAAO;;gBAGxB,IAAI,QAAQ,GAAG,OAAO,CAAC,YAAY;oBACrC,0EAAA;oBACA,yEAAA;oBACA,wEAAA;oBACA,gCAAA;oBACI,IAAI,OAAO,GAAG,KAAK;oBACnB,IAAI,gBAAgB;oBACpB,IAAI,iBAAiB;oBAErB,IAAI,gBAAgB,GAAG,SAAU,YAAY,EAAE;wBAC7C,IAAI,CAAC,OAAO,EAAE;4BACpB,kEAAA;4BACQ,OAAO,GAAG,IAAI;4BACd,gBAAgB,GAAG,YAAY;4BAE/B,IAAI,cAAc,GAAG,QAAQ,CAAC,YAAY,CAAC;4BAE3C,IAAI,OAAO,KAAK,SAAS,EAAE;gCACnC,qEAAA;gCACA,oEAAA;gCACA,kEAAA;gCACU,IAAI,IAAI,CAAC,QAAQ,EAAE;oCACjB,IAAI,gBAAgB,GAAG,IAAI,CAAC,KAAK;oCAEjC,IAAI,OAAO,CAAC,gBAAgB,EAAE,cAAc,CAAC,EAAE;wCAC7C,iBAAiB,GAAG,gBAAgB;wCACpC,OAAO,gBAAgB;;;;4BAK7B,iBAAiB,GAAG,cAAc;4BAClC,OAAO,cAAc;yBACtB,CAAA,4DAAA;wBAGP,4DAAA;wBACM,IAAI,YAAY,GAAG,gBAAgB;wBACnC,IAAI,aAAa,GAAG,iBAAiB;wBAErC,IAAI,QAAQ,CAAC,YAAY,EAAE,YAAY,CAAC,EAAE;4BAChD,uEAAA;4BACQ,OAAO,aAAa;yBACrB,CAAA,mEAAA;wBAGP,mEAAA;wBACM,IAAI,aAAa,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAA,0EAAA;wBACjD,yEAAA;wBACA,uEAAA;wBACA,oBAAA;wBAEA,0EAAA;wBACA,yEAAA;wBACA,uEAAA;wBACA,oBAAA;wBACM,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,EAAE;4BAClE,OAAO,aAAa;;wBAGtB,gBAAgB,GAAG,YAAY;wBAC/B,iBAAiB,GAAG,aAAa;wBACjC,OAAO,aAAa;oBAC1B,CAAK,CAAC,CAAA,mEAAA;oBAGN,mEAAA;oBACI,IAAI,sBAAsB,GAAG,iBAAiB,KAAK,SAAS,GAAG,IAAI,GAAG,iBAAiB;oBAEvF,IAAI,uBAAuB,GAAG,YAAY;wBACxC,OAAO,gBAAgB,CAAC,WAAW,EAAE,CAAC;oBAC5C,CAAK;oBAED,IAAI,6BAA6B,GAAG,sBAAsB,KAAK,IAAI,GAAG,SAAS,GAAG,YAAY;wBAC5F,OAAO,gBAAgB,CAAC,sBAAsB,EAAE,CAAC;oBACvD,CAAK;oBACD,OAAO;wBAAC,uBAAuB;wBAAE,6BAA6B;qBAAC;iBAChE,EAAE;oBAAC,WAAW;oBAAE,iBAAiB;oBAAE,QAAQ;oBAAE,OAAO;iBAAC,CAAC,EACnD,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,EAC1B,kBAAkB,GAAG,QAAQ,CAAC,CAAC,CAAC;gBAEpC,IAAI,KAAK,GAAG,oBAAoB,CAAC,SAAS,EAAE,YAAY,EAAE,kBAAkB,CAAC;gBAC7E,SAAS,CAAC,YAAY;oBACpB,IAAI,CAAC,QAAQ,GAAG,IAAI;oBACpB,IAAI,CAAC,KAAK,GAAG,KAAK;gBACtB,CAAG,EAAE;oBAAC,KAAK;iBAAC,CAAC;gBACX,aAAa,CAAC,KAAK,CAAC;gBACpB,OAAO,KAAK;;YAG0B,wBAAA,CAAA,gCAAA,GAAG,gCAAgC;YAC3E,yCAAA,GACA,IACE,OAAO,8BAA8B,KAAK,WAAW,IACrD,OAAO,8BAA8B,CAAC,0BAA0B,KAC9D,YACF;gBACA,8BAA8B,CAAC,0BAA0B,CAAC,IAAI,KAAK,EAAE,CAAC;;QAGxE,CAAG,GAAG;IACN;;;AClKA,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,UAAc,EAAF;;AAEzC,CAAC,MAAM;IACLI,YAAA,CAAA,OAAc,GAAGF,+BAAA,EAA2E;AAC9F;;ACCA,MAAM,yBAAyB,GAAG,OAAO,MAAM,KAAK,WAAW,yMAAG,kBAAe,yMAAG,YAAS;AA0B7F;;;CAGG,GACH,MAAM,kBAAkB,CAAA;IAWtB,WAAA,CAAY,aAAsB,CAAA;QAV1B,IAAiB,CAAA,iBAAA,GAAG,CAAC;QAErB,IAAqB,CAAA,qBAAA,GAAG,CAAC;QAMzB,IAAA,CAAA,WAAW,GAAG,IAAI,GAAG,EAAc;QAGzC,IAAI,CAAC,MAAM,GAAG,aAAa;QAC3B,IAAI,CAAC,YAAY,GAAG;YAAE,MAAM,EAAE,aAAa;YAAE,iBAAiB,EAAE,CAAC;QAAA,CAAE;QAEnE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;QAC9C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1D,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;QAClC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;;IAG5C;;KAEG,GACH,WAAW,GAAA;QACT,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,CAAC,qBAAqB,EAAE;YACzD,OAAO,IAAI,CAAC,YAAY;;QAE1B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,iBAAiB;QACnD,IAAI,CAAC,YAAY,GAAG;YAAE,MAAM,EAAE,IAAI,CAAC,MAAM;YAAE,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;QAAA,CAAE;QACtF,OAAO,IAAI,CAAC,YAAY;;IAG1B;;KAEG,GACH,iBAAiB,GAAA;QACf,OAAO;YAAE,MAAM,EAAE,IAAI;YAAE,iBAAiB,EAAE,CAAC;QAAA,CAAE;;IAG/C;;KAEG,GACH,SAAS,CAAC,QAAoB,EAAA;QAC5B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC;QAC9B,OAAO,MAAK;YACV,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC;QACnC,CAAC;;IAGH;;KAEG,GACH,KAAK,CAAC,UAAyB,EAAA;QAC7B,IAAI,CAAC,MAAM,GAAG,UAAqB;QAEnC,IAAI,IAAI,CAAC,MAAM,EAAE;YACf;;;;aAIG,GACH,MAAM,EAAE,GAAG,MAAK;gBACd,IAAI,CAAC,iBAAiB,IAAI,CAAC;gBAC3B,IAAI,CAAC,WAAW,CAAC,OAAO,EAAC,QAAQ,GAAI,QAAQ,EAAE,CAAC;YAClD,CAAC;YAED,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM;YAEjC,aAAa,CAAC,EAAE,CAAC,aAAa,EAAE,EAAE,CAAC;YACnC,OAAO,MAAK;gBACV,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,CAAC;YACtC,CAAC;;QAGH,OAAO,SAAS;;AAEnB;AA+BD;;;;;;;;;;CAUG,GACG,SAAU,cAAc,CAC5B,OAA+G,EAAA;;IAE/G,MAAM,CAAC,kBAAkB,CAAC,6MAAG,WAAA,AAAQ,EAAC,IAAM,IAAI,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;;IAGnF,MAAM,aAAa,GAAGG,oBAAAA,gCAAgC,CACpD,kBAAkB,CAAC,SAAS,EAC5B,kBAAkB,CAAC,WAAW,EAC9B,kBAAkB,CAAC,iBAAiB,EACpC,OAAO,CAAC,QAA6E,EACrF,CAAA,EAAA,GAAA,OAAO,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,SAAS,CAChC;IAED,yBAAyB,CAAC,MAAK;QAC7B,OAAO,kBAAkB,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;KAChD,EAAE;QAAC,OAAO,CAAC,MAAM;QAAE,kBAAkB;KAAC,CAAC;8MAExC,gBAAA,AAAa,EAAC,aAAa,CAAC;IAE5B,OAAO,aAAa;AACtB;ACpKA,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,gCAAK,YAAY;AACnD,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK,WAAW;AAC3C,MAAM,MAAM,GAAG,KAAK,IAAI,OAAO,CAAC,OAAO,MAAM,KAAK,WAAW,IAAK,MAAc,CAAC,IAAI,CAAC;AAqBtF;;CAEG,GACH,MAAM,qBAAqB,CAAA;IAqCzB,WAAA,CAAY,OAA2C,CAAA;QApCvD;;SAEG,GACK,IAAM,CAAA,MAAA,GAAkB,IAAI;QAOpC;;;SAGG,GACK,IAAA,CAAA,aAAa,GAAG,IAAI,GAAG,EAAc;QAO7C;;SAEG,GACK,IAAkB,CAAA,kBAAA,GAAG,KAAK;QAElC;;SAEG,GACK,IAAY,CAAA,YAAA,GAA0B,IAAI;QAElD;;SAEG,GACI,IAAU,CAAA,UAAA,GAAG,EAAE;QAGpB,IAAI,CAAC,OAAO,GAAG,OAAO;QACtB,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAc;QAC1C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACvC,IAAI,CAAC,eAAe,EAAE;QAEtB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1C,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC;QAClE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;QACtD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;QACxC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;;IAG1C,SAAS,CAAC,MAAqB,EAAA;QACrC,IAAI,CAAC,MAAM,GAAG,MAAM;QACpB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;QAGxD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAC,EAAE,GAAI,EAAE,EAAE,CAAC;;IAGhC,gBAAgB,GAAA;QACtB,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,KAAK,SAAS,EAAE;YACxD,IAAI,KAAK,IAAI,MAAM,EAAE;;gBAEnB,IAAI,KAAK,+BAAE;oBACT;;;qBAGG,GACH,OAAO,CAAC,IAAI,CACV,0HAA0H,CAC3H;;;gBAIH,OAAO,IAAI;;;YAIb,OAAO,IAAI,CAAC,YAAY,EAAE;;QAG5B,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,IAAI,KAAK,IAAI,KAAK,EAAE;;YAE5D,MAAM,IAAI,KAAK,CACb,kOAAkO,CACnO;;QAGH,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,EAAE;YAC1C,OAAO,IAAI,CAAC,YAAY,EAAE;;QAG5B,OAAO,IAAI;;IAGb;;KAEG,GACK,YAAY,GAAA;QAClB,MAAM,cAAc,GAA2B;YAC7C,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO;;YAEvB,cAAc,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,cAAc,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;YAC3E,MAAM,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;YAC3D,QAAQ,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,QAAQ,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;YAC/D,SAAS,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;YACjE,OAAO,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;YAC7D,iBAAiB,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,iBAAiB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;YACjF,aAAa,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,aAAa,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;YACzE,QAAQ,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,QAAQ,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;YAC/D,cAAc,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,cAAc,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;YAC3E,MAAM,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;YAC3D,OAAO,EAAE,CAAC,GAAG,IAAI,KAAI;gBAAA,IAAA,EAAA,EAAA,EAAA,CAAA;gBAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,GAAG,IAAI,CAAC,CAAA;YAAA,CAAA;SAC9D;QACD,MAAM,MAAM,GAAG,sJAAI,SAAM,CAAC,cAAc,CAAC;;QAIzC,OAAO,MAAM;;IAGf;;KAEG,GACH,SAAS,GAAA;QACP,OAAO,IAAI,CAAC,MAAM;;IAGpB;;KAEG,GACH,iBAAiB,GAAA;QACf,OAAO,IAAI;;IAGb;;KAEG,GACH,SAAS,CAAC,aAAyB,EAAA;QACjC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC;QAErC,OAAO,MAAK;YACV,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC;QAC1C,CAAC;;IAGH,OAAO,cAAc,CAAC,CAAmB,EAAE,CAAmB,EAAA;QAC5D,OAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,CAAgC,CAAC,KAAK,EAAC,GAAG,IAAG;YAChE,IAAI;gBAAC,UAAU;gBAAE,gBAAgB;gBAAE,WAAW;gBAAE,UAAU;gBAAE,eAAe;gBAAE,SAAS;gBAAE,QAAQ;gBAAE,mBAAmB;gBAAE,gBAAgB;gBAAE,QAAQ;gBAAE,SAAS;aAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;;gBAE3K,OAAO,IAAI;;;YAIb,IAAI,GAAG,KAAK,YAAY,IAAI,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,UAAU,EAAE;gBACxD,IAAI,CAAC,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC,UAAU,CAAC,MAAM,EAAE;oBAC/C,OAAO,KAAK;;gBAEd,OAAO,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,KAAK,KAAI;;oBAC7C,IAAI,SAAS,KAAA,CAAK,CAAA,EAAA,GAAA,CAAC,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAG,KAAK,CAAC,CAAA,EAAE;wBACvC,OAAO,KAAK;;oBAEd,OAAO,IAAI;gBACb,CAAC,CAAC;;YAEJ,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE;;gBAErB,OAAO,KAAK;;YAEd,OAAO,IAAI;QACb,CAAC,CAAC;;IAGJ;;;;KAIG,GACH,QAAQ,CAAC,IAAoB,EAAA;;QAE3B,OAAO,MAAK;YACV,IAAI,CAAC,kBAAkB,GAAG,IAAI;;YAE9B,YAAY,CAAC,IAAI,CAAC,2BAA2B,CAAC;YAE9C,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;;gBAEhE,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;;;oBAGpF,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;wBACrB,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO;wBACvB,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;oBACjC,CAAA,CAAC;;mBAEC;;;;;;gBAML,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;;YAGlC,OAAO,MAAK;gBACV,IAAI,CAAC,kBAAkB,GAAG,KAAK;gBAC/B,IAAI,CAAC,eAAe,EAAE;YACxB,CAAC;QACH,CAAC;;IAGH;;KAEG,GACK,qBAAqB,CAAC,IAAoB,EAAA;QAChD,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;;YAE3C,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE;;gBAE9B,IAAI,CAAC,YAAY,GAAG,IAAI;gBACxB;;YAEF,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,IAAI,CAAC,MAAA,IAClD,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,KAAK,GAAK,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC;YAEjE,IAAI,YAAY,EAAE;;gBAEhB;;;QAIJ,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;;YAE3C,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;;QAGvB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;;QAGnC,IAAI,CAAC,YAAY,GAAG,IAAI;;IAG1B;;;;KAIG,GACK,eAAe,GAAA;QACrB,MAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU;QACzC,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM;;QAGjC,IAAI,CAAC,2BAA2B,GAAG,UAAU,CAAC,MAAK;YACjD,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,UAAU,KAAK,iBAAiB,EAAE;;gBAEpE,IAAI,aAAa,EAAE;;oBAEjB,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;;gBAEhD;;YAEF,IAAI,aAAa,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;gBAC/C,aAAa,CAAC,OAAO,EAAE;gBACvB,IAAI,IAAI,CAAC,UAAU,KAAK,iBAAiB,EAAE;oBACzC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;;;;;SAKzB,EAAE,CAAC,CAAC;;AAER;SAuBe,SAAS,CACvB,UAA4B,CAAA,CAAE,EAC9B,OAAuB,EAAE,EAAA;IAEzB,MAAM,iBAAiB,6MAAG,SAAA,AAAM,EAAC,OAAO,CAAC;IAEzC,iBAAiB,CAAC,OAAO,GAAG,OAAO;IAEnC,MAAM,CAAC,eAAe,CAAC,6MAAG,WAAQ,AAAR,EAAS,IAAM,IAAI,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;IAEtF,MAAM,MAAM,GAAGF,YAAAA,oBAAoB,CACjC,eAAe,CAAC,SAAS,EACzB,eAAe,CAAC,SAAS,EACzB,eAAe,CAAC,iBAAiB,CAClC;IAED,0NAAa,AAAb,EAAc,MAAM,CAAC;;;8MAIrB,YAAA,AAAS,EAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;;;IAIzC,cAAc,CAAC;QACb,MAAM;QACN,QAAQ,EAAE,CAAC,EAAE,iBAAiB,EAAE,KAAI;YAClC,IAAI,OAAO,CAAC,2BAA2B,KAAK,KAAK,EAAE;;gBAEjD,OAAO,IAAI;;;YAIb,IAAI,OAAO,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,CAAC,EAAE;gBACxD,OAAO,CAAC;;YAEV,OAAO,iBAAiB,GAAG,CAAC;SAC7B;IACF,CAAA,CAAC;IAEF,OAAO,MAAM;AACf;AC3WO,MAAM,aAAa,GAAG,0NAAA,AAAa,EAAqB;IAC7D,MAAM,EAAE,IAAI;AACb,CAAA;AAEY,MAAA,cAAc,GAAG,aAAa,CAAC,QAAA;AAE5C;;CAEG,GACU,MAAA,gBAAgB,GAAG,QAAM,mNAAA,AAAU,EAAC,aAAa;AAS9D;;;;CAIG,YACa,cAAc,CAAC,EAC7B,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,oBAAoB,GAAG,CAAA,CAAE,EAAE,GAAG,aAAa,EACxD,EAAA;IACpB,MAAM,MAAM,GAAG,SAAS,CAAC,aAAa,CAAC;IAEvC,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,IAAI;;IAGb,6MACE,UAAC,CAAA,aAAA,CAAA,aAAa,CAAC,QAAQ,EAAA;QAAC,KAAK,EAAE;YAAE,MAAM;QAAA,CAAE;IAAA,CAAA,EACtC,UAAU,wMACX,UAAC,CAAA,aAAA,CAAA,cAAc,EAAA,MACZ,CAAC,EAAE,MAAM,EAAE,aAAa,EAAE,yMACzB,UAAC,CAAA,aAAA,CAAA,aAAa,EAAA;YAAC,MAAM,EAAE,aAAa;YAAM,GAAA,oBAAoB;QAAA,CAAI,CAAA,CACnE,CACc,CAChB,QAAQ,EACR,SAAS,CACa;AAE7B;AC1Ca,MAAA,UAAU,GAAG,CAAC,KAAsB,KAAI;IACnD,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,6MAAG,WAAA,AAAQ,EAAwB,IAAI,CAAC;IACnE,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,gBAAgB,EAAE;6MAEpD,aAAA,AAAS,EAAC,MAAK;;QACb,IAAI,CAAC,OAAO,EAAE;YACZ;;QAGF,IAAI,CAAA,CAAA,EAAA,GAAA,KAAK,CAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,WAAW,KAAA,CAAI,aAAa,KAAA,IAAA,IAAb,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAE,WAAW,CAAA,EAAE;YAC3D;;QAGF,MAAM,EACJ,SAAS,GAAG,YAAY,EAAE,MAAM,EAAE,YAAY,GAAG,CAAA,CAAE,EAAE,WAAW,EAAE,UAAU,GAAG,IAAI,EACpF,GAAG,KAAK;QAET,MAAM,UAAU,GAAG,MAAM,IAAI,aAAa;QAE1C,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,CAAC,IAAI,CAAC,kGAAkG,CAAC;YAChH;;QAGF,MAAM,MAAM,GAAG,gMAAA,AAAgB,EAAC;YAC9B,WAAW;YACX,MAAM,EAAE,UAAU;YAClB,OAAO;YACP,SAAS;YACT,UAAU;YACV,YAAY;QACb,CAAA,CAAC;QAEF,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC;QACjC,OAAO,MAAQ;YAAA,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA;QAAA,CAAE;KACxD,EAAE;QAAC,KAAK,CAAC,MAAM;QAAE,aAAa;QAAE,OAAO;KAAC,CAAC;IAE1C,OACE,gNAAK,CAAA,aAAA,CAAA,KAAA,EAAA;QAAA,GAAG,EAAE,UAAU;QAAE,SAAS,EAAE,KAAK,CAAC,SAAS;QAAE,KAAK,EAAE;YAAE,UAAU,EAAE,QAAQ;QAAA,CAAE;IAAA,CAC9E,EAAA,KAAK,CAAC,QAAQ,CACX;AAEV;ACzCa,MAAA,YAAY,GAAG,CAAC,KAAwB,KAAI;IACvD,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,6MAAG,WAAQ,AAAR,EAAgC,IAAI,CAAC;IACnE,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,gBAAgB,EAAE;QAEpD,kNAAA,AAAS,EAAC,MAAK;;QACb,IAAI,CAAC,OAAO,EAAE;YACZ;;QAGF,IAAI,CAAA,CAAA,EAAA,GAAA,KAAK,CAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,WAAW,KAAA,CAAI,aAAa,KAAA,IAAA,IAAb,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAE,WAAW,CAAA,EAAE;YAC3D;;QAGF,MAAM,EACJ,SAAS,GAAG,cAAc,EAC1B,MAAM,EACN,YAAY,GAAG,CAAA,CAAE,EACjB,UAAU,GAAG,IAAI,EAClB,GAAG,KAAK;QAET,MAAM,UAAU,GAAG,MAAM,IAAI,aAAa;QAE1C,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,CAAC,IAAI,CAAC,oGAAoG,CAAC;YAClH;;QAGF,MAAM,MAAM,kLAAG,qBAAkB,AAAlB,EAAmB;YAChC,SAAS;YACT,MAAM,EAAE,UAAU;YAClB,OAAO;YACP,YAAY;YACZ,UAAU;QACX,CAAA,CAAC;QAEF,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC;QACjC,OAAO,MAAQ;YAAA,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA;QAAA,CAAE;IACzD,CAAC,EAAE;QACD,KAAK,CAAC,MAAM;QACZ,aAAa;QACb,OAAO;KACR,CAAC;IAEF,6MACE,UAAK,CAAA,aAAA,CAAA,KAAA,EAAA;QAAA,GAAG,EAAE,UAAU;QAAE,SAAS,EAAE,KAAK,CAAC,SAAS;QAAE,KAAK,EAAE;YAAE,UAAU,EAAE,QAAQ;QAAA,CAAE;IAAA,CAC9E,EAAA,KAAK,CAAC,QAAQ,CACX;AAEV;ACxDO,MAAM,oBAAoB,6MAAG,gBAAa,AAAb,EAAkD;IACpF,WAAW,EAAE,SAAS;AACvB,CAAA;AAEY,MAAA,gBAAgB,GAAG,6MAAM,cAAA,AAAU,EAAC,oBAAoB;ACFxD,MAAA,eAAe,IAAmC,KAAK,IAAG;IACrE,MAAM,GAAG,GAAG,KAAK,CAAC,EAAE,IAAI,KAAK;IAC7B,MAAM,EAAE,kBAAkB,EAAE,GAAG,gBAAgB,EAAE;IAEjD;0MAEE,UAAC,CAAA,aAAA,CAAA,GAAG,EACE;QAAA,GAAA,KAAK;QACT,GAAG,EAAE,kBAAkB;QACA,wBAAA,EAAA,EAAE;QACzB,KAAK,EAAE;YACL,UAAU,EAAE,UAAU;YACtB,GAAG,KAAK,CAAC,KAAK;QACf,CAAA;IAAA,CAAA,CACD;AAEN;AChBO,MAAM,eAAe,yMAAmC,UAAK,CAAC,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAI;IAC7F,MAAM,EAAE,WAAW,EAAE,GAAG,gBAAgB,EAAE;IAC1C,MAAM,GAAG,GAAG,KAAK,CAAC,EAAE,IAAI,KAAK;IAE7B;0MAEE,UAAA,CAAA,aAAA,CAAC,GAAG,EAAA;QAAA,GACE,KAAK;QACT,GAAG,EAAE,GAAG;QACe,wBAAA,EAAA,EAAE;QACzB,WAAW,EAAE,WAAW;QACxB,KAAK,EAAE;YACL,UAAU,EAAE,QAAQ;YACpB,GAAG,KAAK,CAAC,KAAK;QACf,CAAA;IAAA,CAAA,CACD;AAEN,CAAC;ACZD;;;;CAIG,GACH,SAAS,gBAAgB,CAAC,SAAc,EAAA;IACtC,OAAO,CAAC,CAAA,CACN,OAAO,SAAS,KAAK,cAClB,SAAS,CAAC,SAAA,IACV,SAAS,CAAC,SAAS,CAAC,gBAAgB,CACxC;AACH;AAEA;;;;CAIG,GACH,SAAS,qBAAqB,CAAC,SAAc,EAAA;;IAC3C,OAAO,CAAC,CAAA,CACN,OAAO,SAAS,KAAK,YAClB,CAAA,CAAA,EAAA,GAAA,SAAS,CAAC,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,QAAQ,EAAE,MAAK,2BAA2B,CAClE;AACH;AAEA;;;CAGG,GACH,SAAS,aAAa,GAAA;;;;IAIpB,IAAI;;QAEF,0MAAIG,UAAY,EAAE;YAChB,MAAM,YAAY,GAAG,QAAQ,uMAACA,UAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YAE7D,OAAO,YAAY,IAAI,EAAE;;MAE3B,OAAM;;;IAGR,OAAO,KAAK;AACd;AAqCA;;;;;;;;;;AAUE,SACW,aAAa,CAAA;IAexB;;KAEG,GACH,WAAA,CAAY,SAA8B,EAAE,EAC1C,MAAM,EACN,KAAK,GAAG,CAAA,CAAE,EACV,EAAE,GAAG,KAAK,EACV,SAAS,GAAG,EAAE,EACO,CAAA;QAVvB,IAAG,CAAA,GAAA,GAAa,IAAI;QAWlB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,QAAQ,EAAE;QAC3D,IAAI,CAAC,SAAS,GAAG,SAAS;QAC1B,IAAI,CAAC,MAAM,GAAG,MAAoC;QAClD,IAAI,CAAC,KAAK,GAAG,KAAU;QACvB,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;QACzC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC;QAE5C,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;;QAGrD,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE;;;aAG7B,4NAAA,AAAS,EAAC,MAAK;gBACb,IAAI,CAAC,MAAM,EAAE;YACf,CAAC,CAAC;eACG;YACL,IAAI,CAAC,MAAM,EAAE;;;IAIjB;;KAEG,GACH,MAAM,GAAA;;QACJ,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;QAChC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAoC;;QAGxD,MAAM,SAAS,GAAG,aAAa,EAAE;QACjC,MAAM,WAAW,GAAG,gBAAgB,CAAC,SAAS,CAAC;QAC/C,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,SAAS,CAAC;QAEzD,MAAM,YAAY,GAAG;YAAE,GAAG,KAAK;QAAA,CAAE;QAEjC,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE;YACrB,IAAI,SAAS,EAAE;;;gBAGb,YAAY,CAAC,GAAG,GAAG,CAAC,GAAM,KAAI;oBAC5B,IAAI,CAAC,GAAG,GAAG,GAAG;gBAChB,CAAC;mBACI,IAAI,WAAW,IAAI,gBAAgB,EAAE;;;gBAG1C,YAAY,CAAC,GAAG,GAAG,CAAC,GAAM,KAAI;oBAC5B,IAAI,CAAC,GAAG,GAAG,GAAG;gBAChB,CAAC;;;;;QAML,IAAI,CAAC,YAAY,yMAAG,UAAA,CAAA,aAAA,CAAC,SAAS,EAAK;YAAA,GAAA,YAAY;QAAA,EAAI;QAEnD,CAAA,EAAA,GAAA,MAAM,KAAN,IAAA,IAAA,MAAM,KAAN,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,MAAM,CAAE,gBAAgB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,WAAW,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC;;IAGtD;;KAEG,GACH,WAAW,CAAC,QAA6B,CAAA,CAAE,EAAA;QACzC,IAAI,CAAC,KAAK,GAAG;YACX,GAAG,IAAI,CAAC,KAAK;YACb,GAAG,KAAK;SACT;QAED,IAAI,CAAC,MAAM,EAAE;;IAGf;;KAEG,GACH,OAAO,GAAA;;QACL,MAAM,MAAM,GAAG,IAAI,CAAC,MAAoC;QAExD,CAAA,EAAA,GAAA,MAAM,KAAA,IAAA,IAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAE,gBAAgB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;;IAGnD;;KAEG,GACH,gBAAgB,CAAC,UAAkC,EAAA;QACjD,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,GAAG,IAAG;YACpC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;QACjD,CAAC,CAAC;;AAEL;ACvKK,MAAO,aAKX,SAAQ,6JAAwC,CAAA;IAWhD;;;KAGG,GACH,KAAK,GAAA;QACH,MAAM,KAAK,GAAG;YACZ,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,WAAmC;YACrD,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,KAAK;YACf,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,MAAM,EAAE,IAAM,IAAI,CAAC,MAAM,EAAE;YAC3B,gBAAgB,EAAE,CAAC,UAAU,GAAG,CAAA,CAAE,GAAK,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;YACxE,UAAU,EAAE,IAAM,IAAI,CAAC,UAAU,EAAE;YACnC,GAAG,4MAAE,YAAA,AAAS,EAAK;SACY;QAEjC,IAAI,CAAE,IAAI,CAAC,SAAiB,CAAC,WAAW,EAAE;YACxC,MAAM,mBAAmB,GAAG,CAAC,MAAc,KAAY;gBACrD,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;YAC7D,CAAC;YAED,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;;QAGvE,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;QAC/C,MAAM,kBAAkB,IAAoD,OAAO,IAAG;YACpF,IAAI,OAAO,IAAI,IAAI,CAAC,iBAAiB,IAAI,OAAO,CAAC,UAAU,KAAK,IAAI,CAAC,iBAAiB,EAAE;gBACtF,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC;;QAE/C,CAAC;QACD,MAAM,OAAO,GAAG;YAAE,WAAW;YAAE,kBAAkB;QAAA,CAAE;QACnD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;;;QAGhC,MAAM,qBAAqB,6MAAgD,OAAA,AAAI,EAAC,cAAc,IAAG;YAC/F,4MACE,WAAC,CAAA,aAAA,CAAA,oBAAoB,CAAC,QAAQ,EAAA;gBAAC,KAAK,EAAE,OAAO;YAAA,CAC1C,4MAAA,gBAAA,AAAa,EAAC,SAAS,EAAE,cAAc,CAAC,CACX;QAEpC,CAAC,CAAC;QAEF,qBAAqB,CAAC,WAAW,GAAG,eAAe;QAEnD,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACpB,IAAI,CAAC,iBAAiB,GAAG,IAAI;eACxB,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;YAC5C,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC;eAC7E;YACL,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,KAAK,CAAC;;QAGtF,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,oBAAoB,GAAG,EAAE;;;;YAIxD,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS;;QAGrD,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,KAAK;QAE5C,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;YACnB,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE;;QAGtB,MAAM,EAAE,SAAS,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC,OAAO;QAEvC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC;QAElE,IAAI,CAAC,QAAQ,GAAG,IAAI,aAAa,CAAC,qBAAqB,EAAE;YACvD,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK;YACL,EAAE;YACF,SAAS,EAAE,CAAA,KAAA,EAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAI,CAAA,EAAA,SAAS,CAAE,CAAA,CAAC,IAAI,EAAE;QAC7D,CAAA,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,EAAE,IAAI,CAAC,qBAAqB,CAAC;QAC7D,IAAI,CAAC,uBAAuB,EAAE;;IAGhC;;;KAGG,GACH,IAAI,GAAG,GAAA;;QACL,IACE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAA,IACnB,CAAA,CAAC,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,YAAY,CAAC,wBAAwB,CAAC,CAAA,EACnF;YACA,MAAM,KAAK,CAAC,8DAA8D,CAAC;;QAG7E,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAsB;;IAG7C;;;KAGG,GACH,IAAI,UAAU,GAAA;QACZ,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACpB,OAAO,IAAI;;QAGb,OAAO,IAAI,CAAC,iBAAiB;;IAG/B;;;KAGG,GACH,qBAAqB,GAAA;QACnB,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS;QAChD,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE;QAEzB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B;;QAGF,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACjD,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE;gBAChC;;YAGF,IAAI,CAAC,UAAU,EAAE;eACZ;YACL,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACjC;;YAGF,IAAI,CAAC,YAAY,EAAE;;;IAIvB;;;KAGG,GACH,MAAM,CACJ,IAAU,EACV,WAAkC,EAClC,gBAAkC,EAAA;QAElC,MAAM,iBAAiB,GAAG,CAAC,KAA2B,KAAI;YACxD,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC;YAChC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,UAAU,EAAE;gBAC5C,IAAI,CAAC,uBAAuB,EAAE;;QAElC,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YAChC,OAAO,KAAK;;QAGd,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,UAAU,EAAE;YAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI;YACzB,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW;YACvC,MAAM,mBAAmB,GAAG,IAAI,CAAC,gBAAgB;YAEjD,IAAI,CAAC,IAAI,GAAG,IAAI;YAChB,IAAI,CAAC,WAAW,GAAG,WAAW;YAC9B,IAAI,CAAC,gBAAgB,GAAG,gBAAgB;YAExC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;gBACzB,OAAO;gBACP,cAAc;gBACd,OAAO,EAAE,IAAI;gBACb,cAAc,EAAE,WAAW;gBAC3B,mBAAmB;gBACnB,gBAAgB;gBAChB,WAAW,EAAE,IAAM,iBAAiB,CAAC;wBAAE,IAAI;wBAAE,WAAW;wBAAE,gBAAgB;oBAAA,CAAE,CAAC;YAC9E,CAAA,CAAC;;QAGJ,IACE,IAAI,KAAK,IAAI,CAAC,IAAA,IACX,IAAI,CAAC,WAAW,KAAK,eACrB,IAAI,CAAC,gBAAgB,KAAK,gBAAgB,EAC7C;YACA,OAAO,IAAI;;QAGb,IAAI,CAAC,IAAI,GAAG,IAAI;QAChB,IAAI,CAAC,WAAW,GAAG,WAAW;QAC9B,IAAI,CAAC,gBAAgB,GAAG,gBAAgB;QAExC,iBAAiB,CAAC;YAAE,IAAI;YAAE,WAAW;YAAE,gBAAgB;QAAA,CAAE,CAAC;QAE1D,OAAO,IAAI;;IAGb;;;KAGG,GACH,UAAU,GAAA;QACR,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;YACxB,QAAQ,EAAE,IAAI;QACf,CAAA,CAAC;QACF,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,0BAA0B,CAAC;;IAGjE;;;KAGG,GACH,YAAY,GAAA;QACV,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;YACxB,QAAQ,EAAE,KAAK;QAChB,CAAA,CAAC;QACF,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,0BAA0B,CAAC;;IAGpE;;KAEG,GACH,OAAO,GAAA;QACL,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;QACvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,qBAAqB,CAAC;QAC9D,IAAI,CAAC,iBAAiB,GAAG,IAAI;;IAG/B;;;KAGG,GACH,uBAAuB,GAAA;QACrB,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACtB,IAAI,QAAQ,GAA2B,CAAA,CAAE;YAEzC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,UAAU,EAAE;gBAC5C,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU;gBACnE,MAAM,cAAc,yJAAG,wBAAA,AAAqB,EAAC,IAAI,CAAC,IAAI,EAAE,mBAAmB,CAAC;gBAE5E,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;oBAAE,IAAI,EAAE,IAAI,CAAC,IAAI;oBAAE,cAAc;gBAAA,CAAE,CAAC;mBAC7D;gBACL,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK;;YAG/B,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC;;;AAG7C;AAED;;CAEG,GACa,SAAA,qBAAqB,CACnC,SAA+C,EAC/C,OAA+C,EAAA;IAE/C,QAAO,KAAK,IAAG;;;;QAIb,IAAI,CAAE,KAAK,CAAC,MAAqC,CAAC,gBAAgB,EAAE;YAClE,OAAO,CAAA,CAAoC;;QAG7C,OAAO,IAAI,aAAa,CAAI,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC;IACxD,CAAC;AACH", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17], "debugId": null}}]}