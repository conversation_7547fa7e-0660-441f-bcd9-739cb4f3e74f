{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/lib/crypto-polyfill.ts"], "sourcesContent": ["/**\n * Comprehensive crypto polyfill for both server-side and client-side compatibility\n * Provides crypto.randomUUID() and crypto.getRandomValues() functionality when not available\n */\n\n// UUID v4 implementation\nfunction generateUUID(): string {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\n    return v.toString(16);\n  });\n}\n\n// getRandomValues polyfill implementation\nfunction getRandomValuesPolyfill(array: any): any {\n  if (array instanceof Uint8Array) {\n    for (let i = 0; i < array.length; i++) {\n      array[i] = Math.floor(Math.random() * 256);\n    }\n  } else if (array instanceof Uint16Array) {\n    for (let i = 0; i < array.length; i++) {\n      array[i] = Math.floor(Math.random() * 65536);\n    }\n  } else if (array instanceof Uint32Array) {\n    for (let i = 0; i < array.length; i++) {\n      array[i] = Math.floor(Math.random() * 4294967296);\n    }\n  } else if (array instanceof Int8Array) {\n    for (let i = 0; i < array.length; i++) {\n      array[i] = Math.floor(Math.random() * 256) - 128;\n    }\n  } else if (array instanceof Int16Array) {\n    for (let i = 0; i < array.length; i++) {\n      array[i] = Math.floor(Math.random() * 65536) - 32768;\n    }\n  } else if (array instanceof Int32Array) {\n    for (let i = 0; i < array.length; i++) {\n      array[i] = Math.floor(Math.random() * 4294967296) - 2147483648;\n    }\n  }\n  return array;\n}\n\n// Client-side polyfill (browser environment)\nif (typeof window !== 'undefined') {\n  // Ensure crypto object exists\n  if (!window.crypto) {\n    (window as any).crypto = {};\n  }\n\n  // Add randomUUID if it doesn't exist\n  if (!window.crypto.randomUUID) {\n    (window.crypto as any).randomUUID = generateUUID;\n  }\n\n  // Add getRandomValues if it doesn't exist\n  if (!window.crypto.getRandomValues) {\n    (window.crypto as any).getRandomValues = getRandomValuesPolyfill;\n  }\n\n  // Also polyfill the global crypto if it exists but lacks functions\n  if (typeof crypto !== 'undefined') {\n    if (!crypto.randomUUID) {\n      (crypto as any).randomUUID = generateUUID;\n    }\n    if (!crypto.getRandomValues) {\n      (crypto as any).getRandomValues = getRandomValuesPolyfill;\n    }\n  }\n}\n\n// Server-side polyfill (Node.js environment)\nif (typeof globalThis !== 'undefined') {\n  // Ensure crypto object exists\n  if (!globalThis.crypto) {\n    globalThis.crypto = {} as Crypto;\n  }\n\n  // Add randomUUID if it doesn't exist\n  if (!globalThis.crypto.randomUUID) {\n    (globalThis.crypto as any).randomUUID = generateUUID;\n  }\n\n  // Add getRandomValues if it doesn't exist\n  if (!globalThis.crypto.getRandomValues) {\n    (globalThis.crypto as any).getRandomValues = getRandomValuesPolyfill;\n  }\n}\n\n// Additional fallback for environments where crypto exists but functions don't\nif (typeof crypto !== 'undefined') {\n  if (!crypto.randomUUID) {\n    (crypto as any).randomUUID = generateUUID;\n  }\n  if (!crypto.getRandomValues) {\n    (crypto as any).getRandomValues = getRandomValuesPolyfill;\n  }\n}\n\nexport {};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,yBAAyB;;AACzB,SAAS;IACP,OAAO,uCAAuC,OAAO,CAAC,SAAS,SAAS,CAAC;QACvE,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK;QAC/B,MAAM,IAAI,MAAM,MAAM,IAAK,IAAI,MAAM;QACrC,OAAO,EAAE,QAAQ,CAAC;IACpB;AACF;AAEA,0CAA0C;AAC1C,SAAS,wBAAwB,KAAU;IACzC,IAAI,iBAAiB,YAAY;QAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,KAAK,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QACxC;IACF,OAAO,IAAI,iBAAiB,aAAa;QACvC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,KAAK,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QACxC;IACF,OAAO,IAAI,iBAAiB,aAAa;QACvC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,KAAK,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QACxC;IACF,OAAO,IAAI,iBAAiB,WAAW;QACrC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,KAAK,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;QAC/C;IACF,OAAO,IAAI,iBAAiB,YAAY;QACtC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,KAAK,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS;QACjD;IACF,OAAO,IAAI,iBAAiB,YAAY;QACtC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,KAAK,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,cAAc;QACtD;IACF;IACA,OAAO;AACT;AAEA,6CAA6C;AAC7C,uCAAmC;;AAyBnC;AAEA,6CAA6C;AAC7C,IAAI,OAAO,eAAe,aAAa;IACrC,8BAA8B;IAC9B,IAAI,CAAC,WAAW,MAAM,EAAE;QACtB,WAAW,MAAM,GAAG,CAAC;IACvB;IAEA,qCAAqC;IACrC,IAAI,CAAC,WAAW,MAAM,CAAC,UAAU,EAAE;QAChC,WAAW,MAAM,CAAS,UAAU,GAAG;IAC1C;IAEA,0CAA0C;IAC1C,IAAI,CAAC,WAAW,MAAM,CAAC,eAAe,EAAE;QACrC,WAAW,MAAM,CAAS,eAAe,GAAG;IAC/C;AACF;AAEA,+EAA+E;AAC/E,IAAI,OAAO,WAAW,aAAa;IACjC,IAAI,CAAC,OAAO,UAAU,EAAE;QACrB,OAAe,UAAU,GAAG;IAC/B;IACA,IAAI,CAAC,OAAO,eAAe,EAAE;QAC1B,OAAe,eAAe,GAAG;IACpC;AACF", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/convex/_generated/api.js"], "sourcesContent": ["/* eslint-disable */\n/**\n * Generated `api` utility.\n *\n * THIS CODE IS AUTOMATICALLY GENERATED.\n *\n * To regenerate, run `npx convex dev`.\n * @module\n */\n\nimport { anyApi, componentsGeneric } from \"convex/server\";\n\n/**\n * A utility for referencing Convex functions in your app's API.\n *\n * Usage:\n * ```js\n * const myFunctionReference = api.myModule.myFunction;\n * ```\n */\nexport const api = anyApi;\nexport const internal = anyApi;\nexport const components = componentsGeneric();\n"], "names": [], "mappings": "AAAA,kBAAkB,GAClB;;;;;;;CAOC;;;;;AAED;AAAA;AAAA;;AAUO,MAAM,MAAM,sJAAA,CAAA,SAAM;AAClB,MAAM,WAAW,sJAAA,CAAA,SAAM;AACvB,MAAM,aAAa,CAAA,GAAA,sLAAA,CAAA,oBAAiB,AAAD", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport { \n  Home, \n  FileText, \n  Clock, \n  Users, \n  Settings, \n  ChevronLeft,\n  ChevronRight,\n  Film\n} from 'lucide-react';\nimport { Button } from '../ui/button';\nimport { cn } from '@/lib/utils';\n\ninterface SidebarProps {\n  className?: string;\n}\n\nconst navigationItems = [\n  {\n    title: 'Dashboard',\n    href: '/',\n    icon: Home,\n  },\n  {\n    title: 'Scripts',\n    href: '/scripts',\n    icon: Film,\n  },\n  {\n    title: 'Recent',\n    href: '/recent',\n    icon: Clock,\n  },\n  {\n    title: 'Shared with me',\n    href: '/shared',\n    icon: Users,\n  },\n  {\n    title: 'Settings',\n    href: '/settings',\n    icon: Settings,\n  },\n];\n\nexport function Sidebar({ className }: SidebarProps) {\n  const [isCollapsed, setIsCollapsed] = useState(false);\n  const pathname = usePathname();\n\n  return (\n    <div\n      className={cn(\n        'relative flex flex-col bg-white border-r border-gray-200 transition-all duration-300',\n        isCollapsed ? 'w-16' : 'w-64',\n        className\n      )}\n    >\n      {/* Header */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n        {!isCollapsed && (\n          <div className=\"flex items-center gap-2\">\n            <Film className=\"text-blue-600\" size={24} />\n            <h2 className=\"text-xl font-semibold text-gray-900\">Scripty</h2>\n          </div>\n        )}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => setIsCollapsed(!isCollapsed)}\n          className=\"h-8 w-8 p-0\"\n        >\n          {isCollapsed ? (\n            <ChevronRight className=\"h-4 w-4\" />\n          ) : (\n            <ChevronLeft className=\"h-4 w-4\" />\n          )}\n        </Button>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 p-4\">\n        <ul className=\"space-y-2\">\n          {navigationItems.map((item) => {\n            const isActive = pathname === item.href || \n              (item.href !== '/' && pathname.startsWith(item.href));\n            \n            return (\n              <li key={item.href}>\n                <Link\n                  href={item.href}\n                  className={cn(\n                    'flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors',\n                    isActive\n                      ? 'bg-blue-50 text-blue-700 border border-blue-200'\n                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900',\n                    isCollapsed && 'justify-center px-2'\n                  )}\n                >\n                  <item.icon className=\"h-5 w-5 flex-shrink-0\" />\n                  {!isCollapsed && <span>{item.title}</span>}\n                </Link>\n              </li>\n            );\n          })}\n        </ul>\n      </nav>\n\n      {/* User section at bottom */}\n      <div className=\"p-4 border-t border-gray-200\">\n        {!isCollapsed ? (\n          <div className=\"flex items-center gap-3 px-3 py-2\">\n            <div className=\"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center\">\n              <span className=\"text-sm font-medium text-blue-700\">U</span>\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-sm font-medium text-gray-900 truncate\">User</p>\n              <p className=\"text-xs text-gray-500 truncate\"><EMAIL></p>\n            </div>\n          </div>\n        ) : (\n          <div className=\"flex justify-center\">\n            <div className=\"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center\">\n              <span className=\"text-sm font-medium text-blue-700\">U</span>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAhBA;;;;;;;;AAsBA,MAAM,kBAAkB;IACtB;QACE,OAAO;QACP,MAAM;QACN,MAAM,mMAAA,CAAA,OAAI;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,kMAAA,CAAA,OAAI;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;IACb;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;IACb;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;IAChB;CACD;AAEM,SAAS,QAAQ,EAAE,SAAS,EAAgB;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wFACA,cAAc,SAAS,QACvB;;0BAIF,8OAAC;gBAAI,WAAU;;oBACZ,CAAC,6BACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;gCAAgB,MAAM;;;;;;0CACtC,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAGxD,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,eAAe,CAAC;wBAC/B,WAAU;kCAET,4BACC,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;iDAExB,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAM7B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BACX,gBAAgB,GAAG,CAAC,CAAC;wBACpB,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;wBAErD,qBACE,8OAAC;sCACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sFACA,WACI,oDACA,uDACJ,eAAe;;kDAGjB,8OAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;oCACpB,CAAC,6BAAe,8OAAC;kDAAM,KAAK,KAAK;;;;;;;;;;;;2BAZ7B,KAAK,IAAI;;;;;oBAgBtB;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;0BACZ,CAAC,4BACA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAAoC;;;;;;;;;;;sCAEtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAC1D,8OAAC;oCAAE,WAAU;8CAAiC;;;;;;;;;;;;;;;;;yCAIlD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlE", "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/auth/SignOutButton.tsx"], "sourcesContent": ["\"use client\";\nimport { useAuthActions } from \"@convex-dev/auth/react\";\nimport { useConvexAuth } from \"convex/react\";\nimport { Button } from '../ui/button';\n\nexport function SignOutButton() {\n  const { isAuthenticated } = useConvexAuth();\n  const { signOut } = useAuthActions();\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <Button\n      variant={\"default\"}\n      size={\"sm\"}\n      onClick={() => void signOut()}\n    >\n      Sign out\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AAHA;;;;;AAKO,SAAS;IACd,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD;IACxC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD;IAEjC,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAS;QACT,MAAM;QACN,SAAS,IAAM,KAAK;kBACrB;;;;;;AAIL", "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { Film } from 'lucide-react';\nimport { SignOutButton } from '../auth/SignOutButton';\n\ninterface HeaderProps {\n  title?: string;\n  showSidebar?: boolean;\n}\n\nexport function Header({ title = 'Scripty', showSidebar = true }: HeaderProps) {\n  return (\n    <header className=\"sticky top-0 z-10 py-3 bg-white/80 backdrop-blur-sm h-16 flex justify-between items-center border-b shadow-sm px-4\">\n      <div className=\"flex items-center gap-2\">\n        {!showSidebar && (\n          <>\n            <Film className=\"text-blue-600\" size={24} />\n            <h2 className=\"text-xl font-semibold text-gray-900\">{title}</h2>\n          </>\n        )}\n      </div>\n      <SignOutButton />\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUO,SAAS,OAAO,EAAE,QAAQ,SAAS,EAAE,cAAc,IAAI,EAAe;IAC3E,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;0BACZ,CAAC,6BACA;;sCACE,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;4BAAgB,MAAM;;;;;;sCACtC,8OAAC;4BAAG,WAAU;sCAAuC;;;;;;;;;;;;;0BAI3D,8OAAC,2IAAA,CAAA,gBAAa;;;;;;;;;;;AAGpB", "debugId": null}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/auth/SignInForm.tsx"], "sourcesContent": ["\"use client\";\nimport { useAuthActions } from \"@convex-dev/auth/react\";\nimport { useState } from \"react\";\nimport { toast } from \"sonner\";\n\nexport function SignInForm() {\n  const { signIn } = useAuthActions();\n  const [flow, setFlow] = useState<\"signIn\" | \"signUp\">(\"signIn\");\n  const [submitting, setSubmitting] = useState(false);\n\n  return (\n    <div className=\"w-full\">\n      <form\n        className=\"flex flex-col gap-2\"\n        onSubmit={(e) => {\n          e.preventDefault();\n          setSubmitting(true);\n          const formData = new FormData(e.target as HTMLFormElement);\n          formData.set(\"flow\", flow);\n          void signIn(\"password\", formData).catch((error) => {\n            let toastTitle = \"\";\n            if (error.message.includes(\"Invalid password\")) {\n              toastTitle = \"Invalid password. Please try again.\";\n            } else {\n              toastTitle =\n                flow === \"signIn\"\n                  ? \"Could not sign in, did you mean to sign up?\"\n                  : \"Could not sign up, did you mean to sign in?\";\n            }\n            toast.error(toastTitle);\n            setSubmitting(false);\n          });\n        }}\n      >\n        <input\n          className=\"auth-input-field\"\n          type=\"email\"\n          name=\"email\"\n          placeholder=\"Email\"\n          required\n        />\n        <input\n          className=\"auth-input-field\"\n          type=\"password\"\n          name=\"password\"\n          placeholder=\"Password\"\n          required\n        />\n        <button className=\"auth-button\" type=\"submit\" disabled={submitting}>\n          {flow === \"signIn\" ? \"Sign in\" : \"Sign up\"}\n        </button>\n        <div className=\"text-center text-sm text-secondary\">\n          <span className=\"text-slate-400\">\n            {flow === \"signIn\"\n              ? \"Don't have an account? \"\n              : \"Already have an account? \"}\n          </span>\n          <button\n            type=\"button\"\n            className=\"text-primary text-right hover:text-primary-hover hover:underline font-medium cursor-pointer\"\n            onClick={() => setFlow(flow === \"signIn\" ? \"signUp\" : \"signIn\")}\n          >\n            {flow === \"signIn\" ? \"Sign up instead\" : \"Sign in instead\"}\n          </button>\n        </div>\n      </form>\n      <div className=\"flex items-center justify-center my-3\">\n        <hr className=\"my-4 grow border-gray-200\" />\n        <span className=\"mx-4 text-secondary\">or</span>\n        <hr className=\"my-4 grow border-gray-200\" />\n      </div>\n      <button className=\"auth-button\" onClick={() => void signIn(\"anonymous\")}>\n        Sign in anonymously\n      </button>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAKO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD;IAChC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAU;gBACV,UAAU,CAAC;oBACT,EAAE,cAAc;oBAChB,cAAc;oBACd,MAAM,WAAW,IAAI,SAAS,EAAE,MAAM;oBACtC,SAAS,GAAG,CAAC,QAAQ;oBACrB,KAAK,OAAO,YAAY,UAAU,KAAK,CAAC,CAAC;wBACvC,IAAI,aAAa;wBACjB,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,qBAAqB;4BAC9C,aAAa;wBACf,OAAO;4BACL,aACE,SAAS,WACL,gDACA;wBACR;wBACA,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACZ,cAAc;oBAChB;gBACF;;kCAEA,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,MAAK;wBACL,aAAY;wBACZ,QAAQ;;;;;;kCAEV,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,MAAK;wBACL,aAAY;wBACZ,QAAQ;;;;;;kCAEV,8OAAC;wBAAO,WAAU;wBAAc,MAAK;wBAAS,UAAU;kCACrD,SAAS,WAAW,YAAY;;;;;;kCAEnC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CACb,SAAS,WACN,4BACA;;;;;;0CAEN,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,QAAQ,SAAS,WAAW,WAAW;0CAErD,SAAS,WAAW,oBAAoB;;;;;;;;;;;;;;;;;;0BAI/C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;;;;;kCACd,8OAAC;wBAAK,WAAU;kCAAsB;;;;;;kCACtC,8OAAC;wBAAG,WAAU;;;;;;;;;;;;0BAEhB,8OAAC;gBAAO,WAAU;gBAAc,SAAS,IAAM,KAAK,OAAO;0BAAc;;;;;;;;;;;;AAK/E", "debugId": null}}, {"offset": {"line": 686, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/layout/AppLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport { Authenticated, Unauthenticated } from 'convex/react';\nimport { Sidebar } from './Sidebar';\nimport { Header } from './Header';\nimport { SignInForm } from '../auth/SignInForm';\nimport { FileText } from 'lucide-react';\n\ninterface AppLayoutProps {\n  children: ReactNode;\n  showSidebar?: boolean;\n}\n\nexport function AppLayout({ children, showSidebar = true }: AppLayoutProps) {\n  return (\n    <div className=\"h-screen flex flex-col bg-gray-50 overflow-hidden\" data-testid=\"app-loaded\">\n      <Unauthenticated>\n        <div className=\"flex-1 flex items-center justify-center p-8\">\n          <div className=\"w-full max-w-md mx-auto text-center\">\n            <div className=\"mb-8\">\n              <FileText size={64} className=\"mx-auto mb-4 text-blue-600\" />\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                Scripty\n              </h1>\n              <p className=\"text-gray-600\">\n                Create and edit scripts and screenplays together in real-time\n              </p>\n            </div>\n            <SignInForm />\n          </div>\n        </div>\n      </Unauthenticated>\n\n      <Authenticated>\n        <div className=\"flex-1 flex h-full overflow-hidden\">\n          {showSidebar && <Sidebar />}\n          <div className=\"flex-1 flex flex-col\">\n            <Header showSidebar={showSidebar} />\n            <main className=\"flex-1 overflow-hidden\">\n              {children}\n            </main>\n          </div>\n        </div>\n      </Authenticated>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AACA;AACA;AAPA;;;;;;;AAcO,SAAS,UAAU,EAAE,QAAQ,EAAE,cAAc,IAAI,EAAkB;IACxE,qBACE,8OAAC;QAAI,WAAU;QAAoD,eAAY;;0BAC7E,8OAAC,8JAAA,CAAA,kBAAe;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC9B,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAI/B,8OAAC,wIAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;0BAKjB,8OAAC,8JAAA,CAAA,gBAAa;0BACZ,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,6BAAe,8OAAC,uIAAA,CAAA,UAAO;;;;;sCACxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sIAAA,CAAA,SAAM;oCAAC,aAAa;;;;;;8CACrB,8OAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 825, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 906, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1005, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/dialog.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1136, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/hooks/use-toast.ts"], "sourcesContent": ["import * as React from \"react\"\n\nimport type {\n  ToastActionElement,\n  ToastProps,\n} from \"../components/ui/toast\"\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: \"ADD_TOAST\",\n  UPDATE_TOAST: \"UPDATE_TOAST\",\n  DISMISS_TOAST: \"DISMISS_TOAST\",\n  REMOVE_TOAST: \"REMOVE_TOAST\",\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType[\"ADD_TOAST\"]\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType[\"UPDATE_TOAST\"]\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType[\"DISMISS_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n  | {\n      type: ActionType[\"REMOVE_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: \"REMOVE_TOAST\",\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case \"ADD_TOAST\":\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case \"UPDATE_TOAST\":\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      }\n\n    case \"DISMISS_TOAST\": {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case \"REMOVE_TOAST\":\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach((listener) => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, \"id\">\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: \"UPDATE_TOAST\",\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\n\n  dispatch({\n    type: \"ADD_TOAST\",\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\n  }\n}\n\nexport { useToast, toast }\n"], "names": [], "mappings": ";;;;;AAAA;;AAOA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAS3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAc;IACpC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAA2C,EAAE;AAEnD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS,MAAM,EAAE,GAAG,OAAc;IAChC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAS;IAEhD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,UAAU,IAAI,CAAC;QACf,OAAO;YACL,MAAM,QAAQ,UAAU,OAAO,CAAC;YAChC,IAAI,QAAQ,CAAC,GAAG;gBACd,UAAU,MAAM,CAAC,OAAO;YAC1B;QACF;IACF,GAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAC3E;AACF", "debugId": null}}, {"offset": {"line": 1290, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/lib/routing.ts"], "sourcesContent": ["/**\n * URL routing utilities for document navigation\n * Handles document ID parsing, validation, and URL generation\n */\n\nimport { Id } from \"../../convex/_generated/dataModel\";\n\n/**\n * Validates if a string is a valid Convex document ID format\n * Convex IDs are strings at runtime, typically in format like \"j57abc123def456\"\n */\nexport function isValidDocumentId(id: string): boolean {\n  // Basic validation - Convex IDs are non-empty strings\n  // More specific validation could be added based on Convex ID patterns\n  // Exclude common route names that shouldn't be treated as document IDs\n  const excludedRoutes = ['other', 'about', 'settings', 'profile', 'admin', 'api'];\n  return typeof id === \"string\" &&\n         id.length > 0 &&\n         !id.includes(\"/\") &&\n         !id.includes(\"?\") &&\n         !excludedRoutes.includes(id.toLowerCase());\n}\n\n/**\n * Validates if a string is a valid Convex script ID format\n */\nexport function isValidScriptId(id: string): boolean {\n  // Same validation as document IDs\n  const excludedRoutes = ['other', 'about', 'settings', 'profile', 'admin', 'api'];\n  return typeof id === \"string\" &&\n         id.length > 0 &&\n         !id.includes(\"/\") &&\n         !id.includes(\"?\") &&\n         !excludedRoutes.includes(id.toLowerCase());\n}\n\n/**\n * Extracts script and document IDs from URL pathname\n * Supports patterns:\n * - /document/{documentId} (legacy standalone documents)\n * - /script/{scriptId}/document/{documentId} (new hierarchical structure)\n */\nexport function extractIdsFromPath(pathname: string): {\n  scriptId: string | null;\n  documentId: string | null;\n} {\n  // Remove leading slash\n  const cleanPath = pathname.startsWith(\"/\") ? pathname.slice(1) : pathname;\n\n  // Handle /script/{scriptId}/document/{documentId} pattern\n  if (cleanPath.startsWith(\"script/\")) {\n    const parts = cleanPath.split(\"/\");\n    if (parts.length >= 4 && parts[0] === \"script\" && parts[2] === \"document\") {\n      const scriptId = parts[1];\n      const documentId = parts[3];\n\n      if (isValidScriptId(scriptId) && isValidDocumentId(documentId)) {\n        return { scriptId, documentId };\n      }\n    }\n\n    // Handle /script/{scriptId} pattern (script only)\n    if (parts.length === 2 && parts[0] === \"script\") {\n      const scriptId = parts[1];\n      if (isValidScriptId(scriptId)) {\n        return { scriptId, documentId: null };\n      }\n    }\n  }\n\n  // Handle legacy /document/{documentId} pattern\n  if (cleanPath.startsWith(\"document/\")) {\n    const documentId = cleanPath.slice(\"document/\".length);\n    if (isValidDocumentId(documentId)) {\n      return { scriptId: null, documentId };\n    }\n  }\n\n  return { scriptId: null, documentId: null };\n}\n\n/**\n * Extracts document ID from URL pathname (legacy function for backward compatibility)\n * Supports patterns: /document/{documentId} only for better URL structure\n */\nexport function extractDocumentIdFromPath(pathname: string): string | null {\n  const { documentId } = extractIdsFromPath(pathname);\n  return documentId;\n}\n\n/**\n * Extracts document ID from URL search params\n * Supports pattern: ?doc={documentId}\n */\nexport function extractDocumentIdFromSearch(search: string): string | null {\n  const params = new URLSearchParams(search);\n  const docId = params.get(\"doc\");\n  \n  if (docId && isValidDocumentId(docId)) {\n    return docId;\n  }\n  \n  return null;\n}\n\n/**\n * Extracts document ID from full URL (pathname or search params)\n * Tries pathname first, then falls back to search params\n */\nexport function extractDocumentIdFromUrl(url: string): string | null {\n  try {\n    const urlObj = new URL(url, window.location.origin);\n    \n    // Try pathname first\n    const pathId = extractDocumentIdFromPath(urlObj.pathname);\n    if (pathId) {\n      return pathId;\n    }\n    \n    // Fall back to search params\n    return extractDocumentIdFromSearch(urlObj.search);\n  } catch {\n    // If URL parsing fails, try as pathname only\n    return extractDocumentIdFromPath(url);\n  }\n}\n\n/**\n * Generates URL for a script\n */\nexport function generateScriptUrl(scriptId: Id<\"scripts\">): string {\n  return `/script/${scriptId}`;\n}\n\n/**\n * Generates URL for a document within a script\n */\nexport function generateScriptDocumentUrl(scriptId: Id<\"scripts\">, documentId: Id<\"documents\">): string {\n  return `/script/${scriptId}/document/${documentId}`;\n}\n\n/**\n * Generates URL for a document using the /document/{documentId} pattern (legacy standalone documents)\n */\nexport function generateDocumentUrl(documentId: Id<\"documents\">): string {\n  return `/document/${documentId}`;\n}\n\n/**\n * Generates URL for the home page (no document selected)\n */\nexport function generateHomeUrl(): string {\n  return \"/\";\n}\n\n/**\n * Checks if the current URL represents a document route\n */\nexport function isDocumentRoute(pathname: string): boolean {\n  return extractDocumentIdFromPath(pathname) !== null;\n}\n\n/**\n * Navigation utilities for programmatic routing\n */\nexport const navigationUtils = {\n  /**\n   * Navigate to a script without page refresh\n   */\n  navigateToScript: (scriptId: Id<\"scripts\">) => {\n    const url = generateScriptUrl(scriptId);\n    window.history.pushState(null, \"\", url);\n  },\n\n  /**\n   * Navigate to a document within a script without page refresh\n   */\n  navigateToScriptDocument: (scriptId: Id<\"scripts\">, documentId: Id<\"documents\">) => {\n    const url = generateScriptDocumentUrl(scriptId, documentId);\n    window.history.pushState(null, \"\", url);\n  },\n\n  /**\n   * Navigate to a standalone document without page refresh (legacy)\n   */\n  navigateToDocument: (documentId: Id<\"documents\">) => {\n    const url = generateDocumentUrl(documentId);\n    window.history.pushState(null, \"\", url);\n  },\n\n  /**\n   * Navigate to home without page refresh\n   */\n  navigateToHome: () => {\n    const url = generateHomeUrl();\n    window.history.pushState(null, \"\", url);\n  },\n\n  /**\n   * Replace current URL without adding to history\n   */\n  replaceUrl: (url: string) => {\n    window.history.replaceState(null, \"\", url);\n  }\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;AAQM,SAAS,kBAAkB,EAAU;IAC1C,sDAAsD;IACtD,sEAAsE;IACtE,uEAAuE;IACvE,MAAM,iBAAiB;QAAC;QAAS;QAAS;QAAY;QAAW;QAAS;KAAM;IAChF,OAAO,OAAO,OAAO,YACd,GAAG,MAAM,GAAG,KACZ,CAAC,GAAG,QAAQ,CAAC,QACb,CAAC,GAAG,QAAQ,CAAC,QACb,CAAC,eAAe,QAAQ,CAAC,GAAG,WAAW;AAChD;AAKO,SAAS,gBAAgB,EAAU;IACxC,kCAAkC;IAClC,MAAM,iBAAiB;QAAC;QAAS;QAAS;QAAY;QAAW;QAAS;KAAM;IAChF,OAAO,OAAO,OAAO,YACd,GAAG,MAAM,GAAG,KACZ,CAAC,GAAG,QAAQ,CAAC,QACb,CAAC,GAAG,QAAQ,CAAC,QACb,CAAC,eAAe,QAAQ,CAAC,GAAG,WAAW;AAChD;AAQO,SAAS,mBAAmB,QAAgB;IAIjD,uBAAuB;IACvB,MAAM,YAAY,SAAS,UAAU,CAAC,OAAO,SAAS,KAAK,CAAC,KAAK;IAEjE,0DAA0D;IAC1D,IAAI,UAAU,UAAU,CAAC,YAAY;QACnC,MAAM,QAAQ,UAAU,KAAK,CAAC;QAC9B,IAAI,MAAM,MAAM,IAAI,KAAK,KAAK,CAAC,EAAE,KAAK,YAAY,KAAK,CAAC,EAAE,KAAK,YAAY;YACzE,MAAM,WAAW,KAAK,CAAC,EAAE;YACzB,MAAM,aAAa,KAAK,CAAC,EAAE;YAE3B,IAAI,gBAAgB,aAAa,kBAAkB,aAAa;gBAC9D,OAAO;oBAAE;oBAAU;gBAAW;YAChC;QACF;QAEA,kDAAkD;QAClD,IAAI,MAAM,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE,KAAK,UAAU;YAC/C,MAAM,WAAW,KAAK,CAAC,EAAE;YACzB,IAAI,gBAAgB,WAAW;gBAC7B,OAAO;oBAAE;oBAAU,YAAY;gBAAK;YACtC;QACF;IACF;IAEA,+CAA+C;IAC/C,IAAI,UAAU,UAAU,CAAC,cAAc;QACrC,MAAM,aAAa,UAAU,KAAK,CAAC,YAAY,MAAM;QACrD,IAAI,kBAAkB,aAAa;YACjC,OAAO;gBAAE,UAAU;gBAAM;YAAW;QACtC;IACF;IAEA,OAAO;QAAE,UAAU;QAAM,YAAY;IAAK;AAC5C;AAMO,SAAS,0BAA0B,QAAgB;IACxD,MAAM,EAAE,UAAU,EAAE,GAAG,mBAAmB;IAC1C,OAAO;AACT;AAMO,SAAS,4BAA4B,MAAc;IACxD,MAAM,SAAS,IAAI,gBAAgB;IACnC,MAAM,QAAQ,OAAO,GAAG,CAAC;IAEzB,IAAI,SAAS,kBAAkB,QAAQ;QACrC,OAAO;IACT;IAEA,OAAO;AACT;AAMO,SAAS,yBAAyB,GAAW;IAClD,IAAI;QACF,MAAM,SAAS,IAAI,IAAI,KAAK,OAAO,QAAQ,CAAC,MAAM;QAElD,qBAAqB;QACrB,MAAM,SAAS,0BAA0B,OAAO,QAAQ;QACxD,IAAI,QAAQ;YACV,OAAO;QACT;QAEA,6BAA6B;QAC7B,OAAO,4BAA4B,OAAO,MAAM;IAClD,EAAE,OAAM;QACN,6CAA6C;QAC7C,OAAO,0BAA0B;IACnC;AACF;AAKO,SAAS,kBAAkB,QAAuB;IACvD,OAAO,CAAC,QAAQ,EAAE,UAAU;AAC9B;AAKO,SAAS,0BAA0B,QAAuB,EAAE,UAA2B;IAC5F,OAAO,CAAC,QAAQ,EAAE,SAAS,UAAU,EAAE,YAAY;AACrD;AAKO,SAAS,oBAAoB,UAA2B;IAC7D,OAAO,CAAC,UAAU,EAAE,YAAY;AAClC;AAKO,SAAS;IACd,OAAO;AACT;AAKO,SAAS,gBAAgB,QAAgB;IAC9C,OAAO,0BAA0B,cAAc;AACjD;AAKO,MAAM,kBAAkB;IAC7B;;GAEC,GACD,kBAAkB,CAAC;QACjB,MAAM,MAAM,kBAAkB;QAC9B,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI;IACrC;IAEA;;GAEC,GACD,0BAA0B,CAAC,UAAyB;QAClD,MAAM,MAAM,0BAA0B,UAAU;QAChD,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI;IACrC;IAEA;;GAEC,GACD,oBAAoB,CAAC;QACnB,MAAM,MAAM,oBAAoB;QAChC,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI;IACrC;IAEA;;GAEC,GACD,gBAAgB;QACd,MAAM,MAAM;QACZ,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI;IACrC;IAEA;;GAEC,GACD,YAAY,CAAC;QACX,OAAO,OAAO,CAAC,YAAY,CAAC,MAAM,IAAI;IACxC;AACF", "debugId": null}}, {"offset": {"line": 1454, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/hooks/useScriptRouting.ts"], "sourcesContent": ["/**\n * React hook for URL-based script and document routing\n * Manages script and document selection via URL synchronization\n * Updated for Next.js App Router\n */\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { Id } from '../../convex/_generated/dataModel';\nimport {\n  extractIdsFromPath,\n  generateScriptUrl,\n  generateScriptDocumentUrl,\n  generateDocumentUrl,\n  generateHomeUrl\n} from '../lib/routing';\n\ninterface UseScriptRoutingReturn {\n  selectedScriptId: Id<\"scripts\"> | null;\n  selectedDocumentId: Id<\"documents\"> | null;\n  navigateToScript: (scriptId: Id<\"scripts\">) => void;\n  navigateToScriptDocument: (scriptId: Id<\"scripts\">, documentId: Id<\"documents\">) => void;\n  navigateToDocument: (documentId: Id<\"documents\">) => void;\n  navigateToHome: () => void;\n  isLoading: boolean;\n}\n\n/**\n * Hook for managing script routing via Next.js App Router\n */\nexport function useScriptRouting(): UseScriptRoutingReturn {\n  const router = useRouter();\n  const pathname = usePathname();\n  const [selectedScriptId, setSelectedScriptId] = useState<Id<\"scripts\"> | null>(null);\n  const [selectedDocumentId, setSelectedDocumentId] = useState<Id<\"documents\"> | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Extract IDs from current pathname\n  useEffect(() => {\n    const { scriptId, documentId } = extractIdsFromPath(pathname);\n\n    if (scriptId) {\n      setSelectedScriptId(scriptId as Id<\"scripts\">);\n    }\n\n    if (documentId) {\n      setSelectedDocumentId(documentId as Id<\"documents\">);\n    }\n\n    setIsLoading(false);\n  }, [pathname]);\n\n  // Navigate to a specific script\n  const navigateToScript = useCallback((scriptId: Id<\"scripts\">) => {\n    const url = generateScriptUrl(scriptId);\n    router.push(url);\n  }, [router]);\n\n  // Navigate to a document within a script\n  const navigateToScriptDocument = useCallback((scriptId: Id<\"scripts\">, documentId: Id<\"documents\">) => {\n    const url = generateScriptDocumentUrl(scriptId, documentId);\n    router.push(url);\n  }, [router]);\n\n  // Navigate to a standalone document (legacy)\n  const navigateToDocument = useCallback((documentId: Id<\"documents\">) => {\n    const url = generateDocumentUrl(documentId);\n    router.push(url);\n  }, [router]);\n\n  // Navigate to home\n  const navigateToHome = useCallback(() => {\n    const url = generateHomeUrl();\n    router.push(url);\n  }, [router]);\n\n  return {\n    selectedScriptId,\n    selectedDocumentId,\n    navigateToScript,\n    navigateToScriptDocument,\n    navigateToDocument,\n    navigateToHome,\n    isLoading\n  };\n}\n\n\n\n/**\n * Hook for URL generation utilities\n * Provides utilities for creating script and document links\n */\nexport function useScriptUrls() {\n  const getScriptUrl = useCallback((scriptId: Id<\"scripts\">) => {\n    return generateScriptUrl(scriptId);\n  }, []);\n\n  const getScriptDocumentUrl = useCallback((scriptId: Id<\"scripts\">, documentId: Id<\"documents\">) => {\n    return generateScriptDocumentUrl(scriptId, documentId);\n  }, []);\n\n  const getDocumentUrl = useCallback((documentId: Id<\"documents\">) => {\n    return generateDocumentUrl(documentId);\n  }, []);\n\n  const getHomeUrl = useCallback(() => {\n    return generateHomeUrl();\n  }, []);\n\n  return {\n    getScriptUrl,\n    getScriptDocumentUrl,\n    getDocumentUrl,\n    getHomeUrl\n  };\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAED;AACA;AAEA;;;;AAqBO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC/E,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACrF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,qBAAkB,AAAD,EAAE;QAEpD,IAAI,UAAU;YACZ,oBAAoB;QACtB;QAEA,IAAI,YAAY;YACd,sBAAsB;QACxB;QAEA,aAAa;IACf,GAAG;QAAC;KAAS;IAEb,gCAAgC;IAChC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,MAAM,MAAM,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD,EAAE;QAC9B,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;KAAO;IAEX,yCAAyC;IACzC,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAyB;QACrE,MAAM,MAAM,CAAA,GAAA,qHAAA,CAAA,4BAAyB,AAAD,EAAE,UAAU;QAChD,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;KAAO;IAEX,6CAA6C;IAC7C,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,MAAM,MAAM,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAAE;QAChC,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;KAAO;IAEX,mBAAmB;IACnB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,MAAM,MAAM,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD;QAC1B,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;KAAO;IAEX,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAQO,SAAS;IACd,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,OAAO,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD,EAAE;IAC3B,GAAG,EAAE;IAEL,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAyB;QACjE,OAAO,CAAA,GAAA,qHAAA,CAAA,4BAAyB,AAAD,EAAE,UAAU;IAC7C,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,OAAO,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAAE;IAC7B,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,OAAO,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD;IACvB,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1551, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ScriptList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from \"react\";\nimport * as React from \"react\";\nimport { useQuery, useMutation } from \"convex/react\";\nimport { api } from \"../../convex/_generated/api\";\nimport { But<PERSON> } from \"./ui/button\";\nimport { Card, CardContent } from \"./ui/card\";\nimport { Badge } from \"./ui/badge\";\nimport { Input } from \"./ui/input\";\nimport { Textarea } from \"./ui/textarea\";\nimport {\n  Plus,\n  Trash2,\n  Crown,\n  Eye,\n  Edit,\n  Lock,\n  AlertCircle,\n  FolderOpen\n} from \"lucide-react\";\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from \"./ui/dialog\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { useScriptUrls } from \"@/hooks/useScriptRouting\";\nimport { Id } from \"../../convex/_generated/dataModel\";\n\ninterface ScriptListProps {\n  onSelectScript: (scriptId: Id<\"scripts\">) => void;\n  selectedScriptId: Id<\"scripts\"> | null;\n  onScriptCountChange?: (count: number) => void;\n}\n\nexport function ScriptList({ onSelectScript, selectedScriptId, onScriptCountChange }: ScriptListProps) {\n  const scripts = useQuery(api.scripts.getUserScripts);\n  const canCreateResult = useQuery(api.scripts.canCreateScripts);\n  const createScript = useMutation(api.scripts.createScript);\n  const deleteScript = useMutation(api.scripts.deleteScript);\n  const { getScriptUrl } = useScriptUrls();\n\n  // Extract canCreate boolean for backward compatibility\n  const canCreate = canCreateResult?.canCreate ?? false;\n\n  // State for create dialog\n  const [showCreateDialog, setShowCreateDialog] = useState(false);\n  const [newScriptTitle, setNewScriptTitle] = useState(\"\");\n  const [newScriptDescription, setNewScriptDescription] = useState(\"\");\n  const [isCreating, setIsCreating] = useState(false);\n\n  // State for delete dialog\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\n  const [scriptToDelete, setScriptToDelete] = useState<Id<\"scripts\"> | null>(null);\n  const [isDeleting, setIsDeleting] = useState(false);\n\n  const { toast } = useToast();\n\n  // Notify parent component of script count changes\n  React.useEffect(() => {\n    if (scripts && onScriptCountChange) {\n      onScriptCountChange(scripts.length);\n    }\n  }, [scripts, onScriptCountChange]);\n\n  const handleShowCreateDialog = () => {\n    setNewScriptTitle(\"\");\n    setNewScriptDescription(\"\");\n    setShowCreateDialog(true);\n  };\n\n  const handleCreateScript = async () => {\n    if (!newScriptTitle.trim()) {\n      toast({\n        title: \"Error\",\n        description: \"Please enter a script title\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    setIsCreating(true);\n    try {\n      const scriptId = await createScript({\n        title: newScriptTitle.trim(),\n        description: newScriptDescription.trim() || undefined,\n      });\n\n      toast({\n        title: \"Success\",\n        description: \"Script created successfully\",\n      });\n\n      setShowCreateDialog(false);\n      setNewScriptTitle(\"\");\n      setNewScriptDescription(\"\");\n\n      // Navigate to the new script\n      onSelectScript(scriptId);\n    } catch (error) {\n      console.error(\"Failed to create script:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to create script. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsCreating(false);\n    }\n  };\n\n  const handleDeleteScript = async () => {\n    if (!scriptToDelete) return;\n\n    setIsDeleting(true);\n    try {\n      await deleteScript({ id: scriptToDelete });\n      \n      toast({\n        title: \"Success\",\n        description: \"Script deleted successfully\",\n      });\n\n      setShowDeleteDialog(false);\n      setScriptToDelete(null);\n\n      // If the deleted script was selected, clear selection\n      if (selectedScriptId === scriptToDelete) {\n        // Navigate to home or first available script\n        if (scripts && scripts.length > 1) {\n          const remainingScripts = scripts.filter(s => s._id !== scriptToDelete);\n          if (remainingScripts.length > 0) {\n            onSelectScript(remainingScripts[0]._id);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Failed to delete script:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to delete script. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n\n  const getPermissionIcon = (permission: string) => {\n    switch (permission) {\n      case \"owner\":\n        return <Crown className=\"h-3 w-3\" />;\n      case \"write\":\n        return <Edit className=\"h-3 w-3\" />;\n      case \"read\":\n        return <Eye className=\"h-3 w-3\" />;\n      default:\n        return <Lock className=\"h-3 w-3\" />;\n    }\n  };\n\n  const getPermissionColor = (permission: string) => {\n    switch (permission) {\n      case \"owner\":\n        return \"default\";\n      case \"write\":\n        return \"secondary\";\n      case \"read\":\n        return \"outline\";\n      default:\n        return \"destructive\";\n    }\n  };\n\n  if (!scripts) {\n    return (\n      <div className=\"w-64 h-screen bg-white border-r border-gray-200 hidden md:flex md:flex-col\">\n        <div className=\"p-6 border-b border-gray-200 bg-white\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">Scripts</h2>\n        </div>\n        <div className=\"flex-1 flex items-center justify-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"w-64 h-screen bg-white border-r border-gray-200 hidden md:flex md:flex-col\">\n      {/* Header */}\n      <div className=\"p-6 border-b border-gray-200 bg-white\">\n        {/* <div className=\"flex items-center justify-between\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">Scripts</h2>\n          {canCreate && (\n            <Button\n              onClick={handleShowCreateDialog}\n              disabled={isCreating}\n              size=\"sm\"\n              className=\"gap-2\"\n            >\n              <Plus className=\"h-4 w-4\" />\n              New Script\n            </Button>\n          )}\n        </div> */}\n      </div>\n\n      {/* Content */}\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n        {canCreate === false && (\n          <Card className=\"border-amber-200 bg-amber-50\">\n            <CardContent className=\"p-3\">\n              <div className=\"flex items-start gap-2 text-amber-800\">\n                <AlertCircle className=\"h-4 w-4 mt-0.5 flex-shrink-0\" />\n                <div>\n                  <p className=\"font-medium text-sm\">Limited Access</p>\n                  <p className=\"text-xs\">\n                    {canCreateResult?.reason === \"anonymous_user\"\n                      ? \"Anonymous users can only view shared scripts.\"\n                      : canCreateResult?.reason === \"not_authenticated\"\n                        ? \"Please sign in to create and manage scripts.\"\n                        : \"You can view shared scripts but cannot create new ones.\"\n                    }\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        {scripts.length === 0 ? (\n          <div className=\"text-center py-8 text-gray-500\">\n            <FolderOpen size={48} className=\"mx-auto mb-4 opacity-50\" />\n            <p className=\"text-sm\">No scripts available</p>\n            {canCreate && (\n              <p className=\"text-xs mt-2\">Create your first script to get started</p>\n            )}\n          </div>\n        ) : (\n          <div className=\"space-y-3\">\n            {scripts.map((script) => {\n              const isSelected = selectedScriptId === script._id;\n              \n              return (\n                <a\n                  key={script._id}\n                  href={getScriptUrl(script._id)}\n                  onClick={(e) => {\n                    e.preventDefault();\n                    onSelectScript(script._id);\n                  }}\n                  className={`block p-4 rounded-lg border transition-all duration-200 group hover:shadow-md ${\n                    isSelected\n                      ? 'border-blue-500 bg-blue-50 shadow-sm'\n                      : 'border-gray-200 bg-white hover:border-gray-300'\n                  }`}\n                >\n                  <div className=\"flex items-start justify-between mb-2\">\n                    <h3 className={`text-sm font-medium line-clamp-2 flex-1 mr-2 ${\n                      isSelected ? 'text-blue-900' : 'text-gray-900'\n                    }`}>\n                      {script.title}\n                    </h3>\n                    {script.permission === \"owner\" && (\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        onClick={(e) => {\n                          e.preventDefault();\n                          e.stopPropagation();\n                          setScriptToDelete(script._id);\n                          setShowDeleteDialog(true);\n                        }}\n                        className=\"opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0 text-gray-400 hover:text-red-600\"\n                      >\n                        <Trash2 className=\"h-3 w-3\" />\n                      </Button>\n                    )}\n                  </div>\n\n                  {script.description && (\n                    <p className=\"text-xs text-gray-600 mb-2 line-clamp-2\">\n                      {script.description}\n                    </p>\n                  )}\n\n                  <div className=\"flex items-center justify-between\">\n                    <Badge\n                      variant={getPermissionColor(script.permission) as any}\n                      className=\"gap-1 text-xs\"\n                    >\n                      {getPermissionIcon(script.permission)}\n                      {script.permission}\n                    </Badge>\n                    <span className=\"text-xs text-muted-foreground\">\n                      {new Date(script._creationTime).toLocaleDateString()}\n                    </span>\n                  </div>\n                </a>\n              );\n            })}\n          </div>\n        )}\n      </div>\n\n      {/* Create Script Dialog */}\n      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>Create New Script</DialogTitle>\n          </DialogHeader>\n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"script-title\" className=\"text-sm font-medium\">\n                Title *\n              </label>\n              <Input\n                id=\"script-title\"\n                value={newScriptTitle}\n                onChange={(e) => setNewScriptTitle(e.target.value)}\n                placeholder=\"Enter script title...\"\n                className=\"mt-1\"\n              />\n            </div>\n            <div>\n              <label htmlFor=\"script-description\" className=\"text-sm font-medium\">\n                Description\n              </label>\n              <Textarea\n                id=\"script-description\"\n                value={newScriptDescription}\n                onChange={(e) => setNewScriptDescription(e.target.value)}\n                placeholder=\"Enter script description...\"\n                className=\"mt-1\"\n                rows={3}\n              />\n            </div>\n          </div>\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowCreateDialog(false)}\n              disabled={isCreating}\n            >\n              Cancel\n            </Button>\n            <Button onClick={handleCreateScript} disabled={isCreating}>\n              {isCreating ? \"Creating...\" : \"Create Script\"}\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* Delete Script Dialog */}\n      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>Delete Script</DialogTitle>\n          </DialogHeader>\n          <p className=\"text-sm text-gray-600\">\n            Are you sure you want to delete this script? This will also delete all documents within the script. This action cannot be undone.\n          </p>\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowDeleteDialog(false)}\n              disabled={isDeleting}\n            >\n              Cancel\n            </Button>\n            <Button\n              variant=\"destructive\"\n              onClick={handleDeleteScript}\n              disabled={isDeleting}\n            >\n              {isDeleting ? \"Deleting...\" : \"Delete Script\"}\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AAvBA;;;;;;;;;;;;;;;AAgCO,SAAS,WAAW,EAAE,cAAc,EAAE,gBAAgB,EAAE,mBAAmB,EAAmB;IACnG,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,cAAc;IACnD,MAAM,kBAAkB,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,gBAAgB;IAC7D,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,YAAY;IACzD,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,YAAY;IACzD,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;IAErC,uDAAuD;IACvD,MAAM,YAAY,iBAAiB,aAAa;IAEhD,0BAA0B;IAC1B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,0BAA0B;IAC1B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC3E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAEzB,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,WAAW,qBAAqB;YAClC,oBAAoB,QAAQ,MAAM;QACpC;IACF,GAAG;QAAC;QAAS;KAAoB;IAEjC,MAAM,yBAAyB;QAC7B,kBAAkB;QAClB,wBAAwB;QACxB,oBAAoB;IACtB;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,eAAe,IAAI,IAAI;YAC1B,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,cAAc;QACd,IAAI;YACF,MAAM,WAAW,MAAM,aAAa;gBAClC,OAAO,eAAe,IAAI;gBAC1B,aAAa,qBAAqB,IAAI,MAAM;YAC9C;YAEA,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,oBAAoB;YACpB,kBAAkB;YAClB,wBAAwB;YAExB,6BAA6B;YAC7B,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,gBAAgB;QAErB,cAAc;QACd,IAAI;YACF,MAAM,aAAa;gBAAE,IAAI;YAAe;YAExC,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,oBAAoB;YACpB,kBAAkB;YAElB,sDAAsD;YACtD,IAAI,qBAAqB,gBAAgB;gBACvC,6CAA6C;gBAC7C,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;oBACjC,MAAM,mBAAmB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK;oBACvD,IAAI,iBAAiB,MAAM,GAAG,GAAG;wBAC/B,eAAe,gBAAgB,CAAC,EAAE,CAAC,GAAG;oBACxC;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,2MAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;;;;;;8BAEtD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;;;;;0BAkBf,8OAAC;gBAAI,WAAU;;oBACZ,cAAc,uBACb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DACV,iBAAiB,WAAW,mBACzB,kDACA,iBAAiB,WAAW,sBAC1B,iDACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASjB,QAAQ,MAAM,KAAK,kBAClB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kNAAA,CAAA,aAAU;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAChC,8OAAC;gCAAE,WAAU;0CAAU;;;;;;4BACtB,2BACC,8OAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;6CAIhC,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC;4BACZ,MAAM,aAAa,qBAAqB,OAAO,GAAG;4BAElD,qBACE,8OAAC;gCAEC,MAAM,aAAa,OAAO,GAAG;gCAC7B,SAAS,CAAC;oCACR,EAAE,cAAc;oCAChB,eAAe,OAAO,GAAG;gCAC3B;gCACA,WAAW,CAAC,8EAA8E,EACxF,aACI,yCACA,kDACJ;;kDAEF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAW,CAAC,6CAA6C,EAC3D,aAAa,kBAAkB,iBAC/B;0DACC,OAAO,KAAK;;;;;;4CAEd,OAAO,UAAU,KAAK,yBACrB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,EAAE,eAAe;oDACjB,kBAAkB,OAAO,GAAG;oDAC5B,oBAAoB;gDACtB;gDACA,WAAU;0DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;oCAKvB,OAAO,WAAW,kBACjB,8OAAC;wCAAE,WAAU;kDACV,OAAO,WAAW;;;;;;kDAIvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAS,mBAAmB,OAAO,UAAU;gDAC7C,WAAU;;oDAET,kBAAkB,OAAO,UAAU;oDACnC,OAAO,UAAU;;;;;;;0DAEpB,8OAAC;gDAAK,WAAU;0DACb,IAAI,KAAK,OAAO,aAAa,EAAE,kBAAkB;;;;;;;;;;;;;+BAlDjD,OAAO,GAAG;;;;;wBAuDrB;;;;;;;;;;;;0BAMN,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;;sCACZ,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;0CAAC;;;;;;;;;;;sCAEf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAe,WAAU;sDAAsB;;;;;;sDAG9D,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CACjD,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAGd,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAqB,WAAU;sDAAsB;;;;;;sDAGpE,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,wBAAwB,EAAE,MAAM,CAAC,KAAK;4CACvD,aAAY;4CACZ,WAAU;4CACV,MAAM;;;;;;;;;;;;;;;;;;sCAIZ,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,oBAAoB;oCACnC,UAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAoB,UAAU;8CAC5C,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAOtC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;;sCACZ,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;0CAAC;;;;;;;;;;;sCAEf,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;sCAGrC,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,oBAAoB;oCACnC,UAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;8CAET,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C", "debugId": null}}, {"offset": {"line": 2183, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/select.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAEA;;;;;;AAEA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2374, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ScriptDocumentList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from \"react\";\nimport * as React from \"react\";\nimport { useQuery, useMutation } from \"convex/react\";\nimport { api } from \"../../convex/_generated/api\";\nimport { Button } from \"./ui/button\";\nimport { Card, CardContent } from \"./ui/card\";\nimport { Badge } from \"./ui/badge\";\nimport { Input } from \"./ui/input\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"./ui/select\";\nimport {\n  Plus,\n  FileText,\n  Trash2,\n  Crown,\n  Eye,\n  Edit,\n  Lock,\n  AlertCircle,\n  Film,\n  BookOpen,\n  FolderOpen\n} from \"lucide-react\";\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from \"./ui/dialog\";\nimport { useToast } from \"../hooks/use-toast\";\nimport { useScriptUrls } from \"../hooks/useScriptRouting\";\nimport { Id } from \"../../convex/_generated/dataModel\";\n\ninterface ScriptDocumentListProps {\n  scriptId: Id<\"scripts\">;\n  onSelectDocument: (scriptId: Id<\"scripts\">, documentId: Id<\"documents\">) => void;\n  selectedDocumentId: Id<\"documents\"> | null;\n  onDocumentCountChange?: (count: number) => void;\n}\n\ntype DocumentType = \"research\" | \"screenplay\" | \"collection\";\n\nexport function ScriptDocumentList({ \n  scriptId, \n  onSelectDocument, \n  selectedDocumentId, \n  onDocumentCountChange \n}: ScriptDocumentListProps) {\n  const documents = useQuery(api.documents.getScriptDocuments, { scriptId });\n  const scriptPermission = useQuery(api.scriptSharing.getScriptPermission, { scriptId });\n  const createDocument = useMutation(api.documents.createDocument);\n  const deleteDocument = useMutation(api.documents.deleteDocument);\n  const { getScriptDocumentUrl } = useScriptUrls();\n\n  // Check if user can create documents in this script\n  const canCreate = scriptPermission?.permission === \"owner\" || scriptPermission?.permission === \"write\";\n\n  // State for create dialog\n  const [showCreateDialog, setShowCreateDialog] = useState(false);\n  const [newDocumentTitle, setNewDocumentTitle] = useState(\"\");\n  const [newDocumentType, setNewDocumentType] = useState<DocumentType>(\"research\");\n  const [isCreating, setIsCreating] = useState(false);\n\n  // State for delete dialog\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\n  const [documentToDelete, setDocumentToDelete] = useState<Id<\"documents\"> | null>(null);\n  const [isDeleting, setIsDeleting] = useState(false);\n\n  const { toast } = useToast();\n\n  // Notify parent component of document count changes\n  React.useEffect(() => {\n    if (documents && onDocumentCountChange) {\n      onDocumentCountChange(documents.length);\n    }\n  }, [documents, onDocumentCountChange]);\n\n  const handleShowCreateDialog = () => {\n    setNewDocumentTitle(\"\");\n    setNewDocumentType(\"research\");\n    setShowCreateDialog(true);\n  };\n\n  const handleCreateDocument = async () => {\n    if (!newDocumentTitle.trim()) {\n      toast({\n        title: \"Error\",\n        description: \"Please enter a document title\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    setIsCreating(true);\n    try {\n      const documentId = await createDocument({\n        title: newDocumentTitle.trim(),\n        scriptId,\n        documentType: newDocumentType,\n      });\n\n      toast({\n        title: \"Success\",\n        description: \"Document created successfully\",\n      });\n\n      setShowCreateDialog(false);\n      setNewDocumentTitle(\"\");\n      setNewDocumentType(\"research\");\n\n      // Navigate to the new document\n      onSelectDocument(scriptId, documentId);\n    } catch (error) {\n      console.error(\"Failed to create document:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to create document. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsCreating(false);\n    }\n  };\n\n  const handleDeleteDocument = async () => {\n    if (!documentToDelete) return;\n\n    setIsDeleting(true);\n    try {\n      await deleteDocument({ id: documentToDelete });\n      \n      toast({\n        title: \"Success\",\n        description: \"Document deleted successfully\",\n      });\n\n      setShowDeleteDialog(false);\n      setDocumentToDelete(null);\n\n      // If the deleted document was selected, clear selection\n      if (selectedDocumentId === documentToDelete) {\n        // Navigate to first available document or script\n        if (documents && documents.length > 1) {\n          const remainingDocuments = documents.filter(d => d._id !== documentToDelete);\n          if (remainingDocuments.length > 0) {\n            onSelectDocument(scriptId, remainingDocuments[0]._id);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Failed to delete document:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to delete document. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n\n  const getDocumentTypeIcon = (type: string) => {\n    switch (type) {\n      case \"screenplay\":\n        return <Film className=\"h-3 w-3\" />;\n      case \"collection\":\n        return <FolderOpen className=\"h-3 w-3\" />;\n      case \"research\":\n      default:\n        return <BookOpen className=\"h-3 w-3\" />;\n    }\n  };\n\n  const getDocumentTypeColor = (type: string) => {\n    switch (type) {\n      case \"screenplay\":\n        return \"default\";\n      case \"collection\":\n        return \"secondary\";\n      case \"research\":\n      default:\n        return \"outline\";\n    }\n  };\n\n  const getPermissionIcon = (permission: string) => {\n    switch (permission) {\n      case \"owner\":\n        return <Crown className=\"h-3 w-3\" />;\n      case \"write\":\n        return <Edit className=\"h-3 w-3\" />;\n      case \"read\":\n        return <Eye className=\"h-3 w-3\" />;\n      default:\n        return <Lock className=\"h-3 w-3\" />;\n    }\n  };\n\n  const getPermissionColor = (permission: string) => {\n    switch (permission) {\n      case \"owner\":\n        return \"default\";\n      case \"write\":\n        return \"secondary\";\n      case \"read\":\n        return \"outline\";\n      default:\n        return \"destructive\";\n    }\n  };\n\n  if (!documents) {\n    return (\n      <div className=\"w-72 h-screen bg-white border-r border-gray-200 hidden md:flex md:flex-col\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"w-72 h-screen bg-white border-r border-gray-200 hidden md:flex md:flex-col\">\n      {/* Header */}\n      <div className=\"p-6 border-b border-gray-200 bg-white\">\n        <div className=\"flex items-center justify-between\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">Documents</h2>\n          {canCreate && (\n            <Button\n              onClick={handleShowCreateDialog}\n              disabled={isCreating}\n              size=\"sm\"\n              className=\"gap-2\"\n            >\n              <Plus className=\"h-4 w-4\" />\n              \n            </Button>\n          )}\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"flex-1 overflow-y-auto p-6\">\n        {!canCreate && (\n          <Card className=\"border-amber-200 bg-amber-50 mb-4\">\n            <CardContent className=\"p-3\">\n              <div className=\"flex items-start gap-2 text-amber-800\">\n                <AlertCircle className=\"h-4 w-4 mt-0.5 flex-shrink-0\" />\n                <div>\n                  <p className=\"font-medium text-sm\">Read-Only Access</p>\n                  <p className=\"text-xs\">\n                    You can view documents but cannot create or modify them.\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        {documents.length === 0 ? (\n          <div className=\"text-center py-12 text-gray-500\">\n            <FileText size={48} className=\"mx-auto mb-4 opacity-50\" />\n            <p className=\"text-sm\">No documents in this script</p>\n            {canCreate && (\n              <p className=\"text-xs mt-2\">Add your first document to get started</p>\n            )}\n          </div>\n        ) : (\n          <div className=\"grid gap-4\">\n            {documents.map((document) => {\n              const isSelected = selectedDocumentId === document._id;\n              \n              return (\n                <a\n                  key={document._id}\n                  href={getScriptDocumentUrl(scriptId, document._id)}\n                  onClick={(e) => {\n                    e.preventDefault();\n                    onSelectDocument(scriptId, document._id);\n                  }}\n                  className={`block p-4 rounded-lg border transition-all duration-200 group hover:shadow-md ${\n                    isSelected\n                      ? 'border-blue-500 bg-blue-50 shadow-sm'\n                      : 'border-gray-200 bg-white hover:border-gray-300'\n                  }`}\n                >\n                  <div className=\"flex items-start justify-between mb-3\">\n                    <h3 className={`text-sm font-medium line-clamp-2 flex-1 mr-2 ${\n                      isSelected ? 'text-blue-900' : 'text-gray-900'\n                    }`}>\n                      {document.title}\n                    </h3>\n                    {document.permission === \"owner\" && (\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        onClick={(e) => {\n                          e.preventDefault();\n                          e.stopPropagation();\n                          setDocumentToDelete(document._id);\n                          setShowDeleteDialog(true);\n                        }}\n                        className=\"opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0 text-gray-400 hover:text-red-600\"\n                      >\n                        <Trash2 className=\"h-3 w-3\" />\n                      </Button>\n                    )}\n                  </div>\n\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <Badge\n                      variant={getDocumentTypeColor(document.documentType) as any}\n                      className=\"gap-1 text-xs\"\n                    >\n                      {getDocumentTypeIcon(document.documentType)}\n                      {document.documentType}\n                    </Badge>\n                    <Badge\n                      variant={getPermissionColor(document.permission) as any}\n                      className=\"gap-1 text-xs\"\n                    >\n                      {getPermissionIcon(document.permission)}\n                      {document.permission}\n                    </Badge>\n                  </div>\n\n                  <div className=\"text-xs text-muted-foreground\">\n                    {new Date(document._creationTime).toLocaleDateString()}\n                  </div>\n                </a>\n              );\n            })}\n          </div>\n        )}\n      </div>\n\n      {/* Create Document Dialog */}\n      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>Add New Document</DialogTitle>\n          </DialogHeader>\n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"document-title\" className=\"text-sm font-medium\">\n                Title *\n              </label>\n              <Input\n                id=\"document-title\"\n                value={newDocumentTitle}\n                onChange={(e) => setNewDocumentTitle(e.target.value)}\n                placeholder=\"Enter document title...\"\n                className=\"mt-1\"\n              />\n            </div>\n            <div>\n              <label htmlFor=\"document-type\" className=\"text-sm font-medium\">\n                Document Type *\n              </label>\n              <Select value={newDocumentType} onValueChange={(value: DocumentType) => setNewDocumentType(value)}>\n                <SelectTrigger className=\"mt-1\">\n                  <SelectValue placeholder=\"Select document type\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"research\">\n                    <div className=\"flex items-center gap-2\">\n                      <BookOpen className=\"h-4 w-4\" />\n                      Research - Rich text editor\n                    </div>\n                  </SelectItem>\n                  <SelectItem value=\"screenplay\">\n                    <div className=\"flex items-center gap-2\">\n                      <Film className=\"h-4 w-4\" />\n                      Screenplay - Industry format\n                    </div>\n                  </SelectItem>\n                  <SelectItem value=\"collection\">\n                    <div className=\"flex items-center gap-2\">\n                      <FolderOpen className=\"h-4 w-4\" />\n                      Collection - Note organizer\n                    </div>\n                  </SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowCreateDialog(false)}\n              disabled={isCreating}\n            >\n              Cancel\n            </Button>\n            <Button onClick={handleCreateDocument} disabled={isCreating}>\n              {isCreating ? \"Creating...\" : \"Create Document\"}\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* Delete Document Dialog */}\n      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>Delete Document</DialogTitle>\n          </DialogHeader>\n          <p className=\"text-sm text-gray-600\">\n            Are you sure you want to delete this document? This action cannot be undone.\n          </p>\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowDeleteDialog(false)}\n              disabled={isDeleting}\n            >\n              Cancel\n            </Button>\n            <Button\n              variant=\"destructive\"\n              onClick={handleDeleteDocument}\n              disabled={isDeleting}\n            >\n              {isDeleting ? \"Deleting...\" : \"Delete Document\"}\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AA1BA;;;;;;;;;;;;;;;AAsCO,SAAS,mBAAmB,EACjC,QAAQ,EACR,gBAAgB,EAChB,kBAAkB,EAClB,qBAAqB,EACG;IACxB,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,SAAS,CAAC,kBAAkB,EAAE;QAAE;IAAS;IACxE,MAAM,mBAAmB,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,aAAa,CAAC,mBAAmB,EAAE;QAAE;IAAS;IACpF,MAAM,iBAAiB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,SAAS,CAAC,cAAc;IAC/D,MAAM,iBAAiB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,SAAS,CAAC,cAAc;IAC/D,MAAM,EAAE,oBAAoB,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;IAE7C,oDAAoD;IACpD,MAAM,YAAY,kBAAkB,eAAe,WAAW,kBAAkB,eAAe;IAE/F,0BAA0B;IAC1B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,0BAA0B;IAC1B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACjF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAEzB,oDAAoD;IACpD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,aAAa,uBAAuB;YACtC,sBAAsB,UAAU,MAAM;QACxC;IACF,GAAG;QAAC;QAAW;KAAsB;IAErC,MAAM,yBAAyB;QAC7B,oBAAoB;QACpB,mBAAmB;QACnB,oBAAoB;IACtB;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,iBAAiB,IAAI,IAAI;YAC5B,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,cAAc;QACd,IAAI;YACF,MAAM,aAAa,MAAM,eAAe;gBACtC,OAAO,iBAAiB,IAAI;gBAC5B;gBACA,cAAc;YAChB;YAEA,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,oBAAoB;YACpB,oBAAoB;YACpB,mBAAmB;YAEnB,+BAA+B;YAC/B,iBAAiB,UAAU;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,kBAAkB;QAEvB,cAAc;QACd,IAAI;YACF,MAAM,eAAe;gBAAE,IAAI;YAAiB;YAE5C,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,oBAAoB;YACpB,oBAAoB;YAEpB,wDAAwD;YACxD,IAAI,uBAAuB,kBAAkB;gBAC3C,iDAAiD;gBACjD,IAAI,aAAa,UAAU,MAAM,GAAG,GAAG;oBACrC,MAAM,qBAAqB,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK;oBAC3D,IAAI,mBAAmB,MAAM,GAAG,GAAG;wBACjC,iBAAiB,UAAU,kBAAkB,CAAC,EAAE,CAAC,GAAG;oBACtD;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,kNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;YACL;gBACE,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,2MAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,WAAW;QACd,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;wBACnD,2BACC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU;4BACV,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAQxB,8OAAC;gBAAI,WAAU;;oBACZ,CAAC,2BACA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAShC,UAAU,MAAM,KAAK,kBACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC9B,8OAAC;gCAAE,WAAU;0CAAU;;;;;;4BACtB,2BACC,8OAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;6CAIhC,8OAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC;4BACd,MAAM,aAAa,uBAAuB,SAAS,GAAG;4BAEtD,qBACE,8OAAC;gCAEC,MAAM,qBAAqB,UAAU,SAAS,GAAG;gCACjD,SAAS,CAAC;oCACR,EAAE,cAAc;oCAChB,iBAAiB,UAAU,SAAS,GAAG;gCACzC;gCACA,WAAW,CAAC,8EAA8E,EACxF,aACI,yCACA,kDACJ;;kDAEF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAW,CAAC,6CAA6C,EAC3D,aAAa,kBAAkB,iBAC/B;0DACC,SAAS,KAAK;;;;;;4CAEhB,SAAS,UAAU,KAAK,yBACvB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,EAAE,eAAe;oDACjB,oBAAoB,SAAS,GAAG;oDAChC,oBAAoB;gDACtB;gDACA,WAAU;0DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAKxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAS,qBAAqB,SAAS,YAAY;gDACnD,WAAU;;oDAET,oBAAoB,SAAS,YAAY;oDACzC,SAAS,YAAY;;;;;;;0DAExB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAS,mBAAmB,SAAS,UAAU;gDAC/C,WAAU;;oDAET,kBAAkB,SAAS,UAAU;oDACrC,SAAS,UAAU;;;;;;;;;;;;;kDAIxB,8OAAC;wCAAI,WAAU;kDACZ,IAAI,KAAK,SAAS,aAAa,EAAE,kBAAkB;;;;;;;+BArDjD,SAAS,GAAG;;;;;wBAyDvB;;;;;;;;;;;;0BAMN,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;;sCACZ,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;0CAAC;;;;;;;;;;;sCAEf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAiB,WAAU;sDAAsB;;;;;;sDAGhE,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4CACnD,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAGd,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAgB,WAAU;sDAAsB;;;;;;sDAG/D,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAiB,eAAe,CAAC,QAAwB,mBAAmB;;8DACzF,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAChB,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,8MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAY;;;;;;;;;;;;sEAIpC,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAChB,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAY;;;;;;;;;;;;sEAIhC,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAChB,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;oEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ9C,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,oBAAoB;oCACnC,UAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAsB,UAAU;8CAC9C,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAOtC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;;sCACZ,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;0CAAC;;;;;;;;;;;sCAEf,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;sCAGrC,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,oBAAoB;oCACnC,UAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;8CAET,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C", "debugId": null}}, {"offset": {"line": 3177, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/lib/userColors.ts"], "sourcesContent": ["/**\n * User color utilities for collaborative features\n * Provides consistent color assignment for cursors, selections, and avatars\n */\n\nexport interface UserColor {\n  bg: string;\n  text: string;\n  border: string;\n  selection: string;\n  cursor: string;\n  rgb: string;\n  hsl: string;\n}\n\n// Predefined color palette for users\nconst USER_COLORS: UserColor[] = [\n  {\n    bg: \"bg-blue-500\",\n    text: \"text-white\",\n    border: \"border-blue-500\",\n    selection: \"bg-blue-200/40\",\n    cursor: \"bg-blue-500\",\n    rgb: \"59, 130, 246\",\n    hsl: \"217, 91%, 60%\"\n  },\n  {\n    bg: \"bg-green-500\",\n    text: \"text-white\", \n    border: \"border-green-500\",\n    selection: \"bg-green-200/40\",\n    cursor: \"bg-green-500\",\n    rgb: \"34, 197, 94\",\n    hsl: \"142, 71%, 45%\"\n  },\n  {\n    bg: \"bg-purple-500\",\n    text: \"text-white\",\n    border: \"border-purple-500\", \n    selection: \"bg-purple-200/40\",\n    cursor: \"bg-purple-500\",\n    rgb: \"168, 85, 247\",\n    hsl: \"262, 83%, 58%\"\n  },\n  {\n    bg: \"bg-pink-500\",\n    text: \"text-white\",\n    border: \"border-pink-500\",\n    selection: \"bg-pink-200/40\", \n    cursor: \"bg-pink-500\",\n    rgb: \"236, 72, 153\",\n    hsl: \"330, 81%, 60%\"\n  },\n  {\n    bg: \"bg-yellow-500\",\n    text: \"text-black\",\n    border: \"border-yellow-500\",\n    selection: \"bg-yellow-200/40\",\n    cursor: \"bg-yellow-500\", \n    rgb: \"234, 179, 8\",\n    hsl: \"48, 96%, 47%\"\n  },\n  {\n    bg: \"bg-indigo-500\",\n    text: \"text-white\",\n    border: \"border-indigo-500\",\n    selection: \"bg-indigo-200/40\",\n    cursor: \"bg-indigo-500\",\n    rgb: \"99, 102, 241\",\n    hsl: \"239, 84%, 67%\"\n  },\n  {\n    bg: \"bg-red-500\", \n    text: \"text-white\",\n    border: \"border-red-500\",\n    selection: \"bg-red-200/40\",\n    cursor: \"bg-red-500\",\n    rgb: \"239, 68, 68\",\n    hsl: \"0, 84%, 60%\"\n  },\n  {\n    bg: \"bg-teal-500\",\n    text: \"text-white\",\n    border: \"border-teal-500\",\n    selection: \"bg-teal-200/40\",\n    cursor: \"bg-teal-500\",\n    rgb: \"20, 184, 166\", \n    hsl: \"173, 80%, 40%\"\n  }\n];\n\n/**\n * Get a consistent color for a user based on their ID\n */\nexport function getUserColor(userId: string): UserColor {\n  // Create a simple hash from the user ID\n  let hash = 0;\n  for (let i = 0; i < userId.length; i++) {\n    const char = userId.charCodeAt(i);\n    hash = ((hash << 5) - hash) + char;\n    hash = hash & hash; // Convert to 32-bit integer\n  }\n  \n  // Use absolute value and modulo to get a consistent index\n  const index = Math.abs(hash) % USER_COLORS.length;\n  return USER_COLORS[index];\n}\n\n/**\n * Get all available user colors\n */\nexport function getAllUserColors(): UserColor[] {\n  return USER_COLORS;\n}\n\n/**\n * Get color by index (for compatibility with existing PresenceIndicator)\n */\nexport function getUserColorByIndex(index: number): UserColor {\n  return USER_COLORS[index % USER_COLORS.length];\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAYD,qCAAqC;AACrC,MAAM,cAA2B;IAC/B;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,KAAK;QACL,KAAK;IACP;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,KAAK;QACL,KAAK;IACP;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,KAAK;QACL,KAAK;IACP;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,KAAK;QACL,KAAK;IACP;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,KAAK;QACL,KAAK;IACP;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,KAAK;QACL,KAAK;IACP;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,KAAK;QACL,KAAK;IACP;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,KAAK;QACL,KAAK;IACP;CACD;AAKM,SAAS,aAAa,MAAc;IACzC,wCAAwC;IACxC,IAAI,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACtC,MAAM,OAAO,OAAO,UAAU,CAAC;QAC/B,OAAO,AAAC,CAAC,QAAQ,CAAC,IAAI,OAAQ;QAC9B,OAAO,OAAO,MAAM,4BAA4B;IAClD;IAEA,0DAA0D;IAC1D,MAAM,QAAQ,KAAK,GAAG,CAAC,QAAQ,YAAY,MAAM;IACjD,OAAO,WAAW,CAAC,MAAM;AAC3B;AAKO,SAAS;IACd,OAAO;AACT;AAKO,SAAS,oBAAoB,KAAa;IAC/C,OAAO,WAAW,CAAC,QAAQ,YAAY,MAAM,CAAC;AAChD", "debugId": null}}, {"offset": {"line": 3284, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/lib/collaborativeCursors.ts"], "sourcesContent": ["/**\n * Collaborative cursor and selection plugin for ProseMirror\n * Handles real-time cursor positioning and selection highlighting\n */\n\nimport { Plugin, Plugin<PERSON>ey } from \"prosemirror-state\";\nimport { Decoration, DecorationSet } from \"prosemirror-view\";\nimport { getUserColor } from \"./userColors\";\n\nexport interface CursorData {\n  position: number;\n  selection?: {\n    from: number;\n    to: number;\n  };\n  timestamp: number;\n}\n\nexport interface CollaboratorCursor {\n  userId: string;\n  sessionId: string;\n  name: string;\n  image?: string;\n  cursor: CursorData;\n}\n\nexport interface CollaborativeCursorsState {\n  cursors: CollaboratorCursor[];\n  decorations: DecorationSet;\n  lastCursorHash: string; // Track cursor changes to prevent unnecessary updates\n}\n\nexport const collaborativeCursorsKey = new PluginKey<CollaborativeCursorsState>(\"collaborative-cursors\");\n\n/**\n * Create a cursor decoration element\n */\nfunction createCursorElement(collaborator: CollaboratorCursor): HTMLElement {\n  const color = getUserColor(collaborator.userId);\n  \n  const cursor = document.createElement(\"div\");\n  cursor.className = \"collaborative-cursor\";\n  cursor.style.cssText = `\n    position: absolute;\n    width: 2px;\n    height: 1.2em;\n    background-color: rgb(${color.rgb});\n    pointer-events: none;\n    z-index: 10;\n    animation: cursor-blink 1s infinite;\n  `;\n\n  // Create cursor label\n  const label = document.createElement(\"div\");\n  label.className = \"collaborative-cursor-label\";\n  label.textContent = collaborator.name;\n  label.style.cssText = `\n    position: absolute;\n    top: -24px;\n    left: 0;\n    background-color: rgb(${color.rgb});\n    color: white;\n    padding: 2px 6px;\n    border-radius: 4px;\n    font-size: 12px;\n    font-weight: 500;\n    white-space: nowrap;\n    opacity: 0;\n    transition: opacity 0.2s ease;\n    pointer-events: none;\n    z-index: 11;\n  `;\n\n  // Show label on hover\n  cursor.addEventListener(\"mouseenter\", () => {\n    label.style.opacity = \"1\";\n  });\n  \n  cursor.addEventListener(\"mouseleave\", () => {\n    label.style.opacity = \"0\";\n  });\n\n  cursor.appendChild(label);\n  return cursor;\n}\n\n/**\n * Create a selection decoration\n */\nfunction createSelectionDecoration(from: number, to: number, collaborator: CollaboratorCursor) {\n  const color = getUserColor(collaborator.userId);\n\n  return Decoration.inline(from, to, {\n    class: \"collaborative-selection\",\n    style: `background-color: rgba(${color.rgb}, 0.3); border-radius: 2px;`,\n    title: `${collaborator.name}'s selection`,\n    // Add stable key to prevent unnecessary DOM recreations\n    key: `selection-${collaborator.userId}`,\n  });\n}\n\n/**\n * Create a cursor decoration\n */\nfunction createCursorDecoration(position: number, collaborator: CollaboratorCursor) {\n  const cursorElement = createCursorElement(collaborator);\n\n  return Decoration.widget(position, cursorElement, {\n    side: 1,\n    // Use stable key based only on userId to prevent recreation\n    key: `cursor-${collaborator.userId}`,\n  });\n}\n\n/**\n * Generate a hash of cursor data to detect changes\n */\nfunction generateCursorHash(cursors: CollaboratorCursor[]): string {\n  return cursors\n    .map(c => `${c.userId}:${c.cursor.position}:${c.cursor.selection?.from || ''}:${c.cursor.selection?.to || ''}`)\n    .sort()\n    .join('|');\n}\n\n/**\n * Update decorations based on current collaborators with optimization\n */\nfunction updateDecorations(\n  doc: any,\n  cursors: CollaboratorCursor[],\n  currentDecorations: DecorationSet,\n  forceUpdate: boolean = false\n): DecorationSet {\n  // Generate hash to check if cursors actually changed\n  const newHash = generateCursorHash(cursors);\n\n  // If cursors haven't changed and we're not forcing an update, return existing decorations\n  if (!forceUpdate && currentDecorations && currentDecorations.find().length > 0) {\n    // We'll check this in the plugin state\n  }\n\n  const decorations: Decoration[] = [];\n\n  cursors.forEach((collaborator) => {\n    const { cursor } = collaborator;\n\n    // Add selection decoration if there's a selection\n    if (cursor.selection && cursor.selection.from !== cursor.selection.to) {\n      const { from, to } = cursor.selection;\n\n      // Ensure positions are within document bounds\n      if (from >= 0 && to <= doc.content.size && from < to) {\n        decorations.push(createSelectionDecoration(from, to, collaborator));\n      }\n    }\n\n    // Add cursor decoration (only if no selection or selection is empty)\n    if (cursor.position >= 0 && cursor.position <= doc.content.size) {\n      // Only show cursor if there's no selection\n      if (!cursor.selection || cursor.selection.from === cursor.selection.to) {\n        decorations.push(createCursorDecoration(cursor.position, collaborator));\n      }\n    }\n  });\n\n  return DecorationSet.create(doc, decorations);\n}\n\n/**\n * Create the collaborative cursors plugin\n */\nexport function createCollaborativeCursorsPlugin() {\n  return new Plugin<CollaborativeCursorsState>({\n    key: collaborativeCursorsKey,\n    \n    state: {\n      init() {\n        return {\n          cursors: [],\n          decorations: DecorationSet.empty,\n          lastCursorHash: '',\n        };\n      },\n\n      apply(tr, state, oldState, newState) {\n        let { cursors, decorations, lastCursorHash } = state;\n\n        // Check for cursor updates from meta\n        const cursorUpdate = tr.getMeta(collaborativeCursorsKey);\n        if (cursorUpdate) {\n          cursors = cursorUpdate.cursors || cursors;\n        }\n\n        // Generate new hash to check if cursors actually changed\n        const newCursorHash = generateCursorHash(cursors);\n        const cursorsChanged = newCursorHash !== lastCursorHash;\n\n        // Update decorations only if document changed or cursors actually changed\n        if (tr.docChanged || cursorsChanged) {\n          decorations = updateDecorations(\n            newState.doc,\n            cursors,\n            decorations,\n            cursorsChanged || tr.docChanged\n          );\n          lastCursorHash = newCursorHash;\n        } else {\n          // Map existing decorations through the transaction\n          decorations = decorations.map(tr.mapping, tr.doc);\n        }\n\n        return {\n          cursors,\n          decorations,\n          lastCursorHash,\n        };\n      },\n    },\n    \n    props: {\n      decorations(state) {\n        return this.getState(state)?.decorations;\n      },\n    },\n  });\n}\n\n/**\n * Update cursors in the plugin state\n */\nexport function updateCursors(view: any, cursors: CollaboratorCursor[]) {\n  const tr = view.state.tr;\n  tr.setMeta(collaborativeCursorsKey, { cursors });\n  view.dispatch(tr);\n}\n\n/**\n * Get current cursor position and selection from editor state\n */\nexport function getCurrentCursorData(state: any): CursorData {\n  const { selection } = state;\n  \n  return {\n    position: selection.head,\n    selection: selection.from !== selection.to ? {\n      from: selection.from,\n      to: selection.to,\n    } : undefined,\n    timestamp: Date.now(),\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAED;AACA;AACA;;;;AAyBO,MAAM,0BAA0B,IAAI,qJAAA,CAAA,YAAS,CAA4B;AAEhF;;CAEC,GACD,SAAS,oBAAoB,YAAgC;IAC3D,MAAM,QAAQ,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,aAAa,MAAM;IAE9C,MAAM,SAAS,SAAS,aAAa,CAAC;IACtC,OAAO,SAAS,GAAG;IACnB,OAAO,KAAK,CAAC,OAAO,GAAG,CAAC;;;;0BAIA,EAAE,MAAM,GAAG,CAAC;;;;EAIpC,CAAC;IAED,sBAAsB;IACtB,MAAM,QAAQ,SAAS,aAAa,CAAC;IACrC,MAAM,SAAS,GAAG;IAClB,MAAM,WAAW,GAAG,aAAa,IAAI;IACrC,MAAM,KAAK,CAAC,OAAO,GAAG,CAAC;;;;0BAIC,EAAE,MAAM,GAAG,CAAC;;;;;;;;;;;EAWpC,CAAC;IAED,sBAAsB;IACtB,OAAO,gBAAgB,CAAC,cAAc;QACpC,MAAM,KAAK,CAAC,OAAO,GAAG;IACxB;IAEA,OAAO,gBAAgB,CAAC,cAAc;QACpC,MAAM,KAAK,CAAC,OAAO,GAAG;IACxB;IAEA,OAAO,WAAW,CAAC;IACnB,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,0BAA0B,IAAY,EAAE,EAAU,EAAE,YAAgC;IAC3F,MAAM,QAAQ,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,aAAa,MAAM;IAE9C,OAAO,oJAAA,CAAA,aAAU,CAAC,MAAM,CAAC,MAAM,IAAI;QACjC,OAAO;QACP,OAAO,CAAC,uBAAuB,EAAE,MAAM,GAAG,CAAC,2BAA2B,CAAC;QACvE,OAAO,GAAG,aAAa,IAAI,CAAC,YAAY,CAAC;QACzC,wDAAwD;QACxD,KAAK,CAAC,UAAU,EAAE,aAAa,MAAM,EAAE;IACzC;AACF;AAEA;;CAEC,GACD,SAAS,uBAAuB,QAAgB,EAAE,YAAgC;IAChF,MAAM,gBAAgB,oBAAoB;IAE1C,OAAO,oJAAA,CAAA,aAAU,CAAC,MAAM,CAAC,UAAU,eAAe;QAChD,MAAM;QACN,4DAA4D;QAC5D,KAAK,CAAC,OAAO,EAAE,aAAa,MAAM,EAAE;IACtC;AACF;AAEA;;CAEC,GACD,SAAS,mBAAmB,OAA6B;IACvD,OAAO,QACJ,GAAG,CAAC,CAAA,IAAK,GAAG,EAAE,MAAM,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,SAAS,EAAE,QAAQ,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,IAAI,EAC7G,IAAI,GACJ,IAAI,CAAC;AACV;AAEA;;CAEC,GACD,SAAS,kBACP,GAAQ,EACR,OAA6B,EAC7B,kBAAiC,EACjC,cAAuB,KAAK;IAE5B,qDAAqD;IACrD,MAAM,UAAU,mBAAmB;IAEnC,0FAA0F;IAC1F,IAAI,CAAC,eAAe,sBAAsB,mBAAmB,IAAI,GAAG,MAAM,GAAG,GAAG;IAC9E,uCAAuC;IACzC;IAEA,MAAM,cAA4B,EAAE;IAEpC,QAAQ,OAAO,CAAC,CAAC;QACf,MAAM,EAAE,MAAM,EAAE,GAAG;QAEnB,kDAAkD;QAClD,IAAI,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,IAAI,KAAK,OAAO,SAAS,CAAC,EAAE,EAAE;YACrE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,OAAO,SAAS;YAErC,8CAA8C;YAC9C,IAAI,QAAQ,KAAK,MAAM,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,IAAI;gBACpD,YAAY,IAAI,CAAC,0BAA0B,MAAM,IAAI;YACvD;QACF;QAEA,qEAAqE;QACrE,IAAI,OAAO,QAAQ,IAAI,KAAK,OAAO,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI,EAAE;YAC/D,2CAA2C;YAC3C,IAAI,CAAC,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,IAAI,KAAK,OAAO,SAAS,CAAC,EAAE,EAAE;gBACtE,YAAY,IAAI,CAAC,uBAAuB,OAAO,QAAQ,EAAE;YAC3D;QACF;IACF;IAEA,OAAO,oJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,KAAK;AACnC;AAKO,SAAS;IACd,OAAO,IAAI,qJAAA,CAAA,SAAM,CAA4B;QAC3C,KAAK;QAEL,OAAO;YACL;gBACE,OAAO;oBACL,SAAS,EAAE;oBACX,aAAa,oJAAA,CAAA,gBAAa,CAAC,KAAK;oBAChC,gBAAgB;gBAClB;YACF;YAEA,OAAM,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;gBACjC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG;gBAE/C,qCAAqC;gBACrC,MAAM,eAAe,GAAG,OAAO,CAAC;gBAChC,IAAI,cAAc;oBAChB,UAAU,aAAa,OAAO,IAAI;gBACpC;gBAEA,yDAAyD;gBACzD,MAAM,gBAAgB,mBAAmB;gBACzC,MAAM,iBAAiB,kBAAkB;gBAEzC,0EAA0E;gBAC1E,IAAI,GAAG,UAAU,IAAI,gBAAgB;oBACnC,cAAc,kBACZ,SAAS,GAAG,EACZ,SACA,aACA,kBAAkB,GAAG,UAAU;oBAEjC,iBAAiB;gBACnB,OAAO;oBACL,mDAAmD;oBACnD,cAAc,YAAY,GAAG,CAAC,GAAG,OAAO,EAAE,GAAG,GAAG;gBAClD;gBAEA,OAAO;oBACL;oBACA;oBACA;gBACF;YACF;QACF;QAEA,OAAO;YACL,aAAY,KAAK;gBACf,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ;YAC/B;QACF;IACF;AACF;AAKO,SAAS,cAAc,IAAS,EAAE,OAA6B;IACpE,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE;IACxB,GAAG,OAAO,CAAC,yBAAyB;QAAE;IAAQ;IAC9C,KAAK,QAAQ,CAAC;AAChB;AAKO,SAAS,qBAAqB,KAAU;IAC7C,MAAM,EAAE,SAAS,EAAE,GAAG;IAEtB,OAAO;QACL,UAAU,UAAU,IAAI;QACxB,WAAW,UAAU,IAAI,KAAK,UAAU,EAAE,GAAG;YAC3C,MAAM,UAAU,IAAI;YACpB,IAAI,UAAU,EAAE;QAClB,IAAI;QACJ,WAAW,KAAK,GAAG;IACrB;AACF", "debugId": null}}, {"offset": {"line": 3469, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/hooks/useCollaborativeCursors.ts"], "sourcesContent": ["'use client';\n\n/**\n * React hook for managing collaborative cursors and selections\n */\n\nimport { useEffect, useRef, useCallback } from \"react\";\nimport { useQuery, useMutation } from \"convex/react\";\nimport usePresence from \"@convex-dev/presence/react\";\nimport { api } from \"../../convex/_generated/api\";\nimport { Id } from \"../../convex/_generated/dataModel\";\nimport {\n  updateCursors,\n  getCurrentCursorData,\n  type CollaboratorCursor,\n  type CursorData\n} from \"../lib/collaborativeCursors\";\n\ninterface UseCollaborativeCursorsProps {\n  documentId: Id<\"documents\">;\n  editor: any; // BlockNote/ProseMirror editor instance\n  isReadOnly?: boolean;\n}\n\nexport function useCollaborativeCursors({\n  documentId,\n  editor,\n  isReadOnly = false\n}: UseCollaborativeCursorsProps) {\n  const userId = useQuery(api.presence.getUserId);\n  const updateCursorMutation = useMutation(api.presence.updateCursor);\n\n  // Use presence system for the document room (for general presence)\n  const roomId = `document-${documentId}`;\n  const presenceState = usePresence(api.presence, roomId, userId || \"\");\n\n  // Get cursor data directly from the cursors table\n  const cursors = useQuery(api.presence.getCursors, { documentId });\n  \n  const lastCursorUpdate = useRef<number>(0);\n  const updateThrottle = useRef<NodeJS.Timeout | null>(null);\n  const lastCursorData = useRef<CursorData | null>(null);\n  const pendingUpdate = useRef<boolean>(false);\n\n  // Improved throttled cursor update function with debouncing and deduplication\n  const throttledUpdateCursor = useCallback((cursorData: CursorData) => {\n    if (isReadOnly || !userId) return;\n\n    // Check if cursor data actually changed to prevent unnecessary updates\n    const lastData = lastCursorData.current;\n    if (lastData &&\n        lastData.position === cursorData.position &&\n        lastData.selection?.from === cursorData.selection?.from &&\n        lastData.selection?.to === cursorData.selection?.to) {\n      return; // No change, skip update\n    }\n\n    lastCursorData.current = cursorData;\n\n    const now = Date.now();\n    const timeSinceLastUpdate = now - lastCursorUpdate.current;\n\n    // Clear any pending timeout\n    if (updateThrottle.current) {\n      clearTimeout(updateThrottle.current);\n      updateThrottle.current = null;\n    }\n\n    // If enough time has passed and no update is pending, send immediately\n    if (timeSinceLastUpdate >= 150 && !pendingUpdate.current) {\n      pendingUpdate.current = true;\n      updateCursorMutation({\n        documentId,\n        cursorData,\n      }).catch((error) => {\n        console.warn('Failed to update cursor position:', error);\n      }).finally(() => {\n        pendingUpdate.current = false;\n        lastCursorUpdate.current = Date.now();\n      });\n      return;\n    }\n\n    // Otherwise, schedule a debounced update\n    updateThrottle.current = setTimeout(() => {\n      if (!pendingUpdate.current && lastCursorData.current) {\n        pendingUpdate.current = true;\n        updateCursorMutation({\n          documentId,\n          cursorData: lastCursorData.current,\n        }).catch((error) => {\n          console.warn('Failed to update cursor position (debounced):', error);\n        }).finally(() => {\n          pendingUpdate.current = false;\n          lastCursorUpdate.current = Date.now();\n        });\n      }\n      updateThrottle.current = null;\n    }, 150); // Increased to 150ms for better stability\n  }, [documentId, updateCursorMutation, isReadOnly, userId]);\n\n  // Set up editor event listeners\n  useEffect(() => {\n    if (!editor || isReadOnly) return;\n\n    const handleSelectionChange = () => {\n      if (!editor.prosemirrorView) return;\n      \n      const cursorData = getCurrentCursorData(editor.prosemirrorView.state);\n      throttledUpdateCursor(cursorData);\n    };\n\n    const handleTransaction = () => {\n      handleSelectionChange();\n    };\n\n    // Listen to editor changes\n    if (editor.prosemirrorView) {\n      const view = editor.prosemirrorView;\n      \n      // Add transaction listener\n      const originalDispatch = view.dispatch;\n      view.dispatch = (tr: any) => {\n        originalDispatch.call(view, tr);\n        handleTransaction();\n      };\n\n      // Listen to focus/blur events\n      view.dom.addEventListener(\"focus\", handleSelectionChange);\n      view.dom.addEventListener(\"blur\", handleSelectionChange);\n\n      return () => {\n        view.dom.removeEventListener(\"focus\", handleSelectionChange);\n        view.dom.removeEventListener(\"blur\", handleSelectionChange);\n        view.dispatch = originalDispatch;\n      };\n    }\n  }, [editor, throttledUpdateCursor, isReadOnly]);\n\n  // Update cursor decorations when cursors change\n  useEffect(() => {\n    if (!editor?.prosemirrorView || !cursors) return;\n\n    // Filter out current user's cursor\n    const otherCursors = cursors.filter((cursor: CollaboratorCursor) => \n      cursor.userId !== userId\n    );\n\n    updateCursors(editor.prosemirrorView, otherCursors);\n  }, [editor, cursors, userId]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      if (updateThrottle.current) {\n        clearTimeout(updateThrottle.current);\n      }\n    };\n  }, []);\n\n  return {\n    cursors: cursors || [],\n    isConnected: !!presenceState?.roomToken,\n    collaboratorCount: cursors?.length || 0,\n    presenceUsers: presenceState || [],\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;;CAEC,GAED;AACA;AAAA;AACA;AACA;AAEA;AAXA;;;;;;AAwBO,SAAS,wBAAwB,EACtC,UAAU,EACV,MAAM,EACN,aAAa,KAAK,EACW;IAC7B,MAAM,SAAS,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,SAAS;IAC9C,MAAM,uBAAuB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,YAAY;IAElE,mEAAmE;IACnE,MAAM,SAAS,CAAC,SAAS,EAAE,YAAY;IACvC,MAAM,gBAAgB,CAAA,GAAA,4KAAA,CAAA,UAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,EAAE,QAAQ,UAAU;IAElE,kDAAkD;IAClD,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,UAAU,EAAE;QAAE;IAAW;IAE/D,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAU;IACxC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IACrD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IACjD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAW;IAEtC,8EAA8E;IAC9E,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,IAAI,cAAc,CAAC,QAAQ;QAE3B,uEAAuE;QACvE,MAAM,WAAW,eAAe,OAAO;QACvC,IAAI,YACA,SAAS,QAAQ,KAAK,WAAW,QAAQ,IACzC,SAAS,SAAS,EAAE,SAAS,WAAW,SAAS,EAAE,QACnD,SAAS,SAAS,EAAE,OAAO,WAAW,SAAS,EAAE,IAAI;YACvD,QAAQ,yBAAyB;QACnC;QAEA,eAAe,OAAO,GAAG;QAEzB,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,sBAAsB,MAAM,iBAAiB,OAAO;QAE1D,4BAA4B;QAC5B,IAAI,eAAe,OAAO,EAAE;YAC1B,aAAa,eAAe,OAAO;YACnC,eAAe,OAAO,GAAG;QAC3B;QAEA,uEAAuE;QACvE,IAAI,uBAAuB,OAAO,CAAC,cAAc,OAAO,EAAE;YACxD,cAAc,OAAO,GAAG;YACxB,qBAAqB;gBACnB;gBACA;YACF,GAAG,KAAK,CAAC,CAAC;gBACR,QAAQ,IAAI,CAAC,qCAAqC;YACpD,GAAG,OAAO,CAAC;gBACT,cAAc,OAAO,GAAG;gBACxB,iBAAiB,OAAO,GAAG,KAAK,GAAG;YACrC;YACA;QACF;QAEA,yCAAyC;QACzC,eAAe,OAAO,GAAG,WAAW;YAClC,IAAI,CAAC,cAAc,OAAO,IAAI,eAAe,OAAO,EAAE;gBACpD,cAAc,OAAO,GAAG;gBACxB,qBAAqB;oBACnB;oBACA,YAAY,eAAe,OAAO;gBACpC,GAAG,KAAK,CAAC,CAAC;oBACR,QAAQ,IAAI,CAAC,iDAAiD;gBAChE,GAAG,OAAO,CAAC;oBACT,cAAc,OAAO,GAAG;oBACxB,iBAAiB,OAAO,GAAG,KAAK,GAAG;gBACrC;YACF;YACA,eAAe,OAAO,GAAG;QAC3B,GAAG,MAAM,0CAA0C;IACrD,GAAG;QAAC;QAAY;QAAsB;QAAY;KAAO;IAEzD,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU,YAAY;QAE3B,MAAM,wBAAwB;YAC5B,IAAI,CAAC,OAAO,eAAe,EAAE;YAE7B,MAAM,aAAa,CAAA,GAAA,kIAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO,eAAe,CAAC,KAAK;YACpE,sBAAsB;QACxB;QAEA,MAAM,oBAAoB;YACxB;QACF;QAEA,2BAA2B;QAC3B,IAAI,OAAO,eAAe,EAAE;YAC1B,MAAM,OAAO,OAAO,eAAe;YAEnC,2BAA2B;YAC3B,MAAM,mBAAmB,KAAK,QAAQ;YACtC,KAAK,QAAQ,GAAG,CAAC;gBACf,iBAAiB,IAAI,CAAC,MAAM;gBAC5B;YACF;YAEA,8BAA8B;YAC9B,KAAK,GAAG,CAAC,gBAAgB,CAAC,SAAS;YACnC,KAAK,GAAG,CAAC,gBAAgB,CAAC,QAAQ;YAElC,OAAO;gBACL,KAAK,GAAG,CAAC,mBAAmB,CAAC,SAAS;gBACtC,KAAK,GAAG,CAAC,mBAAmB,CAAC,QAAQ;gBACrC,KAAK,QAAQ,GAAG;YAClB;QACF;IACF,GAAG;QAAC;QAAQ;QAAuB;KAAW;IAE9C,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ,mBAAmB,CAAC,SAAS;QAE1C,mCAAmC;QACnC,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAC,SACnC,OAAO,MAAM,KAAK;QAGpB,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,eAAe,EAAE;IACxC,GAAG;QAAC;QAAQ;QAAS;KAAO;IAE5B,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,eAAe,OAAO,EAAE;gBAC1B,aAAa,eAAe,OAAO;YACrC;QACF;IACF,GAAG,EAAE;IAEL,OAAO;QACL,SAAS,WAAW,EAAE;QACtB,aAAa,CAAC,CAAC,eAAe;QAC9B,mBAAmB,SAAS,UAAU;QACtC,eAAe,iBAAiB,EAAE;IACpC;AACF", "debugId": null}}, {"offset": {"line": 3618, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/lib/commentPlugin.ts"], "sourcesContent": ["/**\n * ProseMirror plugin for handling comments and comment decorations\n */\n\nimport { Plugin, Plugin<PERSON>ey } from \"prosemirror-state\";\nimport { Decoration, DecorationSet } from \"prosemirror-view\";\nimport { getUserColor } from \"./userColors\";\n\nexport interface CommentData {\n  id: string;\n  documentId: string;\n  userId: string;\n  content: string;\n  position: number;\n  selection: {\n    from: number;\n    to: number;\n  };\n  selectedText: string;\n  isResolved?: boolean;\n  timestamp: number;\n  user?: {\n    _id: string;\n    name?: string;\n    email: string;\n    image?: string;\n  };\n  replies?: CommentReply[];\n}\n\nexport interface CommentReply {\n  _id: string;\n  commentId: string;\n  userId: string;\n  content: string;\n  timestamp: number;\n  user?: {\n    _id: string;\n    name?: string;\n    email: string;\n    image?: string;\n  };\n}\n\nexport interface CommentPluginState {\n  comments: CommentData[];\n  decorations: DecorationSet;\n  selectedCommentId?: string;\n  showSidebar: boolean;\n}\n\nexport const commentPluginKey = new PluginKey<CommentPluginState>(\"comments\");\n\n/**\n * Create a decoration for a comment with user-specific colors and selection states\n */\nfunction createCommentDecoration(comment: CommentData, isSelected: boolean = false, commentCount: number = 1) {\n  const userColor = getUserColor(comment.userId);\n  const userName = comment.user?.name || comment.user?.email || \"Unknown User\";\n\n  // Build CSS classes\n  const baseClasses = [\"comment-highlight\"];\n\n  if (comment.isResolved) {\n    baseClasses.push(\"comment-resolved\");\n  } else {\n    baseClasses.push(\"comment-active\");\n  }\n\n  if (isSelected) {\n    baseClasses.push(\"comment-selected\");\n  }\n\n  if (commentCount > 1) {\n    baseClasses.push(\"comment-multiple\");\n  }\n\n  // Create inline styles for user-specific colors\n  // Enhanced opacity and styling for better visibility, especially for selected comments\n  const backgroundColor = comment.isResolved\n    ? `rgba(${userColor.rgb}, 0.1)`\n    : `rgba(${userColor.rgb}, ${isSelected ? 0.35 : 0.15})`;\n\n  const borderColor = comment.isResolved\n    ? `rgba(${userColor.rgb}, 0.2)`\n    : `rgba(${userColor.rgb}, ${isSelected ? 0.7 : 0.3})`;\n\n  const style = `\n    background-color: ${backgroundColor};\n    border: 1px solid ${borderColor};\n    ${isSelected ? `\n      box-shadow: 0 0 0 2px rgba(${userColor.rgb}, 0.4), 0 0 8px rgba(${userColor.rgb}, 0.2);\n      transform: scale(1.01);\n      z-index: 10;\n      position: relative;\n    ` : ''}\n  `;\n\n  return Decoration.inline(\n    comment.selection.from,\n    comment.selection.to,\n    {\n      class: baseClasses.join(\" \"),\n      style: style.trim(),\n      \"data-comment-id\": comment.id,\n      \"data-user-id\": comment.userId,\n      \"data-comment-count\": commentCount.toString(),\n      \"data-from\": comment.selection.from.toString(),\n      \"data-to\": comment.selection.to.toString(),\n      title: `Comment by ${userName}: ${comment.content.substring(0, 100)}${comment.content.length > 100 ? '...' : ''}`,\n    },\n    {\n      inclusiveStart: false,\n      inclusiveEnd: false,\n    }\n  );\n}\n\n/**\n * Update decorations based on current comments with selection state and multiple comment handling\n */\nfunction updateDecorations(doc: any, comments: CommentData[], selectedCommentId?: string): DecorationSet {\n  const decorations: Decoration[] = [];\n\n  // Group comments by their text selection range to handle multiple comments on same text\n  const commentGroups = new Map<string, CommentData[]>();\n\n  comments.forEach((comment) => {\n    // Validate that the comment position is still valid in the document\n    if (comment.selection.from >= 0 &&\n        comment.selection.to <= doc.content.size &&\n        comment.selection.from < comment.selection.to) {\n\n      const rangeKey = `${comment.selection.from}-${comment.selection.to}`;\n      if (!commentGroups.has(rangeKey)) {\n        commentGroups.set(rangeKey, []);\n      }\n      commentGroups.get(rangeKey)!.push(comment);\n    }\n  });\n\n  // Create decorations for each group\n  commentGroups.forEach((groupComments, rangeKey) => {\n    // Sort comments by timestamp (oldest first)\n    groupComments.sort((a, b) => a.timestamp - b.timestamp);\n\n    // Check if any comment in this group is selected\n    const hasSelectedComment = groupComments.some(c => c.id === selectedCommentId);\n\n    // For multiple comments on same text, we'll create a decoration for the primary comment\n    // but include information about all comments\n    const primaryComment = groupComments[0];\n    const commentCount = groupComments.length;\n\n    decorations.push(createCommentDecoration(\n      primaryComment,\n      hasSelectedComment,\n      commentCount\n    ));\n  });\n\n  return DecorationSet.create(doc, decorations);\n}\n\n/**\n * Create the comment plugin\n */\nexport function createCommentPlugin() {\n  return new Plugin<CommentPluginState>({\n    key: commentPluginKey,\n    \n    state: {\n      init(): CommentPluginState {\n        return {\n          comments: [],\n          decorations: DecorationSet.empty,\n          showSidebar: false,\n        };\n      },\n      \n      apply(tr, pluginState, oldState, newState) {\n        let { comments, decorations, selectedCommentId, showSidebar } = pluginState;\n        \n        // Handle document changes - update decorations\n        if (tr.docChanged) {\n          decorations = updateDecorations(newState.doc, comments, selectedCommentId);\n        }\n        \n        // Handle comment-related transactions\n        const commentMeta = tr.getMeta(commentPluginKey);\n        if (commentMeta) {\n          switch (commentMeta.type) {\n            case \"setComments\":\n              comments = commentMeta.comments;\n              decorations = updateDecorations(newState.doc, comments, selectedCommentId);\n              break;\n\n            case \"addComment\":\n              comments = [...comments, commentMeta.comment];\n              decorations = updateDecorations(newState.doc, comments, selectedCommentId);\n              break;\n\n            case \"updateComment\":\n              comments = comments.map(c =>\n                c.id === commentMeta.comment.id ? commentMeta.comment : c\n              );\n              decorations = updateDecorations(newState.doc, comments, selectedCommentId);\n              break;\n\n            case \"deleteComment\":\n              comments = comments.filter(c => c.id !== commentMeta.commentId);\n              decorations = updateDecorations(newState.doc, comments, selectedCommentId);\n              if (selectedCommentId === commentMeta.commentId) {\n                selectedCommentId = undefined;\n              }\n              break;\n\n            case \"selectComment\":\n              selectedCommentId = commentMeta.commentId;\n              // Update decorations to reflect the new selection\n              decorations = updateDecorations(newState.doc, comments, selectedCommentId);\n              break;\n              \n            case \"toggleSidebar\":\n              showSidebar = !showSidebar;\n              break;\n              \n            case \"setSidebar\":\n              showSidebar = commentMeta.show;\n              break;\n          }\n        }\n        \n        return {\n          comments,\n          decorations,\n          selectedCommentId,\n          showSidebar,\n        };\n      },\n    },\n    \n    props: {\n      decorations(state) {\n        return commentPluginKey.getState(state)?.decorations;\n      },\n      \n      handleDOMEvents: {\n        // Handle clicks on comment decorations\n        click(view, event) {\n          const target = event.target as HTMLElement;\n          const commentElement = target.closest('[data-comment-id]');\n\n          if (commentElement) {\n            const commentId = commentElement.getAttribute('data-comment-id');\n            const commentCount = parseInt(commentElement.getAttribute('data-comment-count') || '1');\n\n            if (commentId) {\n              // If there are multiple comments on the same text, cycle through them\n              const state = commentPluginKey.getState(view.state);\n              const currentSelected = state?.selectedCommentId;\n\n              if (commentCount > 1 && currentSelected === commentId) {\n                // Find all comments at this position and select the next one\n                const commentsAtPosition = state?.comments.filter(c =>\n                  c.selection.from === parseInt(commentElement.getAttribute('data-from') || '0') &&\n                  c.selection.to === parseInt(commentElement.getAttribute('data-to') || '0')\n                ) || [];\n\n                if (commentsAtPosition.length > 1) {\n                  const currentIndex = commentsAtPosition.findIndex(c => c.id === currentSelected);\n                  const nextIndex = (currentIndex + 1) % commentsAtPosition.length;\n                  const nextCommentId = commentsAtPosition[nextIndex].id;\n\n                  const tr = view.state.tr.setMeta(commentPluginKey, {\n                    type: \"selectComment\",\n                    commentId: nextCommentId,\n                  });\n                  view.dispatch(tr);\n\n                  // Trigger custom event for sidebar scrolling\n                  window.dispatchEvent(new CustomEvent('comment-selected', {\n                    detail: { commentId: nextCommentId }\n                  }));\n\n                  return true;\n                }\n              }\n\n              // Select the comment and show sidebar\n              const tr = view.state.tr.setMeta(commentPluginKey, {\n                type: \"selectComment\",\n                commentId,\n              });\n              view.dispatch(tr);\n\n              // Also ensure sidebar is visible\n              const tr2 = view.state.tr.setMeta(commentPluginKey, {\n                type: \"setSidebar\",\n                show: true,\n              });\n              view.dispatch(tr2);\n\n              // Trigger custom event for sidebar scrolling\n              window.dispatchEvent(new CustomEvent('comment-selected', {\n                detail: { commentId }\n              }));\n\n              return true;\n            }\n          }\n\n          return false;\n        },\n      },\n    },\n  });\n}\n\n/**\n * Helper functions for interacting with the comment plugin\n */\nexport const commentPluginHelpers = {\n  /**\n   * Set all comments for the document\n   */\n  setComments: (view: any, comments: CommentData[]) => {\n    const tr = view.state.tr.setMeta(commentPluginKey, {\n      type: \"setComments\",\n      comments,\n    });\n    view.dispatch(tr);\n\n    // Force a view update to ensure decorations are properly rendered\n    setTimeout(() => {\n      view.updateState(view.state);\n    }, 10);\n  },\n  \n  /**\n   * Add a new comment\n   */\n  addComment: (view: any, comment: CommentData) => {\n    const tr = view.state.tr.setMeta(commentPluginKey, {\n      type: \"addComment\",\n      comment,\n    });\n    view.dispatch(tr);\n  },\n  \n  /**\n   * Update an existing comment\n   */\n  updateComment: (view: any, comment: CommentData) => {\n    const tr = view.state.tr.setMeta(commentPluginKey, {\n      type: \"updateComment\",\n      comment,\n    });\n    view.dispatch(tr);\n  },\n  \n  /**\n   * Delete a comment\n   */\n  deleteComment: (view: any, commentId: string) => {\n    const tr = view.state.tr.setMeta(commentPluginKey, {\n      type: \"deleteComment\",\n      commentId,\n    });\n    view.dispatch(tr);\n  },\n  \n  /**\n   * Select a comment\n   */\n  selectComment: (view: any, commentId: string) => {\n    const tr = view.state.tr.setMeta(commentPluginKey, {\n      type: \"selectComment\",\n      commentId,\n    });\n    view.dispatch(tr);\n\n    // Force a view update to ensure decorations are properly updated\n    setTimeout(() => {\n      view.updateState(view.state);\n\n      // Scroll the selected comment into view if it exists\n      const commentElement = view.dom.querySelector(`[data-comment-id=\"${commentId}\"]`);\n      if (commentElement) {\n        commentElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center',\n          inline: 'nearest'\n        });\n      }\n    }, 10);\n  },\n  \n  /**\n   * Toggle comment sidebar visibility\n   */\n  toggleSidebar: (view: any) => {\n    const tr = view.state.tr.setMeta(commentPluginKey, {\n      type: \"toggleSidebar\",\n    });\n    view.dispatch(tr);\n  },\n  \n  /**\n   * Set sidebar visibility\n   */\n  setSidebar: (view: any, show: boolean) => {\n    const tr = view.state.tr.setMeta(commentPluginKey, {\n      type: \"setSidebar\",\n      show,\n    });\n    view.dispatch(tr);\n  },\n  \n  /**\n   * Get current plugin state\n   */\n  getState: (view: any): CommentPluginState | undefined => {\n    return commentPluginKey.getState(view.state);\n  },\n  \n  /**\n   * Get current selection info for creating a comment\n   */\n  getSelectionInfo: (view: any) => {\n    const { from, to } = view.state.selection;\n    if (from === to) return null; // No selection\n\n    const selectedText = view.state.doc.textBetween(from, to);\n    return {\n      from,\n      to,\n      selectedText,\n      position: from, // Use start position as anchor\n    };\n  },\n\n  /**\n   * Get the currently selected comment ID\n   */\n  getSelectedCommentId: (view: any): string | undefined => {\n    const state = commentPluginKey.getState(view.state);\n    return state?.selectedCommentId;\n  },\n\n  /**\n   * Get comments at a specific position\n   */\n  getCommentsAtPosition: (view: any, position: number): CommentData[] => {\n    const state = commentPluginKey.getState(view.state);\n    if (!state) return [];\n\n    return state.comments.filter(comment =>\n      position >= comment.selection.from && position <= comment.selection.to\n    );\n  },\n\n  /**\n   * Debug function to check plugin state and decorations\n   */\n  debugState: (view: any) => {\n    const state = commentPluginKey.getState(view.state);\n    console.log('Comment Plugin Debug State:', {\n      commentsCount: state?.comments.length || 0,\n      decorationsCount: state?.decorations.find().length || 0,\n      selectedCommentId: state?.selectedCommentId,\n      showSidebar: state?.showSidebar,\n      comments: state?.comments.map(c => ({\n        id: c.id,\n        from: c.selection.from,\n        to: c.selection.to,\n        text: c.selectedText\n      }))\n    });\n    return state;\n  },\n};\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAED;AACA;AACA;;;;AA6CO,MAAM,mBAAmB,IAAI,qJAAA,CAAA,YAAS,CAAqB;AAElE;;CAEC,GACD,SAAS,wBAAwB,OAAoB,EAAE,aAAsB,KAAK,EAAE,eAAuB,CAAC;IAC1G,MAAM,YAAY,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,MAAM;IAC7C,MAAM,WAAW,QAAQ,IAAI,EAAE,QAAQ,QAAQ,IAAI,EAAE,SAAS;IAE9D,oBAAoB;IACpB,MAAM,cAAc;QAAC;KAAoB;IAEzC,IAAI,QAAQ,UAAU,EAAE;QACtB,YAAY,IAAI,CAAC;IACnB,OAAO;QACL,YAAY,IAAI,CAAC;IACnB;IAEA,IAAI,YAAY;QACd,YAAY,IAAI,CAAC;IACnB;IAEA,IAAI,eAAe,GAAG;QACpB,YAAY,IAAI,CAAC;IACnB;IAEA,gDAAgD;IAChD,uFAAuF;IACvF,MAAM,kBAAkB,QAAQ,UAAU,GACtC,CAAC,KAAK,EAAE,UAAU,GAAG,CAAC,MAAM,CAAC,GAC7B,CAAC,KAAK,EAAE,UAAU,GAAG,CAAC,EAAE,EAAE,aAAa,OAAO,KAAK,CAAC,CAAC;IAEzD,MAAM,cAAc,QAAQ,UAAU,GAClC,CAAC,KAAK,EAAE,UAAU,GAAG,CAAC,MAAM,CAAC,GAC7B,CAAC,KAAK,EAAE,UAAU,GAAG,CAAC,EAAE,EAAE,aAAa,MAAM,IAAI,CAAC,CAAC;IAEvD,MAAM,QAAQ,CAAC;sBACK,EAAE,gBAAgB;sBAClB,EAAE,YAAY;IAChC,EAAE,aAAa,CAAC;iCACa,EAAE,UAAU,GAAG,CAAC,qBAAqB,EAAE,UAAU,GAAG,CAAC;;;;IAIlF,CAAC,GAAG,GAAG;EACT,CAAC;IAED,OAAO,oJAAA,CAAA,aAAU,CAAC,MAAM,CACtB,QAAQ,SAAS,CAAC,IAAI,EACtB,QAAQ,SAAS,CAAC,EAAE,EACpB;QACE,OAAO,YAAY,IAAI,CAAC;QACxB,OAAO,MAAM,IAAI;QACjB,mBAAmB,QAAQ,EAAE;QAC7B,gBAAgB,QAAQ,MAAM;QAC9B,sBAAsB,aAAa,QAAQ;QAC3C,aAAa,QAAQ,SAAS,CAAC,IAAI,CAAC,QAAQ;QAC5C,WAAW,QAAQ,SAAS,CAAC,EAAE,CAAC,QAAQ;QACxC,OAAO,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,QAAQ,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,QAAQ,OAAO,CAAC,MAAM,GAAG,MAAM,QAAQ,IAAI;IACnH,GACA;QACE,gBAAgB;QAChB,cAAc;IAChB;AAEJ;AAEA;;CAEC,GACD,SAAS,kBAAkB,GAAQ,EAAE,QAAuB,EAAE,iBAA0B;IACtF,MAAM,cAA4B,EAAE;IAEpC,wFAAwF;IACxF,MAAM,gBAAgB,IAAI;IAE1B,SAAS,OAAO,CAAC,CAAC;QAChB,oEAAoE;QACpE,IAAI,QAAQ,SAAS,CAAC,IAAI,IAAI,KAC1B,QAAQ,SAAS,CAAC,EAAE,IAAI,IAAI,OAAO,CAAC,IAAI,IACxC,QAAQ,SAAS,CAAC,IAAI,GAAG,QAAQ,SAAS,CAAC,EAAE,EAAE;YAEjD,MAAM,WAAW,GAAG,QAAQ,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,SAAS,CAAC,EAAE,EAAE;YACpE,IAAI,CAAC,cAAc,GAAG,CAAC,WAAW;gBAChC,cAAc,GAAG,CAAC,UAAU,EAAE;YAChC;YACA,cAAc,GAAG,CAAC,UAAW,IAAI,CAAC;QACpC;IACF;IAEA,oCAAoC;IACpC,cAAc,OAAO,CAAC,CAAC,eAAe;QACpC,4CAA4C;QAC5C,cAAc,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;QAEtD,iDAAiD;QACjD,MAAM,qBAAqB,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAE5D,wFAAwF;QACxF,6CAA6C;QAC7C,MAAM,iBAAiB,aAAa,CAAC,EAAE;QACvC,MAAM,eAAe,cAAc,MAAM;QAEzC,YAAY,IAAI,CAAC,wBACf,gBACA,oBACA;IAEJ;IAEA,OAAO,oJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,KAAK;AACnC;AAKO,SAAS;IACd,OAAO,IAAI,qJAAA,CAAA,SAAM,CAAqB;QACpC,KAAK;QAEL,OAAO;YACL;gBACE,OAAO;oBACL,UAAU,EAAE;oBACZ,aAAa,oJAAA,CAAA,gBAAa,CAAC,KAAK;oBAChC,aAAa;gBACf;YACF;YAEA,OAAM,EAAE,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ;gBACvC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,iBAAiB,EAAE,WAAW,EAAE,GAAG;gBAEhE,+CAA+C;gBAC/C,IAAI,GAAG,UAAU,EAAE;oBACjB,cAAc,kBAAkB,SAAS,GAAG,EAAE,UAAU;gBAC1D;gBAEA,sCAAsC;gBACtC,MAAM,cAAc,GAAG,OAAO,CAAC;gBAC/B,IAAI,aAAa;oBACf,OAAQ,YAAY,IAAI;wBACtB,KAAK;4BACH,WAAW,YAAY,QAAQ;4BAC/B,cAAc,kBAAkB,SAAS,GAAG,EAAE,UAAU;4BACxD;wBAEF,KAAK;4BACH,WAAW;mCAAI;gCAAU,YAAY,OAAO;6BAAC;4BAC7C,cAAc,kBAAkB,SAAS,GAAG,EAAE,UAAU;4BACxD;wBAEF,KAAK;4BACH,WAAW,SAAS,GAAG,CAAC,CAAA,IACtB,EAAE,EAAE,KAAK,YAAY,OAAO,CAAC,EAAE,GAAG,YAAY,OAAO,GAAG;4BAE1D,cAAc,kBAAkB,SAAS,GAAG,EAAE,UAAU;4BACxD;wBAEF,KAAK;4BACH,WAAW,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,SAAS;4BAC9D,cAAc,kBAAkB,SAAS,GAAG,EAAE,UAAU;4BACxD,IAAI,sBAAsB,YAAY,SAAS,EAAE;gCAC/C,oBAAoB;4BACtB;4BACA;wBAEF,KAAK;4BACH,oBAAoB,YAAY,SAAS;4BACzC,kDAAkD;4BAClD,cAAc,kBAAkB,SAAS,GAAG,EAAE,UAAU;4BACxD;wBAEF,KAAK;4BACH,cAAc,CAAC;4BACf;wBAEF,KAAK;4BACH,cAAc,YAAY,IAAI;4BAC9B;oBACJ;gBACF;gBAEA,OAAO;oBACL;oBACA;oBACA;oBACA;gBACF;YACF;QACF;QAEA,OAAO;YACL,aAAY,KAAK;gBACf,OAAO,iBAAiB,QAAQ,CAAC,QAAQ;YAC3C;YAEA,iBAAiB;gBACf,uCAAuC;gBACvC,OAAM,IAAI,EAAE,KAAK;oBACf,MAAM,SAAS,MAAM,MAAM;oBAC3B,MAAM,iBAAiB,OAAO,OAAO,CAAC;oBAEtC,IAAI,gBAAgB;wBAClB,MAAM,YAAY,eAAe,YAAY,CAAC;wBAC9C,MAAM,eAAe,SAAS,eAAe,YAAY,CAAC,yBAAyB;wBAEnF,IAAI,WAAW;4BACb,sEAAsE;4BACtE,MAAM,QAAQ,iBAAiB,QAAQ,CAAC,KAAK,KAAK;4BAClD,MAAM,kBAAkB,OAAO;4BAE/B,IAAI,eAAe,KAAK,oBAAoB,WAAW;gCACrD,6DAA6D;gCAC7D,MAAM,qBAAqB,OAAO,SAAS,OAAO,CAAA,IAChD,EAAE,SAAS,CAAC,IAAI,KAAK,SAAS,eAAe,YAAY,CAAC,gBAAgB,QAC1E,EAAE,SAAS,CAAC,EAAE,KAAK,SAAS,eAAe,YAAY,CAAC,cAAc,SACnE,EAAE;gCAEP,IAAI,mBAAmB,MAAM,GAAG,GAAG;oCACjC,MAAM,eAAe,mBAAmB,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oCAChE,MAAM,YAAY,CAAC,eAAe,CAAC,IAAI,mBAAmB,MAAM;oCAChE,MAAM,gBAAgB,kBAAkB,CAAC,UAAU,CAAC,EAAE;oCAEtD,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,kBAAkB;wCACjD,MAAM;wCACN,WAAW;oCACb;oCACA,KAAK,QAAQ,CAAC;oCAEd,6CAA6C;oCAC7C,OAAO,aAAa,CAAC,IAAI,YAAY,oBAAoB;wCACvD,QAAQ;4CAAE,WAAW;wCAAc;oCACrC;oCAEA,OAAO;gCACT;4BACF;4BAEA,sCAAsC;4BACtC,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,kBAAkB;gCACjD,MAAM;gCACN;4BACF;4BACA,KAAK,QAAQ,CAAC;4BAEd,iCAAiC;4BACjC,MAAM,MAAM,KAAK,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,kBAAkB;gCAClD,MAAM;gCACN,MAAM;4BACR;4BACA,KAAK,QAAQ,CAAC;4BAEd,6CAA6C;4BAC7C,OAAO,aAAa,CAAC,IAAI,YAAY,oBAAoB;gCACvD,QAAQ;oCAAE;gCAAU;4BACtB;4BAEA,OAAO;wBACT;oBACF;oBAEA,OAAO;gBACT;YACF;QACF;IACF;AACF;AAKO,MAAM,uBAAuB;IAClC;;GAEC,GACD,aAAa,CAAC,MAAW;QACvB,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,kBAAkB;YACjD,MAAM;YACN;QACF;QACA,KAAK,QAAQ,CAAC;QAEd,kEAAkE;QAClE,WAAW;YACT,KAAK,WAAW,CAAC,KAAK,KAAK;QAC7B,GAAG;IACL;IAEA;;GAEC,GACD,YAAY,CAAC,MAAW;QACtB,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,kBAAkB;YACjD,MAAM;YACN;QACF;QACA,KAAK,QAAQ,CAAC;IAChB;IAEA;;GAEC,GACD,eAAe,CAAC,MAAW;QACzB,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,kBAAkB;YACjD,MAAM;YACN;QACF;QACA,KAAK,QAAQ,CAAC;IAChB;IAEA;;GAEC,GACD,eAAe,CAAC,MAAW;QACzB,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,kBAAkB;YACjD,MAAM;YACN;QACF;QACA,KAAK,QAAQ,CAAC;IAChB;IAEA;;GAEC,GACD,eAAe,CAAC,MAAW;QACzB,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,kBAAkB;YACjD,MAAM;YACN;QACF;QACA,KAAK,QAAQ,CAAC;QAEd,iEAAiE;QACjE,WAAW;YACT,KAAK,WAAW,CAAC,KAAK,KAAK;YAE3B,qDAAqD;YACrD,MAAM,iBAAiB,KAAK,GAAG,CAAC,aAAa,CAAC,CAAC,kBAAkB,EAAE,UAAU,EAAE,CAAC;YAChF,IAAI,gBAAgB;gBAClB,eAAe,cAAc,CAAC;oBAC5B,UAAU;oBACV,OAAO;oBACP,QAAQ;gBACV;YACF;QACF,GAAG;IACL;IAEA;;GAEC,GACD,eAAe,CAAC;QACd,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,kBAAkB;YACjD,MAAM;QACR;QACA,KAAK,QAAQ,CAAC;IAChB;IAEA;;GAEC,GACD,YAAY,CAAC,MAAW;QACtB,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,kBAAkB;YACjD,MAAM;YACN;QACF;QACA,KAAK,QAAQ,CAAC;IAChB;IAEA;;GAEC,GACD,UAAU,CAAC;QACT,OAAO,iBAAiB,QAAQ,CAAC,KAAK,KAAK;IAC7C;IAEA;;GAEC,GACD,kBAAkB,CAAC;QACjB,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,KAAK,KAAK,CAAC,SAAS;QACzC,IAAI,SAAS,IAAI,OAAO,MAAM,eAAe;QAE7C,MAAM,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM;QACtD,OAAO;YACL;YACA;YACA;YACA,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,sBAAsB,CAAC;QACrB,MAAM,QAAQ,iBAAiB,QAAQ,CAAC,KAAK,KAAK;QAClD,OAAO,OAAO;IAChB;IAEA;;GAEC,GACD,uBAAuB,CAAC,MAAW;QACjC,MAAM,QAAQ,iBAAiB,QAAQ,CAAC,KAAK,KAAK;QAClD,IAAI,CAAC,OAAO,OAAO,EAAE;QAErB,OAAO,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA,UAC3B,YAAY,QAAQ,SAAS,CAAC,IAAI,IAAI,YAAY,QAAQ,SAAS,CAAC,EAAE;IAE1E;IAEA;;GAEC,GACD,YAAY,CAAC;QACX,MAAM,QAAQ,iBAAiB,QAAQ,CAAC,KAAK,KAAK;QAClD,QAAQ,GAAG,CAAC,+BAA+B;YACzC,eAAe,OAAO,SAAS,UAAU;YACzC,kBAAkB,OAAO,YAAY,OAAO,UAAU;YACtD,mBAAmB,OAAO;YAC1B,aAAa,OAAO;YACpB,UAAU,OAAO,SAAS,IAAI,CAAA,IAAK,CAAC;oBAClC,IAAI,EAAE,EAAE;oBACR,MAAM,EAAE,SAAS,CAAC,IAAI;oBACtB,IAAI,EAAE,SAAS,CAAC,EAAE;oBAClB,MAAM,EAAE,YAAY;gBACtB,CAAC;QACH;QACA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 3974, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/hooks/useComments.ts"], "sourcesContent": ["'use client';\n\n/**\n * React hook for managing comments in the collaborative editor\n */\n\nimport { useEffect, useCallback, useRef } from \"react\";\nimport { useQuery, useMutation } from \"convex/react\";\nimport { api } from \"../../convex/_generated/api\";\nimport { Id } from \"../../convex/_generated/dataModel\";\nimport { commentPluginHelpers, type CommentData } from \"../lib/commentPlugin\";\nimport { toast } from \"sonner\";\n\ninterface UseCommentsProps {\n  documentId: Id<\"documents\">;\n  editor: any; // BlockNote/ProseMirror editor instance\n  isReadOnly?: boolean;\n}\n\nexport function useComments({\n  documentId,\n  editor,\n  isReadOnly = false\n}: UseCommentsProps) {\n  const comments = useQuery(api.comments.getDocumentComments, { documentId });\n  const createCommentMutation = useMutation(api.comments.createComment);\n  const updateCommentMutation = useMutation(api.comments.updateComment);\n  const deleteCommentMutation = useMutation(api.comments.deleteComment);\n  const createReplyMutation = useMutation(api.comments.createReply);\n  const updateReplyMutation = useMutation(api.comments.updateReply);\n  const deleteReplyMutation = useMutation(api.comments.deleteReply);\n  const resolveCommentMutation = useMutation(api.comments.resolveComment);\n  \n  const lastCommentsUpdate = useRef<number>(0);\n\n  // Update plugin state when comments change\n  useEffect(() => {\n    if (!editor?.prosemirrorView || !comments) return;\n\n    // Prevent unnecessary updates\n    const commentsHash = JSON.stringify(comments.map(c => ({\n      id: c._id,\n      content: c.content,\n      isResolved: c.isResolved,\n      timestamp: c.timestamp\n    })));\n    const currentTime = Date.now();\n\n    if (currentTime - lastCommentsUpdate.current < 100) return;\n    lastCommentsUpdate.current = currentTime;\n\n    // Transform comments to plugin format\n    const pluginComments: CommentData[] = comments.map(comment => ({\n      id: comment._id,\n      documentId: comment.documentId,\n      userId: comment.userId,\n      content: comment.content,\n      position: comment.position,\n      selection: comment.selection,\n      selectedText: comment.selectedText,\n      isResolved: comment.isResolved,\n      timestamp: comment.timestamp,\n      user: comment.user,\n      replies: comment.replies,\n    }));\n\n    // Set comments with a small delay to ensure editor is fully ready\n    // This helps with page refresh scenarios where the editor might not be fully initialized\n    const setCommentsWithDelay = () => {\n      if (editor?.prosemirrorView?.state) {\n        commentPluginHelpers.setComments(editor.prosemirrorView, pluginComments);\n      } else {\n        // Retry if editor isn't ready yet\n        setTimeout(setCommentsWithDelay, 50);\n      }\n    };\n\n    setTimeout(setCommentsWithDelay, 10);\n  }, [editor, comments]);\n\n  /**\n   * Create a new comment\n   */\n  const createComment = useCallback(async (content: string) => {\n    if (!editor?.prosemirrorView || isReadOnly) return null;\n    \n    const selectionInfo = commentPluginHelpers.getSelectionInfo(editor.prosemirrorView);\n    if (!selectionInfo) {\n      toast.error(\"Please select text to comment on\");\n      return null;\n    }\n    \n    try {\n      const commentId = await createCommentMutation({\n        documentId,\n        content,\n        position: selectionInfo.position,\n        selection: {\n          from: selectionInfo.from,\n          to: selectionInfo.to,\n        },\n        selectedText: selectionInfo.selectedText,\n      });\n      \n      toast.success(\"Comment added successfully\");\n      return commentId;\n    } catch (error) {\n      toast.error(\"Failed to add comment\");\n      console.error(\"Error creating comment:\", error);\n      return null;\n    }\n  }, [editor, documentId, createCommentMutation, isReadOnly]);\n\n  /**\n   * Update an existing comment\n   */\n  const updateComment = useCallback(async (commentId: Id<\"comments\">, content: string) => {\n    try {\n      await updateCommentMutation({\n        commentId,\n        content,\n      });\n      \n      toast.success(\"Comment updated successfully\");\n      return true;\n    } catch (error) {\n      toast.error(\"Failed to update comment\");\n      console.error(\"Error updating comment:\", error);\n      return false;\n    }\n  }, [updateCommentMutation]);\n\n  /**\n   * Delete a comment\n   */\n  const deleteComment = useCallback(async (commentId: Id<\"comments\">) => {\n    try {\n      await deleteCommentMutation({ commentId });\n      \n      toast.success(\"Comment deleted successfully\");\n      return true;\n    } catch (error) {\n      toast.error(\"Failed to delete comment\");\n      console.error(\"Error deleting comment:\", error);\n      return false;\n    }\n  }, [deleteCommentMutation]);\n\n  /**\n   * Create a reply to a comment\n   */\n  const createReply = useCallback(async (commentId: Id<\"comments\">, content: string) => {\n    try {\n      const replyId = await createReplyMutation({\n        commentId,\n        content,\n      });\n      \n      toast.success(\"Reply added successfully\");\n      return replyId;\n    } catch (error) {\n      toast.error(\"Failed to add reply\");\n      console.error(\"Error creating reply:\", error);\n      return null;\n    }\n  }, [createReplyMutation]);\n\n  /**\n   * Update a reply\n   */\n  const updateReply = useCallback(async (replyId: Id<\"commentReplies\">, content: string) => {\n    try {\n      await updateReplyMutation({\n        replyId,\n        content,\n      });\n      \n      toast.success(\"Reply updated successfully\");\n      return true;\n    } catch (error) {\n      toast.error(\"Failed to update reply\");\n      console.error(\"Error updating reply:\", error);\n      return false;\n    }\n  }, [updateReplyMutation]);\n\n  /**\n   * Delete a reply\n   */\n  const deleteReply = useCallback(async (replyId: Id<\"commentReplies\">) => {\n    try {\n      await deleteReplyMutation({ replyId });\n      \n      toast.success(\"Reply deleted successfully\");\n      return true;\n    } catch (error) {\n      toast.error(\"Failed to delete reply\");\n      console.error(\"Error deleting reply:\", error);\n      return false;\n    }\n  }, [deleteReplyMutation]);\n\n  /**\n   * Resolve or unresolve a comment\n   */\n  const resolveComment = useCallback(async (commentId: Id<\"comments\">, isResolved: boolean) => {\n    if (isReadOnly) {\n      toast.error(\"You don't have permission to resolve comments\");\n      return false;\n    }\n    \n    try {\n      await resolveCommentMutation({\n        commentId,\n        isResolved,\n      });\n      \n      toast.success(isResolved ? \"Comment resolved\" : \"Comment reopened\");\n      return true;\n    } catch (error) {\n      toast.error(\"Failed to update comment status\");\n      console.error(\"Error resolving comment:\", error);\n      return false;\n    }\n  }, [resolveCommentMutation, isReadOnly]);\n\n  /**\n   * Scroll to a comment in the sidebar\n   */\n  const scrollToComment = useCallback((commentId: string) => {\n    setTimeout(() => {\n      const commentElement = document.querySelector(`[data-testid=\"comment-thread\"][data-comment-id=\"${commentId}\"]`);\n      if (commentElement) {\n        commentElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center'\n        });\n      }\n    }, 100);\n  }, []);\n\n  /**\n   * Select a comment (highlight it and show in sidebar)\n   */\n  const selectComment = useCallback((commentId: string) => {\n    if (!editor?.prosemirrorView) return;\n\n    commentPluginHelpers.selectComment(editor.prosemirrorView, commentId);\n    commentPluginHelpers.setSidebar(editor.prosemirrorView, true);\n    scrollToComment(commentId);\n  }, [editor, scrollToComment]);\n\n  /**\n   * Toggle comment sidebar visibility\n   */\n  const toggleSidebar = useCallback(() => {\n    if (!editor?.prosemirrorView) return;\n\n    commentPluginHelpers.toggleSidebar(editor.prosemirrorView);\n  }, [editor]);\n\n  /**\n   * Get current plugin state\n   */\n  const getPluginState = useCallback(() => {\n    if (!editor?.prosemirrorView) return null;\n\n    return commentPluginHelpers.getState(editor.prosemirrorView);\n  }, [editor]);\n\n  /**\n   * Force refresh comment decorations - useful for debugging or fixing sync issues\n   */\n  const forceRefreshDecorations = useCallback(() => {\n    if (!editor?.prosemirrorView || !comments) return;\n\n    const pluginComments: CommentData[] = comments.map(comment => ({\n      id: comment._id,\n      documentId: comment.documentId,\n      userId: comment.userId,\n      content: comment.content,\n      position: comment.position,\n      selection: comment.selection,\n      selectedText: comment.selectedText,\n      isResolved: comment.isResolved,\n      timestamp: comment.timestamp,\n      user: comment.user,\n      replies: comment.replies,\n    }));\n\n    commentPluginHelpers.setComments(editor.prosemirrorView, pluginComments);\n    console.log('Force refreshed comment decorations:', pluginComments.length, 'comments');\n  }, [editor, comments]);\n\n  /**\n   * Debug function to log current state\n   */\n  const debugCommentState = useCallback(() => {\n    if (!editor?.prosemirrorView) return;\n\n    return commentPluginHelpers.debugState(editor.prosemirrorView);\n  }, [editor]);\n\n  // Listen for comment selection events from the editor\n  useEffect(() => {\n    const handleCommentSelected = (event: CustomEvent) => {\n      const { commentId } = event.detail;\n      scrollToComment(commentId);\n    };\n\n    window.addEventListener('comment-selected', handleCommentSelected as EventListener);\n    return () => {\n      window.removeEventListener('comment-selected', handleCommentSelected as EventListener);\n    };\n  }, [scrollToComment]);\n\n  // Listen for window focus events to refresh decorations after page refresh\n  useEffect(() => {\n    const handleWindowFocus = () => {\n      // Small delay to ensure editor is ready\n      setTimeout(() => {\n        if (editor?.prosemirrorView && comments && comments.length > 0) {\n          forceRefreshDecorations();\n        }\n      }, 100);\n    };\n\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('visibilitychange', () => {\n      if (!document.hidden) {\n        handleWindowFocus();\n      }\n    });\n\n    return () => {\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('visibilitychange', handleWindowFocus);\n    };\n  }, [editor, comments, forceRefreshDecorations]);\n\n  return {\n    comments: comments || [],\n    createComment,\n    updateComment,\n    deleteComment,\n    createReply,\n    updateReply,\n    deleteReply,\n    resolveComment,\n    selectComment,\n    toggleSidebar,\n    getPluginState,\n    forceRefreshDecorations,\n    debugCommentState,\n    isLoading: comments === undefined,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;;CAEC,GAED;AACA;AAAA;AACA;AAEA;AACA;AAXA;;;;;;AAmBO,SAAS,YAAY,EAC1B,UAAU,EACV,MAAM,EACN,aAAa,KAAK,EACD;IACjB,MAAM,WAAW,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,mBAAmB,EAAE;QAAE;IAAW;IACzE,MAAM,wBAAwB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,aAAa;IACpE,MAAM,wBAAwB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,aAAa;IACpE,MAAM,wBAAwB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,aAAa;IACpE,MAAM,sBAAsB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,WAAW;IAChE,MAAM,sBAAsB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,WAAW;IAChE,MAAM,sBAAsB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,WAAW;IAChE,MAAM,yBAAyB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,cAAc;IAEtE,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAU;IAE1C,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ,mBAAmB,CAAC,UAAU;QAE3C,8BAA8B;QAC9B,MAAM,eAAe,KAAK,SAAS,CAAC,SAAS,GAAG,CAAC,CAAA,IAAK,CAAC;gBACrD,IAAI,EAAE,GAAG;gBACT,SAAS,EAAE,OAAO;gBAClB,YAAY,EAAE,UAAU;gBACxB,WAAW,EAAE,SAAS;YACxB,CAAC;QACD,MAAM,cAAc,KAAK,GAAG;QAE5B,IAAI,cAAc,mBAAmB,OAAO,GAAG,KAAK;QACpD,mBAAmB,OAAO,GAAG;QAE7B,sCAAsC;QACtC,MAAM,iBAAgC,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;gBAC7D,IAAI,QAAQ,GAAG;gBACf,YAAY,QAAQ,UAAU;gBAC9B,QAAQ,QAAQ,MAAM;gBACtB,SAAS,QAAQ,OAAO;gBACxB,UAAU,QAAQ,QAAQ;gBAC1B,WAAW,QAAQ,SAAS;gBAC5B,cAAc,QAAQ,YAAY;gBAClC,YAAY,QAAQ,UAAU;gBAC9B,WAAW,QAAQ,SAAS;gBAC5B,MAAM,QAAQ,IAAI;gBAClB,SAAS,QAAQ,OAAO;YAC1B,CAAC;QAED,kEAAkE;QAClE,yFAAyF;QACzF,MAAM,uBAAuB;YAC3B,IAAI,QAAQ,iBAAiB,OAAO;gBAClC,2HAAA,CAAA,uBAAoB,CAAC,WAAW,CAAC,OAAO,eAAe,EAAE;YAC3D,OAAO;gBACL,kCAAkC;gBAClC,WAAW,sBAAsB;YACnC;QACF;QAEA,WAAW,sBAAsB;IACnC,GAAG;QAAC;QAAQ;KAAS;IAErB;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACvC,IAAI,CAAC,QAAQ,mBAAmB,YAAY,OAAO;QAEnD,MAAM,gBAAgB,2HAAA,CAAA,uBAAoB,CAAC,gBAAgB,CAAC,OAAO,eAAe;QAClF,IAAI,CAAC,eAAe;YAClB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;QAEA,IAAI;YACF,MAAM,YAAY,MAAM,sBAAsB;gBAC5C;gBACA;gBACA,UAAU,cAAc,QAAQ;gBAChC,WAAW;oBACT,MAAM,cAAc,IAAI;oBACxB,IAAI,cAAc,EAAE;gBACtB;gBACA,cAAc,cAAc,YAAY;YAC1C;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO;QACT,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;QACT;IACF,GAAG;QAAC;QAAQ;QAAY;QAAuB;KAAW;IAE1D;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,WAA2B;QAClE,IAAI;YACF,MAAM,sBAAsB;gBAC1B;gBACA;YACF;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO;QACT,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;QACT;IACF,GAAG;QAAC;KAAsB;IAE1B;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACvC,IAAI;YACF,MAAM,sBAAsB;gBAAE;YAAU;YAExC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO;QACT,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;QACT;IACF,GAAG;QAAC;KAAsB;IAE1B;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,WAA2B;QAChE,IAAI;YACF,MAAM,UAAU,MAAM,oBAAoB;gBACxC;gBACA;YACF;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO;QACT,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;IACF,GAAG;QAAC;KAAoB;IAExB;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,SAA+B;QACpE,IAAI;YACF,MAAM,oBAAoB;gBACxB;gBACA;YACF;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO;QACT,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;IACF,GAAG;QAAC;KAAoB;IAExB;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,IAAI;YACF,MAAM,oBAAoB;gBAAE;YAAQ;YAEpC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO;QACT,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;IACF,GAAG;QAAC;KAAoB;IAExB;;GAEC,GACD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,WAA2B;QACnE,IAAI,YAAY;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;QAEA,IAAI;YACF,MAAM,uBAAuB;gBAC3B;gBACA;YACF;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,aAAa,qBAAqB;YAChD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF,GAAG;QAAC;QAAwB;KAAW;IAEvC;;GAEC,GACD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,WAAW;YACT,MAAM,iBAAiB,SAAS,aAAa,CAAC,CAAC,gDAAgD,EAAE,UAAU,EAAE,CAAC;YAC9G,IAAI,gBAAgB;gBAClB,eAAe,cAAc,CAAC;oBAC5B,UAAU;oBACV,OAAO;gBACT;YACF;QACF,GAAG;IACL,GAAG,EAAE;IAEL;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,IAAI,CAAC,QAAQ,iBAAiB;QAE9B,2HAAA,CAAA,uBAAoB,CAAC,aAAa,CAAC,OAAO,eAAe,EAAE;QAC3D,2HAAA,CAAA,uBAAoB,CAAC,UAAU,CAAC,OAAO,eAAe,EAAE;QACxD,gBAAgB;IAClB,GAAG;QAAC;QAAQ;KAAgB;IAE5B;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,CAAC,QAAQ,iBAAiB;QAE9B,2HAAA,CAAA,uBAAoB,CAAC,aAAa,CAAC,OAAO,eAAe;IAC3D,GAAG;QAAC;KAAO;IAEX;;GAEC,GACD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,CAAC,QAAQ,iBAAiB,OAAO;QAErC,OAAO,2HAAA,CAAA,uBAAoB,CAAC,QAAQ,CAAC,OAAO,eAAe;IAC7D,GAAG;QAAC;KAAO;IAEX;;GAEC,GACD,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1C,IAAI,CAAC,QAAQ,mBAAmB,CAAC,UAAU;QAE3C,MAAM,iBAAgC,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;gBAC7D,IAAI,QAAQ,GAAG;gBACf,YAAY,QAAQ,UAAU;gBAC9B,QAAQ,QAAQ,MAAM;gBACtB,SAAS,QAAQ,OAAO;gBACxB,UAAU,QAAQ,QAAQ;gBAC1B,WAAW,QAAQ,SAAS;gBAC5B,cAAc,QAAQ,YAAY;gBAClC,YAAY,QAAQ,UAAU;gBAC9B,WAAW,QAAQ,SAAS;gBAC5B,MAAM,QAAQ,IAAI;gBAClB,SAAS,QAAQ,OAAO;YAC1B,CAAC;QAED,2HAAA,CAAA,uBAAoB,CAAC,WAAW,CAAC,OAAO,eAAe,EAAE;QACzD,QAAQ,GAAG,CAAC,wCAAwC,eAAe,MAAM,EAAE;IAC7E,GAAG;QAAC;QAAQ;KAAS;IAErB;;GAEC,GACD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,IAAI,CAAC,QAAQ,iBAAiB;QAE9B,OAAO,2HAAA,CAAA,uBAAoB,CAAC,UAAU,CAAC,OAAO,eAAe;IAC/D,GAAG;QAAC;KAAO;IAEX,sDAAsD;IACtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,wBAAwB,CAAC;YAC7B,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,MAAM;YAClC,gBAAgB;QAClB;QAEA,OAAO,gBAAgB,CAAC,oBAAoB;QAC5C,OAAO;YACL,OAAO,mBAAmB,CAAC,oBAAoB;QACjD;IACF,GAAG;QAAC;KAAgB;IAEpB,2EAA2E;IAC3E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,wCAAwC;YACxC,WAAW;gBACT,IAAI,QAAQ,mBAAmB,YAAY,SAAS,MAAM,GAAG,GAAG;oBAC9D;gBACF;YACF,GAAG;QACL;QAEA,OAAO,gBAAgB,CAAC,SAAS;QACjC,OAAO,gBAAgB,CAAC,oBAAoB;YAC1C,IAAI,CAAC,SAAS,MAAM,EAAE;gBACpB;YACF;QACF;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,SAAS;YACpC,OAAO,mBAAmB,CAAC,oBAAoB;QACjD;IACF,GAAG;QAAC;QAAQ;QAAU;KAAwB;IAE9C,OAAO;QACL,UAAU,YAAY,EAAE;QACxB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,WAAW,aAAa;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 4321, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/separator.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Separator = React.forwardRef<\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\n>(\n  (\n    { className, orientation = \"horizontal\", decorative = true, ...props },\n    ref\n  ) => (\n    <SeparatorPrimitive.Root\n      ref={ref}\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"shrink-0 bg-border\",\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSeparator.displayName = SeparatorPrimitive.Root.displayName\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,8OAAC,qKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG,qKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4351, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/avatar.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,kKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4401, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/CommentInput.tsx"], "sourcesContent": ["/**\n * Comment input component for creating and editing comments\n */\n\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { Button } from \"./ui/button\";\nimport { Textarea } from \"./ui/textarea\";\nimport { Card, CardContent } from \"./ui/card\";\nimport { Send, X } from \"lucide-react\";\n\ninterface CommentInputProps {\n  placeholder?: string;\n  initialValue?: string;\n  onSubmit: (content: string) => Promise<any>;\n  onCancel?: () => void;\n  isSubmitting?: boolean;\n  autoFocus?: boolean;\n  showCancel?: boolean;\n  submitLabel?: string;\n  className?: string;\n}\n\nexport function CommentInput({\n  placeholder = \"Add a comment...\",\n  initialValue = \"\",\n  onSubmit,\n  onCancel,\n  isSubmitting = false,\n  autoFocus = false,\n  showCancel = false,\n  submitLabel = \"Comment\",\n  className = \"\",\n}: CommentInputProps) {\n  const [content, setContent] = useState(initialValue);\n  const [isExpanded, setIsExpanded] = useState(!!initialValue || autoFocus);\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\n\n  useEffect(() => {\n    if (autoFocus && textareaRef.current) {\n      textareaRef.current.focus();\n    }\n  }, [autoFocus]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    const trimmedContent = content.trim();\n    if (!trimmedContent) return;\n    \n    try {\n      await onSubmit(trimmedContent);\n      setContent(\"\");\n      setIsExpanded(false);\n    } catch (error) {\n      // Error handling is done in the parent component\n      console.error(\"Error submitting comment:\", error);\n    }\n  };\n\n  const handleCancel = () => {\n    setContent(initialValue);\n    setIsExpanded(!!initialValue);\n    onCancel?.();\n  };\n\n  const handleFocus = () => {\n    setIsExpanded(true);\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === \"Enter\" && (e.metaKey || e.ctrlKey)) {\n      e.preventDefault();\n      handleSubmit(e);\n    } else if (e.key === \"Escape\") {\n      e.preventDefault();\n      handleCancel();\n    }\n  };\n\n  return (\n    <Card className={`w-full ${className}`}>\n      <CardContent className=\"p-3\">\n        <form onSubmit={handleSubmit} className=\"space-y-3\">\n          <Textarea\n            ref={textareaRef}\n            value={content}\n            onChange={(e) => setContent(e.target.value)}\n            onFocus={handleFocus}\n            onKeyDown={handleKeyDown}\n            placeholder={placeholder}\n            className={`min-h-[80px] max-h-[200px] resize-none text-expandable ${\n              isExpanded ? \"min-h-[100px]\" : \"min-h-[40px]\"\n            }`}\n            disabled={isSubmitting}\n            style={{\n              wordWrap: 'break-word',\n              wordBreak: 'break-word',\n              overflowWrap: 'break-word',\n            }}\n          />\n          \n          {(isExpanded || content.trim()) && (\n            <div className=\"flex items-center justify-between\">\n              <div className=\"text-xs text-muted-foreground\">\n                Press Cmd+Enter to submit, Esc to cancel\n              </div>\n              <div className=\"flex items-center gap-2\">\n                {showCancel && (\n                  <Button\n                    type=\"button\"\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={handleCancel}\n                    disabled={isSubmitting}\n                  >\n                    <X className=\"h-4 w-4 mr-1\" />\n                    Cancel\n                  </Button>\n                )}\n                <Button\n                  type=\"submit\"\n                  size=\"sm\"\n                  disabled={!content.trim() || isSubmitting}\n                >\n                  <Send className=\"h-4 w-4 mr-1\" />\n                  {isSubmitting ? \"Submitting...\" : submitLabel}\n                </Button>\n              </div>\n            </div>\n          )}\n        </form>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAED;AACA;AACA;AACA;AACA;AAAA;;;;;;;AAcO,SAAS,aAAa,EAC3B,cAAc,kBAAkB,EAChC,eAAe,EAAE,EACjB,QAAQ,EACR,QAAQ,EACR,eAAe,KAAK,EACpB,YAAY,KAAK,EACjB,aAAa,KAAK,EAClB,cAAc,SAAS,EACvB,YAAY,EAAE,EACI;IAClB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,CAAC,gBAAgB;IAC/D,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IAEhD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,YAAY,OAAO,EAAE;YACpC,YAAY,OAAO,CAAC,KAAK;QAC3B;IACF,GAAG;QAAC;KAAU;IAEd,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,MAAM,iBAAiB,QAAQ,IAAI;QACnC,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,SAAS;YACf,WAAW;YACX,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,iDAAiD;YACjD,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,eAAe;QACnB,WAAW;QACX,cAAc,CAAC,CAAC;QAChB;IACF;IAEA,MAAM,cAAc;QAClB,cAAc;IAChB;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,GAAG;YACjD,EAAE,cAAc;YAChB,aAAa;QACf,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;YAC7B,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAC,OAAO,EAAE,WAAW;kBACpC,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,8OAAC,oIAAA,CAAA,WAAQ;wBACP,KAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;wBAC1C,SAAS;wBACT,WAAW;wBACX,aAAa;wBACb,WAAW,CAAC,uDAAuD,EACjE,aAAa,kBAAkB,gBAC/B;wBACF,UAAU;wBACV,OAAO;4BACL,UAAU;4BACV,WAAW;4BACX,cAAc;wBAChB;;;;;;oBAGD,CAAC,cAAc,QAAQ,IAAI,EAAE,mBAC5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAgC;;;;;;0CAG/C,8OAAC;gCAAI,WAAU;;oCACZ,4BACC,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU;;0DAEV,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAIlC,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAK;wCACL,UAAU,CAAC,QAAQ,IAAI,MAAM;;0DAE7B,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpD", "debugId": null}}, {"offset": {"line": 4576, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/expandable-text.tsx"], "sourcesContent": ["/**\n * Expandable text component with truncation and overflow handling\n */\n\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { Button } from \"./button\";\nimport { ChevronDown, ChevronUp } from \"lucide-react\";\nimport { cn } from \"../../lib/utils\";\n\ninterface ExpandableTextProps {\n  children: string | string[];\n  maxLines?: number;\n  maxHeight?: number;\n  className?: string;\n  showMoreText?: string;\n  showLessText?: string;\n  enableScrolling?: boolean;\n  maxScrollHeight?: number;\n}\n\nexport function ExpandableText({\n  children,\n  maxLines = 3,\n  maxHeight,\n  className = \"\",\n  showMoreText = \"Show more\",\n  showLessText = \"Show less\",\n  enableScrolling = false,\n  maxScrollHeight = 200,\n}: ExpandableTextProps) {\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [shouldTruncate, setShouldTruncate] = useState(false);\n  const [isOverflowing, setIsOverflowing] = useState(false);\n  const textRef = useRef<HTMLDivElement>(null);\n  const measureRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    const checkOverflow = () => {\n      if (!textRef.current || !measureRef.current) return;\n\n      const textElement = textRef.current;\n      const measureElement = measureRef.current;\n\n      // Check if content overflows based on line height\n      const lineHeight = parseInt(getComputedStyle(textElement).lineHeight);\n      const maxHeightPx = maxHeight || (lineHeight * maxLines);\n      \n      const contentHeight = measureElement.scrollHeight;\n      const shouldTruncateContent = contentHeight > maxHeightPx;\n      \n      setShouldTruncate(shouldTruncateContent);\n      \n      // Check if expanded content would need scrolling\n      if (enableScrolling && contentHeight > maxScrollHeight) {\n        setIsOverflowing(true);\n      }\n    };\n\n    checkOverflow();\n    \n    // Recheck on window resize\n    window.addEventListener('resize', checkOverflow);\n    return () => window.removeEventListener('resize', checkOverflow);\n  }, [children, maxLines, maxHeight, enableScrolling, maxScrollHeight]);\n\n  const toggleExpanded = () => {\n    setIsExpanded(!isExpanded);\n  };\n\n  const getDisplayStyle = () => {\n    if (!shouldTruncate || isExpanded) {\n      if (enableScrolling && isOverflowing && isExpanded) {\n        return {\n          maxHeight: `${maxScrollHeight}px`,\n          overflowY: 'auto' as const,\n        };\n      }\n      return {};\n    }\n\n    if (maxHeight) {\n      return {\n        maxHeight: `${maxHeight}px`,\n        overflow: 'hidden',\n      };\n    }\n\n    return {\n      display: '-webkit-box',\n      WebkitLineClamp: maxLines,\n      WebkitBoxOrient: 'vertical' as const,\n      overflow: 'hidden',\n    };\n  };\n\n  return (\n    <div className={cn(\"text-expandable\", className)}>\n      {/* Hidden element for measuring content height */}\n      <div\n        ref={measureRef}\n        className=\"absolute opacity-0 pointer-events-none whitespace-pre-wrap text-sm\"\n        style={{ width: textRef.current?.offsetWidth || 'auto' }}\n        aria-hidden=\"true\"\n      >\n        {children}\n      </div>\n\n      {/* Visible content */}\n      <div\n        ref={textRef}\n        className={cn(\n          \"whitespace-pre-wrap text-sm transition-all duration-200\",\n          enableScrolling && isOverflowing && isExpanded && \"text-scrollable\"\n        )}\n        style={getDisplayStyle()}\n      >\n        {children}\n      </div>\n\n      {/* Show more/less button */}\n      {shouldTruncate && (\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={toggleExpanded}\n          className=\"h-auto p-0 mt-1 text-xs text-muted-foreground hover:text-foreground\"\n        >\n          {isExpanded ? (\n            <>\n              <ChevronUp className=\"h-3 w-3 mr-1\" />\n              {showLessText}\n            </>\n          ) : (\n            <>\n              <ChevronDown className=\"h-3 w-3 mr-1\" />\n              {showMoreText}\n            </>\n          )}\n        </Button>\n      )}\n    </div>\n  );\n}\n\n/**\n * Specialized component for displaying quoted/selected text with proper overflow handling\n */\ninterface QuotedTextProps {\n  children: string;\n  maxLines?: number;\n  className?: string;\n}\n\nexport function QuotedText({ \n  children, \n  maxLines = 2, \n  className = \"\" \n}: QuotedTextProps) {\n  return (\n    <ExpandableText\n      maxLines={maxLines}\n      className={cn(\"text-sm italic text-muted-foreground\", className)}\n      showMoreText=\"Show full text\"\n      showLessText=\"Show less\"\n    >\n      \"{children}\"\n    </ExpandableText>\n  );\n}\n\n/**\n * Specialized component for comment content with scrolling support\n */\ninterface CommentContentProps {\n  children: string;\n  maxLines?: number;\n  enableScrolling?: boolean;\n  className?: string;\n}\n\nexport function CommentContent({ \n  children, \n  maxLines = 4, \n  enableScrolling = true,\n  className = \"\" \n}: CommentContentProps) {\n  return (\n    <ExpandableText\n      maxLines={maxLines}\n      enableScrolling={enableScrolling}\n      maxScrollHeight={150}\n      className={cn(\"text-sm\", className)}\n      showMoreText=\"Read more\"\n      showLessText=\"Show less\"\n    >\n      {children}\n    </ExpandableText>\n  );\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;AAED;AACA;AACA;AAAA;AACA;;;;;;AAaO,SAAS,eAAe,EAC7B,QAAQ,EACR,WAAW,CAAC,EACZ,SAAS,EACT,YAAY,EAAE,EACd,eAAe,WAAW,EAC1B,eAAe,WAAW,EAC1B,kBAAkB,KAAK,EACvB,kBAAkB,GAAG,EACD;IACpB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACvC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,WAAW,OAAO,EAAE;YAE7C,MAAM,cAAc,QAAQ,OAAO;YACnC,MAAM,iBAAiB,WAAW,OAAO;YAEzC,kDAAkD;YAClD,MAAM,aAAa,SAAS,iBAAiB,aAAa,UAAU;YACpE,MAAM,cAAc,aAAc,aAAa;YAE/C,MAAM,gBAAgB,eAAe,YAAY;YACjD,MAAM,wBAAwB,gBAAgB;YAE9C,kBAAkB;YAElB,iDAAiD;YACjD,IAAI,mBAAmB,gBAAgB,iBAAiB;gBACtD,iBAAiB;YACnB;QACF;QAEA;QAEA,2BAA2B;QAC3B,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;QAAU;QAAU;QAAW;QAAiB;KAAgB;IAEpE,MAAM,iBAAiB;QACrB,cAAc,CAAC;IACjB;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,kBAAkB,YAAY;YACjC,IAAI,mBAAmB,iBAAiB,YAAY;gBAClD,OAAO;oBACL,WAAW,GAAG,gBAAgB,EAAE,CAAC;oBACjC,WAAW;gBACb;YACF;YACA,OAAO,CAAC;QACV;QAEA,IAAI,WAAW;YACb,OAAO;gBACL,WAAW,GAAG,UAAU,EAAE,CAAC;gBAC3B,UAAU;YACZ;QACF;QAEA,OAAO;YACL,SAAS;YACT,iBAAiB;YACjB,iBAAiB;YACjB,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;;0BAEpC,8OAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBAAE,OAAO,QAAQ,OAAO,EAAE,eAAe;gBAAO;gBACvD,eAAY;0BAEX;;;;;;0BAIH,8OAAC;gBACC,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA,mBAAmB,iBAAiB,cAAc;gBAEpD,OAAO;0BAEN;;;;;;YAIF,gCACC,8OAAC,kIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS;gBACT,WAAU;0BAET,2BACC;;sCACE,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBACpB;;iDAGH;;sCACE,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBACtB;;;;;;;;;;;;;;AAOf;AAWO,SAAS,WAAW,EACzB,QAAQ,EACR,WAAW,CAAC,EACZ,YAAY,EAAE,EACE;IAChB,qBACE,8OAAC;QACC,UAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACtD,cAAa;QACb,cAAa;;YACd;YACG;YAAS;;;;;;;AAGjB;AAYO,SAAS,eAAe,EAC7B,QAAQ,EACR,WAAW,CAAC,EACZ,kBAAkB,IAAI,EACtB,YAAY,EAAE,EACM;IACpB,qBACE,8OAAC;QACC,UAAU;QACV,iBAAiB;QACjB,iBAAiB;QACjB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;QACzB,cAAa;QACb,cAAa;kBAEZ;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 4757, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/dropdown-menu.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={4}\n      className={cn(\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAEA;;;;;;AAEA,MAAM,eAAe,4KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,4KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,4KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,4KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,4KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,4KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGzC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,4KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,4KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,8OAAC,4KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAClC,4KAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,4KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 4954, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/CommentThread.tsx"], "sourcesContent": ["/**\n * Comment thread component showing a comment and its replies\n */\n\nimport React, { useState } from \"react\";\nimport { Card, CardContent } from \"./ui/card\";\nimport { <PERSON><PERSON> } from \"./ui/button\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"./ui/avatar\";\nimport { Badge } from \"./ui/badge\";\nimport { Separator } from \"./ui/separator\";\nimport { CommentInput } from \"./CommentInput\";\nimport { QuotedText, CommentContent } from \"./ui/expandable-text\";\nimport { getUserColor } from \"../lib/userColors\";\nimport { \n  MoreHorizontal, \n  Reply, \n  Edit2, \n  Trash2, \n  Check, \n  RotateCcw,\n  Quote\n} from \"lucide-react\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"./ui/dropdown-menu\";\n// Helper function to format relative time\nfunction formatDistanceToNow(date: Date, options?: { addSuffix?: boolean }) {\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n\n  if (diffInSeconds < 60) return options?.addSuffix ? \"just now\" : \"now\";\n  if (diffInSeconds < 3600) {\n    const minutes = Math.floor(diffInSeconds / 60);\n    return options?.addSuffix ? `${minutes} minute${minutes > 1 ? 's' : ''} ago` : `${minutes}m`;\n  }\n  if (diffInSeconds < 86400) {\n    const hours = Math.floor(diffInSeconds / 3600);\n    return options?.addSuffix ? `${hours} hour${hours > 1 ? 's' : ''} ago` : `${hours}h`;\n  }\n  const days = Math.floor(diffInSeconds / 86400);\n  return options?.addSuffix ? `${days} day${days > 1 ? 's' : ''} ago` : `${days}d`;\n}\nimport { Id } from \"../../convex/_generated/dataModel\";\n\ninterface CommentUser {\n  _id: string;\n  name?: string;\n  email?: string;\n  image?: string;\n}\n\ninterface CommentReply {\n  _id: string;\n  commentId: string;\n  userId: string;\n  content: string;\n  timestamp: number;\n  user?: CommentUser | null;\n}\n\ninterface Comment {\n  _id: string;\n  documentId: string;\n  userId: string;\n  content: string;\n  position: number;\n  selection: {\n    from: number;\n    to: number;\n  };\n  selectedText: string;\n  isResolved?: boolean;\n  timestamp: number;\n  user?: CommentUser | null;\n  replies?: CommentReply[];\n}\n\ninterface CommentThreadProps {\n  comment: Comment;\n  currentUserId?: string;\n  isSelected?: boolean;\n  canResolve?: boolean;\n  onUpdateComment: (commentId: Id<\"comments\">, content: string) => Promise<boolean>;\n  onDeleteComment: (commentId: Id<\"comments\">) => Promise<boolean>;\n  onCreateReply: (commentId: Id<\"comments\">, content: string) => Promise<any>;\n  onUpdateReply: (replyId: Id<\"commentReplies\">, content: string) => Promise<boolean>;\n  onDeleteReply: (replyId: Id<\"commentReplies\">) => Promise<boolean>;\n  onResolveComment: (commentId: Id<\"comments\">, isResolved: boolean) => Promise<boolean>;\n  onSelectComment: (commentId: string) => void;\n}\n\nexport function CommentThread({\n  comment,\n  currentUserId,\n  isSelected = false,\n  canResolve = false,\n  onUpdateComment,\n  onDeleteComment,\n  onCreateReply,\n  onUpdateReply,\n  onDeleteReply,\n  onResolveComment,\n  onSelectComment,\n}: CommentThreadProps) {\n  const [isEditing, setIsEditing] = useState(false);\n  const [showReplyInput, setShowReplyInput] = useState(false);\n  const [editingReplyId, setEditingReplyId] = useState<string | null>(null);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const isOwner = currentUserId === comment.userId;\n  const userName = comment.user?.name || comment.user?.email || \"Unknown User\";\n  const userInitials = userName.split(\" \").map(n => n[0]).join(\"\").toUpperCase().slice(0, 2);\n  const userColor = getUserColor(comment.userId);\n\n  const handleEditComment = async (content: string) => {\n    setIsSubmitting(true);\n    try {\n      const success = await onUpdateComment(comment._id as Id<\"comments\">, content);\n      if (success) {\n        setIsEditing(false);\n      }\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleDeleteComment = async () => {\n    if (window.confirm(\"Are you sure you want to delete this comment?\")) {\n      await onDeleteComment(comment._id as Id<\"comments\">);\n    }\n  };\n\n  const handleCreateReply = async (content: string) => {\n    setIsSubmitting(true);\n    try {\n      const success = await onCreateReply(comment._id as Id<\"comments\">, content);\n      if (success) {\n        setShowReplyInput(false);\n      }\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleEditReply = async (content: string) => {\n    if (!editingReplyId) return;\n    \n    setIsSubmitting(true);\n    try {\n      const success = await onUpdateReply(editingReplyId as Id<\"commentReplies\">, content);\n      if (success) {\n        setEditingReplyId(null);\n      }\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleDeleteReply = async (replyId: string) => {\n    if (window.confirm(\"Are you sure you want to delete this reply?\")) {\n      await onDeleteReply(replyId as Id<\"commentReplies\">);\n    }\n  };\n\n  const handleResolveToggle = async () => {\n    await onResolveComment(comment._id as Id<\"comments\">, !comment.isResolved);\n  };\n\n  return (\n    <Card\n      data-testid=\"comment-thread\"\n      data-comment-id={comment._id}\n      className={`w-full transition-all duration-200 ${\n        isSelected ? \"ring-2 shadow-md\" : \"\"\n      } ${comment.isResolved ? \"opacity-75\" : \"\"}`}\n      style={{\n        borderLeftColor: `rgb(${userColor.rgb})`,\n        borderLeftWidth: isSelected ? '4px' : '2px',\n        ...(isSelected && {\n          ringColor: `rgba(${userColor.rgb}, 0.3)`,\n        })\n      }}\n      onClick={() => onSelectComment(comment._id)}\n    >\n      <CardContent className=\"p-4\">\n        {/* Comment Header */}\n        <div className=\"flex items-start justify-between mb-3\">\n          <div className=\"flex items-center gap-3\">\n            <Avatar className=\"h-8 w-8\">\n              <AvatarImage src={comment.user?.image} />\n              <AvatarFallback\n                className=\"text-xs\"\n                style={{\n                  backgroundColor: `rgb(${userColor.rgb})`,\n                  color: userColor.text === 'text-white' ? 'white' : 'black'\n                }}\n              >\n                {userInitials}\n              </AvatarFallback>\n            </Avatar>\n            <div>\n              <div className=\"flex items-center gap-2\">\n                <span className=\"font-medium text-sm\">{userName}</span>\n                {comment.isResolved && (\n                  <Badge variant=\"secondary\" className=\"text-xs\">\n                    <Check className=\"h-3 w-3 mr-1\" />\n                    Resolved\n                  </Badge>\n                )}\n              </div>\n              <span className=\"text-xs text-muted-foreground\">\n                {formatDistanceToNow(new Date(comment.timestamp), { addSuffix: true })}\n              </span>\n            </div>\n          </div>\n          \n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button\n                data-testid=\"comment-options\"\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-8 w-8 p-0\"\n              >\n                <MoreHorizontal className=\"h-4 w-4\" />\n              </Button>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent align=\"end\">\n              <DropdownMenuItem onClick={() => setShowReplyInput(true)}>\n                <Reply className=\"h-4 w-4 mr-2\" />\n                Reply\n              </DropdownMenuItem>\n              {isOwner && (\n                <>\n                  <DropdownMenuItem onClick={() => setIsEditing(true)}>\n                    <Edit2 className=\"h-4 w-4 mr-2\" />\n                    Edit\n                  </DropdownMenuItem>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem \n                    onClick={handleDeleteComment}\n                    className=\"text-destructive\"\n                  >\n                    <Trash2 className=\"h-4 w-4 mr-2\" />\n                    Delete\n                  </DropdownMenuItem>\n                </>\n              )}\n              {canResolve && (\n                <>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem onClick={handleResolveToggle}>\n                    {comment.isResolved ? (\n                      <>\n                        <RotateCcw className=\"h-4 w-4 mr-2\" />\n                        Reopen\n                      </>\n                    ) : (\n                      <>\n                        <Check className=\"h-4 w-4 mr-2\" />\n                        Resolve\n                      </>\n                    )}\n                  </DropdownMenuItem>\n                </>\n              )}\n            </DropdownMenuContent>\n          </DropdownMenu>\n        </div>\n\n        {/* Original Text Quote */}\n        <div className=\"mb-3 p-2 bg-muted rounded-md border-l-4 border-muted-foreground/20\">\n          <div className=\"flex items-center gap-1 mb-1\">\n            <Quote className=\"h-3 w-3 text-muted-foreground\" />\n            <span className=\"text-xs text-muted-foreground\">Original text:</span>\n          </div>\n          <QuotedText maxLines={2}>\n            {comment.selectedText}\n          </QuotedText>\n        </div>\n\n        {/* Comment Content */}\n        {isEditing ? (\n          <CommentInput\n            initialValue={comment.content}\n            onSubmit={handleEditComment}\n            onCancel={() => setIsEditing(false)}\n            isSubmitting={isSubmitting}\n            autoFocus\n            showCancel\n            submitLabel=\"Update\"\n            placeholder=\"Edit your comment...\"\n          />\n        ) : (\n          <div className=\"mb-3\">\n            <CommentContent maxLines={4} enableScrolling={true}>\n              {comment.content}\n            </CommentContent>\n          </div>\n        )}\n\n        {/* Replies */}\n        {comment.replies && comment.replies.length > 0 && (\n          <>\n            <Separator className=\"my-3\" />\n            <div className=\"space-y-3\">\n              {comment.replies.map((reply) => (\n                <div key={reply._id} className=\"flex gap-3\">\n                  <Avatar className=\"h-6 w-6 mt-1\">\n                    <AvatarImage src={reply.user?.image} />\n                    <AvatarFallback className=\"text-xs\">\n                      {(reply.user?.name || reply.user?.email || \"U\").slice(0, 1).toUpperCase()}\n                    </AvatarFallback>\n                  </Avatar>\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center gap-2\">\n                        <span className=\"font-medium text-xs\">\n                          {reply.user?.name || reply.user?.email || \"Unknown User\"}\n                        </span>\n                        <span className=\"text-xs text-muted-foreground\">\n                          {formatDistanceToNow(new Date(reply.timestamp), { addSuffix: true })}\n                        </span>\n                      </div>\n                      {currentUserId === reply.userId && (\n                        <DropdownMenu>\n                          <DropdownMenuTrigger asChild>\n                            <Button variant=\"ghost\" size=\"sm\" className=\"h-6 w-6 p-0\">\n                              <MoreHorizontal className=\"h-3 w-3\" />\n                            </Button>\n                          </DropdownMenuTrigger>\n                          <DropdownMenuContent align=\"end\">\n                            <DropdownMenuItem onClick={() => setEditingReplyId(reply._id)}>\n                              <Edit2 className=\"h-3 w-3 mr-2\" />\n                              Edit\n                            </DropdownMenuItem>\n                            <DropdownMenuItem \n                              onClick={() => handleDeleteReply(reply._id)}\n                              className=\"text-destructive\"\n                            >\n                              <Trash2 className=\"h-3 w-3 mr-2\" />\n                              Delete\n                            </DropdownMenuItem>\n                          </DropdownMenuContent>\n                        </DropdownMenu>\n                      )}\n                    </div>\n                    \n                    {editingReplyId === reply._id ? (\n                      <div className=\"mt-2\">\n                        <CommentInput\n                          initialValue={reply.content}\n                          onSubmit={handleEditReply}\n                          onCancel={() => setEditingReplyId(null)}\n                          isSubmitting={isSubmitting}\n                          autoFocus\n                          showCancel\n                          submitLabel=\"Update\"\n                          placeholder=\"Edit your reply...\"\n                        />\n                      </div>\n                    ) : (\n                      <div className=\"mt-1\">\n                        <CommentContent maxLines={3} enableScrolling={true}>\n                          {reply.content}\n                        </CommentContent>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </>\n        )}\n\n        {/* Reply Input */}\n        {showReplyInput && (\n          <>\n            <Separator className=\"my-3\" />\n            <CommentInput\n              placeholder=\"Write a reply...\"\n              onSubmit={handleCreateReply}\n              onCancel={() => setShowReplyInput(false)}\n              isSubmitting={isSubmitting}\n              autoFocus\n              showCancel\n              submitLabel=\"Reply\"\n            />\n          </>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;;;;;;;;;;;;;AAOA,0CAA0C;AAC1C,SAAS,oBAAoB,IAAU,EAAE,OAAiC;IACxE,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI,OAAO,SAAS,YAAY,aAAa;IACjE,IAAI,gBAAgB,MAAM;QACxB,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAC3C,OAAO,SAAS,YAAY,GAAG,QAAQ,OAAO,EAAE,UAAU,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC,CAAC;IAC9F;IACA,IAAI,gBAAgB,OAAO;QACzB,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,SAAS,YAAY,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC;IACtF;IACA,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;IACxC,OAAO,SAAS,YAAY,GAAG,KAAK,IAAI,EAAE,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;AAClF;AAkDO,SAAS,cAAc,EAC5B,OAAO,EACP,aAAa,EACb,aAAa,KAAK,EAClB,aAAa,KAAK,EAClB,eAAe,EACf,eAAe,EACf,aAAa,EACb,aAAa,EACb,aAAa,EACb,gBAAgB,EAChB,eAAe,EACI;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,UAAU,kBAAkB,QAAQ,MAAM;IAChD,MAAM,WAAW,QAAQ,IAAI,EAAE,QAAQ,QAAQ,IAAI,EAAE,SAAS;IAC9D,MAAM,eAAe,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG;IACxF,MAAM,YAAY,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,MAAM;IAE7C,MAAM,oBAAoB,OAAO;QAC/B,gBAAgB;QAChB,IAAI;YACF,MAAM,UAAU,MAAM,gBAAgB,QAAQ,GAAG,EAAoB;YACrE,IAAI,SAAS;gBACX,aAAa;YACf;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,OAAO,OAAO,CAAC,kDAAkD;YACnE,MAAM,gBAAgB,QAAQ,GAAG;QACnC;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,gBAAgB;QAChB,IAAI;YACF,MAAM,UAAU,MAAM,cAAc,QAAQ,GAAG,EAAoB;YACnE,IAAI,SAAS;gBACX,kBAAkB;YACpB;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI,CAAC,gBAAgB;QAErB,gBAAgB;QAChB,IAAI;YACF,MAAM,UAAU,MAAM,cAAc,gBAAwC;YAC5E,IAAI,SAAS;gBACX,kBAAkB;YACpB;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,OAAO,OAAO,CAAC,gDAAgD;YACjE,MAAM,cAAc;QACtB;IACF;IAEA,MAAM,sBAAsB;QAC1B,MAAM,iBAAiB,QAAQ,GAAG,EAAoB,CAAC,QAAQ,UAAU;IAC3E;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QACH,eAAY;QACZ,mBAAiB,QAAQ,GAAG;QAC5B,WAAW,CAAC,mCAAmC,EAC7C,aAAa,qBAAqB,GACnC,CAAC,EAAE,QAAQ,UAAU,GAAG,eAAe,IAAI;QAC5C,OAAO;YACL,iBAAiB,CAAC,IAAI,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;YACxC,iBAAiB,aAAa,QAAQ;YACtC,GAAI,cAAc;gBAChB,WAAW,CAAC,KAAK,EAAE,UAAU,GAAG,CAAC,MAAM,CAAC;YAC1C,CAAC;QACH;QACA,SAAS,IAAM,gBAAgB,QAAQ,GAAG;kBAE1C,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;8BAErB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,8OAAC,kIAAA,CAAA,cAAW;4CAAC,KAAK,QAAQ,IAAI,EAAE;;;;;;sDAChC,8OAAC,kIAAA,CAAA,iBAAc;4CACb,WAAU;4CACV,OAAO;gDACL,iBAAiB,CAAC,IAAI,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;gDACxC,OAAO,UAAU,IAAI,KAAK,eAAe,UAAU;4CACrD;sDAEC;;;;;;;;;;;;8CAGL,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAuB;;;;;;gDACtC,QAAQ,UAAU,kBACjB,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;;sEACnC,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAKxC,8OAAC;4CAAK,WAAU;sDACb,oBAAoB,IAAI,KAAK,QAAQ,SAAS,GAAG;gDAAE,WAAW;4CAAK;;;;;;;;;;;;;;;;;;sCAK1E,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,eAAY;wCACZ,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,8OAAC,gNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAG9B,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAM;;sDACzB,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,kBAAkB;;8DACjD,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;wCAGnC,yBACC;;8DACE,8OAAC,4IAAA,CAAA,mBAAgB;oDAAC,SAAS,IAAM,aAAa;;sEAC5C,8OAAC,kMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGpC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;8DACtB,8OAAC,4IAAA,CAAA,mBAAgB;oDACf,SAAS;oDACT,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;wCAKxC,4BACC;;8DACE,8OAAC,4IAAA,CAAA,wBAAqB;;;;;8DACtB,8OAAC,4IAAA,CAAA,mBAAgB;oDAAC,SAAS;8DACxB,QAAQ,UAAU,iBACjB;;0EACE,8OAAC,gNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DAAiB;;qFAIxC;;0EACE,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAYlD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;sCAElD,8OAAC,8IAAA,CAAA,aAAU;4BAAC,UAAU;sCACnB,QAAQ,YAAY;;;;;;;;;;;;gBAKxB,0BACC,8OAAC,kIAAA,CAAA,eAAY;oBACX,cAAc,QAAQ,OAAO;oBAC7B,UAAU;oBACV,UAAU,IAAM,aAAa;oBAC7B,cAAc;oBACd,SAAS;oBACT,UAAU;oBACV,aAAY;oBACZ,aAAY;;;;;yCAGd,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,8IAAA,CAAA,iBAAc;wBAAC,UAAU;wBAAG,iBAAiB;kCAC3C,QAAQ,OAAO;;;;;;;;;;;gBAMrB,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,mBAC3C;;sCACE,8OAAC,qIAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,sBACpB,8OAAC;oCAAoB,WAAU;;sDAC7B,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,MAAM,IAAI,EAAE;;;;;;8DAC9B,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB,CAAC,MAAM,IAAI,EAAE,QAAQ,MAAM,IAAI,EAAE,SAAS,GAAG,EAAE,KAAK,CAAC,GAAG,GAAG,WAAW;;;;;;;;;;;;sDAG3E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EACb,MAAM,IAAI,EAAE,QAAQ,MAAM,IAAI,EAAE,SAAS;;;;;;8EAE5C,8OAAC;oEAAK,WAAU;8EACb,oBAAoB,IAAI,KAAK,MAAM,SAAS,GAAG;wEAAE,WAAW;oEAAK;;;;;;;;;;;;wDAGrE,kBAAkB,MAAM,MAAM,kBAC7B,8OAAC,4IAAA,CAAA,eAAY;;8EACX,8OAAC,4IAAA,CAAA,sBAAmB;oEAAC,OAAO;8EAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;wEAAK,WAAU;kFAC1C,cAAA,8OAAC,gNAAA,CAAA,iBAAc;4EAAC,WAAU;;;;;;;;;;;;;;;;8EAG9B,8OAAC,4IAAA,CAAA,sBAAmB;oEAAC,OAAM;;sFACzB,8OAAC,4IAAA,CAAA,mBAAgB;4EAAC,SAAS,IAAM,kBAAkB,MAAM,GAAG;;8FAC1D,8OAAC,kMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGpC,8OAAC,4IAAA,CAAA,mBAAgB;4EACf,SAAS,IAAM,kBAAkB,MAAM,GAAG;4EAC1C,WAAU;;8FAEV,8OAAC,0MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;gDAQ5C,mBAAmB,MAAM,GAAG,iBAC3B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kIAAA,CAAA,eAAY;wDACX,cAAc,MAAM,OAAO;wDAC3B,UAAU;wDACV,UAAU,IAAM,kBAAkB;wDAClC,cAAc;wDACd,SAAS;wDACT,UAAU;wDACV,aAAY;wDACZ,aAAY;;;;;;;;;;yEAIhB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8IAAA,CAAA,iBAAc;wDAAC,UAAU;wDAAG,iBAAiB;kEAC3C,MAAM,OAAO;;;;;;;;;;;;;;;;;;mCAzDd,MAAM,GAAG;;;;;;;;;;;;gBAqE1B,gCACC;;sCACE,8OAAC,qIAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC,kIAAA,CAAA,eAAY;4BACX,aAAY;4BACZ,UAAU;4BACV,UAAU,IAAM,kBAAkB;4BAClC,cAAc;4BACd,SAAS;4BACT,UAAU;4BACV,aAAY;;;;;;;;;;;;;;;;;;;AAO1B", "debugId": null}}, {"offset": {"line": 5635, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/CommentSidebar.tsx"], "sourcesContent": ["/**\n * Comment sidebar component showing all comments for a document\n */\n\nimport React, { useState, useMemo } from \"react\";\nimport { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from \"./ui/card\";\nimport { <PERSON><PERSON> } from \"./ui/button\";\nimport { Badge } from \"./ui/badge\";\nimport { Separator } from \"./ui/separator\";\nimport { CommentThread } from \"./CommentThread\";\nimport { CommentInput } from \"./CommentInput\";\nimport { \n  MessageSquare, \n  X, \n  Filter,\n  CheckCircle,\n  Clock,\n  Plus\n} from \"lucide-react\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"./ui/dropdown-menu\";\nimport { Id } from \"../../convex/_generated/dataModel\";\n\ninterface CommentUser {\n  _id: string;\n  name?: string;\n  email?: string;\n  image?: string;\n}\n\ninterface CommentReply {\n  _id: string;\n  commentId: string;\n  userId: string;\n  content: string;\n  timestamp: number;\n  user?: CommentUser | null;\n}\n\ninterface Comment {\n  _id: string;\n  documentId: string;\n  userId: string;\n  content: string;\n  position: number;\n  selection: {\n    from: number;\n    to: number;\n  };\n  selectedText: string;\n  isResolved?: boolean;\n  timestamp: number;\n  user?: CommentUser | null;\n  replies?: CommentReply[];\n}\n\ntype FilterType = \"all\" | \"active\" | \"resolved\";\n\ninterface CommentSidebarProps {\n  comments: Comment[];\n  currentUserId?: string;\n  selectedCommentId?: string;\n  isVisible: boolean;\n  canResolve?: boolean;\n  onClose: () => void;\n  onCreateComment: (content: string) => Promise<any>;\n  onUpdateComment: (commentId: Id<\"comments\">, content: string) => Promise<boolean>;\n  onDeleteComment: (commentId: Id<\"comments\">) => Promise<boolean>;\n  onCreateReply: (commentId: Id<\"comments\">, content: string) => Promise<any>;\n  onUpdateReply: (replyId: Id<\"commentReplies\">, content: string) => Promise<boolean>;\n  onDeleteReply: (replyId: Id<\"commentReplies\">) => Promise<boolean>;\n  onResolveComment: (commentId: Id<\"comments\">, isResolved: boolean) => Promise<boolean>;\n  onSelectComment: (commentId: string) => void;\n}\n\nexport function CommentSidebar({\n  comments,\n  currentUserId,\n  selectedCommentId,\n  isVisible,\n  canResolve = false,\n  onClose,\n  onCreateComment,\n  onUpdateComment,\n  onDeleteComment,\n  onCreateReply,\n  onUpdateReply,\n  onDeleteReply,\n  onResolveComment,\n  onSelectComment,\n}: CommentSidebarProps) {\n  const [filter, setFilter] = useState<FilterType>(\"all\");\n  const [showNewCommentInput, setShowNewCommentInput] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // Filter comments based on current filter\n  const filteredComments = useMemo(() => {\n    switch (filter) {\n      case \"active\":\n        return comments.filter(comment => !comment.isResolved);\n      case \"resolved\":\n        return comments.filter(comment => comment.isResolved);\n      default:\n        return comments;\n    }\n  }, [comments, filter]);\n\n  // Sort comments by timestamp (newest first)\n  const sortedComments = useMemo(() => {\n    return [...filteredComments].sort((a, b) => b.timestamp - a.timestamp);\n  }, [filteredComments]);\n\n  // Count comments by status\n  const commentCounts = useMemo(() => {\n    const active = comments.filter(c => !c.isResolved).length;\n    const resolved = comments.filter(c => c.isResolved).length;\n    return { active, resolved, total: comments.length };\n  }, [comments]);\n\n  const handleCreateComment = async (content: string) => {\n    setIsSubmitting(true);\n    try {\n      const success = await onCreateComment(content);\n      if (success) {\n        setShowNewCommentInput(false);\n      }\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <div\n      data-testid=\"comment-sidebar\"\n      className=\"fixed right-0 top-0 h-full w-96 max-w-[90vw] sm:max-w-[400px] bg-background border-l border-border shadow-lg z-50 flex flex-col\"\n    >\n      {/* Header */}\n      <CardHeader className=\"flex-shrink-0 pb-3\">\n        <div className=\"flex items-center justify-between\">\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\n            <MessageSquare className=\"h-5 w-5\" />\n            Comments\n            <Badge variant=\"secondary\" className=\"ml-2\">\n              {commentCounts.total}\n            </Badge>\n          </CardTitle>\n          <Button variant=\"ghost\" size=\"sm\" onClick={onClose}>\n            <X className=\"h-4 w-4\" />\n          </Button>\n        </div>\n        \n        {/* Filter and Stats */}\n        <div className=\"flex items-center justify-between mt-3\">\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button variant=\"outline\" size=\"sm\" className=\"gap-2\">\n                <Filter className=\"h-4 w-4\" />\n                {filter === \"all\" ? \"All\" : filter === \"active\" ? \"Active\" : \"Resolved\"}\n              </Button>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent align=\"start\">\n              <DropdownMenuItem onClick={() => setFilter(\"all\")}>\n                All ({commentCounts.total})\n              </DropdownMenuItem>\n              <DropdownMenuItem onClick={() => setFilter(\"active\")}>\n                <Clock className=\"h-4 w-4 mr-2\" />\n                Active ({commentCounts.active})\n              </DropdownMenuItem>\n              <DropdownMenuItem onClick={() => setFilter(\"resolved\")}>\n                <CheckCircle className=\"h-4 w-4 mr-2\" />\n                Resolved ({commentCounts.resolved})\n              </DropdownMenuItem>\n            </DropdownMenuContent>\n          </DropdownMenu>\n          \n          <Button \n            size=\"sm\" \n            onClick={() => setShowNewCommentInput(true)}\n            disabled={showNewCommentInput}\n          >\n            <Plus className=\"h-4 w-4 mr-1\" />\n            New\n          </Button>\n        </div>\n      </CardHeader>\n\n      {/* Content */}\n      <div className=\"flex-1 overflow-hidden flex flex-col\">\n        <CardContent className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n          {/* New Comment Input */}\n          {showNewCommentInput && (\n            <div className=\"mb-4\">\n              <CommentInput\n                placeholder=\"Select text in the editor and add your comment...\"\n                onSubmit={handleCreateComment}\n                onCancel={() => setShowNewCommentInput(false)}\n                isSubmitting={isSubmitting}\n                autoFocus\n                showCancel\n                submitLabel=\"Add Comment\"\n              />\n            </div>\n          )}\n\n          {/* Comments List */}\n          {sortedComments.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <MessageSquare className=\"h-12 w-12 mx-auto text-muted-foreground mb-4\" />\n              <h3 className=\"font-medium text-muted-foreground mb-2\">\n                {filter === \"all\" \n                  ? \"No comments yet\" \n                  : filter === \"active\" \n                    ? \"No active comments\" \n                    : \"No resolved comments\"\n                }\n              </h3>\n              <p className=\"text-sm text-muted-foreground\">\n                {filter === \"all\" \n                  ? \"Select text in the editor and right-click to add the first comment.\"\n                  : filter === \"active\"\n                    ? \"All comments have been resolved.\"\n                    : \"No comments have been resolved yet.\"\n                }\n              </p>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {sortedComments.map((comment) => (\n                <CommentThread\n                  data-testid=\"comment-thread\"\n                  key={comment._id}\n                  comment={comment}\n                  currentUserId={currentUserId}\n                  isSelected={selectedCommentId === comment._id}\n                  canResolve={canResolve}\n                  onUpdateComment={onUpdateComment}\n                  onDeleteComment={onDeleteComment}\n                  onCreateReply={onCreateReply}\n                  onUpdateReply={onUpdateReply}\n                  onDeleteReply={onDeleteReply}\n                  onResolveComment={onResolveComment}\n                  onSelectComment={onSelectComment}\n                />\n              ))}\n            </div>\n          )}\n        </CardContent>\n      </div>\n\n      {/* Footer with quick stats */}\n      {commentCounts.total > 0 && (\n        <>\n          <Separator />\n          <div className=\"p-4 bg-muted/30\">\n            <div className=\"flex items-center justify-between text-sm text-muted-foreground\">\n              <span>{commentCounts.active} active</span>\n              <span>{commentCounts.resolved} resolved</span>\n              <span>{commentCounts.total} total</span>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;;;;;;;;;;;AA4DO,SAAS,eAAe,EAC7B,QAAQ,EACR,aAAa,EACb,iBAAiB,EACjB,SAAS,EACT,aAAa,KAAK,EAClB,OAAO,EACP,eAAe,EACf,eAAe,EACf,eAAe,EACf,aAAa,EACb,aAAa,EACb,aAAa,EACb,gBAAgB,EAChB,eAAe,EACK;IACpB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,0CAA0C;IAC1C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,OAAQ;YACN,KAAK;gBACH,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,UAAU;YACvD,KAAK;gBACH,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,UAAU;YACtD;gBACE,OAAO;QACX;IACF,GAAG;QAAC;QAAU;KAAO;IAErB,4CAA4C;IAC5C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,OAAO;eAAI;SAAiB,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;IACvE,GAAG;QAAC;KAAiB;IAErB,2BAA2B;IAC3B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,MAAM,SAAS,SAAS,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,UAAU,EAAE,MAAM;QACzD,MAAM,WAAW,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM;QAC1D,OAAO;YAAE;YAAQ;YAAU,OAAO,SAAS,MAAM;QAAC;IACpD,GAAG;QAAC;KAAS;IAEb,MAAM,sBAAsB,OAAO;QACjC,gBAAgB;QAChB,IAAI;YACF,MAAM,UAAU,MAAM,gBAAgB;YACtC,IAAI,SAAS;gBACX,uBAAuB;YACzB;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QACC,eAAY;QACZ,WAAU;;0BAGV,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAY;kDAErC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAClC,cAAc,KAAK;;;;;;;;;;;;0CAGxB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,SAAS;0CACzC,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4IAAA,CAAA,eAAY;;kDACX,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;;8DAC5C,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDACjB,WAAW,QAAQ,QAAQ,WAAW,WAAW,WAAW;;;;;;;;;;;;kDAGjE,8OAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAM;;0DACzB,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,SAAS,IAAM,UAAU;;oDAAQ;oDAC3C,cAAc,KAAK;oDAAC;;;;;;;0DAE5B,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,SAAS,IAAM,UAAU;;kEACzC,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;oDACzB,cAAc,MAAM;oDAAC;;;;;;;0DAEhC,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,SAAS,IAAM,UAAU;;kEACzC,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;oDAC7B,cAAc,QAAQ;oDAAC;;;;;;;;;;;;;;;;;;;0CAKxC,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS,IAAM,uBAAuB;gCACtC,UAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;wBAEpB,qCACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,eAAY;gCACX,aAAY;gCACZ,UAAU;gCACV,UAAU,IAAM,uBAAuB;gCACvC,cAAc;gCACd,SAAS;gCACT,UAAU;gCACV,aAAY;;;;;;;;;;;wBAMjB,eAAe,MAAM,KAAK,kBACzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;oCAAG,WAAU;8CACX,WAAW,QACR,oBACA,WAAW,WACT,uBACA;;;;;;8CAGR,8OAAC;oCAAE,WAAU;8CACV,WAAW,QACR,wEACA,WAAW,WACT,qCACA;;;;;;;;;;;iDAKV,8OAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,wBACnB,8OAAC,mIAAA,CAAA,gBAAa;oCACZ,eAAY;oCAEZ,SAAS;oCACT,eAAe;oCACf,YAAY,sBAAsB,QAAQ,GAAG;oCAC7C,YAAY;oCACZ,iBAAiB;oCACjB,iBAAiB;oCACjB,eAAe;oCACf,eAAe;oCACf,eAAe;oCACf,kBAAkB;oCAClB,iBAAiB;mCAXZ,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;YAoB3B,cAAc,KAAK,GAAG,mBACrB;;kCACE,8OAAC,qIAAA,CAAA,YAAS;;;;;kCACV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;wCAAM,cAAc,MAAM;wCAAC;;;;;;;8CAC5B,8OAAC;;wCAAM,cAAc,QAAQ;wCAAC;;;;;;;8CAC9B,8OAAC;;wCAAM,cAAc,KAAK;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzC", "debugId": null}}, {"offset": {"line": 6060, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/CommentPopover.tsx"], "sourcesContent": ["/**\n * Comment popover component for quick comment creation\n */\n\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { Card, CardContent } from \"./ui/card\";\nimport { CommentInput } from \"./CommentInput\";\nimport { Button } from \"./ui/button\";\nimport { MessageSquare, X } from \"lucide-react\";\nimport { QuotedText } from \"./ui/expandable-text\";\n\ninterface CommentPopoverProps {\n  isVisible: boolean;\n  position: { x: number; y: number };\n  selectedText: string;\n  onCreateComment: (content: string) => Promise<any>;\n  onClose: () => void;\n}\n\nexport function CommentPopover({\n  isVisible,\n  position,\n  selectedText,\n  onCreateComment,\n  onClose,\n}: CommentPopoverProps) {\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const popoverRef = useRef<HTMLDivElement>(null);\n\n  // <PERSON>le click outside to close\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {\n        onClose();\n      }\n    };\n\n    if (isVisible) {\n      document.addEventListener(\"mousedown\", handleClickOutside);\n      return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n    }\n  }, [isVisible, onClose]);\n\n  // Handle escape key\n  useEffect(() => {\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === \"Escape\") {\n        onClose();\n      }\n    };\n\n    if (isVisible) {\n      document.addEventListener(\"keydown\", handleEscape);\n      return () => document.removeEventListener(\"keydown\", handleEscape);\n    }\n  }, [isVisible, onClose]);\n\n  const handleSubmit = async (content: string) => {\n    setIsSubmitting(true);\n    try {\n      const success = await onCreateComment(content);\n      if (success) {\n        onClose();\n      }\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  if (!isVisible) return null;\n\n  // Calculate position to keep popover in viewport with responsive sizing\n  const popoverWidth = Math.min(400, window.innerWidth * 0.9); // Max 400px or 90% of viewport\n  const popoverHeight = 350; // Approximate popover height\n\n  const adjustedPosition = {\n    x: Math.max(10, Math.min(position.x, window.innerWidth - popoverWidth - 10)),\n    y: Math.max(10, Math.min(position.y, window.innerHeight - popoverHeight - 10)),\n  };\n\n  return (\n    <div\n      ref={popoverRef}\n      data-testid=\"comment-popover\"\n      className=\"fixed z-50 w-96 max-w-[90vw] max-h-[80vh]\"\n      style={{\n        left: adjustedPosition.x,\n        top: adjustedPosition.y,\n      }}\n    >\n      <Card className=\"shadow-lg border-2 flex flex-col max-h-full\">\n        <CardContent className=\"p-4 overflow-y-auto text-scrollable\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between mb-3\">\n            <div className=\"flex items-center gap-2\">\n              <MessageSquare className=\"h-4 w-4 text-blue-600\" />\n              <span className=\"font-medium text-sm\">Add Comment</span>\n            </div>\n            <Button variant=\"ghost\" size=\"sm\" onClick={onClose} className=\"h-6 w-6 p-0\">\n              <X className=\"h-3 w-3\" />\n            </Button>\n          </div>\n\n          {/* Selected Text Preview */}\n          {selectedText && (\n            <div className=\"mb-3 p-2 bg-muted rounded-md border-l-4 border-blue-500\">\n              <div className=\"text-xs text-muted-foreground mb-1\">Selected text:</div>\n              <QuotedText maxLines={3}>\n                {selectedText}\n              </QuotedText>\n            </div>\n          )}\n\n          {/* Comment Input */}\n          <CommentInput\n            placeholder=\"What do you think about this?\"\n            onSubmit={handleSubmit}\n            onCancel={onClose}\n            isSubmitting={isSubmitting}\n            autoFocus\n            showCancel\n            submitLabel=\"Add Comment\"\n          />\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAED;AACA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;AAUO,SAAS,eAAe,EAC7B,SAAS,EACT,QAAQ,EACR,YAAY,EACZ,eAAe,EACf,OAAO,EACa;IACpB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,WAAW,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC5E;YACF;QACF;QAEA,IAAI,WAAW;YACb,SAAS,gBAAgB,CAAC,aAAa;YACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;QACzD;IACF,GAAG;QAAC;QAAW;KAAQ;IAEvB,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,MAAM,GAAG,KAAK,UAAU;gBAC1B;YACF;QACF;QAEA,IAAI,WAAW;YACb,SAAS,gBAAgB,CAAC,WAAW;YACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;QACvD;IACF,GAAG;QAAC;QAAW;KAAQ;IAEvB,MAAM,eAAe,OAAO;QAC1B,gBAAgB;QAChB,IAAI;YACF,MAAM,UAAU,MAAM,gBAAgB;YACtC,IAAI,SAAS;gBACX;YACF;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,wEAAwE;IACxE,MAAM,eAAe,KAAK,GAAG,CAAC,KAAK,OAAO,UAAU,GAAG,MAAM,+BAA+B;IAC5F,MAAM,gBAAgB,KAAK,6BAA6B;IAExD,MAAM,mBAAmB;QACvB,GAAG,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,SAAS,CAAC,EAAE,OAAO,UAAU,GAAG,eAAe;QACxE,GAAG,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,SAAS,CAAC,EAAE,OAAO,WAAW,GAAG,gBAAgB;IAC5E;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,eAAY;QACZ,WAAU;QACV,OAAO;YACL,MAAM,iBAAiB,CAAC;YACxB,KAAK,iBAAiB,CAAC;QACzB;kBAEA,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;0CAExC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,SAAS;gCAAS,WAAU;0CAC5D,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAKhB,8BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAqC;;;;;;0CACpD,8OAAC,8IAAA,CAAA,aAAU;gCAAC,UAAU;0CACnB;;;;;;;;;;;;kCAMP,8OAAC,kIAAA,CAAA,eAAY;wBACX,aAAY;wBACZ,UAAU;wBACV,UAAU;wBACV,cAAc;wBACd,SAAS;wBACT,UAAU;wBACV,aAAY;;;;;;;;;;;;;;;;;;;;;;AAMxB", "debugId": null}}, {"offset": {"line": 6256, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/DocumentSharingModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from \"react\";\nimport { useMutation, useQuery } from \"convex/react\";\nimport { api } from \"../../convex/_generated/api\";\nimport { Id } from \"../../convex/_generated/dataModel\";\nimport { X, Share2, User<PERSON><PERSON>, Trash2, Crown, Eye, Edit } from \"lucide-react\";\nimport { toast } from \"sonner\";\n\ninterface DocumentSharingModalProps {\n  documentId: Id<\"documents\">;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function DocumentSharingModal({ documentId, isOpen, onClose }: DocumentSharingModalProps) {\n  const [email, setEmail] = useState(\"\");\n  const [permission, setPermission] = useState<\"read\" | \"write\">(\"read\");\n  const [isSharing, setIsSharing] = useState(false);\n\n  const shares = useQuery(api.sharing.getDocumentShares, { documentId });\n  const document = useQuery(api.documents.getDocument, { id: documentId });\n  const loggedInUser = useQuery(api.auth.loggedInUser);\n  const shareDocument = useMutation(api.sharing.shareDocument);\n  const removeShare = useMutation(api.sharing.removeShare);\n\n  const handleShare = async () => {\n    if (!email.trim()) return;\n\n    setIsSharing(true);\n    try {\n      await shareDocument({\n        documentId,\n        userEmail: email.trim(),\n        permission,\n      });\n      setEmail(\"\");\n      toast.success(\"Document shared successfully!\");\n    } catch (error) {\n      toast.error(error instanceof Error ? error.message : \"Failed to share document\");\n    } finally {\n      setIsSharing(false);\n    }\n  };\n\n  const handleRemoveShare = async (userId: Id<\"users\">) => {\n    try {\n      await removeShare({ documentId, userId });\n      toast.success(\"Access removed successfully!\");\n    } catch (error) {\n      toast.error(\"Failed to remove access\");\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg shadow-xl w-full max-w-md mx-4\">\n        <div className=\"flex items-center justify-between p-4 border-b\">\n          <div className=\"flex items-center gap-2\">\n            <Share2 size={20} className=\"text-blue-600\" />\n            <h2 className=\"text-lg font-semibold\">Share Document</h2>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"p-1 hover:bg-gray-100 rounded\"\n          >\n            <X size={20} />\n          </button>\n        </div>\n\n        <div className=\"p-4 space-y-4\">\n          {/* Document Owner */}\n          {loggedInUser && document && (\n            <div className=\"p-3 bg-yellow-50 border border-yellow-200 rounded-lg\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\">\n                  <Crown size={16} className=\"text-yellow-600\" />\n                </div>\n                <div>\n                  <p className=\"text-sm font-medium text-yellow-800\">Document Owner</p>\n                  <p className=\"text-sm text-yellow-700\">\n                    {loggedInUser.name || loggedInUser.email}\n                  </p>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Share with new user */}\n          <div className=\"space-y-3\">\n            <h3 className=\"font-medium text-gray-900\">Add people</h3>\n            <div className=\"flex gap-2\">\n              <input\n                type=\"email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                placeholder=\"Enter email address...\"\n                className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\"\n                onKeyDown={(e) => {\n                  if (e.key === \"Enter\") handleShare();\n                }}\n              />\n              <select\n                value={permission}\n                onChange={(e) => setPermission(e.target.value as \"read\" | \"write\")}\n                className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\"\n              >\n                <option value=\"read\">Can view</option>\n                <option value=\"write\">Can edit</option>\n              </select>\n            </div>\n            <button\n              onClick={handleShare}\n              disabled={!email.trim() || isSharing}\n              className=\"w-full flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm\"\n            >\n              <UserPlus size={16} />\n              {isSharing ? \"Sharing...\" : \"Share\"}\n            </button>\n          </div>\n\n          {/* Current shares */}\n          {shares && shares.length > 0 && (\n            <div className=\"space-y-3\">\n              <h3 className=\"font-medium text-gray-900\">People with access</h3>\n              <div className=\"space-y-2\">\n                {shares.map((share) => (\n                  <div\n                    key={share._id}\n                    className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\"\n                  >\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n                        <span className=\"text-sm font-medium text-blue-600\">\n                          {share.user?.name?.charAt(0) || share.user?.email?.charAt(0) || \"?\"}\n                        </span>\n                      </div>\n                      <div>\n                        <p className=\"text-sm font-medium text-gray-900\">\n                          {share.user?.name || \"Unknown User\"}\n                        </p>\n                        <p className=\"text-xs text-gray-500\">{share.user?.email}</p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"flex items-center gap-1 text-xs text-gray-600\">\n                        {share.permission === \"read\" ? (\n                          <>\n                            <Eye size={12} />\n                            <span>Can view</span>\n                          </>\n                        ) : (\n                          <>\n                            <Edit size={12} />\n                            <span>Can edit</span>\n                          </>\n                        )}\n                      </div>\n                      <button\n                        onClick={() => handleRemoveShare(share.sharedWithUserId)}\n                        className=\"p-1 hover:bg-red-100 rounded text-red-600\"\n                        title=\"Remove access\"\n                      >\n                        <Trash2 size={14} />\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAPA;;;;;;;AAeO,SAAS,qBAAqB,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAA6B;IAC7F,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,SAAS,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,iBAAiB,EAAE;QAAE;IAAW;IACpE,MAAM,WAAW,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,SAAS,CAAC,WAAW,EAAE;QAAE,IAAI;IAAW;IACtE,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,IAAI,CAAC,YAAY;IACnD,MAAM,gBAAgB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,aAAa;IAC3D,MAAM,cAAc,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,WAAW;IAEvD,MAAM,cAAc;QAClB,IAAI,CAAC,MAAM,IAAI,IAAI;QAEnB,aAAa;QACb,IAAI;YACF,MAAM,cAAc;gBAClB;gBACA,WAAW,MAAM,IAAI;gBACrB;YACF;YACA,SAAS;YACT,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACvD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,YAAY;gBAAE;gBAAY;YAAO;YACvC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,SAAM;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CAC5B,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;;;;;;;sCAExC,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAIb,8OAAC;oBAAI,WAAU;;wBAEZ,gBAAgB,0BACf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;kDAE7B,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAsC;;;;;;0DACnD,8OAAC;gDAAE,WAAU;0DACV,aAAa,IAAI,IAAI,aAAa,KAAK;;;;;;;;;;;;;;;;;;;;;;;sCAQlD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4B;;;;;;8CAC1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,aAAY;4CACZ,WAAU;4CACV,WAAW,CAAC;gDACV,IAAI,EAAE,GAAG,KAAK,SAAS;4CACzB;;;;;;sDAEF,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;;;;;;;;;;;;;8CAG1B,8OAAC;oCACC,SAAS;oCACT,UAAU,CAAC,MAAM,IAAI,MAAM;oCAC3B,WAAU;;sDAEV,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;wCACf,YAAY,eAAe;;;;;;;;;;;;;wBAK/B,UAAU,OAAO,MAAM,GAAG,mBACzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4B;;;;;;8CAC1C,8OAAC;oCAAI,WAAU;8CACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;4CAEC,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EACb,MAAM,IAAI,EAAE,MAAM,OAAO,MAAM,MAAM,IAAI,EAAE,OAAO,OAAO,MAAM;;;;;;;;;;;sEAGpE,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EACV,MAAM,IAAI,EAAE,QAAQ;;;;;;8EAEvB,8OAAC;oEAAE,WAAU;8EAAyB,MAAM,IAAI,EAAE;;;;;;;;;;;;;;;;;;8DAGtD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,MAAM,UAAU,KAAK,uBACpB;;kFACE,8OAAC,gMAAA,CAAA,MAAG;wEAAC,MAAM;;;;;;kFACX,8OAAC;kFAAK;;;;;;;6FAGR;;kFACE,8OAAC,2MAAA,CAAA,OAAI;wEAAC,MAAM;;;;;;kFACZ,8OAAC;kFAAK;;;;;;;;;;;;;sEAIZ,8OAAC;4DACC,SAAS,IAAM,kBAAkB,MAAM,gBAAgB;4DACvD,WAAU;4DACV,OAAM;sEAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;gEAAC,MAAM;;;;;;;;;;;;;;;;;;2CAnCb,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+ClC", "debugId": null}}, {"offset": {"line": 6691, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/DocumentAccessIndicator.tsx"], "sourcesContent": ["import { useQuery } from \"convex/react\";\nimport { api } from \"../../convex/_generated/api\";\nimport { Id } from \"../../convex/_generated/dataModel\";\nimport { Crown, Eye, Edit, Users } from \"lucide-react\";\n\ninterface DocumentAccessIndicatorProps {\n  documentId: Id<\"documents\">;\n}\n\nexport function DocumentAccessIndicator({ documentId }: DocumentAccessIndicatorProps) {\n  const permission = useQuery(api.sharing.getDocumentPermission, { documentId });\n\n  if (!permission) return null;\n\n  const getIcon = () => {\n    switch (permission.permission) {\n      case \"owner\":\n        return <Crown size={14} className=\"text-yellow-600\" />;\n      case \"write\":\n        return <Edit size={14} className=\"text-green-600\" />;\n      case \"read\":\n        return <Eye size={14} className=\"text-blue-600\" />;\n      default:\n        return <Users size={14} className=\"text-gray-600\" />;\n    }\n  };\n\n  const getLabel = () => {\n    switch (permission.permission) {\n      case \"owner\":\n        return \"Owner\";\n      case \"write\":\n        return \"Can edit\";\n      case \"read\":\n        return \"Can view\";\n      default:\n        return \"No access\";\n    }\n  };\n\n  const getColor = () => {\n    switch (permission.permission) {\n      case \"owner\":\n        return \"bg-yellow-100 text-yellow-800 border-yellow-200\";\n      case \"write\":\n        return \"bg-green-100 text-green-800 border-green-200\";\n      case \"read\":\n        return \"bg-blue-100 text-blue-800 border-blue-200\";\n      default:\n        return \"bg-gray-100 text-gray-800 border-gray-200\";\n    }\n  };\n\n  return (\n    <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getColor()}`}>\n      {getIcon()}\n      <span>{getLabel()}</span>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;;;;;AAMO,SAAS,wBAAwB,EAAE,UAAU,EAAgC;IAClF,MAAM,aAAa,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,qBAAqB,EAAE;QAAE;IAAW;IAE5E,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,UAAU;QACd,OAAQ,WAAW,UAAU;YAC3B,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,MAAM;oBAAI,WAAU;;;;;;YACpC,KAAK;gBACH,qBAAO,8OAAC,2MAAA,CAAA,OAAI;oBAAC,MAAM;oBAAI,WAAU;;;;;;YACnC,KAAK;gBACH,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,MAAM;oBAAI,WAAU;;;;;;YAClC;gBACE,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,MAAM;oBAAI,WAAU;;;;;;QACtC;IACF;IAEA,MAAM,WAAW;QACf,OAAQ,WAAW,UAAU;YAC3B,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,WAAW;QACf,OAAQ,WAAW,UAAU;YAC3B,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,iFAAiF,EAAE,YAAY;;YAC7G;0BACD,8OAAC;0BAAM;;;;;;;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 6799, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/PermissionRequestModal.tsx"], "sourcesContent": ["import { useState } from \"react\";\nimport { X, UserPlus, Crown } from \"lucide-react\";\n\ninterface PermissionRequestModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSubmit: (message?: string) => void;\n  ownerName: string;\n}\n\nexport function PermissionRequestModal({ \n  isOpen, \n  onClose, \n  onSubmit, \n  ownerName \n}: PermissionRequestModalProps) {\n  const [message, setMessage] = useState(\"\");\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const handleSubmit = async () => {\n    setIsSubmitting(true);\n    try {\n      await onSubmit(message.trim() || undefined);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg shadow-xl w-full max-w-md mx-4\">\n        <div className=\"flex items-center justify-between p-4 border-b\">\n          <div className=\"flex items-center gap-2\">\n            <UserPlus size={20} className=\"text-green-600\" />\n            <h2 className=\"text-lg font-semibold\">Request Edit Access</h2>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"p-1 hover:bg-gray-100 rounded\"\n          >\n            <X size={20} />\n          </button>\n        </div>\n\n        <div className=\"p-4 space-y-4\">\n          <div className=\"flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\">\n            <Crown size={16} className=\"text-yellow-600\" />\n            <div>\n              <p className=\"text-sm font-medium text-yellow-800\">\n                Requesting permission from\n              </p>\n              <p className=\"text-sm text-yellow-700\">{ownerName}</p>\n            </div>\n          </div>\n\n          <div>\n            <p className=\"text-sm text-gray-600 mb-3\">\n              You currently have view-only access to this document. Send a request to the owner to upgrade your permissions to edit access.\n            </p>\n            \n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Message (optional)\n            </label>\n            <textarea\n              value={message}\n              onChange={(e) => setMessage(e.target.value)}\n              placeholder=\"Explain why you need edit access...\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 text-sm resize-none\"\n              rows={3}\n              maxLength={500}\n            />\n            <p className=\"text-xs text-gray-500 mt-1\">\n              {message.length}/500 characters\n            </p>\n          </div>\n\n          <div className=\"flex gap-2\">\n            <button\n              onClick={handleSubmit}\n              disabled={isSubmitting}\n              className=\"flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm\"\n            >\n              <UserPlus size={16} />\n              {isSubmitting ? \"Sending...\" : \"Send Request\"}\n            </button>\n            <button\n              onClick={onClose}\n              className=\"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-sm\"\n            >\n              Cancel\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;;;;AASO,SAAS,uBAAuB,EACrC,MAAM,EACN,OAAO,EACP,QAAQ,EACR,SAAS,EACmB;IAC5B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,eAAe;QACnB,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,QAAQ,IAAI,MAAM;QACnC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CAC9B,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;;;;;;;sCAExC,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAIb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CAC3B,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAsC;;;;;;sDAGnD,8OAAC;4CAAE,WAAU;sDAA2B;;;;;;;;;;;;;;;;;;sCAI5C,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAI1C,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC1C,aAAY;oCACZ,WAAU;oCACV,MAAM;oCACN,WAAW;;;;;;8CAEb,8OAAC;oCAAE,WAAU;;wCACV,QAAQ,MAAM;wCAAC;;;;;;;;;;;;;sCAIpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;wCACf,eAAe,eAAe;;;;;;;8CAEjC,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 7028, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/PendingRequestsModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useMutation, useQuery } from \"convex/react\";\nimport { api } from \"../../convex/_generated/api\";\nimport { Id } from \"../../convex/_generated/dataModel\";\nimport { X, CheckCircle, XCircle, Clock, User } from \"lucide-react\";\nimport { toast } from \"sonner\";\n\ninterface PendingRequestsModalProps {\n  documentId: Id<\"documents\">;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function PendingRequestsModal({ documentId, isOpen, onClose }: PendingRequestsModalProps) {\n  const pendingRequests = useQuery(api.sharing.getPendingPermissionRequests, { documentId });\n  const reviewRequest = useMutation(api.sharing.reviewPermissionRequest);\n\n  const handleReview = async (requestId: Id<\"permissionRequests\">, action: \"approve\" | \"deny\") => {\n    try {\n      await reviewRequest({ requestId, action });\n      toast.success(\n        action === \"approve\" \n          ? \"Permission request approved! User now has edit access.\" \n          : \"Permission request denied.\"\n      );\n    } catch (error) {\n      toast.error(\"Failed to process request\");\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg shadow-xl w-full max-w-lg mx-4\">\n        <div className=\"flex items-center justify-between p-4 border-b\">\n          <div className=\"flex items-center gap-2\">\n            <Clock size={20} className=\"text-orange-600\" />\n            <h2 className=\"text-lg font-semibold\">Permission Requests</h2>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"p-1 hover:bg-gray-100 rounded\"\n          >\n            <X size={20} />\n          </button>\n        </div>\n\n        <div className=\"p-4\">\n          {!pendingRequests || pendingRequests.length === 0 ? (\n            <div className=\"text-center py-8 text-gray-500\">\n              <Clock size={48} className=\"mx-auto mb-2 opacity-50\" />\n              <p>No pending permission requests</p>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {pendingRequests.map((request) => (\n                <div\n                  key={request._id}\n                  className=\"border border-gray-200 rounded-lg p-4 space-y-3\"\n                >\n                  <div className=\"flex items-start gap-3\">\n                    <div className=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\">\n                      {request.user?.name ? (\n                        <span className=\"text-sm font-medium text-blue-600\">\n                          {request.user.name.charAt(0).toUpperCase()}\n                        </span>\n                      ) : (\n                        <User size={16} className=\"text-blue-600\" />\n                      )}\n                    </div>\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center gap-2 mb-1\">\n                        <h3 className=\"font-medium text-gray-900\">\n                          {request.user?.name || \"Unknown User\"}\n                        </h3>\n                        <span className=\"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full\">\n                          Requesting Edit Access\n                        </span>\n                      </div>\n                      <p className=\"text-sm text-gray-600\">{request.user?.email}</p>\n                      <p className=\"text-xs text-gray-500 mt-1\">\n                        Requested {new Date(request._creationTime).toLocaleDateString()}\n                      </p>\n                    </div>\n                  </div>\n\n                  {request.message && (\n                    <div className=\"bg-gray-50 rounded-md p-3\">\n                      <p className=\"text-sm text-gray-700 font-medium mb-1\">Message:</p>\n                      <p className=\"text-sm text-gray-600\">{request.message}</p>\n                    </div>\n                  )}\n\n                  <div className=\"flex gap-2\">\n                    <button\n                      onClick={() => handleReview(request._id, \"approve\")}\n                      className=\"flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm\"\n                    >\n                      <CheckCircle size={16} />\n                      Approve\n                    </button>\n                    <button\n                      onClick={() => handleReview(request._id, \"deny\")}\n                      className=\"flex items-center gap-2 px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm\"\n                    >\n                      <XCircle size={16} />\n                      Deny\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;AAcO,SAAS,qBAAqB,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAA6B;IAC7F,MAAM,kBAAkB,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,4BAA4B,EAAE;QAAE;IAAW;IACxF,MAAM,gBAAgB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,uBAAuB;IAErE,MAAM,eAAe,OAAO,WAAqC;QAC/D,IAAI;YACF,MAAM,cAAc;gBAAE;gBAAW;YAAO;YACxC,wIAAA,CAAA,QAAK,CAAC,OAAO,CACX,WAAW,YACP,2DACA;QAER,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CAC3B,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;;;;;;;sCAExC,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAIb,8OAAC;oBAAI,WAAU;8BACZ,CAAC,mBAAmB,gBAAgB,MAAM,KAAK,kBAC9C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC3B,8OAAC;0CAAE;;;;;;;;;;;6CAGL,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,wBACpB,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,IAAI,EAAE,qBACb,8OAAC;oDAAK,WAAU;8DACb,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;yEAG1C,8OAAC,kMAAA,CAAA,OAAI;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAG9B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EACX,QAAQ,IAAI,EAAE,QAAQ;;;;;;0EAEzB,8OAAC;gEAAK,WAAU;0EAA+D;;;;;;;;;;;;kEAIjF,8OAAC;wDAAE,WAAU;kEAAyB,QAAQ,IAAI,EAAE;;;;;;kEACpD,8OAAC;wDAAE,WAAU;;4DAA6B;4DAC7B,IAAI,KAAK,QAAQ,aAAa,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;oCAKlE,QAAQ,OAAO,kBACd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAyC;;;;;;0DACtD,8OAAC;gDAAE,WAAU;0DAAyB,QAAQ,OAAO;;;;;;;;;;;;kDAIzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,aAAa,QAAQ,GAAG,EAAE;gDACzC,WAAU;;kEAEV,8OAAC,2NAAA,CAAA,cAAW;wDAAC,MAAM;;;;;;oDAAM;;;;;;;0DAG3B,8OAAC;gDACC,SAAS,IAAM,aAAa,QAAQ,GAAG,EAAE;gDACzC,WAAU;;kEAEV,8OAAC,4MAAA,CAAA,UAAO;wDAAC,MAAM;;;;;;oDAAM;;;;;;;;;;;;;;+BAhDpB,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DlC", "debugId": null}}, {"offset": {"line": 7336, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/DocumentHeader.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from \"react\";\nimport { useQuery, useMutation } from \"convex/react\";\nimport { api } from \"../../convex/_generated/api\";\nimport { Id } from \"../../convex/_generated/dataModel\";\nimport { Share2, UserPlus, Crown, AlertCircle, Clock, Edit2, Check, X } from \"lucide-react\";\nimport { DocumentSharingModal } from \"./DocumentSharingModal\";\nimport { DocumentAccessIndicator } from \"./DocumentAccessIndicator\";\nimport { PermissionRequestModal } from \"./PermissionRequestModal\";\nimport { PendingRequestsModal } from \"./PendingRequestsModal\";\nimport { Button } from \"./ui/button\";\nimport { Badge } from \"./ui/badge\";\nimport { Card, CardContent } from \"./ui/card\";\nimport { Input } from \"./ui/input\";\nimport { useToast } from \"../hooks/use-toast\";\n\ninterface DocumentHeaderProps {\n  documentId: Id<\"documents\">;\n}\n\nexport function DocumentHeader({ documentId }: DocumentHeaderProps) {\n  const permission = useQuery(api.sharing.getDocumentPermission, { documentId });\n  const document = useQuery(api.documents.getDocument, { id: documentId });\n  const userRequest = useQuery(api.sharing.getUserPermissionRequest, { documentId });\n  const pendingRequests = useQuery(\n    api.sharing.getPendingPermissionRequests, \n    permission?.canShare ? { documentId } : \"skip\"\n  );\n  \n  const [showSharingModal, setShowSharingModal] = useState(false);\n  const [showRequestModal, setShowRequestModal] = useState(false);\n  const [showPendingModal, setShowPendingModal] = useState(false);\n\n  // State for title editing\n  const [isEditingTitle, setIsEditingTitle] = useState(false);\n  const [editedTitle, setEditedTitle] = useState(\"\");\n  const [titleError, setTitleError] = useState(\"\");\n\n  const requestPermissionUpgrade = useMutation(api.sharing.requestPermissionUpgrade);\n  const updateDocument = useMutation(api.documents.updateDocument);\n  const { toast } = useToast();\n\n  // Validate document title\n  const validateTitle = (title: string): string => {\n    if (!title.trim()) {\n      return \"Document name cannot be empty\";\n    }\n    if (title.trim().length > 100) {\n      return \"Document name must be 100 characters or less\";\n    }\n    // Check for invalid characters (basic validation)\n    const invalidChars = /[<>:\"/\\\\|?*]/;\n    if (invalidChars.test(title)) {\n      return \"Document name contains invalid characters\";\n    }\n    return \"\";\n  };\n\n  // Start editing title\n  const handleStartEditTitle = () => {\n    if (permission?.permission !== \"owner\" && permission?.permission !== \"write\") {\n      return; // Only owners and write users can edit title\n    }\n    setEditedTitle(document?.title || \"\");\n    setTitleError(\"\");\n    setIsEditingTitle(true);\n  };\n\n  // Save title changes\n  const handleSaveTitle = async () => {\n    const error = validateTitle(editedTitle);\n    if (error) {\n      setTitleError(error);\n      return;\n    }\n\n    try {\n      await updateDocument({\n        id: documentId,\n        title: editedTitle.trim(),\n      });\n\n      setIsEditingTitle(false);\n      setTitleError(\"\");\n\n      toast({\n        title: \"Document renamed\",\n        description: `Document renamed to \"${editedTitle.trim()}\"`,\n      });\n    } catch (error) {\n      toast({\n        title: \"Error\",\n        description: error instanceof Error ? error.message : \"Failed to rename document\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  // Cancel title editing\n  const handleCancelEditTitle = () => {\n    setIsEditingTitle(false);\n    setEditedTitle(\"\");\n    setTitleError(\"\");\n  };\n\n  const handleRequestUpgrade = async (message?: string) => {\n    try {\n      await requestPermissionUpgrade({ documentId, message });\n      toast({\n        title: \"Request sent\",\n        description: \"Permission request sent to document owner!\",\n      });\n      setShowRequestModal(false);\n    } catch (error) {\n      toast({\n        title: \"Error\",\n        description: error instanceof Error ? error.message : \"Failed to send request\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  return (\n    <>\n      <Card data-testid=\"document-header\">\n        <CardContent className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              {isEditingTitle ? (\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"space-y-1\">\n                    <Input\n                      value={editedTitle}\n                      onChange={(e) => {\n                        setEditedTitle(e.target.value);\n                        if (titleError) setTitleError(\"\");\n                      }}\n                      onKeyDown={(e) => {\n                        if (e.key === \"Enter\" && !titleError && editedTitle.trim()) {\n                          handleSaveTitle();\n                        } else if (e.key === \"Escape\") {\n                          handleCancelEditTitle();\n                        }\n                      }}\n                      className={`text-2xl font-semibold h-auto py-1 px-2 ${titleError ? \"border-red-500\" : \"\"}`}\n                      autoFocus\n                    />\n                    {titleError && (\n                      <p className=\"text-sm text-red-600\">{titleError}</p>\n                    )}\n                  </div>\n                  <Button\n                    size=\"sm\"\n                    onClick={handleSaveTitle}\n                    disabled={!editedTitle.trim() || !!titleError}\n                    className=\"h-8 w-8 p-0\"\n                  >\n                    <Check className=\"h-4 w-4\" />\n                  </Button>\n                  <Button\n                    size=\"sm\"\n                    variant=\"outline\"\n                    onClick={handleCancelEditTitle}\n                    className=\"h-8 w-8 p-0\"\n                  >\n                    <X className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n              ) : (\n                <div className=\"flex items-center gap-2 group\">\n                  <h1 className=\"text-2xl font-semibold text-foreground\">\n                    {document?.title || \"Untitled Document\"}\n                  </h1>\n                  {(permission?.permission === \"owner\" || permission?.permission === \"write\") && (\n                    <Button\n                      size=\"sm\"\n                      variant=\"ghost\"\n                      onClick={handleStartEditTitle}\n                      className=\"opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0\"\n                    >\n                      <Edit2 className=\"h-4 w-4\" />\n                    </Button>\n                  )}\n                </div>\n              )}\n              <DocumentAccessIndicator documentId={documentId} />\n              \n              {/* Owner Information */}\n              {permission?.owner && permission.permission !== \"owner\" && (\n                <Badge variant=\"secondary\" className=\"gap-1\">\n                  <Crown className=\"h-3 w-3\" />\n                  Owned by {permission.owner.name || permission.owner.email}\n                </Badge>\n              )}\n            </div>\n\n            <div className=\"flex items-center gap-2\">\n              {/* Request Edit Access Button */}\n              {permission?.permission === \"read\" && !userRequest && (\n                <Button\n                  onClick={() => setShowRequestModal(true)}\n                  className=\"gap-2\"\n                  variant=\"default\"\n                >\n                  <UserPlus className=\"h-4 w-4\" />\n                  Request Edit Access\n                </Button>\n              )}\n\n              {/* Pending Request Status */}\n              {userRequest && (\n                <Badge variant=\"secondary\" className=\"gap-1\">\n                  <Clock className=\"h-3 w-3\" />\n                  Request Pending\n                </Badge>\n              )}\n\n              {/* Pending Requests Notification for Owner */}\n              {permission?.canShare && pendingRequests && pendingRequests.length > 0 && (\n                <Button\n                  onClick={() => setShowPendingModal(true)}\n                  variant=\"destructive\"\n                  className=\"gap-2 relative\"\n                >\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {pendingRequests.length} Request{pendingRequests.length !== 1 ? 's' : ''}\n                  <span className=\"absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full\"></span>\n                </Button>\n              )}\n\n              {/* Share Button */}\n              {permission?.canShare && (\n                <Button\n                  onClick={() => setShowSharingModal(true)}\n                  className=\"gap-2\"\n                >\n                  <Share2 className=\"h-4 w-4\" />\n                  Share\n                </Button>\n              )}\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      <DocumentSharingModal\n        documentId={documentId}\n        isOpen={showSharingModal}\n        onClose={() => setShowSharingModal(false)}\n      />\n\n      <PermissionRequestModal\n        isOpen={showRequestModal}\n        onClose={() => setShowRequestModal(false)}\n        onSubmit={handleRequestUpgrade}\n        ownerName={permission?.owner?.name || permission?.owner?.email || \"Document Owner\"}\n      />\n\n      <PendingRequestsModal\n        documentId={documentId}\n        isOpen={showPendingModal}\n        onClose={() => setShowPendingModal(false)}\n      />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA;;;;;;;;;;;;;;;AAqBO,SAAS,eAAe,EAAE,UAAU,EAAuB;IAChE,MAAM,aAAa,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,qBAAqB,EAAE;QAAE;IAAW;IAC5E,MAAM,WAAW,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,SAAS,CAAC,WAAW,EAAE;QAAE,IAAI;IAAW;IACtE,MAAM,cAAc,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,wBAAwB,EAAE;QAAE;IAAW;IAChF,MAAM,kBAAkB,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAC7B,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,4BAA4B,EACxC,YAAY,WAAW;QAAE;IAAW,IAAI;IAG1C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,0BAA0B;IAC1B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,2BAA2B,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,wBAAwB;IACjF,MAAM,iBAAiB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,SAAS,CAAC,cAAc;IAC/D,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAEzB,0BAA0B;IAC1B,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,OAAO;QACT;QACA,IAAI,MAAM,IAAI,GAAG,MAAM,GAAG,KAAK;YAC7B,OAAO;QACT;QACA,kDAAkD;QAClD,MAAM,eAAe;QACrB,IAAI,aAAa,IAAI,CAAC,QAAQ;YAC5B,OAAO;QACT;QACA,OAAO;IACT;IAEA,sBAAsB;IACtB,MAAM,uBAAuB;QAC3B,IAAI,YAAY,eAAe,WAAW,YAAY,eAAe,SAAS;YAC5E,QAAQ,6CAA6C;QACvD;QACA,eAAe,UAAU,SAAS;QAClC,cAAc;QACd,kBAAkB;IACpB;IAEA,qBAAqB;IACrB,MAAM,kBAAkB;QACtB,MAAM,QAAQ,cAAc;QAC5B,IAAI,OAAO;YACT,cAAc;YACd;QACF;QAEA,IAAI;YACF,MAAM,eAAe;gBACnB,IAAI;gBACJ,OAAO,YAAY,IAAI;YACzB;YAEA,kBAAkB;YAClB,cAAc;YAEd,MAAM;gBACJ,OAAO;gBACP,aAAa,CAAC,qBAAqB,EAAE,YAAY,IAAI,GAAG,CAAC,CAAC;YAC5D;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,OAAO;gBACP,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACtD,SAAS;YACX;QACF;IACF;IAEA,uBAAuB;IACvB,MAAM,wBAAwB;QAC5B,kBAAkB;QAClB,eAAe;QACf,cAAc;IAChB;IAEA,MAAM,uBAAuB,OAAO;QAClC,IAAI;YACF,MAAM,yBAAyB;gBAAE;gBAAY;YAAQ;YACrD,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YACA,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,OAAO;gBACP,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACtD,SAAS;YACX;QACF;IACF;IAEA,qBACE;;0BACE,8OAAC,gIAAA,CAAA,OAAI;gBAAC,eAAY;0BAChB,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCACZ,+BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDACJ,OAAO;wDACP,UAAU,CAAC;4DACT,eAAe,EAAE,MAAM,CAAC,KAAK;4DAC7B,IAAI,YAAY,cAAc;wDAChC;wDACA,WAAW,CAAC;4DACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,cAAc,YAAY,IAAI,IAAI;gEAC1D;4DACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;gEAC7B;4DACF;wDACF;wDACA,WAAW,CAAC,wCAAwC,EAAE,aAAa,mBAAmB,IAAI;wDAC1F,SAAS;;;;;;oDAEV,4BACC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAGzC,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,YAAY,IAAI,MAAM,CAAC,CAAC;gDACnC,WAAU;0DAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;gDACT,WAAU;0DAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;6DAIjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,UAAU,SAAS;;;;;;4CAErB,CAAC,YAAY,eAAe,WAAW,YAAY,eAAe,OAAO,mBACxE,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;gDACT,WAAU;0DAEV,cAAA,8OAAC,kMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAKzB,8OAAC,6IAAA,CAAA,0BAAuB;wCAAC,YAAY;;;;;;oCAGpC,YAAY,SAAS,WAAW,UAAU,KAAK,yBAC9C,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;0DACnC,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;4CACnB,WAAW,KAAK,CAAC,IAAI,IAAI,WAAW,KAAK,CAAC,KAAK;;;;;;;;;;;;;0CAK/D,8OAAC;gCAAI,WAAU;;oCAEZ,YAAY,eAAe,UAAU,CAAC,6BACrC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,oBAAoB;wCACnC,WAAU;wCACV,SAAQ;;0DAER,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;oCAMnC,6BACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;0DACnC,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;;;;;;;oCAMhC,YAAY,YAAY,mBAAmB,gBAAgB,MAAM,GAAG,mBACnE,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,oBAAoB;wCACnC,SAAQ;wCACR,WAAU;;0DAEV,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,gBAAgB,MAAM;4CAAC;4CAAS,gBAAgB,MAAM,KAAK,IAAI,MAAM;0DACtE,8OAAC;gDAAK,WAAU;;;;;;;;;;;;oCAKnB,YAAY,0BACX,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,oBAAoB;wCACnC,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1C,8OAAC,0IAAA,CAAA,uBAAoB;gBACnB,YAAY;gBACZ,QAAQ;gBACR,SAAS,IAAM,oBAAoB;;;;;;0BAGrC,8OAAC,4IAAA,CAAA,yBAAsB;gBACrB,QAAQ;gBACR,SAAS,IAAM,oBAAoB;gBACnC,UAAU;gBACV,WAAW,YAAY,OAAO,QAAQ,YAAY,OAAO,SAAS;;;;;;0BAGpE,8OAAC,0IAAA,CAAA,uBAAoB;gBACnB,YAAY;gBACZ,QAAQ;gBACR,SAAS,IAAM,oBAAoB;;;;;;;;AAI3C", "debugId": null}}, {"offset": {"line": 7776, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/scroll-area.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,0KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,0KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 7843, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/CollaborativeEditor.tsx"], "sourcesContent": ["import \"../lib/crypto-polyfill\";\nimport { useQuery } from 'convex/react'\nimport { useBlockNoteSync } from \"@convex-dev/prosemirror-sync/blocknote\";\nimport { BlockNoteView } from \"@blocknote/mantine\";\nimport \"@blocknote/core/fonts/inter.css\";\nimport \"@blocknote/mantine/style.css\";\nimport {\n  BasicTextStyleButton,\n  BlockTypeSelect,\n  ColorStyleButton,\n  CreateLinkButton,\n  FileCaptionButton,\n  FileReplaceButton,\n  FormattingToolbar,\n  FormattingToolbarController,\n  NestBlockButton,\n  TextAlignButton,\n  UnnestBlockButton,\n  useComponentsContext,\n  SideMenuController,\n  SideMenu,\n  AddBlockButton,\n  DragHandleButton,\n} from \"@blocknote/react\";\nimport { api } from \"../../convex/_generated/api\";\nimport { Id } from \"../../convex/_generated/dataModel\";\nimport { useState, useEffect, useCallback } from \"react\";\nimport { useCollaborativeCursors } from \"../hooks/useCollaborativeCursors\";\nimport { createCollaborativeCursorsPlugin } from \"../lib/collaborativeCursors\";\nimport { useComments } from \"../hooks/useComments\";\nimport { createCommentPlugin } from \"../lib/commentPlugin\";\nimport { CommentSidebar } from \"./CommentSidebar\";\nimport { CommentPopover } from \"./CommentPopover\";\nimport {\n  Bold,\n  Italic,\n  Underline,\n  List,\n  ListOrdered,\n  Heading1,\n  Heading2,\n  Heading3,\n  Link,\n  Undo,\n  Redo,\n  Lock,\n  MessageSquare,\n  MessageSquarePlus\n} from \"lucide-react\";\nimport { DocumentHeader } from \"./DocumentHeader\";\nimport { Button } from \"./ui/button\";\nimport { Input } from \"./ui/input\";\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from \"./ui/dialog\";\nimport { Separator } from \"./ui/separator\";\nimport { Card } from \"./ui/card\";\nimport { ScrollArea } from \"./ui/scroll-area\";\n\ninterface CollaborativeEditorProps {\n  documentId: Id<\"documents\">;\n}\n\nexport function CollaborativeEditor({ documentId }: CollaborativeEditorProps) {\n  const sync = useBlockNoteSync(api.prosemirror, documentId);\n  const permission = useQuery(api.sharing.getDocumentPermission, { documentId });\n  const canCreate = useQuery(api.documents.canCreateDocuments);\n  const currentUser = useQuery(api.auth.loggedInUser);\n\n  // Check if user has write permission for the editor\n  const isReadOnly = permission?.permission === \"read\";\n\n  // Comment system state\n  const [showCommentSidebar, setShowCommentSidebar] = useState(false);\n  const [commentPopover, setCommentPopover] = useState<{\n    isVisible: boolean;\n    position: { x: number; y: number };\n    selectedText: string;\n  }>({\n    isVisible: false,\n    position: { x: 0, y: 0 },\n    selectedText: \"\",\n  });\n\n  // Initialize collaborative cursors\n  useCollaborativeCursors({\n    documentId,\n    editor: sync.editor,\n    isReadOnly,\n  });\n\n  // Initialize comment system\n  const comments = useComments({\n    documentId,\n    editor: sync.editor,\n    isReadOnly,\n  });\n\n  // Expose debug functions to window for testing (development only)\n  useEffect(() => {\n    if (process.env.NODE_ENV === 'development') {\n      (window as any).debugComments = {\n        forceRefresh: comments.forceRefreshDecorations,\n        debugState: comments.debugCommentState,\n        getState: comments.getPluginState,\n      };\n    }\n  }, [comments]);\n\n  // Add collaborative cursors and comment plugins to the editor\n  useEffect(() => {\n    if (!sync.editor?.prosemirrorView) return;\n\n    const view = sync.editor.prosemirrorView;\n    const state = view.state;\n\n    // Check if plugins are already added\n    const hasCollaborativeCursorsPlugin = state.plugins.some(\n      plugin => (plugin as any).key?.toString().includes(\"collaborative-cursors\")\n    );\n    const hasCommentPlugin = state.plugins.some(\n      plugin => (plugin as any).key?.toString().includes(\"comments\")\n    );\n\n    const pluginsToAdd = [];\n\n    if (!hasCollaborativeCursorsPlugin) {\n      pluginsToAdd.push(createCollaborativeCursorsPlugin());\n    }\n\n    if (!hasCommentPlugin) {\n      pluginsToAdd.push(createCommentPlugin());\n    }\n\n    if (pluginsToAdd.length > 0) {\n      const newState = state.reconfigure({\n        plugins: [...state.plugins, ...pluginsToAdd]\n      });\n      view.updateState(newState);\n    }\n  }, [sync.editor]);\n\n\n\n  // Handle adding comment from toolbar button\n  const handleAddComment = useCallback(() => {\n    if (isReadOnly || !sync.editor?.prosemirrorView) return;\n\n    const selection = sync.editor.prosemirrorView.state.selection;\n    if (!selection || selection.empty) return;\n\n    const selectedText = sync.editor.prosemirrorView.state.doc.textBetween(\n      selection.from,\n      selection.to\n    );\n\n    // Position the popover near the editor but not covering the selected text\n    const editorElement = sync.editor.prosemirrorView.dom;\n    const editorRect = editorElement.getBoundingClientRect();\n\n    setCommentPopover({\n      isVisible: true,\n      position: {\n        x: editorRect.right - 400, // Position to the right of the editor\n        y: editorRect.top + 100    // A bit down from the top\n      },\n      selectedText,\n    });\n  }, [isReadOnly, sync.editor]);\n\n  const handleCloseCommentPopover = () => {\n    setCommentPopover(prev => ({ ...prev, isVisible: false }));\n  };\n\n  // Custom Add Comment Button for the formatting toolbar\n  function AddCommentButton() {\n    const Components = useComponentsContext()!;\n\n    return (\n      <Components.FormattingToolbar.Button\n        className=\"bg-blue-600 hover:bg-blue-700 text-white\"\n        onClick={handleAddComment}\n        mainTooltip=\"Add Comment to Selection\"\n      >\n        <MessageSquarePlus size={16} />\n      </Components.FormattingToolbar.Button>\n    );\n  }\n\n  if (sync.isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"></div>\n        <span className=\"ml-2 text-muted-foreground\">Loading editor...</span>\n      </div>\n    );\n  }\n\n  if (!sync.editor) {\n    // Check if user has permission to access this specific document\n    if (permission === null) {\n      return (\n        <div className=\"flex flex-col items-center justify-center h-64 space-y-4\">\n          <div className=\"flex items-center gap-2 text-muted-foreground\">\n            <Lock className=\"h-6 w-6\" />\n            <span className=\"text-lg\">Document Not Found</span>\n          </div>\n          <div className=\"text-center space-y-2\">\n            <p className=\"text-muted-foreground\">\n              This document doesn't exist or you don't have permission to access it.\n            </p>\n          </div>\n        </div>\n      );\n    }\n\n    // Check if user has permission to create/edit documents\n    if (canCreate?.canCreate === false) {\n      return (\n        <div className=\"flex flex-col items-center justify-center h-64 space-y-4\">\n          <div className=\"flex items-center gap-2 text-muted-foreground\">\n            <Lock className=\"h-6 w-6\" />\n            <span className=\"text-lg\">Access Restricted</span>\n          </div>\n          <div className=\"text-center space-y-2\">\n            <p className=\"text-muted-foreground\">\n              You don't have permission to create documents.\n            </p>\n            <p className=\"text-sm text-muted-foreground\">\n              Anonymous users cannot create documents. Please sign up for an account to create and edit documents.\n            </p>\n          </div>\n        </div>\n      );\n    }\n\n    // Auto-initialize the document content if user has permission\n    if (permission && (permission.permission === \"owner\" || permission.permission === \"write\")) {\n      // Create the initial document content automatically\n      sync.create({\n        type: \"doc\",\n        content: []\n      });\n\n      // Show loading state while the document is being initialized\n      return (\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"></div>\n          <span className=\"ml-2 text-muted-foreground\">Initializing document...</span>\n        </div>\n      );\n    }\n\n    // Show message for read-only users when document content doesn't exist\n    return (\n      <div className=\"flex flex-col items-center justify-center h-64 space-y-4\">\n        <div className=\"flex items-center gap-2 text-muted-foreground\">\n          <Lock className=\"h-6 w-6\" />\n          <span className=\"text-lg\">Document Empty</span>\n        </div>\n        <div className=\"text-center space-y-2\">\n          <p className=\"text-muted-foreground\">\n            This document has no content yet and you have read-only access.\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"w-full h-full space-y-4\">\n      <DocumentHeader documentId={documentId} />\n      <Card>\n        {!isReadOnly && (\n          <EditorToolbar\n            editor={sync.editor}\n            showCommentSidebar={showCommentSidebar}\n            onToggleCommentSidebar={() => setShowCommentSidebar(!showCommentSidebar)}\n          />\n        )}\n        {isReadOnly && (\n          <div className=\"flex items-center gap-2 p-3 border-b bg-muted/30\">\n            <Lock className=\"h-4 w-4 text-muted-foreground\" />\n            <span className=\"text-sm text-muted-foreground\">\n              You have read-only access to this document\n            </span>\n          </div>\n        )}\n        <ScrollArea className=\"h-[calc(100vh-300px)] min-h-[500px] editor-scroll-area\">\n          <div className=\"p-6\">\n            <BlockNoteView\n              editor={sync.editor}\n              theme=\"light\"\n              className=\"prose prose-sm max-w-none\"\n              editable={!isReadOnly}\n              formattingToolbar={false}\n              sideMenu={isReadOnly}\n            >\n              {!isReadOnly && sync.editor && (\n                <>\n                  <FormattingToolbarController\n                    key=\"custom-formatting-toolbar\"\n                    formattingToolbar={() => (\n                      <FormattingToolbar>\n                        <BlockTypeSelect key={\"blockTypeSelect\"} />\n\n                        <FileCaptionButton key={\"fileCaptionButton\"} />\n                        <FileReplaceButton key={\"replaceFileButton\"} />\n\n                        <BasicTextStyleButton\n                          basicTextStyle={\"bold\"}\n                          key={\"boldStyleButton\"}\n                        />\n                        <BasicTextStyleButton\n                          basicTextStyle={\"italic\"}\n                          key={\"italicStyleButton\"}\n                        />\n                        <BasicTextStyleButton\n                          basicTextStyle={\"underline\"}\n                          key={\"underlineStyleButton\"}\n                        />\n                        <BasicTextStyleButton\n                          basicTextStyle={\"strike\"}\n                          key={\"strikeStyleButton\"}\n                        />\n                        <BasicTextStyleButton\n                          basicTextStyle={\"code\"}\n                          key={\"codeStyleButton\"}\n                        />\n\n                        <TextAlignButton\n                          textAlignment={\"left\"}\n                          key={\"textAlignLeftButton\"}\n                        />\n                        <TextAlignButton\n                          textAlignment={\"center\"}\n                          key={\"textAlignCenterButton\"}\n                        />\n                        <TextAlignButton\n                          textAlignment={\"right\"}\n                          key={\"textAlignRightButton\"}\n                        />\n\n                        <ColorStyleButton key={\"colorStyleButton\"} />\n\n                        <NestBlockButton key={\"nestBlockButton\"} />\n                        <UnnestBlockButton key={\"unnestBlockButton\"} />\n\n                        <CreateLinkButton key={\"createLinkButton\"} />\n\n                        {/* Custom Add Comment Button */}\n                        <AddCommentButton key={\"addCommentButton\"} />\n                      </FormattingToolbar>\n                    )}\n                  />\n                  <SideMenuController\n                    key=\"custom-side-menu\"\n                    sideMenu={(props) => (\n                      <SideMenu {...props}>\n                        <AddBlockButton {...props} />\n                        <DragHandleButton {...props} />\n                      </SideMenu>\n                    )}\n                  />\n                </>\n              )}\n            </BlockNoteView>\n          </div>\n        </ScrollArea>\n      </Card>\n\n      {/* Comment Popover */}\n      <CommentPopover\n        isVisible={commentPopover.isVisible}\n        position={commentPopover.position}\n        selectedText={commentPopover.selectedText}\n        onCreateComment={comments.createComment}\n        onClose={handleCloseCommentPopover}\n      />\n\n      {/* Comment Sidebar */}\n      <CommentSidebar\n        comments={comments.comments}\n        currentUserId={currentUser?._id}\n        selectedCommentId={comments.getPluginState()?.selectedCommentId}\n        isVisible={showCommentSidebar}\n        canResolve={!isReadOnly}\n        onClose={() => setShowCommentSidebar(false)}\n        onCreateComment={comments.createComment}\n        onUpdateComment={comments.updateComment}\n        onDeleteComment={comments.deleteComment}\n        onCreateReply={comments.createReply}\n        onUpdateReply={comments.updateReply}\n        onDeleteReply={comments.deleteReply}\n        onResolveComment={comments.resolveComment}\n        onSelectComment={comments.selectComment}\n      />\n    </div>\n  );\n}\n\nfunction EditorToolbar({\n  editor,\n  showCommentSidebar,\n  onToggleCommentSidebar\n}: {\n  editor: any;\n  showCommentSidebar: boolean;\n  onToggleCommentSidebar: () => void;\n}) {\n  const [showLinkDialog, setShowLinkDialog] = useState(false);\n  const [linkUrl, setLinkUrl] = useState(\"\");\n\n  const handleBold = () => {\n    editor.focus();\n    editor.commands.toggleBold();\n  };\n\n  const handleItalic = () => {\n    editor.focus();\n    editor.commands.toggleItalic();\n  };\n\n  const handleUnderline = () => {\n    editor.focus();\n    editor.commands.toggleUnderline();\n  };\n\n  const handleBulletList = () => {\n    editor.focus();\n    editor.commands.toggleBulletList();\n  };\n\n  const handleOrderedList = () => {\n    editor.focus();\n    editor.commands.toggleOrderedList();\n  };\n\n  const handleHeading = (level: 1 | 2 | 3) => {\n    editor.focus();\n    editor.commands.toggleHeading({ level });\n  };\n\n  const handleLink = () => {\n    const selection = editor.state.selection;\n    const selectedText = editor.state.doc.textBetween(selection.from, selection.to);\n\n    if (selectedText) {\n      setShowLinkDialog(true);\n    } else {\n      alert(\"Please select text first to create a link\");\n    }\n  };\n\n  const insertLink = () => {\n    if (linkUrl) {\n      editor.commands.setLink({ href: linkUrl });\n      setShowLinkDialog(false);\n      setLinkUrl(\"\");\n    }\n  };\n\n  const handleUndo = () => {\n    editor.commands.undo();\n  };\n\n  const handleRedo = () => {\n    editor.commands.redo();\n  };\n\n  return (\n    <>\n      <div className=\"flex items-center gap-1 p-3 border-b bg-muted/50\">\n        <Button variant=\"ghost\" size=\"sm\" onClick={handleUndo} title=\"Undo\">\n          <Undo className=\"h-4 w-4\" />\n        </Button>\n        <Button variant=\"ghost\" size=\"sm\" onClick={handleRedo} title=\"Redo\">\n          <Redo className=\"h-4 w-4\" />\n        </Button>\n\n        <Separator orientation=\"vertical\" className=\"mx-2 h-6\" />\n\n        <Button variant=\"ghost\" size=\"sm\" onClick={handleBold} title=\"Bold\">\n          <Bold className=\"h-4 w-4\" />\n        </Button>\n        <Button variant=\"ghost\" size=\"sm\" onClick={handleItalic} title=\"Italic\">\n          <Italic className=\"h-4 w-4\" />\n        </Button>\n        <Button variant=\"ghost\" size=\"sm\" onClick={handleUnderline} title=\"Underline\">\n          <Underline className=\"h-4 w-4\" />\n        </Button>\n\n        <Separator orientation=\"vertical\" className=\"mx-2 h-6\" />\n\n        <Button variant=\"ghost\" size=\"sm\" onClick={() => handleHeading(1)} title=\"Heading 1\">\n          <Heading1 className=\"h-4 w-4\" />\n        </Button>\n        <Button variant=\"ghost\" size=\"sm\" onClick={() => handleHeading(2)} title=\"Heading 2\">\n          <Heading2 className=\"h-4 w-4\" />\n        </Button>\n        <Button variant=\"ghost\" size=\"sm\" onClick={() => handleHeading(3)} title=\"Heading 3\">\n          <Heading3 className=\"h-4 w-4\" />\n        </Button>\n\n        <Separator orientation=\"vertical\" className=\"mx-2 h-6\" />\n\n        <Button variant=\"ghost\" size=\"sm\" onClick={handleBulletList} title=\"Bullet List\">\n          <List className=\"h-4 w-4\" />\n        </Button>\n        <Button variant=\"ghost\" size=\"sm\" onClick={handleOrderedList} title=\"Numbered List\">\n          <ListOrdered className=\"h-4 w-4\" />\n        </Button>\n\n        <Separator orientation=\"vertical\" className=\"mx-2 h-6\" />\n\n        <Button variant=\"ghost\" size=\"sm\" onClick={handleLink} title=\"Insert Link\">\n          <Link className=\"h-4 w-4\" />\n        </Button>\n\n        <Separator orientation=\"vertical\" className=\"mx-2 h-6\" />\n\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={onToggleCommentSidebar}\n          title=\"Toggle Comments\"\n          className={showCommentSidebar ? \"bg-accent\" : \"\"}\n        >\n          <MessageSquare className=\"h-4 w-4\" />\n        </Button>\n      </div>\n\n      <Dialog open={showLinkDialog} onOpenChange={setShowLinkDialog}>\n        <DialogContent className=\"sm:max-w-md\">\n          <DialogHeader>\n            <DialogTitle>Insert Link</DialogTitle>\n          </DialogHeader>\n          <div className=\"space-y-4\">\n            <Input\n              type=\"url\"\n              value={linkUrl}\n              onChange={(e) => setLinkUrl(e.target.value)}\n              placeholder=\"Enter URL...\"\n              autoFocus\n            />\n          </div>\n          <DialogFooter>\n            <Button variant=\"outline\" onClick={() => setShowLinkDialog(false)}>\n              Cancel\n            </Button>\n            <Button onClick={insertLink}>\n              Insert\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;AAGA;AAkBA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;AAMO,SAAS,oBAAoB,EAAE,UAAU,EAA4B;IAC1E,MAAM,OAAO,CAAA,GAAA,2LAAA,CAAA,mBAAgB,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,WAAW,EAAE;IAC/C,MAAM,aAAa,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,qBAAqB,EAAE;QAAE;IAAW;IAC5E,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,SAAS,CAAC,kBAAkB;IAC3D,MAAM,cAAc,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,IAAI,CAAC,YAAY;IAElD,oDAAoD;IACpD,MAAM,aAAa,YAAY,eAAe;IAE9C,uBAAuB;IACvB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAIhD;QACD,WAAW;QACX,UAAU;YAAE,GAAG;YAAG,GAAG;QAAE;QACvB,cAAc;IAChB;IAEA,mCAAmC;IACnC,CAAA,GAAA,uIAAA,CAAA,0BAAuB,AAAD,EAAE;QACtB;QACA,QAAQ,KAAK,MAAM;QACnB;IACF;IAEA,4BAA4B;IAC5B,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE;QAC3B;QACA,QAAQ,KAAK,MAAM;QACnB;IACF;IAEA,kEAAkE;IAClE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAA4C;YACzC,OAAe,aAAa,GAAG;gBAC9B,cAAc,SAAS,uBAAuB;gBAC9C,YAAY,SAAS,iBAAiB;gBACtC,UAAU,SAAS,cAAc;YACnC;QACF;IACF,GAAG;QAAC;KAAS;IAEb,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,KAAK,MAAM,EAAE,iBAAiB;QAEnC,MAAM,OAAO,KAAK,MAAM,CAAC,eAAe;QACxC,MAAM,QAAQ,KAAK,KAAK;QAExB,qCAAqC;QACrC,MAAM,gCAAgC,MAAM,OAAO,CAAC,IAAI,CACtD,CAAA,SAAU,AAAC,OAAe,GAAG,EAAE,WAAW,SAAS;QAErD,MAAM,mBAAmB,MAAM,OAAO,CAAC,IAAI,CACzC,CAAA,SAAU,AAAC,OAAe,GAAG,EAAE,WAAW,SAAS;QAGrD,MAAM,eAAe,EAAE;QAEvB,IAAI,CAAC,+BAA+B;YAClC,aAAa,IAAI,CAAC,CAAA,GAAA,kIAAA,CAAA,mCAAgC,AAAD;QACnD;QAEA,IAAI,CAAC,kBAAkB;YACrB,aAAa,IAAI,CAAC,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD;QACtC;QAEA,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,MAAM,WAAW,MAAM,WAAW,CAAC;gBACjC,SAAS;uBAAI,MAAM,OAAO;uBAAK;iBAAa;YAC9C;YACA,KAAK,WAAW,CAAC;QACnB;IACF,GAAG;QAAC,KAAK,MAAM;KAAC;IAIhB,4CAA4C;IAC5C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,IAAI,cAAc,CAAC,KAAK,MAAM,EAAE,iBAAiB;QAEjD,MAAM,YAAY,KAAK,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,SAAS;QAC7D,IAAI,CAAC,aAAa,UAAU,KAAK,EAAE;QAEnC,MAAM,eAAe,KAAK,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CACpE,UAAU,IAAI,EACd,UAAU,EAAE;QAGd,0EAA0E;QAC1E,MAAM,gBAAgB,KAAK,MAAM,CAAC,eAAe,CAAC,GAAG;QACrD,MAAM,aAAa,cAAc,qBAAqB;QAEtD,kBAAkB;YAChB,WAAW;YACX,UAAU;gBACR,GAAG,WAAW,KAAK,GAAG;gBACtB,GAAG,WAAW,GAAG,GAAG,IAAO,0BAA0B;YACvD;YACA;QACF;IACF,GAAG;QAAC;QAAY,KAAK,MAAM;KAAC;IAE5B,MAAM,4BAA4B;QAChC,kBAAkB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAM,CAAC;IAC1D;IAEA,uDAAuD;IACvD,SAAS;QACP,MAAM,aAAa,CAAA,GAAA,kKAAA,CAAA,uBAAoB,AAAD;QAEtC,qBACE,8OAAC,WAAW,iBAAiB,CAAC,MAAM;YAClC,WAAU;YACV,SAAS;YACT,aAAY;sBAEZ,cAAA,8OAAC,oOAAA,CAAA,oBAAiB;gBAAC,MAAM;;;;;;;;;;;IAG/B;IAEA,IAAI,KAAK,SAAS,EAAE;QAClB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAA6B;;;;;;;;;;;;IAGnD;IAEA,IAAI,CAAC,KAAK,MAAM,EAAE;QAChB,gEAAgE;QAChE,IAAI,eAAe,MAAM;YACvB,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;kCAE5B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;QAM7C;QAEA,wDAAwD;QACxD,IAAI,WAAW,cAAc,OAAO;YAClC,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;kCAE5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAGrC,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;QAMrD;QAEA,8DAA8D;QAC9D,IAAI,cAAc,CAAC,WAAW,UAAU,KAAK,WAAW,WAAW,UAAU,KAAK,OAAO,GAAG;YAC1F,oDAAoD;YACpD,KAAK,MAAM,CAAC;gBACV,MAAM;gBACN,SAAS,EAAE;YACb;YAEA,6DAA6D;YAC7D,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAK,WAAU;kCAA6B;;;;;;;;;;;;QAGnD;QAEA,uEAAuE;QACvE,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;8BAE5B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAM7C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,oIAAA,CAAA,iBAAc;gBAAC,YAAY;;;;;;0BAC5B,8OAAC,gIAAA,CAAA,OAAI;;oBACF,CAAC,4BACA,8OAAC;wBACC,QAAQ,KAAK,MAAM;wBACnB,oBAAoB;wBACpB,wBAAwB,IAAM,sBAAsB,CAAC;;;;;;oBAGxD,4BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;gCAAK,WAAU;0CAAgC;;;;;;;;;;;;kCAKpD,8OAAC,0IAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,sKAAA,CAAA,gBAAa;gCACZ,QAAQ,KAAK,MAAM;gCACnB,OAAM;gCACN,WAAU;gCACV,UAAU,CAAC;gCACX,mBAAmB;gCACnB,UAAU;0CAET,CAAC,cAAc,KAAK,MAAM,kBACzB;;sDACE,8OAAC,kKAAA,CAAA,8BAA2B;4CAE1B,mBAAmB,kBACjB,8OAAC,kKAAA,CAAA,oBAAiB;;sEAChB,8OAAC,kKAAA,CAAA,kBAAe,MAAM;;;;;sEAEtB,8OAAC,kKAAA,CAAA,oBAAiB,MAAM;;;;;sEACxB,8OAAC,kKAAA,CAAA,oBAAiB,MAAM;;;;;sEAExB,8OAAC,kKAAA,CAAA,uBAAoB;4DACnB,gBAAgB;2DACX;;;;;sEAEP,8OAAC,kKAAA,CAAA,uBAAoB;4DACnB,gBAAgB;2DACX;;;;;sEAEP,8OAAC,kKAAA,CAAA,uBAAoB;4DACnB,gBAAgB;2DACX;;;;;sEAEP,8OAAC,kKAAA,CAAA,uBAAoB;4DACnB,gBAAgB;2DACX;;;;;sEAEP,8OAAC,kKAAA,CAAA,uBAAoB;4DACnB,gBAAgB;2DACX;;;;;sEAGP,8OAAC,kKAAA,CAAA,kBAAe;4DACd,eAAe;2DACV;;;;;sEAEP,8OAAC,kKAAA,CAAA,kBAAe;4DACd,eAAe;2DACV;;;;;sEAEP,8OAAC,kKAAA,CAAA,kBAAe;4DACd,eAAe;2DACV;;;;;sEAGP,8OAAC,kKAAA,CAAA,mBAAgB,MAAM;;;;;sEAEvB,8OAAC,kKAAA,CAAA,kBAAe,MAAM;;;;;sEACtB,8OAAC,kKAAA,CAAA,oBAAiB,MAAM;;;;;sEAExB,8OAAC,kKAAA,CAAA,mBAAgB,MAAM;;;;;sEAGvB,8OAAC,sBAAsB;;;;;;;;;;;2CAlDvB;;;;;sDAsDN,8OAAC,kKAAA,CAAA,qBAAkB;4CAEjB,UAAU,CAAC,sBACT,8OAAC,kKAAA,CAAA,WAAQ;oDAAE,GAAG,KAAK;;sEACjB,8OAAC,kKAAA,CAAA,iBAAc;4DAAE,GAAG,KAAK;;;;;;sEACzB,8OAAC,kKAAA,CAAA,mBAAgB;4DAAE,GAAG,KAAK;;;;;;;;;;;;2CAJ3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgBlB,8OAAC,oIAAA,CAAA,iBAAc;gBACb,WAAW,eAAe,SAAS;gBACnC,UAAU,eAAe,QAAQ;gBACjC,cAAc,eAAe,YAAY;gBACzC,iBAAiB,SAAS,aAAa;gBACvC,SAAS;;;;;;0BAIX,8OAAC,oIAAA,CAAA,iBAAc;gBACb,UAAU,SAAS,QAAQ;gBAC3B,eAAe,aAAa;gBAC5B,mBAAmB,SAAS,cAAc,IAAI;gBAC9C,WAAW;gBACX,YAAY,CAAC;gBACb,SAAS,IAAM,sBAAsB;gBACrC,iBAAiB,SAAS,aAAa;gBACvC,iBAAiB,SAAS,aAAa;gBACvC,iBAAiB,SAAS,aAAa;gBACvC,eAAe,SAAS,WAAW;gBACnC,eAAe,SAAS,WAAW;gBACnC,eAAe,SAAS,WAAW;gBACnC,kBAAkB,SAAS,cAAc;gBACzC,iBAAiB,SAAS,aAAa;;;;;;;;;;;;AAI/C;AAEA,SAAS,cAAc,EACrB,MAAM,EACN,kBAAkB,EAClB,sBAAsB,EAKvB;IACC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,aAAa;QACjB,OAAO,KAAK;QACZ,OAAO,QAAQ,CAAC,UAAU;IAC5B;IAEA,MAAM,eAAe;QACnB,OAAO,KAAK;QACZ,OAAO,QAAQ,CAAC,YAAY;IAC9B;IAEA,MAAM,kBAAkB;QACtB,OAAO,KAAK;QACZ,OAAO,QAAQ,CAAC,eAAe;IACjC;IAEA,MAAM,mBAAmB;QACvB,OAAO,KAAK;QACZ,OAAO,QAAQ,CAAC,gBAAgB;IAClC;IAEA,MAAM,oBAAoB;QACxB,OAAO,KAAK;QACZ,OAAO,QAAQ,CAAC,iBAAiB;IACnC;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,KAAK;QACZ,OAAO,QAAQ,CAAC,aAAa,CAAC;YAAE;QAAM;IACxC;IAEA,MAAM,aAAa;QACjB,MAAM,YAAY,OAAO,KAAK,CAAC,SAAS;QACxC,MAAM,eAAe,OAAO,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,UAAU,IAAI,EAAE,UAAU,EAAE;QAE9E,IAAI,cAAc;YAChB,kBAAkB;QACpB,OAAO;YACL,MAAM;QACR;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,SAAS;YACX,OAAO,QAAQ,CAAC,OAAO,CAAC;gBAAE,MAAM;YAAQ;YACxC,kBAAkB;YAClB,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,OAAO,QAAQ,CAAC,IAAI;IACtB;IAEA,MAAM,aAAa;QACjB,OAAO,QAAQ,CAAC,IAAI;IACtB;IAEA,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,SAAS;wBAAY,OAAM;kCAC3D,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,SAAS;wBAAY,OAAM;kCAC3D,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAGlB,8OAAC,qIAAA,CAAA,YAAS;wBAAC,aAAY;wBAAW,WAAU;;;;;;kCAE5C,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,SAAS;wBAAY,OAAM;kCAC3D,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,SAAS;wBAAc,OAAM;kCAC7D,cAAA,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,SAAS;wBAAiB,OAAM;kCAChE,cAAA,8OAAC,4MAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;kCAGvB,8OAAC,qIAAA,CAAA,YAAS;wBAAC,aAAY;wBAAW,WAAU;;;;;;kCAE5C,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,SAAS,IAAM,cAAc;wBAAI,OAAM;kCACvE,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAEtB,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,SAAS,IAAM,cAAc;wBAAI,OAAM;kCACvE,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAEtB,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,SAAS,IAAM,cAAc;wBAAI,OAAM;kCACvE,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAGtB,8OAAC,qIAAA,CAAA,YAAS;wBAAC,aAAY;wBAAW,WAAU;;;;;;kCAE5C,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,SAAS;wBAAkB,OAAM;kCACjE,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,SAAS;wBAAmB,OAAM;kCAClE,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAGzB,8OAAC,qIAAA,CAAA,YAAS;wBAAC,aAAY;wBAAW,WAAU;;;;;;kCAE5C,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,SAAS;wBAAY,OAAM;kCAC3D,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAGlB,8OAAC,qIAAA,CAAA,YAAS;wBAAC,aAAY;wBAAW,WAAU;;;;;;kCAE5C,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,OAAM;wBACN,WAAW,qBAAqB,cAAc;kCAE9C,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAI7B,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAgB,cAAc;0BAC1C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;0CAAC;;;;;;;;;;;sCAEf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC1C,aAAY;gCACZ,SAAS;;;;;;;;;;;sCAGb,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,kBAAkB;8CAAQ;;;;;;8CAGnE,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;8CAAY;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC", "debugId": null}}, {"offset": {"line": 8894, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/lib/screenplayFormatting.ts"], "sourcesContent": ["/**\n * Screenplay formatting utilities and constants\n * Based on industry-standard screenplay formatting rules\n */\n\n// Standard screenplay page dimensions (in inches)\nexport const PAGE_DIMENSIONS = {\n  width: 8.5,\n  height: 11,\n  marginTop: 1,\n  marginBottom: 1,\n  marginLeft: 1.5,\n  marginRight: 1,\n} as const;\n\n// Convert inches to pixels (assuming 96 DPI)\nexport const PAGE_DIMENSIONS_PX = {\n  width: PAGE_DIMENSIONS.width * 96,\n  height: PAGE_DIMENSIONS.height * 96,\n  marginTop: PAGE_DIMENSIONS.marginTop * 96,\n  marginBottom: PAGE_DIMENSIONS.marginBottom * 96,\n  marginLeft: PAGE_DIMENSIONS.marginLeft * 96,\n  marginRight: PAGE_DIMENSIONS.marginRight * 96,\n} as const;\n\n// Calculate content area\nexport const CONTENT_AREA = {\n  width: PAGE_DIMENSIONS_PX.width - PAGE_DIMENSIONS_PX.marginLeft - PAGE_DIMENSIONS_PX.marginRight,\n  height: PAGE_DIMENSIONS_PX.height - PAGE_DIMENSIONS_PX.marginTop - PAGE_DIMENSIONS_PX.marginBottom,\n} as const;\n\n// Screenplay element types\nexport enum ScreenplayElementType {\n  SCENE_HEADING = 'scene_heading',\n  ACTION = 'action',\n  CHARACTER = 'character',\n  DIALOGUE = 'dialogue',\n  PARENTHETICAL = 'parenthetical',\n  TRANSITION = 'transition',\n  SHOT = 'shot',\n  GENERAL = 'general',\n}\n\n// Formatting rules for each element type\nexport const ELEMENT_FORMATTING = {\n  [ScreenplayElementType.SCENE_HEADING]: {\n    marginLeft: 0,\n    marginRight: 0,\n    textTransform: 'uppercase' as const,\n    fontWeight: 'bold' as const,\n    marginTop: 24,\n    marginBottom: 12,\n    lineHeight: 1.2,\n  },\n  [ScreenplayElementType.ACTION]: {\n    marginLeft: 0,\n    marginRight: 0,\n    textTransform: 'none' as const,\n    fontWeight: 'normal' as const,\n    marginTop: 12,\n    marginBottom: 12,\n    lineHeight: 1.2,\n  },\n  [ScreenplayElementType.CHARACTER]: {\n    marginLeft: 220, // ~3.7 inches from left\n    marginRight: 0,\n    textTransform: 'uppercase' as const,\n    fontWeight: 'normal' as const,\n    marginTop: 12,\n    marginBottom: 0,\n    lineHeight: 1.2,\n  },\n  [ScreenplayElementType.DIALOGUE]: {\n    marginLeft: 100, // ~1.5 inches from left\n    marginRight: 100, // ~1.5 inches from right\n    textTransform: 'none' as const,\n    fontWeight: 'normal' as const,\n    marginTop: 0,\n    marginBottom: 12,\n    lineHeight: 1.2,\n  },\n  [ScreenplayElementType.PARENTHETICAL]: {\n    marginLeft: 160, // ~2.5 inches from left\n    marginRight: 120, // ~2 inches from right\n    textTransform: 'none' as const,\n    fontWeight: 'normal' as const,\n    fontStyle: 'italic' as const,\n    marginTop: 0,\n    marginBottom: 0,\n    lineHeight: 1.2,\n  },\n  [ScreenplayElementType.TRANSITION]: {\n    marginLeft: 400, // Right-aligned\n    marginRight: 0,\n    textTransform: 'uppercase' as const,\n    fontWeight: 'normal' as const,\n    marginTop: 12,\n    marginBottom: 24,\n    lineHeight: 1.2,\n    textAlign: 'right' as const,\n  },\n  [ScreenplayElementType.SHOT]: {\n    marginLeft: 0,\n    marginRight: 0,\n    textTransform: 'uppercase' as const,\n    fontWeight: 'normal' as const,\n    marginTop: 12,\n    marginBottom: 12,\n    lineHeight: 1.2,\n  },\n  [ScreenplayElementType.GENERAL]: {\n    marginLeft: 0,\n    marginRight: 0,\n    textTransform: 'none' as const,\n    fontWeight: 'normal' as const,\n    marginTop: 12,\n    marginBottom: 12,\n    lineHeight: 1.2,\n  },\n} as const;\n\n// Auto-detection patterns for screenplay elements\nexport const ELEMENT_PATTERNS = {\n  [ScreenplayElementType.SCENE_HEADING]: /^(INT\\.|EXT\\.|FADE IN:|FADE OUT\\.|CUT TO:)/i,\n  [ScreenplayElementType.TRANSITION]: /^(FADE IN:|FADE OUT\\.|CUT TO:|DISSOLVE TO:|SMASH CUT TO:)$/i,\n  [ScreenplayElementType.CHARACTER]: /^[A-Z][A-Z\\s]+(\\(.*\\))?$/,\n  [ScreenplayElementType.PARENTHETICAL]: /^\\(.*\\)$/,\n} as const;\n\n// Page break rules\nexport const PAGE_BREAK_RULES = {\n  // Don't break these elements\n  keepTogether: [\n    ScreenplayElementType.CHARACTER,\n    ScreenplayElementType.PARENTHETICAL,\n  ],\n  // Minimum lines to keep together\n  minLinesBeforeBreak: 2,\n  minLinesAfterBreak: 2,\n  // Orphan/widow control\n  preventOrphans: true,\n  preventWidows: true,\n} as const;\n\n/**\n * Detect screenplay element type from text content\n */\nexport function detectElementType(text: string): ScreenplayElementType {\n  const trimmedText = text.trim();\n  \n  if (!trimmedText) {\n    return ScreenplayElementType.ACTION;\n  }\n  \n  // Check patterns in order of specificity\n  if (ELEMENT_PATTERNS[ScreenplayElementType.SCENE_HEADING].test(trimmedText)) {\n    return ScreenplayElementType.SCENE_HEADING;\n  }\n  \n  if (ELEMENT_PATTERNS[ScreenplayElementType.TRANSITION].test(trimmedText)) {\n    return ScreenplayElementType.TRANSITION;\n  }\n  \n  if (ELEMENT_PATTERNS[ScreenplayElementType.PARENTHETICAL].test(trimmedText)) {\n    return ScreenplayElementType.PARENTHETICAL;\n  }\n  \n  if (ELEMENT_PATTERNS[ScreenplayElementType.CHARACTER].test(trimmedText)) {\n    return ScreenplayElementType.CHARACTER;\n  }\n  \n  return ScreenplayElementType.ACTION;\n}\n\n/**\n * Get CSS styles for a screenplay element type\n */\nexport function getElementStyles(elementType: ScreenplayElementType) {\n  const formatting = ELEMENT_FORMATTING[elementType];\n  \n  return {\n    marginLeft: `${formatting.marginLeft}px`,\n    marginRight: `${formatting.marginRight}px`,\n    marginTop: `${formatting.marginTop}px`,\n    marginBottom: `${formatting.marginBottom}px`,\n    textTransform: formatting.textTransform,\n    fontWeight: formatting.fontWeight,\n    fontStyle: (formatting as any).fontStyle || 'normal',\n    lineHeight: formatting.lineHeight,\n    textAlign: (formatting as any).textAlign || 'left',\n  };\n}\n\n/**\n * Calculate if a page break should occur\n */\nexport function shouldBreakPage(\n  currentPageHeight: number,\n  elementHeight: number,\n  elementType: ScreenplayElementType,\n  nextElementType?: ScreenplayElementType\n): boolean {\n  const availableHeight = CONTENT_AREA.height - currentPageHeight;\n  \n  // If element doesn't fit, break\n  if (elementHeight > availableHeight) {\n    return true;\n  }\n  \n  // Keep character names with dialogue\n  if (elementType === ScreenplayElementType.CHARACTER && \n      nextElementType === ScreenplayElementType.DIALOGUE) {\n    // Estimate dialogue height (rough calculation)\n    const estimatedDialogueHeight = 60; // ~3 lines\n    if (elementHeight + estimatedDialogueHeight > availableHeight) {\n      return true;\n    }\n  }\n  \n  // Keep parentheticals with dialogue\n  if (elementType === ScreenplayElementType.PARENTHETICAL &&\n      nextElementType === ScreenplayElementType.DIALOGUE) {\n    const estimatedDialogueHeight = 40; // ~2 lines\n    if (elementHeight + estimatedDialogueHeight > availableHeight) {\n      return true;\n    }\n  }\n  \n  return false;\n}\n\n/**\n * Format text according to screenplay element type\n */\nexport function formatScreenplayText(text: string, elementType: ScreenplayElementType): string {\n  const formatting = ELEMENT_FORMATTING[elementType];\n  \n  if (formatting.textTransform === 'uppercase') {\n    return text.toUpperCase();\n  }\n  \n  return text;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,kDAAkD;;;;;;;;;;;;;;AAC3C,MAAM,kBAAkB;IAC7B,OAAO;IACP,QAAQ;IACR,WAAW;IACX,cAAc;IACd,YAAY;IACZ,aAAa;AACf;AAGO,MAAM,qBAAqB;IAChC,OAAO,gBAAgB,KAAK,GAAG;IAC/B,QAAQ,gBAAgB,MAAM,GAAG;IACjC,WAAW,gBAAgB,SAAS,GAAG;IACvC,cAAc,gBAAgB,YAAY,GAAG;IAC7C,YAAY,gBAAgB,UAAU,GAAG;IACzC,aAAa,gBAAgB,WAAW,GAAG;AAC7C;AAGO,MAAM,eAAe;IAC1B,OAAO,mBAAmB,KAAK,GAAG,mBAAmB,UAAU,GAAG,mBAAmB,WAAW;IAChG,QAAQ,mBAAmB,MAAM,GAAG,mBAAmB,SAAS,GAAG,mBAAmB,YAAY;AACpG;AAGO,IAAA,AAAK,+CAAA;;;;;;;;;WAAA;;AAYL,MAAM,qBAAqB;IAChC,iBAAqC,EAAE;QACrC,YAAY;QACZ,aAAa;QACb,eAAe;QACf,YAAY;QACZ,WAAW;QACX,cAAc;QACd,YAAY;IACd;IACA,UAA8B,EAAE;QAC9B,YAAY;QACZ,aAAa;QACb,eAAe;QACf,YAAY;QACZ,WAAW;QACX,cAAc;QACd,YAAY;IACd;IACA,aAAiC,EAAE;QACjC,YAAY;QACZ,aAAa;QACb,eAAe;QACf,YAAY;QACZ,WAAW;QACX,cAAc;QACd,YAAY;IACd;IACA,YAAgC,EAAE;QAChC,YAAY;QACZ,aAAa;QACb,eAAe;QACf,YAAY;QACZ,WAAW;QACX,cAAc;QACd,YAAY;IACd;IACA,iBAAqC,EAAE;QACrC,YAAY;QACZ,aAAa;QACb,eAAe;QACf,YAAY;QACZ,WAAW;QACX,WAAW;QACX,cAAc;QACd,YAAY;IACd;IACA,cAAkC,EAAE;QAClC,YAAY;QACZ,aAAa;QACb,eAAe;QACf,YAAY;QACZ,WAAW;QACX,cAAc;QACd,YAAY;QACZ,WAAW;IACb;IACA,QAA4B,EAAE;QAC5B,YAAY;QACZ,aAAa;QACb,eAAe;QACf,YAAY;QACZ,WAAW;QACX,cAAc;QACd,YAAY;IACd;IACA,WAA+B,EAAE;QAC/B,YAAY;QACZ,aAAa;QACb,eAAe;QACf,YAAY;QACZ,WAAW;QACX,cAAc;QACd,YAAY;IACd;AACF;AAGO,MAAM,mBAAmB;IAC9B,iBAAqC,EAAE;IACvC,cAAkC,EAAE;IACpC,aAAiC,EAAE;IACnC,iBAAqC,EAAE;AACzC;AAGO,MAAM,mBAAmB;IAC9B,6BAA6B;IAC7B,cAAc;;;KAGb;IACD,iCAAiC;IACjC,qBAAqB;IACrB,oBAAoB;IACpB,uBAAuB;IACvB,gBAAgB;IAChB,eAAe;AACjB;AAKO,SAAS,kBAAkB,IAAY;IAC5C,MAAM,cAAc,KAAK,IAAI;IAE7B,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,yCAAyC;IACzC,IAAI,gBAAgB,iBAAqC,CAAC,IAAI,CAAC,cAAc;QAC3E;IACF;IAEA,IAAI,gBAAgB,cAAkC,CAAC,IAAI,CAAC,cAAc;QACxE;IACF;IAEA,IAAI,gBAAgB,iBAAqC,CAAC,IAAI,CAAC,cAAc;QAC3E;IACF;IAEA,IAAI,gBAAgB,aAAiC,CAAC,IAAI,CAAC,cAAc;QACvE;IACF;IAEA;AACF;AAKO,SAAS,iBAAiB,WAAkC;IACjE,MAAM,aAAa,kBAAkB,CAAC,YAAY;IAElD,OAAO;QACL,YAAY,GAAG,WAAW,UAAU,CAAC,EAAE,CAAC;QACxC,aAAa,GAAG,WAAW,WAAW,CAAC,EAAE,CAAC;QAC1C,WAAW,GAAG,WAAW,SAAS,CAAC,EAAE,CAAC;QACtC,cAAc,GAAG,WAAW,YAAY,CAAC,EAAE,CAAC;QAC5C,eAAe,WAAW,aAAa;QACvC,YAAY,WAAW,UAAU;QACjC,WAAW,AAAC,WAAmB,SAAS,IAAI;QAC5C,YAAY,WAAW,UAAU;QACjC,WAAW,AAAC,WAAmB,SAAS,IAAI;IAC9C;AACF;AAKO,SAAS,gBACd,iBAAyB,EACzB,aAAqB,EACrB,WAAkC,EAClC,eAAuC;IAEvC,MAAM,kBAAkB,aAAa,MAAM,GAAG;IAE9C,gCAAgC;IAChC,IAAI,gBAAgB,iBAAiB;QACnC,OAAO;IACT;IAEA,qCAAqC;IACrC,IAAI,+BACA,gCAAoD;QACtD,+CAA+C;QAC/C,MAAM,0BAA0B,IAAI,WAAW;QAC/C,IAAI,gBAAgB,0BAA0B,iBAAiB;YAC7D,OAAO;QACT;IACF;IAEA,oCAAoC;IACpC,IAAI,mCACA,gCAAoD;QACtD,MAAM,0BAA0B,IAAI,WAAW;QAC/C,IAAI,gBAAgB,0BAA0B,iBAAiB;YAC7D,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAKO,SAAS,qBAAqB,IAAY,EAAE,WAAkC;IACnF,MAAM,aAAa,kBAAkB,CAAC,YAAY;IAElD,IAAI,WAAW,aAAa,KAAK,aAAa;QAC5C,OAAO,KAAK,WAAW;IACzB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 9107, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ScreenplayToolbar.tsx"], "sourcesContent": ["/**\n * Screenplay-specific toolbar component\n * Provides formatting buttons for screenplay elements\n */\n\nimport React from 'react';\nimport { But<PERSON> } from './ui/button';\nimport { \n  MapPin, \n  Type, \n  MessageSquare, \n  ArrowRight, \n  Camera, \n  FileText,\n  MessageSquare as MessageSquareIcon\n} from 'lucide-react';\nimport { ScreenplayElementType } from '../lib/screenplayFormatting';\n\ninterface ScreenplayToolbarProps {\n  onFormatElement: (elementType: ScreenplayElementType) => void;\n  onToggleComments: () => void;\n  commentsCount: number;\n  showComments: boolean;\n  currentElementType?: ScreenplayElementType;\n  disabled?: boolean;\n}\n\nexport function ScreenplayToolbar({\n  onFormatElement,\n  onToggleComments,\n  commentsCount,\n  showComments,\n  currentElementType,\n  disabled = false,\n}: ScreenplayToolbarProps) {\n  const formatButtons = [\n    {\n      type: ScreenplayElementType.SCENE_HEADING,\n      label: 'Scene Heading',\n      icon: MapPin,\n      description: 'INT./EXT. Location',\n      shortcut: 'Ctrl+1',\n    },\n    {\n      type: ScreenplayElementType.ACTION,\n      label: 'Action',\n      icon: FileText,\n      description: 'Action description',\n      shortcut: 'Ctrl+2',\n    },\n    {\n      type: ScreenplayElementType.CHARACTER,\n      label: 'Character',\n      icon: Type,\n      description: 'Character name',\n      shortcut: 'Ctrl+3',\n    },\n    {\n      type: ScreenplayElementType.DIALOGUE,\n      label: 'Dialogue',\n      icon: MessageSquare,\n      description: 'Character dialogue',\n      shortcut: 'Ctrl+4',\n    },\n    {\n      type: ScreenplayElementType.PARENTHETICAL,\n      label: 'Parenthetical',\n      icon: MessageSquareIcon,\n      description: '(direction)',\n      shortcut: 'Ctrl+5',\n    },\n    {\n      type: ScreenplayElementType.TRANSITION,\n      label: 'Transition',\n      icon: ArrowRight,\n      description: 'CUT TO:',\n      shortcut: 'Ctrl+6',\n    },\n  ];\n\n  return (\n    <div className=\"screenplay-toolbar border-b bg-white px-6 py-3\">\n      <div className=\"flex items-center justify-between\">\n        {/* Format Buttons */}\n        <div className=\"flex items-center gap-1\">\n          <span className=\"text-sm font-medium text-gray-700 mr-3\">\n            Screenplay Format:\n          </span>\n          \n          {formatButtons.map((button) => {\n            const Icon = button.icon;\n            const isActive = currentElementType === button.type;\n            \n            return (\n              <Button\n                key={button.type}\n                variant={isActive ? \"default\" : \"ghost\"}\n                size=\"sm\"\n                onClick={() => onFormatElement(button.type)}\n                disabled={disabled}\n                className={`gap-2 ${isActive ? 'bg-blue-600 text-white' : ''}`}\n                title={`${button.description} (${button.shortcut})`}\n              >\n                <Icon className=\"h-4 w-4\" />\n                <span className=\"hidden sm:inline\">{button.label}</span>\n              </Button>\n            );\n          })}\n        </div>\n\n        {/* Right side controls */}\n        <div className=\"flex items-center gap-3\">\n          {/* Current Element Indicator */}\n          {currentElementType && (\n            <div className=\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\">\n              {formatButtons.find(b => b.type === currentElementType)?.label || 'Unknown'}\n            </div>\n          )}\n          \n          {/* Comments Toggle */}\n          <Button\n            variant={showComments ? \"default\" : \"ghost\"}\n            size=\"sm\"\n            onClick={onToggleComments}\n            className={`gap-2 ${showComments ? 'bg-blue-600 text-white' : ''}`}\n            title=\"Toggle Comments Sidebar\"\n          >\n            <MessageSquareIcon className=\"h-4 w-4\" />\n            <span className=\"hidden sm:inline\">Comments</span>\n            {commentsCount > 0 && (\n              <span className=\"bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[1.25rem] h-5 flex items-center justify-center\">\n                {commentsCount}\n              </span>\n            )}\n          </Button>\n        </div>\n      </div>\n\n      {/* Keyboard Shortcuts Help */}\n      <div className=\"mt-2 text-xs text-gray-500 flex items-center gap-4\">\n        <span>Keyboard shortcuts:</span>\n        <div className=\"flex items-center gap-3\">\n          <span>Ctrl+1-6: Format elements</span>\n          <span>Tab: Next element</span>\n          <span>Enter: New line/element</span>\n        </div>\n      </div>\n\n\n    </div>\n  );\n}\n\nexport default ScreenplayToolbar;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AACA;AAAA;AAAA;AAAA;AAAA;AASA;;;;;AAWO,SAAS,kBAAkB,EAChC,eAAe,EACf,gBAAgB,EAChB,aAAa,EACb,YAAY,EACZ,kBAAkB,EAClB,WAAW,KAAK,EACO;IACvB,MAAM,gBAAgB;QACpB;YACE,MAAM,kIAAA,CAAA,wBAAqB,CAAC,aAAa;YACzC,OAAO;YACP,MAAM,0MAAA,CAAA,SAAM;YACZ,aAAa;YACb,UAAU;QACZ;QACA;YACE,MAAM,kIAAA,CAAA,wBAAqB,CAAC,MAAM;YAClC,OAAO;YACP,MAAM,8MAAA,CAAA,WAAQ;YACd,aAAa;YACb,UAAU;QACZ;QACA;YACE,MAAM,kIAAA,CAAA,wBAAqB,CAAC,SAAS;YACrC,OAAO;YACP,MAAM,kMAAA,CAAA,OAAI;YACV,aAAa;YACb,UAAU;QACZ;QACA;YACE,MAAM,kIAAA,CAAA,wBAAqB,CAAC,QAAQ;YACpC,OAAO;YACP,MAAM,wNAAA,CAAA,gBAAa;YACnB,aAAa;YACb,UAAU;QACZ;QACA;YACE,MAAM,kIAAA,CAAA,wBAAqB,CAAC,aAAa;YACzC,OAAO;YACP,MAAM,wNAAA,CAAA,gBAAiB;YACvB,aAAa;YACb,UAAU;QACZ;QACA;YACE,MAAM,kIAAA,CAAA,wBAAqB,CAAC,UAAU;YACtC,OAAO;YACP,MAAM,kNAAA,CAAA,aAAU;YAChB,aAAa;YACb,UAAU;QACZ;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAyC;;;;;;4BAIxD,cAAc,GAAG,CAAC,CAAC;gCAClB,MAAM,OAAO,OAAO,IAAI;gCACxB,MAAM,WAAW,uBAAuB,OAAO,IAAI;gCAEnD,qBACE,8OAAC,kIAAA,CAAA,SAAM;oCAEL,SAAS,WAAW,YAAY;oCAChC,MAAK;oCACL,SAAS,IAAM,gBAAgB,OAAO,IAAI;oCAC1C,UAAU;oCACV,WAAW,CAAC,MAAM,EAAE,WAAW,2BAA2B,IAAI;oCAC9D,OAAO,GAAG,OAAO,WAAW,CAAC,EAAE,EAAE,OAAO,QAAQ,CAAC,CAAC,CAAC;;sDAEnD,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAAoB,OAAO,KAAK;;;;;;;mCAT3C,OAAO,IAAI;;;;;4BAYtB;;;;;;;kCAIF,8OAAC;wBAAI,WAAU;;4BAEZ,oCACC,8OAAC;gCAAI,WAAU;0CACZ,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,qBAAqB,SAAS;;;;;;0CAKtE,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,eAAe,YAAY;gCACpC,MAAK;gCACL,SAAS;gCACT,WAAW,CAAC,MAAM,EAAE,eAAe,2BAA2B,IAAI;gCAClE,OAAM;;kDAEN,8OAAC,wNAAA,CAAA,gBAAiB;wCAAC,WAAU;;;;;;kDAC7B,8OAAC;wCAAK,WAAU;kDAAmB;;;;;;oCAClC,gBAAgB,mBACf,8OAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;;;;;;;;;;;;;0BAQX,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAK;;;;;;kCACN,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAOhB;uCAEe", "debugId": null}}, {"offset": {"line": 9346, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/PaginatedEditor.tsx"], "sourcesContent": ["/**\n * Paginated editor component for screenplay formatting\n * Provides page-based layout with proper page breaks and formatting\n */\n\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport { PAGE_DIMENSIONS_PX, CONTENT_AREA } from '../lib/screenplayFormatting';\n\ninterface PaginatedEditorProps {\n  children: React.ReactNode;\n  className?: string;\n  onPageChange?: (pageNumber: number, totalPages: number) => void;\n}\n\ninterface PageInfo {\n  pageNumber: number;\n  startOffset: number;\n  endOffset: number;\n  height: number;\n}\n\nexport function PaginatedEditor({ \n  children, \n  className = '', \n  onPageChange \n}: PaginatedEditorProps) {\n  const containerRef = useRef<HTMLDivElement>(null);\n  const [pages, setPages] = useState<PageInfo[]>([]);\n  const [currentPage, setCurrentPage] = useState(1);\n\n  // Calculate page breaks based on content\n  const calculatePageBreaks = useCallback(() => {\n    if (!containerRef.current) return;\n\n    const container = containerRef.current;\n    const elements = Array.from(container.querySelectorAll('[data-screenplay-element]'));\n    \n    const newPages: PageInfo[] = [];\n    let currentPageHeight = 0;\n    let currentPageStart = 0;\n    let pageNumber = 1;\n\n    elements.forEach((element, index) => {\n      const elementHeight = element.getBoundingClientRect().height;\n      const elementType = element.getAttribute('data-screenplay-element');\n      \n      // Check if we need a page break\n      if (currentPageHeight + elementHeight > CONTENT_AREA.height && currentPageHeight > 0) {\n        // Finish current page\n        newPages.push({\n          pageNumber,\n          startOffset: currentPageStart,\n          endOffset: index - 1,\n          height: currentPageHeight,\n        });\n        \n        // Start new page\n        pageNumber++;\n        currentPageStart = index;\n        currentPageHeight = elementHeight;\n      } else {\n        currentPageHeight += elementHeight;\n      }\n    });\n\n    // Add the last page\n    if (elements.length > 0) {\n      newPages.push({\n        pageNumber,\n        startOffset: currentPageStart,\n        endOffset: elements.length - 1,\n        height: currentPageHeight,\n      });\n    }\n\n    setPages(newPages);\n    onPageChange?.(currentPage, newPages.length);\n  }, [currentPage, onPageChange]);\n\n  // Recalculate page breaks when content changes\n  useEffect(() => {\n    const observer = new MutationObserver(calculatePageBreaks);\n    const resizeObserver = new ResizeObserver(calculatePageBreaks);\n    \n    if (containerRef.current) {\n      observer.observe(containerRef.current, {\n        childList: true,\n        subtree: true,\n        characterData: true,\n      });\n      resizeObserver.observe(containerRef.current);\n    }\n\n    // Initial calculation\n    setTimeout(calculatePageBreaks, 100);\n\n    return () => {\n      observer.disconnect();\n      resizeObserver.disconnect();\n    };\n  }, [calculatePageBreaks]);\n\n  return (\n    <div className=\"paginated-editor-container\">\n      {/* Page Navigation */}\n      <div className=\"page-navigation sticky top-0 z-10 bg-white border-b px-4 py-2 flex items-center justify-between text-sm text-gray-600\">\n        <div className=\"flex items-center gap-4\">\n          <span>Page {currentPage} of {pages.length}</span>\n          <div className=\"flex items-center gap-2\">\n            <button\n              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}\n              disabled={currentPage <= 1}\n              className=\"px-2 py-1 border rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\"\n            >\n              Previous\n            </button>\n            <button\n              onClick={() => setCurrentPage(Math.min(pages.length, currentPage + 1))}\n              disabled={currentPage >= pages.length}\n              className=\"px-2 py-1 border rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\"\n            >\n              Next\n            </button>\n          </div>\n        </div>\n        <div className=\"text-xs text-gray-500\">\n          Industry Standard Format\n        </div>\n      </div>\n\n      {/* Pages Container */}\n      <div className=\"pages-container bg-gray-100 min-h-screen p-8\">\n        <div className=\"pages-wrapper max-w-none mx-auto\">\n          {pages.map((page) => (\n            <div\n              key={page.pageNumber}\n              className={`page ${currentPage === page.pageNumber ? 'current-page' : ''}`}\n              style={{\n                width: `${PAGE_DIMENSIONS_PX.width}px`,\n                minHeight: `${PAGE_DIMENSIONS_PX.height}px`,\n                margin: '0 auto 2rem auto',\n                backgroundColor: 'white',\n                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n                position: 'relative',\n                padding: `${PAGE_DIMENSIONS_PX.marginTop}px ${PAGE_DIMENSIONS_PX.marginRight}px ${PAGE_DIMENSIONS_PX.marginBottom}px ${PAGE_DIMENSIONS_PX.marginLeft}px`,\n              }}\n            >\n              {/* Page Number */}\n              <div \n                className=\"page-number absolute text-xs text-gray-500\"\n                style={{\n                  top: `${PAGE_DIMENSIONS_PX.marginTop / 2}px`,\n                  right: `${PAGE_DIMENSIONS_PX.marginRight}px`,\n                }}\n              >\n                {page.pageNumber}\n              </div>\n\n              {/* Page Content */}\n              <div \n                className=\"page-content\"\n                style={{\n                  minHeight: `${CONTENT_AREA.height}px`,\n                  fontFamily: '\"Courier New\", monospace',\n                  fontSize: '12pt',\n                  lineHeight: '1.2',\n                }}\n              >\n                {/* This will be populated by the editor content */}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Hidden content container for measurement */}\n      <div\n        ref={containerRef}\n        className={`screenplay-content-measurer ${className}`}\n        style={{\n          position: 'absolute',\n          left: '-9999px',\n          top: '-9999px',\n          width: `${CONTENT_AREA.width}px`,\n          fontFamily: '\"Courier New\", monospace',\n          fontSize: '12pt',\n          lineHeight: '1.2',\n          visibility: 'hidden',\n        }}\n      >\n        {children}\n      </div>\n\n      <style jsx>{`\n        .paginated-editor-container {\n          height: 100%;\n          overflow-y: auto;\n        }\n\n        .page {\n          transition: opacity 0.2s ease;\n        }\n\n        .page:not(.current-page) {\n          opacity: 0.7;\n        }\n\n        .current-page {\n          opacity: 1;\n          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\n        }\n\n        .page-content {\n          position: relative;\n        }\n\n        /* Print styles */\n        @media print {\n          .page-navigation {\n            display: none;\n          }\n          \n          .pages-container {\n            background: white;\n            padding: 0;\n          }\n          \n          .page {\n            box-shadow: none;\n            margin: 0;\n            page-break-after: always;\n          }\n          \n          .page:last-child {\n            page-break-after: auto;\n          }\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default PaginatedEditor;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAED;AACA;;;;;AAeO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,YAAY,EAAE,EACd,YAAY,EACS;IACrB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,yCAAyC;IACzC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,IAAI,CAAC,aAAa,OAAO,EAAE;QAE3B,MAAM,YAAY,aAAa,OAAO;QACtC,MAAM,WAAW,MAAM,IAAI,CAAC,UAAU,gBAAgB,CAAC;QAEvD,MAAM,WAAuB,EAAE;QAC/B,IAAI,oBAAoB;QACxB,IAAI,mBAAmB;QACvB,IAAI,aAAa;QAEjB,SAAS,OAAO,CAAC,CAAC,SAAS;YACzB,MAAM,gBAAgB,QAAQ,qBAAqB,GAAG,MAAM;YAC5D,MAAM,cAAc,QAAQ,YAAY,CAAC;YAEzC,gCAAgC;YAChC,IAAI,oBAAoB,gBAAgB,kIAAA,CAAA,eAAY,CAAC,MAAM,IAAI,oBAAoB,GAAG;gBACpF,sBAAsB;gBACtB,SAAS,IAAI,CAAC;oBACZ;oBACA,aAAa;oBACb,WAAW,QAAQ;oBACnB,QAAQ;gBACV;gBAEA,iBAAiB;gBACjB;gBACA,mBAAmB;gBACnB,oBAAoB;YACtB,OAAO;gBACL,qBAAqB;YACvB;QACF;QAEA,oBAAoB;QACpB,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,SAAS,IAAI,CAAC;gBACZ;gBACA,aAAa;gBACb,WAAW,SAAS,MAAM,GAAG;gBAC7B,QAAQ;YACV;QACF;QAEA,SAAS;QACT,eAAe,aAAa,SAAS,MAAM;IAC7C,GAAG;QAAC;QAAa;KAAa;IAE9B,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,iBAAiB;QACtC,MAAM,iBAAiB,IAAI,eAAe;QAE1C,IAAI,aAAa,OAAO,EAAE;YACxB,SAAS,OAAO,CAAC,aAAa,OAAO,EAAE;gBACrC,WAAW;gBACX,SAAS;gBACT,eAAe;YACjB;YACA,eAAe,OAAO,CAAC,aAAa,OAAO;QAC7C;QAEA,sBAAsB;QACtB,WAAW,qBAAqB;QAEhC,OAAO;YACL,SAAS,UAAU;YACnB,eAAe,UAAU;QAC3B;IACF,GAAG;QAAC;KAAoB;IAExB,qBACE,8OAAC;kDAAc;;0BAEb,8OAAC;0DAAc;;kCACb,8OAAC;kEAAc;;0CACb,8OAAC;;;oCAAK;oCAAM;oCAAY;oCAAK,MAAM,MAAM;;;;;;;0CACzC,8OAAC;0EAAc;;kDACb,8OAAC;wCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;wCACxD,UAAU,eAAe;kFACf;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,cAAc;wCACnE,UAAU,eAAe,MAAM,MAAM;kFAC3B;kDACX;;;;;;;;;;;;;;;;;;kCAKL,8OAAC;kEAAc;kCAAwB;;;;;;;;;;;;0BAMzC,8OAAC;0DAAc;0BACb,cAAA,8OAAC;8DAAc;8BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;4BAGC,OAAO;gCACL,OAAO,GAAG,kIAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;gCACtC,WAAW,GAAG,kIAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC;gCAC3C,QAAQ;gCACR,iBAAiB;gCACjB,WAAW;gCACX,UAAU;gCACV,SAAS,GAAG,kIAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,GAAG,EAAE,kIAAA,CAAA,qBAAkB,CAAC,WAAW,CAAC,GAAG,EAAE,kIAAA,CAAA,qBAAkB,CAAC,YAAY,CAAC,GAAG,EAAE,kIAAA,CAAA,qBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;4BAC1J;sEATW,CAAC,KAAK,EAAE,gBAAgB,KAAK,UAAU,GAAG,iBAAiB,IAAI;;8CAY1E,8OAAC;oCAEC,OAAO;wCACL,KAAK,GAAG,kIAAA,CAAA,qBAAkB,CAAC,SAAS,GAAG,EAAE,EAAE,CAAC;wCAC5C,OAAO,GAAG,kIAAA,CAAA,qBAAkB,CAAC,WAAW,CAAC,EAAE,CAAC;oCAC9C;8EAJU;8CAMT,KAAK,UAAU;;;;;;8CAIlB,8OAAC;oCAEC,OAAO;wCACL,WAAW,GAAG,kIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,EAAE,CAAC;wCACrC,YAAY;wCACZ,UAAU;wCACV,YAAY;oCACd;8EANU;;;;;;;2BAzBP,KAAK,UAAU;;;;;;;;;;;;;;;0BAyC5B,8OAAC;gBACC,KAAK;gBAEL,OAAO;oBACL,UAAU;oBACV,MAAM;oBACN,KAAK;oBACL,OAAO,GAAG,kIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,EAAE,CAAC;oBAChC,YAAY;oBACZ,UAAU;oBACV,YAAY;oBACZ,YAAY;gBACd;0DAVW,CAAC,4BAA4B,EAAE,WAAW;0BAYpD;;;;;;;;;;;;;;;;AAkDT;uCAEe", "debugId": null}}, {"offset": {"line": 9595, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/lib/screenplaySchema.ts"], "sourcesContent": ["/**\n * Custom BlockNote schema for screenplay formatting\n * Defines custom block types for screenplay elements\n */\n\nimport { defaultBlockSpecs, BlockNoteSchema } from \"@blocknote/core\";\nimport { ScreenplayElementType, getElementStyles, formatScreenplayText } from \"./screenplayFormatting\";\n\n// Define custom block specs for screenplay elements\nexport const screenplayBlockSpecs = {\n  // Keep the default paragraph block but modify it\n  paragraph: {\n    ...defaultBlockSpecs.paragraph,\n    propSchema: {\n      ...defaultBlockSpecs.paragraph.propSchema,\n      screenplayType: {\n        default: ScreenplayElementType.ACTION,\n        values: Object.values(ScreenplayElementType),\n      },\n    },\n  },\n  \n  // Scene heading block\n  sceneHeading: {\n    type: \"sceneHeading\" as const,\n    propSchema: {\n      textColor: {\n        default: \"default\",\n        values: [\"default\", \"gray\", \"brown\", \"red\", \"orange\", \"yellow\", \"green\", \"blue\", \"purple\", \"pink\"],\n      },\n      backgroundColor: {\n        default: \"default\",\n        values: [\"default\", \"gray\", \"brown\", \"red\", \"orange\", \"yellow\", \"green\", \"blue\", \"purple\", \"pink\"],\n      },\n    },\n    content: \"inline\",\n  },\n  \n  // Character name block\n  character: {\n    type: \"character\" as const,\n    propSchema: {\n      textColor: {\n        default: \"default\",\n        values: [\"default\", \"gray\", \"brown\", \"red\", \"orange\", \"yellow\", \"green\", \"blue\", \"purple\", \"pink\"],\n      },\n      backgroundColor: {\n        default: \"default\",\n        values: [\"default\", \"gray\", \"brown\", \"red\", \"orange\", \"yellow\", \"green\", \"blue\", \"purple\", \"pink\"],\n      },\n    },\n    content: \"inline\",\n  },\n  \n  // Dialogue block\n  dialogue: {\n    type: \"dialogue\" as const,\n    propSchema: {\n      textColor: {\n        default: \"default\",\n        values: [\"default\", \"gray\", \"brown\", \"red\", \"orange\", \"yellow\", \"green\", \"blue\", \"purple\", \"pink\"],\n      },\n      backgroundColor: {\n        default: \"default\",\n        values: [\"default\", \"gray\", \"brown\", \"red\", \"orange\", \"yellow\", \"green\", \"blue\", \"purple\", \"pink\"],\n      },\n    },\n    content: \"inline\",\n  },\n  \n  // Parenthetical block\n  parenthetical: {\n    type: \"parenthetical\" as const,\n    propSchema: {\n      textColor: {\n        default: \"default\",\n        values: [\"default\", \"gray\", \"brown\", \"red\", \"orange\", \"yellow\", \"green\", \"blue\", \"purple\", \"pink\"],\n      },\n      backgroundColor: {\n        default: \"default\",\n        values: [\"default\", \"gray\", \"brown\", \"red\", \"orange\", \"yellow\", \"green\", \"blue\", \"purple\", \"pink\"],\n      },\n    },\n    content: \"inline\",\n  },\n  \n  // Transition block\n  transition: {\n    type: \"transition\" as const,\n    propSchema: {\n      textColor: {\n        default: \"default\",\n        values: [\"default\", \"gray\", \"brown\", \"red\", \"orange\", \"yellow\", \"green\", \"blue\", \"purple\", \"pink\"],\n      },\n      backgroundColor: {\n        default: \"default\",\n        values: [\"default\", \"gray\", \"brown\", \"red\", \"orange\", \"yellow\", \"green\", \"blue\", \"purple\", \"pink\"],\n      },\n    },\n    content: \"inline\",\n  },\n  \n  // Action block (default)\n  action: {\n    type: \"action\" as const,\n    propSchema: {\n      textColor: {\n        default: \"default\",\n        values: [\"default\", \"gray\", \"brown\", \"red\", \"orange\", \"yellow\", \"green\", \"blue\", \"purple\", \"pink\"],\n      },\n      backgroundColor: {\n        default: \"default\",\n        values: [\"default\", \"gray\", \"brown\", \"red\", \"orange\", \"yellow\", \"green\", \"blue\", \"purple\", \"pink\"],\n      },\n    },\n    content: \"inline\",\n  },\n};\n\n// Create the screenplay schema\nexport const screenplaySchema = BlockNoteSchema.create({\n  blockSpecs: screenplayBlockSpecs,\n});\n\n// Type for the screenplay editor\nexport type ScreenplayEditor = typeof screenplaySchema.BlockNoteEditor;\nexport type ScreenplayBlock = typeof screenplaySchema.Block;\n\n// Helper function to convert block type to screenplay element type\nexport function blockTypeToScreenplayType(blockType: string): ScreenplayElementType {\n  switch (blockType) {\n    case 'sceneHeading':\n      return ScreenplayElementType.SCENE_HEADING;\n    case 'character':\n      return ScreenplayElementType.CHARACTER;\n    case 'dialogue':\n      return ScreenplayElementType.DIALOGUE;\n    case 'parenthetical':\n      return ScreenplayElementType.PARENTHETICAL;\n    case 'transition':\n      return ScreenplayElementType.TRANSITION;\n    case 'action':\n    default:\n      return ScreenplayElementType.ACTION;\n  }\n}\n\n// Helper function to convert screenplay element type to block type\nexport function screenplayTypeToBlockType(screenplayType: ScreenplayElementType): string {\n  switch (screenplayType) {\n    case ScreenplayElementType.SCENE_HEADING:\n      return 'sceneHeading';\n    case ScreenplayElementType.CHARACTER:\n      return 'character';\n    case ScreenplayElementType.DIALOGUE:\n      return 'dialogue';\n    case ScreenplayElementType.PARENTHETICAL:\n      return 'parenthetical';\n    case ScreenplayElementType.TRANSITION:\n      return 'transition';\n    case ScreenplayElementType.ACTION:\n    default:\n      return 'action';\n  }\n}\n\n// Auto-formatting function for screenplay elements\nexport function autoFormatScreenplayBlock(\n  text: string, \n  currentBlockType: string\n): { blockType: string; formattedText: string } {\n  const trimmedText = text.trim();\n  \n  if (!trimmedText) {\n    return { blockType: 'action', formattedText: text };\n  }\n  \n  // Scene heading detection\n  if (/^(INT\\.|EXT\\.|FADE IN:|FADE OUT\\.|CUT TO:)/i.test(trimmedText)) {\n    return {\n      blockType: 'sceneHeading',\n      formattedText: formatScreenplayText(text, ScreenplayElementType.SCENE_HEADING),\n    };\n  }\n  \n  // Transition detection\n  if (/^(FADE IN:|FADE OUT\\.|CUT TO:|DISSOLVE TO:|SMASH CUT TO:)$/i.test(trimmedText)) {\n    return {\n      blockType: 'transition',\n      formattedText: formatScreenplayText(text, ScreenplayElementType.TRANSITION),\n    };\n  }\n  \n  // Parenthetical detection\n  if (/^\\(.*\\)$/.test(trimmedText)) {\n    return {\n      blockType: 'parenthetical',\n      formattedText: formatScreenplayText(text, ScreenplayElementType.PARENTHETICAL),\n    };\n  }\n  \n  // Character name detection (all caps, possibly with extension)\n  if (/^[A-Z][A-Z\\s]+(\\(.*\\))?$/.test(trimmedText) && currentBlockType !== 'dialogue') {\n    return {\n      blockType: 'character',\n      formattedText: formatScreenplayText(text, ScreenplayElementType.CHARACTER),\n    };\n  }\n  \n  // If previous block was character, this is likely dialogue\n  // (This would need to be handled in the editor logic)\n  \n  // Default to action\n  return {\n    blockType: 'action',\n    formattedText: formatScreenplayText(text, ScreenplayElementType.ACTION),\n  };\n}\n\n// CSS class mapping for screenplay elements\nexport const SCREENPLAY_ELEMENT_CLASSES = {\n  sceneHeading: 'screenplay-scene-heading',\n  character: 'screenplay-character',\n  dialogue: 'screenplay-dialogue',\n  parenthetical: 'screenplay-parenthetical',\n  transition: 'screenplay-transition',\n  action: 'screenplay-action',\n} as const;\n\n// Generate CSS styles for screenplay elements\nexport function generateScreenplayCSS(): string {\n  const styles = Object.entries(SCREENPLAY_ELEMENT_CLASSES).map(([blockType, className]) => {\n    const elementType = blockTypeToScreenplayType(blockType);\n    const styles = getElementStyles(elementType);\n\n    return `\n      .${className} {\n        margin-left: ${styles.marginLeft};\n        margin-right: ${styles.marginRight};\n        margin-top: ${styles.marginTop};\n        margin-bottom: ${styles.marginBottom};\n        text-transform: ${styles.textTransform};\n        font-weight: ${styles.fontWeight};\n        font-style: ${styles.fontStyle};\n        line-height: ${styles.lineHeight};\n        text-align: ${styles.textAlign};\n        font-family: \"Courier New\", monospace;\n        font-size: 12pt;\n        color: #000;\n        background: transparent;\n      }\n    `;\n  }).join('\\n');\n\n  // Add general screenplay editor styles\n  const generalStyles = `\n    .screenplay-content {\n      font-family: \"Courier New\", monospace;\n      font-size: 12pt;\n      line-height: 1.5;\n      color: #000;\n      background: white;\n      padding: 1in;\n      max-width: 8.5in;\n      margin: 0 auto;\n      min-height: 11in;\n    }\n\n    .screenplay-content .bn-editor {\n      font-family: \"Courier New\", monospace !important;\n      font-size: 12pt !important;\n    }\n\n    .screenplay-content p {\n      margin: 0;\n      padding: 0;\n      font-family: \"Courier New\", monospace;\n      font-size: 12pt;\n      line-height: 1.5;\n    }\n\n    /* Hide BlockNote formatting toolbar for screenplay */\n    .screenplay-content .bn-formatting-toolbar {\n      display: none !important;\n    }\n\n    /* Style the side menu for screenplay */\n    .screenplay-content .bn-side-menu {\n      background: rgba(255, 255, 255, 0.9);\n      border: 1px solid #ddd;\n      border-radius: 4px;\n    }\n\n    /* Screenplay toolbar styles */\n    .screenplay-toolbar {\n      position: sticky;\n      top: 0;\n      z-index: 10;\n      backdrop-filter: blur(8px);\n      background-color: rgba(255, 255, 255, 0.95);\n    }\n\n    @media (max-width: 640px) {\n      .screenplay-toolbar .gap-2 span {\n        display: none;\n      }\n    }\n  `;\n\n  return styles + '\\n' + generalStyles;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAED;AACA;;;AAGO,MAAM,uBAAuB;IAClC,iDAAiD;IACjD,WAAW;QACT,GAAG,wJAAA,CAAA,oBAAiB,CAAC,SAAS;QAC9B,YAAY;YACV,GAAG,wJAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,UAAU;YACzC,gBAAgB;gBACd,SAAS,kIAAA,CAAA,wBAAqB,CAAC,MAAM;gBACrC,QAAQ,OAAO,MAAM,CAAC,kIAAA,CAAA,wBAAqB;YAC7C;QACF;IACF;IAEA,sBAAsB;IACtB,cAAc;QACZ,MAAM;QACN,YAAY;YACV,WAAW;gBACT,SAAS;gBACT,QAAQ;oBAAC;oBAAW;oBAAQ;oBAAS;oBAAO;oBAAU;oBAAU;oBAAS;oBAAQ;oBAAU;iBAAO;YACpG;YACA,iBAAiB;gBACf,SAAS;gBACT,QAAQ;oBAAC;oBAAW;oBAAQ;oBAAS;oBAAO;oBAAU;oBAAU;oBAAS;oBAAQ;oBAAU;iBAAO;YACpG;QACF;QACA,SAAS;IACX;IAEA,uBAAuB;IACvB,WAAW;QACT,MAAM;QACN,YAAY;YACV,WAAW;gBACT,SAAS;gBACT,QAAQ;oBAAC;oBAAW;oBAAQ;oBAAS;oBAAO;oBAAU;oBAAU;oBAAS;oBAAQ;oBAAU;iBAAO;YACpG;YACA,iBAAiB;gBACf,SAAS;gBACT,QAAQ;oBAAC;oBAAW;oBAAQ;oBAAS;oBAAO;oBAAU;oBAAU;oBAAS;oBAAQ;oBAAU;iBAAO;YACpG;QACF;QACA,SAAS;IACX;IAEA,iBAAiB;IACjB,UAAU;QACR,MAAM;QACN,YAAY;YACV,WAAW;gBACT,SAAS;gBACT,QAAQ;oBAAC;oBAAW;oBAAQ;oBAAS;oBAAO;oBAAU;oBAAU;oBAAS;oBAAQ;oBAAU;iBAAO;YACpG;YACA,iBAAiB;gBACf,SAAS;gBACT,QAAQ;oBAAC;oBAAW;oBAAQ;oBAAS;oBAAO;oBAAU;oBAAU;oBAAS;oBAAQ;oBAAU;iBAAO;YACpG;QACF;QACA,SAAS;IACX;IAEA,sBAAsB;IACtB,eAAe;QACb,MAAM;QACN,YAAY;YACV,WAAW;gBACT,SAAS;gBACT,QAAQ;oBAAC;oBAAW;oBAAQ;oBAAS;oBAAO;oBAAU;oBAAU;oBAAS;oBAAQ;oBAAU;iBAAO;YACpG;YACA,iBAAiB;gBACf,SAAS;gBACT,QAAQ;oBAAC;oBAAW;oBAAQ;oBAAS;oBAAO;oBAAU;oBAAU;oBAAS;oBAAQ;oBAAU;iBAAO;YACpG;QACF;QACA,SAAS;IACX;IAEA,mBAAmB;IACnB,YAAY;QACV,MAAM;QACN,YAAY;YACV,WAAW;gBACT,SAAS;gBACT,QAAQ;oBAAC;oBAAW;oBAAQ;oBAAS;oBAAO;oBAAU;oBAAU;oBAAS;oBAAQ;oBAAU;iBAAO;YACpG;YACA,iBAAiB;gBACf,SAAS;gBACT,QAAQ;oBAAC;oBAAW;oBAAQ;oBAAS;oBAAO;oBAAU;oBAAU;oBAAS;oBAAQ;oBAAU;iBAAO;YACpG;QACF;QACA,SAAS;IACX;IAEA,yBAAyB;IACzB,QAAQ;QACN,MAAM;QACN,YAAY;YACV,WAAW;gBACT,SAAS;gBACT,QAAQ;oBAAC;oBAAW;oBAAQ;oBAAS;oBAAO;oBAAU;oBAAU;oBAAS;oBAAQ;oBAAU;iBAAO;YACpG;YACA,iBAAiB;gBACf,SAAS;gBACT,QAAQ;oBAAC;oBAAW;oBAAQ;oBAAS;oBAAO;oBAAU;oBAAU;oBAAS;oBAAQ;oBAAU;iBAAO;YACpG;QACF;QACA,SAAS;IACX;AACF;AAGO,MAAM,mBAAmB,wJAAA,CAAA,kBAAe,CAAC,MAAM,CAAC;IACrD,YAAY;AACd;AAOO,SAAS,0BAA0B,SAAiB;IACzD,OAAQ;QACN,KAAK;YACH,OAAO,kIAAA,CAAA,wBAAqB,CAAC,aAAa;QAC5C,KAAK;YACH,OAAO,kIAAA,CAAA,wBAAqB,CAAC,SAAS;QACxC,KAAK;YACH,OAAO,kIAAA,CAAA,wBAAqB,CAAC,QAAQ;QACvC,KAAK;YACH,OAAO,kIAAA,CAAA,wBAAqB,CAAC,aAAa;QAC5C,KAAK;YACH,OAAO,kIAAA,CAAA,wBAAqB,CAAC,UAAU;QACzC,KAAK;QACL;YACE,OAAO,kIAAA,CAAA,wBAAqB,CAAC,MAAM;IACvC;AACF;AAGO,SAAS,0BAA0B,cAAqC;IAC7E,OAAQ;QACN,KAAK,kIAAA,CAAA,wBAAqB,CAAC,aAAa;YACtC,OAAO;QACT,KAAK,kIAAA,CAAA,wBAAqB,CAAC,SAAS;YAClC,OAAO;QACT,KAAK,kIAAA,CAAA,wBAAqB,CAAC,QAAQ;YACjC,OAAO;QACT,KAAK,kIAAA,CAAA,wBAAqB,CAAC,aAAa;YACtC,OAAO;QACT,KAAK,kIAAA,CAAA,wBAAqB,CAAC,UAAU;YACnC,OAAO;QACT,KAAK,kIAAA,CAAA,wBAAqB,CAAC,MAAM;QACjC;YACE,OAAO;IACX;AACF;AAGO,SAAS,0BACd,IAAY,EACZ,gBAAwB;IAExB,MAAM,cAAc,KAAK,IAAI;IAE7B,IAAI,CAAC,aAAa;QAChB,OAAO;YAAE,WAAW;YAAU,eAAe;QAAK;IACpD;IAEA,0BAA0B;IAC1B,IAAI,8CAA8C,IAAI,CAAC,cAAc;QACnE,OAAO;YACL,WAAW;YACX,eAAe,CAAA,GAAA,kIAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM,kIAAA,CAAA,wBAAqB,CAAC,aAAa;QAC/E;IACF;IAEA,uBAAuB;IACvB,IAAI,8DAA8D,IAAI,CAAC,cAAc;QACnF,OAAO;YACL,WAAW;YACX,eAAe,CAAA,GAAA,kIAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM,kIAAA,CAAA,wBAAqB,CAAC,UAAU;QAC5E;IACF;IAEA,0BAA0B;IAC1B,IAAI,WAAW,IAAI,CAAC,cAAc;QAChC,OAAO;YACL,WAAW;YACX,eAAe,CAAA,GAAA,kIAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM,kIAAA,CAAA,wBAAqB,CAAC,aAAa;QAC/E;IACF;IAEA,+DAA+D;IAC/D,IAAI,2BAA2B,IAAI,CAAC,gBAAgB,qBAAqB,YAAY;QACnF,OAAO;YACL,WAAW;YACX,eAAe,CAAA,GAAA,kIAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM,kIAAA,CAAA,wBAAqB,CAAC,SAAS;QAC3E;IACF;IAEA,2DAA2D;IAC3D,sDAAsD;IAEtD,oBAAoB;IACpB,OAAO;QACL,WAAW;QACX,eAAe,CAAA,GAAA,kIAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM,kIAAA,CAAA,wBAAqB,CAAC,MAAM;IACxE;AACF;AAGO,MAAM,6BAA6B;IACxC,cAAc;IACd,WAAW;IACX,UAAU;IACV,eAAe;IACf,YAAY;IACZ,QAAQ;AACV;AAGO,SAAS;IACd,MAAM,SAAS,OAAO,OAAO,CAAC,4BAA4B,GAAG,CAAC,CAAC,CAAC,WAAW,UAAU;QACnF,MAAM,cAAc,0BAA0B;QAC9C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,mBAAgB,AAAD,EAAE;QAEhC,OAAO,CAAC;OACL,EAAE,UAAU;qBACE,EAAE,OAAO,UAAU,CAAC;sBACnB,EAAE,OAAO,WAAW,CAAC;oBACvB,EAAE,OAAO,SAAS,CAAC;uBAChB,EAAE,OAAO,YAAY,CAAC;wBACrB,EAAE,OAAO,aAAa,CAAC;qBAC1B,EAAE,OAAO,UAAU,CAAC;oBACrB,EAAE,OAAO,SAAS,CAAC;qBAClB,EAAE,OAAO,UAAU,CAAC;oBACrB,EAAE,OAAO,SAAS,CAAC;;;;;;IAMnC,CAAC;IACH,GAAG,IAAI,CAAC;IAER,uCAAuC;IACvC,MAAM,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAoDvB,CAAC;IAED,OAAO,SAAS,OAAO;AACzB", "debugId": null}}, {"offset": {"line": 10019, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/hooks/useScreenplayFormatting.ts"], "sourcesContent": ["/**\n * Custom hook for screenplay formatting functionality\n * Handles auto-formatting, element detection, and keyboard shortcuts\n */\n\nimport { useCallback, useEffect, useState } from 'react';\nimport { \n  ScreenplayElementType, \n  detectElementType, \n  formatScreenplayText \n} from '../lib/screenplayFormatting';\nimport { \n  autoFormatScreenplayBlock, \n  screenplayTypeToBlockType \n} from '../lib/screenplaySchema';\n\ninterface UseScreenplayFormattingProps {\n  editor: any; // BlockNote editor instance\n  isReadOnly?: boolean;\n}\n\nexport function useScreenplayFormatting({ \n  editor, \n  isReadOnly = false \n}: UseScreenplayFormattingProps) {\n  const [currentElementType, setCurrentElementType] = useState<ScreenplayElementType>(\n    ScreenplayElementType.ACTION\n  );\n  const [isAutoFormatting, setIsAutoFormatting] = useState(true);\n\n  // Format current block as specific screenplay element\n  const formatAsElement = useCallback((elementType: ScreenplayElementType) => {\n    if (!editor || isReadOnly) return;\n\n    try {\n      // Check if editor has required methods\n      if (!editor.getTextCursorPosition || !editor.updateBlock) {\n        console.warn('Editor does not have required methods for screenplay formatting');\n        return;\n      }\n\n      const blockType = screenplayTypeToBlockType(elementType);\n\n      // Get current block\n      const currentBlock = editor.getTextCursorPosition().block;\n\n      if (!currentBlock) {\n        console.warn('No current block found');\n        return;\n      }\n\n      // Update block type\n      editor.updateBlock(currentBlock, {\n        type: blockType,\n      });\n\n      setCurrentElementType(elementType);\n    } catch (error) {\n      console.error('Error formatting screenplay element:', error);\n    }\n  }, [editor, isReadOnly]);\n\n  // Auto-format based on text content\n  const autoFormat = useCallback((text: string, currentBlockType: string) => {\n    if (!isAutoFormatting || !editor || isReadOnly) return;\n\n    const { blockType, formattedText } = autoFormatScreenplayBlock(text, currentBlockType);\n    \n    if (blockType !== currentBlockType) {\n      try {\n        const currentBlock = editor.getTextCursorPosition().block;\n        editor.updateBlock(currentBlock, {\n          type: blockType,\n        });\n        \n        // Update current element type\n        const elementType = detectElementType(text);\n        setCurrentElementType(elementType);\n      } catch (error) {\n        console.error('Error auto-formatting screenplay element:', error);\n      }\n    }\n  }, [editor, isAutoFormatting, isReadOnly]);\n\n  // Handle keyboard shortcuts\n  const handleKeyboardShortcuts = useCallback((event: KeyboardEvent) => {\n    if (!editor || isReadOnly) return;\n\n    // Check for Ctrl/Cmd + number keys\n    if ((event.ctrlKey || event.metaKey) && !event.shiftKey && !event.altKey) {\n      const key = event.key;\n      let elementType: ScreenplayElementType | null = null;\n\n      switch (key) {\n        case '1':\n          elementType = ScreenplayElementType.SCENE_HEADING;\n          break;\n        case '2':\n          elementType = ScreenplayElementType.ACTION;\n          break;\n        case '3':\n          elementType = ScreenplayElementType.CHARACTER;\n          break;\n        case '4':\n          elementType = ScreenplayElementType.DIALOGUE;\n          break;\n        case '5':\n          elementType = ScreenplayElementType.PARENTHETICAL;\n          break;\n        case '6':\n          elementType = ScreenplayElementType.TRANSITION;\n          break;\n      }\n\n      if (elementType) {\n        event.preventDefault();\n        formatAsElement(elementType);\n      }\n    }\n\n    // Handle Tab key for smart element progression\n    if (event.key === 'Tab' && !event.shiftKey) {\n      event.preventDefault();\n      handleTabProgression();\n    }\n\n    // Handle Enter key for smart line breaks\n    if (event.key === 'Enter' && !event.shiftKey) {\n      handleEnterProgression(event);\n    }\n  }, [editor, isReadOnly, formatAsElement]);\n\n  // Smart Tab progression between screenplay elements\n  const handleTabProgression = useCallback(() => {\n    if (!editor || isReadOnly) return;\n\n    const progressionMap: Record<ScreenplayElementType, ScreenplayElementType> = {\n      [ScreenplayElementType.SCENE_HEADING]: ScreenplayElementType.ACTION,\n      [ScreenplayElementType.ACTION]: ScreenplayElementType.CHARACTER,\n      [ScreenplayElementType.CHARACTER]: ScreenplayElementType.DIALOGUE,\n      [ScreenplayElementType.DIALOGUE]: ScreenplayElementType.ACTION,\n      [ScreenplayElementType.PARENTHETICAL]: ScreenplayElementType.DIALOGUE,\n      [ScreenplayElementType.TRANSITION]: ScreenplayElementType.SCENE_HEADING,\n      [ScreenplayElementType.SHOT]: ScreenplayElementType.ACTION,\n      [ScreenplayElementType.GENERAL]: ScreenplayElementType.ACTION,\n    };\n\n    const nextElementType = progressionMap[currentElementType] || ScreenplayElementType.ACTION;\n    formatAsElement(nextElementType);\n  }, [currentElementType, formatAsElement, editor, isReadOnly]);\n\n  // Smart Enter progression\n  const handleEnterProgression = useCallback((event: KeyboardEvent) => {\n    if (!editor || isReadOnly) return;\n\n    // Let default Enter behavior happen first\n    setTimeout(() => {\n      try {\n        const currentBlock = editor.getTextCursorPosition().block;\n        const blockType = currentBlock.type;\n        \n        // Determine next element type based on current type\n        let nextElementType: ScreenplayElementType;\n        \n        switch (blockType) {\n          case 'sceneHeading':\n            nextElementType = ScreenplayElementType.ACTION;\n            break;\n          case 'character':\n            nextElementType = ScreenplayElementType.DIALOGUE;\n            break;\n          case 'parenthetical':\n            nextElementType = ScreenplayElementType.DIALOGUE;\n            break;\n          case 'transition':\n            nextElementType = ScreenplayElementType.SCENE_HEADING;\n            break;\n          default:\n            nextElementType = ScreenplayElementType.ACTION;\n        }\n        \n        // Update the new block\n        const nextBlockType = screenplayTypeToBlockType(nextElementType);\n        editor.updateBlock(currentBlock, {\n          type: nextBlockType,\n        });\n        \n        setCurrentElementType(nextElementType);\n      } catch (error) {\n        console.error('Error handling Enter progression:', error);\n      }\n    }, 10);\n  }, [editor, isReadOnly]);\n\n  // Track current element type based on cursor position\n  const updateCurrentElementType = useCallback(() => {\n    if (!editor || !editor.getTextCursorPosition) return;\n\n    try {\n      const currentBlock = editor.getTextCursorPosition().block;\n      if (!currentBlock) return;\n\n      const blockType = currentBlock.type;\n\n      // Map block type to screenplay element type\n      let elementType: ScreenplayElementType;\n\n      switch (blockType) {\n        case 'sceneHeading':\n          elementType = ScreenplayElementType.SCENE_HEADING;\n          break;\n        case 'character':\n          elementType = ScreenplayElementType.CHARACTER;\n          break;\n        case 'dialogue':\n          elementType = ScreenplayElementType.DIALOGUE;\n          break;\n        case 'parenthetical':\n          elementType = ScreenplayElementType.PARENTHETICAL;\n          break;\n        case 'transition':\n          elementType = ScreenplayElementType.TRANSITION;\n          break;\n        default:\n          elementType = ScreenplayElementType.ACTION;\n      }\n\n      setCurrentElementType(elementType);\n    } catch (error) {\n      // Ignore errors when editor is not ready\n      console.debug('Editor not ready for element type detection:', error);\n    }\n  }, [editor]);\n\n  // Set up event listeners\n  useEffect(() => {\n    if (!editor) return;\n\n    // Add keyboard event listener\n    document.addEventListener('keydown', handleKeyboardShortcuts);\n\n    // Listen for cursor position changes\n    const handleSelectionChange = () => {\n      updateCurrentElementType();\n    };\n\n    // Listen for text changes for auto-formatting\n    const handleTextChange = () => {\n      if (isAutoFormatting && editor.getTextCursorPosition) {\n        try {\n          const currentBlock = editor.getTextCursorPosition().block;\n          if (currentBlock) {\n            const text = currentBlock.content?.[0]?.text || '';\n            autoFormat(text, currentBlock.type);\n          }\n        } catch (error) {\n          // Ignore errors when editor is not ready\n          console.debug('Editor not ready for auto-formatting:', error);\n        }\n      }\n    };\n\n    // Set up editor event listeners with error handling\n    try {\n      if (editor.prosemirrorView && editor.prosemirrorView.dom) {\n        const view = editor.prosemirrorView;\n        view.dom.addEventListener('selectionchange', handleSelectionChange);\n        view.dom.addEventListener('input', handleTextChange);\n      }\n    } catch (error) {\n      console.debug('Error setting up editor event listeners:', error);\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleKeyboardShortcuts);\n      try {\n        if (editor.prosemirrorView && editor.prosemirrorView.dom) {\n          const view = editor.prosemirrorView;\n          view.dom.removeEventListener('selectionchange', handleSelectionChange);\n          view.dom.removeEventListener('input', handleTextChange);\n        }\n      } catch (error) {\n        console.debug('Error cleaning up editor event listeners:', error);\n      }\n    };\n  }, [editor, handleKeyboardShortcuts, updateCurrentElementType, autoFormat, isAutoFormatting]);\n\n  // Initialize current element type\n  useEffect(() => {\n    updateCurrentElementType();\n  }, [updateCurrentElementType]);\n\n  return {\n    currentElementType,\n    formatAsElement,\n    isAutoFormatting,\n    setIsAutoFormatting,\n    handleTabProgression,\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;AAKA;;;;AAUO,SAAS,wBAAwB,EACtC,MAAM,EACN,aAAa,KAAK,EACW;IAC7B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACzD,kIAAA,CAAA,wBAAqB,CAAC,MAAM;IAE9B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,sDAAsD;IACtD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,IAAI,CAAC,UAAU,YAAY;QAE3B,IAAI;YACF,uCAAuC;YACvC,IAAI,CAAC,OAAO,qBAAqB,IAAI,CAAC,OAAO,WAAW,EAAE;gBACxD,QAAQ,IAAI,CAAC;gBACb;YACF;YAEA,MAAM,YAAY,CAAA,GAAA,8HAAA,CAAA,4BAAyB,AAAD,EAAE;YAE5C,oBAAoB;YACpB,MAAM,eAAe,OAAO,qBAAqB,GAAG,KAAK;YAEzD,IAAI,CAAC,cAAc;gBACjB,QAAQ,IAAI,CAAC;gBACb;YACF;YAEA,oBAAoB;YACpB,OAAO,WAAW,CAAC,cAAc;gBAC/B,MAAM;YACR;YAEA,sBAAsB;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD;IACF,GAAG;QAAC;QAAQ;KAAW;IAEvB,oCAAoC;IACpC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAc;QAC5C,IAAI,CAAC,oBAAoB,CAAC,UAAU,YAAY;QAEhD,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,4BAAyB,AAAD,EAAE,MAAM;QAErE,IAAI,cAAc,kBAAkB;YAClC,IAAI;gBACF,MAAM,eAAe,OAAO,qBAAqB,GAAG,KAAK;gBACzD,OAAO,WAAW,CAAC,cAAc;oBAC/B,MAAM;gBACR;gBAEA,8BAA8B;gBAC9B,MAAM,cAAc,CAAA,GAAA,kIAAA,CAAA,oBAAiB,AAAD,EAAE;gBACtC,sBAAsB;YACxB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6CAA6C;YAC7D;QACF;IACF,GAAG;QAAC;QAAQ;QAAkB;KAAW;IAEzC,4BAA4B;IAC5B,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3C,IAAI,CAAC,UAAU,YAAY;QAE3B,mCAAmC;QACnC,IAAI,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,KAAK,CAAC,MAAM,QAAQ,IAAI,CAAC,MAAM,MAAM,EAAE;YACxE,MAAM,MAAM,MAAM,GAAG;YACrB,IAAI,cAA4C;YAEhD,OAAQ;gBACN,KAAK;oBACH,cAAc,kIAAA,CAAA,wBAAqB,CAAC,aAAa;oBACjD;gBACF,KAAK;oBACH,cAAc,kIAAA,CAAA,wBAAqB,CAAC,MAAM;oBAC1C;gBACF,KAAK;oBACH,cAAc,kIAAA,CAAA,wBAAqB,CAAC,SAAS;oBAC7C;gBACF,KAAK;oBACH,cAAc,kIAAA,CAAA,wBAAqB,CAAC,QAAQ;oBAC5C;gBACF,KAAK;oBACH,cAAc,kIAAA,CAAA,wBAAqB,CAAC,aAAa;oBACjD;gBACF,KAAK;oBACH,cAAc,kIAAA,CAAA,wBAAqB,CAAC,UAAU;oBAC9C;YACJ;YAEA,IAAI,aAAa;gBACf,MAAM,cAAc;gBACpB,gBAAgB;YAClB;QACF;QAEA,+CAA+C;QAC/C,IAAI,MAAM,GAAG,KAAK,SAAS,CAAC,MAAM,QAAQ,EAAE;YAC1C,MAAM,cAAc;YACpB;QACF;QAEA,yCAAyC;QACzC,IAAI,MAAM,GAAG,KAAK,WAAW,CAAC,MAAM,QAAQ,EAAE;YAC5C,uBAAuB;QACzB;IACF,GAAG;QAAC;QAAQ;QAAY;KAAgB;IAExC,oDAAoD;IACpD,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,IAAI,CAAC,UAAU,YAAY;QAE3B,MAAM,iBAAuE;YAC3E,CAAC,kIAAA,CAAA,wBAAqB,CAAC,aAAa,CAAC,EAAE,kIAAA,CAAA,wBAAqB,CAAC,MAAM;YACnE,CAAC,kIAAA,CAAA,wBAAqB,CAAC,MAAM,CAAC,EAAE,kIAAA,CAAA,wBAAqB,CAAC,SAAS;YAC/D,CAAC,kIAAA,CAAA,wBAAqB,CAAC,SAAS,CAAC,EAAE,kIAAA,CAAA,wBAAqB,CAAC,QAAQ;YACjE,CAAC,kIAAA,CAAA,wBAAqB,CAAC,QAAQ,CAAC,EAAE,kIAAA,CAAA,wBAAqB,CAAC,MAAM;YAC9D,CAAC,kIAAA,CAAA,wBAAqB,CAAC,aAAa,CAAC,EAAE,kIAAA,CAAA,wBAAqB,CAAC,QAAQ;YACrE,CAAC,kIAAA,CAAA,wBAAqB,CAAC,UAAU,CAAC,EAAE,kIAAA,CAAA,wBAAqB,CAAC,aAAa;YACvE,CAAC,kIAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,EAAE,kIAAA,CAAA,wBAAqB,CAAC,MAAM;YAC1D,CAAC,kIAAA,CAAA,wBAAqB,CAAC,OAAO,CAAC,EAAE,kIAAA,CAAA,wBAAqB,CAAC,MAAM;QAC/D;QAEA,MAAM,kBAAkB,cAAc,CAAC,mBAAmB,IAAI,kIAAA,CAAA,wBAAqB,CAAC,MAAM;QAC1F,gBAAgB;IAClB,GAAG;QAAC;QAAoB;QAAiB;QAAQ;KAAW;IAE5D,0BAA0B;IAC1B,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1C,IAAI,CAAC,UAAU,YAAY;QAE3B,0CAA0C;QAC1C,WAAW;YACT,IAAI;gBACF,MAAM,eAAe,OAAO,qBAAqB,GAAG,KAAK;gBACzD,MAAM,YAAY,aAAa,IAAI;gBAEnC,oDAAoD;gBACpD,IAAI;gBAEJ,OAAQ;oBACN,KAAK;wBACH,kBAAkB,kIAAA,CAAA,wBAAqB,CAAC,MAAM;wBAC9C;oBACF,KAAK;wBACH,kBAAkB,kIAAA,CAAA,wBAAqB,CAAC,QAAQ;wBAChD;oBACF,KAAK;wBACH,kBAAkB,kIAAA,CAAA,wBAAqB,CAAC,QAAQ;wBAChD;oBACF,KAAK;wBACH,kBAAkB,kIAAA,CAAA,wBAAqB,CAAC,aAAa;wBACrD;oBACF;wBACE,kBAAkB,kIAAA,CAAA,wBAAqB,CAAC,MAAM;gBAClD;gBAEA,uBAAuB;gBACvB,MAAM,gBAAgB,CAAA,GAAA,8HAAA,CAAA,4BAAyB,AAAD,EAAE;gBAChD,OAAO,WAAW,CAAC,cAAc;oBAC/B,MAAM;gBACR;gBAEA,sBAAsB;YACxB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;YACrD;QACF,GAAG;IACL,GAAG;QAAC;QAAQ;KAAW;IAEvB,sDAAsD;IACtD,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3C,IAAI,CAAC,UAAU,CAAC,OAAO,qBAAqB,EAAE;QAE9C,IAAI;YACF,MAAM,eAAe,OAAO,qBAAqB,GAAG,KAAK;YACzD,IAAI,CAAC,cAAc;YAEnB,MAAM,YAAY,aAAa,IAAI;YAEnC,4CAA4C;YAC5C,IAAI;YAEJ,OAAQ;gBACN,KAAK;oBACH,cAAc,kIAAA,CAAA,wBAAqB,CAAC,aAAa;oBACjD;gBACF,KAAK;oBACH,cAAc,kIAAA,CAAA,wBAAqB,CAAC,SAAS;oBAC7C;gBACF,KAAK;oBACH,cAAc,kIAAA,CAAA,wBAAqB,CAAC,QAAQ;oBAC5C;gBACF,KAAK;oBACH,cAAc,kIAAA,CAAA,wBAAqB,CAAC,aAAa;oBACjD;gBACF,KAAK;oBACH,cAAc,kIAAA,CAAA,wBAAqB,CAAC,UAAU;oBAC9C;gBACF;oBACE,cAAc,kIAAA,CAAA,wBAAqB,CAAC,MAAM;YAC9C;YAEA,sBAAsB;QACxB,EAAE,OAAO,OAAO;YACd,yCAAyC;YACzC,QAAQ,KAAK,CAAC,gDAAgD;QAChE;IACF,GAAG;QAAC;KAAO;IAEX,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ;QAEb,8BAA8B;QAC9B,SAAS,gBAAgB,CAAC,WAAW;QAErC,qCAAqC;QACrC,MAAM,wBAAwB;YAC5B;QACF;QAEA,8CAA8C;QAC9C,MAAM,mBAAmB;YACvB,IAAI,oBAAoB,OAAO,qBAAqB,EAAE;gBACpD,IAAI;oBACF,MAAM,eAAe,OAAO,qBAAqB,GAAG,KAAK;oBACzD,IAAI,cAAc;wBAChB,MAAM,OAAO,aAAa,OAAO,EAAE,CAAC,EAAE,EAAE,QAAQ;wBAChD,WAAW,MAAM,aAAa,IAAI;oBACpC;gBACF,EAAE,OAAO,OAAO;oBACd,yCAAyC;oBACzC,QAAQ,KAAK,CAAC,yCAAyC;gBACzD;YACF;QACF;QAEA,oDAAoD;QACpD,IAAI;YACF,IAAI,OAAO,eAAe,IAAI,OAAO,eAAe,CAAC,GAAG,EAAE;gBACxD,MAAM,OAAO,OAAO,eAAe;gBACnC,KAAK,GAAG,CAAC,gBAAgB,CAAC,mBAAmB;gBAC7C,KAAK,GAAG,CAAC,gBAAgB,CAAC,SAAS;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;QAC5D;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,IAAI;gBACF,IAAI,OAAO,eAAe,IAAI,OAAO,eAAe,CAAC,GAAG,EAAE;oBACxD,MAAM,OAAO,OAAO,eAAe;oBACnC,KAAK,GAAG,CAAC,mBAAmB,CAAC,mBAAmB;oBAChD,KAAK,GAAG,CAAC,mBAAmB,CAAC,SAAS;gBACxC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6CAA6C;YAC7D;QACF;IACF,GAAG;QAAC;QAAQ;QAAyB;QAA0B;QAAY;KAAiB;IAE5F,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAyB;IAE7B,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 10300, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ScreenplayEditor.tsx"], "sourcesContent": ["import \"../lib/crypto-polyfill\";\nimport { useQuery } from \"convex/react\";\nimport { useBlockNoteSync } from \"@convex-dev/prosemirror-sync/blocknote\";\nimport { BlockNoteView } from \"@blocknote/mantine\";\nimport \"@blocknote/core/fonts/inter.css\";\nimport \"@blocknote/mantine/style.css\";\nimport {\n  SideMenuController,\n  SideMenu,\n  AddBlockButton,\n  DragHandleButton,\n} from \"@blocknote/react\";\nimport { api } from \"../../convex/_generated/api\";\nimport { Id } from \"../../convex/_generated/dataModel\";\nimport { useState, useEffect, useCallback } from \"react\";\nimport { useCollaborativeCursors } from \"../hooks/useCollaborativeCursors\";\nimport { useComments } from \"../hooks/useComments\";\nimport { CommentSidebar } from \"./CommentSidebar\";\nimport { CommentPopover } from \"./CommentPopover\";\nimport { DocumentHeader } from \"./DocumentHeader\";\nimport { ScreenplayToolbar } from \"./ScreenplayToolbar\";\nimport { PaginatedEditor } from \"./PaginatedEditor\";\nimport { useScreenplayFormatting } from \"../hooks/useScreenplayFormatting\";\nimport { generateScreenplayCSS } from \"../lib/screenplaySchema\";\n// import { useScreenplayAutoFormat } from \"../hooks/useScreenplayAutoFormat\";\nimport { AlertCircle } from \"lucide-react\";\n\ninterface ScreenplayEditorProps {\n  documentId: Id<\"documents\">;\n}\n\nexport function ScreenplayEditor({ documentId }: ScreenplayEditorProps) {\n  const documentData = useQuery(api.documents.getDocument, { id: documentId });\n  const permission = useQuery(api.sharing.getDocumentPermission, { documentId });\n  const currentUser = useQuery(api.auth.loggedInUser);\n\n  // Check if user has write permission for the editor\n  const isReadOnly = permission?.permission === \"read\";\n\n  // Use the sync hook to get the editor\n  const sync = useBlockNoteSync(api.prosemirror, documentId);\n\n  // Create document if it doesn't exist and user has permission\n  useEffect(() => {\n    if (!sync.isLoading && sync.editor === null && permission &&\n        (permission.permission === \"owner\" || permission.permission === \"write\")) {\n      sync.create({ type: \"doc\", content: [] });\n    }\n  }, [sync.isLoading, sync.editor, sync.create, permission]);\n\n  // Show error after a delay if editor still hasn't initialized\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      if (!sync.isLoading && !sync.editor) {\n        setShowEditorError(true);\n      }\n    }, 3000); // Wait 3 seconds before showing error\n\n    return () => clearTimeout(timer);\n  }, [sync.isLoading, sync.editor]);\n\n  // Debug logging\n  useEffect(() => {\n    console.log('ScreenplayEditor Debug:', {\n      documentId,\n      documentData,\n      permission,\n      currentUser,\n      syncEditor: !!sync.editor,\n      syncLoading: sync.isLoading\n    });\n  }, [documentId, documentData, permission, currentUser, sync.editor, sync.isLoading]);\n\n  // Comment system state\n  const [showCommentSidebar, setShowCommentSidebar] = useState(false);\n  const [commentPopover, setCommentPopover] = useState<{\n    isVisible: boolean;\n    position: { x: number; y: number };\n    selectedText: string;\n  }>({\n    isVisible: false,\n    position: { x: 0, y: 0 },\n    selectedText: \"\",\n  });\n\n  // Page state\n  const [, setCurrentPage] = useState(1);\n  const [, setTotalPages] = useState(1);\n\n  // Editor initialization state\n  const [showEditorError, setShowEditorError] = useState(false);\n\n  // Collaborative cursors\n  const { cursors } = useCollaborativeCursors({\n    documentId,\n    editor: sync.editor,\n    isReadOnly,\n  });\n\n  // Comments\n  const {\n    comments,\n    createComment,\n    updateComment,\n    deleteComment,\n    createReply,\n    updateReply,\n    deleteReply,\n    resolveComment,\n  } = useComments({\n    documentId,\n    editor: sync.editor,\n    isReadOnly,\n  });\n\n  // Screenplay formatting - only initialize when editor is ready\n  const {\n    currentElementType,\n  } = useScreenplayFormatting({\n    editor: sync.editor,\n    isReadOnly,\n  });\n\n  // Auto-formatting for screenplay elements (temporarily disabled)\n  // const { formatAsScreenplayElement } = useScreenplayAutoFormat({\n  //   editor: sync.editor,\n  //   isReadOnly,\n  // });\n\n  // Temporary simple formatting function\n  const formatAsScreenplayElement = useCallback((elementType: any) => {\n    console.log('Format as screenplay element:', elementType);\n    // TODO: Implement formatting logic\n  }, []);\n\n\n\n  // Handle page changes\n  const handlePageChange = useCallback((page: number, total: number) => {\n    setCurrentPage(page);\n    setTotalPages(total);\n  }, []);\n\n  // Handle text selection for comments\n  const handleTextSelection = useCallback((selectedText: string, position: { x: number; y: number }) => {\n    if (selectedText.trim() && !isReadOnly) {\n      setCommentPopover({\n        isVisible: true,\n        position,\n        selectedText: selectedText.trim(),\n      });\n    } else {\n      setCommentPopover(prev => ({ ...prev, isVisible: false }));\n    }\n  }, [isReadOnly]);\n\n  // Handle comment creation\n  const handleCreateComment = useCallback(async (content: string) => {\n    if (!sync.editor || !currentUser) return;\n\n    try {\n      await createComment(content);\n\n      setCommentPopover(prev => ({ ...prev, isVisible: false }));\n    } catch (error) {\n      console.error(\"Failed to create comment:\", error);\n    }\n  }, [sync.editor, currentUser, createComment, commentPopover.selectedText]);\n\n  // Setup collaborative cursors plugin\n  useEffect(() => {\n    if (sync.editor && cursors.length > 0) {\n      // Note: Plugin integration would need to be added to BlockNote configuration\n      // For now, cursors are handled by the useCollaborativeCursors hook\n    }\n  }, [sync.editor, cursors, currentUser]);\n\n  // Setup comment plugin\n  useEffect(() => {\n    if (sync.editor && comments.length > 0) {\n      // Note: Plugin integration would need to be added to BlockNote configuration\n      // For now, comments are handled by the useComments hook\n    }\n  }, [sync.editor, comments, currentUser, handleTextSelection]);\n\n  // Inject screenplay CSS\n  useEffect(() => {\n    const styleId = 'screenplay-styles';\n    let styleElement = document.getElementById(styleId);\n\n    if (!styleElement) {\n      styleElement = document.createElement('style');\n      styleElement.id = styleId;\n      document.head.appendChild(styleElement);\n    }\n\n    styleElement.textContent = generateScreenplayCSS();\n\n    return () => {\n      const element = document.getElementById(styleId);\n      if (element) {\n        element.remove();\n      }\n    };\n  }, []);\n\n  // Loading state\n  if (!documentData || !permission || sync.isLoading) {\n    return (\n      <div className=\"flex-1 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  // Document initialization state for users with write permission\n  if (!sync.editor && permission && (permission.permission === \"owner\" || permission.permission === \"write\")) {\n    return (\n      <div className=\"flex-1 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n        <span className=\"ml-2 text-gray-600\">Initializing screenplay editor...</span>\n      </div>\n    );\n  }\n\n  // For read-only users, show error if document doesn't exist\n  if (!sync.editor && permission && permission.permission === \"read\") {\n    return (\n      <div className=\"flex-1 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <AlertCircle className=\"h-12 w-12 text-yellow-500 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Document Not Available</h3>\n          <p className=\"text-gray-600\">This screenplay document has not been created yet.</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Error state - document not found\n  if (!documentData) {\n    return (\n      <div className=\"flex-1 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <AlertCircle className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Document Not Found</h3>\n          <p className=\"text-gray-600\">The screenplay document could not be loaded.</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Error state - wrong document type\n  if (documentData.documentType !== \"screenplay\") {\n    return (\n      <div className=\"flex-1 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <AlertCircle className=\"h-12 w-12 text-yellow-500 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Wrong Document Type</h3>\n          <p className=\"text-gray-600\">\n            This document is not a screenplay. Document type: {documentData.documentType}\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  // Error state - editor failed to initialize (only show after delay)\n  if (showEditorError && !sync.editor) {\n    return (\n      <div className=\"flex-1 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <AlertCircle className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Editor Failed to Load</h3>\n          <p className=\"text-gray-600\">The screenplay editor could not be initialized.</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n          >\n            Reload Page\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Still loading or waiting for editor to initialize\n  if (!sync.editor) {\n    return (\n      <div className=\"flex-1 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex-1 flex flex-col h-full relative\" data-testid=\"screenplay-editor\">\n      <DocumentHeader\n        documentId={documentId}\n      />\n\n      <div className=\"flex-1 flex relative\">\n        {/* Main Editor Area */}\n        <div className=\"flex-1 flex flex-col\">\n          {/* Screenplay Toolbar */}\n          <ScreenplayToolbar\n            onFormatElement={formatAsScreenplayElement}\n            onToggleComments={() => setShowCommentSidebar(!showCommentSidebar)}\n            commentsCount={comments.length}\n            showComments={showCommentSidebar}\n            currentElementType={currentElementType}\n            disabled={isReadOnly}\n          />\n\n          {/* Paginated Editor */}\n          <PaginatedEditor\n            onPageChange={handlePageChange}\n            className=\"screenplay-editor\"\n          >\n            <BlockNoteView\n              editor={sync.editor}\n              theme=\"light\"\n              className=\"screenplay-content\"\n              editable={!isReadOnly}\n              formattingToolbar={false}\n              sideMenu={false}\n            >\n              {!isReadOnly && sync.editor && (\n                <SideMenuController\n                  key=\"screenplay-side-menu\"\n                  sideMenu={(props) => (\n                    <SideMenu {...props}>\n                      <AddBlockButton {...props} />\n                      <DragHandleButton {...props} />\n                    </SideMenu>\n                  )}\n                />\n              )}\n            </BlockNoteView>\n          </PaginatedEditor>\n        </div>\n\n        {/* Comment Sidebar */}\n        {showCommentSidebar && (\n          <CommentSidebar\n            comments={comments}\n            currentUserId={currentUser?._id}\n            selectedCommentId={undefined}\n            isVisible={showCommentSidebar}\n            canResolve={!isReadOnly}\n            onClose={() => setShowCommentSidebar(false)}\n            onCreateComment={createComment}\n            onUpdateComment={updateComment}\n            onDeleteComment={deleteComment}\n            onCreateReply={createReply}\n            onUpdateReply={updateReply}\n            onDeleteReply={deleteReply}\n            onResolveComment={resolveComment}\n            onSelectComment={() => {}}\n          />\n        )}\n\n        {/* Comment Popover */}\n        {commentPopover.isVisible && (\n          <CommentPopover\n            isVisible={commentPopover.isVisible}\n            position={commentPopover.position}\n            selectedText={commentPopover.selectedText}\n            onCreateComment={handleCreateComment}\n            onClose={() => setCommentPopover(prev => ({ ...prev, isVisible: false }))}\n          />\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;AAGA;AAMA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8EAA8E;AAC9E;;;;;;;;;;;;;;;;;;;;;AAMO,SAAS,iBAAiB,EAAE,UAAU,EAAyB;IACpE,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,SAAS,CAAC,WAAW,EAAE;QAAE,IAAI;IAAW;IAC1E,MAAM,aAAa,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,qBAAqB,EAAE;QAAE;IAAW;IAC5E,MAAM,cAAc,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,IAAI,CAAC,YAAY;IAElD,oDAAoD;IACpD,MAAM,aAAa,YAAY,eAAe;IAE9C,sCAAsC;IACtC,MAAM,OAAO,CAAA,GAAA,2LAAA,CAAA,mBAAgB,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,WAAW,EAAE;IAE/C,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,KAAK,SAAS,IAAI,KAAK,MAAM,KAAK,QAAQ,cAC3C,CAAC,WAAW,UAAU,KAAK,WAAW,WAAW,UAAU,KAAK,OAAO,GAAG;YAC5E,KAAK,MAAM,CAAC;gBAAE,MAAM;gBAAO,SAAS,EAAE;YAAC;QACzC;IACF,GAAG;QAAC,KAAK,SAAS;QAAE,KAAK,MAAM;QAAE,KAAK,MAAM;QAAE;KAAW;IAEzD,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,MAAM,EAAE;gBACnC,mBAAmB;YACrB;QACF,GAAG,OAAO,sCAAsC;QAEhD,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC,KAAK,SAAS;QAAE,KAAK,MAAM;KAAC;IAEhC,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,2BAA2B;YACrC;YACA;YACA;YACA;YACA,YAAY,CAAC,CAAC,KAAK,MAAM;YACzB,aAAa,KAAK,SAAS;QAC7B;IACF,GAAG;QAAC;QAAY;QAAc;QAAY;QAAa,KAAK,MAAM;QAAE,KAAK,SAAS;KAAC;IAEnF,uBAAuB;IACvB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAIhD;QACD,WAAW;QACX,UAAU;YAAE,GAAG;YAAG,GAAG;QAAE;QACvB,cAAc;IAChB;IAEA,aAAa;IACb,MAAM,GAAG,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACpC,MAAM,GAAG,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,8BAA8B;IAC9B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,wBAAwB;IACxB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,0BAAuB,AAAD,EAAE;QAC1C;QACA,QAAQ,KAAK,MAAM;QACnB;IACF;IAEA,WAAW;IACX,MAAM,EACJ,QAAQ,EACR,aAAa,EACb,aAAa,EACb,aAAa,EACb,WAAW,EACX,WAAW,EACX,WAAW,EACX,cAAc,EACf,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE;QACd;QACA,QAAQ,KAAK,MAAM;QACnB;IACF;IAEA,+DAA+D;IAC/D,MAAM,EACJ,kBAAkB,EACnB,GAAG,CAAA,GAAA,uIAAA,CAAA,0BAAuB,AAAD,EAAE;QAC1B,QAAQ,KAAK,MAAM;QACnB;IACF;IAEA,iEAAiE;IACjE,kEAAkE;IAClE,yBAAyB;IACzB,gBAAgB;IAChB,MAAM;IAEN,uCAAuC;IACvC,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7C,QAAQ,GAAG,CAAC,iCAAiC;IAC7C,mCAAmC;IACrC,GAAG,EAAE;IAIL,sBAAsB;IACtB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAc;QAClD,eAAe;QACf,cAAc;IAChB,GAAG,EAAE;IAEL,qCAAqC;IACrC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,cAAsB;QAC7D,IAAI,aAAa,IAAI,MAAM,CAAC,YAAY;YACtC,kBAAkB;gBAChB,WAAW;gBACX;gBACA,cAAc,aAAa,IAAI;YACjC;QACF,OAAO;YACL,kBAAkB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;gBAAM,CAAC;QAC1D;IACF,GAAG;QAAC;KAAW;IAEf,0BAA0B;IAC1B,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC7C,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,aAAa;QAElC,IAAI;YACF,MAAM,cAAc;YAEpB,kBAAkB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;gBAAM,CAAC;QAC1D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF,GAAG;QAAC,KAAK,MAAM;QAAE;QAAa;QAAe,eAAe,YAAY;KAAC;IAEzE,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,KAAK,MAAM,IAAI,QAAQ,MAAM,GAAG,GAAG;QACrC,6EAA6E;QAC7E,mEAAmE;QACrE;IACF,GAAG;QAAC,KAAK,MAAM;QAAE;QAAS;KAAY;IAEtC,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,KAAK,MAAM,IAAI,SAAS,MAAM,GAAG,GAAG;QACtC,6EAA6E;QAC7E,wDAAwD;QAC1D;IACF,GAAG;QAAC,KAAK,MAAM;QAAE;QAAU;QAAa;KAAoB;IAE5D,wBAAwB;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU;QAChB,IAAI,eAAe,SAAS,cAAc,CAAC;QAE3C,IAAI,CAAC,cAAc;YACjB,eAAe,SAAS,aAAa,CAAC;YACtC,aAAa,EAAE,GAAG;YAClB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;QAEA,aAAa,WAAW,GAAG,CAAA,GAAA,8HAAA,CAAA,wBAAqB,AAAD;QAE/C,OAAO;YACL,MAAM,UAAU,SAAS,cAAc,CAAC;YACxC,IAAI,SAAS;gBACX,QAAQ,MAAM;YAChB;QACF;IACF,GAAG,EAAE;IAEL,gBAAgB;IAChB,IAAI,CAAC,gBAAgB,CAAC,cAAc,KAAK,SAAS,EAAE;QAClD,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,gEAAgE;IAChE,IAAI,CAAC,KAAK,MAAM,IAAI,cAAc,CAAC,WAAW,UAAU,KAAK,WAAW,WAAW,UAAU,KAAK,OAAO,GAAG;QAC1G,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAqB;;;;;;;;;;;;IAG3C;IAEA,4DAA4D;IAC5D,IAAI,CAAC,KAAK,MAAM,IAAI,cAAc,WAAW,UAAU,KAAK,QAAQ;QAClE,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,mCAAmC;IACnC,IAAI,CAAC,cAAc;QACjB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,oCAAoC;IACpC,IAAI,aAAa,YAAY,KAAK,cAAc;QAC9C,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAE,WAAU;;4BAAgB;4BACwB,aAAa,YAAY;;;;;;;;;;;;;;;;;;IAKtF;IAEA,oEAAoE;IACpE,IAAI,mBAAmB,CAAC,KAAK,MAAM,EAAE;QACnC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,8OAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,oDAAoD;IACpD,IAAI,CAAC,KAAK,MAAM,EAAE;QAChB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAuC,eAAY;;0BAChE,8OAAC,oIAAA,CAAA,iBAAc;gBACb,YAAY;;;;;;0BAGd,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,uIAAA,CAAA,oBAAiB;gCAChB,iBAAiB;gCACjB,kBAAkB,IAAM,sBAAsB,CAAC;gCAC/C,eAAe,SAAS,MAAM;gCAC9B,cAAc;gCACd,oBAAoB;gCACpB,UAAU;;;;;;0CAIZ,8OAAC,qIAAA,CAAA,kBAAe;gCACd,cAAc;gCACd,WAAU;0CAEV,cAAA,8OAAC,sKAAA,CAAA,gBAAa;oCACZ,QAAQ,KAAK,MAAM;oCACnB,OAAM;oCACN,WAAU;oCACV,UAAU,CAAC;oCACX,mBAAmB;oCACnB,UAAU;8CAET,CAAC,cAAc,KAAK,MAAM,kBACzB,8OAAC,kKAAA,CAAA,qBAAkB;wCAEjB,UAAU,CAAC,sBACT,8OAAC,kKAAA,CAAA,WAAQ;gDAAE,GAAG,KAAK;;kEACjB,8OAAC,kKAAA,CAAA,iBAAc;wDAAE,GAAG,KAAK;;;;;;kEACzB,8OAAC,kKAAA,CAAA,mBAAgB;wDAAE,GAAG,KAAK;;;;;;;;;;;;uCAJ3B;;;;;;;;;;;;;;;;;;;;;oBAcb,oCACC,8OAAC,oIAAA,CAAA,iBAAc;wBACb,UAAU;wBACV,eAAe,aAAa;wBAC5B,mBAAmB;wBACnB,WAAW;wBACX,YAAY,CAAC;wBACb,SAAS,IAAM,sBAAsB;wBACrC,iBAAiB;wBACjB,iBAAiB;wBACjB,iBAAiB;wBACjB,eAAe;wBACf,eAAe;wBACf,eAAe;wBACf,kBAAkB;wBAClB,iBAAiB,KAAO;;;;;;oBAK3B,eAAe,SAAS,kBACvB,8OAAC,oIAAA,CAAA,iBAAc;wBACb,WAAW,eAAe,SAAS;wBACnC,UAAU,eAAe,QAAQ;wBACjC,cAAc,eAAe,YAAY;wBACzC,iBAAiB;wBACjB,SAAS,IAAM,kBAAkB,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,WAAW;gCAAM,CAAC;;;;;;;;;;;;;;;;;;AAMnF", "debugId": null}}, {"offset": {"line": 10899, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/CollectionEditor.tsx"], "sourcesContent": ["import { useQuery } from 'convex/react'\nimport { useBlockNoteSync } from \"@convex-dev/prosemirror-sync/blocknote\";\nimport { BlockNoteView } from \"@blocknote/mantine\";\nimport \"@blocknote/core/fonts/inter.css\";\nimport \"@blocknote/mantine/style.css\";\nimport {\n  BasicTextStyleButton,\n  BlockTypeSelect,\n  ColorStyleButton,\n  CreateLinkButton,\n  FileCaptionButton,\n  FileReplaceButton,\n  FormattingToolbar,\n  FormattingToolbarController,\n  NestBlockButton,\n  TextAlignButton,\n  UnnestBlockButton,\n  SideMenuController,\n  SideMenu,\n  AddBlockButton,\n  DragHandleButton,\n} from \"@blocknote/react\";\nimport { api } from \"../../convex/_generated/api\";\nimport { Id } from \"../../convex/_generated/dataModel\";\nimport { useState, useEffect, useCallback } from \"react\";\nimport { useCollaborativeCursors } from \"../hooks/useCollaborativeCursors\";\nimport { createCollaborativeCursorsPlugin } from \"../lib/collaborativeCursors\";\nimport { useComments } from \"../hooks/useComments\";\nimport { createCommentPlugin } from \"../lib/commentPlugin\";\nimport { CommentSidebar } from \"./CommentSidebar\";\nimport { CommentPopover } from \"./CommentPopover\";\nimport {\n  Bold,\n  Italic,\n  Underline,\n  List,\n  ListOrdered,\n  Heading1,\n  Heading2,\n  Heading3,\n  Link,\n  Undo,\n  Redo,\n  Lock,\n  MessageSquare,\n  MessageSquarePlus,\n  FolderOpen,\n  Tag,\n  Archive\n} from \"lucide-react\";\nimport { DocumentHeader } from \"./DocumentHeader\";\nimport { Button } from \"./ui/button\";\nimport { ScrollArea } from \"./ui/scroll-area\";\n\ninterface CollectionEditorProps {\n  documentId: Id<\"documents\">;\n}\n\nexport function CollectionEditor({ documentId }: CollectionEditorProps) {\n  const sync = useBlockNoteSync(api.prosemirror, documentId);\n  const permission = useQuery(api.sharing.getDocumentPermission, { documentId });\n  const canCreate = useQuery(api.documents.canCreateDocuments);\n  const currentUser = useQuery(api.auth.loggedInUser);\n\n  // Check if user has write permission for the editor\n  const isReadOnly = permission?.permission === \"read\";\n\n  // Comment system state\n  const [showCommentSidebar, setShowCommentSidebar] = useState(false);\n  const [commentPopover, setCommentPopover] = useState<{\n    isVisible: boolean;\n    position: { x: number; y: number };\n    selectedText: string;\n  }>({\n    isVisible: false,\n    position: { x: 0, y: 0 },\n    selectedText: \"\",\n  });\n\n  // Collaborative cursors\n  const { cursors } = useCollaborativeCursors({\n    documentId,\n    editor: sync.editor,\n    isReadOnly,\n  });\n\n  // Comments\n  const {\n    comments,\n    createComment,\n    updateComment,\n    deleteComment,\n    createReply,\n    updateReply,\n    deleteReply,\n    resolveComment,\n  } = useComments({\n    documentId,\n    editor: sync.editor,\n    isReadOnly,\n  });\n\n  // Handle text selection for comments\n  const handleTextSelection = useCallback((selectedText: string, position: { x: number; y: number }) => {\n    if (selectedText.trim() && !isReadOnly) {\n      setCommentPopover({\n        isVisible: true,\n        position,\n        selectedText: selectedText.trim(),\n      });\n    } else {\n      setCommentPopover(prev => ({ ...prev, isVisible: false }));\n    }\n  }, [isReadOnly]);\n\n  // Handle comment creation\n  const handleCreateComment = useCallback(async (content: string) => {\n    if (!sync.editor || !currentUser) return;\n\n    const view = sync.editor._tiptapEditor.view;\n    const { from, to } = view.state.selection;\n    const selectedText = view.state.doc.textBetween(from, to);\n\n    try {\n      await createComment({\n        content,\n        position: from,\n        selection: { from, to },\n        selectedText,\n      });\n\n      setCommentPopover(prev => ({ ...prev, isVisible: false }));\n    } catch (error) {\n      console.error(\"Failed to create comment:\", error);\n    }\n  }, [sync.editor, currentUser, createComment]);\n\n  // Setup collaborative cursors plugin\n  useEffect(() => {\n    if (sync.editor && cursors.length > 0) {\n      const plugin = createCollaborativeCursorsPlugin(cursors, currentUser?._id);\n      // Note: Plugin integration would need to be added to BlockNote configuration\n    }\n  }, [sync.editor, cursors, currentUser]);\n\n  // Setup comment plugin\n  useEffect(() => {\n    if (sync.editor && comments.length > 0) {\n      const plugin = createCommentPlugin(\n        comments,\n        currentUser?._id,\n        handleTextSelection,\n        () => setShowCommentSidebar(true)\n      );\n      // Note: Plugin integration would need to be added to BlockNote configuration\n    }\n  }, [sync.editor, comments, currentUser, handleTextSelection]);\n\n  if (!sync.editor) {\n    return (\n      <div className=\"flex-1 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex-1 flex flex-col h-full relative\">\n      <DocumentHeader \n        documentId={documentId}\n        permission={permission}\n        canCreate={canCreate}\n      />\n\n      <div className=\"flex-1 flex relative\">\n        {/* Main Editor Area */}\n        <div className=\"flex-1 flex flex-col\">\n          {/* Collection-specific Toolbar */}\n          <div className=\"border-b bg-white px-6 py-3\">\n            <div className=\"flex items-center gap-2 text-sm\">\n              <span className=\"font-medium text-gray-700\">Collection Tools:</span>\n              <div className=\"flex items-center gap-4\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"gap-2\">\n                  <Tag className=\"h-4 w-4\" />\n                  Add Tag\n                </Button>\n                <Button variant=\"ghost\" size=\"sm\" className=\"gap-2\">\n                  <Archive className=\"h-4 w-4\" />\n                  Archive\n                </Button>\n                <Button variant=\"ghost\" size=\"sm\" className=\"gap-2\">\n                  <FolderOpen className=\"h-4 w-4\" />\n                  Organize\n                </Button>\n              </div>\n              \n              {/* Comment Toggle */}\n              <div className=\"ml-auto\">\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => setShowCommentSidebar(!showCommentSidebar)}\n                  className=\"gap-2\"\n                >\n                  <MessageSquare className=\"h-4 w-4\" />\n                  Comments ({comments.length})\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          {/* Editor */}\n          <ScrollArea className=\"h-[calc(100vh-300px)] min-h-[500px] editor-scroll-area\">\n            <div className=\"p-6\">\n              <BlockNoteView\n                editor={sync.editor}\n                theme=\"light\"\n                className=\"collection-editor prose prose-sm max-w-none\"\n                editable={!isReadOnly}\n                formattingToolbar={false}\n                sideMenu={isReadOnly}\n              >\n                {!isReadOnly && sync.editor && (\n                  <>\n                    <FormattingToolbarController\n                      key=\"collection-formatting-toolbar\"\n                      formattingToolbar={() => (\n                        <FormattingToolbar>\n                          <BlockTypeSelect key={\"blockTypeSelect\"} />\n\n                          <FileCaptionButton key={\"fileCaptionButton\"} />\n                          <FileReplaceButton key={\"replaceFileButton\"} />\n\n                          <BasicTextStyleButton\n                            key={\"boldStyleButton\"}\n                            basicTextStyle={\"bold\"}\n                          >\n                            <Bold className=\"h-4 w-4\" />\n                          </BasicTextStyleButton>\n                          <BasicTextStyleButton\n                            key={\"italicStyleButton\"}\n                            basicTextStyle={\"italic\"}\n                          >\n                            <Italic className=\"h-4 w-4\" />\n                          </BasicTextStyleButton>\n                          <BasicTextStyleButton\n                            key={\"underlineStyleButton\"}\n                            basicTextStyle={\"underline\"}\n                          >\n                            <Underline className=\"h-4 w-4\" />\n                          </BasicTextStyleButton>\n\n                          <TextAlignButton\n                            key={\"textAlignButton\"}\n                            textAlignment={\"left\"}\n                          />\n\n                          <ColorStyleButton key={\"colorStyleButton\"} />\n\n                          <NestBlockButton key={\"nestBlockButton\"}>\n                            <List className=\"h-4 w-4\" />\n                          </NestBlockButton>\n                          <UnnestBlockButton key={\"unnestBlockButton\"}>\n                            <ListOrdered className=\"h-4 w-4\" />\n                          </UnnestBlockButton>\n\n                          <CreateLinkButton key={\"createLinkButton\"}>\n                            <Link className=\"h-4 w-4\" />\n                          </CreateLinkButton>\n\n                          {/* Comment button for selected text */}\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={() => {\n                              const view = sync.editor._tiptapEditor.view;\n                              const { from, to } = view.state.selection;\n                              if (from !== to) {\n                                const selectedText = view.state.doc.textBetween(from, to);\n                                const coords = view.coordsAtPos(from);\n                                handleTextSelection(selectedText, { x: coords.left, y: coords.top });\n                              }\n                            }}\n                            className=\"gap-2\"\n                          >\n                            <MessageSquarePlus className=\"h-4 w-4\" />\n                          </Button>\n                        </FormattingToolbar>\n                      )}\n                    />\n\n                    <SideMenuController\n                      key=\"collection-side-menu\"\n                      sideMenu={(props) => (\n                        <SideMenu {...props}>\n                          <AddBlockButton {...props} />\n                          <DragHandleButton {...props} />\n                        </SideMenu>\n                      )}\n                    />\n                  </>\n                )}\n              </BlockNoteView>\n            </div>\n          </ScrollArea>\n        </div>\n\n        {/* Comment Sidebar */}\n        {showCommentSidebar && (\n          <CommentSidebar\n            documentId={documentId}\n            comments={comments}\n            currentUser={currentUser}\n            onClose={() => setShowCommentSidebar(false)}\n            onCreateComment={createComment}\n            onUpdateComment={updateComment}\n            onDeleteComment={deleteComment}\n            onCreateReply={createReply}\n            onUpdateReply={updateReply}\n            onDeleteReply={deleteReply}\n            onResolveComment={resolveComment}\n          />\n        )}\n\n        {/* Comment Popover */}\n        {commentPopover.isVisible && (\n          <CommentPopover\n            position={commentPopover.position}\n            selectedText={commentPopover.selectedText}\n            onCreateComment={handleCreateComment}\n            onClose={() => setCommentPopover(prev => ({ ...prev, isVisible: false }))}\n          />\n        )}\n      </div>\n\n      <style jsx>{`\n        .collection-editor {\n          /* Collection-specific styling */\n        }\n        \n        .collection-editor .note-item {\n          border: 1px solid #e5e7eb;\n          border-radius: 0.5rem;\n          padding: 1rem;\n          margin: 0.5rem 0;\n          background: #f9fafb;\n        }\n        \n        .collection-editor .tag {\n          display: inline-block;\n          background: #dbeafe;\n          color: #1e40af;\n          padding: 0.25rem 0.5rem;\n          border-radius: 0.25rem;\n          font-size: 0.75rem;\n          margin: 0.125rem;\n        }\n      `}</style>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;AACA;AAGA;AAiBA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;AAMO,SAAS,iBAAiB,EAAE,UAAU,EAAyB;IACpE,MAAM,OAAO,CAAA,GAAA,2LAAA,CAAA,mBAAgB,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,WAAW,EAAE;IAC/C,MAAM,aAAa,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,qBAAqB,EAAE;QAAE;IAAW;IAC5E,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,SAAS,CAAC,kBAAkB;IAC3D,MAAM,cAAc,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,IAAI,CAAC,YAAY;IAElD,oDAAoD;IACpD,MAAM,aAAa,YAAY,eAAe;IAE9C,uBAAuB;IACvB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAIhD;QACD,WAAW;QACX,UAAU;YAAE,GAAG;YAAG,GAAG;QAAE;QACvB,cAAc;IAChB;IAEA,wBAAwB;IACxB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,0BAAuB,AAAD,EAAE;QAC1C;QACA,QAAQ,KAAK,MAAM;QACnB;IACF;IAEA,WAAW;IACX,MAAM,EACJ,QAAQ,EACR,aAAa,EACb,aAAa,EACb,aAAa,EACb,WAAW,EACX,WAAW,EACX,WAAW,EACX,cAAc,EACf,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE;QACd;QACA,QAAQ,KAAK,MAAM;QACnB;IACF;IAEA,qCAAqC;IACrC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,cAAsB;QAC7D,IAAI,aAAa,IAAI,MAAM,CAAC,YAAY;YACtC,kBAAkB;gBAChB,WAAW;gBACX;gBACA,cAAc,aAAa,IAAI;YACjC;QACF,OAAO;YACL,kBAAkB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;gBAAM,CAAC;QAC1D;IACF,GAAG;QAAC;KAAW;IAEf,0BAA0B;IAC1B,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC7C,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,aAAa;QAElC,MAAM,OAAO,KAAK,MAAM,CAAC,aAAa,CAAC,IAAI;QAC3C,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,KAAK,KAAK,CAAC,SAAS;QACzC,MAAM,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM;QAEtD,IAAI;YACF,MAAM,cAAc;gBAClB;gBACA,UAAU;gBACV,WAAW;oBAAE;oBAAM;gBAAG;gBACtB;YACF;YAEA,kBAAkB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;gBAAM,CAAC;QAC1D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF,GAAG;QAAC,KAAK,MAAM;QAAE;QAAa;KAAc;IAE5C,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,KAAK,MAAM,IAAI,QAAQ,MAAM,GAAG,GAAG;YACrC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,mCAAgC,AAAD,EAAE,SAAS,aAAa;QACtE,6EAA6E;QAC/E;IACF,GAAG;QAAC,KAAK,MAAM;QAAE;QAAS;KAAY;IAEtC,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,KAAK,MAAM,IAAI,SAAS,MAAM,GAAG,GAAG;YACtC,MAAM,SAAS,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD,EAC/B,UACA,aAAa,KACb,qBACA,IAAM,sBAAsB;QAE9B,6EAA6E;QAC/E;IACF,GAAG;QAAC,KAAK,MAAM;QAAE;QAAU;QAAa;KAAoB;IAE5D,IAAI,CAAC,KAAK,MAAM,EAAE;QAChB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;kDAAc;;0BACb,8OAAC,oIAAA,CAAA,iBAAc;gBACb,YAAY;gBACZ,YAAY;gBACZ,WAAW;;;;;;0BAGb,8OAAC;0DAAc;;kCAEb,8OAAC;kEAAc;;0CAEb,8OAAC;0EAAc;0CACb,cAAA,8OAAC;8EAAc;;sDACb,8OAAC;sFAAe;sDAA4B;;;;;;sDAC5C,8OAAC;sFAAc;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,WAAU;;sEAC1C,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAY;;;;;;;8DAG7B,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,WAAU;;sEAC1C,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAAY;;;;;;;8DAGjC,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,WAAU;;sEAC1C,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;sDAMtC,8OAAC;sFAAc;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,sBAAsB,CAAC;gDACtC,WAAU;;kEAEV,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDAAY;oDAC1B,SAAS,MAAM;oDAAC;;;;;;;;;;;;;;;;;;;;;;;0CAOnC,8OAAC,0IAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC;8EAAc;8CACb,cAAA,8OAAC,sKAAA,CAAA,gBAAa;wCACZ,QAAQ,KAAK,MAAM;wCACnB,OAAM;wCACN,WAAU;wCACV,UAAU,CAAC;wCACX,mBAAmB;wCACnB,UAAU;kDAET,CAAC,cAAc,KAAK,MAAM,kBACzB;;8DACE,8OAAC,kKAAA,CAAA,8BAA2B;oDAE1B,mBAAmB,kBACjB,8OAAC,kKAAA,CAAA,oBAAiB;;8EAChB,8OAAC,kKAAA,CAAA,kBAAe,MAAM;;;;;8EAEtB,8OAAC,kKAAA,CAAA,oBAAiB,MAAM;;;;;8EACxB,8OAAC,kKAAA,CAAA,oBAAiB,MAAM;;;;;8EAExB,8OAAC,kKAAA,CAAA,uBAAoB;oEAEnB,gBAAgB;8EAEhB,cAAA,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;mEAHX;;;;;8EAKP,8OAAC,kKAAA,CAAA,uBAAoB;oEAEnB,gBAAgB;8EAEhB,cAAA,8OAAC,sMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;mEAHb;;;;;8EAKP,8OAAC,kKAAA,CAAA,uBAAoB;oEAEnB,gBAAgB;8EAEhB,cAAA,8OAAC,4MAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;mEAHhB;;;;;8EAMP,8OAAC,kKAAA,CAAA,kBAAe;oEAEd,eAAe;mEADV;;;;;8EAIP,8OAAC,kKAAA,CAAA,mBAAgB,MAAM;;;;;8EAEvB,8OAAC,kKAAA,CAAA,kBAAe;8EACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;mEADI;;;;;8EAGtB,8OAAC,kKAAA,CAAA,oBAAiB;8EAChB,cAAA,8OAAC,oNAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;mEADD;;;;;8EAIxB,8OAAC,kKAAA,CAAA,mBAAgB;8EACf,cAAA,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;mEADK;;;;;8EAKvB,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS;wEACP,MAAM,OAAO,KAAK,MAAM,CAAC,aAAa,CAAC,IAAI;wEAC3C,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,KAAK,KAAK,CAAC,SAAS;wEACzC,IAAI,SAAS,IAAI;4EACf,MAAM,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM;4EACtD,MAAM,SAAS,KAAK,WAAW,CAAC;4EAChC,oBAAoB,cAAc;gFAAE,GAAG,OAAO,IAAI;gFAAE,GAAG,OAAO,GAAG;4EAAC;wEACpE;oEACF;oEACA,WAAU;8EAEV,cAAA,8OAAC,oOAAA,CAAA,oBAAiB;wEAAC,WAAU;;;;;;;;;;;;;;;;;mDA5D/B;;;;;8DAkEN,8OAAC,kKAAA,CAAA,qBAAkB;oDAEjB,UAAU,CAAC,sBACT,8OAAC,kKAAA,CAAA,WAAQ;4DAAE,GAAG,KAAK;;8EACjB,8OAAC,kKAAA,CAAA,iBAAc;oEAAE,GAAG,KAAK;;;;;;8EACzB,8OAAC,kKAAA,CAAA,mBAAgB;oEAAE,GAAG,KAAK;;;;;;;;;;;;mDAJ3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAgBjB,oCACC,8OAAC,oIAAA,CAAA,iBAAc;wBACb,YAAY;wBACZ,UAAU;wBACV,aAAa;wBACb,SAAS,IAAM,sBAAsB;wBACrC,iBAAiB;wBACjB,iBAAiB;wBACjB,iBAAiB;wBACjB,eAAe;wBACf,eAAe;wBACf,eAAe;wBACf,kBAAkB;;;;;;oBAKrB,eAAe,SAAS,kBACvB,8OAAC,oIAAA,CAAA,iBAAc;wBACb,UAAU,eAAe,QAAQ;wBACjC,cAAc,eAAe,YAAY;wBACzC,iBAAiB;wBACjB,SAAS,IAAM,kBAAkB,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,WAAW;gCAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;AA8BnF", "debugId": null}}, {"offset": {"line": 11471, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/DocumentEditor.tsx"], "sourcesContent": ["import { useQuery } from 'convex/react';\nimport { api } from \"../../convex/_generated/api\";\nimport { Id } from \"../../convex/_generated/dataModel\";\nimport { CollaborativeEditor } from \"./CollaborativeEditor\";\nimport { ScreenplayEditor } from \"./ScreenplayEditor\";\nimport { CollectionEditor } from \"./CollectionEditor\";\n\ninterface DocumentEditorProps {\n  documentId: Id<\"documents\">;\n}\n\nexport function DocumentEditor({ documentId }: DocumentEditorProps) {\n  const document = useQuery(api.documents.getDocument, { id: documentId });\n  const permission = useQuery(api.sharing.getDocumentPermission, { documentId });\n\n  if (!document || !permission) {\n    return (\n      <div className=\"flex-1 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  // Route to appropriate editor based on document type\n  switch (document.documentType) {\n    case \"screenplay\":\n      return <ScreenplayEditor documentId={documentId} />;\n    case \"collection\":\n      return <CollectionEditor documentId={documentId} />;\n    case \"research\":\n    default:\n      return <CollaborativeEditor documentId={documentId} />;\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAEA;AACA;AACA;;;;;;;AAMO,SAAS,eAAe,EAAE,UAAU,EAAuB;IAChE,MAAM,WAAW,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,SAAS,CAAC,WAAW,EAAE;QAAE,IAAI;IAAW;IACtE,MAAM,aAAa,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,qBAAqB,EAAE;QAAE;IAAW;IAE5E,IAAI,CAAC,YAAY,CAAC,YAAY;QAC5B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qDAAqD;IACrD,OAAQ,SAAS,YAAY;QAC3B,KAAK;YACH,qBAAO,8OAAC,sIAAA,CAAA,mBAAgB;gBAAC,YAAY;;;;;;QACvC,KAAK;YACH,qBAAO,8OAAC,sIAAA,CAAA,mBAAgB;gBAAC,YAAY;;;;;;QACvC,KAAK;QACL;YACE,qBAAO,8OAAC,yIAAA,CAAA,sBAAmB;gBAAC,YAAY;;;;;;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 11545, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/PresenceIndicator.tsx"], "sourcesContent": ["'use client';\n\nimport usePresence from \"@convex-dev/presence/react\";\nimport { api } from \"../../convex/_generated/api\";\nimport { useQuery } from \"convex/react\";\nimport { getUserColor } from \"../lib/userColors\";\n\ninterface PresenceIndicatorProps {\n  roomId: string;\n}\n\nexport function PresenceIndicator({ roomId }: PresenceIndicatorProps) {\n  const userId = useQuery(api.presence.getUserId);\n  const presenceState = usePresence(api.presence, roomId, userId || \"\");\n\n  if (!userId || !presenceState || presenceState.length === 0) {\n    return null;\n  }\n\n  return (\n    <div className=\"flex items-center gap-2 p-3 bg-white border-b border-gray-200\">\n      <span className=\"text-sm text-gray-600 font-medium\">\n        {presenceState.length === 1 ? \"1 person\" : `${presenceState.length} people`} editing\n      </span>\n      <div className=\"flex -space-x-2\">\n        {presenceState.slice(0, 8).map((user, index) => {\n          const userColor = getUserColor(user.userId);\n          return (\n            <div\n              key={`${user.userId}-${index}`}\n              className={`w-8 h-8 rounded-full border-2 border-white flex items-center justify-center ${userColor.text} text-xs font-semibold ${userColor.bg} shadow-sm`}\n              title={user.name || \"Anonymous\"}\n            >\n              {user.image ? (\n                <img\n                  src={user.image}\n                  alt={user.name || \"User\"}\n                  className=\"w-full h-full rounded-full object-cover\"\n                />\n              ) : (\n                <span>\n                  {(user.name || \"A\").charAt(0).toUpperCase()}\n                </span>\n              )}\n            </div>\n          );\n        })}\n        {presenceState.length > 8 && (\n          <div className=\"w-8 h-8 rounded-full border-2 border-white bg-gray-500 flex items-center justify-center text-white text-xs font-semibold shadow-sm\">\n            +{presenceState.length - 8}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AALA;;;;;;AAWO,SAAS,kBAAkB,EAAE,MAAM,EAA0B;IAClE,MAAM,SAAS,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,SAAS;IAC9C,MAAM,gBAAgB,CAAA,GAAA,4KAAA,CAAA,UAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,QAAQ,EAAE,QAAQ,UAAU;IAElE,IAAI,CAAC,UAAU,CAAC,iBAAiB,cAAc,MAAM,KAAK,GAAG;QAC3D,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAK,WAAU;;oBACb,cAAc,MAAM,KAAK,IAAI,aAAa,GAAG,cAAc,MAAM,CAAC,OAAO,CAAC;oBAAC;;;;;;;0BAE9E,8OAAC;gBAAI,WAAU;;oBACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM;wBACpC,MAAM,YAAY,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,MAAM;wBAC1C,qBACE,8OAAC;4BAEC,WAAW,CAAC,4EAA4E,EAAE,UAAU,IAAI,CAAC,uBAAuB,EAAE,UAAU,EAAE,CAAC,UAAU,CAAC;4BAC1J,OAAO,KAAK,IAAI,IAAI;sCAEnB,KAAK,KAAK,iBACT,8OAAC;gCACC,KAAK,KAAK,KAAK;gCACf,KAAK,KAAK,IAAI,IAAI;gCAClB,WAAU;;;;;qDAGZ,8OAAC;0CACE,CAAC,KAAK,IAAI,IAAI,GAAG,EAAE,MAAM,CAAC,GAAG,WAAW;;;;;;2BAZxC,GAAG,KAAK,MAAM,CAAC,CAAC,EAAE,OAAO;;;;;oBAiBpC;oBACC,cAAc,MAAM,GAAG,mBACtB,8OAAC;wBAAI,WAAU;;4BAAqI;4BAChJ,cAAc,MAAM,GAAG;;;;;;;;;;;;;;;;;;;AAMrC", "debugId": null}}, {"offset": {"line": 11639, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/script/%5BscriptId%5D/document/%5BdocumentId%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport \"../../../../lib/crypto-polyfill\";\nimport { useQuery } from \"convex/react\";\nimport { api } from \"../../../../../convex/_generated/api\";\nimport { AppLayout } from \"../../../../components/layout/AppLayout\";\nimport { ScriptList } from \"../../../../components/ScriptList\";\nimport { ScriptDocumentList } from \"../../../../components/ScriptDocumentList\";\nimport { DocumentEditor } from \"../../../../components/DocumentEditor\";\nimport { PresenceIndicator } from \"../../../../components/PresenceIndicator\";\nimport { useRouter, useParams } from \"next/navigation\";\nimport { useState } from \"react\";\nimport { Id } from \"../../../../../convex/_generated/dataModel\";\n\nexport default function ScriptDocumentPage() {\n  const router = useRouter();\n  const params = useParams();\n  const scriptId = params.scriptId as Id<\"scripts\">;\n  const documentId = params.documentId as Id<\"documents\">;\n  \n  const [documentCount, setDocumentCount] = useState<number>(0);\n  const [scriptCount, setScriptCount] = useState<number>(0);\n  const loggedInUser = useQuery(api.auth.loggedInUser);\n\n  const navigateToScript = (newScriptId: Id<\"scripts\">) => {\n    router.push(`/script/${newScriptId}`);\n  };\n\n  const navigateToScriptDocument = (scriptId: Id<\"scripts\">, documentId: Id<\"documents\">) => {\n    router.push(`/script/${scriptId}/document/${documentId}`);\n  };\n\n  if (loggedInUser === undefined) {\n    return (\n      <AppLayout>\n        <div className=\"flex-1 flex justify-center items-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n        </div>\n      </AppLayout>\n    );\n  }\n\n  return (\n    <AppLayout showSidebar={false}>\n      <div className=\"flex-1 flex h-full overflow-hidden\">\n        {/* Left Sidebar - Scripts */}\n        <ScriptList\n          onSelectScript={navigateToScript}\n          selectedScriptId={scriptId}\n          onScriptCountChange={setScriptCount}\n        />\n        \n        {/* Middle Panel - Documents */}\n        <ScriptDocumentList\n          scriptId={scriptId}\n          onSelectDocument={navigateToScriptDocument}\n          selectedDocumentId={documentId}\n          onDocumentCountChange={setDocumentCount}\n        />\n\n        {/* Main Content Area */}\n        <div className=\"flex-1 flex flex-col bg-gray-50\">\n          <PresenceIndicator roomId={documentId} />\n          <DocumentEditor documentId={documentId} />\n        </div>\n      </div>\n    </AppLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAce,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,OAAO,QAAQ;IAChC,MAAM,aAAa,OAAO,UAAU;IAEpC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,IAAI,CAAC,YAAY;IAEnD,MAAM,mBAAmB,CAAC;QACxB,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,aAAa;IACtC;IAEA,MAAM,2BAA2B,CAAC,UAAyB;QACzD,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,SAAS,UAAU,EAAE,YAAY;IAC1D;IAEA,IAAI,iBAAiB,WAAW;QAC9B,qBACE,8OAAC,yIAAA,CAAA,YAAS;sBACR,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC,yIAAA,CAAA,YAAS;QAAC,aAAa;kBACtB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,gIAAA,CAAA,aAAU;oBACT,gBAAgB;oBAChB,kBAAkB;oBAClB,qBAAqB;;;;;;8BAIvB,8OAAC,wIAAA,CAAA,qBAAkB;oBACjB,UAAU;oBACV,kBAAkB;oBAClB,oBAAoB;oBACpB,uBAAuB;;;;;;8BAIzB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,uIAAA,CAAA,oBAAiB;4BAAC,QAAQ;;;;;;sCAC3B,8OAAC,oIAAA,CAAA,iBAAc;4BAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;;AAKtC", "debugId": null}}]}