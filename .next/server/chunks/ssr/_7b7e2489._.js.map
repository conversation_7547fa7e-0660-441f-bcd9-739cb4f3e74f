{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/convex/_generated/api.js"], "sourcesContent": ["/* eslint-disable */\n/**\n * Generated `api` utility.\n *\n * THIS CODE IS AUTOMATICALLY GENERATED.\n *\n * To regenerate, run `npx convex dev`.\n * @module\n */\n\nimport { anyApi, componentsGeneric } from \"convex/server\";\n\n/**\n * A utility for referencing Convex functions in your app's API.\n *\n * Usage:\n * ```js\n * const myFunctionReference = api.myModule.myFunction;\n * ```\n */\nexport const api = anyApi;\nexport const internal = anyApi;\nexport const components = componentsGeneric();\n"], "names": [], "mappings": "AAAA,kBAAkB,GAClB;;;;;;;CAOC;;;;;AAED;AAAA;AAAA;;AAUO,MAAM,MAAM,sJAAA,CAAA,SAAM;AAClB,MAAM,WAAW,sJAAA,CAAA,SAAM;AACvB,MAAM,aAAa,CAAA,GAAA,sLAAA,CAAA,oBAAiB,AAAD", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport { \n  Home, \n  FileText, \n  Clock, \n  Users, \n  Settings, \n  ChevronLeft,\n  ChevronRight,\n  Film\n} from 'lucide-react';\nimport { Button } from '../ui/button';\nimport { cn } from '@/lib/utils';\n\ninterface SidebarProps {\n  className?: string;\n}\n\nconst navigationItems = [\n  {\n    title: 'Dashboard',\n    href: '/',\n    icon: Home,\n  },\n  {\n    title: 'Scripts',\n    href: '/scripts',\n    icon: Film,\n  },\n  {\n    title: 'Recent',\n    href: '/recent',\n    icon: Clock,\n  },\n  {\n    title: 'Shared with me',\n    href: '/shared',\n    icon: Users,\n  },\n  {\n    title: 'Settings',\n    href: '/settings',\n    icon: Settings,\n  },\n];\n\nexport function Sidebar({ className }: SidebarProps) {\n  const [isCollapsed, setIsCollapsed] = useState(false);\n  const pathname = usePathname();\n\n  return (\n    <div\n      className={cn(\n        'relative flex flex-col bg-white border-r border-gray-200 transition-all duration-300',\n        isCollapsed ? 'w-16' : 'w-64',\n        className\n      )}\n    >\n      {/* Header */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n        {!isCollapsed && (\n          <div className=\"flex items-center gap-2\">\n            <Film className=\"text-blue-600\" size={24} />\n            <h2 className=\"text-xl font-semibold text-gray-900\">Scripty</h2>\n          </div>\n        )}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => setIsCollapsed(!isCollapsed)}\n          className=\"h-8 w-8 p-0\"\n        >\n          {isCollapsed ? (\n            <ChevronRight className=\"h-4 w-4\" />\n          ) : (\n            <ChevronLeft className=\"h-4 w-4\" />\n          )}\n        </Button>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 p-4\">\n        <ul className=\"space-y-2\">\n          {navigationItems.map((item) => {\n            const isActive = pathname === item.href || \n              (item.href !== '/' && pathname.startsWith(item.href));\n            \n            return (\n              <li key={item.href}>\n                <Link\n                  href={item.href}\n                  className={cn(\n                    'flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors',\n                    isActive\n                      ? 'bg-blue-50 text-blue-700 border border-blue-200'\n                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900',\n                    isCollapsed && 'justify-center px-2'\n                  )}\n                >\n                  <item.icon className=\"h-5 w-5 flex-shrink-0\" />\n                  {!isCollapsed && <span>{item.title}</span>}\n                </Link>\n              </li>\n            );\n          })}\n        </ul>\n      </nav>\n\n      {/* User section at bottom */}\n      <div className=\"p-4 border-t border-gray-200\">\n        {!isCollapsed ? (\n          <div className=\"flex items-center gap-3 px-3 py-2\">\n            <div className=\"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center\">\n              <span className=\"text-sm font-medium text-blue-700\">U</span>\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-sm font-medium text-gray-900 truncate\">User</p>\n              <p className=\"text-xs text-gray-500 truncate\"><EMAIL></p>\n            </div>\n          </div>\n        ) : (\n          <div className=\"flex justify-center\">\n            <div className=\"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center\">\n              <span className=\"text-sm font-medium text-blue-700\">U</span>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAhBA;;;;;;;;AAsBA,MAAM,kBAAkB;IACtB;QACE,OAAO;QACP,MAAM;QACN,MAAM,mMAAA,CAAA,OAAI;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,kMAAA,CAAA,OAAI;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;IACb;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;IACb;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;IAChB;CACD;AAEM,SAAS,QAAQ,EAAE,SAAS,EAAgB;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wFACA,cAAc,SAAS,QACvB;;0BAIF,8OAAC;gBAAI,WAAU;;oBACZ,CAAC,6BACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;gCAAgB,MAAM;;;;;;0CACtC,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAGxD,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,eAAe,CAAC;wBAC/B,WAAU;kCAET,4BACC,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;iDAExB,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAM7B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BACX,gBAAgB,GAAG,CAAC,CAAC;wBACpB,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;wBAErD,qBACE,8OAAC;sCACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sFACA,WACI,oDACA,uDACJ,eAAe;;kDAGjB,8OAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;oCACpB,CAAC,6BAAe,8OAAC;kDAAM,KAAK,KAAK;;;;;;;;;;;;2BAZ7B,KAAK,IAAI;;;;;oBAgBtB;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;0BACZ,CAAC,4BACA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAAoC;;;;;;;;;;;sCAEtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAC1D,8OAAC;oCAAE,WAAU;8CAAiC;;;;;;;;;;;;;;;;;yCAIlD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlE", "debugId": null}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/auth/SignOutButton.tsx"], "sourcesContent": ["\"use client\";\nimport { useAuthActions } from \"@convex-dev/auth/react\";\nimport { useConvexAuth } from \"convex/react\";\nimport { Button } from '../ui/button';\n\nexport function SignOutButton() {\n  const { isAuthenticated } = useConvexAuth();\n  const { signOut } = useAuthActions();\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <Button\n      variant={\"default\"}\n      size={\"sm\"}\n      onClick={() => void signOut()}\n    >\n      Sign out\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AAHA;;;;;AAKO,SAAS;IACd,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD;IACxC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD;IAEjC,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAS;QACT,MAAM;QACN,SAAS,IAAM,KAAK;kBACrB;;;;;;AAIL", "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { Film } from 'lucide-react';\nimport { SignOutButton } from '../auth/SignOutButton';\n\ninterface HeaderProps {\n  title?: string;\n  showSidebar?: boolean;\n}\n\nexport function Header({ title = 'Scripty', showSidebar = true }: HeaderProps) {\n  return (\n    <header className=\"sticky top-0 z-10 py-3 bg-white/80 backdrop-blur-sm h-16 flex justify-between items-center border-b shadow-sm px-4\">\n      <div className=\"flex items-center gap-2\">\n        {!showSidebar && (\n          <>\n            <Film className=\"text-blue-600\" size={24} />\n            <h2 className=\"text-xl font-semibold text-gray-900\">{title}</h2>\n          </>\n        )}\n      </div>\n      <SignOutButton />\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUO,SAAS,OAAO,EAAE,QAAQ,SAAS,EAAE,cAAc,IAAI,EAAe;IAC3E,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;0BACZ,CAAC,6BACA;;sCACE,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;4BAAgB,MAAM;;;;;;sCACtC,8OAAC;4BAAG,WAAU;sCAAuC;;;;;;;;;;;;;0BAI3D,8OAAC,2IAAA,CAAA,gBAAa;;;;;;;;;;;AAGpB", "debugId": null}}, {"offset": {"line": 454, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/auth/SignInForm.tsx"], "sourcesContent": ["\"use client\";\nimport { useAuthActions } from \"@convex-dev/auth/react\";\nimport { useState } from \"react\";\nimport { toast } from \"sonner\";\n\nexport function SignInForm() {\n  const { signIn } = useAuthActions();\n  const [flow, setFlow] = useState<\"signIn\" | \"signUp\">(\"signIn\");\n  const [submitting, setSubmitting] = useState(false);\n\n  return (\n    <div className=\"w-full\">\n      <form\n        className=\"flex flex-col gap-2\"\n        onSubmit={(e) => {\n          e.preventDefault();\n          setSubmitting(true);\n          const formData = new FormData(e.target as HTMLFormElement);\n          formData.set(\"flow\", flow);\n          void signIn(\"password\", formData).catch((error) => {\n            let toastTitle = \"\";\n            if (error.message.includes(\"Invalid password\")) {\n              toastTitle = \"Invalid password. Please try again.\";\n            } else {\n              toastTitle =\n                flow === \"signIn\"\n                  ? \"Could not sign in, did you mean to sign up?\"\n                  : \"Could not sign up, did you mean to sign in?\";\n            }\n            toast.error(toastTitle);\n            setSubmitting(false);\n          });\n        }}\n      >\n        <input\n          className=\"auth-input-field\"\n          type=\"email\"\n          name=\"email\"\n          placeholder=\"Email\"\n          required\n        />\n        <input\n          className=\"auth-input-field\"\n          type=\"password\"\n          name=\"password\"\n          placeholder=\"Password\"\n          required\n        />\n        <button className=\"auth-button\" type=\"submit\" disabled={submitting}>\n          {flow === \"signIn\" ? \"Sign in\" : \"Sign up\"}\n        </button>\n        <div className=\"text-center text-sm text-secondary\">\n          <span className=\"text-slate-400\">\n            {flow === \"signIn\"\n              ? \"Don't have an account? \"\n              : \"Already have an account? \"}\n          </span>\n          <button\n            type=\"button\"\n            className=\"text-primary text-right hover:text-primary-hover hover:underline font-medium cursor-pointer\"\n            onClick={() => setFlow(flow === \"signIn\" ? \"signUp\" : \"signIn\")}\n          >\n            {flow === \"signIn\" ? \"Sign up instead\" : \"Sign in instead\"}\n          </button>\n        </div>\n      </form>\n      <div className=\"flex items-center justify-center my-3\">\n        <hr className=\"my-4 grow border-gray-200\" />\n        <span className=\"mx-4 text-secondary\">or</span>\n        <hr className=\"my-4 grow border-gray-200\" />\n      </div>\n      <button className=\"auth-button\" onClick={() => void signIn(\"anonymous\")}>\n        Sign in anonymously\n      </button>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAKO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD;IAChC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAU;gBACV,UAAU,CAAC;oBACT,EAAE,cAAc;oBAChB,cAAc;oBACd,MAAM,WAAW,IAAI,SAAS,EAAE,MAAM;oBACtC,SAAS,GAAG,CAAC,QAAQ;oBACrB,KAAK,OAAO,YAAY,UAAU,KAAK,CAAC,CAAC;wBACvC,IAAI,aAAa;wBACjB,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,qBAAqB;4BAC9C,aAAa;wBACf,OAAO;4BACL,aACE,SAAS,WACL,gDACA;wBACR;wBACA,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACZ,cAAc;oBAChB;gBACF;;kCAEA,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,MAAK;wBACL,aAAY;wBACZ,QAAQ;;;;;;kCAEV,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,MAAK;wBACL,aAAY;wBACZ,QAAQ;;;;;;kCAEV,8OAAC;wBAAO,WAAU;wBAAc,MAAK;wBAAS,UAAU;kCACrD,SAAS,WAAW,YAAY;;;;;;kCAEnC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CACb,SAAS,WACN,4BACA;;;;;;0CAEN,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,QAAQ,SAAS,WAAW,WAAW;0CAErD,SAAS,WAAW,oBAAoB;;;;;;;;;;;;;;;;;;0BAI/C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;;;;;kCACd,8OAAC;wBAAK,WAAU;kCAAsB;;;;;;kCACtC,8OAAC;wBAAG,WAAU;;;;;;;;;;;;0BAEhB,8OAAC;gBAAO,WAAU;gBAAc,SAAS,IAAM,KAAK,OAAO;0BAAc;;;;;;;;;;;;AAK/E", "debugId": null}}, {"offset": {"line": 610, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/layout/AppLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport { Authenticated, Unauthenticated } from 'convex/react';\nimport { Sidebar } from './Sidebar';\nimport { Header } from './Header';\nimport { SignInForm } from '../auth/SignInForm';\nimport { FileText } from 'lucide-react';\n\ninterface AppLayoutProps {\n  children: ReactNode;\n  showSidebar?: boolean;\n}\n\nexport function AppLayout({ children, showSidebar = true }: AppLayoutProps) {\n  return (\n    <div className=\"h-screen flex flex-col bg-gray-50 overflow-hidden\" data-testid=\"app-loaded\">\n      <Unauthenticated>\n        <div className=\"flex-1 flex items-center justify-center p-8\">\n          <div className=\"w-full max-w-md mx-auto text-center\">\n            <div className=\"mb-8\">\n              <FileText size={64} className=\"mx-auto mb-4 text-blue-600\" />\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                Scripty\n              </h1>\n              <p className=\"text-gray-600\">\n                Create and edit scripts and screenplays together in real-time\n              </p>\n            </div>\n            <SignInForm />\n          </div>\n        </div>\n      </Unauthenticated>\n\n      <Authenticated>\n        <div className=\"flex-1 flex h-full overflow-hidden\">\n          {showSidebar && <Sidebar />}\n          <div className=\"flex-1 flex flex-col\">\n            <Header showSidebar={showSidebar} />\n            <main className=\"flex-1 overflow-hidden\">\n              {children}\n            </main>\n          </div>\n        </div>\n      </Authenticated>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AACA;AACA;AAPA;;;;;;;AAcO,SAAS,UAAU,EAAE,QAAQ,EAAE,cAAc,IAAI,EAAkB;IACxE,qBACE,8OAAC;QAAI,WAAU;QAAoD,eAAY;;0BAC7E,8OAAC,8JAAA,CAAA,kBAAe;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC9B,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAI/B,8OAAC,wIAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;0BAKjB,8OAAC,8JAAA,CAAA,gBAAa;0BACZ,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,6BAAe,8OAAC,uIAAA,CAAA,UAAO;;;;;sCACxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sIAAA,CAAA,SAAM;oCAAC,aAAa;;;;;;8CACrB,8OAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 749, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 872, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 901, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 929, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/dialog.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1060, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/hooks/use-toast.ts"], "sourcesContent": ["import * as React from \"react\"\n\nimport type {\n  ToastActionElement,\n  ToastProps,\n} from \"../components/ui/toast\"\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: \"ADD_TOAST\",\n  UPDATE_TOAST: \"UPDATE_TOAST\",\n  DISMISS_TOAST: \"DISMISS_TOAST\",\n  REMOVE_TOAST: \"REMOVE_TOAST\",\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType[\"ADD_TOAST\"]\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType[\"UPDATE_TOAST\"]\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType[\"DISMISS_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n  | {\n      type: ActionType[\"REMOVE_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: \"REMOVE_TOAST\",\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case \"ADD_TOAST\":\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case \"UPDATE_TOAST\":\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      }\n\n    case \"DISMISS_TOAST\": {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case \"REMOVE_TOAST\":\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach((listener) => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, \"id\">\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: \"UPDATE_TOAST\",\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\n\n  dispatch({\n    type: \"ADD_TOAST\",\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\n  }\n}\n\nexport { useToast, toast }\n"], "names": [], "mappings": ";;;;;AAAA;;AAOA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAS3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAc;IACpC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAA2C,EAAE;AAEnD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS,MAAM,EAAE,GAAG,OAAc;IAChC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAS;IAEhD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,UAAU,IAAI,CAAC;QACf,OAAO;YACL,MAAM,QAAQ,UAAU,OAAO,CAAC;YAChC,IAAI,QAAQ,CAAC,GAAG;gBACd,UAAU,MAAM,CAAC,OAAO;YAC1B;QACF;IACF,GAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAC3E;AACF", "debugId": null}}, {"offset": {"line": 1214, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/lib/routing.ts"], "sourcesContent": ["/**\n * URL routing utilities for document navigation\n * Handles document ID parsing, validation, and URL generation\n */\n\nimport { Id } from \"../../convex/_generated/dataModel\";\n\n/**\n * Validates if a string is a valid Convex document ID format\n * Convex IDs are strings at runtime, typically in format like \"j57abc123def456\"\n */\nexport function isValidDocumentId(id: string): boolean {\n  // Basic validation - Convex IDs are non-empty strings\n  // More specific validation could be added based on Convex ID patterns\n  // Exclude common route names that shouldn't be treated as document IDs\n  const excludedRoutes = ['other', 'about', 'settings', 'profile', 'admin', 'api'];\n  return typeof id === \"string\" &&\n         id.length > 0 &&\n         !id.includes(\"/\") &&\n         !id.includes(\"?\") &&\n         !excludedRoutes.includes(id.toLowerCase());\n}\n\n/**\n * Validates if a string is a valid Convex script ID format\n */\nexport function isValidScriptId(id: string): boolean {\n  // Same validation as document IDs\n  const excludedRoutes = ['other', 'about', 'settings', 'profile', 'admin', 'api'];\n  return typeof id === \"string\" &&\n         id.length > 0 &&\n         !id.includes(\"/\") &&\n         !id.includes(\"?\") &&\n         !excludedRoutes.includes(id.toLowerCase());\n}\n\n/**\n * Extracts script and document IDs from URL pathname\n * Supports patterns:\n * - /document/{documentId} (legacy standalone documents)\n * - /script/{scriptId}/document/{documentId} (new hierarchical structure)\n */\nexport function extractIdsFromPath(pathname: string): {\n  scriptId: string | null;\n  documentId: string | null;\n} {\n  // Remove leading slash\n  const cleanPath = pathname.startsWith(\"/\") ? pathname.slice(1) : pathname;\n\n  // Handle /script/{scriptId}/document/{documentId} pattern\n  if (cleanPath.startsWith(\"script/\")) {\n    const parts = cleanPath.split(\"/\");\n    if (parts.length >= 4 && parts[0] === \"script\" && parts[2] === \"document\") {\n      const scriptId = parts[1];\n      const documentId = parts[3];\n\n      if (isValidScriptId(scriptId) && isValidDocumentId(documentId)) {\n        return { scriptId, documentId };\n      }\n    }\n\n    // Handle /script/{scriptId} pattern (script only)\n    if (parts.length === 2 && parts[0] === \"script\") {\n      const scriptId = parts[1];\n      if (isValidScriptId(scriptId)) {\n        return { scriptId, documentId: null };\n      }\n    }\n  }\n\n  // Handle legacy /document/{documentId} pattern\n  if (cleanPath.startsWith(\"document/\")) {\n    const documentId = cleanPath.slice(\"document/\".length);\n    if (isValidDocumentId(documentId)) {\n      return { scriptId: null, documentId };\n    }\n  }\n\n  return { scriptId: null, documentId: null };\n}\n\n/**\n * Extracts document ID from URL pathname (legacy function for backward compatibility)\n * Supports patterns: /document/{documentId} only for better URL structure\n */\nexport function extractDocumentIdFromPath(pathname: string): string | null {\n  const { documentId } = extractIdsFromPath(pathname);\n  return documentId;\n}\n\n/**\n * Extracts document ID from URL search params\n * Supports pattern: ?doc={documentId}\n */\nexport function extractDocumentIdFromSearch(search: string): string | null {\n  const params = new URLSearchParams(search);\n  const docId = params.get(\"doc\");\n  \n  if (docId && isValidDocumentId(docId)) {\n    return docId;\n  }\n  \n  return null;\n}\n\n/**\n * Extracts document ID from full URL (pathname or search params)\n * Tries pathname first, then falls back to search params\n */\nexport function extractDocumentIdFromUrl(url: string): string | null {\n  try {\n    const urlObj = new URL(url, window.location.origin);\n    \n    // Try pathname first\n    const pathId = extractDocumentIdFromPath(urlObj.pathname);\n    if (pathId) {\n      return pathId;\n    }\n    \n    // Fall back to search params\n    return extractDocumentIdFromSearch(urlObj.search);\n  } catch {\n    // If URL parsing fails, try as pathname only\n    return extractDocumentIdFromPath(url);\n  }\n}\n\n/**\n * Generates URL for a script\n */\nexport function generateScriptUrl(scriptId: Id<\"scripts\">): string {\n  return `/script/${scriptId}`;\n}\n\n/**\n * Generates URL for a document within a script\n */\nexport function generateScriptDocumentUrl(scriptId: Id<\"scripts\">, documentId: Id<\"documents\">): string {\n  return `/script/${scriptId}/document/${documentId}`;\n}\n\n/**\n * Generates URL for a document using the /document/{documentId} pattern (legacy standalone documents)\n */\nexport function generateDocumentUrl(documentId: Id<\"documents\">): string {\n  return `/document/${documentId}`;\n}\n\n/**\n * Generates URL for the home page (no document selected)\n */\nexport function generateHomeUrl(): string {\n  return \"/\";\n}\n\n/**\n * Checks if the current URL represents a document route\n */\nexport function isDocumentRoute(pathname: string): boolean {\n  return extractDocumentIdFromPath(pathname) !== null;\n}\n\n/**\n * Navigation utilities for programmatic routing\n */\nexport const navigationUtils = {\n  /**\n   * Navigate to a script without page refresh\n   */\n  navigateToScript: (scriptId: Id<\"scripts\">) => {\n    const url = generateScriptUrl(scriptId);\n    window.history.pushState(null, \"\", url);\n  },\n\n  /**\n   * Navigate to a document within a script without page refresh\n   */\n  navigateToScriptDocument: (scriptId: Id<\"scripts\">, documentId: Id<\"documents\">) => {\n    const url = generateScriptDocumentUrl(scriptId, documentId);\n    window.history.pushState(null, \"\", url);\n  },\n\n  /**\n   * Navigate to a standalone document without page refresh (legacy)\n   */\n  navigateToDocument: (documentId: Id<\"documents\">) => {\n    const url = generateDocumentUrl(documentId);\n    window.history.pushState(null, \"\", url);\n  },\n\n  /**\n   * Navigate to home without page refresh\n   */\n  navigateToHome: () => {\n    const url = generateHomeUrl();\n    window.history.pushState(null, \"\", url);\n  },\n\n  /**\n   * Replace current URL without adding to history\n   */\n  replaceUrl: (url: string) => {\n    window.history.replaceState(null, \"\", url);\n  }\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;AAQM,SAAS,kBAAkB,EAAU;IAC1C,sDAAsD;IACtD,sEAAsE;IACtE,uEAAuE;IACvE,MAAM,iBAAiB;QAAC;QAAS;QAAS;QAAY;QAAW;QAAS;KAAM;IAChF,OAAO,OAAO,OAAO,YACd,GAAG,MAAM,GAAG,KACZ,CAAC,GAAG,QAAQ,CAAC,QACb,CAAC,GAAG,QAAQ,CAAC,QACb,CAAC,eAAe,QAAQ,CAAC,GAAG,WAAW;AAChD;AAKO,SAAS,gBAAgB,EAAU;IACxC,kCAAkC;IAClC,MAAM,iBAAiB;QAAC;QAAS;QAAS;QAAY;QAAW;QAAS;KAAM;IAChF,OAAO,OAAO,OAAO,YACd,GAAG,MAAM,GAAG,KACZ,CAAC,GAAG,QAAQ,CAAC,QACb,CAAC,GAAG,QAAQ,CAAC,QACb,CAAC,eAAe,QAAQ,CAAC,GAAG,WAAW;AAChD;AAQO,SAAS,mBAAmB,QAAgB;IAIjD,uBAAuB;IACvB,MAAM,YAAY,SAAS,UAAU,CAAC,OAAO,SAAS,KAAK,CAAC,KAAK;IAEjE,0DAA0D;IAC1D,IAAI,UAAU,UAAU,CAAC,YAAY;QACnC,MAAM,QAAQ,UAAU,KAAK,CAAC;QAC9B,IAAI,MAAM,MAAM,IAAI,KAAK,KAAK,CAAC,EAAE,KAAK,YAAY,KAAK,CAAC,EAAE,KAAK,YAAY;YACzE,MAAM,WAAW,KAAK,CAAC,EAAE;YACzB,MAAM,aAAa,KAAK,CAAC,EAAE;YAE3B,IAAI,gBAAgB,aAAa,kBAAkB,aAAa;gBAC9D,OAAO;oBAAE;oBAAU;gBAAW;YAChC;QACF;QAEA,kDAAkD;QAClD,IAAI,MAAM,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE,KAAK,UAAU;YAC/C,MAAM,WAAW,KAAK,CAAC,EAAE;YACzB,IAAI,gBAAgB,WAAW;gBAC7B,OAAO;oBAAE;oBAAU,YAAY;gBAAK;YACtC;QACF;IACF;IAEA,+CAA+C;IAC/C,IAAI,UAAU,UAAU,CAAC,cAAc;QACrC,MAAM,aAAa,UAAU,KAAK,CAAC,YAAY,MAAM;QACrD,IAAI,kBAAkB,aAAa;YACjC,OAAO;gBAAE,UAAU;gBAAM;YAAW;QACtC;IACF;IAEA,OAAO;QAAE,UAAU;QAAM,YAAY;IAAK;AAC5C;AAMO,SAAS,0BAA0B,QAAgB;IACxD,MAAM,EAAE,UAAU,EAAE,GAAG,mBAAmB;IAC1C,OAAO;AACT;AAMO,SAAS,4BAA4B,MAAc;IACxD,MAAM,SAAS,IAAI,gBAAgB;IACnC,MAAM,QAAQ,OAAO,GAAG,CAAC;IAEzB,IAAI,SAAS,kBAAkB,QAAQ;QACrC,OAAO;IACT;IAEA,OAAO;AACT;AAMO,SAAS,yBAAyB,GAAW;IAClD,IAAI;QACF,MAAM,SAAS,IAAI,IAAI,KAAK,OAAO,QAAQ,CAAC,MAAM;QAElD,qBAAqB;QACrB,MAAM,SAAS,0BAA0B,OAAO,QAAQ;QACxD,IAAI,QAAQ;YACV,OAAO;QACT;QAEA,6BAA6B;QAC7B,OAAO,4BAA4B,OAAO,MAAM;IAClD,EAAE,OAAM;QACN,6CAA6C;QAC7C,OAAO,0BAA0B;IACnC;AACF;AAKO,SAAS,kBAAkB,QAAuB;IACvD,OAAO,CAAC,QAAQ,EAAE,UAAU;AAC9B;AAKO,SAAS,0BAA0B,QAAuB,EAAE,UAA2B;IAC5F,OAAO,CAAC,QAAQ,EAAE,SAAS,UAAU,EAAE,YAAY;AACrD;AAKO,SAAS,oBAAoB,UAA2B;IAC7D,OAAO,CAAC,UAAU,EAAE,YAAY;AAClC;AAKO,SAAS;IACd,OAAO;AACT;AAKO,SAAS,gBAAgB,QAAgB;IAC9C,OAAO,0BAA0B,cAAc;AACjD;AAKO,MAAM,kBAAkB;IAC7B;;GAEC,GACD,kBAAkB,CAAC;QACjB,MAAM,MAAM,kBAAkB;QAC9B,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI;IACrC;IAEA;;GAEC,GACD,0BAA0B,CAAC,UAAyB;QAClD,MAAM,MAAM,0BAA0B,UAAU;QAChD,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI;IACrC;IAEA;;GAEC,GACD,oBAAoB,CAAC;QACnB,MAAM,MAAM,oBAAoB;QAChC,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI;IACrC;IAEA;;GAEC,GACD,gBAAgB;QACd,MAAM,MAAM;QACZ,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI;IACrC;IAEA;;GAEC,GACD,YAAY,CAAC;QACX,OAAO,OAAO,CAAC,YAAY,CAAC,MAAM,IAAI;IACxC;AACF", "debugId": null}}, {"offset": {"line": 1378, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/hooks/useScriptRouting.ts"], "sourcesContent": ["/**\n * React hook for URL-based script and document routing\n * Manages script and document selection via URL synchronization\n * Updated for Next.js App Router\n */\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { Id } from '../../convex/_generated/dataModel';\nimport {\n  extractIdsFromPath,\n  generateScriptUrl,\n  generateScriptDocumentUrl,\n  generateDocumentUrl,\n  generateHomeUrl\n} from '../lib/routing';\n\ninterface UseScriptRoutingReturn {\n  selectedScriptId: Id<\"scripts\"> | null;\n  selectedDocumentId: Id<\"documents\"> | null;\n  navigateToScript: (scriptId: Id<\"scripts\">) => void;\n  navigateToScriptDocument: (scriptId: Id<\"scripts\">, documentId: Id<\"documents\">) => void;\n  navigateToDocument: (documentId: Id<\"documents\">) => void;\n  navigateToHome: () => void;\n  isLoading: boolean;\n}\n\n/**\n * Hook for managing script routing via Next.js App Router\n */\nexport function useScriptRouting(): UseScriptRoutingReturn {\n  const router = useRouter();\n  const pathname = usePathname();\n  const [selectedScriptId, setSelectedScriptId] = useState<Id<\"scripts\"> | null>(null);\n  const [selectedDocumentId, setSelectedDocumentId] = useState<Id<\"documents\"> | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Extract IDs from current pathname\n  useEffect(() => {\n    const { scriptId, documentId } = extractIdsFromPath(pathname);\n\n    if (scriptId) {\n      setSelectedScriptId(scriptId as Id<\"scripts\">);\n    }\n\n    if (documentId) {\n      setSelectedDocumentId(documentId as Id<\"documents\">);\n    }\n\n    setIsLoading(false);\n  }, [pathname]);\n\n  // Navigate to a specific script\n  const navigateToScript = useCallback((scriptId: Id<\"scripts\">) => {\n    const url = generateScriptUrl(scriptId);\n    router.push(url);\n  }, [router]);\n\n  // Navigate to a document within a script\n  const navigateToScriptDocument = useCallback((scriptId: Id<\"scripts\">, documentId: Id<\"documents\">) => {\n    const url = generateScriptDocumentUrl(scriptId, documentId);\n    router.push(url);\n  }, [router]);\n\n  // Navigate to a standalone document (legacy)\n  const navigateToDocument = useCallback((documentId: Id<\"documents\">) => {\n    const url = generateDocumentUrl(documentId);\n    router.push(url);\n  }, [router]);\n\n  // Navigate to home\n  const navigateToHome = useCallback(() => {\n    const url = generateHomeUrl();\n    router.push(url);\n  }, [router]);\n\n  return {\n    selectedScriptId,\n    selectedDocumentId,\n    navigateToScript,\n    navigateToScriptDocument,\n    navigateToDocument,\n    navigateToHome,\n    isLoading\n  };\n}\n\n\n\n/**\n * Hook for URL generation utilities\n * Provides utilities for creating script and document links\n */\nexport function useScriptUrls() {\n  const getScriptUrl = useCallback((scriptId: Id<\"scripts\">) => {\n    return generateScriptUrl(scriptId);\n  }, []);\n\n  const getScriptDocumentUrl = useCallback((scriptId: Id<\"scripts\">, documentId: Id<\"documents\">) => {\n    return generateScriptDocumentUrl(scriptId, documentId);\n  }, []);\n\n  const getDocumentUrl = useCallback((documentId: Id<\"documents\">) => {\n    return generateDocumentUrl(documentId);\n  }, []);\n\n  const getHomeUrl = useCallback(() => {\n    return generateHomeUrl();\n  }, []);\n\n  return {\n    getScriptUrl,\n    getScriptDocumentUrl,\n    getDocumentUrl,\n    getHomeUrl\n  };\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAED;AACA;AAEA;;;;AAqBO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC/E,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACrF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,qBAAkB,AAAD,EAAE;QAEpD,IAAI,UAAU;YACZ,oBAAoB;QACtB;QAEA,IAAI,YAAY;YACd,sBAAsB;QACxB;QAEA,aAAa;IACf,GAAG;QAAC;KAAS;IAEb,gCAAgC;IAChC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,MAAM,MAAM,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD,EAAE;QAC9B,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;KAAO;IAEX,yCAAyC;IACzC,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAyB;QACrE,MAAM,MAAM,CAAA,GAAA,qHAAA,CAAA,4BAAyB,AAAD,EAAE,UAAU;QAChD,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;KAAO;IAEX,6CAA6C;IAC7C,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,MAAM,MAAM,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAAE;QAChC,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;KAAO;IAEX,mBAAmB;IACnB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,MAAM,MAAM,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD;QAC1B,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;KAAO;IAEX,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAQO,SAAS;IACd,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,OAAO,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD,EAAE;IAC3B,GAAG,EAAE;IAEL,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAyB;QACjE,OAAO,CAAA,GAAA,qHAAA,CAAA,4BAAyB,AAAD,EAAE,UAAU;IAC7C,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,OAAO,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAAE;IAC7B,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,OAAO,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD;IACvB,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1475, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ScriptList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from \"react\";\nimport * as React from \"react\";\nimport { useQuery, useMutation } from \"convex/react\";\nimport { api } from \"../../convex/_generated/api\";\nimport { Button } from \"./ui/button\";\nimport { Card, CardContent } from \"./ui/card\";\nimport { Badge } from \"./ui/badge\";\nimport { Input } from \"./ui/input\";\nimport { Textarea } from \"./ui/textarea\";\nimport {\n  Plus,\n  FileText,\n  Trash2,\n  Crown,\n  Eye,\n  Edit,\n  Lock,\n  AlertCircle,\n  FolderOpen\n} from \"lucide-react\";\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from \"./ui/dialog\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { useScriptUrls } from \"@/hooks/useScriptRouting\";\nimport { Id } from \"../../convex/_generated/dataModel\";\n\ninterface ScriptListProps {\n  onSelectScript: (scriptId: Id<\"scripts\">) => void;\n  selectedScriptId: Id<\"scripts\"> | null;\n  onScriptCountChange?: (count: number) => void;\n}\n\nexport function ScriptList({ onSelectScript, selectedScriptId, onScriptCountChange }: ScriptListProps) {\n  const scripts = useQuery(api.scripts.getUserScripts);\n  const canCreateResult = useQuery(api.scripts.canCreateScripts);\n  const createScript = useMutation(api.scripts.createScript);\n  const deleteScript = useMutation(api.scripts.deleteScript);\n  const { getScriptUrl } = useScriptUrls();\n\n  // Extract canCreate boolean for backward compatibility\n  const canCreate = canCreateResult?.canCreate ?? false;\n\n  // State for create dialog\n  const [showCreateDialog, setShowCreateDialog] = useState(false);\n  const [newScriptTitle, setNewScriptTitle] = useState(\"\");\n  const [newScriptDescription, setNewScriptDescription] = useState(\"\");\n  const [isCreating, setIsCreating] = useState(false);\n\n  // State for delete dialog\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\n  const [scriptToDelete, setScriptToDelete] = useState<Id<\"scripts\"> | null>(null);\n  const [isDeleting, setIsDeleting] = useState(false);\n\n  const { toast } = useToast();\n\n  // Notify parent component of script count changes\n  React.useEffect(() => {\n    if (scripts && onScriptCountChange) {\n      onScriptCountChange(scripts.length);\n    }\n  }, [scripts, onScriptCountChange]);\n\n  const handleShowCreateDialog = () => {\n    setNewScriptTitle(\"\");\n    setNewScriptDescription(\"\");\n    setShowCreateDialog(true);\n  };\n\n  const handleCreateScript = async () => {\n    if (!newScriptTitle.trim()) {\n      toast({\n        title: \"Error\",\n        description: \"Please enter a script title\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    setIsCreating(true);\n    try {\n      const scriptId = await createScript({\n        title: newScriptTitle.trim(),\n        description: newScriptDescription.trim() || undefined,\n      });\n\n      toast({\n        title: \"Success\",\n        description: \"Script created successfully\",\n      });\n\n      setShowCreateDialog(false);\n      setNewScriptTitle(\"\");\n      setNewScriptDescription(\"\");\n\n      // Navigate to the new script\n      onSelectScript(scriptId);\n    } catch (error) {\n      console.error(\"Failed to create script:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to create script. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsCreating(false);\n    }\n  };\n\n  const handleDeleteScript = async () => {\n    if (!scriptToDelete) return;\n\n    setIsDeleting(true);\n    try {\n      await deleteScript({ id: scriptToDelete });\n      \n      toast({\n        title: \"Success\",\n        description: \"Script deleted successfully\",\n      });\n\n      setShowDeleteDialog(false);\n      setScriptToDelete(null);\n\n      // If the deleted script was selected, clear selection\n      if (selectedScriptId === scriptToDelete) {\n        // Navigate to home or first available script\n        if (scripts && scripts.length > 1) {\n          const remainingScripts = scripts.filter(s => s._id !== scriptToDelete);\n          if (remainingScripts.length > 0) {\n            onSelectScript(remainingScripts[0]._id);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Failed to delete script:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to delete script. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n\n  const getPermissionIcon = (permission: string) => {\n    switch (permission) {\n      case \"owner\":\n        return <Crown className=\"h-3 w-3\" />;\n      case \"write\":\n        return <Edit className=\"h-3 w-3\" />;\n      case \"read\":\n        return <Eye className=\"h-3 w-3\" />;\n      default:\n        return <Lock className=\"h-3 w-3\" />;\n    }\n  };\n\n  const getPermissionColor = (permission: string) => {\n    switch (permission) {\n      case \"owner\":\n        return \"default\";\n      case \"write\":\n        return \"secondary\";\n      case \"read\":\n        return \"outline\";\n      default:\n        return \"destructive\";\n    }\n  };\n\n  if (!scripts) {\n    return (\n      <div className=\"w-80 h-screen bg-white border-r border-gray-200 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"w-80 h-screen bg-white border-r border-gray-200 flex flex-col\">\n      {/* Sidebar Content */}\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n        {canCreate === false && (\n          <Card className=\"border-amber-200 bg-amber-50\">\n            <CardContent className=\"p-3\">\n              <div className=\"flex items-start gap-2 text-amber-800\">\n                <AlertCircle className=\"h-4 w-4 mt-0.5 flex-shrink-0\" />\n                <div>\n                  <p className=\"font-medium text-sm\">Limited Access</p>\n                  <p className=\"text-xs\">\n                    {canCreateResult?.reason === \"anonymous_user\"\n                      ? \"Anonymous users can only view shared scripts.\"\n                      : canCreateResult?.reason === \"not_authenticated\"\n                        ? \"Please sign in to create and manage scripts.\"\n                        : \"You can view shared scripts but cannot create new ones.\"\n                    }\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        {scripts.length === 0 ? (\n          <div className=\"text-center py-8 text-gray-500\">\n            <FolderOpen size={48} className=\"mx-auto mb-4 opacity-50\" />\n            <p className=\"text-sm\">No scripts available</p>\n            {canCreate && (\n              <p className=\"text-xs mt-2\">Create your first script to get started</p>\n            )}\n          </div>\n        ) : (\n          <div className=\"space-y-3\">\n            {scripts.map((script) => {\n              const isSelected = selectedScriptId === script._id;\n              \n              return (\n                <a\n                  key={script._id}\n                  href={getScriptUrl(script._id)}\n                  onClick={(e) => {\n                    e.preventDefault();\n                    onSelectScript(script._id);\n                  }}\n                  className={`block p-4 rounded-lg border transition-all duration-200 group hover:shadow-md ${\n                    isSelected\n                      ? 'border-blue-500 bg-blue-50 shadow-sm'\n                      : 'border-gray-200 bg-white hover:border-gray-300'\n                  }`}\n                >\n                  <div className=\"flex items-start justify-between mb-2\">\n                    <h3 className={`text-sm font-medium line-clamp-2 flex-1 mr-2 ${\n                      isSelected ? 'text-blue-900' : 'text-gray-900'\n                    }`}>\n                      {script.title}\n                    </h3>\n                    {script.permission === \"owner\" && (\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        onClick={(e) => {\n                          e.preventDefault();\n                          e.stopPropagation();\n                          setScriptToDelete(script._id);\n                          setShowDeleteDialog(true);\n                        }}\n                        className=\"opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0 text-gray-400 hover:text-red-600\"\n                      >\n                        <Trash2 className=\"h-3 w-3\" />\n                      </Button>\n                    )}\n                  </div>\n\n                  {script.description && (\n                    <p className=\"text-xs text-gray-600 mb-2 line-clamp-2\">\n                      {script.description}\n                    </p>\n                  )}\n\n                  <div className=\"flex items-center justify-between\">\n                    <Badge\n                      variant={getPermissionColor(script.permission) as any}\n                      className=\"gap-1 text-xs\"\n                    >\n                      {getPermissionIcon(script.permission)}\n                      {script.permission}\n                    </Badge>\n                    <span className=\"text-xs text-muted-foreground\">\n                      {new Date(script._creationTime).toLocaleDateString()}\n                    </span>\n                  </div>\n                </a>\n              );\n            })}\n          </div>\n        )}\n      </div>\n\n      {/* Create Script Dialog */}\n      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>Create New Script</DialogTitle>\n          </DialogHeader>\n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"script-title\" className=\"text-sm font-medium\">\n                Title *\n              </label>\n              <Input\n                id=\"script-title\"\n                value={newScriptTitle}\n                onChange={(e) => setNewScriptTitle(e.target.value)}\n                placeholder=\"Enter script title...\"\n                className=\"mt-1\"\n              />\n            </div>\n            <div>\n              <label htmlFor=\"script-description\" className=\"text-sm font-medium\">\n                Description\n              </label>\n              <Textarea\n                id=\"script-description\"\n                value={newScriptDescription}\n                onChange={(e) => setNewScriptDescription(e.target.value)}\n                placeholder=\"Enter script description...\"\n                className=\"mt-1\"\n                rows={3}\n              />\n            </div>\n          </div>\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowCreateDialog(false)}\n              disabled={isCreating}\n            >\n              Cancel\n            </Button>\n            <Button onClick={handleCreateScript} disabled={isCreating}>\n              {isCreating ? \"Creating...\" : \"Create Script\"}\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* Delete Script Dialog */}\n      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>Delete Script</DialogTitle>\n          </DialogHeader>\n          <p className=\"text-sm text-gray-600\">\n            Are you sure you want to delete this script? This will also delete all documents within the script. This action cannot be undone.\n          </p>\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowDeleteDialog(false)}\n              disabled={isDeleting}\n            >\n              Cancel\n            </Button>\n            <Button\n              variant=\"destructive\"\n              onClick={handleDeleteScript}\n              disabled={isDeleting}\n            >\n              {isDeleting ? \"Deleting...\" : \"Delete Script\"}\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AAxBA;;;;;;;;;;;;;;;AAiCO,SAAS,WAAW,EAAE,cAAc,EAAE,gBAAgB,EAAE,mBAAmB,EAAmB;IACnG,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,cAAc;IACnD,MAAM,kBAAkB,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,gBAAgB;IAC7D,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,YAAY;IACzD,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,YAAY;IACzD,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;IAErC,uDAAuD;IACvD,MAAM,YAAY,iBAAiB,aAAa;IAEhD,0BAA0B;IAC1B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,0BAA0B;IAC1B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC3E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAEzB,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,WAAW,qBAAqB;YAClC,oBAAoB,QAAQ,MAAM;QACpC;IACF,GAAG;QAAC;QAAS;KAAoB;IAEjC,MAAM,yBAAyB;QAC7B,kBAAkB;QAClB,wBAAwB;QACxB,oBAAoB;IACtB;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,eAAe,IAAI,IAAI;YAC1B,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,cAAc;QACd,IAAI;YACF,MAAM,WAAW,MAAM,aAAa;gBAClC,OAAO,eAAe,IAAI;gBAC1B,aAAa,qBAAqB,IAAI,MAAM;YAC9C;YAEA,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,oBAAoB;YACpB,kBAAkB;YAClB,wBAAwB;YAExB,6BAA6B;YAC7B,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,gBAAgB;QAErB,cAAc;QACd,IAAI;YACF,MAAM,aAAa;gBAAE,IAAI;YAAe;YAExC,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,oBAAoB;YACpB,kBAAkB;YAElB,sDAAsD;YACtD,IAAI,qBAAqB,gBAAgB;gBACvC,6CAA6C;gBAC7C,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;oBACjC,MAAM,mBAAmB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK;oBACvD,IAAI,iBAAiB,MAAM,GAAG,GAAG;wBAC/B,eAAe,gBAAgB,CAAC,EAAE,CAAC,GAAG;oBACxC;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,2MAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;oBACZ,cAAc,uBACb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DACV,iBAAiB,WAAW,mBACzB,kDACA,iBAAiB,WAAW,sBAC1B,iDACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASjB,QAAQ,MAAM,KAAK,kBAClB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kNAAA,CAAA,aAAU;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAChC,8OAAC;gCAAE,WAAU;0CAAU;;;;;;4BACtB,2BACC,8OAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;6CAIhC,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC;4BACZ,MAAM,aAAa,qBAAqB,OAAO,GAAG;4BAElD,qBACE,8OAAC;gCAEC,MAAM,aAAa,OAAO,GAAG;gCAC7B,SAAS,CAAC;oCACR,EAAE,cAAc;oCAChB,eAAe,OAAO,GAAG;gCAC3B;gCACA,WAAW,CAAC,8EAA8E,EACxF,aACI,yCACA,kDACJ;;kDAEF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAW,CAAC,6CAA6C,EAC3D,aAAa,kBAAkB,iBAC/B;0DACC,OAAO,KAAK;;;;;;4CAEd,OAAO,UAAU,KAAK,yBACrB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,EAAE,eAAe;oDACjB,kBAAkB,OAAO,GAAG;oDAC5B,oBAAoB;gDACtB;gDACA,WAAU;0DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;oCAKvB,OAAO,WAAW,kBACjB,8OAAC;wCAAE,WAAU;kDACV,OAAO,WAAW;;;;;;kDAIvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAS,mBAAmB,OAAO,UAAU;gDAC7C,WAAU;;oDAET,kBAAkB,OAAO,UAAU;oDACnC,OAAO,UAAU;;;;;;;0DAEpB,8OAAC;gDAAK,WAAU;0DACb,IAAI,KAAK,OAAO,aAAa,EAAE,kBAAkB;;;;;;;;;;;;;+BAlDjD,OAAO,GAAG;;;;;wBAuDrB;;;;;;;;;;;;0BAMN,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;;sCACZ,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;0CAAC;;;;;;;;;;;sCAEf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAe,WAAU;sDAAsB;;;;;;sDAG9D,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CACjD,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAGd,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAqB,WAAU;sDAAsB;;;;;;sDAGpE,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,wBAAwB,EAAE,MAAM,CAAC,KAAK;4CACvD,aAAY;4CACZ,WAAU;4CACV,MAAM;;;;;;;;;;;;;;;;;;sCAIZ,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,oBAAoB;oCACnC,UAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAoB,UAAU;8CAC5C,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAOtC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;;sCACZ,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;0CAAC;;;;;;;;;;;sCAEf,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;sCAGrC,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,oBAAoB;oCACnC,UAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;8CAET,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C", "debugId": null}}, {"offset": {"line": 2076, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/select.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAEA;;;;;;AAEA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2267, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ScriptDocumentList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from \"react\";\nimport * as React from \"react\";\nimport { useQuery, useMutation } from \"convex/react\";\nimport { api } from \"../../convex/_generated/api\";\nimport { Button } from \"./ui/button\";\nimport { Card, CardContent } from \"./ui/card\";\nimport { Badge } from \"./ui/badge\";\nimport { Input } from \"./ui/input\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"./ui/select\";\nimport {\n  Plus,\n  FileText,\n  Trash2,\n  Crown,\n  Eye,\n  Edit,\n  Lock,\n  AlertCircle,\n  Film,\n  BookOpen,\n  FolderOpen\n} from \"lucide-react\";\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from \"./ui/dialog\";\nimport { useToast } from \"../hooks/use-toast\";\nimport { useScriptUrls } from \"../hooks/useScriptRouting\";\nimport { Id } from \"../../convex/_generated/dataModel\";\n\ninterface ScriptDocumentListProps {\n  scriptId: Id<\"scripts\">;\n  onSelectDocument: (scriptId: Id<\"scripts\">, documentId: Id<\"documents\">) => void;\n  selectedDocumentId: Id<\"documents\"> | null;\n  onDocumentCountChange?: (count: number) => void;\n}\n\ntype DocumentType = \"research\" | \"screenplay\" | \"collection\";\n\nexport function ScriptDocumentList({ \n  scriptId, \n  onSelectDocument, \n  selectedDocumentId, \n  onDocumentCountChange \n}: ScriptDocumentListProps) {\n  const documents = useQuery(api.documents.getScriptDocuments, { scriptId });\n  const scriptPermission = useQuery(api.scriptSharing.getScriptPermission, { scriptId });\n  const createDocument = useMutation(api.documents.createDocument);\n  const deleteDocument = useMutation(api.documents.deleteDocument);\n  const { getScriptDocumentUrl } = useScriptUrls();\n\n  // Check if user can create documents in this script\n  const canCreate = scriptPermission?.permission === \"owner\" || scriptPermission?.permission === \"write\";\n\n  // State for create dialog\n  const [showCreateDialog, setShowCreateDialog] = useState(false);\n  const [newDocumentTitle, setNewDocumentTitle] = useState(\"\");\n  const [newDocumentType, setNewDocumentType] = useState<DocumentType>(\"research\");\n  const [isCreating, setIsCreating] = useState(false);\n\n  // State for delete dialog\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\n  const [documentToDelete, setDocumentToDelete] = useState<Id<\"documents\"> | null>(null);\n  const [isDeleting, setIsDeleting] = useState(false);\n\n  const { toast } = useToast();\n\n  // Notify parent component of document count changes\n  React.useEffect(() => {\n    if (documents && onDocumentCountChange) {\n      onDocumentCountChange(documents.length);\n    }\n  }, [documents, onDocumentCountChange]);\n\n  const handleShowCreateDialog = () => {\n    setNewDocumentTitle(\"\");\n    setNewDocumentType(\"research\");\n    setShowCreateDialog(true);\n  };\n\n  const handleCreateDocument = async () => {\n    if (!newDocumentTitle.trim()) {\n      toast({\n        title: \"Error\",\n        description: \"Please enter a document title\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    setIsCreating(true);\n    try {\n      const documentId = await createDocument({\n        title: newDocumentTitle.trim(),\n        scriptId,\n        documentType: newDocumentType,\n      });\n\n      toast({\n        title: \"Success\",\n        description: \"Document created successfully\",\n      });\n\n      setShowCreateDialog(false);\n      setNewDocumentTitle(\"\");\n      setNewDocumentType(\"research\");\n\n      // Navigate to the new document\n      onSelectDocument(scriptId, documentId);\n    } catch (error) {\n      console.error(\"Failed to create document:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to create document. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsCreating(false);\n    }\n  };\n\n  const handleDeleteDocument = async () => {\n    if (!documentToDelete) return;\n\n    setIsDeleting(true);\n    try {\n      await deleteDocument({ id: documentToDelete });\n      \n      toast({\n        title: \"Success\",\n        description: \"Document deleted successfully\",\n      });\n\n      setShowDeleteDialog(false);\n      setDocumentToDelete(null);\n\n      // If the deleted document was selected, clear selection\n      if (selectedDocumentId === documentToDelete) {\n        // Navigate to first available document or script\n        if (documents && documents.length > 1) {\n          const remainingDocuments = documents.filter(d => d._id !== documentToDelete);\n          if (remainingDocuments.length > 0) {\n            onSelectDocument(scriptId, remainingDocuments[0]._id);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Failed to delete document:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to delete document. Please try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n\n  const getDocumentTypeIcon = (type: string) => {\n    switch (type) {\n      case \"screenplay\":\n        return <Film className=\"h-3 w-3\" />;\n      case \"collection\":\n        return <FolderOpen className=\"h-3 w-3\" />;\n      case \"research\":\n      default:\n        return <BookOpen className=\"h-3 w-3\" />;\n    }\n  };\n\n  const getDocumentTypeColor = (type: string) => {\n    switch (type) {\n      case \"screenplay\":\n        return \"default\";\n      case \"collection\":\n        return \"secondary\";\n      case \"research\":\n      default:\n        return \"outline\";\n    }\n  };\n\n  const getPermissionIcon = (permission: string) => {\n    switch (permission) {\n      case \"owner\":\n        return <Crown className=\"h-3 w-3\" />;\n      case \"write\":\n        return <Edit className=\"h-3 w-3\" />;\n      case \"read\":\n        return <Eye className=\"h-3 w-3\" />;\n      default:\n        return <Lock className=\"h-3 w-3\" />;\n    }\n  };\n\n  const getPermissionColor = (permission: string) => {\n    switch (permission) {\n      case \"owner\":\n        return \"default\";\n      case \"write\":\n        return \"secondary\";\n      case \"read\":\n        return \"outline\";\n      default:\n        return \"destructive\";\n    }\n  };\n\n  if (!documents) {\n    return (\n      <div className=\"flex-1 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex-1 flex flex-col\">\n      {/* Header */}\n      <div className=\"p-6 border-b border-gray-200 bg-white\">\n        <div className=\"flex items-center justify-between\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">Documents</h2>\n          {canCreate && (\n            <Button\n              onClick={handleShowCreateDialog}\n              disabled={isCreating}\n              size=\"sm\"\n              className=\"gap-2\"\n            >\n              <Plus className=\"h-4 w-4\" />\n              Add Document\n            </Button>\n          )}\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"flex-1 overflow-y-auto p-6\">\n        {!canCreate && (\n          <Card className=\"border-amber-200 bg-amber-50 mb-4\">\n            <CardContent className=\"p-3\">\n              <div className=\"flex items-start gap-2 text-amber-800\">\n                <AlertCircle className=\"h-4 w-4 mt-0.5 flex-shrink-0\" />\n                <div>\n                  <p className=\"font-medium text-sm\">Read-Only Access</p>\n                  <p className=\"text-xs\">\n                    You can view documents but cannot create or modify them.\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        {documents.length === 0 ? (\n          <div className=\"text-center py-12 text-gray-500\">\n            <FileText size={48} className=\"mx-auto mb-4 opacity-50\" />\n            <p className=\"text-sm\">No documents in this script</p>\n            {canCreate && (\n              <p className=\"text-xs mt-2\">Add your first document to get started</p>\n            )}\n          </div>\n        ) : (\n          <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n            {documents.map((document) => {\n              const isSelected = selectedDocumentId === document._id;\n              \n              return (\n                <a\n                  key={document._id}\n                  href={getScriptDocumentUrl(scriptId, document._id)}\n                  onClick={(e) => {\n                    e.preventDefault();\n                    onSelectDocument(scriptId, document._id);\n                  }}\n                  className={`block p-4 rounded-lg border transition-all duration-200 group hover:shadow-md ${\n                    isSelected\n                      ? 'border-blue-500 bg-blue-50 shadow-sm'\n                      : 'border-gray-200 bg-white hover:border-gray-300'\n                  }`}\n                >\n                  <div className=\"flex items-start justify-between mb-3\">\n                    <h3 className={`text-sm font-medium line-clamp-2 flex-1 mr-2 ${\n                      isSelected ? 'text-blue-900' : 'text-gray-900'\n                    }`}>\n                      {document.title}\n                    </h3>\n                    {document.permission === \"owner\" && (\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        onClick={(e) => {\n                          e.preventDefault();\n                          e.stopPropagation();\n                          setDocumentToDelete(document._id);\n                          setShowDeleteDialog(true);\n                        }}\n                        className=\"opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0 text-gray-400 hover:text-red-600\"\n                      >\n                        <Trash2 className=\"h-3 w-3\" />\n                      </Button>\n                    )}\n                  </div>\n\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <Badge\n                      variant={getDocumentTypeColor(document.documentType) as any}\n                      className=\"gap-1 text-xs\"\n                    >\n                      {getDocumentTypeIcon(document.documentType)}\n                      {document.documentType}\n                    </Badge>\n                    <Badge\n                      variant={getPermissionColor(document.permission) as any}\n                      className=\"gap-1 text-xs\"\n                    >\n                      {getPermissionIcon(document.permission)}\n                      {document.permission}\n                    </Badge>\n                  </div>\n\n                  <div className=\"text-xs text-muted-foreground\">\n                    {new Date(document._creationTime).toLocaleDateString()}\n                  </div>\n                </a>\n              );\n            })}\n          </div>\n        )}\n      </div>\n\n      {/* Create Document Dialog */}\n      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>Add New Document</DialogTitle>\n          </DialogHeader>\n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"document-title\" className=\"text-sm font-medium\">\n                Title *\n              </label>\n              <Input\n                id=\"document-title\"\n                value={newDocumentTitle}\n                onChange={(e) => setNewDocumentTitle(e.target.value)}\n                placeholder=\"Enter document title...\"\n                className=\"mt-1\"\n              />\n            </div>\n            <div>\n              <label htmlFor=\"document-type\" className=\"text-sm font-medium\">\n                Document Type *\n              </label>\n              <Select value={newDocumentType} onValueChange={(value: DocumentType) => setNewDocumentType(value)}>\n                <SelectTrigger className=\"mt-1\">\n                  <SelectValue placeholder=\"Select document type\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"research\">\n                    <div className=\"flex items-center gap-2\">\n                      <BookOpen className=\"h-4 w-4\" />\n                      Research - Rich text editor\n                    </div>\n                  </SelectItem>\n                  <SelectItem value=\"screenplay\">\n                    <div className=\"flex items-center gap-2\">\n                      <Film className=\"h-4 w-4\" />\n                      Screenplay - Industry format\n                    </div>\n                  </SelectItem>\n                  <SelectItem value=\"collection\">\n                    <div className=\"flex items-center gap-2\">\n                      <FolderOpen className=\"h-4 w-4\" />\n                      Collection - Note organizer\n                    </div>\n                  </SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowCreateDialog(false)}\n              disabled={isCreating}\n            >\n              Cancel\n            </Button>\n            <Button onClick={handleCreateDocument} disabled={isCreating}>\n              {isCreating ? \"Creating...\" : \"Create Document\"}\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* Delete Document Dialog */}\n      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>Delete Document</DialogTitle>\n          </DialogHeader>\n          <p className=\"text-sm text-gray-600\">\n            Are you sure you want to delete this document? This action cannot be undone.\n          </p>\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowDeleteDialog(false)}\n              disabled={isDeleting}\n            >\n              Cancel\n            </Button>\n            <Button\n              variant=\"destructive\"\n              onClick={handleDeleteDocument}\n              disabled={isDeleting}\n            >\n              {isDeleting ? \"Deleting...\" : \"Delete Document\"}\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AA1BA;;;;;;;;;;;;;;;AAsCO,SAAS,mBAAmB,EACjC,QAAQ,EACR,gBAAgB,EAChB,kBAAkB,EAClB,qBAAqB,EACG;IACxB,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,SAAS,CAAC,kBAAkB,EAAE;QAAE;IAAS;IACxE,MAAM,mBAAmB,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,aAAa,CAAC,mBAAmB,EAAE;QAAE;IAAS;IACpF,MAAM,iBAAiB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,SAAS,CAAC,cAAc;IAC/D,MAAM,iBAAiB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,SAAS,CAAC,cAAc;IAC/D,MAAM,EAAE,oBAAoB,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;IAE7C,oDAAoD;IACpD,MAAM,YAAY,kBAAkB,eAAe,WAAW,kBAAkB,eAAe;IAE/F,0BAA0B;IAC1B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,0BAA0B;IAC1B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACjF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAEzB,oDAAoD;IACpD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,aAAa,uBAAuB;YACtC,sBAAsB,UAAU,MAAM;QACxC;IACF,GAAG;QAAC;QAAW;KAAsB;IAErC,MAAM,yBAAyB;QAC7B,oBAAoB;QACpB,mBAAmB;QACnB,oBAAoB;IACtB;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,iBAAiB,IAAI,IAAI;YAC5B,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,cAAc;QACd,IAAI;YACF,MAAM,aAAa,MAAM,eAAe;gBACtC,OAAO,iBAAiB,IAAI;gBAC5B;gBACA,cAAc;YAChB;YAEA,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,oBAAoB;YACpB,oBAAoB;YACpB,mBAAmB;YAEnB,+BAA+B;YAC/B,iBAAiB,UAAU;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,kBAAkB;QAEvB,cAAc;QACd,IAAI;YACF,MAAM,eAAe;gBAAE,IAAI;YAAiB;YAE5C,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,oBAAoB;YACpB,oBAAoB;YAEpB,wDAAwD;YACxD,IAAI,uBAAuB,kBAAkB;gBAC3C,iDAAiD;gBACjD,IAAI,aAAa,UAAU,MAAM,GAAG,GAAG;oBACrC,MAAM,qBAAqB,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK;oBAC3D,IAAI,mBAAmB,MAAM,GAAG,GAAG;wBACjC,iBAAiB,UAAU,kBAAkB,CAAC,EAAE,CAAC,GAAG;oBACtD;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,kNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;YACL;gBACE,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,2MAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,WAAW;QACd,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;wBACnD,2BACC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU;4BACV,MAAK;4BACL,WAAU;;8CAEV,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;0BAQpC,8OAAC;gBAAI,WAAU;;oBACZ,CAAC,2BACA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAShC,UAAU,MAAM,KAAK,kBACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC9B,8OAAC;gCAAE,WAAU;0CAAU;;;;;;4BACtB,2BACC,8OAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;6CAIhC,8OAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC;4BACd,MAAM,aAAa,uBAAuB,SAAS,GAAG;4BAEtD,qBACE,8OAAC;gCAEC,MAAM,qBAAqB,UAAU,SAAS,GAAG;gCACjD,SAAS,CAAC;oCACR,EAAE,cAAc;oCAChB,iBAAiB,UAAU,SAAS,GAAG;gCACzC;gCACA,WAAW,CAAC,8EAA8E,EACxF,aACI,yCACA,kDACJ;;kDAEF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAW,CAAC,6CAA6C,EAC3D,aAAa,kBAAkB,iBAC/B;0DACC,SAAS,KAAK;;;;;;4CAEhB,SAAS,UAAU,KAAK,yBACvB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,EAAE,eAAe;oDACjB,oBAAoB,SAAS,GAAG;oDAChC,oBAAoB;gDACtB;gDACA,WAAU;0DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAKxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAS,qBAAqB,SAAS,YAAY;gDACnD,WAAU;;oDAET,oBAAoB,SAAS,YAAY;oDACzC,SAAS,YAAY;;;;;;;0DAExB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAS,mBAAmB,SAAS,UAAU;gDAC/C,WAAU;;oDAET,kBAAkB,SAAS,UAAU;oDACrC,SAAS,UAAU;;;;;;;;;;;;;kDAIxB,8OAAC;wCAAI,WAAU;kDACZ,IAAI,KAAK,SAAS,aAAa,EAAE,kBAAkB;;;;;;;+BArDjD,SAAS,GAAG;;;;;wBAyDvB;;;;;;;;;;;;0BAMN,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;;sCACZ,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;0CAAC;;;;;;;;;;;sCAEf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAiB,WAAU;sDAAsB;;;;;;sDAGhE,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4CACnD,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAGd,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAgB,WAAU;sDAAsB;;;;;;sDAG/D,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAiB,eAAe,CAAC,QAAwB,mBAAmB;;8DACzF,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAChB,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,8MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAY;;;;;;;;;;;;sEAIpC,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAChB,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAY;;;;;;;;;;;;sEAIhC,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAChB,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;oEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ9C,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,oBAAoB;oCACnC,UAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAsB,UAAU;8CAC9C,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAOtC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;;sCACZ,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;0CAAC;;;;;;;;;;;sCAEf,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;sCAGrC,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,oBAAoB;oCACnC,UAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;8CAET,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C", "debugId": null}}, {"offset": {"line": 3057, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/script/%5BscriptId%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useQuery } from \"convex/react\";\nimport { api } from \"../../../convex/_generated/api\";\nimport { AppLayout } from \"../../components/layout/AppLayout\";\nimport { ScriptList } from \"../../components/ScriptList\";\nimport { ScriptDocumentList } from \"../../components/ScriptDocumentList\";\nimport { useRouter, useParams } from \"next/navigation\";\nimport { useState } from \"react\";\nimport { Film, Users } from \"lucide-react\";\nimport { Id } from \"../../../convex/_generated/dataModel\";\n\nexport default function ScriptPage() {\n  const router = useRouter();\n  const params = useParams();\n  const scriptId = params.scriptId as Id<\"scripts\">;\n  \n  const [documentCount, setDocumentCount] = useState<number>(0);\n  const [scriptCount, setScriptCount] = useState<number>(0);\n  const loggedInUser = useQuery(api.auth.loggedInUser);\n\n  const navigateToScript = (newScriptId: Id<\"scripts\">) => {\n    router.push(`/script/${newScriptId}`);\n  };\n\n  const navigateToScriptDocument = (scriptId: Id<\"scripts\">, documentId: Id<\"documents\">) => {\n    router.push(`/script/${scriptId}/document/${documentId}`);\n  };\n\n  if (loggedInUser === undefined) {\n    return (\n      <AppLayout>\n        <div className=\"flex-1 flex justify-center items-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n        </div>\n      </AppLayout>\n    );\n  }\n\n  return (\n    <AppLayout showSidebar={false}>\n      <div className=\"flex-1 flex h-full overflow-hidden\">\n        {/* Left Sidebar - Scripts */}\n        <ScriptList\n          onSelectScript={navigateToScript}\n          selectedScriptId={scriptId}\n          onScriptCountChange={setScriptCount}\n        />\n        \n        {/* Middle Panel - Documents */}\n        <ScriptDocumentList\n          scriptId={scriptId}\n          onSelectDocument={navigateToScriptDocument}\n          selectedDocumentId={null}\n          onDocumentCountChange={setDocumentCount}\n        />\n\n        {/* Main Content Area */}\n        <div className=\"flex-1 flex flex-col bg-gray-50\">\n          <div className=\"flex-1 flex items-center justify-center p-8\">\n            <div className=\"text-center text-gray-500 max-w-md\">\n              <Film size={64} className=\"mx-auto mb-6 opacity-50\" />\n              <h2 className=\"text-2xl font-semibold mb-3 text-gray-900\">Script Selected</h2>\n              <p className=\"mb-6 text-gray-600\">\n                {documentCount === 0\n                  ? \"Add your first document to this script to get started\"\n                  : \"Select a document from the script to start editing\"\n                }\n              </p>\n              <div className=\"flex items-center justify-center gap-6 text-sm\">\n                <div className=\"flex items-center gap-2\">\n                  <Users size={18} className=\"text-blue-600\" />\n                  <span>Real-time collaboration</span>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <Film size={18} className=\"text-blue-600\" />\n                  <span>Screenplay formatting</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </AppLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AATA;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,OAAO,QAAQ;IAEhC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,IAAI,CAAC,YAAY;IAEnD,MAAM,mBAAmB,CAAC;QACxB,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,aAAa;IACtC;IAEA,MAAM,2BAA2B,CAAC,UAAyB;QACzD,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,SAAS,UAAU,EAAE,YAAY;IAC1D;IAEA,IAAI,iBAAiB,WAAW;QAC9B,qBACE,8OAAC,yIAAA,CAAA,YAAS;sBACR,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC,yIAAA,CAAA,YAAS;QAAC,aAAa;kBACtB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,gIAAA,CAAA,aAAU;oBACT,gBAAgB;oBAChB,kBAAkB;oBAClB,qBAAqB;;;;;;8BAIvB,8OAAC,wIAAA,CAAA,qBAAkB;oBACjB,UAAU;oBACV,kBAAkB;oBAClB,oBAAoB;oBACpB,uBAAuB;;;;;;8BAIzB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CAC1B,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAC1D,8OAAC;oCAAE,WAAU;8CACV,kBAAkB,IACf,0DACA;;;;;;8CAGN,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC3B,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC1B,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB", "debugId": null}}]}