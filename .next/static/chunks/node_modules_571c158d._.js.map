{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/syscall.ts"], "sourcesContent": ["import { ConvexError } from \"../../values/errors.js\";\nimport { jsonToConvex } from \"../../values/value.js\";\n\ndeclare const Convex: {\n  syscall: (op: string, jsonArgs: string) => string;\n  asyncSyscall: (op: string, jsonArgs: string) => Promise<string>;\n  jsSyscall: (op: string, args: Record<string, any>) => any;\n};\n/**\n * Perform a syscall, taking in a JSON-encodable object as an argument, serializing with\n * JSON.stringify, calling into Rust, and then parsing the response as a JSON-encodable\n * value. If one of your arguments is a Convex value, you must call `convexToJson` on it\n * before passing it to this function, and if the return value has a Convex value, you're\n * also responsible for calling `jsonToConvex`: This layer only deals in JSON.\n */\n\nexport function performSyscall(op: string, arg: Record<string, any>): any {\n  if (typeof Convex === \"undefined\" || Convex.syscall === undefined) {\n    throw new Error(\n      \"The Convex database and auth objects are being used outside of a Convex backend. \" +\n        \"Did you mean to use `useQuery` or `useMutation` to call a Convex function?\",\n    );\n  }\n  const resultStr = Convex.syscall(op, JSON.stringify(arg));\n  return JSON.parse(resultStr);\n}\n\nexport async function performAsyncSyscall(\n  op: string,\n  arg: Record<string, any>,\n): Promise<any> {\n  if (typeof Convex === \"undefined\" || Convex.asyncSyscall === undefined) {\n    throw new Error(\n      \"The Convex database and auth objects are being used outside of a Convex backend. \" +\n        \"Did you mean to use `useQuery` or `useMutation` to call a Convex function?\",\n    );\n  }\n  let resultStr;\n  try {\n    resultStr = await Convex.asyncSyscall(op, JSON.stringify(arg));\n  } catch (e: any) {\n    // Rethrow the exception to attach stack trace starting from here.\n    // If the error came from JS it will include its own stack trace in the message.\n    // If it came from Rust it won't.\n\n    // This only happens if we're propagating ConvexErrors\n    if (e.data !== undefined) {\n      const rethrown = new ConvexError(e.message);\n      rethrown.data = jsonToConvex(e.data);\n      throw rethrown;\n    }\n    throw new Error(e.message);\n  }\n  return JSON.parse(resultStr);\n}\n\n/**\n * Call into a \"JS\" syscall. Like `performSyscall`, this calls a dynamically linked\n * function set up in the Convex function execution. Unlike `performSyscall`, the\n * arguments do not need to be JSON-encodable and neither does the return value.\n *\n * @param op\n * @param arg\n * @returns\n */\nexport function performJsSyscall(op: string, arg: Record<string, any>): any {\n  if (typeof Convex === \"undefined\" || Convex.jsSyscall === undefined) {\n    throw new Error(\n      \"The Convex database and auth objects are being used outside of a Convex backend. \" +\n        \"Did you mean to use `useQuery` or `useMutation` to call a Convex function?\",\n    );\n  }\n  return Convex.jsSyscall(op, arg);\n}\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,mBAAmB;AAC5B,SAAS,oBAAoB;;;;AAetB,SAAS,eAAe,EAAA,EAAY,GAAA,EAA+B;IACxE,IAAI,OAAO,WAAW,eAAe,OAAO,OAAA,KAAY,KAAA,GAAW;QACjE,MAAM,IAAI,MACR;IAGJ;IACA,MAAM,YAAY,OAAO,OAAA,CAAQ,IAAI,KAAK,SAAA,CAAU,GAAG,CAAC;IACxD,OAAO,KAAK,KAAA,CAAM,SAAS;AAC7B;AAEA,eAAsB,oBACpB,EAAA,EACA,GAAA,EACc;IACd,IAAI,OAAO,WAAW,eAAe,OAAO,YAAA,KAAiB,KAAA,GAAW;QACtE,MAAM,IAAI,MACR;IAGJ;IACA,IAAI;IACJ,IAAI;QACF,YAAY,MAAM,OAAO,YAAA,CAAa,IAAI,KAAK,SAAA,CAAU,GAAG,CAAC;IAC/D,EAAA,OAAS,GAAQ;QAMf,IAAI,EAAE,IAAA,KAAS,KAAA,GAAW;YACxB,MAAM,WAAW,iKAAI,cAAA,CAAY,EAAE,OAAO;YAC1C,SAAS,IAAA,IAAO,8KAAA,EAAa,EAAE,IAAI;YACnC,MAAM;QACR;QACA,MAAM,IAAI,MAAM,EAAE,OAAO;IAC3B;IACA,OAAO,KAAK,KAAA,CAAM,SAAS;AAC7B;AAWO,SAAS,iBAAiB,EAAA,EAAY,GAAA,EAA+B;IAC1E,IAAI,OAAO,WAAW,eAAe,OAAO,SAAA,KAAc,KAAA,GAAW;QACnE,MAAM,IAAI,MACR;IAGJ;IACA,OAAO,OAAO,SAAA,CAAU,IAAI,GAAG;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/actions_impl.ts"], "sourcesContent": ["import { convexTo<PERSON>son, jsonToConvex, Value } from \"../../values/index.js\";\nimport { version } from \"../../index.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\nimport { parseArgs } from \"../../common/index.js\";\nimport { FunctionReference } from \"../../server/api.js\";\nimport { getFunctionAddress } from \"../components/paths.js\";\n\nfunction syscallArgs(\n  requestId: string,\n  functionReference: any,\n  args?: Record<string, Value>,\n) {\n  const address = getFunctionAddress(functionReference);\n  return {\n    ...address,\n    args: convexToJson(parseArgs(args)),\n    version,\n    requestId,\n  };\n}\n\nexport function setupActionCalls(requestId: string) {\n  return {\n    runQuery: async (\n      query: FunctionReference<\"query\", \"public\" | \"internal\">,\n      args?: Record<string, Value>,\n    ): Promise<any> => {\n      const result = await performAsyncSyscall(\n        \"1.0/actions/query\",\n        syscallArgs(requestId, query, args),\n      );\n      return jsonToConvex(result);\n    },\n    runMutation: async (\n      mutation: FunctionReference<\"mutation\", \"public\" | \"internal\">,\n      args?: Record<string, Value>,\n    ): Promise<any> => {\n      const result = await performAsyncSyscall(\n        \"1.0/actions/mutation\",\n        syscallArgs(requestId, mutation, args),\n      );\n      return jsonToConvex(result);\n    },\n    runAction: async (\n      action: FunctionReference<\"action\", \"public\" | \"internal\">,\n      args?: Record<string, Value>,\n    ): Promise<any> => {\n      const result = await performAsyncSyscall(\n        \"1.0/actions/action\",\n        syscallArgs(requestId, action, args),\n      );\n      return jsonToConvex(result);\n    },\n  };\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,cAAc,oBAA2B;;AAClD,SAAS,eAAe;AACxB,SAAS,2BAA2B;AACpC,SAAS,iBAAiB;AAE1B,SAAS,0BAA0B;;;;;;;AAEnC,SAAS,YACP,SAAA,EACA,iBAAA,EACA,IAAA,EACA;IACA,MAAM,wLAAU,qBAAA,EAAmB,iBAAiB;IACpD,OAAO;QACL,GAAG,OAAA;QACH,sKAAM,eAAA,kKAAa,YAAA,EAAU,IAAI,CAAC;QAClC,qKAAA;QACA;IACF;AACF;AAEO,SAAS,iBAAiB,SAAA,EAAmB;IAClD,OAAO;QACL,UAAU,OACR,OACA,SACiB;YACjB,MAAM,SAAS,gLAAM,sBAAA,EACnB,qBACA,YAAY,WAAW,OAAO,IAAI;YAEpC,QAAO,8KAAA,EAAa,MAAM;QAC5B;QACA,aAAa,OACX,UACA,SACiB;YACjB,MAAM,SAAS,OAAM,+LAAA,EACnB,wBACA,YAAY,WAAW,UAAU,IAAI;YAEvC,uKAAO,eAAA,EAAa,MAAM;QAC5B;QACA,WAAW,OACT,QACA,SACiB;YACjB,MAAM,SAAS,gLAAM,sBAAA,EACnB,sBACA,YAAY,WAAW,QAAQ,IAAI;YAErC,uKAAO,eAAA,EAAa,MAAM;QAC5B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/vector_search.ts"], "sourcesContent": ["import { Id, Value } from \"../values/value.js\";\nimport {\n  DocumentByInfo,\n  FieldTypeFromFieldPath,\n  GenericDataModel,\n  GenericDocument,\n  GenericTableInfo,\n  GenericVectorIndexConfig,\n  NamedTableInfo,\n  NamedVectorIndex,\n  TableNamesInDataModel,\n  VectorIndexNames,\n} from \"./data_model.js\";\n\n/**\n * An object with parameters for performing a vector search against a vector index.\n * @public\n */\nexport interface VectorSearchQuery<\n  TableInfo extends GenericTableInfo,\n  IndexName extends VectorIndexNames<TableInfo>,\n> {\n  /**\n   * The query vector.\n   *\n   * This must have the same length as the `dimensions` of the index.\n   * This vector search will return the IDs of the documents most similar to\n   * this vector.\n   */\n  vector: number[];\n  /**\n   * The number of results to return. If specified, must be between 1 and 256\n   * inclusive.\n   *\n   * @default 10\n   */\n  limit?: number;\n  /**\n   * Optional filter expression made up of `q.or` and `q.eq` operating\n   * over the filter fields of the index.\n   *\n   * e.g. `filter: q => q.or(q.eq(\"genre\", \"comedy\"), q.eq(\"genre\", \"drama\"))`\n   *\n   * @param q\n   * @returns\n   */\n  filter?: (\n    q: VectorFilterBuilder<\n      DocumentByInfo<TableInfo>,\n      NamedVectorIndex<TableInfo, IndexName>\n    >,\n  ) => FilterExpression<boolean>;\n}\n\nexport type VectorSearch<\n  DataModel extends GenericDataModel,\n  TableName extends TableNamesInDataModel<DataModel>,\n  IndexName extends VectorIndexNames<NamedTableInfo<DataModel, TableName>>,\n> = (\n  tableName: TableName,\n  indexName: IndexName,\n  query: VectorSearchQuery<NamedTableInfo<DataModel, TableName>, IndexName>,\n) => Promise<Array<{ _id: Id<TableName>; _score: number }>>;\n\n/**\n * Expressions are evaluated to produce a {@link values.Value} in the course of executing a query.\n *\n * To construct an expression, use the {@link VectorFilterBuilder} provided within\n * {@link VectorSearchQuery}.\n *\n * @typeParam T - The type that this expression evaluates to.\n * @public\n */\nexport abstract class FilterExpression<T extends Value | undefined> {\n  // Property for nominal type support.\n  private _isExpression: undefined;\n\n  // Property to distinguish expressions by the type they resolve to.\n  private _value!: T;\n\n  /**\n   * @internal\n   */\n  constructor() {\n    // only defining the constructor so we can mark it as internal and keep\n    // it out of the docs.\n  }\n}\n\n/**\n * An interface for defining filters for vector searches.\n *\n * This has a similar interface to {@link FilterBuilder}, which is used in\n * database queries, but supports only the methods that can be efficiently\n * done in a vector search.\n *\n * @public\n */\nexport interface VectorFilterBuilder<\n  Document extends GenericDocument,\n  VectorIndexConfig extends GenericVectorIndexConfig,\n> {\n  //  Comparisons  /////////////////////////////////////////////////////////////\n\n  /**\n   * Is the field at `fieldName` equal to `value`\n   *\n   * @public\n   * */\n  eq<FieldName extends VectorIndexConfig[\"filterFields\"]>(\n    fieldName: FieldName,\n    value: FieldTypeFromFieldPath<Document, FieldName>,\n  ): FilterExpression<boolean>;\n\n  //  Logic  ///////////////////////////////////////////////////////////////////\n\n  /**\n   * `exprs[0] || exprs[1] || ... || exprs[n]`\n   *\n   * @public\n   */\n  or(...exprs: Array<FilterExpression<boolean>>): FilterExpression<boolean>;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAyEO,MAAe,iBAA8C;IAAA;;GAAA,GAUlE,aAAc;QARd,qCAAA;QAAA,cAAA,IAAA,EAAQ;QAGR,mEAAA;QAAA,cAAA,IAAA,EAAQ;IAQR;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/validate.ts"], "sourcesContent": ["export function validateArg(\n  arg: any,\n  idx: number,\n  method: string,\n  argName: string,\n) {\n  if (arg === undefined) {\n    throw new TypeError(\n      `Must provide arg ${idx} \\`${argName}\\` to \\`${method}\\``,\n    );\n  }\n}\n\nexport function validateArgIsInteger(\n  arg: any,\n  idx: number,\n  method: string,\n  argName: string,\n) {\n  if (!Number.isInteger(arg)) {\n    throw new TypeError(\n      `Arg ${idx} \\`${argName}\\` to \\`${method}\\` must be an integer`,\n    );\n  }\n}\n\nexport function validateArgIsNonNegativeInteger(\n  arg: any,\n  idx: number,\n  method: string,\n  argName: string,\n) {\n  if (!Number.isInteger(arg) || arg < 0) {\n    throw new TypeError(\n      `Arg ${idx} \\`${argName}\\` to \\`${method}\\` must be a non-negative integer`,\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAO,SAAS,YACd,GAAA,EACA,GAAA,EACA,MAAA,EACA,OAAA,EACA;IACA,IAAI,QAAQ,KAAA,GAAW;QACrB,MAAM,IAAI,UACR,CAAA,iBAAA,EAAoB,GAAG,CAAA,GAAA,EAAM,OAAO,CAAA,QAAA,EAAW,MAAM,CAAA,EAAA,CAAA;IAEzD;AACF;AAEO,SAAS,qBACd,GAAA,EACA,GAAA,EACA,MAAA,EACA,OAAA,EACA;IACA,IAAI,CAAC,OAAO,SAAA,CAAU,GAAG,GAAG;QAC1B,MAAM,IAAI,UACR,CAAA,IAAA,EAAO,GAAG,CAAA,GAAA,EAAM,OAAO,CAAA,QAAA,EAAW,MAAM,CAAA,qBAAA,CAAA;IAE5C;AACF;AAEO,SAAS,gCACd,GAAA,EACA,GAAA,EACA,MAAA,EACA,OAAA,EACA;IACA,IAAI,CAAC,OAAO,SAAA,CAAU,GAAG,KAAK,MAAM,GAAG;QACrC,MAAM,IAAI,UACR,CAAA,IAAA,EAAO,GAAG,CAAA,GAAA,EAAM,OAAO,CAAA,QAAA,EAAW,MAAM,CAAA,iCAAA,CAAA;IAE5C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/vector_search_impl.ts"], "sourcesContent": ["import { J<PERSON>NValue } from \"../../values/index.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\nimport { version } from \"../../index.js\";\nimport {\n  FilterExpression,\n  VectorFilterBuilder,\n  VectorSearch,\n  VectorSearchQuery,\n} from \"../vector_search.js\";\nimport {\n  FieldTypeFromFieldPath,\n  GenericDataModel,\n  GenericDocument,\n  GenericTableInfo,\n  GenericVectorIndexConfig,\n} from \"../data_model.js\";\nimport { validateArg } from \"./validate.js\";\nimport { Value, convexOrUndefinedToJson } from \"../../values/value.js\";\n\nexport function setupActionVectorSearch(\n  requestId: string,\n): VectorSearch<GenericDataModel, string, string> {\n  return async (\n    tableName: string,\n    indexName: string,\n    query: VectorSearchQuery<GenericTableInfo, string>,\n  ) => {\n    validateArg(tableName, 1, \"vectorSearch\", \"tableName\");\n    validateArg(indexName, 2, \"vectorSearch\", \"indexName\");\n    validateArg(query, 3, \"vectorSearch\", \"query\");\n    if (\n      !query.vector ||\n      !Array.isArray(query.vector) ||\n      query.vector.length === 0\n    ) {\n      throw Error(\"`vector` must be a non-empty Array in vectorSearch\");\n    }\n\n    return await new VectorQueryImpl(\n      requestId,\n      tableName + \".\" + indexName,\n      query,\n    ).collect();\n  };\n}\n\nexport class VectorQueryImpl {\n  private requestId: string;\n  private state:\n    | { type: \"preparing\"; query: SerializedVectorQuery }\n    | { type: \"consumed\" };\n\n  constructor(\n    requestId: string,\n    indexName: string,\n    query: VectorSearchQuery<GenericTableInfo, string>,\n  ) {\n    this.requestId = requestId;\n    const filters = query.filter\n      ? serializeExpression(query.filter(filterBuilderImpl))\n      : null;\n\n    this.state = {\n      type: \"preparing\",\n      query: {\n        indexName,\n        limit: query.limit,\n        vector: query.vector,\n        expressions: filters,\n      },\n    };\n  }\n\n  async collect(): Promise<Array<any>> {\n    if (this.state.type === \"consumed\") {\n      throw new Error(\"This query is closed and can't emit any more values.\");\n    }\n    const query = this.state.query;\n    this.state = { type: \"consumed\" };\n\n    const { results } = await performAsyncSyscall(\"1.0/actions/vectorSearch\", {\n      requestId: this.requestId,\n      version,\n      query,\n    });\n    return results;\n  }\n}\n\ntype SerializedVectorQuery = {\n  indexName: string;\n  limit?: number;\n  vector: Array<number>;\n  expressions: JSONValue;\n};\n\ntype ExpressionOrValue<T extends Value | undefined> = FilterExpression<T> | T;\n\n// The `any` type parameter in `Expression<any>` allows us to use this class\n// in place of any `Expression` type in `filterBuilderImpl`.\nexport class ExpressionImpl extends FilterExpression<any> {\n  private inner: JSONValue;\n  constructor(inner: JSONValue) {\n    super();\n    this.inner = inner;\n  }\n\n  serialize(): JSONValue {\n    return this.inner;\n  }\n}\n\nexport function serializeExpression(\n  expr: ExpressionOrValue<Value | undefined>,\n): JSONValue {\n  if (expr instanceof ExpressionImpl) {\n    return expr.serialize();\n  } else {\n    // Assume that the expression is a literal Convex value, which we'll serialize\n    // to its JSON representation.\n    return { $literal: convexOrUndefinedToJson(expr as Value | undefined) };\n  }\n}\n\nexport const filterBuilderImpl: VectorFilterBuilder<\n  GenericDocument,\n  GenericVectorIndexConfig\n> = {\n  //  Comparisons  /////////////////////////////////////////////////////////////\n\n  eq<FieldName extends GenericVectorIndexConfig[\"filterFields\"]>(\n    fieldName: FieldName,\n    value: FieldTypeFromFieldPath<GenericDocument, FieldName>,\n  ): FilterExpression<boolean> {\n    if (typeof fieldName !== \"string\") {\n      throw new Error(\"The first argument to `q.eq` must be a field name.\");\n    }\n    return new ExpressionImpl({\n      $eq: [\n        serializeExpression(new ExpressionImpl({ $field: fieldName })),\n        serializeExpression(value),\n      ],\n    });\n  },\n\n  //  Logic  ///////////////////////////////////////////////////////////////////\n\n  or(...exprs: Array<ExpressionOrValue<boolean>>): FilterExpression<boolean> {\n    return new ExpressionImpl({ $or: exprs.map(serializeExpression) });\n  },\n};\n"], "names": [], "mappings": ";;;;;;;AACA,SAAS,2BAA2B;AACpC,SAAS,eAAe;AACxB;AAaA,SAAS,mBAAmB;AAC5B,SAAgB,+BAA+B;;;;;;;;;;;;;;;AAExC,SAAS,wBACd,SAAA,EACgD;IAChD,OAAO,OACL,WACA,WACA,UACG;QACH,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,WAAW,GAAG,gBAAgB,WAAW;QACrD,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,WAAW,GAAG,gBAAgB,WAAW;QACrD,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,OAAO,GAAG,gBAAgB,OAAO;QAC7C,IACE,CAAC,MAAM,MAAA,IACP,CAAC,MAAM,OAAA,CAAQ,MAAM,MAAM,KAC3B,MAAM,MAAA,CAAO,MAAA,KAAW,GACxB;YACA,MAAM,MAAM,oDAAoD;QAClE;QAEA,OAAO,MAAM,IAAI,gBACf,WACA,YAAY,MAAM,WAClB,OACA,OAAA,CAAQ;IACZ;AACF;AAEO,MAAM,gBAAgB;IAM3B,YACE,SAAA,EACA,SAAA,EACA,KAAA,CACA;QATF,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QASN,IAAA,CAAK,SAAA,GAAY;QACjB,MAAM,UAAU,MAAM,MAAA,GAClB,oBAAoB,MAAM,MAAA,CAAO,iBAAiB,CAAC,IACnD;QAEJ,IAAA,CAAK,KAAA,GAAQ;YACX,MAAM;YACN,OAAO;gBACL;gBACA,OAAO,MAAM,KAAA;gBACb,QAAQ,MAAM,MAAA;gBACd,aAAa;YACf;QACF;IACF;IAEA,MAAM,UAA+B;QACnC,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,YAAY;YAClC,MAAM,IAAI,MAAM,sDAAsD;QACxE;QACA,MAAM,QAAQ,IAAA,CAAK,KAAA,CAAM,KAAA;QACzB,IAAA,CAAK,KAAA,GAAQ;YAAE,MAAM;QAAW;QAEhC,MAAM,EAAE,OAAA,CAAQ,CAAA,GAAI,MAAM,gMAAA,EAAoB,4BAA4B;YACxE,WAAW,IAAA,CAAK,SAAA;uKAChB,UAAA;YACA;QACF,CAAC;QACD,OAAO;IACT;AACF;AAaO,MAAM,uBAAuB,uLAAA,CAAsB;IAExD,YAAY,KAAA,CAAkB;QAC5B,KAAA,CAAM;QAFR,cAAA,IAAA,EAAQ;QAGN,IAAA,CAAK,KAAA,GAAQ;IACf;IAEA,YAAuB;QACrB,OAAO,IAAA,CAAK,KAAA;IACd;AACF;AAEO,SAAS,oBACd,IAAA,EACW;IACX,IAAI,gBAAgB,gBAAgB;QAClC,OAAO,KAAK,SAAA,CAAU;IACxB,OAAO;QAGL,OAAO;YAAE,0KAAU,0BAAA,EAAwB,IAAyB;QAAE;IACxE;AACF;AAEO,MAAM,oBAGT;IAAA,8EAAA;IAGF,IACE,SAAA,EACA,KAAA,EAC2B;QAC3B,IAAI,OAAO,cAAc,UAAU;YACjC,MAAM,IAAI,MAAM,oDAAoD;QACtE;QACA,OAAO,IAAI,eAAe;YACxB,KAAK;gBACH,oBAAoB,IAAI,eAAe;oBAAE,QAAQ;gBAAU,CAAC,CAAC;gBAC7D,oBAAoB,KAAK;aAC3B;QACF,CAAC;IACH;IAAA,8EAAA;IAIA,IAAA,GAAM,KAAA,EAAqE;QACzE,OAAO,IAAI,eAAe;YAAE,KAAK,MAAM,GAAA,CAAI,mBAAmB;QAAE,CAAC;IACnE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/authentication_impl.ts"], "sourcesContent": ["import { Auth } from \"../authentication.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\n\nexport function setupAuth(requestId: string): Auth {\n  return {\n    getUserIdentity: async () => {\n      return await performAsyncSyscall(\"1.0/getUserIdentity\", {\n        requestId,\n      });\n    },\n  };\n}\n"], "names": [], "mappings": ";;;AACA,SAAS,2BAA2B;;;AAE7B,SAAS,UAAU,SAAA,EAAyB;IACjD,OAAO;QACL,iBAAiB,YAAY;YAC3B,OAAO,gLAAM,sBAAA,EAAoB,uBAAuB;gBACtD;YACF,CAAC;QACH;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/filter_builder.ts"], "sourcesContent": ["import { Value, NumericValue } from \"../values/index.js\";\nimport {\n  DocumentByInfo,\n  FieldPaths,\n  FieldTypeFromFieldPath,\n  GenericTableInfo,\n} from \"./data_model.js\";\n\n/**\n * Expressions are evaluated to produce a {@link values.Value} in the course of executing a query.\n *\n * To construct an expression, use the {@link FilterBuilder} provided within\n * {@link OrderedQuery.filter}.\n *\n * @typeParam T - The type that this expression evaluates to.\n * @public\n */\nexport abstract class Expression<T extends Value | undefined> {\n  // Property for nominal type support.\n  private _isExpression: undefined;\n\n  // Property to distinguish expressions by the type they resolve to.\n  private _value!: T;\n\n  /**\n   * @internal\n   */\n  constructor() {\n    // only defining the constructor so we can mark it as internal and keep\n    // it out of the docs.\n  }\n}\n/**\n * An {@link Expression} or a constant {@link values.Value}\n *\n * @public\n */\nexport type ExpressionOrValue<T extends Value | undefined> = Expression<T> | T;\n\n/**\n * An interface for defining filters in queries.\n *\n * `FilterBuilder` has various methods that produce {@link Expression}s.\n * These expressions can be nested together along with constants to express\n * a filter predicate.\n *\n * `FilterBuilder` is used within {@link OrderedQuery.filter} to create query\n * filters.\n *\n * Here are the available methods:\n *\n * |                               |                                               |\n * |-------------------------------|-----------------------------------------------|\n * | **Comparisons**               | Error when `l` and `r` are not the same type. |\n * | [`eq(l, r)`](#eq)             | `l === r`                                     |\n * | [`neq(l, r)`](#neq)           | `l !== r`                                     |\n * | [`lt(l, r)`](#lt)             | `l < r`                                       |\n * | [`lte(l, r)`](#lte)           | `l <= r`                                      |\n * | [`gt(l, r)`](#gt)             | `l > r`                                       |\n * | [`gte(l, r)`](#gte)           | `l >= r`                                      |\n * |                               |                                               |\n * | **Arithmetic**                | Error when `l` and `r` are not the same type. |\n * | [`add(l, r)`](#add)           | `l + r`                                       |\n * | [`sub(l, r)`](#sub)           | `l - r`                                       |\n * | [`mul(l, r)`](#mul)           | `l * r`                                       |\n * | [`div(l, r)`](#div)           | `l / r`                                       |\n * | [`mod(l, r)`](#mod)           | `l % r`                                       |\n * | [`neg(x)`](#neg)              | `-x`                                          |\n * |                               |                                               |\n * | **Logic**                     | Error if any param is not a `bool`.           |\n * | [`not(x)`](#not)              | `!x`                                          |\n * | [`and(a, b, ..., z)`](#and)   | `a && b && ... && z`                          |\n * | [`or(a, b, ..., z)`](#or)     | <code>a &#124;&#124; b &#124;&#124; ... &#124;&#124; z</code> |\n * |                               |                                               |\n * | **Other**                     |                                               |\n * | [`field(fieldPath)`](#field)  | Evaluates to the field at `fieldPath`.        |\n * @public\n */\nexport interface FilterBuilder<TableInfo extends GenericTableInfo> {\n  //  Comparisons  /////////////////////////////////////////////////////////////\n\n  /**\n   * `l === r`\n   *\n   * @public\n   * */\n  eq<T extends Value | undefined>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  /**\n   * `l !== r`\n   *\n   * @public\n   * */\n  neq<T extends Value | undefined>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  /**\n   * `l < r`\n   *\n   * @public\n   */\n  lt<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  /**\n   * `l <= r`\n   *\n   * @public\n   */\n  lte<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  /**\n   * `l > r`\n   *\n   * @public\n   */\n  gt<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  /**\n   * `l >= r`\n   *\n   * @public\n   */\n  gte<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  //  Arithmetic  //////////////////////////////////////////////////////////////\n\n  /**\n   * `l + r`\n   *\n   * @public\n   */\n  add<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T>;\n\n  /**\n   * `l - r`\n   *\n   * @public\n   */\n  sub<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T>;\n\n  /**\n   * `l * r`\n   *\n   * @public\n   */\n  mul<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T>;\n\n  /**\n   * `l / r`\n   *\n   * @public\n   */\n  div<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T>;\n\n  /**\n   * `l % r`\n   *\n   * @public\n   */\n  mod<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T>;\n\n  /**\n   * `-x`\n   *\n   * @public\n   */\n  neg<T extends NumericValue>(x: ExpressionOrValue<T>): Expression<T>;\n\n  //  Logic  ///////////////////////////////////////////////////////////////////\n\n  /**\n   * `exprs[0] && exprs[1] && ... && exprs[n]`\n   *\n   * @public\n   */\n  and(...exprs: Array<ExpressionOrValue<boolean>>): Expression<boolean>;\n\n  /**\n   * `exprs[0] || exprs[1] || ... || exprs[n]`\n   *\n   * @public\n   */\n  or(...exprs: Array<ExpressionOrValue<boolean>>): Expression<boolean>;\n\n  /**\n   * `!x`\n   *\n   * @public\n   */\n  not(x: ExpressionOrValue<boolean>): Expression<boolean>;\n\n  //  Other  ///////////////////////////////////////////////////////////////////\n\n  /**\n   * Evaluates to the field at the given `fieldPath`.\n   *\n   * For example, in {@link OrderedQuery.filter} this can be used to examine the values being filtered.\n   *\n   * #### Example\n   *\n   * On this object:\n   * ```\n   * {\n   *   \"user\": {\n   *     \"isActive\": true\n   *   }\n   * }\n   * ```\n   *\n   * `field(\"user.isActive\")` evaluates to `true`.\n   *\n   * @public\n   */\n  field<FieldPath extends FieldPaths<TableInfo>>(\n    fieldPath: FieldPath,\n  ): Expression<FieldTypeFromFieldPath<DocumentByInfo<TableInfo>, FieldPath>>;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAiBO,MAAe,WAAwC;IAAA;;GAAA,GAU5D,aAAc;QARd,qCAAA;QAAA,cAAA,IAAA,EAAQ;QAGR,mEAAA;QAAA,cAAA,IAAA,EAAQ;IAQR;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/filter_builder_impl.ts"], "sourcesContent": ["import { JSONValue, Value, NumericValue } from \"../../values/index.js\";\nimport { convexOrUndefinedTo<PERSON><PERSON> } from \"../../values/value.js\";\nimport { GenericTableInfo } from \"../data_model.js\";\nimport {\n  Expression,\n  ExpressionOrValue,\n  FilterBuilder,\n} from \"../filter_builder.js\";\n\n// The `any` type parameter in `Expression<any>` allows us to use this class\n// in place of any `Expression` type in `filterBuilderImpl`.\nexport class ExpressionImpl extends Expression<any> {\n  private inner: JSONValue;\n  constructor(inner: JSONValue) {\n    super();\n    this.inner = inner;\n  }\n\n  serialize(): JSONValue {\n    return this.inner;\n  }\n}\n\nexport function serializeExpression(\n  expr: ExpressionOrValue<Value | undefined>,\n): JSONValue {\n  if (expr instanceof ExpressionImpl) {\n    return expr.serialize();\n  } else {\n    // Assume that the expression is a literal Convex value, which we'll serialize\n    // to its JSON representation.\n    return { $literal: convexOrUndefinedToJson(expr as Value | undefined) };\n  }\n}\n\nexport const filterBuilderImpl: FilterBuilder<GenericTableInfo> = {\n  //  Comparisons  /////////////////////////////////////////////////////////////\n\n  eq<T extends Value | undefined>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $eq: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  neq<T extends Value | undefined>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $neq: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  lt<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $lt: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  lte<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $lte: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  gt<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $gt: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  gte<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $gte: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  //  Arithmetic  //////////////////////////////////////////////////////////////\n\n  add<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $add: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  sub<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $sub: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  mul<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $mul: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  div<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $div: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  mod<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $mod: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  neg<T extends NumericValue>(x: ExpressionOrValue<T>): Expression<T> {\n    return new ExpressionImpl({ $neg: serializeExpression(x) });\n  },\n\n  //  Logic  ///////////////////////////////////////////////////////////////////\n\n  and(...exprs: Array<ExpressionOrValue<boolean>>): Expression<boolean> {\n    return new ExpressionImpl({ $and: exprs.map(serializeExpression) });\n  },\n\n  or(...exprs: Array<ExpressionOrValue<boolean>>): Expression<boolean> {\n    return new ExpressionImpl({ $or: exprs.map(serializeExpression) });\n  },\n\n  not(x: ExpressionOrValue<boolean>): Expression<boolean> {\n    return new ExpressionImpl({ $not: serializeExpression(x) });\n  },\n\n  //  Other  ///////////////////////////////////////////////////////////////////\n  field(fieldPath: string): Expression<any> {\n    return new ExpressionImpl({ $field: fieldPath });\n  },\n};\n"], "names": [], "mappings": ";;;;;AACA,SAAS,+BAA+B;AAExC;;;;;;;;;;;;AAQO,MAAM,4LAAuB,aAAA,CAAgB;IAElD,YAAY,KAAA,CAAkB;QAC5B,KAAA,CAAM;QAFR,cAAA,IAAA,EAAQ;QAGN,IAAA,CAAK,KAAA,GAAQ;IACf;IAEA,YAAuB;QACrB,OAAO,IAAA,CAAK,KAAA;IACd;AACF;AAEO,SAAS,oBACd,IAAA,EACW;IACX,IAAI,gBAAgB,gBAAgB;QAClC,OAAO,KAAK,SAAA,CAAU;IACxB,OAAO;QAGL,OAAO;YAAE,0KAAU,0BAAA,EAAwB,IAAyB;QAAE;IACxE;AACF;AAEO,MAAM,oBAAqD;IAAA,8EAAA;IAGhE,IACE,CAAA,EACA,CAAA,EACqB;QACrB,OAAO,IAAI,eAAe;YACxB,KAAK;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACtD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACqB;QACrB,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,IACE,CAAA,EACA,CAAA,EACqB;QACrB,OAAO,IAAI,eAAe;YACxB,KAAK;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACtD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACqB;QACrB,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,IACE,CAAA,EACA,CAAA,EACqB;QACrB,OAAO,IAAI,eAAe;YACxB,KAAK;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACtD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACqB;QACrB,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAAA,8EAAA;IAIA,KACE,CAAA,EACA,CAAA,EACe;QACf,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACe;QACf,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACe;QACf,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACe;QACf,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACe;QACf,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,KAA4B,CAAA,EAAwC;QAClE,OAAO,IAAI,eAAe;YAAE,MAAM,oBAAoB,CAAC;QAAE,CAAC;IAC5D;IAAA,8EAAA;IAIA,KAAA,GAAO,KAAA,EAA+D;QACpE,OAAO,IAAI,eAAe;YAAE,MAAM,MAAM,GAAA,CAAI,mBAAmB;QAAE,CAAC;IACpE;IAEA,IAAA,GAAM,KAAA,EAA+D;QACnE,OAAO,IAAI,eAAe;YAAE,KAAK,MAAM,GAAA,CAAI,mBAAmB;QAAE,CAAC;IACnE;IAEA,KAAI,CAAA,EAAoD;QACtD,OAAO,IAAI,eAAe;YAAE,MAAM,oBAAoB,CAAC;QAAE,CAAC;IAC5D;IAAA,8EAAA;IAGA,OAAM,SAAA,EAAoC;QACxC,OAAO,IAAI,eAAe;YAAE,QAAQ;QAAU,CAAC;IACjD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/index_range_builder.ts"], "sourcesContent": ["import {\n  GenericIndexFields,\n  GenericDocument,\n  FieldTypeFromFieldPath,\n} from \"./data_model.js\";\n\n/**\n * A type that adds 1 to a number literal type (up to 14).\n *\n * This is necessary to step through the fields in an index.\n */\ntype PlusOne<N extends number> = [\n  1,\n  2,\n  3,\n  4,\n  5,\n  6,\n  7,\n  8,\n  9,\n  10,\n  11,\n  12,\n  13,\n  14,\n  15,\n][N];\n\n/**\n * Builder to define an index range to query.\n *\n * An index range is a description of which documents Convex should consider\n * when running the query.\n *\n * An index range is always a chained list of:\n * 1. 0 or more equality expressions defined with `.eq`.\n * 2. [Optionally] A lower bound expression defined with `.gt` or `.gte`.\n * 3. [Optionally] An upper bound expression defined with `.lt` or `.lte`.\n *\n * **You must step through fields in index order.**\n *\n * Each equality expression must compare a different index field, starting from\n * the beginning and in order. The upper and lower bounds must follow the\n * equality expressions and compare the next field.\n *\n * For example, if there is an index of messages on\n * `[\"projectId\", \"priority\"]`, a range searching for \"messages in 'myProjectId'\n * with priority at least 100\" would look like:\n * ```ts\n * q.eq(\"projectId\", myProjectId)\n *  .gte(\"priority\", 100)\n * ```\n *\n * **The performance of your query is based on the specificity of the range.**\n *\n * This class is designed to only allow you to specify ranges that Convex can\n * efficiently use your index to find. For all other filtering use\n * {@link OrderedQuery.filter}.\n *\n * To learn about indexes, see [Indexes](https://docs.convex.dev/using/indexes).\n * @public\n */\nexport interface IndexRangeBuilder<\n  Document extends GenericDocument,\n  IndexFields extends GenericIndexFields,\n  FieldNum extends number = 0,\n> extends LowerBoundIndexRangeBuilder<Document, IndexFields[FieldNum]> {\n  /**\n   * Restrict this range to documents where `doc[fieldName] === value`.\n   *\n   * @param fieldName - The name of the field to compare. Must be the next field\n   * in the index.\n   * @param value - The value to compare against.\n   */\n  eq(\n    fieldName: IndexFields[FieldNum],\n    value: FieldTypeFromFieldPath<Document, IndexFields[FieldNum]>,\n  ): NextIndexRangeBuilder<Document, IndexFields, FieldNum>;\n}\n\n/**\n * An {@link IndexRangeBuilder} for the next field of the index.\n *\n * This type is careful to check if adding one to the `FieldNum` will exceed\n * the length of the `IndexFields`.\n */\ntype NextIndexRangeBuilder<\n  Document extends GenericDocument,\n  IndexFields extends GenericIndexFields,\n  FieldNum extends number,\n> =\n  PlusOne<FieldNum> extends IndexFields[\"length\"]\n    ? IndexRange\n    : IndexRangeBuilder<Document, IndexFields, PlusOne<FieldNum>>;\n\n/**\n * Builder to define the lower bound of an index range.\n *\n * See {@link IndexRangeBuilder}.\n *\n * @public\n */\nexport interface LowerBoundIndexRangeBuilder<\n  Document extends GenericDocument,\n  IndexFieldName extends string,\n> extends UpperBoundIndexRangeBuilder<Document, IndexFieldName> {\n  /**\n   * Restrict this range to documents where `doc[fieldName] > value`.\n   *\n   * @param fieldName - The name of the field to compare. Must be the next field\n   * in the index.\n   * @param value - The value to compare against.\n   */\n  gt(\n    fieldName: IndexFieldName,\n    value: FieldTypeFromFieldPath<Document, IndexFieldName>,\n  ): UpperBoundIndexRangeBuilder<Document, IndexFieldName>;\n  /**\n   * Restrict this range to documents where `doc[fieldName] >= value`.\n   *\n   * @param fieldName - The name of the field to compare. Must be the next field\n   * in the index.\n   * @param value - The value to compare against.\n   */\n  gte(\n    fieldName: IndexFieldName,\n    value: FieldTypeFromFieldPath<Document, IndexFieldName>,\n  ): UpperBoundIndexRangeBuilder<Document, IndexFieldName>;\n}\n\n/**\n * Builder to define the upper bound of an index range.\n *\n * See {@link IndexRangeBuilder}.\n *\n * @public\n */\nexport interface UpperBoundIndexRangeBuilder<\n  Document extends GenericDocument,\n  IndexFieldName extends string,\n> extends IndexRange {\n  /**\n   * Restrict this range to documents where `doc[fieldName] < value`.\n   *\n   * @param fieldName - The name of the field to compare. Must be the same index\n   * field used in the lower bound (`.gt` or `.gte`) or the next field if no\n   * lower bound was specified.\n   * @param value - The value to compare against.\n   */\n  lt(\n    fieldName: IndexFieldName,\n    value: FieldTypeFromFieldPath<Document, IndexFieldName>,\n  ): IndexRange;\n\n  /**\n   * Restrict this range to documents where `doc[fieldName] <= value`.\n   *\n   * @param fieldName - The name of the field to compare. Must be the same index\n   * field used in the lower bound (`.gt` or `.gte`) or the next field if no\n   * lower bound was specified.\n   * @param value - The value to compare against.\n   */\n  lte(\n    fieldName: IndexFieldName,\n    value: FieldTypeFromFieldPath<Document, IndexFieldName>,\n  ): IndexRange;\n}\n\n/**\n * An expression representing an index range created by\n * {@link IndexRangeBuilder}.\n * @public\n */\nexport abstract class IndexRange {\n  // Property for nominal type support.\n  private _isIndexRange: undefined;\n\n  /**\n   * @internal\n   */\n  constructor() {\n    // only defining the constructor so we can mark it as internal and keep\n    // it out of the docs.\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AA8KO,MAAe,WAAW;IAAA;;GAAA,GAO/B,aAAc;QALd,qCAAA;QAAA,cAAA,IAAA,EAAQ;IAQR;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 515, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/index_range_builder_impl.ts"], "sourcesContent": ["import { convexT<PERSON><PERSON><PERSON>, JSONValue, Value } from \"../../values/index.js\";\nimport { convexOrUndefinedToJson } from \"../../values/value.js\";\nimport { GenericDocument, GenericIndexFields } from \"../data_model.js\";\nimport {\n  IndexRange,\n  IndexRangeBuilder,\n  LowerBoundIndexRangeBuilder,\n  UpperBoundIndexRangeBuilder,\n} from \"../index_range_builder.js\";\n\nexport type SerializedRangeExpression = {\n  type: \"Eq\" | \"Gt\" | \"Gte\" | \"Lt\" | \"Lte\";\n  fieldPath: string;\n  value: JSONValue;\n};\n\nexport class IndexRangeBuilderImpl\n  extends IndexRange\n  implements\n    IndexRangeBuilder<GenericDocument, GenericIndexFields>,\n    LowerBoundIndexRangeBuilder<GenericDocument, string>,\n    UpperBoundIndexRangeBuilder<GenericDocument, string>\n{\n  private rangeExpressions: ReadonlyArray<SerializedRangeExpression>;\n  private isConsumed: boolean;\n  private constructor(\n    rangeExpressions: ReadonlyArray<SerializedRangeExpression>,\n  ) {\n    super();\n    this.rangeExpressions = rangeExpressions;\n    this.isConsumed = false;\n  }\n\n  static new(): IndexRangeBuilderImpl {\n    return new IndexRangeBuilderImpl([]);\n  }\n\n  private consume() {\n    if (this.isConsumed) {\n      throw new Error(\n        \"IndexRangeBuilder has already been used! Chain your method calls like `q => q.eq(...).eq(...)`. See https://docs.convex.dev/using/indexes\",\n      );\n    }\n    this.isConsumed = true;\n  }\n\n  eq(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Eq\",\n        fieldPath: fieldName,\n        value: convexOrUndefinedToJson(value),\n      }),\n    );\n  }\n\n  gt(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Gt\",\n        fieldPath: fieldName,\n        value: convexToJson(value),\n      }),\n    );\n  }\n  gte(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Gte\",\n        fieldPath: fieldName,\n        value: convexToJson(value),\n      }),\n    );\n  }\n  lt(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Lt\",\n        fieldPath: fieldName,\n        value: convexToJson(value),\n      }),\n    );\n  }\n  lte(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Lte\",\n        fieldPath: fieldName,\n        value: convexToJson(value),\n      }),\n    );\n  }\n\n  export() {\n    this.consume();\n    return this.rangeExpressions;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,oBAAsC;;AAG/C;;;;;;;;;;;;;AAaO,MAAM,uMACH,cAAA,CAKV;IAGU,YACN,gBAAA,CACA;QACA,KAAA,CAAM;QALR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QAKN,IAAA,CAAK,gBAAA,GAAmB;QACxB,IAAA,CAAK,UAAA,GAAa;IACpB;IAEA,OAAO,MAA6B;QAClC,OAAO,IAAI,sBAAsB,CAAC,CAAC;IACrC;IAEQ,UAAU;QAChB,IAAI,IAAA,CAAK,UAAA,EAAY;YACnB,MAAM,IAAI,MACR;QAEJ;QACA,IAAA,CAAK,UAAA,GAAa;IACpB;IAEA,GAAG,SAAA,EAAmB,KAAA,EAAc;QAClC,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,sBACT,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO;YAC3B,MAAM;YACN,WAAW;YACX,uKAAO,0BAAA,EAAwB,KAAK;QACtC,CAAC;IAEL;IAEA,GAAG,SAAA,EAAmB,KAAA,EAAc;QAClC,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,sBACT,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO;YAC3B,MAAM;YACN,WAAW;YACX,uKAAO,eAAA,EAAa,KAAK;QAC3B,CAAC;IAEL;IACA,IAAI,SAAA,EAAmB,KAAA,EAAc;QACnC,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,sBACT,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO;YAC3B,MAAM;YACN,WAAW;YACX,QAAO,8KAAA,EAAa,KAAK;QAC3B,CAAC;IAEL;IACA,GAAG,SAAA,EAAmB,KAAA,EAAc;QAClC,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,sBACT,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO;YAC3B,MAAM;YACN,WAAW;YACX,OAAO,+KAAA,EAAa,KAAK;QAC3B,CAAC;IAEL;IACA,IAAI,SAAA,EAAmB,KAAA,EAAc;QACnC,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,sBACT,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO;YAC3B,MAAM;YACN,WAAW;YACX,uKAAO,eAAA,EAAa,KAAK;QAC3B,CAAC;IAEL;IAEA,SAAS;QACP,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAA,CAAK,gBAAA;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 601, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/search_filter_builder.ts"], "sourcesContent": ["import {\n  FieldType<PERSON><PERSON><PERSON><PERSON>Path,\n  GenericDocument,\n  GenericSearchIndexConfig,\n} from \"./data_model.js\";\n\n/**\n * Builder for defining search filters.\n *\n * A search filter is a chained list of:\n * 1. One search expression constructed with `.search`.\n * 2. Zero or more equality expressions constructed with `.eq`.\n *\n * The search expression must search for text in the index's `searchField`. The\n * filter expressions can use any of the `filterFields` defined in the index.\n *\n * For all other filtering use {@link OrderedQuery.filter}.\n *\n * To learn about full text search, see [Indexes](https://docs.convex.dev/text-search).\n * @public\n */\nexport interface SearchFilterBuilder<\n  Document extends GenericDocument,\n  SearchIndexConfig extends GenericSearchIndexConfig,\n> {\n  /**\n   * Search for the terms in `query` within `doc[fieldName]`.\n   *\n   * This will do a full text search that returns results where any word of of\n   * `query` appears in the field.\n   *\n   * Documents will be returned based on their relevance to the query. This\n   * takes into account:\n   * - How many words in the query appear in the text?\n   * - How many times do they appear?\n   * - How long is the text field?\n   *\n   * @param fieldName - The name of the field to search in. This must be listed\n   * as the index's `searchField`.\n   * @param query - The query text to search for.\n   */\n  search(\n    fieldName: SearchIndexConfig[\"searchField\"],\n    query: string,\n  ): SearchFilterFinalizer<Document, SearchIndexConfig>;\n}\n\n/**\n * Builder to define equality expressions as part of a search filter.\n *\n * See {@link SearchFilterBuilder}.\n *\n * @public\n */\nexport interface SearchFilterFinalizer<\n  Document extends GenericDocument,\n  SearchIndexConfig extends GenericSearchIndexConfig,\n> extends SearchFilter {\n  /**\n   * Restrict this query to documents where `doc[fieldName] === value`.\n   *\n   * @param fieldName - The name of the field to compare. This must be listed in\n   * the search index's `filterFields`.\n   * @param value - The value to compare against.\n   */\n  eq<FieldName extends SearchIndexConfig[\"filterFields\"]>(\n    fieldName: FieldName,\n    value: FieldTypeFromFieldPath<Document, FieldName>,\n  ): SearchFilterFinalizer<Document, SearchIndexConfig>;\n}\n\n/**\n * An expression representing a search filter created by\n * {@link SearchFilterBuilder}.\n *\n * @public\n */\nexport abstract class SearchFilter {\n  // Property for nominal type support.\n  private _isSearchFilter: undefined;\n\n  /**\n   * @internal\n   */\n  constructor() {\n    // only defining the constructor so we can mark it as internal and keep\n    // it out of the docs.\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AA6EO,MAAe,aAAa;IAAA;;GAAA,GAOjC,aAAc;QALd,qCAAA;QAAA,cAAA,IAAA,EAAQ;IAQR;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 627, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/search_filter_builder_impl.ts"], "sourcesContent": ["import { JSONValue, convexOrUndefinedToJson } from \"../../values/value.js\";\nimport {\n  FieldTypeFromFieldPath,\n  GenericDocument,\n  GenericSearchIndexConfig,\n} from \"../data_model.js\";\nimport {\n  SearchFilter,\n  SearchFilterBuilder,\n  SearchFilterFinalizer,\n} from \"../search_filter_builder.js\";\nimport { validateArg } from \"./validate.js\";\n\nexport type SerializedSearchFilter =\n  | {\n      type: \"Search\";\n      fieldPath: string;\n      value: string;\n    }\n  | {\n      type: \"Eq\";\n      fieldPath: string;\n      value: JSONValue;\n    };\n\nexport class SearchFilterBuilderImpl\n  extends SearchFilter\n  implements\n    SearchFilterBuilder<GenericDocument, GenericSearchIndexConfig>,\n    SearchFilterFinalizer<GenericDocument, GenericSearchIndexConfig>\n{\n  private filters: ReadonlyArray<SerializedSearchFilter>;\n  private isConsumed: boolean;\n  private constructor(filters: ReadonlyArray<SerializedSearchFilter>) {\n    super();\n    this.filters = filters;\n    this.isConsumed = false;\n  }\n\n  static new(): SearchFilterBuilderImpl {\n    return new SearchFilterBuilderImpl([]);\n  }\n\n  private consume() {\n    if (this.isConsumed) {\n      throw new Error(\n        \"SearchFilterBuilder has already been used! Chain your method calls like `q => q.search(...).eq(...)`.\",\n      );\n    }\n    this.isConsumed = true;\n  }\n\n  search(\n    fieldName: string,\n    query: string,\n  ): SearchFilterFinalizer<GenericDocument, GenericSearchIndexConfig> {\n    validateArg(fieldName, 1, \"search\", \"fieldName\");\n    validateArg(query, 2, \"search\", \"query\");\n    this.consume();\n    return new SearchFilterBuilderImpl(\n      this.filters.concat({\n        type: \"Search\",\n        fieldPath: fieldName,\n        value: query,\n      }),\n    );\n  }\n  eq<FieldName extends string>(\n    fieldName: FieldName,\n    value: FieldTypeFromFieldPath<GenericDocument, FieldName>,\n  ): SearchFilterFinalizer<GenericDocument, GenericSearchIndexConfig> {\n    validateArg(fieldName, 1, \"eq\", \"fieldName\");\n    // when `undefined` is passed explicitly, it is allowed.\n    if (arguments.length !== 2) {\n      validateArg(value, 2, \"search\", \"value\");\n    }\n    this.consume();\n    return new SearchFilterBuilderImpl(\n      this.filters.concat({\n        type: \"Eq\",\n        fieldPath: fieldName,\n        value: convexOrUndefinedToJson(value),\n      }),\n    );\n  }\n\n  export() {\n    this.consume();\n    return this.filters;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,SAAoB,+BAA+B;AAMnD;AAKA,SAAS,mBAAmB;;;;;;;;;;;;;AAcrB,MAAM,4MACH,eAAA,CAIV;IAGU,YAAY,OAAA,CAAgD;QAClE,KAAA,CAAM;QAHR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QAGN,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,UAAA,GAAa;IACpB;IAEA,OAAO,MAA+B;QACpC,OAAO,IAAI,wBAAwB,CAAC,CAAC;IACvC;IAEQ,UAAU;QAChB,IAAI,IAAA,CAAK,UAAA,EAAY;YACnB,MAAM,IAAI,MACR;QAEJ;QACA,IAAA,CAAK,UAAA,GAAa;IACpB;IAEA,OACE,SAAA,EACA,KAAA,EACkE;QAClE,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,WAAW,GAAG,UAAU,WAAW;QAC/C,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,OAAO,GAAG,UAAU,OAAO;QACvC,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,wBACT,IAAA,CAAK,OAAA,CAAQ,MAAA,CAAO;YAClB,MAAM;YACN,WAAW;YACX,OAAO;QACT,CAAC;IAEL;IACA,GACE,SAAA,EACA,KAAA,EACkE;QAClE,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,WAAW,GAAG,MAAM,WAAW;QAE3C,IAAI,UAAU,MAAA,KAAW,GAAG;YAC1B,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,OAAO,GAAG,UAAU,OAAO;QACzC;QACA,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,wBACT,IAAA,CAAK,OAAA,CAAQ,MAAA,CAAO;YAClB,MAAM;YACN,WAAW;YACX,uKAAO,0BAAA,EAAwB,KAAK;QACtC,CAAC;IAEL;IAEA,SAAS;QACP,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAA,CAAK,OAAA;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 695, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/query_impl.ts"], "sourcesContent": ["import { Value, JSONValue, jsonToConvex } from \"../../values/index.js\";\nimport { PaginationResult, PaginationOptions } from \"../pagination.js\";\nimport { performAsyncSyscall, performSyscall } from \"./syscall.js\";\nimport {\n  filterBuilderImpl,\n  serializeExpression,\n} from \"./filter_builder_impl.js\";\nimport { Query, QueryInitializer } from \"../query.js\";\nimport { ExpressionOrValue, FilterBuilder } from \"../filter_builder.js\";\nimport { GenericTableInfo } from \"../data_model.js\";\nimport {\n  IndexRangeBuilderImpl,\n  SerializedRangeExpression,\n} from \"./index_range_builder_impl.js\";\nimport {\n  SearchFilterBuilderImpl,\n  SerializedSearchFilter,\n} from \"./search_filter_builder_impl.js\";\nimport { validateArg, validateArgIsNonNegativeInteger } from \"./validate.js\";\nimport { version } from \"../../index.js\";\n\nconst MAX_QUERY_OPERATORS = 256;\n\ntype QueryOperator = { filter: JSONValue } | { limit: number };\ntype Source =\n  | { type: \"FullTableScan\"; tableName: string; order: \"asc\" | \"desc\" | null }\n  | {\n      type: \"IndexRange\";\n      indexName: string;\n      range: ReadonlyArray<SerializedRangeExpression>;\n      order: \"asc\" | \"desc\" | null;\n    }\n  | {\n      type: \"Search\";\n      indexName: string;\n      filters: ReadonlyArray<SerializedSearchFilter>;\n    };\n\ntype SerializedQuery = {\n  source: Source;\n  operators: Array<QueryOperator>;\n};\n\nexport class QueryInitializerImpl\n  implements QueryInitializer<GenericTableInfo>\n{\n  private tableName: string;\n\n  constructor(tableName: string) {\n    this.tableName = tableName;\n  }\n\n  withIndex(\n    indexName: string,\n    indexRange?: (q: IndexRangeBuilderImpl) => IndexRangeBuilderImpl,\n  ): QueryImpl {\n    validateArg(indexName, 1, \"withIndex\", \"indexName\");\n    let rangeBuilder = IndexRangeBuilderImpl.new();\n    if (indexRange !== undefined) {\n      rangeBuilder = indexRange(rangeBuilder);\n    }\n    return new QueryImpl({\n      source: {\n        type: \"IndexRange\",\n        indexName: this.tableName + \".\" + indexName,\n        range: rangeBuilder.export(),\n        order: null,\n      },\n      operators: [],\n    });\n  }\n\n  withSearchIndex(\n    indexName: string,\n    searchFilter: (q: SearchFilterBuilderImpl) => SearchFilterBuilderImpl,\n  ): QueryImpl {\n    validateArg(indexName, 1, \"withSearchIndex\", \"indexName\");\n    validateArg(searchFilter, 2, \"withSearchIndex\", \"searchFilter\");\n    const searchFilterBuilder = SearchFilterBuilderImpl.new();\n    return new QueryImpl({\n      source: {\n        type: \"Search\",\n        indexName: this.tableName + \".\" + indexName,\n        filters: searchFilter(searchFilterBuilder).export(),\n      },\n      operators: [],\n    });\n  }\n\n  fullTableScan(): QueryImpl {\n    return new QueryImpl({\n      source: {\n        type: \"FullTableScan\",\n        tableName: this.tableName,\n        order: null,\n      },\n      operators: [],\n    });\n  }\n\n  order(order: \"asc\" | \"desc\"): QueryImpl {\n    return this.fullTableScan().order(order);\n  }\n\n  // This is internal API and should not be exposed to developers yet.\n  async count(): Promise<number> {\n    const syscallJSON = await performAsyncSyscall(\"1.0/count\", {\n      table: this.tableName,\n    });\n    const syscallResult = jsonToConvex(syscallJSON) as number;\n    return syscallResult;\n  }\n\n  filter(\n    predicate: (\n      q: FilterBuilder<GenericTableInfo>,\n    ) => ExpressionOrValue<boolean>,\n  ) {\n    return this.fullTableScan().filter(predicate);\n  }\n\n  limit(n: number) {\n    return this.fullTableScan().limit(n);\n  }\n\n  collect(): Promise<any[]> {\n    return this.fullTableScan().collect();\n  }\n\n  take(n: number): Promise<Array<any>> {\n    return this.fullTableScan().take(n);\n  }\n\n  paginate(paginationOpts: PaginationOptions): Promise<PaginationResult<any>> {\n    return this.fullTableScan().paginate(paginationOpts);\n  }\n\n  first(): Promise<any> {\n    return this.fullTableScan().first();\n  }\n\n  unique(): Promise<any> {\n    return this.fullTableScan().unique();\n  }\n\n  [Symbol.asyncIterator](): AsyncIterableIterator<any> {\n    return this.fullTableScan()[Symbol.asyncIterator]();\n  }\n}\n\n/**\n * @param type Whether the query was consumed or closed.\n * @throws An error indicating the query has been closed.\n */\nfunction throwClosedError(type: \"closed\" | \"consumed\"): never {\n  throw new Error(\n    type === \"consumed\"\n      ? \"This query is closed and can't emit any more values.\"\n      : \"This query has been chained with another operator and can't be reused.\",\n  );\n}\n\nexport class QueryImpl implements Query<GenericTableInfo> {\n  private state:\n    | { type: \"preparing\"; query: SerializedQuery }\n    | { type: \"executing\"; queryId: number }\n    | { type: \"closed\" }\n    | { type: \"consumed\" };\n\n  constructor(query: SerializedQuery) {\n    this.state = { type: \"preparing\", query };\n  }\n\n  private takeQuery(): SerializedQuery {\n    if (this.state.type !== \"preparing\") {\n      throw new Error(\n        \"A query can only be chained once and can't be chained after iteration begins.\",\n      );\n    }\n    const query = this.state.query;\n    this.state = { type: \"closed\" };\n    return query;\n  }\n\n  private startQuery(): number {\n    if (this.state.type === \"executing\") {\n      throw new Error(\"Iteration can only begin on a query once.\");\n    }\n    if (this.state.type === \"closed\" || this.state.type === \"consumed\") {\n      throwClosedError(this.state.type);\n    }\n    const query = this.state.query;\n    const { queryId } = performSyscall(\"1.0/queryStream\", { query, version });\n    this.state = { type: \"executing\", queryId };\n    return queryId;\n  }\n\n  private closeQuery() {\n    if (this.state.type === \"executing\") {\n      const queryId = this.state.queryId;\n      performSyscall(\"1.0/queryCleanup\", { queryId });\n    }\n    this.state = { type: \"consumed\" };\n  }\n\n  order(order: \"asc\" | \"desc\"): QueryImpl {\n    validateArg(order, 1, \"order\", \"order\");\n    const query = this.takeQuery();\n    if (query.source.type === \"Search\") {\n      throw new Error(\n        \"Search queries must always be in relevance order. Can not set order manually.\",\n      );\n    }\n    if (query.source.order !== null) {\n      throw new Error(\"Queries may only specify order at most once\");\n    }\n    query.source.order = order;\n    return new QueryImpl(query);\n  }\n\n  filter(\n    predicate: (\n      q: FilterBuilder<GenericTableInfo>,\n    ) => ExpressionOrValue<boolean>,\n  ): any {\n    validateArg(predicate, 1, \"filter\", \"predicate\");\n    const query = this.takeQuery();\n    if (query.operators.length >= MAX_QUERY_OPERATORS) {\n      throw new Error(\n        `Can't construct query with more than ${MAX_QUERY_OPERATORS} operators`,\n      );\n    }\n    query.operators.push({\n      filter: serializeExpression(predicate(filterBuilderImpl)),\n    });\n    return new QueryImpl(query);\n  }\n\n  limit(n: number): any {\n    validateArg(n, 1, \"limit\", \"n\");\n    const query = this.takeQuery();\n    query.operators.push({ limit: n });\n    return new QueryImpl(query);\n  }\n\n  [Symbol.asyncIterator](): AsyncIterableIterator<any> {\n    this.startQuery();\n    return this;\n  }\n\n  async next(): Promise<IteratorResult<any>> {\n    if (this.state.type === \"closed\" || this.state.type === \"consumed\") {\n      throwClosedError(this.state.type);\n    }\n    // Allow calling `.next()` when the query is in \"preparing\" state to implicitly start the\n    // query. This allows the developer to call `.next()` on the query without having to use\n    // a `for await` statement.\n    const queryId =\n      this.state.type === \"preparing\" ? this.startQuery() : this.state.queryId;\n    const { value, done } = await performAsyncSyscall(\"1.0/queryStreamNext\", {\n      queryId,\n    });\n    if (done) {\n      this.closeQuery();\n    }\n    const convexValue = jsonToConvex(value);\n    return { value: convexValue, done };\n  }\n\n  return() {\n    this.closeQuery();\n    return Promise.resolve({ done: true, value: undefined });\n  }\n\n  async paginate(\n    paginationOpts: PaginationOptions,\n  ): Promise<PaginationResult<any>> {\n    validateArg(paginationOpts, 1, \"paginate\", \"options\");\n    if (\n      typeof paginationOpts?.numItems !== \"number\" ||\n      paginationOpts.numItems < 0\n    ) {\n      throw new Error(\n        `\\`options.numItems\\` must be a positive number. Received \\`${paginationOpts?.numItems}\\`.`,\n      );\n    }\n    const query = this.takeQuery();\n    const pageSize = paginationOpts.numItems;\n    const cursor = paginationOpts.cursor;\n    const endCursor = paginationOpts?.endCursor ?? null;\n    const maximumRowsRead = paginationOpts.maximumRowsRead ?? null;\n    const { page, isDone, continueCursor, splitCursor, pageStatus } =\n      await performAsyncSyscall(\"1.0/queryPage\", {\n        query,\n        cursor,\n        endCursor,\n        pageSize,\n        maximumRowsRead,\n        maximumBytesRead: paginationOpts.maximumBytesRead,\n        version,\n      });\n    return {\n      page: page.map((json: string) => jsonToConvex(json)),\n      isDone,\n      continueCursor,\n      splitCursor,\n      pageStatus,\n    };\n  }\n\n  async collect(): Promise<Array<any>> {\n    const out: Value[] = [];\n    for await (const item of this) {\n      out.push(item);\n    }\n    return out;\n  }\n\n  async take(n: number): Promise<Array<any>> {\n    validateArg(n, 1, \"take\", \"n\");\n    validateArgIsNonNegativeInteger(n, 1, \"take\", \"n\");\n    return this.limit(n).collect();\n  }\n\n  async first(): Promise<any | null> {\n    const first_array = await this.take(1);\n    return first_array.length === 0 ? null : first_array[0];\n  }\n\n  async unique(): Promise<any | null> {\n    const first_two_array = await this.take(2);\n    if (first_two_array.length === 0) {\n      return null;\n    }\n    if (first_two_array.length === 2) {\n      throw new Error(`unique() query returned more than one result: \n [${first_two_array[0]._id}, ${first_two_array[1]._id}, ...]`);\n    }\n    return first_two_array[0];\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA,SAA2B,oBAAoB;AAE/C,SAAS,qBAAqB,sBAAsB;AACpD;AAOA;AAIA;AAIA,SAAS,aAAa,uCAAuC;AAC7D,SAAS,eAAe;;;;;;;;;;;;;;;;;AAExB,MAAM,sBAAsB;AAsBrB,MAAM,qBAEb;IAGE,YAAY,SAAA,CAAmB;QAF/B,cAAA,IAAA,EAAQ;QAGN,IAAA,CAAK,SAAA,GAAY;IACnB;IAEA,UACE,SAAA,EACA,UAAA,EACW;QACX,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,WAAW,GAAG,aAAa,WAAW;QAClD,IAAI,qMAAe,yBAAA,CAAsB,GAAA,CAAI;QAC7C,IAAI,eAAe,KAAA,GAAW;YAC5B,eAAe,WAAW,YAAY;QACxC;QACA,OAAO,IAAI,UAAU;YACnB,QAAQ;gBACN,MAAM;gBACN,WAAW,IAAA,CAAK,SAAA,GAAY,MAAM;gBAClC,OAAO,aAAa,MAAA,CAAO;gBAC3B,OAAO;YACT;YACA,WAAW,CAAC,CAAA;QACd,CAAC;IACH;IAEA,gBACE,SAAA,EACA,YAAA,EACW;QACX,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,WAAW,GAAG,mBAAmB,WAAW;QACxD,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,cAAc,GAAG,mBAAmB,cAAc;QAC9D,MAAM,+MAAsB,0BAAA,CAAwB,GAAA,CAAI;QACxD,OAAO,IAAI,UAAU;YACnB,QAAQ;gBACN,MAAM;gBACN,WAAW,IAAA,CAAK,SAAA,GAAY,MAAM;gBAClC,SAAS,aAAa,mBAAmB,EAAE,MAAA,CAAO;YACpD;YACA,WAAW,CAAC,CAAA;QACd,CAAC;IACH;IAEA,gBAA2B;QACzB,OAAO,IAAI,UAAU;YACnB,QAAQ;gBACN,MAAM;gBACN,WAAW,IAAA,CAAK,SAAA;gBAChB,OAAO;YACT;YACA,WAAW,CAAC,CAAA;QACd,CAAC;IACH;IAEA,MAAM,KAAA,EAAkC;QACtC,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,KAAA,CAAM,KAAK;IACzC;IAAA,oEAAA;IAGA,MAAM,QAAyB;QAC7B,MAAM,cAAc,+KAAM,uBAAA,EAAoB,aAAa;YACzD,OAAO,IAAA,CAAK,SAAA;QACd,CAAC;QACD,MAAM,gLAAgB,eAAA,EAAa,WAAW;QAC9C,OAAO;IACT;IAEA,OACE,SAAA,EAGA;QACA,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,MAAA,CAAO,SAAS;IAC9C;IAEA,MAAM,CAAA,EAAW;QACf,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,KAAA,CAAM,CAAC;IACrC;IAEA,UAA0B;QACxB,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,OAAA,CAAQ;IACtC;IAEA,KAAK,CAAA,EAAgC;QACnC,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,IAAA,CAAK,CAAC;IACpC;IAEA,SAAS,cAAA,EAAmE;QAC1E,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,QAAA,CAAS,cAAc;IACrD;IAEA,QAAsB;QACpB,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,KAAA,CAAM;IACpC;IAEA,SAAuB;QACrB,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,MAAA,CAAO;IACrC;IAEA,CAAC,OAAO,aAAa,CAAA,GAAgC;QACnD,OAAO,IAAA,CAAK,aAAA,CAAc,CAAA,CAAE,OAAO,aAAa,CAAA,CAAE;IACpD;AACF;AAMA,SAAS,iBAAiB,IAAA,EAAoC;IAC5D,MAAM,IAAI,MACR,SAAS,aACL,yDACA;AAER;AAEO,MAAM,UAA6C;IAOxD,YAAY,KAAA,CAAwB;QANpC,cAAA,IAAA,EAAQ;QAON,IAAA,CAAK,KAAA,GAAQ;YAAE,MAAM;YAAa;QAAM;IAC1C;IAEQ,YAA6B;QACnC,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,aAAa;YACnC,MAAM,IAAI,MACR;QAEJ;QACA,MAAM,QAAQ,IAAA,CAAK,KAAA,CAAM,KAAA;QACzB,IAAA,CAAK,KAAA,GAAQ;YAAE,MAAM;QAAS;QAC9B,OAAO;IACT;IAEQ,aAAqB;QAC3B,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,aAAa;YACnC,MAAM,IAAI,MAAM,2CAA2C;QAC7D;QACA,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,YAAY,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,YAAY;YAClE,iBAAiB,IAAA,CAAK,KAAA,CAAM,IAAI;QAClC;QACA,MAAM,QAAQ,IAAA,CAAK,KAAA,CAAM,KAAA;QACzB,MAAM,EAAE,OAAA,CAAQ,CAAA,6KAAI,iBAAA,EAAe,mBAAmB;YAAE;uKAAO,UAAA;QAAQ,CAAC;QACxE,IAAA,CAAK,KAAA,GAAQ;YAAE,MAAM;YAAa;QAAQ;QAC1C,OAAO;IACT;IAEQ,aAAa;QACnB,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,aAAa;YACnC,MAAM,UAAU,IAAA,CAAK,KAAA,CAAM,OAAA;YAC3B,CAAA,GAAA,qKAAA,CAAA,iBAAA,EAAe,oBAAoB;gBAAE;YAAQ,CAAC;QAChD;QACA,IAAA,CAAK,KAAA,GAAQ;YAAE,MAAM;QAAW;IAClC;IAEA,MAAM,KAAA,EAAkC;QACtC,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,OAAO,GAAG,SAAS,OAAO;QACtC,MAAM,QAAQ,IAAA,CAAK,SAAA,CAAU;QAC7B,IAAI,MAAM,MAAA,CAAO,IAAA,KAAS,UAAU;YAClC,MAAM,IAAI,MACR;QAEJ;QACA,IAAI,MAAM,MAAA,CAAO,KAAA,KAAU,MAAM;YAC/B,MAAM,IAAI,MAAM,6CAA6C;QAC/D;QACA,MAAM,MAAA,CAAO,KAAA,GAAQ;QACrB,OAAO,IAAI,UAAU,KAAK;IAC5B;IAEA,OACE,SAAA,EAGK;QACL,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,WAAW,GAAG,UAAU,WAAW;QAC/C,MAAM,QAAQ,IAAA,CAAK,SAAA,CAAU;QAC7B,IAAI,MAAM,SAAA,CAAU,MAAA,IAAU,qBAAqB;YACjD,MAAM,IAAI,MACR,CAAA,qCAAA,EAAwC,mBAAmB,CAAA,UAAA,CAAA;QAE/D;QACA,MAAM,SAAA,CAAU,IAAA,CAAK;YACnB,8LAAQ,sBAAA,EAAoB,UAAU,sMAAiB,CAAC;QAC1D,CAAC;QACD,OAAO,IAAI,UAAU,KAAK;IAC5B;IAEA,MAAM,CAAA,EAAgB;QACpB,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,GAAG,GAAG,SAAS,GAAG;QAC9B,MAAM,QAAQ,IAAA,CAAK,SAAA,CAAU;QAC7B,MAAM,SAAA,CAAU,IAAA,CAAK;YAAE,OAAO;QAAE,CAAC;QACjC,OAAO,IAAI,UAAU,KAAK;IAC5B;IAEA,CAAC,OAAO,aAAa,CAAA,GAAgC;QACnD,IAAA,CAAK,UAAA,CAAW;QAChB,OAAO,IAAA;IACT;IAEA,MAAM,OAAqC;QACzC,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,YAAY,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,YAAY;YAClE,iBAAiB,IAAA,CAAK,KAAA,CAAM,IAAI;QAClC;QAIA,MAAM,UACJ,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,cAAc,IAAA,CAAK,UAAA,CAAW,IAAI,IAAA,CAAK,KAAA,CAAM,OAAA;QACnE,MAAM,EAAE,KAAA,EAAO,IAAA,CAAK,CAAA,GAAI,gLAAM,sBAAA,EAAoB,uBAAuB;YACvE;QACF,CAAC;QACD,IAAI,MAAM;YACR,IAAA,CAAK,UAAA,CAAW;QAClB;QACA,MAAM,kBAAc,2KAAA,EAAa,KAAK;QACtC,OAAO;YAAE,OAAO;YAAa;QAAK;IACpC;IAEA,SAAS;QACP,IAAA,CAAK,UAAA,CAAW;QAChB,OAAO,QAAQ,OAAA,CAAQ;YAAE,MAAM;YAAM,OAAO,KAAA;QAAU,CAAC;IACzD;IAEA,MAAM,SACJ,cAAA,EACgC;QAChC,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,gBAAgB,GAAG,YAAY,SAAS;QACpD,IACE,OAAO,gBAAgB,aAAa,YACpC,eAAe,QAAA,GAAW,GAC1B;YACA,MAAM,IAAI,MACR,CAAA,2DAAA,EAA8D,gBAAgB,QAAQ,CAAA,GAAA,CAAA;QAE1F;QACA,MAAM,QAAQ,IAAA,CAAK,SAAA,CAAU;QAC7B,MAAM,WAAW,eAAe,QAAA;QAChC,MAAM,SAAS,eAAe,MAAA;QAC9B,MAAM,YAAY,gBAAgB,aAAa;QAC/C,MAAM,kBAAkB,eAAe,eAAA,IAAmB;QAC1D,MAAM,EAAE,IAAA,EAAM,MAAA,EAAQ,cAAA,EAAgB,WAAA,EAAa,UAAA,CAAW,CAAA,GAC5D,UAAM,4LAAA,EAAoB,iBAAiB;YACzC;YACA;YACA;YACA;YACA;YACA,kBAAkB,eAAe,gBAAA;uKACjC,UAAA;QACF,CAAC;QACH,OAAO;YACL,MAAM,KAAK,GAAA,CAAI,CAAC,QAAiB,8KAAA,EAAa,IAAI,CAAC;YACnD;YACA;YACA;YACA;QACF;IACF;IAEA,MAAM,UAA+B;QACnC,MAAM,MAAe,CAAC,CAAA;QACtB,WAAA,MAAiB,QAAQ,IAAA,CAAM;YAC7B,IAAI,IAAA,CAAK,IAAI;QACf;QACA,OAAO;IACT;IAEA,MAAM,KAAK,CAAA,EAAgC;QACzC,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,GAAG,GAAG,QAAQ,GAAG;QAC7B,CAAA,GAAA,sKAAA,CAAA,kCAAA,EAAgC,GAAG,GAAG,QAAQ,GAAG;QACjD,OAAO,IAAA,CAAK,KAAA,CAAM,CAAC,EAAE,OAAA,CAAQ;IAC/B;IAEA,MAAM,QAA6B;QACjC,MAAM,cAAc,MAAM,IAAA,CAAK,IAAA,CAAK,CAAC;QACrC,OAAO,YAAY,MAAA,KAAW,IAAI,OAAO,WAAA,CAAY,CAAC,CAAA;IACxD;IAEA,MAAM,SAA8B;QAClC,MAAM,kBAAkB,MAAM,IAAA,CAAK,IAAA,CAAK,CAAC;QACzC,IAAI,gBAAgB,MAAA,KAAW,GAAG;YAChC,OAAO;QACT;QACA,IAAI,gBAAgB,MAAA,KAAW,GAAG;YAChC,MAAM,IAAI,MAAM,CAAA;EAAA,EAClB,eAAA,CAAgB,CAAC,CAAA,CAAE,GAAG,CAAA,EAAA,EAAK,eAAA,CAAgB,CAAC,CAAA,CAAE,GAAG,CAAA,MAAA,CAAQ;QACzD;QACA,OAAO,eAAA,CAAgB,CAAC,CAAA;IAC1B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 974, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/database_impl.ts"], "sourcesContent": ["import {\n  convexTo<PERSON>son,\n  GenericId,\n  jsonToConvex,\n  Value,\n} from \"../../values/index.js\";\nimport { performAsyncSyscall, performSyscall } from \"./syscall.js\";\nimport {\n  GenericDatabaseReader,\n  GenericDatabaseReaderWithTable,\n  GenericDatabaseWriter,\n  GenericDatabaseWriterWithTable,\n} from \"../database.js\";\nimport { QueryInitializerImpl } from \"./query_impl.js\";\nimport { GenericDataModel, GenericDocument } from \"../data_model.js\";\nimport { validateArg } from \"./validate.js\";\nimport { version } from \"../../index.js\";\nimport { patchValueToJson } from \"../../values/value.js\";\n\nasync function get(id: GenericId<string>, isSystem: boolean) {\n  validateArg(id, 1, \"get\", \"id\");\n  if (typeof id !== \"string\") {\n    throw new Error(\n      `Invalid argument \\`id\\` for \\`db.get\\`, expected string but got '${typeof id}': ${\n        id as any\n      }`,\n    );\n  }\n  const args = {\n    id: convexToJson(id),\n    isSystem,\n    version,\n  };\n  const syscallJSON = await performAsyncSyscall(\"1.0/get\", args);\n\n  return jsonToConvex(syscallJSON) as GenericDocument;\n}\n\nexport function setupReader(): GenericDatabaseReader<GenericDataModel> {\n  const reader = (\n    isSystem = false,\n  ): GenericDatabaseReader<GenericDataModel> &\n    GenericDatabaseReaderWithTable<GenericDataModel> => {\n    return {\n      get: async (id: GenericId<string>) => {\n        return await get(id, isSystem);\n      },\n      query: (tableName: string) => {\n        return new TableReader(tableName, isSystem).query();\n      },\n      normalizeId: <TableName extends string>(\n        tableName: TableName,\n        id: string,\n      ): GenericId<TableName> | null => {\n        validateArg(tableName, 1, \"normalizeId\", \"tableName\");\n        validateArg(id, 2, \"normalizeId\", \"id\");\n        const accessingSystemTable = tableName.startsWith(\"_\");\n        if (accessingSystemTable !== isSystem) {\n          throw new Error(\n            `${\n              accessingSystemTable ? \"System\" : \"User\"\n            } tables can only be accessed from db.${\n              isSystem ? \"\" : \"system.\"\n            }normalizeId().`,\n          );\n        }\n        const syscallJSON = performSyscall(\"1.0/db/normalizeId\", {\n          table: tableName,\n          idString: id,\n        });\n        const syscallResult = jsonToConvex(syscallJSON) as any;\n        return syscallResult.id;\n      },\n      // We set the system reader on the next line\n      system: null as any,\n      table: (tableName) => {\n        return new TableReader(tableName, isSystem);\n      },\n    };\n  };\n  const { system: _, ...rest } = reader(true);\n  const r = reader();\n  r.system = rest as any;\n  return r;\n}\n\nasync function insert(tableName: string, value: any) {\n  if (tableName.startsWith(\"_\")) {\n    throw new Error(\"System tables (prefixed with `_`) are read-only.\");\n  }\n  validateArg(tableName, 1, \"insert\", \"table\");\n  validateArg(value, 2, \"insert\", \"value\");\n  const syscallJSON = await performAsyncSyscall(\"1.0/insert\", {\n    table: tableName,\n    value: convexToJson(value),\n  });\n  const syscallResult = jsonToConvex(syscallJSON) as any;\n  return syscallResult._id;\n}\n\nasync function patch(id: any, value: any) {\n  validateArg(id, 1, \"patch\", \"id\");\n  validateArg(value, 2, \"patch\", \"value\");\n  await performAsyncSyscall(\"1.0/shallowMerge\", {\n    id: convexToJson(id),\n    value: patchValueToJson(value as Value),\n  });\n}\n\nasync function replace(id: any, value: any) {\n  validateArg(id, 1, \"replace\", \"id\");\n  validateArg(value, 2, \"replace\", \"value\");\n  await performAsyncSyscall(\"1.0/replace\", {\n    id: convexToJson(id),\n    value: convexToJson(value),\n  });\n}\n\nasync function delete_(id: any) {\n  validateArg(id, 1, \"delete\", \"id\");\n  await performAsyncSyscall(\"1.0/remove\", { id: convexToJson(id) });\n}\n\nexport function setupWriter(): GenericDatabaseWriter<GenericDataModel> &\n  GenericDatabaseWriterWithTable<GenericDataModel> {\n  const reader = setupReader();\n  return {\n    get: reader.get,\n    query: reader.query,\n    normalizeId: reader.normalizeId,\n    system: reader.system as any,\n    insert: async (table, value) => {\n      return await insert(table, value);\n    },\n    patch: async (id, value) => {\n      return await patch(id, value);\n    },\n    replace: async (id, value) => {\n      return await replace(id, value);\n    },\n    delete: async (id) => {\n      return await delete_(id);\n    },\n    table: (tableName) => {\n      return new TableWriter(tableName, false);\n    },\n  };\n}\n\nclass TableReader {\n  constructor(\n    protected readonly tableName: string,\n    protected readonly isSystem: boolean,\n  ) {}\n\n  async get(id: GenericId<string>) {\n    return get(id, this.isSystem);\n  }\n\n  query() {\n    const accessingSystemTable = this.tableName.startsWith(\"_\");\n    if (accessingSystemTable !== this.isSystem) {\n      throw new Error(\n        `${\n          accessingSystemTable ? \"System\" : \"User\"\n        } tables can only be accessed from db.${\n          this.isSystem ? \"\" : \"system.\"\n        }query().`,\n      );\n    }\n    return new QueryInitializerImpl(this.tableName);\n  }\n}\n\nclass TableWriter extends TableReader {\n  async insert(value: any) {\n    return insert(this.tableName, value);\n  }\n  async patch(id: any, value: any) {\n    return patch(id, value);\n  }\n  async replace(id: any, value: any) {\n    return replace(id, value);\n  }\n  async delete(id: any) {\n    return delete_(id);\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAMA,SAAS,qBAAqB,sBAAsB;AAOpD,SAAS,4BAA4B;AAErC,SAAS,mBAAmB;AAC5B,SAAS,eAAe;;;;;;;;AAGxB,eAAe,IAAI,EAAA,EAAuB,QAAA,EAAmB;IAC3D,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,IAAI,GAAG,OAAO,IAAI;IAC9B,IAAI,OAAO,OAAO,UAAU;QAC1B,MAAM,IAAI,MACR,CAAA,iEAAA,EAAoE,OAAO,EAAE,CAAA,GAAA,EAC3E,EACF,EAAA;IAEJ;IACA,MAAM,OAAO;QACX,oKAAI,eAAA,EAAa,EAAE;QACnB;mKACA,UAAA;IACF;IACA,MAAM,cAAc,UAAM,4LAAA,EAAoB,WAAW,IAAI;IAE7D,uKAAO,eAAA,EAAa,WAAW;AACjC;AAEO,SAAS,cAAuD;IACrE,MAAM,SAAS,CACb,WAAW,KAAA,KAEyC;QACpD,OAAO;YACL,KAAK,OAAO,OAA0B;gBACpC,OAAO,MAAM,IAAI,IAAI,QAAQ;YAC/B;YACA,OAAO,CAAC,cAAsB;gBAC5B,OAAO,IAAI,YAAY,WAAW,QAAQ,EAAE,KAAA,CAAM;YACpD;YACA,aAAa,CACX,WACA,OACgC;gBAChC,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,WAAW,GAAG,eAAe,WAAW;gBACpD,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,IAAI,GAAG,eAAe,IAAI;gBACtC,MAAM,uBAAuB,UAAU,UAAA,CAAW,GAAG;gBACrD,IAAI,yBAAyB,UAAU;oBACrC,MAAM,IAAI,MACR,GACE,uBAAuB,WAAW,MACpC,CAAA,qCAAA,EACE,WAAW,KAAK,SAClB,CAAA,cAAA,CAAA;gBAEJ;gBACA,MAAM,cAAc,2LAAA,EAAe,sBAAsB;oBACvD,OAAO;oBACP,UAAU;gBACZ,CAAC;gBACD,MAAM,oBAAgB,2KAAA,EAAa,WAAW;gBAC9C,OAAO,cAAc,EAAA;YACvB;YAAA,4CAAA;YAEA,QAAQ;YACR,OAAO,CAAC,cAAc;gBACpB,OAAO,IAAI,YAAY,WAAW,QAAQ;YAC5C;QACF;IACF;IACA,MAAM,EAAE,QAAQ,CAAA,EAAG,GAAG,KAAK,CAAA,GAAI,OAAO,IAAI;IAC1C,MAAM,IAAI,OAAO;IACjB,EAAE,MAAA,GAAS;IACX,OAAO;AACT;AAEA,eAAe,OAAO,SAAA,EAAmB,KAAA,EAAY;IACnD,IAAI,UAAU,UAAA,CAAW,GAAG,GAAG;QAC7B,MAAM,IAAI,MAAM,kDAAkD;IACpE;IACA,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,WAAW,GAAG,UAAU,OAAO;IAC3C,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,OAAO,GAAG,UAAU,OAAO;IACvC,MAAM,cAAc,MAAM,gMAAA,EAAoB,cAAc;QAC1D,OAAO;QACP,uKAAO,eAAA,EAAa,KAAK;IAC3B,CAAC;IACD,MAAM,gBAAgB,+KAAA,EAAa,WAAW;IAC9C,OAAO,cAAc,GAAA;AACvB;AAEA,eAAe,MAAM,EAAA,EAAS,KAAA,EAAY;IACxC,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,IAAI,GAAG,SAAS,IAAI;IAChC,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,OAAO,GAAG,SAAS,OAAO;IACtC,MAAM,gMAAA,EAAoB,oBAAoB;QAC5C,oKAAI,eAAA,EAAa,EAAE;QACnB,uKAAO,mBAAA,EAAiB,KAAc;IACxC,CAAC;AACH;AAEA,eAAe,QAAQ,EAAA,EAAS,KAAA,EAAY;IAC1C,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,IAAI,GAAG,WAAW,IAAI;IAClC,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,OAAO,GAAG,WAAW,OAAO;IACxC,gLAAM,sBAAA,EAAoB,eAAe;QACvC,oKAAI,eAAA,EAAa,EAAE;QACnB,uKAAO,eAAA,EAAa,KAAK;IAC3B,CAAC;AACH;AAEA,eAAe,QAAQ,EAAA,EAAS;IAC9B,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,IAAI,GAAG,UAAU,IAAI;IACjC,MAAM,gMAAA,EAAoB,cAAc;QAAE,oKAAI,eAAA,EAAa,EAAE;IAAE,CAAC;AAClE;AAEO,SAAS,cACmC;IACjD,MAAM,SAAS,YAAY;IAC3B,OAAO;QACL,KAAK,OAAO,GAAA;QACZ,OAAO,OAAO,KAAA;QACd,aAAa,OAAO,WAAA;QACpB,QAAQ,OAAO,MAAA;QACf,QAAQ,OAAO,OAAO,UAAU;YAC9B,OAAO,MAAM,OAAO,OAAO,KAAK;QAClC;QACA,OAAO,OAAO,IAAI,UAAU;YAC1B,OAAO,MAAM,MAAM,IAAI,KAAK;QAC9B;QACA,SAAS,OAAO,IAAI,UAAU;YAC5B,OAAO,MAAM,QAAQ,IAAI,KAAK;QAChC;QACA,QAAQ,OAAO,OAAO;YACpB,OAAO,MAAM,QAAQ,EAAE;QACzB;QACA,OAAO,CAAC,cAAc;YACpB,OAAO,IAAI,YAAY,WAAW,KAAK;QACzC;IACF;AACF;AAEA,MAAM,YAAY;IAChB,YACqB,SAAA,EACA,QAAA,CACnB;QAFmB,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,QAAA,GAAA;IAClB;IAEH,MAAM,IAAI,EAAA,EAAuB;QAC/B,OAAO,IAAI,IAAI,IAAA,CAAK,QAAQ;IAC9B;IAEA,QAAQ;QACN,MAAM,uBAAuB,IAAA,CAAK,SAAA,CAAU,UAAA,CAAW,GAAG;QAC1D,IAAI,yBAAyB,IAAA,CAAK,QAAA,EAAU;YAC1C,MAAM,IAAI,MACR,GACE,uBAAuB,WAAW,MACpC,CAAA,qCAAA,EACE,IAAA,CAAK,QAAA,GAAW,KAAK,SACvB,CAAA,QAAA,CAAA;QAEJ;QACA,OAAO,6KAAI,uBAAA,CAAqB,IAAA,CAAK,SAAS;IAChD;AACF;AAEA,MAAM,oBAAoB,YAAY;IACpC,MAAM,OAAO,KAAA,EAAY;QACvB,OAAO,OAAO,IAAA,CAAK,SAAA,EAAW,KAAK;IACrC;IACA,MAAM,MAAM,EAAA,EAAS,KAAA,EAAY;QAC/B,OAAO,MAAM,IAAI,KAAK;IACxB;IACA,MAAM,QAAQ,EAAA,EAAS,KAAA,EAAY;QACjC,OAAO,QAAQ,IAAI,KAAK;IAC1B;IACA,MAAM,OAAO,EAAA,EAAS;QACpB,OAAO,QAAQ,EAAE;IACnB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1134, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/scheduler_impl.ts"], "sourcesContent": ["import { convexToJson, Value } from \"../../values/index.js\";\nimport { version } from \"../../index.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\nimport { parseArgs } from \"../../common/index.js\";\nimport { SchedulableFunctionReference, Scheduler } from \"../scheduler.js\";\nimport { Id } from \"../../values/value.js\";\nimport { validateArg } from \"./validate.js\";\nimport { getFunctionAddress } from \"../components/paths.js\";\n\nexport function setupMutationScheduler(): Scheduler {\n  return {\n    runAfter: async (\n      delayMs: number,\n      functionReference: SchedulableFunctionReference,\n      args?: Record<string, Value>,\n    ) => {\n      const syscallArgs = runAfterSyscallArgs(delayMs, functionReference, args);\n      return await performAsyncSyscall(\"1.0/schedule\", syscallArgs);\n    },\n    runAt: async (\n      ms_since_epoch_or_date: number | Date,\n      functionReference: SchedulableFunctionReference,\n      args?: Record<string, Value>,\n    ) => {\n      const syscallArgs = runAtSyscallArgs(\n        ms_since_epoch_or_date,\n        functionReference,\n        args,\n      );\n      return await performAsyncSyscall(\"1.0/schedule\", syscallArgs);\n    },\n    cancel: async (id: Id<\"_scheduled_functions\">) => {\n      validateArg(id, 1, \"cancel\", \"id\");\n      const args = { id: convexToJson(id) };\n      await performAsyncSyscall(\"1.0/cancel_job\", args);\n    },\n  };\n}\n\nexport function setupActionScheduler(requestId: string): Scheduler {\n  return {\n    runAfter: async (\n      delayMs: number,\n      functionReference: SchedulableFunctionReference,\n      args?: Record<string, Value>,\n    ) => {\n      const syscallArgs = {\n        requestId,\n        ...runAfterSyscallArgs(delayMs, functionReference, args),\n      };\n      return await performAsyncSyscall(\"1.0/actions/schedule\", syscallArgs);\n    },\n    runAt: async (\n      ms_since_epoch_or_date: number | Date,\n      functionReference: SchedulableFunctionReference,\n      args?: Record<string, Value>,\n    ) => {\n      const syscallArgs = {\n        requestId,\n        ...runAtSyscallArgs(ms_since_epoch_or_date, functionReference, args),\n      };\n      return await performAsyncSyscall(\"1.0/actions/schedule\", syscallArgs);\n    },\n    cancel: async (id: Id<\"_scheduled_functions\">) => {\n      validateArg(id, 1, \"cancel\", \"id\");\n      const syscallArgs = { id: convexToJson(id) };\n      return await performAsyncSyscall(\"1.0/actions/cancel_job\", syscallArgs);\n    },\n  };\n}\n\nfunction runAfterSyscallArgs(\n  delayMs: number,\n  functionReference: SchedulableFunctionReference,\n  args?: Record<string, Value>,\n) {\n  if (typeof delayMs !== \"number\") {\n    throw new Error(\"`delayMs` must be a number\");\n  }\n  if (!isFinite(delayMs)) {\n    throw new Error(\"`delayMs` must be a finite number\");\n  }\n  if (delayMs < 0) {\n    throw new Error(\"`delayMs` must be non-negative\");\n  }\n  const functionArgs = parseArgs(args);\n  const address = getFunctionAddress(functionReference);\n  // Note the syscall expects a unix timestamp, measured in seconds.\n  const ts = (Date.now() + delayMs) / 1000.0;\n  return {\n    ...address,\n    ts,\n    args: convexToJson(functionArgs),\n    version,\n  };\n}\n\nfunction runAtSyscallArgs(\n  ms_since_epoch_or_date: number | Date,\n  functionReference: SchedulableFunctionReference,\n  args?: Record<string, Value>,\n) {\n  let ts;\n  if (ms_since_epoch_or_date instanceof Date) {\n    ts = ms_since_epoch_or_date.valueOf() / 1000.0;\n  } else if (typeof ms_since_epoch_or_date === \"number\") {\n    // The timestamp the developer passes is in milliseconds, while the syscall\n    // accepts seconds since the epoch.\n    ts = ms_since_epoch_or_date / 1000;\n  } else {\n    throw new Error(\"The invoke time must a Date or a timestamp\");\n  }\n  const address = getFunctionAddress(functionReference);\n  const functionArgs = parseArgs(args);\n  return {\n    ...address,\n    ts,\n    args: convexToJson(functionArgs),\n    version,\n  };\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,oBAA2B;;AACpC,SAAS,eAAe;AACxB,SAAS,2BAA2B;AACpC,SAAS,iBAAiB;AAG1B,SAAS,mBAAmB;AAC5B,SAAS,0BAA0B;;;;;;;;AAE5B,SAAS,yBAAoC;IAClD,OAAO;QACL,UAAU,OACR,SACA,mBACA,SACG;YACH,MAAM,cAAc,oBAAoB,SAAS,mBAAmB,IAAI;YACxE,OAAO,gLAAM,sBAAA,EAAoB,gBAAgB,WAAW;QAC9D;QACA,OAAO,OACL,wBACA,mBACA,SACG;YACH,MAAM,cAAc,iBAClB,wBACA,mBACA;YAEF,OAAO,gLAAM,sBAAA,EAAoB,gBAAgB,WAAW;QAC9D;QACA,QAAQ,OAAO,OAAmC;YAChD,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,IAAI,GAAG,UAAU,IAAI;YACjC,MAAM,OAAO;gBAAE,oKAAI,eAAA,EAAa,EAAE;YAAE;YACpC,MAAM,gMAAA,EAAoB,kBAAkB,IAAI;QAClD;IACF;AACF;AAEO,SAAS,qBAAqB,SAAA,EAA8B;IACjE,OAAO;QACL,UAAU,OACR,SACA,mBACA,SACG;YACH,MAAM,cAAc;gBAClB;gBACA,GAAG,oBAAoB,SAAS,mBAAmB,IAAI,CAAA;YACzD;YACA,OAAO,gLAAM,sBAAA,EAAoB,wBAAwB,WAAW;QACtE;QACA,OAAO,OACL,wBACA,mBACA,SACG;YACH,MAAM,cAAc;gBAClB;gBACA,GAAG,iBAAiB,wBAAwB,mBAAmB,IAAI,CAAA;YACrE;YACA,OAAO,gLAAM,sBAAA,EAAoB,wBAAwB,WAAW;QACtE;QACA,QAAQ,OAAO,OAAmC;YAChD,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,IAAI,GAAG,UAAU,IAAI;YACjC,MAAM,cAAc;gBAAE,oKAAI,eAAA,EAAa,EAAE;YAAE;YAC3C,OAAO,gLAAM,sBAAA,EAAoB,0BAA0B,WAAW;QACxE;IACF;AACF;AAEA,SAAS,oBACP,OAAA,EACA,iBAAA,EACA,IAAA,EACA;IACA,IAAI,OAAO,YAAY,UAAU;QAC/B,MAAM,IAAI,MAAM,4BAA4B;IAC9C;IACA,IAAI,CAAC,SAAS,OAAO,GAAG;QACtB,MAAM,IAAI,MAAM,mCAAmC;IACrD;IACA,IAAI,UAAU,GAAG;QACf,MAAM,IAAI,MAAM,gCAAgC;IAClD;IACA,MAAM,+KAAe,YAAA,EAAU,IAAI;IACnC,MAAM,WAAU,kMAAA,EAAmB,iBAAiB;IAEpD,MAAM,KAAA,CAAM,KAAK,GAAA,CAAI,IAAI,OAAA,IAAW;IACpC,OAAO;QACL,GAAG,OAAA;QACH;QACA,sKAAM,eAAA,EAAa,YAAY;mKAC/B,UAAA;IACF;AACF;AAEA,SAAS,iBACP,sBAAA,EACA,iBAAA,EACA,IAAA,EACA;IACA,IAAI;IACJ,IAAI,kCAAkC,MAAM;QAC1C,KAAK,uBAAuB,OAAA,CAAQ,IAAI;IAC1C,OAAA,IAAW,OAAO,2BAA2B,UAAU;QAGrD,KAAK,yBAAyB;IAChC,OAAO;QACL,MAAM,IAAI,MAAM,4CAA4C;IAC9D;IACA,MAAM,wLAAU,qBAAA,EAAmB,iBAAiB;IACpD,MAAM,+KAAe,YAAA,EAAU,IAAI;IACnC,OAAO;QACL,GAAG,OAAA;QACH;QACA,sKAAM,eAAA,EAAa,YAAY;mKAC/B,UAAA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1240, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/storage_impl.ts"], "sourcesContent": ["import {\n  FileMetadata,\n  StorageActionWriter,\n  FileStorageId,\n  StorageReader,\n  StorageWriter,\n} from \"../storage.js\";\nimport { version } from \"../../index.js\";\nimport { performAsyncSyscall, performJsSyscall } from \"./syscall.js\";\nimport { validateArg } from \"./validate.js\";\n\nexport function setupStorageReader(requestId: string): StorageReader {\n  return {\n    getUrl: async (storageId: FileStorageId) => {\n      validateArg(storageId, 1, \"getUrl\", \"storageId\");\n      return await performAsyncSyscall(\"1.0/storageGetUrl\", {\n        requestId,\n        version,\n        storageId,\n      });\n    },\n    getMetadata: async (storageId: FileStorageId): Promise<FileMetadata> => {\n      return await performAsyncSyscall(\"1.0/storageGetMetadata\", {\n        requestId,\n        version,\n        storageId,\n      });\n    },\n  };\n}\n\nexport function setupStorageWriter(requestId: string): StorageWriter {\n  const reader = setupStorageReader(requestId);\n  return {\n    generateUploadUrl: async () => {\n      return await performAsyncSyscall(\"1.0/storageGenerateUploadUrl\", {\n        requestId,\n        version,\n      });\n    },\n    delete: async (storageId: FileStorageId) => {\n      await performAsyncSyscall(\"1.0/storageDelete\", {\n        requestId,\n        version,\n        storageId,\n      });\n    },\n    getUrl: reader.getUrl,\n    getMetadata: reader.getMetadata,\n  };\n}\n\nexport function setupStorageActionWriter(\n  requestId: string,\n): StorageActionWriter {\n  const writer = setupStorageWriter(requestId);\n  return {\n    ...writer,\n    store: async (blob: Blob, options?: { sha256?: string }) => {\n      return await performJsSyscall(\"storage/storeBlob\", {\n        requestId,\n        version,\n        blob,\n        options,\n      });\n    },\n    get: async (storageId: FileStorageId) => {\n      return await performJsSyscall(\"storage/getBlob\", {\n        requestId,\n        version,\n        storageId,\n      });\n    },\n  };\n}\n"], "names": [], "mappings": ";;;;;AAOA,SAAS,eAAe;AACxB,SAAS,qBAAqB,wBAAwB;AACtD,SAAS,mBAAmB;;;;;AAErB,SAAS,mBAAmB,SAAA,EAAkC;IACnE,OAAO;QACL,QAAQ,OAAO,cAA6B;YAC1C,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,WAAW,GAAG,UAAU,WAAW;YAC/C,OAAO,UAAM,4LAAA,EAAoB,qBAAqB;gBACpD;2KACA,UAAA;gBACA;YACF,CAAC;QACH;QACA,aAAa,OAAO,cAAoD;YACtE,OAAO,gLAAM,sBAAA,EAAoB,0BAA0B;gBACzD;gBACA,qKAAA;gBACA;YACF,CAAC;QACH;IACF;AACF;AAEO,SAAS,mBAAmB,SAAA,EAAkC;IACnE,MAAM,SAAS,mBAAmB,SAAS;IAC3C,OAAO;QACL,mBAAmB,YAAY;YAC7B,OAAO,gLAAM,sBAAA,EAAoB,gCAAgC;gBAC/D;2KACA,UAAA;YACF,CAAC;QACH;QACA,QAAQ,OAAO,cAA6B;YAC1C,OAAM,+LAAA,EAAoB,qBAAqB;gBAC7C;2KACA,UAAA;gBACA;YACF,CAAC;QACH;QACA,QAAQ,OAAO,MAAA;QACf,aAAa,OAAO,WAAA;IACtB;AACF;AAEO,SAAS,yBACd,SAAA,EACqB;IACrB,MAAM,SAAS,mBAAmB,SAAS;IAC3C,OAAO;QACL,GAAG,MAAA;QACH,OAAO,OAAO,MAAY,YAAkC;YAC1D,OAAO,OAAM,4LAAA,EAAiB,qBAAqB;gBACjD;2KACA,UAAA;gBACA;gBACA;YACF,CAAC;QACH;QACA,KAAK,OAAO,cAA6B;YACvC,OAAO,gLAAM,mBAAA,EAAiB,mBAAmB;gBAC/C;2KACA,UAAA;gBACA;YACF,CAAC;QACH;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1318, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/registration_impl.ts"], "sourcesContent": ["import {\n  ConvexError,\n  convexTo<PERSON>son,\n  GenericValidator,\n  jsonToConvex,\n  v,\n  Validator,\n  Value,\n} from \"../../values/index.js\";\nimport { GenericDataModel } from \"../data_model.js\";\nimport {\n  ActionBuilder,\n  DefaultFunctionArgs,\n  GenericActionCtx,\n  GenericMutationCtx,\n  GenericQueryCtx,\n  MutationBuilder,\n  PublicHttpAction,\n  QueryBuilder,\n  RegisteredAction,\n  RegisteredMutation,\n  RegisteredQuery,\n} from \"../registration.js\";\nimport { setupActionCalls } from \"./actions_impl.js\";\nimport { setupActionVectorSearch } from \"./vector_search_impl.js\";\nimport { setupAuth } from \"./authentication_impl.js\";\nimport { setupReader, setupWriter } from \"./database_impl.js\";\nimport { QueryImpl, QueryInitializerImpl } from \"./query_impl.js\";\nimport {\n  setupActionScheduler,\n  setupMutationScheduler,\n} from \"./scheduler_impl.js\";\nimport {\n  setupStorageActionWriter,\n  setupStorageReader,\n  setupStorageWriter,\n} from \"./storage_impl.js\";\nimport { parseArgs } from \"../../common/index.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\nimport { asObjectValidator } from \"../../values/validator.js\";\nimport { getFunctionAddress } from \"../components/paths.js\";\n\nasync function invokeMutation<\n  F extends (ctx: GenericMutationCtx<GenericDataModel>, ...args: any) => any,\n>(func: F, argsStr: string) {\n  // TODO(presley): Change the function signature and propagate the requestId from Rust.\n  // Ok, to mock it out for now, since queries are only running in V8.\n  const requestId = \"\";\n  const args = jsonToConvex(JSON.parse(argsStr));\n  const mutationCtx = {\n    db: setupWriter(),\n    auth: setupAuth(requestId),\n    storage: setupStorageWriter(requestId),\n    scheduler: setupMutationScheduler(),\n\n    runQuery: (reference: any, args?: any) => runUdf(\"query\", reference, args),\n    runMutation: (reference: any, args?: any) =>\n      runUdf(\"mutation\", reference, args),\n  };\n  const result = await invokeFunction(func, mutationCtx, args as any);\n  validateReturnValue(result);\n  return JSON.stringify(convexToJson(result === undefined ? null : result));\n}\n\nexport function validateReturnValue(v: any) {\n  if (v instanceof QueryInitializerImpl || v instanceof QueryImpl) {\n    throw new Error(\n      \"Return value is a Query. Results must be retrieved with `.collect()`, `.take(n), `.unique()`, or `.first()`.\",\n    );\n  }\n}\n\nexport async function invokeFunction<\n  Ctx,\n  Args extends any[],\n  F extends (ctx: Ctx, ...args: Args) => any,\n>(func: F, ctx: Ctx, args: Args) {\n  let result;\n  try {\n    result = await Promise.resolve(func(ctx, ...args));\n  } catch (thrown: unknown) {\n    throw serializeConvexErrorData(thrown);\n  }\n  return result;\n}\n\nfunction dontCallDirectly(\n  funcType: string,\n  handler: (ctx: any, args: any) => any,\n): unknown {\n  return (ctx: any, args: any) => {\n    globalThis.console.warn(\n      \"Convex functions should not directly call other Convex functions. Consider calling a helper function instead. \" +\n        `e.g. \\`export const foo = ${funcType}(...); await foo(ctx);\\` is not supported. ` +\n        \"See https://docs.convex.dev/production/best-practices/#use-helper-functions-to-write-shared-code\",\n    );\n    return handler(ctx, args);\n  };\n}\n\n// Keep in sync with node executor\nfunction serializeConvexErrorData(thrown: unknown) {\n  if (\n    typeof thrown === \"object\" &&\n    thrown !== null &&\n    Symbol.for(\"ConvexError\") in thrown\n  ) {\n    const error = thrown as ConvexError<any>;\n    error.data = JSON.stringify(\n      convexToJson(error.data === undefined ? null : error.data),\n    );\n    (error as any).ConvexErrorSymbol = Symbol.for(\"ConvexError\");\n    return error;\n  } else {\n    return thrown;\n  }\n}\n\n/**\n * Guard against Convex functions accidentally getting included in a browser bundle.\n * Convex functions may include secret logic or credentials that should not be\n * send to untrusted clients (browsers).\n */\nfunction assertNotBrowser() {\n  if (\n    typeof window === \"undefined\" ||\n    !(window as any).__convexAllowFunctionsInBrowser\n  ) {\n    return;\n  }\n  // JSDom doesn't count, developers are allowed to use JSDom in Convex functions.\n  const isRealBrowser =\n    Object.getOwnPropertyDescriptor(globalThis, \"window\")\n      ?.get?.toString()\n      .includes(\"[native code]\") ?? false;\n  if (isRealBrowser) {\n    throw new Error(\"Convex functions should not be imported in the browser.\");\n  }\n}\n\ntype FunctionDefinition =\n  | ((ctx: any, args: DefaultFunctionArgs) => any)\n  | {\n      args?: GenericValidator | Record<string, GenericValidator>;\n      returns?: GenericValidator | Record<string, GenericValidator>;\n      handler: (ctx: any, args: DefaultFunctionArgs) => any;\n    };\n\nfunction exportArgs(functionDefinition: FunctionDefinition) {\n  return () => {\n    let args: GenericValidator = v.any();\n    if (\n      typeof functionDefinition === \"object\" &&\n      functionDefinition.args !== undefined\n    ) {\n      args = asObjectValidator(functionDefinition.args);\n    }\n    return JSON.stringify(args.json);\n  };\n}\n\nfunction exportReturns(functionDefinition: FunctionDefinition) {\n  return () => {\n    let returns: Validator<any, any, any> | undefined;\n    if (\n      typeof functionDefinition === \"object\" &&\n      functionDefinition.returns !== undefined\n    ) {\n      returns = asObjectValidator(functionDefinition.returns);\n    }\n    return JSON.stringify(returns ? returns.json : null);\n  };\n}\n\n/**\n * Define a mutation in this Convex app's public API.\n *\n * This function will be allowed to modify your Convex database and will be accessible from the client.\n *\n * If you're using code generation, use the `mutation` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The mutation function. It receives a {@link GenericMutationCtx} as its first argument.\n * @returns The wrapped mutation. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const mutationGeneric: MutationBuilder<any, \"public\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericMutationCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"mutation\", handler) as RegisteredMutation<\n    \"public\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isMutation = true;\n  func.isPublic = true;\n  func.invokeMutation = (argsStr) => invokeMutation(handler, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as MutationBuilder<any, \"public\">;\n\n/**\n * Define a mutation that is only accessible from other Convex functions (but not from the client).\n *\n * This function will be allowed to modify your Convex database. It will not be accessible from the client.\n *\n * If you're using code generation, use the `internalMutation` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The mutation function. It receives a {@link GenericMutationCtx} as its first argument.\n * @returns The wrapped mutation. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const internalMutationGeneric: MutationBuilder<any, \"internal\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericMutationCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\n    \"internalMutation\",\n    handler,\n  ) as RegisteredMutation<\"internal\", any, any>;\n\n  assertNotBrowser();\n  func.isMutation = true;\n  func.isInternal = true;\n  func.invokeMutation = (argsStr) => invokeMutation(handler, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as MutationBuilder<any, \"internal\">;\n\nasync function invokeQuery<\n  F extends (ctx: GenericQueryCtx<GenericDataModel>, ...args: any) => any,\n>(func: F, argsStr: string) {\n  // TODO(presley): Change the function signature and propagate the requestId from Rust.\n  // Ok, to mock it out for now, since queries are only running in V8.\n  const requestId = \"\";\n  const args = jsonToConvex(JSON.parse(argsStr));\n  const queryCtx = {\n    db: setupReader(),\n    auth: setupAuth(requestId),\n    storage: setupStorageReader(requestId),\n    runQuery: (reference: any, args?: any) => runUdf(\"query\", reference, args),\n  };\n  const result = await invokeFunction(func, queryCtx, args as any);\n  validateReturnValue(result);\n  return JSON.stringify(convexToJson(result === undefined ? null : result));\n}\n\n/**\n * Define a query in this Convex app's public API.\n *\n * This function will be allowed to read your Convex database and will be accessible from the client.\n *\n * If you're using code generation, use the `query` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The query function. It receives a {@link GenericQueryCtx} as its first argument.\n * @returns The wrapped query. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const queryGeneric: QueryBuilder<any, \"public\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericQueryCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"query\", handler) as RegisteredQuery<\n    \"public\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isQuery = true;\n  func.isPublic = true;\n  func.invokeQuery = (argsStr) => invokeQuery(handler, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as QueryBuilder<any, \"public\">;\n\n/**\n * Define a query that is only accessible from other Convex functions (but not from the client).\n *\n * This function will be allowed to read from your Convex database. It will not be accessible from the client.\n *\n * If you're using code generation, use the `internalQuery` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The query function. It receives a {@link GenericQueryCtx} as its first argument.\n * @returns The wrapped query. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const internalQueryGeneric: QueryBuilder<any, \"internal\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericQueryCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"internalQuery\", handler) as RegisteredQuery<\n    \"internal\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isQuery = true;\n  func.isInternal = true;\n  func.invokeQuery = (argsStr) => invokeQuery(handler as any, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as QueryBuilder<any, \"internal\">;\n\nasync function invokeAction<\n  F extends (ctx: GenericActionCtx<GenericDataModel>, ...args: any) => any,\n>(func: F, requestId: string, argsStr: string) {\n  const args = jsonToConvex(JSON.parse(argsStr));\n  const calls = setupActionCalls(requestId);\n  const ctx = {\n    ...calls,\n    auth: setupAuth(requestId),\n    scheduler: setupActionScheduler(requestId),\n    storage: setupStorageActionWriter(requestId),\n    vectorSearch: setupActionVectorSearch(requestId) as any,\n  };\n  const result = await invokeFunction(func, ctx, args as any);\n  return JSON.stringify(convexToJson(result === undefined ? null : result));\n}\n\n/**\n * Define an action in this Convex app's public API.\n *\n * If you're using code generation, use the `action` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The function. It receives a {@link GenericActionCtx} as its first argument.\n * @returns The wrapped function. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const actionGeneric: ActionBuilder<any, \"public\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericActionCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"action\", handler) as RegisteredAction<\n    \"public\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isAction = true;\n  func.isPublic = true;\n  func.invokeAction = (requestId, argsStr) =>\n    invokeAction(handler, requestId, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as ActionBuilder<any, \"public\">;\n\n/**\n * Define an action that is only accessible from other Convex functions (but not from the client).\n *\n * If you're using code generation, use the `internalAction` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The function. It receives a {@link GenericActionCtx} as its first argument.\n * @returns The wrapped function. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const internalActionGeneric: ActionBuilder<any, \"internal\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericActionCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"internalAction\", handler) as RegisteredAction<\n    \"internal\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isAction = true;\n  func.isInternal = true;\n  func.invokeAction = (requestId, argsStr) =>\n    invokeAction(handler, requestId, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as ActionBuilder<any, \"internal\">;\n\nasync function invokeHttpAction<\n  F extends (ctx: GenericActionCtx<GenericDataModel>, request: Request) => any,\n>(func: F, request: Request) {\n  // TODO(presley): Change the function signature and propagate the requestId from Rust.\n  // Ok, to mock it out for now, since http endpoints are only running in V8.\n  const requestId = \"\";\n  const calls = setupActionCalls(requestId);\n  const ctx = {\n    ...calls,\n    auth: setupAuth(requestId),\n    storage: setupStorageActionWriter(requestId),\n    scheduler: setupActionScheduler(requestId),\n    vectorSearch: setupActionVectorSearch(requestId) as any,\n  };\n  return await invokeFunction(func, ctx, [request]);\n}\n\n/**\n * Define a Convex HTTP action.\n *\n * @param func - The function. It receives an {@link GenericActionCtx} as its first argument, and a `Request` object\n * as its second.\n * @returns The wrapped function. Route a URL path to this function in `convex/http.js`.\n *\n * @public\n */\nexport const httpActionGeneric = (\n  func: (\n    ctx: GenericActionCtx<GenericDataModel>,\n    request: Request,\n  ) => Promise<Response>,\n): PublicHttpAction => {\n  const q = dontCallDirectly(\"httpAction\", func) as PublicHttpAction;\n  assertNotBrowser();\n  q.isHttp = true;\n  q.invokeHttpAction = (request) => invokeHttpAction(func as any, request);\n  q._handler = func;\n  return q;\n};\n\nasync function runUdf(\n  udfType: \"query\" | \"mutation\",\n  f: any,\n  args?: Record<string, Value>,\n): Promise<any> {\n  const queryArgs = parseArgs(args);\n  const syscallArgs = {\n    udfType,\n    args: convexToJson(queryArgs),\n    ...getFunctionAddress(f),\n  };\n  const result = await performAsyncSyscall(\"1.0/runUdf\", syscallArgs);\n  return jsonToConvex(result);\n}\n"], "names": ["args", "v"], "mappings": ";;;;;;;;;;;;AAAA;;AAuBA,SAAS,wBAAwB;AACjC,SAAS,+BAA+B;AACxC,SAAS,iBAAiB;AAC1B,SAAS,aAAa,mBAAmB;AACzC,SAAS,WAAW,4BAA4B;AAChD;AAIA;AAKA,SAAS,iBAAiB;AAC1B,SAAS,2BAA2B;AAEpC,SAAS,0BAA0B;;;;;;;;;;;;;;AAEnC,eAAe,eAEb,IAAA,EAAS,OAAA,EAAiB;IAG1B,MAAM,YAAY;IAClB,MAAM,uKAAO,eAAA,EAAa,KAAK,KAAA,CAAM,OAAO,CAAC;IAC7C,MAAM,cAAc;QAClB,oLAAI,cAAA,CAAY;QAChB,4LAAM,YAAA,EAAU,SAAS;QACzB,uLAAS,sBAAA,EAAmB,SAAS;QACrC,4LAAW,yBAAA,CAAuB;QAElC,UAAU,CAAC,WAAgBA,QAAe,OAAO,SAAS,WAAWA,KAAI;QACzE,aAAa,CAAC,WAAgBA,QAC5B,OAAO,YAAY,WAAWA,KAAI;IACtC;IACA,MAAM,SAAS,MAAM,eAAe,MAAM,aAAa,IAAW;IAClE,oBAAoB,MAAM;IAC1B,OAAO,KAAK,SAAA,gKAAU,gBAAA,EAAa,WAAW,KAAA,IAAY,OAAO,MAAM,CAAC;AAC1E;AAEO,SAAS,oBAAoBC,EAAAA,EAAQ;IAC1C,IAAIA,uLAAa,uBAAA,IAAwBA,uLAAa,YAAA,EAAW;QAC/D,MAAM,IAAI,MACR;IAEJ;AACF;AAEA,eAAsB,eAIpB,IAAA,EAAS,GAAA,EAAU,IAAA,EAAY;IAC/B,IAAI;IACJ,IAAI;QACF,SAAS,MAAM,QAAQ,OAAA,CAAQ,KAAK,KAAK,GAAG,IAAI,CAAC;IACnD,EAAA,OAAS,QAAiB;QACxB,MAAM,yBAAyB,MAAM;IACvC;IACA,OAAO;AACT;AAEA,SAAS,iBACP,QAAA,EACA,OAAA,EACS;IACT,OAAO,CAAC,KAAU,SAAc;QAC9B,WAAW,OAAA,CAAQ,IAAA,CACjB,CAAA,wIAAA,EAC+B,QAAQ,CAAA,2IAAA,CAAA;QAGzC,OAAO,QAAQ,KAAK,IAAI;IAC1B;AACF;AAGA,SAAS,yBAAyB,MAAA,EAAiB;IACjD,IACE,OAAO,WAAW,YAClB,WAAW,QACX,OAAO,GAAA,CAAI,aAAa,KAAK,QAC7B;QACA,MAAM,QAAQ;QACd,MAAM,IAAA,GAAO,KAAK,SAAA,iKAChB,eAAA,EAAa,MAAM,IAAA,KAAS,KAAA,IAAY,OAAO,MAAM,IAAI;QAE1D,MAAc,iBAAA,GAAoB,OAAO,GAAA,CAAI,aAAa;QAC3D,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAOA,SAAS,mBAAmB;IAC1B,IACE,OAAO,WAAW,eAClB,CAAE,OAAe,+BAAA,EACjB;QACA;IACF;IAEA,MAAM,gBACJ,OAAO,wBAAA,CAAyB,YAAY,QAAQ,GAChD,KAAK,SAAS,EACf,SAAS,eAAe,KAAK;IAClC,IAAI,eAAe;QACjB,MAAM,IAAI,MAAM,yDAAyD;IAC3E;AACF;AAUA,SAAS,WAAW,kBAAA,EAAwC;IAC1D,OAAO,MAAM;QACX,IAAI,uKAAyB,IAAA,CAAE,GAAA,CAAI;QACnC,IACE,OAAO,uBAAuB,YAC9B,mBAAmB,IAAA,KAAS,KAAA,GAC5B;YACA,2KAAO,oBAAA,EAAkB,mBAAmB,IAAI;QAClD;QACA,OAAO,KAAK,SAAA,CAAU,KAAK,IAAI;IACjC;AACF;AAEA,SAAS,cAAc,kBAAA,EAAwC;IAC7D,OAAO,MAAM;QACX,IAAI;QACJ,IACE,OAAO,uBAAuB,YAC9B,mBAAmB,OAAA,KAAY,KAAA,GAC/B;YACA,8KAAU,oBAAA,EAAkB,mBAAmB,OAAO;QACxD;QACA,OAAO,KAAK,SAAA,CAAU,UAAU,QAAQ,IAAA,GAAO,IAAI;IACrD;AACF;AAeO,MAAM,kBAAmD,CAC9D,uBACG;IACH,MAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB,OAAA;IAEzB,MAAM,OAAO,iBAAiB,YAAY,OAAO;IAMjD,iBAAiB;IACjB,KAAK,UAAA,GAAa;IAClB,KAAK,QAAA,GAAW;IAChB,KAAK,cAAA,GAAiB,CAAC,UAAY,eAAe,SAAS,OAAO;IAClE,KAAK,UAAA,GAAa,WAAW,kBAAkB;IAC/C,KAAK,aAAA,GAAgB,cAAc,kBAAkB;IACrD,KAAK,QAAA,GAAW;IAChB,OAAO;AACT;AAeO,MAAM,0BAA6D,CACxE,uBACG;IACH,MAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB,OAAA;IAEzB,MAAM,OAAO,iBACX,oBACA;IAGF,iBAAiB;IACjB,KAAK,UAAA,GAAa;IAClB,KAAK,UAAA,GAAa;IAClB,KAAK,cAAA,GAAiB,CAAC,UAAY,eAAe,SAAS,OAAO;IAClE,KAAK,UAAA,GAAa,WAAW,kBAAkB;IAC/C,KAAK,aAAA,GAAgB,cAAc,kBAAkB;IACrD,KAAK,QAAA,GAAW;IAChB,OAAO;AACT;AAEA,eAAe,YAEb,IAAA,EAAS,OAAA,EAAiB;IAG1B,MAAM,YAAY;IAClB,MAAM,uKAAO,eAAA,EAAa,KAAK,KAAA,CAAM,OAAO,CAAC;IAC7C,MAAM,WAAW;QACf,KAAI,6LAAA,CAAY;QAChB,4LAAM,YAAA,EAAU,SAAS;QACzB,wLAAS,qBAAA,EAAmB,SAAS;QACrC,UAAU,CAAC,WAAgBD,QAAe,OAAO,SAAS,WAAWA,KAAI;IAC3E;IACA,MAAM,SAAS,MAAM,eAAe,MAAM,UAAU,IAAW;IAC/D,oBAAoB,MAAM;IAC1B,OAAO,KAAK,SAAA,iKAAU,eAAA,EAAa,WAAW,KAAA,IAAY,OAAO,MAAM,CAAC;AAC1E;AAeO,MAAM,eAA6C,CACxD,uBACG;IACH,MAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB,OAAA;IAEzB,MAAM,OAAO,iBAAiB,SAAS,OAAO;IAM9C,iBAAiB;IACjB,KAAK,OAAA,GAAU;IACf,KAAK,QAAA,GAAW;IAChB,KAAK,WAAA,GAAc,CAAC,UAAY,YAAY,SAAS,OAAO;IAC5D,KAAK,UAAA,GAAa,WAAW,kBAAkB;IAC/C,KAAK,aAAA,GAAgB,cAAc,kBAAkB;IACrD,KAAK,QAAA,GAAW;IAChB,OAAO;AACT;AAeO,MAAM,uBAAuD,CAClE,uBACG;IACH,MAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB,OAAA;IAEzB,MAAM,OAAO,iBAAiB,iBAAiB,OAAO;IAMtD,iBAAiB;IACjB,KAAK,OAAA,GAAU;IACf,KAAK,UAAA,GAAa;IAClB,KAAK,WAAA,GAAc,CAAC,UAAY,YAAY,SAAgB,OAAO;IACnE,KAAK,UAAA,GAAa,WAAW,kBAAkB;IAC/C,KAAK,aAAA,GAAgB,cAAc,kBAAkB;IACrD,KAAK,QAAA,GAAW;IAChB,OAAO;AACT;AAEA,eAAe,aAEb,IAAA,EAAS,SAAA,EAAmB,OAAA,EAAiB;IAC7C,MAAM,uKAAO,eAAA,EAAa,KAAK,KAAA,CAAM,OAAO,CAAC;IAC7C,MAAM,YAAQ,8LAAA,EAAiB,SAAS;IACxC,MAAM,MAAM;QACV,GAAG,KAAA;QACH,4LAAM,YAAA,EAAU,SAAS;QACzB,4LAAW,uBAAA,EAAqB,SAAS;QACzC,UAAS,yMAAA,EAAyB,SAAS;QAC3C,mMAAc,0BAAA,EAAwB,SAAS;IACjD;IACA,MAAM,SAAS,MAAM,eAAe,MAAM,KAAK,IAAW;IAC1D,OAAO,KAAK,SAAA,CAAU,+KAAA,EAAa,WAAW,KAAA,IAAY,OAAO,MAAM,CAAC;AAC1E;AAaO,MAAM,gBAA+C,CAC1D,uBACG;IACH,MAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB,OAAA;IAEzB,MAAM,OAAO,iBAAiB,UAAU,OAAO;IAM/C,iBAAiB;IACjB,KAAK,QAAA,GAAW;IAChB,KAAK,QAAA,GAAW;IAChB,KAAK,YAAA,GAAe,CAAC,WAAW,UAC9B,aAAa,SAAS,WAAW,OAAO;IAC1C,KAAK,UAAA,GAAa,WAAW,kBAAkB;IAC/C,KAAK,aAAA,GAAgB,cAAc,kBAAkB;IACrD,KAAK,QAAA,GAAW;IAChB,OAAO;AACT;AAaO,MAAM,wBAAyD,CACpE,uBACG;IACH,MAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB,OAAA;IAEzB,MAAM,OAAO,iBAAiB,kBAAkB,OAAO;IAMvD,iBAAiB;IACjB,KAAK,QAAA,GAAW;IAChB,KAAK,UAAA,GAAa;IAClB,KAAK,YAAA,GAAe,CAAC,WAAW,UAC9B,aAAa,SAAS,WAAW,OAAO;IAC1C,KAAK,UAAA,GAAa,WAAW,kBAAkB;IAC/C,KAAK,aAAA,GAAgB,cAAc,kBAAkB;IACrD,KAAK,QAAA,GAAW;IAChB,OAAO;AACT;AAEA,eAAe,iBAEb,IAAA,EAAS,OAAA,EAAkB;IAG3B,MAAM,YAAY;IAClB,MAAM,uLAAQ,mBAAA,EAAiB,SAAS;IACxC,MAAM,MAAM;QACV,GAAG,KAAA;QACH,OAAM,iMAAA,EAAU,SAAS;QACzB,wLAAS,2BAAA,EAAyB,SAAS;QAC3C,4LAAW,uBAAA,EAAqB,SAAS;QACzC,cAAc,+MAAA,EAAwB,SAAS;IACjD;IACA,OAAO,MAAM,eAAe,MAAM,KAAK;QAAC,OAAO;KAAC;AAClD;AAWO,MAAM,oBAAoB,CAC/B,SAIqB;IACrB,MAAM,IAAI,iBAAiB,cAAc,IAAI;IAC7C,iBAAiB;IACjB,EAAE,MAAA,GAAS;IACX,EAAE,gBAAA,GAAmB,CAAC,UAAY,iBAAiB,MAAa,OAAO;IACvE,EAAE,QAAA,GAAW;IACb,OAAO;AACT;AAEA,eAAe,OACb,OAAA,EACA,CAAA,EACA,IAAA,EACc;IACd,MAAM,4KAAY,YAAA,EAAU,IAAI;IAChC,MAAM,cAAc;QAClB;QACA,OAAM,8KAAA,EAAa,SAAS;QAC5B,iLAAG,qBAAA,EAAmB,CAAC,CAAA;IACzB;IACA,MAAM,SAAS,gLAAM,sBAAA,EAAoB,cAAc,WAAW;IAClE,uKAAO,eAAA,EAAa,MAAM;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1563, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/pagination.ts"], "sourcesContent": ["import { v } from \"../values/validator.js\";\n\n/**\n * An opaque identifier used for paginating a database query.\n *\n * Cursors are returned from {@link OrderedQuery.paginate} and represent the\n * point of the query where the page of results ended.\n *\n * To continue paginating, pass the cursor back into\n * {@link OrderedQuery.paginate} in the {@link PaginationOptions} object to\n * fetch another page of results.\n *\n * Note: Cursors can only be passed to _exactly_ the same database query that\n * they were generated from. You may not reuse a cursor between different\n * database queries.\n *\n * @public\n */\nexport type Cursor = string;\n\n/**\n * The result of paginating using {@link OrderedQuery.paginate}.\n *\n * @public\n */\nexport interface PaginationResult<T> {\n  /**\n   * The page of results.\n   */\n  page: T[];\n\n  /**\n   * Have we reached the end of the results?\n   */\n  isDone: boolean;\n\n  /**\n   * A {@link Cursor} to continue loading more results.\n   */\n  continueCursor: Cursor;\n\n  /**\n   * A {@link Cursor} to split the page into two, so the page from\n   * (cursor, continueCursor] can be replaced by two pages (cursor, splitCursor]\n   * and (splitCursor, continueCursor].\n   */\n  splitCursor?: Cursor | null;\n\n  /**\n   * When a query reads too much data, it may return 'SplitRecommended' to\n   * indicate that the page should be split into two with `splitCursor`.\n   * When a query reads so much data that `page` might be incomplete, its status\n   * becomes 'SplitRequired'.\n   */\n  pageStatus?: \"SplitRecommended\" | \"SplitRequired\" | null;\n}\n\n/**\n * The options passed to {@link OrderedQuery.paginate}.\n *\n * To use this type in [argument validation](https://docs.convex.dev/functions/validation),\n * use the {@link paginationOptsValidator}.\n *\n * @public\n */\nexport interface PaginationOptions {\n  /**\n   * Number of items to load in this page of results.\n   *\n   * Note: This is only an initial value!\n   *\n   * If you are running this paginated query in a reactive query function, you\n   * may receive more or less items than this if items were added to or removed\n   * from the query range.\n   */\n  numItems: number;\n\n  /**\n   * A {@link Cursor} representing the start of this page or `null` to start\n   * at the beginning of the query results.\n   */\n  cursor: Cursor | null;\n\n  /**\n   * A {@link Cursor} representing the end of this page or `null | undefined` to\n   * use `numItems` instead.\n   *\n   * @internal\n   */\n  endCursor?: Cursor | null;\n\n  /**\n   * The maximum number of rows that should be read from the database.\n   *\n   * This option is different from `numItems` in that it controls the number of rows entering a query's\n   * pipeline, where `numItems` controls the number of rows coming out. For example, a `filter`\n   * may disqualify most of the rows coming in, so setting a low `numItems` would not help\n   * bound its execution time. Instead, set a low `maximumRowsRead` to efficiently paginate\n   * through the filter.\n   *\n   * Currently this is not enforced for search queries.\n   *\n   * @internal\n   */\n  maximumRowsRead?: number;\n\n  /**\n   * The maximum number of bytes that should be read from the database.\n   *\n   * As with {@link PaginationOptions.maximumRowsRead}, this affects the number\n   * of rows entering a query's pipeline.\n   *\n   * Once a paginated query hits its bytes read budget, an incomplete page\n   * will be returned.\n   *\n   * Currently this is not enforced for search queries.\n   *\n   * @internal\n   */\n  maximumBytesRead?: number;\n}\n\n/**\n * A {@link values.Validator} for {@link PaginationOptions}.\n *\n * This includes the standard {@link PaginationOptions} properties along with\n * an optional cache-busting `id` property used by {@link react.usePaginatedQuery}.\n *\n * @public\n */\nexport const paginationOptsValidator = v.object({\n  numItems: v.number(),\n  cursor: v.union(v.string(), v.null()),\n  endCursor: v.optional(v.union(v.string(), v.null())),\n  id: v.optional(v.number()),\n  maximumRowsRead: v.optional(v.number()),\n  maximumBytesRead: v.optional(v.number()),\n});\n"], "names": [], "mappings": ";;;AAAA,SAAS,SAAS;;;AAkIX,MAAM,0LAA0B,IAAA,CAAE,MAAA,CAAO;IAC9C,0KAAU,IAAA,CAAE,MAAA,CAAO;IACnB,wKAAQ,IAAA,CAAE,KAAA,iKAAM,IAAA,CAAE,MAAA,CAAO,mKAAG,IAAA,CAAE,IAAA,CAAK,CAAC;IACpC,2KAAW,IAAA,CAAE,QAAA,iKAAS,IAAA,CAAE,KAAA,iKAAM,IAAA,CAAE,MAAA,CAAO,kKAAG,KAAA,CAAE,IAAA,CAAK,CAAC,CAAC;IACnD,oKAAI,IAAA,CAAE,QAAA,iKAAS,IAAA,CAAE,MAAA,CAAO,CAAC;IACzB,iLAAiB,IAAA,CAAE,QAAA,iKAAS,IAAA,CAAE,MAAA,CAAO,CAAC;IACtC,kLAAkB,IAAA,CAAE,QAAA,iKAAS,IAAA,CAAE,MAAA,CAAO,CAAC;AACzC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1583, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1591, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/cron.ts"], "sourcesContent": ["import { getFunctionName, OptionalRestArgs } from \"../server/api.js\";\nimport { parseArgs } from \"../common/index.js\";\nimport { convexTo<PERSON>son, JSONValue, Value } from \"../values/index.js\";\nimport { SchedulableFunctionReference } from \"./scheduler.js\";\n\ntype CronSchedule = {\n  type: \"cron\";\n  cron: string;\n};\n/** @public */\nexport type IntervalSchedule =\n  | { type: \"interval\"; seconds: number }\n  | { type: \"interval\"; minutes: number }\n  | { type: \"interval\"; hours: number };\n/** @public */\nexport type HourlySchedule = {\n  type: \"hourly\";\n  minuteUTC: number;\n};\n/** @public */\nexport type DailySchedule = {\n  type: \"daily\";\n  hourUTC: number;\n  minuteUTC: number;\n};\nconst DAYS_OF_WEEK = [\n  \"sunday\",\n  \"monday\",\n  \"tuesday\",\n  \"wednesday\",\n  \"thursday\",\n  \"friday\",\n  \"saturday\",\n] as const;\ntype DayOfWeek = (typeof DAYS_OF_WEEK)[number];\n/** @public */\nexport type WeeklySchedule = {\n  type: \"weekly\";\n  dayOfWeek: DayOfWeek;\n  hourUTC: number;\n  minuteUTC: number;\n};\n/** @public */\nexport type MonthlySchedule = {\n  type: \"monthly\";\n  day: number;\n  hourUTC: number;\n  minuteUTC: number;\n};\n\n// Duplicating types so docstrings are visible in signatures:\n// `Expand<Omit<MonthlySchedule, \"type\">>` doesn't preserve docstrings.\n// When we get to TypeScript 4.9, `satisfies` would go nicely here.\n\n/** @public */\nexport type Interval =\n  | {\n      /**\n       * Run a job every `seconds` seconds, beginning\n       * when the job is first deployed to Convex.\n       */\n      seconds: number;\n      minutes?: undefined;\n      hours?: undefined;\n    }\n  | {\n      /**\n       * Run a job every `minutes` minutes, beginning\n       * when the job is first deployed to Convex.\n       */\n      minutes: number;\n      seconds?: undefined;\n      hours?: undefined;\n    }\n  | {\n      /**\n       * Run a job every `hours` hours, beginning when\n       * when the job is first deployed to Convex.\n       */\n      hours: number;\n      seconds?: undefined;\n      minutes?: undefined;\n    };\n\n/** @public */\nexport type Hourly = {\n  /**\n   * Minutes past the hour, 0-59.\n   */\n  minuteUTC: number;\n};\n\n/** @public */\nexport type Daily = {\n  /**\n   * 0-23, hour of day. Remember, this is UTC.\n   */\n  hourUTC: number;\n  /**\n   * 0-59, minute of hour. Remember, this is UTC.\n   */\n  minuteUTC: number;\n};\n\n/** @public */\nexport type Monthly = {\n  /**\n   * 1-31, day of month. Days greater that 28 will not run every month.\n   */\n  day: number;\n  /**\n   * 0-23, hour of day. Remember to convert from your own time zone to UTC.\n   */\n  hourUTC: number;\n  /**\n   * 0-59, minute of hour. Remember to convert from your own time zone to UTC.\n   */\n  minuteUTC: number;\n};\n/** @public */\nexport type Weekly = {\n  /**\n   * \"monday\", \"tuesday\", etc.\n   */\n  dayOfWeek: DayOfWeek;\n  /**\n   * 0-23, hour of day. Remember to convert from your own time zone to UTC.\n   */\n  hourUTC: number;\n  /**\n   * 0-59, minute of hour. Remember to convert from your own time zone to UTC.\n   */\n  minuteUTC: number;\n};\n\n/** @public */\nexport type Schedule =\n  | CronSchedule\n  | IntervalSchedule\n  | HourlySchedule\n  | DailySchedule\n  | WeeklySchedule\n  | MonthlySchedule;\n\n/**\n * A schedule to run a Convex mutation or action on.\n * You can schedule Convex functions to run regularly with\n * {@link interval} and exporting it.\n *\n * @public\n **/\nexport interface CronJob {\n  name: string;\n  args: JSONValue;\n  schedule: Schedule;\n}\n\n/**\n * Create a CronJobs object to schedule recurring tasks.\n *\n * ```js\n * // convex/crons.js\n * import { cronJobs } from 'convex/server';\n * import { api } from \"./_generated/api\";\n *\n * const crons = cronJobs();\n * crons.weekly(\n *   \"weekly re-engagement email\",\n *   {\n *     hourUTC: 17, // (9:30am Pacific/10:30am Daylight Savings Pacific)\n *     minuteUTC: 30,\n *   },\n *   api.emails.send\n * )\n * export default crons;\n * ```\n *\n * @public\n */\nexport const cronJobs = () => new Crons();\n\n/**\n * @public\n *\n * This is a cron string. They're complicated!\n */\ntype CronString = string;\n\nfunction validateIntervalNumber(n: number) {\n  if (!Number.isInteger(n) || n <= 0) {\n    throw new Error(\"Interval must be an integer greater than 0\");\n  }\n}\n\nfunction validatedDayOfMonth(n: number) {\n  if (!Number.isInteger(n) || n < 1 || n > 31) {\n    throw new Error(\"Day of month must be an integer from 1 to 31\");\n  }\n  return n;\n}\n\nfunction validatedDayOfWeek(s: string) {\n  if (!DAYS_OF_WEEK.includes(s as DayOfWeek)) {\n    throw new Error('Day of week must be a string like \"monday\".');\n  }\n  return s as DayOfWeek;\n}\n\nfunction validatedHourOfDay(n: number) {\n  if (!Number.isInteger(n) || n < 0 || n > 23) {\n    throw new Error(\"Hour of day must be an integer from 0 to 23\");\n  }\n  return n;\n}\n\nfunction validatedMinuteOfHour(n: number) {\n  if (!Number.isInteger(n) || n < 0 || n > 59) {\n    throw new Error(\"Minute of hour must be an integer from 0 to 59\");\n  }\n  return n;\n}\n\nfunction validatedCronString(s: string) {\n  return s;\n}\n\nfunction validatedCronIdentifier(s: string) {\n  if (!s.match(/^[ -~]*$/)) {\n    throw new Error(\n      `Invalid cron identifier ${s}: use ASCII letters that are not control characters`,\n    );\n  }\n  return s;\n}\n\n/**\n * A class for scheduling cron jobs.\n *\n * To learn more see the documentation at https://docs.convex.dev/scheduling/cron-jobs\n *\n * @public\n */\nexport class Crons {\n  crons: Record<string, CronJob>;\n  isCrons: true;\n  constructor() {\n    this.isCrons = true;\n    this.crons = {};\n  }\n\n  /** @internal */\n  schedule(\n    cronIdentifier: string,\n    schedule: Schedule,\n    functionReference: SchedulableFunctionReference,\n    args?: Record<string, Value>,\n  ) {\n    const cronArgs = parseArgs(args);\n    validatedCronIdentifier(cronIdentifier);\n    if (cronIdentifier in this.crons) {\n      throw new Error(`Cron identifier registered twice: ${cronIdentifier}`);\n    }\n    this.crons[cronIdentifier] = {\n      name: getFunctionName(functionReference),\n      args: [convexToJson(cronArgs)],\n      schedule: schedule,\n    };\n  }\n\n  /**\n   * Schedule a mutation or action to run at some interval.\n   *\n   * ```js\n   * crons.interval(\"Clear presence data\", {seconds: 30}, api.presence.clear);\n   * ```\n   *\n   * @param identifier - A unique name for this scheduled job.\n   * @param schedule - The time between runs for this scheduled job.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - The arguments to the function.\n   */\n  interval<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    schedule: Interval,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const s = schedule;\n    const hasSeconds = +(\"seconds\" in s && s.seconds !== undefined);\n    const hasMinutes = +(\"minutes\" in s && s.minutes !== undefined);\n    const hasHours = +(\"hours\" in s && s.hours !== undefined);\n    const total = hasSeconds + hasMinutes + hasHours;\n    if (total !== 1) {\n      throw new Error(\"Must specify one of seconds, minutes, or hours\");\n    }\n    if (hasSeconds) {\n      validateIntervalNumber(schedule.seconds!);\n    } else if (hasMinutes) {\n      validateIntervalNumber(schedule.minutes!);\n    } else if (hasHours) {\n      validateIntervalNumber(schedule.hours!);\n    }\n    this.schedule(\n      cronIdentifier,\n      { ...schedule, type: \"interval\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /**\n   * Schedule a mutation or action to run on an hourly basis.\n   *\n   * ```js\n   * crons.hourly(\n   *   \"Reset high scores\",\n   *   {\n   *     minuteUTC: 30,\n   *   },\n   *   api.scores.reset\n   * )\n   * ```\n   *\n   * @param cronIdentifier - A unique name for this scheduled job.\n   * @param schedule - What time (UTC) each day to run this function.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - The arguments to the function.\n   */\n  hourly<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    schedule: Hourly,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const minuteUTC = validatedMinuteOfHour(schedule.minuteUTC);\n    this.schedule(\n      cronIdentifier,\n      { minuteUTC, type: \"hourly\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /**\n   * Schedule a mutation or action to run on a daily basis.\n   *\n   * ```js\n   * crons.daily(\n   *   \"Reset high scores\",\n   *   {\n   *     hourUTC: 17, // (9:30am Pacific/10:30am Daylight Savings Pacific)\n   *     minuteUTC: 30,\n   *   },\n   *   api.scores.reset\n   * )\n   * ```\n   *\n   * @param cronIdentifier - A unique name for this scheduled job.\n   * @param schedule - What time (UTC) each day to run this function.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - The arguments to the function.\n   */\n  daily<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    schedule: Daily,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const hourUTC = validatedHourOfDay(schedule.hourUTC);\n    const minuteUTC = validatedMinuteOfHour(schedule.minuteUTC);\n    this.schedule(\n      cronIdentifier,\n      { hourUTC, minuteUTC, type: \"daily\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /**\n   * Schedule a mutation or action to run on a weekly basis.\n   *\n   * ```js\n   * crons.weekly(\n   *   \"Weekly re-engagement email\",\n   *   {\n   *     dayOfWeek: \"Tuesday\",\n   *     hourUTC: 17, // (9:30am Pacific/10:30am Daylight Savings Pacific)\n   *     minuteUTC: 30,\n   *   },\n   *   api.emails.send\n   * )\n   * ```\n   *\n   * @param cronIdentifier - A unique name for this scheduled job.\n   * @param schedule - What day and time (UTC) each week to run this function.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   */\n  weekly<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    schedule: Weekly,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const dayOfWeek = validatedDayOfWeek(schedule.dayOfWeek);\n    const hourUTC = validatedHourOfDay(schedule.hourUTC);\n    const minuteUTC = validatedMinuteOfHour(schedule.minuteUTC);\n    this.schedule(\n      cronIdentifier,\n      { dayOfWeek, hourUTC, minuteUTC, type: \"weekly\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /**\n   * Schedule a mutation or action to run on a monthly basis.\n   *\n   * Note that some months have fewer days than others, so e.g. a function\n   * scheduled to run on the 30th will not run in February.\n   *\n   * ```js\n   * crons.monthly(\n   *   \"Bill customers at \",\n   *   {\n   *     hourUTC: 17, // (9:30am Pacific/10:30am Daylight Savings Pacific)\n   *     minuteUTC: 30,\n   *     day: 1,\n   *   },\n   *   api.billing.billCustomers\n   * )\n   * ```\n   *\n   * @param cronIdentifier - A unique name for this scheduled job.\n   * @param schedule - What day and time (UTC) each month to run this function.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - The arguments to the function.\n   */\n  monthly<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    schedule: Monthly,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const day = validatedDayOfMonth(schedule.day);\n    const hourUTC = validatedHourOfDay(schedule.hourUTC);\n    const minuteUTC = validatedMinuteOfHour(schedule.minuteUTC);\n    this.schedule(\n      cronIdentifier,\n      { day, hourUTC, minuteUTC, type: \"monthly\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /**\n   * Schedule a mutation or action to run on a recurring basis.\n   *\n   * Like the unix command `cron`, Sunday is 0, Monday is 1, etc.\n   *\n   * ```\n   *  ┌─ minute (0 - 59)\n   *  │ ┌─ hour (0 - 23)\n   *  │ │ ┌─ day of the month (1 - 31)\n   *  │ │ │ ┌─ month (1 - 12)\n   *  │ │ │ │ ┌─ day of the week (0 - 6) (Sunday to Saturday)\n   * \"* * * * *\"\n   * ```\n   *\n   * @param cronIdentifier - A unique name for this scheduled job.\n   * @param cron - Cron string like `\"15 7 * * *\"` (Every day at 7:15 UTC)\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - The arguments to the function.\n   */\n  cron<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    cron: CronString,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const c = validatedCronString(cron);\n    this.schedule(\n      cronIdentifier,\n      { cron: c, type: \"cron\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /** @internal */\n  export() {\n    return JSON.stringify(this.crons);\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,uBAAyC;AAClD,SAAS,iBAAiB;AAC1B,SAAS,oBAAsC;;;;;;;;;;;;;;AAuB/C,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AAkJO,MAAM,WAAW,IAAM,IAAI,MAAM;AASxC,SAAS,uBAAuB,CAAA,EAAW;IACzC,IAAI,CAAC,OAAO,SAAA,CAAU,CAAC,KAAK,KAAK,GAAG;QAClC,MAAM,IAAI,MAAM,4CAA4C;IAC9D;AACF;AAEA,SAAS,oBAAoB,CAAA,EAAW;IACtC,IAAI,CAAC,OAAO,SAAA,CAAU,CAAC,KAAK,IAAI,KAAK,IAAI,IAAI;QAC3C,MAAM,IAAI,MAAM,8CAA8C;IAChE;IACA,OAAO;AACT;AAEA,SAAS,mBAAmB,CAAA,EAAW;IACrC,IAAI,CAAC,aAAa,QAAA,CAAS,CAAc,GAAG;QAC1C,MAAM,IAAI,MAAM,6CAA6C;IAC/D;IACA,OAAO;AACT;AAEA,SAAS,mBAAmB,CAAA,EAAW;IACrC,IAAI,CAAC,OAAO,SAAA,CAAU,CAAC,KAAK,IAAI,KAAK,IAAI,IAAI;QAC3C,MAAM,IAAI,MAAM,6CAA6C;IAC/D;IACA,OAAO;AACT;AAEA,SAAS,sBAAsB,CAAA,EAAW;IACxC,IAAI,CAAC,OAAO,SAAA,CAAU,CAAC,KAAK,IAAI,KAAK,IAAI,IAAI;QAC3C,MAAM,IAAI,MAAM,gDAAgD;IAClE;IACA,OAAO;AACT;AAEA,SAAS,oBAAoB,CAAA,EAAW;IACtC,OAAO;AACT;AAEA,SAAS,wBAAwB,CAAA,EAAW;IAC1C,IAAI,CAAC,EAAE,KAAA,CAAM,UAAU,GAAG;QACxB,MAAM,IAAI,MACR,CAAA,wBAAA,EAA2B,CAAC,CAAA,mDAAA,CAAA;IAEhC;IACA,OAAO;AACT;AASO,MAAM,MAAM;IAGjB,aAAc;QAFd,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEE,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,KAAA,GAAQ,CAAC;IAChB;IAAA,cAAA,GAGA,SACE,cAAA,EACA,QAAA,EACA,iBAAA,EACA,IAAA,EACA;QACA,MAAM,WAAW,4KAAA,EAAU,IAAI;QAC/B,wBAAwB,cAAc;QACtC,IAAI,kBAAkB,IAAA,CAAK,KAAA,EAAO;YAChC,MAAM,IAAI,MAAM,CAAA,kCAAA,EAAqC,cAAc,EAAE;QACvE;QACA,IAAA,CAAK,KAAA,CAAM,cAAc,CAAA,GAAI;YAC3B,MAAM,gLAAA,EAAgB,iBAAiB;YACvC,MAAM;gLAAC,eAAA,EAAa,QAAQ,CAAC;aAAA;YAC7B;QACF;IACF;IAAA;;;;;;;;;;;;GAAA,GAeA,SACE,cAAA,EACA,QAAA,EACA,iBAAA,EAAA,GACG,IAAA,EACH;QACA,MAAM,IAAI;QACV,MAAM,aAAa,CAAA,CAAE,aAAa,KAAK,EAAE,OAAA,KAAY,KAAA,CAAA;QACrD,MAAM,aAAa,CAAA,CAAE,aAAa,KAAK,EAAE,OAAA,KAAY,KAAA,CAAA;QACrD,MAAM,WAAW,CAAA,CAAE,WAAW,KAAK,EAAE,KAAA,KAAU,KAAA,CAAA;QAC/C,MAAM,QAAQ,aAAa,aAAa;QACxC,IAAI,UAAU,GAAG;YACf,MAAM,IAAI,MAAM,gDAAgD;QAClE;QACA,IAAI,YAAY;YACd,uBAAuB,SAAS,OAAQ;QAC1C,OAAA,IAAW,YAAY;YACrB,uBAAuB,SAAS,OAAQ;QAC1C,OAAA,IAAW,UAAU;YACnB,uBAAuB,SAAS,KAAM;QACxC;QACA,IAAA,CAAK,QAAA,CACH,gBACA;YAAE,GAAG,QAAA;YAAU,MAAM;QAAW,GAChC,sBACG;IAEP;IAAA;;;;;;;;;;;;;;;;;;GAAA,GAqBA,OACE,cAAA,EACA,QAAA,EACA,iBAAA,EAAA,GACG,IAAA,EACH;QACA,MAAM,YAAY,sBAAsB,SAAS,SAAS;QAC1D,IAAA,CAAK,QAAA,CACH,gBACA;YAAE;YAAW,MAAM;QAAS,GAC5B,sBACG;IAEP;IAAA;;;;;;;;;;;;;;;;;;;GAAA,GAsBA,MACE,cAAA,EACA,QAAA,EACA,iBAAA,EAAA,GACG,IAAA,EACH;QACA,MAAM,UAAU,mBAAmB,SAAS,OAAO;QACnD,MAAM,YAAY,sBAAsB,SAAS,SAAS;QAC1D,IAAA,CAAK,QAAA,CACH,gBACA;YAAE;YAAS;YAAW,MAAM;QAAQ,GACpC,sBACG;IAEP;IAAA;;;;;;;;;;;;;;;;;;;GAAA,GAsBA,OACE,cAAA,EACA,QAAA,EACA,iBAAA,EAAA,GACG,IAAA,EACH;QACA,MAAM,YAAY,mBAAmB,SAAS,SAAS;QACvD,MAAM,UAAU,mBAAmB,SAAS,OAAO;QACnD,MAAM,YAAY,sBAAsB,SAAS,SAAS;QAC1D,IAAA,CAAK,QAAA,CACH,gBACA;YAAE;YAAW;YAAS;YAAW,MAAM;QAAS,GAChD,sBACG;IAEP;IAAA;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA0BA,QACE,cAAA,EACA,QAAA,EACA,iBAAA,EAAA,GACG,IAAA,EACH;QACA,MAAM,MAAM,oBAAoB,SAAS,GAAG;QAC5C,MAAM,UAAU,mBAAmB,SAAS,OAAO;QACnD,MAAM,YAAY,sBAAsB,SAAS,SAAS;QAC1D,IAAA,CAAK,QAAA,CACH,gBACA;YAAE;YAAK;YAAS;YAAW,MAAM;QAAU,GAC3C,sBACG;IAEP;IAAA;;;;;;;;;;;;;;;;;;;GAAA,GAsBA,KACE,cAAA,EACA,IAAA,EACA,iBAAA,EAAA,GACG,IAAA,EACH;QACA,MAAM,IAAI,oBAAoB,IAAI;QAClC,IAAA,CAAK,QAAA,CACH,gBACA;YAAE,MAAM;YAAG,MAAM;QAAO,GACxB,sBACG;IAEP;IAAA,cAAA,GAGA,SAAS;QACP,OAAO,KAAK,SAAA,CAAU,IAAA,CAAK,KAAK;IAClC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1866, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/router.ts"], "sourcesContent": ["import { performJsSyscall } from \"./impl/syscall.js\";\nimport { PublicHttpAction } from \"./registration.js\";\n\n// Note: this list is duplicated in the dashboard.\n/**\n * A list of the methods supported by Convex HTTP actions.\n *\n * HEAD is handled by Convex by running GET and stripping the body.\n * CONNECT is not supported and will not be supported.\n * TRACE is not supported and will not be supported.\n *\n * @public\n */\nexport const ROUTABLE_HTTP_METHODS = [\n  \"GET\",\n  \"POST\",\n  \"PUT\",\n  \"DELETE\",\n  \"OPTIONS\",\n  \"PATCH\",\n] as const;\n/**\n * A type representing the methods supported by Convex HTTP actions.\n *\n * HEAD is handled by Convex by running GET and stripping the body.\n * CONNECT is not supported and will not be supported.\n * TRACE is not supported and will not be supported.\n *\n * @public\n */\nexport type RoutableMethod = (typeof ROUTABLE_HTTP_METHODS)[number];\n\nexport function normalizeMethod(\n  method: RoutableMethod | \"HEAD\",\n): RoutableMethod {\n  // This router routes HEAD requests as GETs, letting <PERSON><PERSON><PERSON> strip thee response\n  // bodies are response bodies afterward.\n  if (method === \"HEAD\") return \"GET\";\n  return method;\n}\n\n/**\n * Return a new {@link HttpRouter} object.\n *\n * @public\n */\nexport const httpRouter = () => new HttpRouter();\n\n/**\n * A type representing a route to an HTTP action using an exact request URL path match.\n *\n * Used by {@link HttpRouter} to route requests to HTTP actions.\n *\n * @public\n */\nexport type RouteSpecWithPath = {\n  /**\n   * Exact HTTP request path to route.\n   */\n  path: string;\n  /**\n   * HTTP method (\"GET\", \"POST\", ...) to route.\n   */\n  method: RoutableMethod;\n  /**\n   * The HTTP action to execute.\n   */\n  handler: PublicHttpAction;\n};\n\n/**\n * A type representing a route to an HTTP action using a request URL path prefix match.\n *\n * Used by {@link HttpRouter} to route requests to HTTP actions.\n *\n * @public\n */\nexport type RouteSpecWithPathPrefix = {\n  /**\n   * An HTTP request path prefix to route. Requests with a path starting with this value\n   * will be routed to the HTTP action.\n   */\n  pathPrefix: string;\n  /**\n   * HTTP method (\"GET\", \"POST\", ...) to route.\n   */\n  method: RoutableMethod;\n  /**\n   * The HTTP action to execute.\n   */\n  handler: PublicHttpAction;\n};\n\n/**\n * A type representing a route to an HTTP action.\n *\n * Used by {@link HttpRouter} to route requests to HTTP actions.\n *\n * @public\n */\nexport type RouteSpec = RouteSpecWithPath | RouteSpecWithPathPrefix;\n\n/**\n * HTTP router for specifying the paths and methods of {@link httpActionGeneric}s\n *\n * An example `convex/http.js` file might look like this.\n *\n * ```js\n * import { httpRouter } from \"convex/server\";\n * import { getMessagesByAuthor } from \"./getMessagesByAuthor\";\n * import { httpAction } from \"./_generated/server\";\n *\n * const http = httpRouter();\n *\n * // HTTP actions can be defined inline...\n * http.route({\n *   path: \"/message\",\n *   method: \"POST\",\n *   handler: httpAction(async ({ runMutation }, request) => {\n *     const { author, body } = await request.json();\n *\n *     await runMutation(api.sendMessage.default, { body, author });\n *     return new Response(null, {\n *       status: 200,\n *     });\n *   })\n * });\n *\n * // ...or they can be imported from other files.\n * http.route({\n *   path: \"/getMessagesByAuthor\",\n *   method: \"GET\",\n *   handler: getMessagesByAuthor,\n * });\n *\n * // Convex expects the router to be the default export of `convex/http.js`.\n * export default http;\n * ```\n *\n * @public\n */\nexport class HttpRouter {\n  exactRoutes: Map<string, Map<RoutableMethod, PublicHttpAction>> = new Map();\n  prefixRoutes: Map<RoutableMethod, Map<string, PublicHttpAction>> = new Map();\n  isRouter: true = true;\n\n  /**\n   * Specify an HttpAction to be used to respond to requests\n   * for an HTTP method (e.g. \"GET\") and a path or pathPrefix.\n   *\n   * Paths must begin with a slash. Path prefixes must also end in a slash.\n   *\n   * ```js\n   * // matches `/profile` (but not `/profile/`)\n   * http.route({ path: \"/profile\", method: \"GET\", handler: getProfile})\n   *\n   * // matches `/profiles/`, `/profiles/abc`, and `/profiles/a/c/b` (but not `/profile`)\n   * http.route({ pathPrefix: \"/profile/\", method: \"GET\", handler: getProfile})\n   * ```\n   */\n  route = (spec: RouteSpec) => {\n    if (!spec.handler) throw new Error(`route requires handler`);\n    if (!spec.method) throw new Error(`route requires method`);\n    const { method, handler } = spec;\n    if (!ROUTABLE_HTTP_METHODS.includes(method)) {\n      throw new Error(\n        `'${method}' is not an allowed HTTP method (like GET, POST, PUT etc.)`,\n      );\n    }\n\n    if (\"path\" in spec) {\n      if (\"pathPrefix\" in spec) {\n        throw new Error(\n          `Invalid httpRouter route: cannot contain both 'path' and 'pathPrefix'`,\n        );\n      }\n      if (!spec.path.startsWith(\"/\")) {\n        throw new Error(`path '${spec.path}' does not start with a /`);\n      }\n      const methods: Map<RoutableMethod, PublicHttpAction> =\n        this.exactRoutes.has(spec.path)\n          ? this.exactRoutes.get(spec.path)!\n          : new Map();\n      if (methods.has(method)) {\n        throw new Error(\n          `Path '${spec.path}' for method ${method} already in use`,\n        );\n      }\n      methods.set(method, handler);\n      this.exactRoutes.set(spec.path, methods);\n    } else if (\"pathPrefix\" in spec) {\n      if (!spec.pathPrefix.startsWith(\"/\")) {\n        throw new Error(\n          `pathPrefix '${spec.pathPrefix}' does not start with a /`,\n        );\n      }\n      if (!spec.pathPrefix.endsWith(\"/\")) {\n        throw new Error(`pathPrefix ${spec.pathPrefix} must end with a /`);\n      }\n      const prefixes =\n        this.prefixRoutes.get(method) || new Map<string, PublicHttpAction>();\n      if (prefixes.has(spec.pathPrefix)) {\n        throw new Error(\n          `${spec.method} pathPrefix ${spec.pathPrefix} is already defined`,\n        );\n      }\n      prefixes.set(spec.pathPrefix, handler);\n      this.prefixRoutes.set(method, prefixes);\n    } else {\n      throw new Error(\n        `Invalid httpRouter route entry: must contain either field 'path' or 'pathPrefix'`,\n      );\n    }\n  };\n\n  /**\n   * Returns a list of routed HTTP actions.\n   *\n   * These are used to populate the list of routes shown in the Functions page of the Convex dashboard.\n   *\n   * @returns - an array of [path, method, endpoint] tuples.\n   */\n  getRoutes = (): Array<\n    Readonly<[string, RoutableMethod, PublicHttpAction]>\n  > => {\n    const exactPaths: string[] = [...this.exactRoutes.keys()].sort();\n    const exact = exactPaths.flatMap((path) =>\n      [...this.exactRoutes.get(path)!.keys()]\n        .sort()\n        .map(\n          (method) =>\n            [path, method, this.exactRoutes.get(path)!.get(method)!] as const,\n        ),\n    );\n\n    const prefixPathMethods = [...this.prefixRoutes.keys()].sort();\n    const prefixes = prefixPathMethods.flatMap((method) =>\n      [...this.prefixRoutes.get(method)!.keys()]\n        .sort()\n        .map(\n          (pathPrefix) =>\n            [\n              `${pathPrefix}*`,\n              method,\n              this.prefixRoutes.get(method)!.get(pathPrefix)!,\n            ] as const,\n        ),\n    );\n\n    return [...exact, ...prefixes];\n  };\n\n  /**\n   * Returns the appropriate HTTP action and its routed request path and method.\n   *\n   * The path and method returned are used for logging and metrics, and should\n   * match up with one of the routes returned by `getRoutes`.\n   *\n   * For example,\n   *\n   * ```js\n   * http.route({ pathPrefix: \"/profile/\", method: \"GET\", handler: getProfile});\n   *\n   * http.lookup(\"/profile/abc\", \"GET\") // returns [getProfile, \"GET\", \"/profile/*\"]\n   *```\n   *\n   * @returns - a tuple [{@link PublicHttpAction}, method, path] or null.\n   */\n  lookup = (\n    path: string,\n    method: RoutableMethod | \"HEAD\",\n  ): Readonly<[PublicHttpAction, RoutableMethod, string]> | null => {\n    method = normalizeMethod(method);\n    const exactMatch = this.exactRoutes.get(path)?.get(method);\n    if (exactMatch) return [exactMatch, method, path];\n\n    const prefixes = this.prefixRoutes.get(method) || new Map();\n    const prefixesSorted = [...prefixes.entries()].sort(\n      ([prefixA, _a], [prefixB, _b]) => prefixB.length - prefixA.length,\n    );\n    for (const [pathPrefix, endpoint] of prefixesSorted) {\n      if (path.startsWith(pathPrefix)) {\n        return [endpoint, method, `${pathPrefix}*`];\n      }\n    }\n    return null;\n  };\n\n  /**\n   * Given a JSON string representation of a Request object, return a Response\n   * by routing the request and running the appropriate endpoint or returning\n   * a 404 Response.\n   *\n   * @param argsStr - a JSON string representing a Request object.\n   *\n   * @returns - a Response object.\n   */\n  runRequest = async (\n    argsStr: string,\n    requestRoute: string,\n  ): Promise<string> => {\n    const request = performJsSyscall(\"requestFromConvexJson\", {\n      convexJson: JSON.parse(argsStr),\n    });\n\n    let pathname = requestRoute;\n    if (!pathname || typeof pathname !== \"string\") {\n      pathname = new URL(request.url).pathname;\n    }\n\n    const method = request.method;\n    const match = this.lookup(pathname, method as RoutableMethod);\n    if (!match) {\n      const response = new Response(`No HttpAction routed for ${pathname}`, {\n        status: 404,\n      });\n      return JSON.stringify(\n        performJsSyscall(\"convexJsonFromResponse\", { response }),\n      );\n    }\n    const [endpoint, _method, _path] = match;\n    const response = await endpoint.invokeHttpAction(request);\n    return JSON.stringify(\n      performJsSyscall(\"convexJsonFromResponse\", { response }),\n    );\n  };\n}\n"], "names": ["response"], "mappings": ";;;;;;AAAA,SAAS,wBAAwB;;;;;;;;;;;AAa1B,MAAM,wBAAwB;IACnC;IACA;IACA;IACA;IACA;IACA;CACF;AAYO,SAAS,gBACd,MAAA,EACgB;IAGhB,IAAI,WAAW,OAAQ,CAAA,OAAO;IAC9B,OAAO;AACT;AAOO,MAAM,aAAa,IAAM,IAAI,WAAW;AA+FxC,MAAM,WAAW;IAAjB,aAAA;QACL,cAAA,IAAA,EAAA,eAAkE,aAAA,GAAA,IAAI,IAAI;QAC1E,cAAA,IAAA,EAAA,gBAAmE,aAAA,GAAA,IAAI,IAAI;QAC3E,cAAA,IAAA,EAAA,YAAiB;QAgBjB;;;;;;;;;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,SAAQ,CAAC,SAAoB;YAC3B,IAAI,CAAC,KAAK,OAAA,CAAS,CAAA,MAAM,IAAI,MAAM,CAAA,sBAAA,CAAwB;YAC3D,IAAI,CAAC,KAAK,MAAA,CAAQ,CAAA,MAAM,IAAI,MAAM,CAAA,qBAAA,CAAuB;YACzD,MAAM,EAAE,MAAA,EAAQ,OAAA,CAAQ,CAAA,GAAI;YAC5B,IAAI,CAAC,sBAAsB,QAAA,CAAS,MAAM,GAAG;gBAC3C,MAAM,IAAI,MACR,CAAA,CAAA,EAAI,MAAM,CAAA,0DAAA,CAAA;YAEd;YAEA,IAAI,UAAU,MAAM;gBAClB,IAAI,gBAAgB,MAAM;oBACxB,MAAM,IAAI,MACR,CAAA,qEAAA,CAAA;gBAEJ;gBACA,IAAI,CAAC,KAAK,IAAA,CAAK,UAAA,CAAW,GAAG,GAAG;oBAC9B,MAAM,IAAI,MAAM,CAAA,MAAA,EAAS,KAAK,IAAI,CAAA,yBAAA,CAA2B;gBAC/D;gBACA,MAAM,UACJ,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI,KAAK,IAAI,IAC1B,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI,KAAK,IAAI,IAC9B,aAAA,GAAA,IAAI,IAAI;gBACd,IAAI,QAAQ,GAAA,CAAI,MAAM,GAAG;oBACvB,MAAM,IAAI,MACR,CAAA,MAAA,EAAS,KAAK,IAAI,CAAA,aAAA,EAAgB,MAAM,CAAA,eAAA,CAAA;gBAE5C;gBACA,QAAQ,GAAA,CAAI,QAAQ,OAAO;gBAC3B,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI,KAAK,IAAA,EAAM,OAAO;YACzC,OAAA,IAAW,gBAAgB,MAAM;gBAC/B,IAAI,CAAC,KAAK,UAAA,CAAW,UAAA,CAAW,GAAG,GAAG;oBACpC,MAAM,IAAI,MACR,CAAA,YAAA,EAAe,KAAK,UAAU,CAAA,yBAAA,CAAA;gBAElC;gBACA,IAAI,CAAC,KAAK,UAAA,CAAW,QAAA,CAAS,GAAG,GAAG;oBAClC,MAAM,IAAI,MAAM,CAAA,WAAA,EAAc,KAAK,UAAU,CAAA,kBAAA,CAAoB;gBACnE;gBACA,MAAM,WACJ,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,MAAM,KAAK,aAAA,GAAA,IAAI,IAA8B;gBACrE,IAAI,SAAS,GAAA,CAAI,KAAK,UAAU,GAAG;oBACjC,MAAM,IAAI,MACR,GAAG,KAAK,MAAM,CAAA,YAAA,EAAe,KAAK,UAAU,CAAA,mBAAA,CAAA;gBAEhD;gBACA,SAAS,GAAA,CAAI,KAAK,UAAA,EAAY,OAAO;gBACrC,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,QAAQ,QAAQ;YACxC,OAAO;gBACL,MAAM,IAAI,MACR,CAAA,gFAAA,CAAA;YAEJ;QACF;QASA;;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,aAAY,MAEP;YACH,MAAM,aAAuB,CAAC;mBAAG,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,CAAC;aAAA,CAAE,IAAA,CAAK;YAC/D,MAAM,QAAQ,WAAW,OAAA,CAAQ,CAAC,OAChC,CAAC;uBAAG,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI,IAAI,EAAG,IAAA,CAAK,CAAC;iBAAA,CACnC,IAAA,CAAK,EACL,GAAA,CACC,CAAC,SACC;wBAAC;wBAAM;wBAAQ,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI,IAAI,EAAG,GAAA,CAAI,MAAM,CAAE;qBAAA;YAI/D,MAAM,oBAAoB,CAAC;mBAAG,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,CAAC;aAAA,CAAE,IAAA,CAAK;YAC7D,MAAM,WAAW,kBAAkB,OAAA,CAAQ,CAAC,SAC1C,CAAC;uBAAG,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,MAAM,EAAG,IAAA,CAAK,CAAC;iBAAA,CACtC,IAAA,CAAK,EACL,GAAA,CACC,CAAC,aACC;wBACE,GAAG,UAAU,CAAA,CAAA,CAAA;wBACb;wBACA,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,MAAM,EAAG,GAAA,CAAI,UAAU;qBAC/C;YAIR,OAAO,CAAC;mBAAG,OAAO;mBAAG,QAAQ;aAAA;QAC/B;QAkBA;;;;;;;;;;;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,UAAS,CACP,MACA,WACgE;YAChE,SAAS,gBAAgB,MAAM;YAC/B,MAAM,aAAa,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI,IAAI,GAAG,IAAI,MAAM;YACzD,IAAI,WAAY,CAAA,OAAO;gBAAC;gBAAY;gBAAQ,IAAI;aAAA;YAEhD,MAAM,WAAW,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,MAAM,KAAK,aAAA,GAAA,IAAI,IAAI;YAC1D,MAAM,iBAAiB,CAAC;mBAAG,SAAS,OAAA,CAAQ,CAAC;aAAA,CAAE,IAAA,CAC7C,CAAC,CAAC,SAAS,EAAE,CAAA,EAAG,CAAC,SAAS,EAAE,CAAA,GAAM,QAAQ,MAAA,GAAS,QAAQ,MAAA;YAE7D,KAAA,MAAW,CAAC,YAAY,QAAQ,CAAA,IAAK,eAAgB;gBACnD,IAAI,KAAK,UAAA,CAAW,UAAU,GAAG;oBAC/B,OAAO;wBAAC;wBAAU;wBAAQ,GAAG,UAAU,CAAA,CAAA,CAAG;qBAAA;gBAC5C;YACF;YACA,OAAO;QACT;QAWA;;;;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,cAAa,OACX,SACA,iBACoB;YACpB,MAAM,WAAU,4LAAA,EAAiB,yBAAyB;gBACxD,YAAY,KAAK,KAAA,CAAM,OAAO;YAChC,CAAC;YAED,IAAI,WAAW;YACf,IAAI,CAAC,YAAY,OAAO,aAAa,UAAU;gBAC7C,WAAW,IAAI,IAAI,QAAQ,GAAG,EAAE,QAAA;YAClC;YAEA,MAAM,SAAS,QAAQ,MAAA;YACvB,MAAM,QAAQ,IAAA,CAAK,MAAA,CAAO,UAAU,MAAwB;YAC5D,IAAI,CAAC,OAAO;gBACV,MAAMA,YAAW,IAAI,SAAS,CAAA,yBAAA,EAA4B,QAAQ,EAAA,EAAI;oBACpE,QAAQ;gBACV,CAAC;gBACD,OAAO,KAAK,SAAA,2KACV,mBAAA,EAAiB,0BAA0B;oBAAE,UAAAA;gBAAS,CAAC;YAE3D;YACA,MAAM,CAAC,UAAU,SAAS,KAAK,CAAA,GAAI;YACnC,MAAM,WAAW,MAAM,SAAS,gBAAA,CAAiB,OAAO;YACxD,OAAO,KAAK,SAAA,2KACV,mBAAA,EAAiB,0BAA0B;gBAAE;YAAS,CAAC;QAE3D;IAAA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2061, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/components/index.ts"], "sourcesContent": ["import { PropertyValidators, convexToJson } from \"../../values/index.js\";\nimport { version } from \"../../index.js\";\nimport {\n  AnyFunctionReference,\n  FunctionReference,\n  FunctionType,\n} from \"../api.js\";\nimport { performAsyncSyscall } from \"../impl/syscall.js\";\nimport { DefaultFunctionArgs } from \"../registration.js\";\nimport {\n  AppDefinitionAnalysis,\n  ComponentDefinitionAnalysis,\n  ComponentDefinitionType,\n} from \"./definition.js\";\nimport {\n  getFunctionAddress,\n  setReferencePath,\n  toReferencePath,\n} from \"./paths.js\";\nexport { getFunctionAddress } from \"./paths.js\";\n\n/**\n * A serializable reference to a Convex function.\n * Passing a this reference to another component allows that component to call this\n * function during the current function execution or at any later time.\n * Function handles are used like `api.folder.function` FunctionReferences,\n * e.g. `ctx.scheduler.runAfter(0, functionReference, args)`.\n *\n * A function reference is stable across code pushes but it's possible\n * the Convex function it refers to might no longer exist.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport type FunctionHandle<\n  Type extends FunctionType,\n  Args extends DefaultFunctionArgs = any,\n  ReturnType = any,\n> = string & FunctionReference<Type, \"internal\", Args, ReturnType>;\n\n/**\n * Create a serializable reference to a Convex function.\n * Passing a this reference to another component allows that component to call this\n * function during the current function execution or at any later time.\n * Function handles are used like `api.folder.function` FunctionReferences,\n * e.g. `ctx.scheduler.runAfter(0, functionReference, args)`.\n *\n * A function reference is stable across code pushes but it's possible\n * the Convex function it refers to might no longer exist.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport async function createFunctionHandle<\n  Type extends FunctionType,\n  Args extends DefaultFunctionArgs,\n  ReturnType,\n>(\n  functionReference: FunctionReference<\n    Type,\n    \"public\" | \"internal\",\n    Args,\n    ReturnType\n  >,\n): Promise<FunctionHandle<Type, Args, ReturnType>> {\n  const address = getFunctionAddress(functionReference);\n  return await performAsyncSyscall(\"1.0/createFunctionHandle\", {\n    ...address,\n    version,\n  });\n}\n\ninterface ComponentExports {\n  [key: string]: FunctionReference<any, any, any, any> | ComponentExports;\n}\n\n/**\n * An object of this type should be the default export of a\n * convex.config.ts file in a component definition directory.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport type ComponentDefinition<Exports extends ComponentExports = any> = {\n  /**\n   * Install a component with the given definition in this component definition.\n   *\n   * Takes a component definition and an optional name.\n   *\n   * For editor tooling this method expects a {@link ComponentDefinition}\n   * but at runtime the object that is imported will be a {@link ImportedComponentDefinition}\n   */\n  use<Definition extends ComponentDefinition<any>>(\n    definition: Definition,\n    options?: {\n      name?: string;\n    },\n  ): InstalledComponent<Definition>;\n\n  /**\n   * Internal type-only property tracking exports provided.\n   *\n   * @deprecated This is a type-only property, don't use it.\n   */\n  __exports: Exports;\n};\n\ntype ComponentDefinitionExports<T extends ComponentDefinition<any>> =\n  T[\"__exports\"];\n\n/**\n * An object of this type should be the default export of a\n * convex.config.ts file in a component-aware convex directory.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport type AppDefinition = {\n  /**\n   * Install a component with the given definition in this component definition.\n   *\n   * Takes a component definition and an optional name.\n   *\n   * For editor tooling this method expects a {@link ComponentDefinition}\n   * but at runtime the object that is imported will be a {@link ImportedComponentDefinition}\n   */\n  use<Definition extends ComponentDefinition<any>>(\n    definition: Definition,\n    options?: {\n      name?: string;\n    },\n  ): InstalledComponent<Definition>;\n};\n\ninterface ExportTree {\n  // Tree with serialized `Reference`s as leaves.\n  [key: string]: string | ExportTree;\n}\n\ntype CommonDefinitionData = {\n  _isRoot: boolean;\n  _childComponents: [\n    string,\n    ImportedComponentDefinition,\n    Record<string, any> | null,\n  ][];\n  _exportTree: ExportTree;\n};\n\ntype ComponentDefinitionData = CommonDefinitionData & {\n  _args: PropertyValidators;\n  _name: string;\n  _onInitCallbacks: Record<string, (argsStr: string) => string>;\n};\ntype AppDefinitionData = CommonDefinitionData;\n\n/**\n * Used to refer to an already-installed component.\n */\nclass InstalledComponent<Definition extends ComponentDefinition<any>> {\n  /**\n   * @internal\n   */\n  _definition: Definition;\n\n  /**\n   * @internal\n   */\n  _name: string;\n\n  constructor(definition: Definition, name: string) {\n    this._definition = definition;\n    this._name = name;\n    setReferencePath(this, `_reference/childComponent/${name}`);\n  }\n\n  get exports(): ComponentDefinitionExports<Definition> {\n    return createExports(this._name, []);\n  }\n}\n\nfunction createExports(name: string, pathParts: string[]): any {\n  const handler: ProxyHandler<any> = {\n    get(_, prop: string | symbol) {\n      if (typeof prop === \"string\") {\n        const newParts = [...pathParts, prop];\n        return createExports(name, newParts);\n      } else if (prop === toReferencePath) {\n        let reference = `_reference/childComponent/${name}`;\n        for (const part of pathParts) {\n          reference += `/${part}`;\n        }\n        return reference;\n      } else {\n        return undefined;\n      }\n    },\n  };\n  return new Proxy({}, handler);\n}\n\nfunction use<Definition extends ComponentDefinition<any>>(\n  this: CommonDefinitionData,\n  definition: Definition,\n  options?: {\n    name?: string;\n  },\n): InstalledComponent<Definition> {\n  // At runtime an imported component will have this shape.\n  const importedComponentDefinition =\n    definition as unknown as ImportedComponentDefinition;\n  if (typeof importedComponentDefinition.componentDefinitionPath !== \"string\") {\n    throw new Error(\n      \"Component definition does not have the required componentDefinitionPath property. This code only works in Convex runtime.\",\n    );\n  }\n  const name =\n    options?.name ||\n    // added recently\n    importedComponentDefinition.defaultName ||\n    // can be removed once backend is out\n    importedComponentDefinition.componentDefinitionPath.split(\"/\").pop()!;\n  this._childComponents.push([name, importedComponentDefinition, {}]);\n  return new InstalledComponent(definition, name);\n}\n\n/**\n * The runtime type of a ComponentDefinition. TypeScript will claim\n * the default export of a module like \"cool-component/convex.config.js\"\n * is a `@link ComponentDefinition}, but during component definition evaluation\n * this is its type instead.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport type ImportedComponentDefinition = {\n  componentDefinitionPath: string;\n  defaultName: string;\n};\n\nfunction exportAppForAnalysis(\n  this: ComponentDefinition<any> & AppDefinitionData,\n): AppDefinitionAnalysis {\n  const definitionType = { type: \"app\" as const };\n  const childComponents = serializeChildComponents(this._childComponents);\n  return {\n    definitionType,\n    childComponents: childComponents as any,\n    httpMounts: {},\n    exports: serializeExportTree(this._exportTree),\n  };\n}\n\nfunction serializeExportTree(tree: ExportTree): any {\n  const branch: any[] = [];\n  for (const [key, child] of Object.entries(tree)) {\n    let node;\n    if (typeof child === \"string\") {\n      node = { type: \"leaf\", leaf: child };\n    } else {\n      node = serializeExportTree(child);\n    }\n    branch.push([key, node]);\n  }\n  return { type: \"branch\", branch };\n}\n\nfunction serializeChildComponents(\n  childComponents: [\n    string,\n    ImportedComponentDefinition,\n    Record<string, any> | null,\n  ][],\n): {\n  name: string;\n  path: string;\n  args: [string, { type: \"value\"; value: string }][] | null;\n}[] {\n  return childComponents.map(([name, definition, p]) => {\n    let args: [string, { type: \"value\"; value: string }][] | null = null;\n    if (p !== null) {\n      args = [];\n      for (const [name, value] of Object.entries(p)) {\n        if (value !== undefined) {\n          args.push([\n            name,\n            { type: \"value\", value: JSON.stringify(convexToJson(value)) },\n          ]);\n        }\n      }\n    }\n    // we know that components carry this extra information\n    const path = definition.componentDefinitionPath;\n    if (!path)\n      throw new Error(\n        \"no .componentPath for component definition \" +\n          JSON.stringify(definition, null, 2),\n      );\n\n    return {\n      name: name!,\n      path: path!,\n      args,\n    };\n  });\n}\n\nfunction exportComponentForAnalysis(\n  this: ComponentDefinition<any> & ComponentDefinitionData,\n): ComponentDefinitionAnalysis {\n  const args: [string, { type: \"value\"; value: string }][] = Object.entries(\n    this._args,\n  ).map(([name, validator]) => [\n    name,\n    {\n      type: \"value\",\n      value: JSON.stringify(validator.json),\n    },\n  ]);\n  const definitionType: ComponentDefinitionType = {\n    type: \"childComponent\" as const,\n    name: this._name,\n    args,\n  };\n  const childComponents = serializeChildComponents(this._childComponents);\n  return {\n    name: this._name,\n    definitionType,\n    childComponents: childComponents as any,\n    httpMounts: {},\n    exports: serializeExportTree(this._exportTree),\n  };\n}\n\n// This is what is actually contained in a ComponentDefinition.\ntype RuntimeComponentDefinition = Omit<ComponentDefinition<any>, \"__exports\"> &\n  ComponentDefinitionData & {\n    export: () => ComponentDefinitionAnalysis;\n  };\ntype RuntimeAppDefinition = AppDefinition &\n  AppDefinitionData & {\n    export: () => AppDefinitionAnalysis;\n  };\n\n/**\n * Define a component, a piece of a Convex deployment with namespaced resources.\n *\n * The default\n * the default export of a module like \"cool-component/convex.config.js\"\n * is a `@link ComponentDefinition}, but during component definition evaluation\n * this is its type instead.\n *\n * @param name Name must be alphanumeric plus underscores. Typically these are\n * lowercase with underscores like `\"onboarding_flow_tracker\"`.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport function defineComponent<Exports extends ComponentExports = any>(\n  name: string,\n): ComponentDefinition<Exports> {\n  const ret: RuntimeComponentDefinition = {\n    _isRoot: false,\n    _name: name,\n    _args: {},\n    _childComponents: [],\n    _exportTree: {},\n    _onInitCallbacks: {},\n\n    export: exportComponentForAnalysis,\n    use,\n\n    // pretend to conform to ComponentDefinition, which temporarily expects __args\n    ...({} as { __args: any; __exports: any }),\n  };\n  return ret as any as ComponentDefinition<Exports>;\n}\n\n/**\n * Attach components, reuseable pieces of a Convex deployment, to this Convex app.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport function defineApp(): AppDefinition {\n  const ret: RuntimeAppDefinition = {\n    _isRoot: true,\n    _childComponents: [],\n    _exportTree: {},\n\n    export: exportAppForAnalysis,\n    use,\n  };\n  return ret as AppDefinition;\n}\n\ntype AnyInterfaceType = {\n  [key: string]: AnyInterfaceType;\n} & AnyFunctionReference;\nexport type AnyComponentReference = Record<string, AnyInterfaceType>;\n\nexport type AnyChildComponents = Record<string, AnyComponentReference>;\n\n/**\n * @internal\n */\nexport function currentSystemUdfInComponent(\n  componentId: string,\n): AnyComponentReference {\n  return {\n    [toReferencePath]: `_reference/currentSystemUdfInComponent/${componentId}`,\n  };\n}\n\nfunction createChildComponents(\n  root: string,\n  pathParts: string[],\n): AnyChildComponents {\n  const handler: ProxyHandler<object> = {\n    get(_, prop: string | symbol) {\n      if (typeof prop === \"string\") {\n        const newParts = [...pathParts, prop];\n        return createChildComponents(root, newParts);\n      } else if (prop === toReferencePath) {\n        if (pathParts.length < 1) {\n          const found = [root, ...pathParts].join(\".\");\n          throw new Error(\n            `API path is expected to be of the form \\`${root}.childComponent.functionName\\`. Found: \\`${found}\\``,\n          );\n        }\n        return `_reference/childComponent/` + pathParts.join(\"/\");\n      } else {\n        return undefined;\n      }\n    },\n  };\n  return new Proxy({}, handler);\n}\n\nexport const componentsGeneric = () => createChildComponents(\"components\", []);\n\nexport type AnyComponents = AnyChildComponents;\n"], "names": ["name"], "mappings": ";;;;;;;AAAA,SAA6B,oBAAoB;;AACjD,SAAS,eAAe;AAMxB,SAAS,2BAA2B;AAOpC;;;;;;;;;;;;;;;AAuCA,eAAsB,qBAKpB,iBAAA,EAMiD;IACjD,MAAM,wLAAU,qBAAA,EAAmB,iBAAiB;IACpD,OAAO,MAAM,gMAAA,EAAoB,4BAA4B;QAC3D,GAAG,OAAA;mKACH,UAAA;IACF,CAAC;AACH;AAyFA,MAAM,mBAAgE;IAWpE,YAAY,UAAA,EAAwB,IAAA,CAAc;QAPlD;;KAAA,GAAA,cAAA,IAAA,EAAA;QAKA;;KAAA,GAAA,cAAA,IAAA,EAAA;QAGE,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,KAAA,GAAQ;QACb,CAAA,GAAA,yKAAA,CAAA,mBAAA,EAAiB,IAAA,EAAM,CAAA,0BAAA,EAA6B,IAAI,EAAE;IAC5D;IAEA,IAAI,UAAkD;QACpD,OAAO,cAAc,IAAA,CAAK,KAAA,EAAO,CAAC,CAAC;IACrC;AACF;AAEA,SAAS,cAAc,IAAA,EAAc,SAAA,EAA0B;IAC7D,MAAM,UAA6B;QACjC,KAAI,CAAA,EAAG,IAAA,EAAuB;YAC5B,IAAI,OAAO,SAAS,UAAU;gBAC5B,MAAM,WAAW,CAAC;uBAAG;oBAAW,IAAI;iBAAA;gBACpC,OAAO,cAAc,MAAM,QAAQ;YACrC,OAAA,IAAW,mLAAS,kBAAA,EAAiB;gBACnC,IAAI,YAAY,CAAA,0BAAA,EAA6B,IAAI,EAAA;gBACjD,KAAA,MAAW,QAAQ,UAAW;oBAC5B,aAAa,CAAA,CAAA,EAAI,IAAI,EAAA;gBACvB;gBACA,OAAO;YACT,OAAO;gBACL,OAAO,KAAA;YACT;QACF;IACF;IACA,OAAO,IAAI,MAAM,CAAC,GAAG,OAAO;AAC9B;AAEA,SAAS,IAEP,UAAA,EACA,OAAA,EAGgC;IAEhC,MAAM,8BACJ;IACF,IAAI,OAAO,4BAA4B,uBAAA,KAA4B,UAAU;QAC3E,MAAM,IAAI,MACR;IAEJ;IACA,MAAM,OACJ,SAAS,QAAA,iBAAA;IAET,4BAA4B,WAAA,IAAA,qCAAA;IAE5B,4BAA4B,uBAAA,CAAwB,KAAA,CAAM,GAAG,EAAE,GAAA,CAAI;IACrE,IAAA,CAAK,gBAAA,CAAiB,IAAA,CAAK;QAAC;QAAM;QAA6B,CAAC,CAAC;KAAC;IAClE,OAAO,IAAI,mBAAmB,YAAY,IAAI;AAChD;AAgBA,SAAS,uBAEgB;IACvB,MAAM,iBAAiB;QAAE,MAAM;IAAe;IAC9C,MAAM,kBAAkB,yBAAyB,IAAA,CAAK,gBAAgB;IACtE,OAAO;QACL;QACA;QACA,YAAY,CAAC;QACb,SAAS,oBAAoB,IAAA,CAAK,WAAW;IAC/C;AACF;AAEA,SAAS,oBAAoB,IAAA,EAAuB;IAClD,MAAM,SAAgB,CAAC,CAAA;IACvB,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,IAAI,EAAG;QAC/C,IAAI;QACJ,IAAI,OAAO,UAAU,UAAU;YAC7B,OAAO;gBAAE,MAAM;gBAAQ,MAAM;YAAM;QACrC,OAAO;YACL,OAAO,oBAAoB,KAAK;QAClC;QACA,OAAO,IAAA,CAAK;YAAC;YAAK,IAAI;SAAC;IACzB;IACA,OAAO;QAAE,MAAM;QAAU;IAAO;AAClC;AAEA,SAAS,yBACP,eAAA,EASE;IACF,OAAO,gBAAgB,GAAA,CAAI,CAAC,CAAC,MAAM,YAAY,CAAC,CAAA,KAAM;QACpD,IAAI,OAA4D;QAChE,IAAI,MAAM,MAAM;YACd,OAAO,CAAC,CAAA;YACR,KAAA,MAAW,CAACA,OAAM,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,CAAC,EAAG;gBAC7C,IAAI,UAAU,KAAA,GAAW;oBACvB,KAAK,IAAA,CAAK;wBACRA;wBACA;4BAAE,MAAM;4BAAS,OAAO,KAAK,SAAA,iKAAU,eAAA,EAAa,KAAK,CAAC;wBAAE;qBAC7D;gBACH;YACF;QACF;QAEA,MAAM,OAAO,WAAW,uBAAA;QACxB,IAAI,CAAC,MACH,MAAM,IAAI,MACR,gDACE,KAAK,SAAA,CAAU,YAAY,MAAM,CAAC;QAGxC,OAAO;YACL;YACA;YACA;QACF;IACF,CAAC;AACH;AAEA,SAAS,6BAEsB;IAC7B,MAAM,OAAqD,OAAO,OAAA,CAChE,IAAA,CAAK,KAAA,EACL,GAAA,CAAI,CAAC,CAAC,MAAM,SAAS,CAAA,GAAM;YAC3B;YACA;gBACE,MAAM;gBACN,OAAO,KAAK,SAAA,CAAU,UAAU,IAAI;YACtC;SACD;IACD,MAAM,iBAA0C;QAC9C,MAAM;QACN,MAAM,IAAA,CAAK,KAAA;QACX;IACF;IACA,MAAM,kBAAkB,yBAAyB,IAAA,CAAK,gBAAgB;IACtE,OAAO;QACL,MAAM,IAAA,CAAK,KAAA;QACX;QACA;QACA,YAAY,CAAC;QACb,SAAS,oBAAoB,IAAA,CAAK,WAAW;IAC/C;AACF;AA0BO,SAAS,gBACd,IAAA,EAC8B;IAC9B,MAAM,MAAkC;QACtC,SAAS;QACT,OAAO;QACP,OAAO,CAAC;QACR,kBAAkB,CAAC,CAAA;QACnB,aAAa,CAAC;QACd,kBAAkB,CAAC;QAEnB,QAAQ;QACR;QAAA,8EAAA;QAGA,GAAI,CAAC,CAAA;IACP;IACA,OAAO;AACT;AAQO,SAAS,YAA2B;IACzC,MAAM,MAA4B;QAChC,SAAS;QACT,kBAAkB,CAAC,CAAA;QACnB,aAAa,CAAC;QAEd,QAAQ;QACR;IACF;IACA,OAAO;AACT;AAYO,SAAS,4BACd,WAAA,EACuB;IACvB,OAAO;QACL,2KAAC,kBAAe,CAAA,EAAG,CAAA,uCAAA,EAA0C,WAAW,EAAA;IAC1E;AACF;AAEA,SAAS,sBACP,IAAA,EACA,SAAA,EACoB;IACpB,MAAM,UAAgC;QACpC,KAAI,CAAA,EAAG,IAAA,EAAuB;YAC5B,IAAI,OAAO,SAAS,UAAU;gBAC5B,MAAM,WAAW,CAAC;uBAAG;oBAAW,IAAI;iBAAA;gBACpC,OAAO,sBAAsB,MAAM,QAAQ;YAC7C,OAAA,IAAW,mLAAS,kBAAA,EAAiB;gBACnC,IAAI,UAAU,MAAA,GAAS,GAAG;oBACxB,MAAM,QAAQ;wBAAC,MAAM;2BAAG,SAAS;qBAAA,CAAE,IAAA,CAAK,GAAG;oBAC3C,MAAM,IAAI,MACR,CAAA,yCAAA,EAA4C,IAAI,CAAA,yCAAA,EAA4C,KAAK,CAAA,EAAA,CAAA;gBAErG;gBACA,OAAO,CAAA,0BAAA,CAAA,GAA+B,UAAU,IAAA,CAAK,GAAG;YAC1D,OAAO;gBACL,OAAO,KAAA;YACT;QACF;IACF;IACA,OAAO,IAAI,MAAM,CAAC,GAAG,OAAO;AAC9B;AAEO,MAAM,oBAAoB,IAAM,sBAAsB,cAAc,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2303, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/schema.ts"], "sourcesContent": ["/**\n * Utilities for defining the schema of your Convex project.\n *\n * ## Usage\n *\n * Schemas should be placed in a `schema.ts` file in your `convex/` directory.\n *\n * Schema definitions should be built using {@link defineSchema},\n * {@link defineTable}, and {@link values.v}. Make sure to export the schema as the\n * default export.\n *\n * ```ts\n * import { defineSchema, defineTable } from \"convex/server\";\n * import { v } from \"convex/values\";\n *\n *  export default defineSchema({\n *    messages: defineTable({\n *      body: v.string(),\n *      user: v.id(\"users\"),\n *    }),\n *    users: defineTable({\n *      name: v.string(),\n *    }),\n *  });\n * ```\n *\n * To learn more about schemas, see [Defining a Schema](https://docs.convex.dev/using/schemas).\n * @module\n */\nimport {\n  AnyDataModel,\n  GenericDataModel,\n  GenericTableIndexes,\n  GenericTableSearchIndexes,\n  GenericTableVectorIndexes,\n  TableNamesInDataModel,\n} from \"../server/data_model.js\";\nimport {\n  <PERSON><PERSON><PERSON><PERSON>,\n  IndexTiebreakerField,\n  SystemFields,\n  SystemIndexes,\n} from \"../server/system_fields.js\";\nimport { Expand } from \"../type_utils.js\";\nimport {\n  GenericValidator,\n  ObjectType,\n  isValidator,\n  v,\n} from \"../values/validator.js\";\nimport { VObject, Validator } from \"../values/validators.js\";\n\n/**\n * Extract all of the index field paths within a {@link Validator}.\n *\n * This is used within {@link defineTable}.\n * @public\n */\ntype ExtractFieldPaths<T extends Validator<any, any, any>> =\n  // Add in the system fields available in index definitions.\n  // This should be everything except for `_id` because thats added to indexes\n  // automatically.\n  T[\"fieldPaths\"] | keyof SystemFields;\n\n/**\n * Extract the {@link GenericDocument} within a {@link Validator} and\n * add on the system fields.\n *\n * This is used within {@link defineTable}.\n * @public\n */\ntype ExtractDocument<T extends Validator<any, any, any>> =\n  // Add the system fields to `Value` (except `_id` because it depends on\n  //the table name) and trick TypeScript into expanding them.\n  Expand<SystemFields & T[\"type\"]>;\n\n/**\n * The configuration for a full text search index.\n *\n * @public\n */\nexport interface SearchIndexConfig<\n  SearchField extends string,\n  FilterFields extends string,\n> {\n  /**\n   * The field to index for full text search.\n   *\n   * This must be a field of type `string`.\n   */\n  searchField: SearchField;\n\n  /**\n   * Additional fields to index for fast filtering when running search queries.\n   */\n  filterFields?: FilterFields[];\n}\n\n/**\n * The configuration for a vector index.\n *\n * @public\n */\nexport interface VectorIndexConfig<\n  VectorField extends string,\n  FilterFields extends string,\n> {\n  /**\n   * The field to index for vector search.\n   *\n   * This must be a field of type `v.array(v.float64())` (or a union)\n   */\n  vectorField: VectorField;\n  /**\n   * The length of the vectors indexed. This must be between 2 and 2048 inclusive.\n   */\n  dimensions: number;\n  /**\n   * Additional fields to index for fast filtering when running vector searches.\n   */\n  filterFields?: FilterFields[];\n}\n\n/**\n * @internal\n */\nexport type VectorIndex = {\n  indexDescriptor: string;\n  vectorField: string;\n  dimensions: number;\n  filterFields: string[];\n};\n\n/**\n * @internal\n */\nexport type Index = {\n  indexDescriptor: string;\n  fields: string[];\n};\n\n/**\n * @internal\n */\nexport type SearchIndex = {\n  indexDescriptor: string;\n  searchField: string;\n  filterFields: string[];\n};\n/**\n * The definition of a table within a schema.\n *\n * This should be produced by using {@link defineTable}.\n * @public\n */\nexport class TableDefinition<\n  DocumentType extends Validator<any, any, any> = Validator<any, any, any>,\n  Indexes extends GenericTableIndexes = {},\n  SearchIndexes extends GenericTableSearchIndexes = {},\n  VectorIndexes extends GenericTableVectorIndexes = {},\n> {\n  private indexes: Index[];\n  private searchIndexes: SearchIndex[];\n  private vectorIndexes: VectorIndex[];\n  // The type of documents stored in this table.\n  validator: DocumentType;\n\n  /**\n   * @internal\n   */\n  constructor(documentType: DocumentType) {\n    this.indexes = [];\n    this.searchIndexes = [];\n    this.vectorIndexes = [];\n    this.validator = documentType;\n  }\n\n  /**\n   * Define an index on this table.\n   *\n   * To learn about indexes, see [Defining Indexes](https://docs.convex.dev/using/indexes).\n   *\n   * @param name - The name of the index.\n   * @param fields - The fields to index, in order. Must specify at least one\n   * field.\n   * @returns A {@link TableDefinition} with this index included.\n   */\n  index<\n    IndexName extends string,\n    FirstFieldPath extends ExtractFieldPaths<DocumentType>,\n    RestFieldPaths extends ExtractFieldPaths<DocumentType>[],\n  >(\n    name: IndexName,\n    fields: [FirstFieldPath, ...RestFieldPaths],\n  ): TableDefinition<\n    DocumentType,\n    // Update `Indexes` to include the new index and use `Expand` to make the\n    // types look pretty in editors.\n    Expand<\n      Indexes &\n        Record<\n          IndexName,\n          [FirstFieldPath, ...RestFieldPaths, IndexTiebreakerField]\n        >\n    >,\n    SearchIndexes,\n    VectorIndexes\n  > {\n    this.indexes.push({ indexDescriptor: name, fields });\n    return this;\n  }\n\n  /**\n   * Define a search index on this table.\n   *\n   * To learn about search indexes, see [Search](https://docs.convex.dev/text-search).\n   *\n   * @param name - The name of the index.\n   * @param indexConfig - The search index configuration object.\n   * @returns A {@link TableDefinition} with this search index included.\n   */\n  searchIndex<\n    IndexName extends string,\n    SearchField extends ExtractFieldPaths<DocumentType>,\n    FilterFields extends ExtractFieldPaths<DocumentType> = never,\n  >(\n    name: IndexName,\n    indexConfig: Expand<SearchIndexConfig<SearchField, FilterFields>>,\n  ): TableDefinition<\n    DocumentType,\n    Indexes,\n    // Update `SearchIndexes` to include the new index and use `Expand` to make\n    // the types look pretty in editors.\n    Expand<\n      SearchIndexes &\n        Record<\n          IndexName,\n          {\n            searchField: SearchField;\n            filterFields: FilterFields;\n          }\n        >\n    >,\n    VectorIndexes\n  > {\n    this.searchIndexes.push({\n      indexDescriptor: name,\n      searchField: indexConfig.searchField,\n      filterFields: indexConfig.filterFields || [],\n    });\n    return this;\n  }\n\n  /**\n   * Define a vector index on this table.\n   *\n   * To learn about vector indexes, see [Vector Search](https://docs.convex.dev/vector-search).\n   *\n   * @param name - The name of the index.\n   * @param indexConfig - The vector index configuration object.\n   * @returns A {@link TableDefinition} with this vector index included.\n   */\n  vectorIndex<\n    IndexName extends string,\n    VectorField extends ExtractFieldPaths<DocumentType>,\n    FilterFields extends ExtractFieldPaths<DocumentType> = never,\n  >(\n    name: IndexName,\n    indexConfig: Expand<VectorIndexConfig<VectorField, FilterFields>>,\n  ): TableDefinition<\n    DocumentType,\n    Indexes,\n    SearchIndexes,\n    Expand<\n      VectorIndexes &\n        Record<\n          IndexName,\n          {\n            vectorField: VectorField;\n            dimensions: number;\n            filterFields: FilterFields;\n          }\n        >\n    >\n  > {\n    this.vectorIndexes.push({\n      indexDescriptor: name,\n      vectorField: indexConfig.vectorField,\n      dimensions: indexConfig.dimensions,\n      filterFields: indexConfig.filterFields || [],\n    });\n    return this;\n  }\n\n  /**\n   * Work around for https://github.com/microsoft/TypeScript/issues/57035\n   */\n  protected self(): TableDefinition<\n    DocumentType,\n    Indexes,\n    SearchIndexes,\n    VectorIndexes\n  > {\n    return this;\n  }\n  /**\n   * Export the contents of this definition.\n   *\n   * This is called internally by the Convex framework.\n   * @internal\n   */\n  export() {\n    const documentType = this.validator.json;\n    if (typeof documentType !== \"object\") {\n      throw new Error(\n        \"Invalid validator: please make sure that the parameter of `defineTable` is valid (see https://docs.convex.dev/database/schemas)\",\n      );\n    }\n\n    return {\n      indexes: this.indexes,\n      searchIndexes: this.searchIndexes,\n      vectorIndexes: this.vectorIndexes,\n      documentType,\n    };\n  }\n}\n\n/**\n * Define a table in a schema.\n *\n * You can either specify the schema of your documents as an object like\n * ```ts\n * defineTable({\n *   field: v.string()\n * });\n * ```\n *\n * or as a schema type like\n * ```ts\n * defineTable(\n *  v.union(\n *    v.object({...}),\n *    v.object({...})\n *  )\n * );\n * ```\n *\n * @param documentSchema - The type of documents stored in this table.\n * @returns A {@link TableDefinition} for the table.\n *\n * @public\n */\nexport function defineTable<\n  DocumentSchema extends Validator<Record<string, any>, \"required\", any>,\n>(documentSchema: DocumentSchema): TableDefinition<DocumentSchema>;\n/**\n * Define a table in a schema.\n *\n * You can either specify the schema of your documents as an object like\n * ```ts\n * defineTable({\n *   field: v.string()\n * });\n * ```\n *\n * or as a schema type like\n * ```ts\n * defineTable(\n *  v.union(\n *    v.object({...}),\n *    v.object({...})\n *  )\n * );\n * ```\n *\n * @param documentSchema - The type of documents stored in this table.\n * @returns A {@link TableDefinition} for the table.\n *\n * @public\n */\nexport function defineTable<\n  DocumentSchema extends Record<string, GenericValidator>,\n>(\n  documentSchema: DocumentSchema,\n): TableDefinition<VObject<ObjectType<DocumentSchema>, DocumentSchema>>;\nexport function defineTable<\n  DocumentSchema extends\n    | Validator<Record<string, any>, \"required\", any>\n    | Record<string, GenericValidator>,\n>(documentSchema: DocumentSchema): TableDefinition<any, any, any> {\n  if (isValidator(documentSchema)) {\n    return new TableDefinition(documentSchema);\n  } else {\n    return new TableDefinition(v.object(documentSchema));\n  }\n}\n\n/**\n * A type describing the schema of a Convex project.\n *\n * This should be constructed using {@link defineSchema}, {@link defineTable},\n * and {@link v}.\n * @public\n */\nexport type GenericSchema = Record<string, TableDefinition>;\n\n/**\n *\n * The definition of a Convex project schema.\n *\n * This should be produced by using {@link defineSchema}.\n * @public\n */\nexport class SchemaDefinition<\n  Schema extends GenericSchema,\n  StrictTableTypes extends boolean,\n> {\n  public tables: Schema;\n  public strictTableNameTypes!: StrictTableTypes;\n  private readonly schemaValidation: boolean;\n\n  /**\n   * @internal\n   */\n  constructor(tables: Schema, options?: DefineSchemaOptions<StrictTableTypes>) {\n    this.tables = tables;\n    this.schemaValidation =\n      options?.schemaValidation === undefined ? true : options.schemaValidation;\n  }\n\n  /**\n   * Export the contents of this definition.\n   *\n   * This is called internally by the Convex framework.\n   * @internal\n   */\n  export(): string {\n    return JSON.stringify({\n      tables: Object.entries(this.tables).map(([tableName, definition]) => {\n        const { indexes, searchIndexes, vectorIndexes, documentType } =\n          definition.export();\n        return {\n          tableName,\n          indexes,\n          searchIndexes,\n          vectorIndexes,\n          documentType,\n        };\n      }),\n      schemaValidation: this.schemaValidation,\n    });\n  }\n}\n\n/**\n * Options for {@link defineSchema}.\n *\n * @public\n */\nexport interface DefineSchemaOptions<StrictTableNameTypes extends boolean> {\n  /**\n   * Whether Convex should validate at runtime that all documents match\n   * your schema.\n   *\n   * If `schemaValidation` is `true`, Convex will:\n   * 1. Check that all existing documents match your schema when your schema\n   * is pushed.\n   * 2. Check that all insertions and updates match your schema during mutations.\n   *\n   * If `schemaValidation` is `false`, Convex will not validate that new or\n   * existing documents match your schema. You'll still get schema-specific\n   * TypeScript types, but there will be no validation at runtime that your\n   * documents match those types.\n   *\n   * By default, `schemaValidation` is `true`.\n   */\n  schemaValidation?: boolean;\n\n  /**\n   * Whether the TypeScript types should allow accessing tables not in the schema.\n   *\n   * If `strictTableNameTypes` is `true`, using tables not listed in the schema\n   * will generate a TypeScript compilation error.\n   *\n   * If `strictTableNameTypes` is `false`, you'll be able to access tables not\n   * listed in the schema and their document type will be `any`.\n   *\n   * `strictTableNameTypes: false` is useful for rapid prototyping.\n   *\n   * Regardless of the value of `strictTableNameTypes`, your schema will only\n   * validate documents in the tables listed in the schema. You can still create\n   * and modify other tables on the dashboard or in JavaScript mutations.\n   *\n   * By default, `strictTableNameTypes` is `true`.\n   */\n  strictTableNameTypes?: StrictTableNameTypes;\n}\n\n/**\n * Define the schema of this Convex project.\n *\n * This should be exported from a `schema.ts` file in your `convex/` directory\n * like:\n *\n * ```ts\n * export default defineSchema({\n *   ...\n * });\n * ```\n *\n * @param schema - A map from table name to {@link TableDefinition} for all of\n * the tables in this project.\n * @param options - Optional configuration. See {@link DefineSchemaOptions} for\n * a full description.\n * @returns The schema.\n *\n * @public\n */\nexport function defineSchema<\n  Schema extends GenericSchema,\n  StrictTableNameTypes extends boolean = true,\n>(\n  schema: Schema,\n  options?: DefineSchemaOptions<StrictTableNameTypes>,\n): SchemaDefinition<Schema, StrictTableNameTypes> {\n  return new SchemaDefinition(schema, options);\n}\n\n/**\n * Internal type used in Convex code generation!\n *\n * Convert a {@link SchemaDefinition} into a {@link server.GenericDataModel}.\n *\n * @public\n */\nexport type DataModelFromSchemaDefinition<\n  SchemaDef extends SchemaDefinition<any, boolean>,\n> = MaybeMakeLooseDataModel<\n  {\n    [TableName in keyof SchemaDef[\"tables\"] &\n      string]: SchemaDef[\"tables\"][TableName] extends TableDefinition<\n      infer DocumentType,\n      infer Indexes,\n      infer SearchIndexes,\n      infer VectorIndexes\n    >\n      ? {\n          // We've already added all of the system fields except for `_id`.\n          // Add that here.\n          document: Expand<IdField<TableName> & ExtractDocument<DocumentType>>;\n          fieldPaths:\n            | keyof IdField<TableName>\n            | ExtractFieldPaths<DocumentType>;\n          indexes: Expand<Indexes & SystemIndexes>;\n          searchIndexes: SearchIndexes;\n          vectorIndexes: VectorIndexes;\n        }\n      : never;\n  },\n  SchemaDef[\"strictTableNameTypes\"]\n>;\n\ntype MaybeMakeLooseDataModel<\n  DataModel extends GenericDataModel,\n  StrictTableNameTypes extends boolean,\n> = StrictTableNameTypes extends true\n  ? DataModel\n  : Expand<DataModel & AnyDataModel>;\n\nconst _systemSchema = defineSchema({\n  _scheduled_functions: defineTable({\n    name: v.string(),\n    args: v.array(v.any()),\n    scheduledTime: v.float64(),\n    completedTime: v.optional(v.float64()),\n    state: v.union(\n      v.object({ kind: v.literal(\"pending\") }),\n      v.object({ kind: v.literal(\"inProgress\") }),\n      v.object({ kind: v.literal(\"success\") }),\n      v.object({ kind: v.literal(\"failed\"), error: v.string() }),\n      v.object({ kind: v.literal(\"canceled\") }),\n    ),\n  }),\n  _storage: defineTable({\n    sha256: v.string(),\n    size: v.float64(),\n    contentType: v.optional(v.string()),\n  }),\n});\n\nexport interface SystemDataModel\n  extends DataModelFromSchemaDefinition<typeof _systemSchema> {}\n\nexport type SystemTableNames = TableNamesInDataModel<SystemDataModel>;\n"], "names": [], "mappings": ";;;;;;AA4CA;;;;;;;;;;;AA+GO,MAAM,gBAKX;IAAA;;GAAA,GAUA,YAAY,YAAA,CAA4B;QATxC,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QAER,8CAAA;QAAA,cAAA,IAAA,EAAA;QAME,IAAA,CAAK,OAAA,GAAU,CAAC,CAAA;QAChB,IAAA,CAAK,aAAA,GAAgB,CAAC,CAAA;QACtB,IAAA,CAAK,aAAA,GAAgB,CAAC,CAAA;QACtB,IAAA,CAAK,SAAA,GAAY;IACnB;IAAA;;;;;;;;;GAAA,GAYA,MAKE,IAAA,EACA,MAAA,EAcA;QACA,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK;YAAE,iBAAiB;YAAM;QAAO,CAAC;QACnD,OAAO,IAAA;IACT;IAAA;;;;;;;;GAAA,GAWA,YAKE,IAAA,EACA,WAAA,EAiBA;QACA,IAAA,CAAK,aAAA,CAAc,IAAA,CAAK;YACtB,iBAAiB;YACjB,aAAa,YAAY,WAAA;YACzB,cAAc,YAAY,YAAA,IAAgB,CAAC,CAAA;QAC7C,CAAC;QACD,OAAO,IAAA;IACT;IAAA;;;;;;;;GAAA,GAWA,YAKE,IAAA,EACA,WAAA,EAgBA;QACA,IAAA,CAAK,aAAA,CAAc,IAAA,CAAK;YACtB,iBAAiB;YACjB,aAAa,YAAY,WAAA;YACzB,YAAY,YAAY,UAAA;YACxB,cAAc,YAAY,YAAA,IAAgB,CAAC,CAAA;QAC7C,CAAC;QACD,OAAO,IAAA;IACT;IAAA;;GAAA,GAKU,OAKR;QACA,OAAO,IAAA;IACT;IAAA;;;;;GAAA,GAOA,SAAS;QACP,MAAM,eAAe,IAAA,CAAK,SAAA,CAAU,IAAA;QACpC,IAAI,OAAO,iBAAiB,UAAU;YACpC,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;YACL,SAAS,IAAA,CAAK,OAAA;YACd,eAAe,IAAA,CAAK,aAAA;YACpB,eAAe,IAAA,CAAK,aAAA;YACpB;QACF;IACF;AACF;AA4DO,SAAS,YAId,cAAA,EAAgE;IAChE,wKAAI,cAAA,EAAY,cAAc,GAAG;QAC/B,OAAO,IAAI,gBAAgB,cAAc;IAC3C,OAAO;QACL,OAAO,IAAI,gLAAgB,IAAA,CAAE,MAAA,CAAO,cAAc,CAAC;IACrD;AACF;AAkBO,MAAM,iBAGX;IAAA;;GAAA,GAQA,YAAY,MAAA,EAAgB,OAAA,CAAiD;QAP7E,cAAA,IAAA,EAAO;QACP,cAAA,IAAA,EAAO;QACP,cAAA,IAAA,EAAiB;QAMf,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,gBAAA,GACH,SAAS,qBAAqB,KAAA,IAAY,OAAO,QAAQ,gBAAA;IAC7D;IAAA;;;;;GAAA,GAQA,SAAiB;QACf,OAAO,KAAK,SAAA,CAAU;YACpB,QAAQ,OAAO,OAAA,CAAQ,IAAA,CAAK,MAAM,EAAE,GAAA,CAAI,CAAC,CAAC,WAAW,UAAU,CAAA,KAAM;gBACnE,MAAM,EAAE,OAAA,EAAS,aAAA,EAAe,aAAA,EAAe,YAAA,CAAa,CAAA,GAC1D,WAAW,MAAA,CAAO;gBACpB,OAAO;oBACL;oBACA;oBACA;oBACA;oBACA;gBACF;YACF,CAAC;YACD,kBAAkB,IAAA,CAAK,gBAAA;QACzB,CAAC;IACH;AACF;AAkEO,SAAS,aAId,MAAA,EACA,OAAA,EACgD;IAChD,OAAO,IAAI,iBAAiB,QAAQ,OAAO;AAC7C;AA2CA,MAAM,gBAAgB,aAAa;IACjC,sBAAsB,YAAY;QAChC,qKAAM,KAAA,CAAE,MAAA,CAAO;QACf,sKAAM,IAAA,CAAE,KAAA,iKAAM,IAAA,CAAE,GAAA,CAAI,CAAC;QACrB,+KAAe,IAAA,CAAE,OAAA,CAAQ;QACzB,+KAAe,IAAA,CAAE,QAAA,iKAAS,IAAA,CAAE,OAAA,CAAQ,CAAC;QACrC,uKAAO,IAAA,CAAE,KAAA,iKACP,IAAA,CAAE,MAAA,CAAO;YAAE,sKAAM,IAAA,CAAE,OAAA,CAAQ,SAAS;QAAE,CAAC,mKACvC,IAAA,CAAE,MAAA,CAAO;YAAE,sKAAM,IAAA,CAAE,OAAA,CAAQ,YAAY;QAAE,CAAC,mKAC1C,IAAA,CAAE,MAAA,CAAO;YAAE,sKAAM,IAAA,CAAE,OAAA,CAAQ,SAAS;QAAE,CAAC,mKACvC,IAAA,CAAE,MAAA,CAAO;YAAE,sKAAM,IAAA,CAAE,OAAA,CAAQ,QAAQ;YAAG,uKAAO,IAAA,CAAE,MAAA,CAAO;QAAE,CAAC,GACzD,oKAAA,CAAE,MAAA,CAAO;YAAE,sKAAM,IAAA,CAAE,OAAA,CAAQ,UAAU;QAAE,CAAC;IAE5C,CAAC;IACD,UAAU,YAAY;QACpB,wKAAQ,IAAA,CAAE,MAAA,CAAO;QACjB,sKAAM,IAAA,CAAE,OAAA,CAAQ;QAChB,6KAAa,IAAA,CAAE,QAAA,iKAAS,IAAA,CAAE,MAAA,CAAO,CAAC;IACpC,CAAC;AACH,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2478, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/index.ts"], "sourcesContent": ["/**\n * Utilities for implementing server-side Convex query and mutation functions.\n *\n * ## Usage\n *\n * ### Code Generation\n *\n * This module is typically used alongside generated server code.\n *\n * To generate the server code, run `npx convex dev` in your Convex project.\n * This will create a `convex/_generated/server.js` file with the following\n * functions, typed for your schema:\n * - [query](https://docs.convex.dev/generated-api/server#query)\n * - [mutation](https://docs.convex.dev/generated-api/server#mutation)\n *\n * If you aren't using TypeScript and code generation, you can use these untyped\n * functions instead:\n * - {@link queryGeneric}\n * - {@link mutationGeneric}\n *\n * ### Example\n *\n * Convex functions are defined by using either the `query` or\n * `mutation` wrappers.\n *\n * Queries receive a `db` that implements the {@link GenericDatabaseReader} interface.\n *\n * ```js\n * import { query } from \"./_generated/server\";\n *\n * export default query({\n *   handler: async ({ db }, { arg1, arg2 }) => {\n *     // Your (read-only) code here!\n *   },\n * });\n * ```\n *\n * If your function needs to write to the database, such as inserting, updating,\n * or deleting documents, use `mutation` instead which provides a `db` that\n * implements the {@link GenericDatabaseWriter} interface.\n *\n * ```js\n * import { mutation } from \"./_generated/server\";\n *\n * export default mutation({\n *   handler: async ({ db }, { arg1, arg2 }) => {\n *     // Your mutation code here!\n *   },\n * });\n * ```\n * @module\n */\n\nexport type {\n  Auth,\n  UserIdentity,\n  UserIdentityAttributes,\n} from \"./authentication.js\";\nexport * from \"./database.js\";\nexport type {\n  GenericDocument,\n  GenericFieldPaths,\n  GenericIndexFields,\n  GenericTableIndexes,\n  GenericSearchIndexConfig,\n  GenericTableSearchIndexes,\n  GenericVectorIndexConfig,\n  GenericTableVectorIndexes,\n  FieldTypeFromFieldPath,\n  FieldTypeFromFieldPathInner,\n  GenericTableInfo,\n  DocumentByInfo,\n  FieldPaths,\n  Indexes,\n  IndexNames,\n  NamedIndex,\n  SearchIndexes,\n  SearchIndexNames,\n  NamedSearchIndex,\n  VectorIndexes,\n  VectorIndexNames,\n  NamedVectorIndex,\n  GenericDataModel,\n  AnyDataModel,\n  TableNamesInDataModel,\n  NamedTableInfo,\n  DocumentByName,\n} from \"./data_model.js\";\n\nexport type {\n  Expression,\n  ExpressionOrValue,\n  FilterBuilder,\n} from \"./filter_builder.js\";\nexport {\n  actionGeneric,\n  httpActionGeneric,\n  mutationGeneric,\n  queryGeneric,\n  internalActionGeneric,\n  internalMutationGeneric,\n  internalQueryGeneric,\n} from \"./impl/registration_impl.js\";\nexport type { IndexRange, IndexRangeBuilder } from \"./index_range_builder.js\";\nexport * from \"./pagination.js\";\nexport type { OrderedQuery, Query, QueryInitializer } from \"./query.js\";\nexport type {\n  ArgsArray,\n  DefaultFunctionArgs,\n  FunctionVisibility,\n  ActionBuilder,\n  MutationBuilder,\n  MutationBuilderWithTable,\n  QueryBuilder,\n  QueryBuilderWithTable,\n  HttpActionBuilder,\n  GenericActionCtx,\n  GenericMutationCtx,\n  GenericMutationCtxWithTable,\n  GenericQueryCtx,\n  GenericQueryCtxWithTable,\n  RegisteredAction,\n  RegisteredMutation,\n  RegisteredQuery,\n  PublicHttpAction,\n  UnvalidatedFunction,\n  ValidatedFunction,\n  ReturnValueForOptionalValidator,\n  ArgsArrayForOptionalValidator,\n  ArgsArrayToObject,\n  DefaultArgsForOptionalValidator,\n} from \"./registration.js\";\nexport * from \"./search_filter_builder.js\";\nexport * from \"./storage.js\";\nexport type { Scheduler, SchedulableFunctionReference } from \"./scheduler.js\";\nexport { cronJobs } from \"./cron.js\";\nexport type { CronJob, Crons } from \"./cron.js\";\nexport type {\n  SystemFields,\n  IdField,\n  WithoutSystemFields,\n  WithOptionalSystemFields,\n  SystemIndexes,\n  IndexTiebreakerField,\n} from \"./system_fields.js\";\nexport { httpRouter, HttpRouter, ROUTABLE_HTTP_METHODS } from \"./router.js\";\nexport type {\n  RoutableMethod,\n  RouteSpec,\n  RouteSpecWithPath,\n  RouteSpecWithPathPrefix,\n} from \"./router.js\";\nexport {\n  anyApi,\n  getFunctionName,\n  makeFunctionReference,\n  filterApi,\n} from \"./api.js\";\nexport type {\n  ApiFromModules,\n  AnyApi,\n  FilterApi,\n  FunctionType,\n  FunctionReference,\n  FunctionArgs,\n  OptionalRestArgs,\n  PartialApi,\n  ArgsAndOptions,\n  FunctionReturnType,\n} from \"./api.js\";\nexport {\n  defineApp,\n  defineComponent,\n  componentsGeneric,\n  createFunctionHandle,\n  type AnyChildComponents,\n} from \"./components/index.js\";\n/**\n * @internal\n */\nexport { currentSystemUdfInComponent } from \"./components/index.js\";\nexport { getFunctionAddress } from \"./components/index.js\";\nexport type {\n  ComponentDefinition,\n  AnyComponents,\n  FunctionHandle,\n} from \"./components/index.js\";\n\n/**\n * @internal\n */\nexport type { Index, SearchIndex, VectorIndex } from \"./schema.js\";\n\nexport type {\n  SearchIndexConfig,\n  VectorIndexConfig,\n  TableDefinition,\n  SchemaDefinition,\n  DefineSchemaOptions,\n  GenericSchema,\n  DataModelFromSchemaDefinition,\n  SystemDataModel,\n  SystemTableNames,\n} from \"./schema.js\";\nexport { defineTable, defineSchema } from \"./schema.js\";\n\nexport type {\n  VectorSearch,\n  VectorSearchQuery,\n  VectorFilterBuilder,\n  FilterExpression,\n} from \"./vector_search.js\";\n\n/**\n * @public\n */\nexport type { BetterOmit, Expand } from \"../type_utils.js\";\n"], "names": [], "mappings": ";AA0DA,cAAc;AAoCd;AAUA,cAAc;AA4Bd,cAAc;AACd,cAAc;AAEd,SAAS,gBAAgB;AAUzB,SAAS,YAAY,YAAY,6BAA6B;AAO9D;AAkBA;AAkCA,SAAS,aAAa,oBAAoB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2525, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2531, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/next/src/shared/lib/router/utils/querystring.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nexport function searchParamsToUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  const query: ParsedUrlQuery = {}\n  for (const [key, value] of searchParams.entries()) {\n    const existing = query[key]\n    if (typeof existing === 'undefined') {\n      query[key] = value\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      query[key] = [existing, value]\n    }\n  }\n  return query\n}\n\nfunction stringifyUrlQueryParam(param: unknown): string {\n  if (typeof param === 'string') {\n    return param\n  }\n\n  if (\n    (typeof param === 'number' && !isNaN(param)) ||\n    typeof param === 'boolean'\n  ) {\n    return String(param)\n  } else {\n    return ''\n  }\n}\n\nexport function urlQueryToSearchParams(query: ParsedUrlQuery): URLSearchParams {\n  const searchParams = new URLSearchParams()\n  for (const [key, value] of Object.entries(query)) {\n    if (Array.isArray(value)) {\n      for (const item of value) {\n        searchParams.append(key, stringifyUrlQueryParam(item))\n      }\n    } else {\n      searchParams.set(key, stringifyUrlQueryParam(value))\n    }\n  }\n  return searchParams\n}\n\nexport function assign(\n  target: URLSearchParams,\n  ...searchParamsList: URLSearchParams[]\n): URLSearchParams {\n  for (const searchParams of searchParamsList) {\n    for (const key of searchParams.keys()) {\n      target.delete(key)\n    }\n\n    for (const [key, value] of searchParams.entries()) {\n      target.append(key, value)\n    }\n  }\n\n  return target\n}\n"], "names": ["assign", "searchParamsToUrlQuery", "urlQueryToSearchParams", "searchParams", "query", "key", "value", "entries", "existing", "Array", "isArray", "push", "stringifyUrlQueryParam", "param", "isNaN", "String", "URLSearchParams", "Object", "item", "append", "set", "target", "searchParamsList", "keys", "delete"], "mappings": ";;;;;;;;;;;;;;;;IAgDgBA,MAAM,EAAA;eAANA;;IA9CAC,sBAAsB,EAAA;eAAtBA;;IAgCAC,sBAAsB,EAAA;eAAtBA;;;AAhCT,SAASD,uBACdE,YAA6B;IAE7B,MAAMC,QAAwB,CAAC;IAC/B,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;QACjD,MAAMC,WAAWJ,KAAK,CAACC,IAAI;QAC3B,IAAI,OAAOG,aAAa,aAAa;YACnCJ,KAAK,CAACC,IAAI,GAAGC;QACf,OAAO,IAAIG,MAAMC,OAAO,CAACF,WAAW;YAClCA,SAASG,IAAI,CAACL;QAChB,OAAO;YACLF,KAAK,CAACC,IAAI,GAAG;gBAACG;gBAAUF;aAAM;QAChC;IACF;IACA,OAAOF;AACT;AAEA,SAASQ,uBAAuBC,KAAc;IAC5C,IAAI,OAAOA,UAAU,UAAU;QAC7B,OAAOA;IACT;IAEA,IACG,OAAOA,UAAU,YAAY,CAACC,MAAMD,UACrC,OAAOA,UAAU,WACjB;QACA,OAAOE,OAAOF;IAChB,OAAO;QACL,OAAO;IACT;AACF;AAEO,SAASX,uBAAuBE,KAAqB;IAC1D,MAAMD,eAAe,IAAIa;IACzB,KAAK,MAAM,CAACX,KAAKC,MAAM,IAAIW,OAAOV,OAAO,CAACH,OAAQ;QAChD,IAAIK,MAAMC,OAAO,CAACJ,QAAQ;YACxB,KAAK,MAAMY,QAAQZ,MAAO;gBACxBH,aAAagB,MAAM,CAACd,KAAKO,uBAAuBM;YAClD;QACF,OAAO;YACLf,aAAaiB,GAAG,CAACf,KAAKO,uBAAuBN;QAC/C;IACF;IACA,OAAOH;AACT;AAEO,SAASH,OACdqB,MAAuB;IACvB,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,mBAAH,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,IAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,gBAAAA,CAAH,OAAA,EAAA,GAAA,SAAA,CAAA,KAAsC;;IAEtC,KAAK,MAAMnB,gBAAgBmB,iBAAkB;QAC3C,KAAK,MAAMjB,OAAOF,aAAaoB,IAAI,GAAI;YACrCF,OAAOG,MAAM,CAACnB;QAChB;QAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;YACjDc,OAAOF,MAAM,CAACd,KAAKC;QACrB;IACF;IAEA,OAAOe;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2616, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/next/src/shared/lib/router/utils/format-url.ts"], "sourcesContent": ["// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nimport type { UrlObject } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport * as querystring from './querystring'\n\nconst slashedProtocols = /https?|ftp|gopher|file/\n\nexport function formatUrl(urlObj: UrlObject) {\n  let { auth, hostname } = urlObj\n  let protocol = urlObj.protocol || ''\n  let pathname = urlObj.pathname || ''\n  let hash = urlObj.hash || ''\n  let query = urlObj.query || ''\n  let host: string | false = false\n\n  auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : ''\n\n  if (urlObj.host) {\n    host = auth + urlObj.host\n  } else if (hostname) {\n    host = auth + (~hostname.indexOf(':') ? `[${hostname}]` : hostname)\n    if (urlObj.port) {\n      host += ':' + urlObj.port\n    }\n  }\n\n  if (query && typeof query === 'object') {\n    query = String(querystring.urlQueryToSearchParams(query as ParsedUrlQuery))\n  }\n\n  let search = urlObj.search || (query && `?${query}`) || ''\n\n  if (protocol && !protocol.endsWith(':')) protocol += ':'\n\n  if (\n    urlObj.slashes ||\n    ((!protocol || slashedProtocols.test(protocol)) && host !== false)\n  ) {\n    host = '//' + (host || '')\n    if (pathname && pathname[0] !== '/') pathname = '/' + pathname\n  } else if (!host) {\n    host = ''\n  }\n\n  if (hash && hash[0] !== '#') hash = '#' + hash\n  if (search && search[0] !== '?') search = '?' + search\n\n  pathname = pathname.replace(/[?#]/g, encodeURIComponent)\n  search = search.replace('#', '%23')\n\n  return `${protocol}${host}${pathname}${search}${hash}`\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (!urlObjectKeys.includes(key)) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n"], "names": ["formatUrl", "formatWithValidation", "urlObjectKeys", "slashedProtocols", "url<PERSON>bj", "auth", "hostname", "protocol", "pathname", "hash", "query", "host", "encodeURIComponent", "replace", "indexOf", "port", "String", "querystring", "urlQueryToSearchParams", "search", "endsWith", "slashes", "test", "url", "process", "env", "NODE_ENV", "Object", "keys", "for<PERSON>ach", "key", "includes", "console", "warn"], "mappings": "AAAA,uCAAuC;AACvC,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAsEnCwB,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;IA9Df1B,SAAS,EAAA;eAATA;;IA6DAC,oBAAoB,EAAA;eAApBA;;IAfHC,aAAa,EAAA;eAAbA;;;;uEAlDgB;AAE7B,MAAMC,mBAAmB;AAElB,SAASH,UAAUI,MAAiB;IACzC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAE,GAAGF;IACzB,IAAIG,WAAWH,OAAOG,QAAQ,IAAI;IAClC,IAAIC,WAAWJ,OAAOI,QAAQ,IAAI;IAClC,IAAIC,OAAOL,OAAOK,IAAI,IAAI;IAC1B,IAAIC,QAAQN,OAAOM,KAAK,IAAI;IAC5B,IAAIC,OAAuB;IAE3BN,OAAOA,OAAOO,mBAAmBP,MAAMQ,OAAO,CAAC,QAAQ,OAAO,MAAM;IAEpE,IAAIT,OAAOO,IAAI,EAAE;QACfA,OAAON,OAAOD,OAAOO,IAAI;IAC3B,OAAO,IAAIL,UAAU;QACnBK,OAAON,OAAQ,CAAA,CAACC,SAASQ,OAAO,CAAC,OAAQ,MAAGR,WAAS,MAAKA,QAAO;QACjE,IAAIF,OAAOW,IAAI,EAAE;YACfJ,QAAQ,MAAMP,OAAOW,IAAI;QAC3B;IACF;IAEA,IAAIL,SAAS,OAAOA,UAAU,UAAU;QACtCA,QAAQM,OAAOC,aAAYC,sBAAsB,CAACR;IACpD;IAEA,IAAIS,SAASf,OAAOe,MAAM,IAAKT,SAAU,MAAGA,SAAY;IAExD,IAAIH,YAAY,CAACA,SAASa,QAAQ,CAAC,MAAMb,YAAY;IAErD,IACEH,OAAOiB,OAAO,IACZ,CAAA,CAACd,YAAYJ,iBAAiBmB,IAAI,CAACf,SAAQ,KAAMI,SAAS,OAC5D;QACAA,OAAO,OAAQA,CAAAA,QAAQ,EAAC;QACxB,IAAIH,YAAYA,QAAQ,CAAC,EAAE,KAAK,KAAKA,WAAW,MAAMA;IACxD,OAAO,IAAI,CAACG,MAAM;QAChBA,OAAO;IACT;IAEA,IAAIF,QAAQA,IAAI,CAAC,EAAE,KAAK,KAAKA,OAAO,MAAMA;IAC1C,IAAIU,UAAUA,MAAM,CAAC,EAAE,KAAK,KAAKA,SAAS,MAAMA;IAEhDX,WAAWA,SAASK,OAAO,CAAC,SAASD;IACrCO,SAASA,OAAON,OAAO,CAAC,KAAK;IAE7B,OAAQ,KAAEN,WAAWI,OAAOH,WAAWW,SAASV;AAClD;AAEO,MAAMP,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAASD,qBAAqBsB,GAAc;IACjD,wCAA4C;QAC1C,IAAIA,QAAQ,QAAQ,OAAOA,QAAQ,UAAU;YAC3CI,OAAOC,IAAI,CAACL,KAAKM,OAAO,CAAC,CAACC;gBACxB,IAAI,CAAC5B,cAAc6B,QAAQ,CAACD,MAAM;oBAChCE,QAAQC,IAAI,CACT,uDAAoDH;gBAEzD;YACF;QACF;IACF;IAEA,OAAO9B,UAAUuB;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2731, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/next/src/client/use-merged-ref.ts"], "sourcesContent": ["import { useCallback, useRef, type Ref } from 'react'\n\n// This is a compatibility hook to support React 18 and 19 refs.\n// In 19, a cleanup function from refs may be returned.\n// In 18, returning a cleanup function creates a warning.\n// Since we take userspace refs, we don't know ahead of time if a cleanup function will be returned.\n// This implements cleanup functions with the old behavior in 18.\n// We know refs are always called alternating with `null` and then `T`.\n// So a call with `null` means we need to call the previous cleanup functions.\nexport function useMergedRef<TElement>(\n  refA: Ref<TElement>,\n  refB: Ref<TElement>\n): Ref<TElement> {\n  const cleanupA = useRef<(() => void) | null>(null)\n  const cleanupB = useRef<(() => void) | null>(null)\n\n  // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n  // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n  // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n  // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n  // (because it hasn't been updated for React 19)\n  // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n  // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n  return useCallback(\n    (current: TElement | null): void => {\n      if (current === null) {\n        const cleanupFnA = cleanupA.current\n        if (cleanupFnA) {\n          cleanupA.current = null\n          cleanupFnA()\n        }\n        const cleanupFnB = cleanupB.current\n        if (cleanupFnB) {\n          cleanupB.current = null\n          cleanupFnB()\n        }\n      } else {\n        if (refA) {\n          cleanupA.current = applyRef(refA, current)\n        }\n        if (refB) {\n          cleanupB.current = applyRef(refB, current)\n        }\n      }\n    },\n    [refA, refB]\n  )\n}\n\nfunction applyRef<TElement>(\n  refA: NonNullable<Ref<TElement>>,\n  current: TElement\n) {\n  if (typeof refA === 'function') {\n    const cleanup = refA(current)\n    if (typeof cleanup === 'function') {\n      return cleanup\n    } else {\n      return () => refA(null)\n    }\n  } else {\n    refA.current = current\n    return () => {\n      refA.current = null\n    }\n  }\n}\n"], "names": ["useMergedRef", "refA", "refB", "cleanupA", "useRef", "cleanupB", "useCallback", "current", "cleanupFnA", "cleanupFnB", "applyRef", "cleanup"], "mappings": ";;;;+BASgBA,gBAAAA;;;eAAAA;;;uBAT8B;AASvC,SAASA,aACdC,IAAmB,EACnBC,IAAmB;IAEnB,MAAMC,WAAWC,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAC7C,MAAMC,WAAWD,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAE7C,mFAAmF;IACnF,yEAAyE;IACzE,iGAAiG;IACjG,8FAA8F;IAC9F,gDAAgD;IAChD,mGAAmG;IACnG,wFAAwF;IACxF,OAAOE,CAAAA,GAAAA,OAAAA,WAAW,EAChB,CAACC;QACC,IAAIA,YAAY,MAAM;YACpB,MAAMC,aAAaL,SAASI,OAAO;YACnC,IAAIC,YAAY;gBACdL,SAASI,OAAO,GAAG;gBACnBC;YACF;YACA,MAAMC,aAAaJ,SAASE,OAAO;YACnC,IAAIE,YAAY;gBACdJ,SAASE,OAAO,GAAG;gBACnBE;YACF;QACF,OAAO;YACL,IAAIR,MAAM;gBACRE,SAASI,OAAO,GAAGG,SAAST,MAAMM;YACpC;YACA,IAAIL,MAAM;gBACRG,SAASE,OAAO,GAAGG,SAASR,MAAMK;YACpC;QACF;IACF,GACA;QAACN;QAAMC;KAAK;AAEhB;AAEA,SAASQ,SACPT,IAAgC,EAChCM,OAAiB;IAEjB,IAAI,OAAON,SAAS,YAAY;QAC9B,MAAMU,UAAUV,KAAKM;QACrB,IAAI,OAAOI,YAAY,YAAY;YACjC,OAAOA;QACT,OAAO;YACL,OAAO,IAAMV,KAAK;QACpB;IACF,OAAO;QACLA,KAAKM,OAAO,GAAGA;QACf,OAAO;YACLN,KAAKM,OAAO,GAAG;QACjB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2804, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/next/src/shared/lib/utils.ts"], "sourcesContent": ["import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n"], "names": ["DecodeError", "MiddlewareNotFoundError", "MissingStaticPage", "NormalizeError", "PageNotFoundError", "SP", "ST", "WEB_VITALS", "execOnce", "getDisplayName", "getLocationOrigin", "getURL", "isAbsoluteUrl", "isResSent", "loadGetInitialProps", "normalizeRepeatedSlashes", "stringifyError", "fn", "used", "result", "args", "ABSOLUTE_URL_REGEX", "url", "test", "protocol", "hostname", "port", "window", "location", "href", "origin", "substring", "length", "Component", "displayName", "name", "res", "finished", "headersSent", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replace", "slice", "join", "App", "ctx", "process", "env", "NODE_ENV", "prototype", "getInitialProps", "message", "Error", "pageProps", "props", "Object", "keys", "console", "warn", "performance", "every", "method", "constructor", "page", "code", "error", "JSON", "stringify", "stack"], "mappings": "AA8WM+C,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsDlBjD,WAAW,EAAA;eAAXA;;IAoBAC,uBAAuB,EAAA;eAAvBA;;IAPAC,iBAAiB,EAAA;eAAjBA;;IAZAC,cAAc,EAAA;eAAdA;;IACAC,iBAAiB,EAAA;eAAjBA;;IATAC,EAAE,EAAA;eAAFA;;IACAC,EAAE,EAAA;eAAFA;;IAlXAC,UAAU,EAAA;eAAVA;;IAsQGC,QAAQ,EAAA;eAARA;;IA+BAC,cAAc,EAAA;eAAdA;;IAXAC,iBAAiB,EAAA;eAAjBA;;IAKAC,MAAM,EAAA;eAANA;;IAPHC,aAAa,EAAA;eAAbA;;IAmBGC,SAAS,EAAA;eAATA;;IAkBMC,mBAAmB,EAAA;eAAnBA;;IAdNC,wBAAwB,EAAA;eAAxBA;;IA+GAC,cAAc,EAAA;eAAdA;;;AA9ZT,MAAMT,aAAa;IAAC;IAAO;IAAO;IAAO;IAAO;IAAO;CAAO;AAsQ9D,SAASC,SACdS,EAAK;IAEL,IAAIC,OAAO;IACX,IAAIC;IAEJ,OAAQ;yCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QACV,IAAI,CAACF,MAAM;YACTA,OAAO;YACPC,SAASF,MAAMG;QACjB;QACA,OAAOD;IACT;AACF;AAEA,0DAA0D;AAC1D,gEAAgE;AAChE,MAAME,qBAAqB;AACpB,MAAMT,gBAAgB,CAACU,MAAgBD,mBAAmBE,IAAI,CAACD;AAE/D,SAASZ;IACd,MAAM,EAAEc,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGC,OAAOC,QAAQ;IACpD,OAAUJ,WAAS,OAAIC,WAAWC,CAAAA,OAAO,MAAMA,OAAO,EAAC;AACzD;AAEO,SAASf;IACd,MAAM,EAAEkB,IAAI,EAAE,GAAGF,OAAOC,QAAQ;IAChC,MAAME,SAASpB;IACf,OAAOmB,KAAKE,SAAS,CAACD,OAAOE,MAAM;AACrC;AAEO,SAASvB,eAAkBwB,SAA2B;IAC3D,OAAO,OAAOA,cAAc,WACxBA,YACAA,UAAUC,WAAW,IAAID,UAAUE,IAAI,IAAI;AACjD;AAEO,SAAStB,UAAUuB,GAAmB;IAC3C,OAAOA,IAAIC,QAAQ,IAAID,IAAIE,WAAW;AACxC;AAEO,SAASvB,yBAAyBO,GAAW;IAClD,MAAMiB,WAAWjB,IAAIkB,KAAK,CAAC;IAC3B,MAAMC,aAAaF,QAAQ,CAAC,EAAE;IAE9B,OACEE,WACE,4DAA4D;IAC5D,0CAA0C;KACzCC,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,UAAU,OACpBH,CAAAA,QAAQ,CAAC,EAAE,GAAI,MAAGA,SAASI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS,EAAC;AAExD;AAEO,eAAe9B,oBAIpB+B,GAAgC,EAAEC,GAAM;IACxC,wCAA2C;YACrCD;QAAJ,IAAA,CAAIA,iBAAAA,IAAIK,SAAS,KAAA,OAAA,KAAA,IAAbL,eAAeM,eAAe,EAAE;YAClC,MAAMC,UAAW,MAAG3C,eAClBoC,OACA;YACF,MAAM,OAAA,cAAkB,CAAlB,IAAIQ,MAAMD,UAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiB;QACzB;IACF;IACA,iDAAiD;IACjD,MAAMhB,MAAMU,IAAIV,GAAG,IAAKU,IAAIA,GAAG,IAAIA,IAAIA,GAAG,CAACV,GAAG;IAE9C,IAAI,CAACS,IAAIM,eAAe,EAAE;QACxB,IAAIL,IAAIA,GAAG,IAAIA,IAAIb,SAAS,EAAE;YAC5B,+BAA+B;YAC/B,OAAO;gBACLqB,WAAW,MAAMxC,oBAAoBgC,IAAIb,SAAS,EAAEa,IAAIA,GAAG;YAC7D;QACF;QACA,OAAO,CAAC;IACV;IAEA,MAAMS,QAAQ,MAAMV,IAAIM,eAAe,CAACL;IAExC,IAAIV,OAAOvB,UAAUuB,MAAM;QACzB,OAAOmB;IACT;IAEA,IAAI,CAACA,OAAO;QACV,MAAMH,UAAW,MAAG3C,eAClBoC,OACA,iEAA8DU,QAAM;QACtE,MAAM,OAAA,cAAkB,CAAlB,IAAIF,MAAMD,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IAEA,IAAIL,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAIO,OAAOC,IAAI,CAACF,OAAOvB,MAAM,KAAK,KAAK,CAACc,IAAIA,GAAG,EAAE;YAC/CY,QAAQC,IAAI,CACT,KAAElD,eACDoC,OACA;QAEN;IACF;IAEA,OAAOU;AACT;AAEO,MAAMlD,KAAK,OAAOuD,gBAAgB;AAClC,MAAMtD,KACXD,MACC;IAAC;IAAQ;IAAW;CAAmB,CAAWwD,KAAK,CACtD,CAACC,SAAW,OAAOF,WAAW,CAACE,OAAO,KAAK;AAGxC,MAAM9D,oBAAoBqD;AAAO;AACjC,MAAMlD,uBAAuBkD;AAAO;AACpC,MAAMjD,0BAA0BiD;IAGrCU,YAAYC,IAAY,CAAE;QACxB,KAAK;QACL,IAAI,CAACC,IAAI,GAAG;QACZ,IAAI,CAAC9B,IAAI,GAAG;QACZ,IAAI,CAACiB,OAAO,GAAI,kCAA+BY;IACjD;AACF;AAEO,MAAM9D,0BAA0BmD;IACrCU,YAAYC,IAAY,EAAEZ,OAAe,CAAE;QACzC,KAAK;QACL,IAAI,CAACA,OAAO,GAAI,0CAAuCY,OAAK,MAAGZ;IACjE;AACF;AAEO,MAAMnD,gCAAgCoD;IAE3CU,aAAc;QACZ,KAAK;QACL,IAAI,CAACE,IAAI,GAAG;QACZ,IAAI,CAACb,OAAO,GAAI;IAClB;AACF;AAWO,SAASpC,eAAekD,KAAY;IACzC,OAAOC,KAAKC,SAAS,CAAC;QAAEhB,SAASc,MAAMd,OAAO;QAAEiB,OAAOH,MAAMG,KAAK;IAAC;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3019, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/next/src/shared/lib/router/utils/is-local-url.ts"], "sourcesContent": ["import { isAbsoluteUrl, getLocationOrigin } from '../../utils'\nimport { hasBasePath } from '../../../../client/has-base-path'\n\n/**\n * Detects whether a given url is routable by the Next.js router (browser only).\n */\nexport function isLocalURL(url: string): boolean {\n  // prevent a hydration mismatch on href for url with anchor refs\n  if (!isAbsoluteUrl(url)) return true\n  try {\n    // absolute urls can be local if they are on the same origin\n    const locationOrigin = getLocationOrigin()\n    const resolved = new URL(url, locationOrigin)\n    return resolved.origin === locationOrigin && hasBasePath(resolved.pathname)\n  } catch (_) {\n    return false\n  }\n}\n"], "names": ["isLocalURL", "url", "isAbsoluteUrl", "locationOrigin", "getLocationOrigin", "resolved", "URL", "origin", "has<PERSON>ase<PERSON><PERSON>", "pathname", "_"], "mappings": ";;;;+BAMgBA,cAAAA;;;eAAAA;;;uBANiC;6BACrB;AAKrB,SAASA,WAAWC,GAAW;IACpC,gEAAgE;IAChE,IAAI,CAACC,CAAAA,GAAAA,OAAAA,aAAa,EAACD,MAAM,OAAO;IAChC,IAAI;QACF,4DAA4D;QAC5D,MAAME,iBAAiBC,CAAAA,GAAAA,OAAAA,iBAAiB;QACxC,MAAMC,WAAW,IAAIC,IAAIL,KAAKE;QAC9B,OAAOE,SAASE,MAAM,KAAKJ,kBAAkBK,CAAAA,GAAAA,aAAAA,WAAW,EAACH,SAASI,QAAQ;IAC5E,EAAE,OAAOC,GAAG;QACV,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3048, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/next/src/shared/lib/utils/error-once.ts"], "sourcesContent": ["let errorOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const errors = new Set<string>()\n  errorOnce = (msg: string) => {\n    if (!errors.has(msg)) {\n      console.error(msg)\n    }\n    errors.add(msg)\n  }\n}\n\nexport { errorOnce }\n"], "names": ["errorOnce", "_", "process", "env", "NODE_ENV", "errors", "Set", "msg", "has", "console", "error", "add"], "mappings": "AACIE,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAUpBJ,aAAAA;;;eAAAA;;;AAXT,IAAIA,YAAY,CAACC,KAAe;AAChC,wCAA2C;IACzC,MAAMI,SAAS,IAAIC;IACnBN,YAAY,CAACO;QACX,IAAI,CAACF,OAAOG,GAAG,CAACD,MAAM;YACpBE,QAAQC,KAAK,CAACH;QAChB;QACAF,OAAOM,GAAG,CAACJ;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3074, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/next/src/client/app-dir/link.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `null` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | null\n\n  /**\n   * (unstable) Switch to a dynamic prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    if (onNavigate) {\n      let isDefaultPrevented = false\n\n      onNavigate({\n        preventDefault: () => {\n          isDefaultPrevented = true\n        },\n      })\n\n      if (isDefaultPrevented) {\n        return\n      }\n    }\n\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  }\n\n  React.startTransition(navigate)\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n  /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */\n  const appPrefetchKind =\n    prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'prefetch' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          appPrefetchKind,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, appPrefetchKind, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n"], "names": ["LinkComponent", "useLinkStatus", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "href", "as", "linkInstanceRef", "replace", "scroll", "onNavigate", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "isLocalURL", "preventDefault", "location", "navigate", "isDefaultPrevented", "dispatchNavigateAction", "current", "React", "startTransition", "formatStringOrUrl", "urlObjOrString", "formatUrl", "props", "linkStatus", "setOptimisticLinkStatus", "useOptimistic", "IDLE_LINK_STATUS", "children", "useRef", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "ref", "forwardedRef", "unstable_dynamicOnHover", "restProps", "a", "router", "useContext", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "process", "env", "NODE_ENV", "createPropError", "args", "Error", "key", "expected", "actual", "window", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "locale", "warnOnce", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "child", "console", "warn", "Children", "only", "err", "type", "childRef", "observeLinkVisibilityOnMount", "useCallback", "element", "mountLinkInstance", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "mergedRef", "useMergedRef", "childProps", "defaultPrevented", "upgradeToDynamicPrefetch", "onNavigationIntent", "__NEXT_LINK_NO_TOUCH_START", "undefined", "isAbsoluteUrl", "addBasePath", "link", "errorOnce", "cloneElement", "LinkStatusContext", "Provider", "value", "createContext"], "mappings": "AAkXMuE,QAAQC,GAAG,CAACC,QAAQ,KAAK;AAlX/B;;;;;;;;;;;;;;;;IAmTA;;;;;;;;;CASC,GACD,OAyZC,EAAA;eAzZuBzE;;IA+ZXC,aAAa,EAAA;eAAbA;;;;;iEA1tB2D;2BAE9C;+CACO;oCACJ;8BACA;uBACC;6BACF;0BACH;uBASlB;4BACoB;mCACY;2BACb;AA0M1B,SAASC,gBAAgBC,KAAuB;IAC9C,MAAMC,cAAcD,MAAME,aAAa;IACvC,MAAMC,SAASF,YAAYG,YAAY,CAAC;IACxC,OACGD,UAAUA,WAAW,WACtBH,MAAMK,OAAO,IACbL,MAAMM,OAAO,IACbN,MAAMO,QAAQ,IACdP,MAAMQ,MAAM,IAAI,6BAA6B;IAC5CR,MAAMS,WAAW,IAAIT,MAAMS,WAAW,CAACC,KAAK,KAAK;AAEtD;AAEA,SAASC,YACPC,CAAmB,EACnBC,IAAY,EACZC,EAAU,EACVC,eAAqD,EACrDC,OAAiB,EACjBC,MAAgB,EAChBC,UAAmC;IAEnC,MAAM,EAAEC,QAAQ,EAAE,GAAGP,EAAEV,aAAa;IAEpC,kDAAkD;IAClD,MAAMkB,mBAAmBD,SAASE,WAAW,OAAO;IAEpD,IACGD,oBAAoBrB,gBAAgBa,MACrCA,EAAEV,aAAa,CAACoB,YAAY,CAAC,aAC7B;QACA,8CAA8C;QAC9C;IACF;IAEA,IAAI,CAACC,CAAAA,GAAAA,YAAAA,UAAU,EAACV,OAAO;QACrB,IAAIG,SAAS;YACX,8DAA8D;YAC9D,+BAA+B;YAC/BJ,EAAEY,cAAc;YAChBC,SAAST,OAAO,CAACH;QACnB;QAEA,8CAA8C;QAC9C;IACF;IAEAD,EAAEY,cAAc;IAEhB,MAAME,WAAW;QACf,IAAIR,YAAY;YACd,IAAIS,qBAAqB;YAEzBT,WAAW;gBACTM,gBAAgB;oBACdG,qBAAqB;gBACvB;YACF;YAEA,IAAIA,oBAAoB;gBACtB;YACF;QACF;QAEAC,CAAAA,GAAAA,mBAAAA,sBAAsB,EACpBd,MAAMD,MACNG,UAAU,YAAY,QACtBC,UAAAA,OAAAA,SAAU,MACVF,gBAAgBc,OAAO;IAE3B;IAEAC,OAAAA,OAAK,CAACC,eAAe,CAACL;AACxB;AAEA,SAASM,kBAAkBC,cAAkC;IAC3D,IAAI,OAAOA,mBAAmB,UAAU;QACtC,OAAOA;IACT;IAEA,OAAOC,CAAAA,GAAAA,WAAAA,SAAS,EAACD;AACnB;AAYe,SAASpC,cACtBsC,KAGC;IAED,MAAM,CAACC,YAAYC,wBAAwB,GAAGC,CAAAA,GAAAA,OAAAA,aAAa,EAACC,OAAAA,gBAAgB;IAE5E,IAAIC;IAEJ,MAAMzB,kBAAkB0B,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAEpD,MAAM,EACJ5B,MAAM6B,QAAQ,EACd5B,IAAI6B,MAAM,EACVH,UAAUI,YAAY,EACtBC,UAAUC,eAAe,IAAI,EAC7BC,QAAQ,EACR/B,OAAO,EACPgC,OAAO,EACP/B,MAAM,EACNgC,OAAO,EACPC,cAAcC,gBAAgB,EAC9BC,cAAcC,gBAAgB,EAC9BC,iBAAiB,KAAK,EACtBpC,UAAU,EACVqC,KAAKC,YAAY,EACjBC,uBAAuB,EACvB,GAAGC,WACJ,GAAGvB;IAEJK,WAAWI;IAEX,IACEU,kBACC,CAAA,OAAOd,aAAa,YAAY,OAAOA,aAAa,QAAO,GAC5D;QACAA,WAAAA,WAAAA,GAAW,CAAA,GAAA,YAAA,GAAA,EAACmB,KAAAA;sBAAGnB;;IACjB;IAEA,MAAMoB,SAAS9B,OAAAA,OAAK,CAAC+B,UAAU,CAACC,+BAAAA,gBAAgB;IAEhD,MAAMC,kBAAkBjB,iBAAiB;IACzC;;;;;;GAMC,GACD,MAAMkB,kBACJlB,iBAAiB,OAAOmB,oBAAAA,YAAY,CAACC,IAAI,GAAGD,oBAAAA,YAAY,CAACE,IAAI;IAE/D,wCAA2C;QACzC,SAASI,gBAAgBC,IAIxB;YACC,OAAO,OAAA,cAKN,CALM,IAAIC,MACR,iCAA+BD,KAAKE,GAAG,GAAC,iBAAeF,KAAKG,QAAQ,GAAC,4BAA4BH,KAAKI,MAAM,GAAC,eAC3G,CAAA,OAAOC,WAAW,cACf,qEACA,EAAC,IAJF,qBAAA;uBAAA;4BAAA;8BAAA;YAKP;QACF;QAEA,sCAAsC;QACtC,MAAMC,qBAAsD;YAC1DjE,MAAM;QACR;QACA,MAAMkE,gBAAqCC,OAAOC,IAAI,CACpDH;QAEFC,cAAcG,OAAO,CAAC,CAACR;YACrB,IAAIA,QAAQ,QAAQ;gBAClB,IACEvC,KAAK,CAACuC,IAAI,IAAI,QACb,OAAOvC,KAAK,CAACuC,IAAI,KAAK,YAAY,OAAOvC,KAAK,CAACuC,IAAI,KAAK,UACzD;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQzC,KAAK,CAACuC,IAAI,KAAK,OAAO,SAAS,OAAOvC,KAAK,CAACuC,IAAI;oBAC1D;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMS,IAAWT;YACnB;QACF;QAEA,sCAAsC;QACtC,MAAMU,qBAAsD;YAC1DtE,IAAI;YACJE,SAAS;YACTC,QAAQ;YACR+B,SAAS;YACTD,UAAU;YACVF,UAAU;YACVY,yBAAyB;YACzBR,SAAS;YACTC,cAAc;YACdE,cAAc;YACdE,gBAAgB;YAChBpC,YAAY;QACd;QACA,MAAMmE,gBAAqCL,OAAOC,IAAI,CACpDG;QAEFC,cAAcH,OAAO,CAAC,CAACR;YACrB,MAAMY,UAAU,OAAOnD,KAAK,CAACuC,IAAI;YAEjC,IAAIA,QAAQ,MAAM;gBAChB,IAAIvC,KAAK,CAACuC,IAAI,IAAIY,YAAY,YAAYA,YAAY,UAAU;oBAC9D,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IACLZ,QAAQ,aACRA,QAAQ,kBACRA,QAAQ,kBACRA,QAAQ,cACR;gBACA,IAAIvC,KAAK,CAACuC,IAAI,IAAIY,YAAY,YAAY;oBACxC,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IACLZ,QAAQ,aACRA,QAAQ,YACRA,QAAQ,aACRA,QAAQ,cACRA,QAAQ,cACRA,QAAQ,oBACRA,QAAQ,2BACR;gBACA,IAAIvC,KAAK,CAACuC,IAAI,IAAI,QAAQY,YAAY,WAAW;oBAC/C,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMH,IAAWT;YACnB;QACF;IACF;IAEA,IAAIN,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAInC,MAAMoD,MAAM,EAAE;YAChBC,CAAAA,GAAAA,UAAAA,QAAQ,EACN;QAEJ;QACA,IAAI,CAAC7C,QAAQ;YACX,IAAI9B;YACJ,IAAI,OAAO6B,aAAa,UAAU;gBAChC7B,OAAO6B;YACT,OAAO,IACL,OAAOA,aAAa,YACpB,OAAOA,SAAS+C,QAAQ,KAAK,UAC7B;gBACA5E,OAAO6B,SAAS+C,QAAQ;YAC1B;YAEA,IAAI5E,MAAM;gBACR,MAAM6E,oBAAoB7E,KACvB8E,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UAAYA,QAAQC,UAAU,CAAC,QAAQD,QAAQE,QAAQ,CAAC;gBAEjE,IAAIL,mBAAmB;oBACrB,MAAM,OAAA,cAEL,CAFK,IAAIjB,MACP,mBAAiB5D,OAAK,6IADnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;IACF;IAEA,MAAM,EAAEA,IAAI,EAAEC,EAAE,EAAE,GAAGgB,OAAAA,OAAK,CAACkE,OAAO;iCAAC;YACjC,MAAMC,eAAejE,kBAAkBU;YACvC,OAAO;gBACL7B,MAAMoF;gBACNnF,IAAI6B,SAASX,kBAAkBW,UAAUsD;YAC3C;QACF;gCAAG;QAACvD;QAAUC;KAAO;IAErB,oFAAoF;IACpF,IAAIuD;IACJ,IAAI5C,gBAAgB;QAClB,IAAIc,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,IAAIrB,SAAS;gBACXkD,QAAQC,IAAI,CACT,oDAAoD1D,WAAS;YAElE;YACA,IAAIS,kBAAkB;gBACpBgD,QAAQC,IAAI,CACT,yDAAyD1D,WAAS;YAEvE;YACA,IAAI;gBACFwD,QAAQpE,OAAAA,OAAK,CAACuE,QAAQ,CAACC,IAAI,CAAC9D;YAC9B,EAAE,OAAO+D,KAAK;gBACZ,IAAI,CAAC/D,UAAU;oBACb,MAAM,OAAA,cAEL,CAFK,IAAIiC,MACP,uDAAuD/B,WAAS,kFAD7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,MAAM,OAAA,cAKL,CALK,IAAI+B,MACP,6DAA6D/B,WAAS,8FACpE,CAAA,OAAOmC,WAAW,cACf,sEACA,EAAC,IAJH,qBAAA;2BAAA;gCAAA;kCAAA;gBAKN;YACF;QACF,OAAO;;QAEP;IACF,OAAO;QACL,IAAIT,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,IAAI,CAAC9B,YAAAA,OAAAA,KAAAA,IAAAA,SAAkBgE,IAAI,MAAK,KAAK;gBACnC,MAAM,OAAA,cAEL,CAFK,IAAI/B,MACR,oKADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,MAAMgC,WAAgBnD,iBAClB4C,SAAS,OAAOA,UAAU,YAAYA,MAAM3C,GAAG,GAC/CC;IAEJ,4EAA4E;IAC5E,sEAAsE;IACtE,4EAA4E;IAC5E,6BAA6B;IAC7B,MAAMkD,+BAA+B5E,OAAAA,OAAK,CAAC6E,WAAW;mEACpD,CAACC;YACC,IAAIhD,WAAW,MAAM;gBACnB7C,gBAAgBc,OAAO,GAAGgF,CAAAA,GAAAA,OAAAA,iBAAiB,EACzCD,SACA/F,MACA+C,QACAI,iBACAD,iBACA1B;YAEJ;YAEA;2EAAO;oBACL,IAAItB,gBAAgBc,OAAO,EAAE;wBAC3BiF,CAAAA,GAAAA,OAAAA,+BAA+B,EAAC/F,gBAAgBc,OAAO;wBACvDd,gBAAgBc,OAAO,GAAG;oBAC5B;oBACAkF,CAAAA,GAAAA,OAAAA,2BAA2B,EAACH;gBAC9B;;QACF;kEACA;QAAC7C;QAAiBlD;QAAM+C;QAAQI;QAAiB3B;KAAwB;IAG3E,MAAM2E,YAAYC,CAAAA,GAAAA,cAAAA,YAAY,EAACP,8BAA8BD;IAE7D,MAAMS,aAMF;QACF3D,KAAKyD;QACL/D,SAAQrC,CAAC;YACP,IAAIwD,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;gBACzC,IAAI,CAAC1D,GAAG;oBACN,MAAM,OAAA,cAEL,CAFK,IAAI6D,MACP,mFADG,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,IAAI,CAACnB,kBAAkB,OAAOL,YAAY,YAAY;gBACpDA,QAAQrC;YACV;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACc,OAAO,KAAK,YAC/B;gBACAiD,MAAM/D,KAAK,CAACc,OAAO,CAACrC;YACtB;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAIhD,EAAEuG,gBAAgB,EAAE;gBACtB;YACF;YAEAxG,YAAYC,GAAGC,MAAMC,IAAIC,iBAAiBC,SAASC,QAAQC;QAC7D;QACAgC,cAAatC,CAAC;YACZ,IAAI,CAAC0C,kBAAkB,OAAOH,qBAAqB,YAAY;gBAC7DA,iBAAiBvC;YACnB;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACe,YAAY,KAAK,YACpC;gBACAgD,MAAM/D,KAAK,CAACe,YAAY,CAACtC;YAC3B;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAI,CAACG,mBAAmBK,QAAQC,GAAG,CAACC,IAA4B,IAApB,KAAK;gBAC/C;YACF;;YAEA,MAAM8C,2BAA2B3D,4BAA4B;QAK/D;QACAL,cAAcgB,QAAQC,GAAG,CAACiD,0BAA0B,GAChDC,oCACA,SAASnE,aAAaxC,CAAC;YACrB,IAAI,CAAC0C,kBAAkB,OAAOD,qBAAqB,YAAY;gBAC7DA,iBAAiBzC;YACnB;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACiB,YAAY,KAAK,YACpC;gBACA8C,MAAM/D,KAAK,CAACiB,YAAY,CAACxC;YAC3B;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAI,CAACG,iBAAiB;gBACpB;YACF;YAEA,MAAMqD,2BAA2B3D,4BAA4B;YAC7D4D,CAAAA,GAAAA,OAAAA,kBAAkB,EAChBzG,EAAEV,aAAa,EACfkH;QAEJ;IACN;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,2EAA2E;IAC3E,IAAII,CAAAA,GAAAA,OAAAA,aAAa,EAAC1G,KAAK;QACrBoG,WAAWrG,IAAI,GAAGC;IACpB,OAAO,IACL,CAACwC,kBACDP,YACCmD,MAAMM,IAAI,KAAK,OAAO,CAAE,CAAA,UAAUN,MAAM/D,KAAI,GAC7C;QACA+E,WAAWrG,IAAI,GAAG4G,CAAAA,GAAAA,aAAAA,WAAW,EAAC3G;IAChC;IAEA,IAAI4G;IAEJ,IAAIpE,gBAAgB;QAClB,IAAIc,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1CqD,CAAAA,GAAAA,WAAAA,SAAS,EACP,oEACE,oEACA,4CACA;QAEN;QACAD,OAAAA,WAAAA,GAAO5F,OAAAA,OAAK,CAAC8F,YAAY,CAAC1B,OAAOgB;IACnC,OAAO;QACLQ,OAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAAC/D,KAAAA;YAAG,GAAGD,SAAS;YAAG,GAAGwD,UAAU;sBAC7B1E;;IAGP;IAEA,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACqF,kBAAkBC,QAAQ,EAAA;QAACC,OAAO3F;kBAChCsF;;AAGP;AAEA,MAAMG,oBAAAA,WAAAA,GAAoBG,CAAAA,GAAAA,OAAAA,aAAa,EAErCzF,OAAAA,gBAAgB;AAEX,MAAMzC,gBAAgB;IAC3B,OAAO+D,CAAAA,GAAAA,OAAAA,UAAU,EAACgE;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3470, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n\n/**\n * Check if a component has an accessibility prop\n *\n * @param {object} props\n * @returns {boolean} Whether the component has an accessibility prop\n */\nexport const hasA11yProp = (props: Record<string, any>) => {\n  for (const prop in props) {\n    if (prop.startsWith('aria-') || prop === 'role' || prop === 'title') {\n      return true;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAC1B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAA,CAAQ,CAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAQ/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,CAAA,CAAmB,MAAA,CAC5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAyB,CAAC,OAAO,CAAI,CAAA,CAAA,CAAA,EAAA,CAClD,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAS9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA;IACvE,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,YAAY,MAAM,CAAA;IAE5B,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,MAAA,CAAO,CAAC,CAAA,CAAE,WAAA,EAAgB,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,KAAA,CAAM,CAAC,CAAA;AAC/D,CAAA;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,CAAA,CAAA,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACrE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,OAAO,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA;QAEjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CACjC,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAG,CAAA,CAAA,CAAA,CACR,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA;AAQG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA;IACzD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,QAAQ,KAAO,CAAA;QACxB,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,OAAS,CAAA,CAAA;YAC5D,OAAA,CAAA,CAAA,CAAA,CAAA;QAAA;IACT;AAEJ,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3506, "column": 0}, "map": {"version": 3, "file": "defaultAttributes.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,cAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3533, "column": 0}, "map": {"version": 3, "file": "Icon.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwBA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,qKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACX,CACE,EACE,CAAA,CAAA,CAAA,CAAA,CAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACR,CAAA,CAAA,CAAA,CAAO,GAAA,CAAA,CAAA,EACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,GAAA,CAAA,EACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,GAAA,CAAA,CAAA,EACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,EAEL,CAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uKAAA,EACE,CAAA,CAAA,CAAA,CAAA,CAAA,EACA;QACE,CAAA,CAAA,CAAA;QACA,0KAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAI,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAW,iLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA;QAC3C,CAAI,CAAA,CAAA,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iLAAC,cAAA,EAAY,CAAI,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,MAAO;QAAA,CAAA;QAC/D,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACL,CAAA,EACA;WACK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI,CAAC,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM,qKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAc,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA;WACvD,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAA;YAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;SAAA;KAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3573, "column": 0}, "map": {"version": 3, "file": "createLucideIcon.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAWM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAuB,CAAA,CAAA,CAAA,CAAA,CAAA;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAuC,CAAC,CAAA,CAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iKACjF,gBAAA,4JAAc,UAAM,CAAA,CAAA;YAClB,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,kLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACT,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,kLAAY,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,CAAA,CAAA,EAC7C,CAAA,OAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,EAClB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAEF,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACJ,CAAA;IAGO,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mLAAc,eAAA,EAAa,QAAQ,CAAA;IAEtC,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACT,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3605, "column": 0}, "map": {"version": 3, "file": "house.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/lucide-react/src/icons/house.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8', key: '5wwlr5' }],\n  [\n    'path',\n    {\n      d: 'M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z',\n      key: '1d0kgt',\n    },\n  ],\n];\n\n/**\n * @component @name House\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMjF2LThhMSAxIDAgMCAwLTEtMWgtNGExIDEgMCAwIDAtMSAxdjgiIC8+CiAgPHBhdGggZD0iTTMgMTBhMiAyIDAgMCAxIC43MDktMS41MjhsNy01Ljk5OWEyIDIgMCAwIDEgMi41ODIgMGw3IDUuOTk5QTIgMiAwIDAgMSAyMSAxMHY5YTIgMiAwIDAgMS0yIDJINWEyIDIgMCAwIDEtMi0yeiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/house\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst House = createLucideIcon('house', __iconNode);\n\nexport default House;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3E;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3651, "column": 0}, "map": {"version": 3, "file": "clock.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['polyline', { points: '12 6 12 12 16 14', key: '68esgv' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxMiA2IDEyIDEyIDE2IDE0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3699, "column": 0}, "map": {"version": 3, "file": "users.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/lucide-react/src/icons/users.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['path', { d: 'M16 3.128a4 4 0 0 1 0 7.744', key: '16gr8j' }],\n  ['path', { d: 'M22 21v-2a4 4 0 0 0-3-3.87', key: 'kshegd' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n];\n\n/**\n * @component @name Users\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8cGF0aCBkPSJNMTYgMy4xMjhhNCA0IDAgMCAxIDAgNy43NDQiIC8+CiAgPHBhdGggZD0iTTIyIDIxdi0yYTQgNCAwIDAgMC0zLTMuODciIC8+CiAgPGNpcmNsZSBjeD0iOSIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/users\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Users = createLucideIcon('users', __iconNode);\n\nexport default Users;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3D;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,OAAA;QAAS,CAAA;KAAA;CACvD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3761, "column": 0}, "map": {"version": 3, "file": "settings.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/lucide-react/src/icons/settings.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z',\n      key: '1qme2f',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n];\n\n/**\n * @component @name Settings\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuMjIgMmgtLjQ0YTIgMiAwIDAgMC0yIDJ2LjE4YTIgMiAwIDAgMS0xIDEuNzNsLS40My4yNWEyIDIgMCAwIDEtMiAwbC0uMTUtLjA4YTIgMiAwIDAgMC0yLjczLjczbC0uMjIuMzhhMiAyIDAgMCAwIC43MyAyLjczbC4xNS4xYTIgMiAwIDAgMSAxIDEuNzJ2LjUxYTIgMiAwIDAgMS0xIDEuNzRsLS4xNS4wOWEyIDIgMCAwIDAtLjczIDIuNzNsLjIyLjM4YTIgMiAwIDAgMCAyLjczLjczbC4xNS0uMDhhMiAyIDAgMCAxIDIgMGwuNDMuMjVhMiAyIDAgMCAxIDEgMS43M1YyMGEyIDIgMCAwIDAgMiAyaC40NGEyIDIgMCAwIDAgMi0ydi0uMThhMiAyIDAgMCAxIDEtMS43M2wuNDMtLjI1YTIgMiAwIDAgMSAyIDBsLjE1LjA4YTIgMiAwIDAgMCAyLjczLS43M2wuMjItLjM5YTIgMiAwIDAgMC0uNzMtMi43M2wtLjE1LS4wOGEyIDIgMCAwIDEtMS0xLjc0di0uNWEyIDIgMCAwIDEgMS0xLjc0bC4xNS0uMDlhMiAyIDAgMCAwIC43My0yLjczbC0uMjItLjM4YTIgMiAwIDAgMC0yLjczLS43M2wtLjE1LjA4YTIgMiAwIDAgMS0yIDBsLS40My0uMjVhMiAyIDAgMCAxLTEtMS43M1Y0YTIgMiAwIDAgMC0yLTJ6IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/settings\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Settings = createLucideIcon('settings', __iconNode);\n\nexport default Settings;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3809, "column": 0}, "map": {"version": 3, "file": "chevron-left.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/lucide-react/src/icons/chevron-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm15 18-6-6 6-6', key: '1wnfg3' }]];\n\n/**\n * @component @name ChevronLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTUgMTgtNi02IDYtNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chevron-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronLeft = createLucideIcon('chevron-left', __iconNode);\n\nexport default ChevronLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,gBAAkB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa/E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3848, "column": 0}, "map": {"version": 3, "file": "chevron-right.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/lucide-react/src/icons/chevron-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm9 18 6-6-6-6', key: 'mthhwq' }]];\n\n/**\n * @component @name ChevronRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtOSAxOCA2LTYtNi02IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/chevron-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronRight = createLucideIcon('chevron-right', __iconNode);\n\nexport default ChevronRight;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,eAAiB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa9E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3887, "column": 0}, "map": {"version": 3, "file": "film.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/lucide-react/src/icons/film.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '18', height: '18', x: '3', y: '3', rx: '2', key: 'afitv7' }],\n  ['path', { d: 'M7 3v18', key: 'bbkbws' }],\n  ['path', { d: 'M3 7.5h4', key: 'zfgn84' }],\n  ['path', { d: 'M3 12h18', key: '1i2n21' }],\n  ['path', { d: 'M3 16.5h4', key: '1230mu' }],\n  ['path', { d: 'M17 3v18', key: 'in4fa5' }],\n  ['path', { d: 'M17 7.5h4', key: 'myr1c1' }],\n  ['path', { d: 'M17 16.5h4', key: 'go4c1d' }],\n];\n\n/**\n * @component @name Film\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik03IDN2MTgiIC8+CiAgPHBhdGggZD0iTTMgNy41aDQiIC8+CiAgPHBhdGggZD0iTTMgMTJoMTgiIC8+CiAgPHBhdGggZD0iTTMgMTYuNWg0IiAvPgogIDxwYXRoIGQ9Ik0xNyAzdjE4IiAvPgogIDxwYXRoIGQ9Ik0xNyA3LjVoNCIgLz4KICA8cGF0aCBkPSJNMTcgMTYuNWg0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/film\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Film = createLucideIcon('film', __iconNode);\n\nexport default Film;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAA,AAAjB,CAAA,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3979, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40radix-ui/react-compose-refs/src/compose-refs.tsx"], "sourcesContent": ["import * as React from 'react';\n\ntype PossibleRef<T> = React.Ref<T> | undefined;\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */\nfunction setRef<T>(ref: PossibleRef<T>, value: T) {\n  if (typeof ref === 'function') {\n    return ref(value);\n  } else if (ref !== null && ref !== undefined) {\n    ref.current = value;\n  }\n}\n\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */\nfunction composeRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == 'function') {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n\n    // React <19 will log an error to the console if a callback ref returns a\n    // value. We don't use ref cleanups internally so this will only happen if a\n    // user's ref callback returns a value, which we only expect if they are\n    // using the cleanup functionality added in React 19.\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == 'function') {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\n\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */\nfunction useComposedRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return React.useCallback(composeRefs(...refs), refs);\n}\n\nexport { composeRefs, useComposedRefs };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;;AAQvB,SAAS,OAAU,GAAA,EAAqB,KAAA,EAAU;IAChD,IAAI,OAAO,QAAQ,YAAY;QAC7B,OAAO,IAAI,KAAK;IAClB,OAAA,IAAW,QAAQ,QAAQ,QAAQ,KAAA,GAAW;QAC5C,IAAI,OAAA,GAAU;IAChB;AACF;AAMA,SAAS,YAAA,GAAkB,IAAA,EAA8C;IACvE,OAAO,CAAC,SAAS;QACf,IAAI,aAAa;QACjB,MAAM,WAAW,KAAK,GAAA,CAAI,CAAC,QAAQ;YACjC,MAAM,UAAU,OAAO,KAAK,IAAI;YAChC,IAAI,CAAC,cAAc,OAAO,WAAW,YAAY;gBAC/C,aAAa;YACf;YACA,OAAO;QACT,CAAC;QAMD,IAAI,YAAY;YACd,OAAO,MAAM;gBACX,IAAA,IAAS,IAAI,GAAG,IAAI,SAAS,MAAA,EAAQ,IAAK;oBACxC,MAAM,UAAU,QAAA,CAAS,CAAC,CAAA;oBAC1B,IAAI,OAAO,WAAW,YAAY;wBAChC,QAAQ;oBACV,OAAO;wBACL,OAAO,IAAA,CAAK,CAAC,CAAA,EAAG,IAAI;oBACtB;gBACF;YACF;QACF;IACF;AACF;AAMA,SAAS,gBAAA,GAAsB,IAAA,EAA8C;IAE3E,yKAAa,cAAA,EAAY,YAAY,GAAG,IAAI,GAAG,IAAI;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4028, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40radix-ui/react-slot/src/slot.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotProps extends React.HTMLAttributes<HTMLElement> {\n  children?: React.ReactNode;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ export function createSlot(ownerName: string) {\n  const SlotClone = createSlotClone(ownerName);\n  const Slot = React.forwardRef<HTMLElement, SlotProps>((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n\n    if (slottable) {\n      // the new element to render is the one passed as a child of `Slottable`\n      const newElement = slottable.props.children;\n\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          // because the new element will be the one rendered, we are only interested\n          // in grabbing its children (`newElement.props.children`)\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement)\n            ? (newElement.props as { children: React.ReactNode }).children\n            : null;\n        } else {\n          return child;\n        }\n      });\n\n      return (\n        <SlotClone {...slotProps} ref={forwardedRef}>\n          {React.isValidElement(newElement)\n            ? React.cloneElement(newElement, undefined, newChildren)\n            : null}\n        </SlotClone>\n      );\n    }\n\n    return (\n      <SlotClone {...slotProps} ref={forwardedRef}>\n        {children}\n      </SlotClone>\n    );\n  });\n\n  Slot.displayName = `${ownerName}.Slot`;\n  return Slot;\n}\n\nconst Slot = createSlot('Slot');\n\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotCloneProps {\n  children: React.ReactNode;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ function createSlotClone(ownerName: string) {\n  const SlotClone = React.forwardRef<any, SlotCloneProps>((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props = mergeProps(slotProps, children.props as AnyProps);\n      // do not pass ref to React.Fragment for React 19 compatibility\n      if (children.type !== React.Fragment) {\n        props.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props);\n    }\n\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/\n\nconst SLOTTABLE_IDENTIFIER = Symbol('radix.slottable');\n\ninterface SlottableProps {\n  children: React.ReactNode;\n}\n\ninterface SlottableComponent extends React.FC<SlottableProps> {\n  __radixId: symbol;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ export function createSlottable(ownerName: string) {\n  const Slottable: SlottableComponent = ({ children }) => {\n    return <>{children}</>;\n  };\n  Slottable.displayName = `${ownerName}.Slottable`;\n  Slottable.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable;\n}\n\nconst Slottable = createSlottable('Slottable');\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype AnyProps = Record<string, any>;\n\nfunction isSlottable(\n  child: React.ReactNode\n): child is React.ReactElement<SlottableProps, typeof Slottable> {\n  return (\n    React.isValidElement(child) &&\n    typeof child.type === 'function' &&\n    '__radixId' in child.type &&\n    child.type.__radixId === SLOTTABLE_IDENTIFIER\n  );\n}\n\nfunction mergeProps(slotProps: AnyProps, childProps: AnyProps) {\n  // all child props should override\n  const overrideProps = { ...childProps };\n\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      // if the handler exists on both, we compose them\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args: unknown[]) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      }\n      // but if it exists only on the slot, we use only this one\n      else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    }\n    // if it's `style`, we merge them\n    else if (propName === 'style') {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === 'className') {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(' ');\n    }\n  }\n\n  return { ...slotProps, ...overrideProps };\n}\n\n// Before React 19 accessing `element.props.ref` will throw a warning and suggest using `element.ref`\n// After React 19 accessing `element.ref` does the opposite.\n// https://github.com/facebook/react/pull/28348\n//\n// Access the ref using the method that doesn't yield a warning.\nfunction getElementRef(element: React.ReactElement) {\n  // React <=18 in DEV\n  let getter = Object.getOwnPropertyDescriptor(element.props, 'ref')?.get;\n  let mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element as any).ref;\n  }\n\n  // React 19 in DEV\n  getter = Object.getOwnPropertyDescriptor(element, 'ref')?.get;\n  mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element.props as { ref?: React.Ref<unknown> }).ref;\n  }\n\n  // Not DEV\n  return (element.props as { ref?: React.Ref<unknown> }).ref || (element as any).ref;\n}\n\nexport {\n  Slot,\n  Slottable,\n  //\n  Slot as Root,\n};\nexport type { SlotProps };\n"], "names": ["Fragment", "Slot", "props", "Slottable"], "mappings": ";;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,mBAAmB;AAmCpB,SAkEG,YAAAA,WAlEH;;;;AAAA,uBAAA;AAzB0B,SAAS,WAAW,SAAA,EAAmB;IACvE,MAAM,YAAY,aAAA,GAAA,gBAAgB,SAAS;IAC3C,MAAMC,0KAAa,aAAA,EAAmC,CAAC,OAAO,iBAAiB;QAC7E,MAAM,EAAE,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;QACnC,MAAM,8KAAsB,WAAA,CAAS,OAAA,CAAQ,QAAQ;QACrD,MAAM,YAAY,cAAc,IAAA,CAAK,WAAW;QAEhD,IAAI,WAAW;YAEb,MAAM,aAAa,UAAU,KAAA,CAAM,QAAA;YAEnC,MAAM,cAAc,cAAc,GAAA,CAAI,CAAC,UAAU;gBAC/C,IAAI,UAAU,WAAW;oBAGvB,IAAU,yKAAA,CAAS,KAAA,CAAM,UAAU,IAAI,EAAG,CAAA,qKAAa,WAAA,CAAS,IAAA,CAAK,IAAI;oBACzE,WAAa,+KAAA,EAAe,UAAU,IACjC,WAAW,KAAA,CAAwC,QAAA,GACpD;gBACN,OAAO;oBACL,OAAO;gBACT;YACF,CAAC;YAED,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAA;gBAAW,GAAG,SAAA;gBAAW,KAAK;gBAC5B,4KAAM,iBAAA,EAAe,UAAU,QACtB,6KAAA,EAAa,YAAY,KAAA,GAAW,WAAW,IACrD;YAAA,CACN;QAEJ;QAEA,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAA;YAAW,GAAG,SAAA;YAAW,KAAK;YAC5B;QAAA,CACH;IAEJ,CAAC;IAEDA,MAAK,WAAA,GAAc,GAAG,SAAS,CAAA,KAAA,CAAA;IAC/B,OAAOA;AACT;AAEA,IAAM,OAAO,aAAA,GAAA,WAAW,MAAM;AAAA,uBAAA;AAUH,SAAS,gBAAgB,SAAA,EAAmB;IACrE,MAAM,6KAAkB,cAAA,EAAgC,CAAC,OAAO,iBAAiB;QAC/E,MAAM,EAAE,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;QAEnC,QAAU,+KAAA,EAAe,QAAQ,GAAG;YAClC,MAAM,cAAc,cAAc,QAAQ;YAC1C,MAAMC,SAAQ,WAAW,WAAW,SAAS,KAAiB;YAE9D,IAAI,SAAS,IAAA,mKAAe,WAAA,EAAU;gBACpCA,OAAM,GAAA,GAAM,gBAAe,gMAAA,EAAY,cAAc,WAAW,IAAI;YACtE;YACA,yKAAa,eAAA,EAAa,UAAUA,MAAK;QAC3C;QAEA,qKAAa,WAAA,CAAS,KAAA,CAAM,QAAQ,IAAI,kKAAU,WAAA,CAAS,IAAA,CAAK,IAAI,IAAI;IAC1E,CAAC;IAED,UAAU,WAAA,GAAc,GAAG,SAAS,CAAA,UAAA,CAAA;IACpC,OAAO;AACT;AAMA,IAAM,uBAAuB,OAAO,iBAAiB;AAAA,uBAAA;AAUnB,SAAS,gBAAgB,SAAA,EAAmB;IAC5E,MAAMC,aAAgC,CAAC,EAAE,QAAA,CAAS,CAAA,KAAM;QACtD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,yKAAAH,WAAAA,EAAA;YAAG;QAAA,CAAS;IACrB;IACAG,WAAU,WAAA,GAAc,GAAG,SAAS,CAAA,UAAA,CAAA;IACpCA,WAAU,SAAA,GAAY;IACtB,OAAOA;AACT;AAEA,IAAM,YAAY,aAAA,GAAA,gBAAgB,WAAW;AAM7C,SAAS,YACP,KAAA,EAC+D;IAC/D,QACQ,kLAAA,EAAe,KAAK,KAC1B,OAAO,MAAM,IAAA,KAAS,cACtB,eAAe,MAAM,IAAA,IACrB,MAAM,IAAA,CAAK,SAAA,KAAc;AAE7B;AAEA,SAAS,WAAW,SAAA,EAAqB,UAAA,EAAsB;IAE7D,MAAM,gBAAgB;QAAE,GAAG,UAAA;IAAW;IAEtC,IAAA,MAAW,YAAY,WAAY;QACjC,MAAM,gBAAgB,SAAA,CAAU,QAAQ,CAAA;QACxC,MAAM,iBAAiB,UAAA,CAAW,QAAQ,CAAA;QAE1C,MAAM,YAAY,WAAW,IAAA,CAAK,QAAQ;QAC1C,IAAI,WAAW;YAEb,IAAI,iBAAiB,gBAAgB;gBACnC,aAAA,CAAc,QAAQ,CAAA,GAAI,CAAA,GAAI,SAAoB;oBAChD,MAAM,SAAS,eAAe,GAAG,IAAI;oBACrC,cAAc,GAAG,IAAI;oBACrB,OAAO;gBACT;YACF,OAAA,IAES,eAAe;gBACtB,aAAA,CAAc,QAAQ,CAAA,GAAI;YAC5B;QACF,OAAA,IAES,aAAa,SAAS;YAC7B,aAAA,CAAc,QAAQ,CAAA,GAAI;gBAAE,GAAG,aAAA;gBAAe,GAAG,cAAA;YAAe;QAClE,OAAA,IAAW,aAAa,aAAa;YACnC,aAAA,CAAc,QAAQ,CAAA,GAAI;gBAAC;gBAAe,cAAc;aAAA,CAAE,MAAA,CAAO,OAAO,EAAE,IAAA,CAAK,GAAG;QACpF;IACF;IAEA,OAAO;QAAE,GAAG,SAAA;QAAW,GAAG,aAAA;IAAc;AAC1C;AAOA,SAAS,cAAc,OAAA,EAA6B;IAElD,IAAI,SAAS,OAAO,wBAAA,CAAyB,QAAQ,KAAA,EAAO,KAAK,GAAG;IACpE,IAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IAC7D,IAAI,SAAS;QACX,OAAQ,QAAgB,GAAA;IAC1B;IAGA,SAAS,OAAO,wBAAA,CAAyB,SAAS,KAAK,GAAG;IAC1D,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IACzD,IAAI,SAAS;QACX,OAAQ,QAAQ,KAAA,CAAuC,GAAA;IACzD;IAGA,OAAQ,QAAQ,KAAA,CAAuC,GAAA,IAAQ,QAAgB,GAAA;AACjF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4164, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/clsx/dist/clsx.mjs"], "sourcesContent": ["function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}export function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;"], "names": [], "mappings": ";;;;AAAA,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE,GAAE,IAAE;IAAG,IAAG,YAAU,OAAO,KAAG,YAAU,OAAO,GAAE,KAAG;SAAO,IAAG,YAAU,OAAO,GAAE,IAAG,MAAM,OAAO,CAAC,IAAG;QAAC,IAAI,IAAE,EAAE,MAAM;QAAC,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,CAAC,CAAC,EAAE,IAAE,CAAC,IAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAC,OAAM,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,IAAE,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;AAAQ,SAAS;IAAO,IAAI,IAAI,GAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU,MAAM,EAAC,IAAE,GAAE,IAAI,CAAC,IAAE,SAAS,CAAC,EAAE,KAAG,CAAC,IAAE,EAAE,EAAE,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;uCAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4188, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/class-variance-authority/dist/index.mjs"], "sourcesContent": ["/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ import { clsx } from \"clsx\";\nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nexport const cx = clsx;\nexport const cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;;AAAG;;AACJ,MAAM,gBAAgB,CAAC,QAAQ,OAAO,UAAU,YAAY,GAAG,OAAO,GAAG,UAAU,IAAI,MAAM;AACtF,MAAM,KAAK,wIAAA,CAAA,OAAI;AACf,MAAM,MAAM,CAAC,MAAM,SAAS,CAAC;QAC5B,IAAI;QACJ,IAAI,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,QAAQ,KAAK,MAAM,OAAO,GAAG,MAAM,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS;QACvN,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG;QACtC,MAAM,uBAAuB,OAAO,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;YACpD,MAAM,cAAc,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ;YAChF,MAAM,qBAAqB,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,eAAe,CAAC,QAAQ;YACrH,IAAI,gBAAgB,MAAM,OAAO;YACjC,MAAM,aAAa,cAAc,gBAAgB,cAAc;YAC/D,OAAO,QAAQ,CAAC,QAAQ,CAAC,WAAW;QACxC;QACA,MAAM,wBAAwB,SAAS,OAAO,OAAO,CAAC,OAAO,MAAM,CAAC,CAAC,KAAK;YACtE,IAAI,CAAC,KAAK,MAAM,GAAG;YACnB,IAAI,UAAU,WAAW;gBACrB,OAAO;YACX;YACA,GAAG,CAAC,IAAI,GAAG;YACX,OAAO;QACX,GAAG,CAAC;QACJ,MAAM,+BAA+B,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,CAAC,2BAA2B,OAAO,gBAAgB,MAAM,QAAQ,6BAA6B,KAAK,IAAI,KAAK,IAAI,yBAAyB,MAAM,CAAC,CAAC,KAAK;YACvO,IAAI,EAAE,OAAO,OAAO,EAAE,WAAW,WAAW,EAAE,GAAG,wBAAwB,GAAG;YAC5E,OAAO,OAAO,OAAO,CAAC,wBAAwB,KAAK,CAAC,CAAC;gBACjD,IAAI,CAAC,KAAK,MAAM,GAAG;gBACnB,OAAO,MAAM,OAAO,CAAC,SAAS,MAAM,QAAQ,CAAC;oBACzC,GAAG,eAAe;oBAClB,GAAG,qBAAqB;gBAC5B,CAAC,CAAC,IAAI,IAAI,CAAC;oBACP,GAAG,eAAe;oBAClB,GAAG,qBAAqB;gBAC5B,CAAC,CAAC,CAAC,IAAI,KAAK;YAChB,KAAK;mBACE;gBACH;gBACA;aACH,GAAG;QACR,GAAG,EAAE;QACL,OAAO,GAAG,MAAM,sBAAsB,8BAA8B,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS;IAChM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4254, "column": 0}, "map": {"version": 3, "file": "bundle-mjs.mjs", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/tailwind-merge/src/lib/class-group-utils.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/tailwind-merge/src/lib/lru-cache.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/tailwind-merge/src/lib/parse-class-name.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/tailwind-merge/src/lib/sort-modifiers.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/tailwind-merge/src/lib/config-utils.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/tailwind-merge/src/lib/merge-classlist.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/tailwind-merge/src/lib/tw-join.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/tailwind-merge/src/lib/create-tailwind-merge.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/tailwind-merge/src/lib/from-theme.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/tailwind-merge/src/lib/validators.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/tailwind-merge/src/lib/default-config.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/tailwind-merge/src/lib/merge-configs.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/tailwind-merge/src/lib/extend-tailwind-merge.ts", "file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/tailwind-merge/src/lib/tw-merge.ts"], "sourcesContent": ["import {\n    AnyClassGroupIds,\n    AnyConfig,\n    AnyThemeGroupIds,\n    ClassGroup,\n    ClassValidator,\n    Config,\n    ThemeGetter,\n    ThemeObject,\n} from './types'\n\nexport interface ClassPartObject {\n    nextPart: Map<string, ClassPartObject>\n    validators: ClassValidatorObject[]\n    classGroupId?: AnyClassGroupIds\n}\n\ninterface ClassValidatorObject {\n    classGroupId: AnyClassGroupIds\n    validator: ClassValidator\n}\n\nconst CLASS_PART_SEPARATOR = '-'\n\nexport const createClassGroupUtils = (config: AnyConfig) => {\n    const classMap = createClassMap(config)\n    const { conflictingClassGroups, conflictingClassGroupModifiers } = config\n\n    const getClassGroupId = (className: string) => {\n        const classParts = className.split(CLASS_PART_SEPARATOR)\n\n        // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n        if (classParts[0] === '' && classParts.length !== 1) {\n            classParts.shift()\n        }\n\n        return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className)\n    }\n\n    const getConflictingClassGroupIds = (\n        classGroupId: AnyClassGroupIds,\n        hasPostfixModifier: boolean,\n    ) => {\n        const conflicts = conflictingClassGroups[classGroupId] || []\n\n        if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n            return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]!]\n        }\n\n        return conflicts\n    }\n\n    return {\n        getClassGroupId,\n        getConflictingClassGroupIds,\n    }\n}\n\nconst getGroupRecursive = (\n    classParts: string[],\n    classPartObject: ClassPartObject,\n): AnyClassGroupIds | undefined => {\n    if (classParts.length === 0) {\n        return classPartObject.classGroupId\n    }\n\n    const currentClassPart = classParts[0]!\n    const nextClassPartObject = classPartObject.nextPart.get(currentClassPart)\n    const classGroupFromNextClassPart = nextClassPartObject\n        ? getGroupRecursive(classParts.slice(1), nextClassPartObject)\n        : undefined\n\n    if (classGroupFromNextClassPart) {\n        return classGroupFromNextClassPart\n    }\n\n    if (classPartObject.validators.length === 0) {\n        return undefined\n    }\n\n    const classRest = classParts.join(CLASS_PART_SEPARATOR)\n\n    return classPartObject.validators.find(({ validator }) => validator(classRest))?.classGroupId\n}\n\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/\n\nconst getGroupIdForArbitraryProperty = (className: string) => {\n    if (arbitraryPropertyRegex.test(className)) {\n        const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)![1]\n        const property = arbitraryPropertyClassName?.substring(\n            0,\n            arbitraryPropertyClassName.indexOf(':'),\n        )\n\n        if (property) {\n            // I use two dots here because one dot is used as prefix for class groups in plugins\n            return 'arbitrary..' + property\n        }\n    }\n}\n\n/**\n * Exported for testing only\n */\nexport const createClassMap = (config: Config<AnyClassGroupIds, AnyThemeGroupIds>) => {\n    const { theme, classGroups } = config\n    const classMap: ClassPartObject = {\n        nextPart: new Map<string, ClassPartObject>(),\n        validators: [],\n    }\n\n    for (const classGroupId in classGroups) {\n        processClassesRecursively(classGroups[classGroupId]!, classMap, classGroupId, theme)\n    }\n\n    return classMap\n}\n\nconst processClassesRecursively = (\n    classGroup: ClassGroup<AnyThemeGroupIds>,\n    classPartObject: ClassPartObject,\n    classGroupId: AnyClassGroupIds,\n    theme: ThemeObject<AnyThemeGroupIds>,\n) => {\n    classGroup.forEach((classDefinition) => {\n        if (typeof classDefinition === 'string') {\n            const classPartObjectToEdit =\n                classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition)\n            classPartObjectToEdit.classGroupId = classGroupId\n            return\n        }\n\n        if (typeof classDefinition === 'function') {\n            if (isThemeGetter(classDefinition)) {\n                processClassesRecursively(\n                    classDefinition(theme),\n                    classPartObject,\n                    classGroupId,\n                    theme,\n                )\n                return\n            }\n\n            classPartObject.validators.push({\n                validator: classDefinition,\n                classGroupId,\n            })\n\n            return\n        }\n\n        Object.entries(classDefinition).forEach(([key, classGroup]) => {\n            processClassesRecursively(\n                classGroup,\n                getPart(classPartObject, key),\n                classGroupId,\n                theme,\n            )\n        })\n    })\n}\n\nconst getPart = (classPartObject: ClassPartObject, path: string) => {\n    let currentClassPartObject = classPartObject\n\n    path.split(CLASS_PART_SEPARATOR).forEach((pathPart) => {\n        if (!currentClassPartObject.nextPart.has(pathPart)) {\n            currentClassPartObject.nextPart.set(pathPart, {\n                nextPart: new Map(),\n                validators: [],\n            })\n        }\n\n        currentClassPartObject = currentClassPartObject.nextPart.get(pathPart)!\n    })\n\n    return currentClassPartObject\n}\n\nconst isThemeGetter = (func: ClassValidator | ThemeGetter): func is ThemeGetter =>\n    (func as ThemeGetter).isThemeGetter\n", "// Export is needed because TypeScript complains about an error otherwise:\n// Error: …/tailwind-merge/src/config-utils.ts(8,17): semantic error TS4058: Return type of exported function has or is using name 'LruCache' from external module \"…/tailwind-merge/src/lru-cache\" but cannot be named.\nexport interface LruCache<Key, Value> {\n    get(key: Key): Value | undefined\n    set(key: Key, value: Value): void\n}\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nexport const createLruCache = <Key, Value>(maxCacheSize: number): LruCache<Key, Value> => {\n    if (maxCacheSize < 1) {\n        return {\n            get: () => undefined,\n            set: () => {},\n        }\n    }\n\n    let cacheSize = 0\n    let cache = new Map<Key, Value>()\n    let previousCache = new Map<Key, Value>()\n\n    const update = (key: Key, value: Value) => {\n        cache.set(key, value)\n        cacheSize++\n\n        if (cacheSize > maxCacheSize) {\n            cacheSize = 0\n            previousCache = cache\n            cache = new Map()\n        }\n    }\n\n    return {\n        get(key) {\n            let value = cache.get(key)\n\n            if (value !== undefined) {\n                return value\n            }\n            if ((value = previousCache.get(key)) !== undefined) {\n                update(key, value)\n                return value\n            }\n        },\n        set(key, value) {\n            if (cache.has(key)) {\n                cache.set(key, value)\n            } else {\n                update(key, value)\n            }\n        },\n    }\n}\n", "import { AnyConfig, ParsedClassName } from './types'\n\nexport const IMPORTANT_MODIFIER = '!'\nconst MODIFIER_SEPARATOR = ':'\nconst MODIFIER_SEPARATOR_LENGTH = MODIFIER_SEPARATOR.length\n\nexport const createParseClassName = (config: AnyConfig) => {\n    const { prefix, experimentalParseClassName } = config\n\n    /**\n     * Parse class name into parts.\n     *\n     * Inspired by `splitAtTopLevelOnly` used in Tailwind CSS\n     * @see https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n     */\n    let parseClassName = (className: string): ParsedClassName => {\n        const modifiers = []\n\n        let bracketDepth = 0\n        let parenDepth = 0\n        let modifierStart = 0\n        let postfixModifierPosition: number | undefined\n\n        for (let index = 0; index < className.length; index++) {\n            let currentCharacter = className[index]\n\n            if (bracketDepth === 0 && parenDepth === 0) {\n                if (currentCharacter === MODIFIER_SEPARATOR) {\n                    modifiers.push(className.slice(modifierStart, index))\n                    modifierStart = index + MODIFIER_SEPARATOR_LENGTH\n                    continue\n                }\n\n                if (currentCharacter === '/') {\n                    postfixModifierPosition = index\n                    continue\n                }\n            }\n\n            if (currentCharacter === '[') {\n                bracketDepth++\n            } else if (currentCharacter === ']') {\n                bracketDepth--\n            } else if (currentCharacter === '(') {\n                parenDepth++\n            } else if (currentCharacter === ')') {\n                parenDepth--\n            }\n        }\n\n        const baseClassNameWithImportantModifier =\n            modifiers.length === 0 ? className : className.substring(modifierStart)\n        const baseClassName = stripImportantModifier(baseClassNameWithImportantModifier)\n        const hasImportantModifier = baseClassName !== baseClassNameWithImportantModifier\n        const maybePostfixModifierPosition =\n            postfixModifierPosition && postfixModifierPosition > modifierStart\n                ? postfixModifierPosition - modifierStart\n                : undefined\n\n        return {\n            modifiers,\n            hasImportantModifier,\n            baseClassName,\n            maybePostfixModifierPosition,\n        }\n    }\n\n    if (prefix) {\n        const fullPrefix = prefix + MODIFIER_SEPARATOR\n        const parseClassNameOriginal = parseClassName\n        parseClassName = (className) =>\n            className.startsWith(fullPrefix)\n                ? parseClassNameOriginal(className.substring(fullPrefix.length))\n                : {\n                      isExternal: true,\n                      modifiers: [],\n                      hasImportantModifier: false,\n                      baseClassName: className,\n                      maybePostfixModifierPosition: undefined,\n                  }\n    }\n\n    if (experimentalParseClassName) {\n        const parseClassNameOriginal = parseClassName\n        parseClassName = (className) =>\n            experimentalParseClassName({ className, parseClassName: parseClassNameOriginal })\n    }\n\n    return parseClassName\n}\n\nconst stripImportantModifier = (baseClassName: string) => {\n    if (baseClassName.endsWith(IMPORTANT_MODIFIER)) {\n        return baseClassName.substring(0, baseClassName.length - 1)\n    }\n\n    /**\n     * In Tailwind CSS v3 the important modifier was at the start of the base class name. This is still supported for legacy reasons.\n     * @see https://github.com/dcastil/tailwind-merge/issues/513#issuecomment-2614029864\n     */\n    if (baseClassName.startsWith(IMPORTANT_MODIFIER)) {\n        return baseClassName.substring(1)\n    }\n\n    return baseClassName\n}\n", "import { AnyConfig } from './types'\n\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nexport const createSortModifiers = (config: AnyConfig) => {\n    const orderSensitiveModifiers = Object.fromEntries(\n        config.orderSensitiveModifiers.map((modifier) => [modifier, true]),\n    )\n\n    const sortModifiers = (modifiers: string[]) => {\n        if (modifiers.length <= 1) {\n            return modifiers\n        }\n\n        const sortedModifiers: string[] = []\n        let unsortedModifiers: string[] = []\n\n        modifiers.forEach((modifier) => {\n            const isPositionSensitive = modifier[0] === '[' || orderSensitiveModifiers[modifier]\n\n            if (isPositionSensitive) {\n                sortedModifiers.push(...unsortedModifiers.sort(), modifier)\n                unsortedModifiers = []\n            } else {\n                unsortedModifiers.push(modifier)\n            }\n        })\n\n        sortedModifiers.push(...unsortedModifiers.sort())\n\n        return sortedModifiers\n    }\n\n    return sortModifiers\n}\n", "import { createClassGroupUtils } from './class-group-utils'\nimport { createLruCache } from './lru-cache'\nimport { createParseClassName } from './parse-class-name'\nimport { createSortModifiers } from './sort-modifiers'\nimport { AnyConfig } from './types'\n\nexport type ConfigUtils = ReturnType<typeof createConfigUtils>\n\nexport const createConfigUtils = (config: AnyConfig) => ({\n    cache: createLruCache<string, string>(config.cacheSize),\n    parseClassName: createParseClassName(config),\n    sortModifiers: createSortModifiers(config),\n    ...createClassGroupUtils(config),\n})\n", "import { ConfigUtils } from './config-utils'\nimport { IMPORTANT_MODIFIER } from './parse-class-name'\n\nconst SPLIT_CLASSES_REGEX = /\\s+/\n\nexport const mergeClassList = (classList: string, configUtils: ConfigUtils) => {\n    const { parseClassName, getClassGroupId, getConflictingClassGroupIds, sortModifiers } =\n        configUtils\n\n    /**\n     * Set of classGroupIds in following format:\n     * `{importantModifier}{variantModifiers}{classGroupId}`\n     * @example 'float'\n     * @example 'hover:focus:bg-color'\n     * @example 'md:!pr'\n     */\n    const classGroupsInConflict: string[] = []\n    const classNames = classList.trim().split(SPLIT_CLASSES_REGEX)\n\n    let result = ''\n\n    for (let index = classNames.length - 1; index >= 0; index -= 1) {\n        const originalClassName = classNames[index]!\n\n        const {\n            isExternal,\n            modifiers,\n            hasImportantModifier,\n            baseClassName,\n            maybePostfixModifierPosition,\n        } = parseClassName(originalClassName)\n\n        if (isExternal) {\n            result = originalClassName + (result.length > 0 ? ' ' + result : result)\n            continue\n        }\n\n        let hasPostfixModifier = !!maybePostfixModifierPosition\n        let classGroupId = getClassGroupId(\n            hasPostfixModifier\n                ? baseClassName.substring(0, maybePostfixModifierPosition)\n                : baseClassName,\n        )\n\n        if (!classGroupId) {\n            if (!hasPostfixModifier) {\n                // Not a Tailwind class\n                result = originalClassName + (result.length > 0 ? ' ' + result : result)\n                continue\n            }\n\n            classGroupId = getClassGroupId(baseClassName)\n\n            if (!classGroupId) {\n                // Not a Tailwind class\n                result = originalClassName + (result.length > 0 ? ' ' + result : result)\n                continue\n            }\n\n            hasPostfixModifier = false\n        }\n\n        const variantModifier = sortModifiers(modifiers).join(':')\n\n        const modifierId = hasImportantModifier\n            ? variantModifier + IMPORTANT_MODIFIER\n            : variantModifier\n\n        const classId = modifierId + classGroupId\n\n        if (classGroupsInConflict.includes(classId)) {\n            // Tailwind class omitted due to conflict\n            continue\n        }\n\n        classGroupsInConflict.push(classId)\n\n        const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier)\n        for (let i = 0; i < conflictGroups.length; ++i) {\n            const group = conflictGroups[i]!\n            classGroupsInConflict.push(modifierId + group)\n        }\n\n        // Tailwind class not in conflict\n        result = originalClassName + (result.length > 0 ? ' ' + result : result)\n    }\n\n    return result\n}\n", "/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) <PERSON> <<EMAIL>> (lukeed.com)\n */\n\nexport type ClassNameValue = ClassNameArray | string | null | undefined | 0 | 0n | false\ntype ClassNameArray = ClassNameValue[]\n\nexport function twJoin(...classLists: ClassNameValue[]): string\nexport function twJoin() {\n    let index = 0\n    let argument: ClassNameValue\n    let resolvedValue: string\n    let string = ''\n\n    while (index < arguments.length) {\n        if ((argument = arguments[index++])) {\n            if ((resolvedValue = toValue(argument))) {\n                string && (string += ' ')\n                string += resolvedValue\n            }\n        }\n    }\n    return string\n}\n\nconst toValue = (mix: ClassNameArray | string) => {\n    if (typeof mix === 'string') {\n        return mix\n    }\n\n    let resolvedValue: string\n    let string = ''\n\n    for (let k = 0; k < mix.length; k++) {\n        if (mix[k]) {\n            if ((resolvedValue = toValue(mix[k] as ClassNameArray | string))) {\n                string && (string += ' ')\n                string += resolvedValue\n            }\n        }\n    }\n\n    return string\n}\n", "import { createConfigUtils } from './config-utils'\nimport { mergeClassList } from './merge-classlist'\nimport { ClassNameValue, twJoin } from './tw-join'\nimport { AnyConfig } from './types'\n\ntype CreateConfigFirst = () => AnyConfig\ntype CreateConfigSubsequent = (config: AnyConfig) => AnyConfig\ntype TailwindMerge = (...classLists: ClassNameValue[]) => string\ntype ConfigUtils = ReturnType<typeof createConfigUtils>\n\nexport function createTailwindMerge(\n    createConfigFirst: CreateConfigFirst,\n    ...createConfigRest: CreateConfigSubsequent[]\n): TailwindMerge {\n    let configUtils: ConfigUtils\n    let cacheGet: ConfigUtils['cache']['get']\n    let cacheSet: ConfigUtils['cache']['set']\n    let functionToCall = initTailwindMerge\n\n    function initTailwindMerge(classList: string) {\n        const config = createConfigRest.reduce(\n            (previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig),\n            createConfigFirst() as AnyConfig,\n        )\n\n        configUtils = createConfigUtils(config)\n        cacheGet = configUtils.cache.get\n        cacheSet = configUtils.cache.set\n        functionToCall = tailwindMerge\n\n        return tailwindMerge(classList)\n    }\n\n    function tailwindMerge(classList: string) {\n        const cachedResult = cacheGet(classList)\n\n        if (cachedResult) {\n            return cachedResult\n        }\n\n        const result = mergeClassList(classList, configUtils)\n        cacheSet(classList, result)\n\n        return result\n    }\n\n    return function callTailwindMerge() {\n        return functionToCall(twJoin.apply(null, arguments as any))\n    }\n}\n", "import { DefaultThemeGroupIds, <PERSON><PERSON><PERSON><PERSON>, ThemeGetter, ThemeObject } from './types'\n\nexport const fromTheme = <\n    AdditionalThemeGroupIds extends string = never,\n    DefaultThemeGroupIdsInner extends string = DefaultThemeGroupIds,\n>(key: NoInfer<DefaultThemeGroupIdsInner | AdditionalThemeGroupIds>): ThemeGetter => {\n    const themeGetter = (theme: ThemeObject<DefaultThemeGroupIdsInner | AdditionalThemeGroupIds>) =>\n        theme[key] || []\n\n    themeGetter.isThemeGetter = true as const\n\n    return themeGetter\n}\n", "const arbitraryValueRegex = /^\\[(?:(\\w[\\w-]*):)?(.+)\\]$/i\nconst arbitraryVariableRegex = /^\\((?:(\\w[\\w-]*):)?(.+)\\)$/i\nconst fractionRegex = /^\\d+\\/\\d+$/\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/\nconst lengthUnitRegex =\n    /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch))\\(.+\\)$/\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/\nconst imageRegex =\n    /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/\n\nexport const isFraction = (value: string) => fractionRegex.test(value)\n\nexport const isNumber = (value: string) => !!value && !Number.isNaN(Number(value))\n\nexport const isInteger = (value: string) => !!value && Number.isInteger(Number(value))\n\nexport const isPercent = (value: string) => value.endsWith('%') && isNumber(value.slice(0, -1))\n\nexport const isTshirtSize = (value: string) => tshirtUnitRegex.test(value)\n\nexport const isAny = () => true\n\nconst isLengthOnly = (value: string) =>\n    // `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n    // For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n    // I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\n    lengthUnitRegex.test(value) && !colorFunctionRegex.test(value)\n\nconst isNever = () => false\n\nconst isShadow = (value: string) => shadowRegex.test(value)\n\nconst isImage = (value: string) => imageRegex.test(value)\n\nexport const isAnyNonArbitrary = (value: string) =>\n    !isArbitraryValue(value) && !isArbitraryVariable(value)\n\nexport const isArbitrarySize = (value: string) => getIsArbitraryValue(value, isLabelSize, isNever)\n\nexport const isArbitraryValue = (value: string) => arbitraryValueRegex.test(value)\n\nexport const isArbitraryLength = (value: string) =>\n    getIsArbitraryValue(value, isLabelLength, isLengthOnly)\n\nexport const isArbitraryNumber = (value: string) =>\n    getIsArbitraryValue(value, isLabelNumber, isNumber)\n\nexport const isArbitraryPosition = (value: string) =>\n    getIsArbitraryValue(value, isLabelPosition, isNever)\n\nexport const isArbitraryImage = (value: string) => getIsArbitraryValue(value, isLabelImage, isImage)\n\nexport const isArbitraryShadow = (value: string) =>\n    getIsArbitraryValue(value, isLabelShadow, isShadow)\n\nexport const isArbitraryVariable = (value: string) => arbitraryVariableRegex.test(value)\n\nexport const isArbitraryVariableLength = (value: string) =>\n    getIsArbitraryVariable(value, isLabelLength)\n\nexport const isArbitraryVariableFamilyName = (value: string) =>\n    getIsArbitraryVariable(value, isLabelFamilyName)\n\nexport const isArbitraryVariablePosition = (value: string) =>\n    getIsArbitraryVariable(value, isLabelPosition)\n\nexport const isArbitraryVariableSize = (value: string) => getIsArbitraryVariable(value, isLabelSize)\n\nexport const isArbitraryVariableImage = (value: string) =>\n    getIsArbitraryVariable(value, isLabelImage)\n\nexport const isArbitraryVariableShadow = (value: string) =>\n    getIsArbitraryVariable(value, isLabelShadow, true)\n\n// Helpers\n\nconst getIsArbitraryValue = (\n    value: string,\n    testLabel: (label: string) => boolean,\n    testValue: (value: string) => boolean,\n) => {\n    const result = arbitraryValueRegex.exec(value)\n\n    if (result) {\n        if (result[1]) {\n            return testLabel(result[1])\n        }\n\n        return testValue(result[2]!)\n    }\n\n    return false\n}\n\nconst getIsArbitraryVariable = (\n    value: string,\n    testLabel: (label: string) => boolean,\n    shouldMatchNoLabel = false,\n) => {\n    const result = arbitraryVariableRegex.exec(value)\n\n    if (result) {\n        if (result[1]) {\n            return testLabel(result[1])\n        }\n        return shouldMatchNoLabel\n    }\n\n    return false\n}\n\n// Labels\n\nconst isLabelPosition = (label: string) => label === 'position' || label === 'percentage'\n\nconst isLabelImage = (label: string) => label === 'image' || label === 'url'\n\nconst isLabelSize = (label: string) => label === 'length' || label === 'size' || label === 'bg-size'\n\nconst isLabelLength = (label: string) => label === 'length'\n\nconst isLabelNumber = (label: string) => label === 'number'\n\nconst isLabelFamilyName = (label: string) => label === 'family-name'\n\nconst isLabelShadow = (label: string) => label === 'shadow'\n", "import { fromTheme } from './from-theme'\nimport { Config, DefaultClassGroupIds, DefaultThemeGroupIds } from './types'\nimport {\n    isAny,\n    isAnyNonArbitrary,\n    isArbitraryImage,\n    isArbitraryLength,\n    isArbitraryNumber,\n    isArbitraryPosition,\n    isArbitraryShadow,\n    isArbitrarySize,\n    isArbitraryValue,\n    isArbitraryVariable,\n    isArbitraryVariableFamilyName,\n    isArbitraryVariableImage,\n    isArbitraryVariableLength,\n    isArbitraryVariablePosition,\n    isArbitraryVariableShadow,\n    isArbitraryVariableSize,\n    isFraction,\n    isInteger,\n    isNumber,\n    isPercent,\n    isTshirtSize,\n} from './validators'\n\nexport const getDefaultConfig = () => {\n    /**\n     * Theme getters for theme variable namespaces\n     * @see https://tailwindcss.com/docs/theme#theme-variable-namespaces\n     */\n    /***/\n\n    const themeColor = fromTheme('color')\n    const themeFont = fromTheme('font')\n    const themeText = fromTheme('text')\n    const themeFontWeight = fromTheme('font-weight')\n    const themeTracking = fromTheme('tracking')\n    const themeLeading = fromTheme('leading')\n    const themeBreakpoint = fromTheme('breakpoint')\n    const themeContainer = fromTheme('container')\n    const themeSpacing = fromTheme('spacing')\n    const themeRadius = fromTheme('radius')\n    const themeShadow = fromTheme('shadow')\n    const themeInsetShadow = fromTheme('inset-shadow')\n    const themeTextShadow = fromTheme('text-shadow')\n    const themeDropShadow = fromTheme('drop-shadow')\n    const themeBlur = fromTheme('blur')\n    const themePerspective = fromTheme('perspective')\n    const themeAspect = fromTheme('aspect')\n    const themeEase = fromTheme('ease')\n    const themeAnimate = fromTheme('animate')\n\n    /**\n     * Helpers to avoid repeating the same scales\n     *\n     * We use functions that create a new array every time they're called instead of static arrays.\n     * This ensures that users who modify any scale by mutating the array (e.g. with `array.push(element)`) don't accidentally mutate arrays in other parts of the config.\n     */\n    /***/\n\n    const scaleBreak = () =>\n        ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'] as const\n    const scalePosition = () =>\n        [\n            'center',\n            'top',\n            'bottom',\n            'left',\n            'right',\n            'top-left',\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            'left-top',\n            'top-right',\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            'right-top',\n            'bottom-right',\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            'right-bottom',\n            'bottom-left',\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            'left-bottom',\n        ] as const\n    const scalePositionWithArbitrary = () =>\n        [...scalePosition(), isArbitraryVariable, isArbitraryValue] as const\n    const scaleOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'] as const\n    const scaleOverscroll = () => ['auto', 'contain', 'none'] as const\n    const scaleUnambiguousSpacing = () =>\n        [isArbitraryVariable, isArbitraryValue, themeSpacing] as const\n    const scaleInset = () => [isFraction, 'full', 'auto', ...scaleUnambiguousSpacing()] as const\n    const scaleGridTemplateColsRows = () =>\n        [isInteger, 'none', 'subgrid', isArbitraryVariable, isArbitraryValue] as const\n    const scaleGridColRowStartAndEnd = () =>\n        [\n            'auto',\n            { span: ['full', isInteger, isArbitraryVariable, isArbitraryValue] },\n            isInteger,\n            isArbitraryVariable,\n            isArbitraryValue,\n        ] as const\n    const scaleGridColRowStartOrEnd = () =>\n        [isInteger, 'auto', isArbitraryVariable, isArbitraryValue] as const\n    const scaleGridAutoColsRows = () =>\n        ['auto', 'min', 'max', 'fr', isArbitraryVariable, isArbitraryValue] as const\n    const scaleAlignPrimaryAxis = () =>\n        [\n            'start',\n            'end',\n            'center',\n            'between',\n            'around',\n            'evenly',\n            'stretch',\n            'baseline',\n            'center-safe',\n            'end-safe',\n        ] as const\n    const scaleAlignSecondaryAxis = () =>\n        ['start', 'end', 'center', 'stretch', 'center-safe', 'end-safe'] as const\n    const scaleMargin = () => ['auto', ...scaleUnambiguousSpacing()] as const\n    const scaleSizing = () =>\n        [\n            isFraction,\n            'auto',\n            'full',\n            'dvw',\n            'dvh',\n            'lvw',\n            'lvh',\n            'svw',\n            'svh',\n            'min',\n            'max',\n            'fit',\n            ...scaleUnambiguousSpacing(),\n        ] as const\n    const scaleColor = () => [themeColor, isArbitraryVariable, isArbitraryValue] as const\n    const scaleBgPosition = () =>\n        [\n            ...scalePosition(),\n            isArbitraryVariablePosition,\n            isArbitraryPosition,\n            { position: [isArbitraryVariable, isArbitraryValue] },\n        ] as const\n    const scaleBgRepeat = () => ['no-repeat', { repeat: ['', 'x', 'y', 'space', 'round'] }] as const\n    const scaleBgSize = () =>\n        [\n            'auto',\n            'cover',\n            'contain',\n            isArbitraryVariableSize,\n            isArbitrarySize,\n            { size: [isArbitraryVariable, isArbitraryValue] },\n        ] as const\n    const scaleGradientStopPosition = () =>\n        [isPercent, isArbitraryVariableLength, isArbitraryLength] as const\n    const scaleRadius = () =>\n        [\n            // Deprecated since Tailwind CSS v4.0.0\n            '',\n            'none',\n            'full',\n            themeRadius,\n            isArbitraryVariable,\n            isArbitraryValue,\n        ] as const\n    const scaleBorderWidth = () =>\n        ['', isNumber, isArbitraryVariableLength, isArbitraryLength] as const\n    const scaleLineStyle = () => ['solid', 'dashed', 'dotted', 'double'] as const\n    const scaleBlendMode = () =>\n        [\n            'normal',\n            'multiply',\n            'screen',\n            'overlay',\n            'darken',\n            'lighten',\n            'color-dodge',\n            'color-burn',\n            'hard-light',\n            'soft-light',\n            'difference',\n            'exclusion',\n            'hue',\n            'saturation',\n            'color',\n            'luminosity',\n        ] as const\n    const scaleMaskImagePosition = () =>\n        [isNumber, isPercent, isArbitraryVariablePosition, isArbitraryPosition] as const\n    const scaleBlur = () =>\n        [\n            // Deprecated since Tailwind CSS v4.0.0\n            '',\n            'none',\n            themeBlur,\n            isArbitraryVariable,\n            isArbitraryValue,\n        ] as const\n    const scaleRotate = () => ['none', isNumber, isArbitraryVariable, isArbitraryValue] as const\n    const scaleScale = () => ['none', isNumber, isArbitraryVariable, isArbitraryValue] as const\n    const scaleSkew = () => [isNumber, isArbitraryVariable, isArbitraryValue] as const\n    const scaleTranslate = () => [isFraction, 'full', ...scaleUnambiguousSpacing()] as const\n\n    return {\n        cacheSize: 500,\n        theme: {\n            animate: ['spin', 'ping', 'pulse', 'bounce'],\n            aspect: ['video'],\n            blur: [isTshirtSize],\n            breakpoint: [isTshirtSize],\n            color: [isAny],\n            container: [isTshirtSize],\n            'drop-shadow': [isTshirtSize],\n            ease: ['in', 'out', 'in-out'],\n            font: [isAnyNonArbitrary],\n            'font-weight': [\n                'thin',\n                'extralight',\n                'light',\n                'normal',\n                'medium',\n                'semibold',\n                'bold',\n                'extrabold',\n                'black',\n            ],\n            'inset-shadow': [isTshirtSize],\n            leading: ['none', 'tight', 'snug', 'normal', 'relaxed', 'loose'],\n            perspective: ['dramatic', 'near', 'normal', 'midrange', 'distant', 'none'],\n            radius: [isTshirtSize],\n            shadow: [isTshirtSize],\n            spacing: ['px', isNumber],\n            text: [isTshirtSize],\n            'text-shadow': [isTshirtSize],\n            tracking: ['tighter', 'tight', 'normal', 'wide', 'wider', 'widest'],\n        },\n        classGroups: {\n            // --------------\n            // --- Layout ---\n            // --------------\n\n            /**\n             * Aspect Ratio\n             * @see https://tailwindcss.com/docs/aspect-ratio\n             */\n            aspect: [\n                {\n                    aspect: [\n                        'auto',\n                        'square',\n                        isFraction,\n                        isArbitraryValue,\n                        isArbitraryVariable,\n                        themeAspect,\n                    ],\n                },\n            ],\n            /**\n             * Container\n             * @see https://tailwindcss.com/docs/container\n             * @deprecated since Tailwind CSS v4.0.0\n             */\n            container: ['container'],\n            /**\n             * Columns\n             * @see https://tailwindcss.com/docs/columns\n             */\n            columns: [\n                { columns: [isNumber, isArbitraryValue, isArbitraryVariable, themeContainer] },\n            ],\n            /**\n             * Break After\n             * @see https://tailwindcss.com/docs/break-after\n             */\n            'break-after': [{ 'break-after': scaleBreak() }],\n            /**\n             * Break Before\n             * @see https://tailwindcss.com/docs/break-before\n             */\n            'break-before': [{ 'break-before': scaleBreak() }],\n            /**\n             * Break Inside\n             * @see https://tailwindcss.com/docs/break-inside\n             */\n            'break-inside': [{ 'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column'] }],\n            /**\n             * Box Decoration Break\n             * @see https://tailwindcss.com/docs/box-decoration-break\n             */\n            'box-decoration': [{ 'box-decoration': ['slice', 'clone'] }],\n            /**\n             * Box Sizing\n             * @see https://tailwindcss.com/docs/box-sizing\n             */\n            box: [{ box: ['border', 'content'] }],\n            /**\n             * Display\n             * @see https://tailwindcss.com/docs/display\n             */\n            display: [\n                'block',\n                'inline-block',\n                'inline',\n                'flex',\n                'inline-flex',\n                'table',\n                'inline-table',\n                'table-caption',\n                'table-cell',\n                'table-column',\n                'table-column-group',\n                'table-footer-group',\n                'table-header-group',\n                'table-row-group',\n                'table-row',\n                'flow-root',\n                'grid',\n                'inline-grid',\n                'contents',\n                'list-item',\n                'hidden',\n            ],\n            /**\n             * Screen Reader Only\n             * @see https://tailwindcss.com/docs/display#screen-reader-only\n             */\n            sr: ['sr-only', 'not-sr-only'],\n            /**\n             * Floats\n             * @see https://tailwindcss.com/docs/float\n             */\n            float: [{ float: ['right', 'left', 'none', 'start', 'end'] }],\n            /**\n             * Clear\n             * @see https://tailwindcss.com/docs/clear\n             */\n            clear: [{ clear: ['left', 'right', 'both', 'none', 'start', 'end'] }],\n            /**\n             * Isolation\n             * @see https://tailwindcss.com/docs/isolation\n             */\n            isolation: ['isolate', 'isolation-auto'],\n            /**\n             * Object Fit\n             * @see https://tailwindcss.com/docs/object-fit\n             */\n            'object-fit': [{ object: ['contain', 'cover', 'fill', 'none', 'scale-down'] }],\n            /**\n             * Object Position\n             * @see https://tailwindcss.com/docs/object-position\n             */\n            'object-position': [{ object: scalePositionWithArbitrary() }],\n            /**\n             * Overflow\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            overflow: [{ overflow: scaleOverflow() }],\n            /**\n             * Overflow X\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            'overflow-x': [{ 'overflow-x': scaleOverflow() }],\n            /**\n             * Overflow Y\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            'overflow-y': [{ 'overflow-y': scaleOverflow() }],\n            /**\n             * Overscroll Behavior\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            overscroll: [{ overscroll: scaleOverscroll() }],\n            /**\n             * Overscroll Behavior X\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            'overscroll-x': [{ 'overscroll-x': scaleOverscroll() }],\n            /**\n             * Overscroll Behavior Y\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            'overscroll-y': [{ 'overscroll-y': scaleOverscroll() }],\n            /**\n             * Position\n             * @see https://tailwindcss.com/docs/position\n             */\n            position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n            /**\n             * Top / Right / Bottom / Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            inset: [{ inset: scaleInset() }],\n            /**\n             * Right / Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            'inset-x': [{ 'inset-x': scaleInset() }],\n            /**\n             * Top / Bottom\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            'inset-y': [{ 'inset-y': scaleInset() }],\n            /**\n             * Start\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            start: [{ start: scaleInset() }],\n            /**\n             * End\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            end: [{ end: scaleInset() }],\n            /**\n             * Top\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            top: [{ top: scaleInset() }],\n            /**\n             * Right\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            right: [{ right: scaleInset() }],\n            /**\n             * Bottom\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            bottom: [{ bottom: scaleInset() }],\n            /**\n             * Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            left: [{ left: scaleInset() }],\n            /**\n             * Visibility\n             * @see https://tailwindcss.com/docs/visibility\n             */\n            visibility: ['visible', 'invisible', 'collapse'],\n            /**\n             * Z-Index\n             * @see https://tailwindcss.com/docs/z-index\n             */\n            z: [{ z: [isInteger, 'auto', isArbitraryVariable, isArbitraryValue] }],\n\n            // ------------------------\n            // --- Flexbox and Grid ---\n            // ------------------------\n\n            /**\n             * Flex Basis\n             * @see https://tailwindcss.com/docs/flex-basis\n             */\n            basis: [\n                {\n                    basis: [\n                        isFraction,\n                        'full',\n                        'auto',\n                        themeContainer,\n                        ...scaleUnambiguousSpacing(),\n                    ],\n                },\n            ],\n            /**\n             * Flex Direction\n             * @see https://tailwindcss.com/docs/flex-direction\n             */\n            'flex-direction': [{ flex: ['row', 'row-reverse', 'col', 'col-reverse'] }],\n            /**\n             * Flex Wrap\n             * @see https://tailwindcss.com/docs/flex-wrap\n             */\n            'flex-wrap': [{ flex: ['nowrap', 'wrap', 'wrap-reverse'] }],\n            /**\n             * Flex\n             * @see https://tailwindcss.com/docs/flex\n             */\n            flex: [{ flex: [isNumber, isFraction, 'auto', 'initial', 'none', isArbitraryValue] }],\n            /**\n             * Flex Grow\n             * @see https://tailwindcss.com/docs/flex-grow\n             */\n            grow: [{ grow: ['', isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Flex Shrink\n             * @see https://tailwindcss.com/docs/flex-shrink\n             */\n            shrink: [{ shrink: ['', isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Order\n             * @see https://tailwindcss.com/docs/order\n             */\n            order: [\n                {\n                    order: [\n                        isInteger,\n                        'first',\n                        'last',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Grid Template Columns\n             * @see https://tailwindcss.com/docs/grid-template-columns\n             */\n            'grid-cols': [{ 'grid-cols': scaleGridTemplateColsRows() }],\n            /**\n             * Grid Column Start / End\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-start-end': [{ col: scaleGridColRowStartAndEnd() }],\n            /**\n             * Grid Column Start\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-start': [{ 'col-start': scaleGridColRowStartOrEnd() }],\n            /**\n             * Grid Column End\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-end': [{ 'col-end': scaleGridColRowStartOrEnd() }],\n            /**\n             * Grid Template Rows\n             * @see https://tailwindcss.com/docs/grid-template-rows\n             */\n            'grid-rows': [{ 'grid-rows': scaleGridTemplateColsRows() }],\n            /**\n             * Grid Row Start / End\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-start-end': [{ row: scaleGridColRowStartAndEnd() }],\n            /**\n             * Grid Row Start\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-start': [{ 'row-start': scaleGridColRowStartOrEnd() }],\n            /**\n             * Grid Row End\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-end': [{ 'row-end': scaleGridColRowStartOrEnd() }],\n            /**\n             * Grid Auto Flow\n             * @see https://tailwindcss.com/docs/grid-auto-flow\n             */\n            'grid-flow': [{ 'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense'] }],\n            /**\n             * Grid Auto Columns\n             * @see https://tailwindcss.com/docs/grid-auto-columns\n             */\n            'auto-cols': [{ 'auto-cols': scaleGridAutoColsRows() }],\n            /**\n             * Grid Auto Rows\n             * @see https://tailwindcss.com/docs/grid-auto-rows\n             */\n            'auto-rows': [{ 'auto-rows': scaleGridAutoColsRows() }],\n            /**\n             * Gap\n             * @see https://tailwindcss.com/docs/gap\n             */\n            gap: [{ gap: scaleUnambiguousSpacing() }],\n            /**\n             * Gap X\n             * @see https://tailwindcss.com/docs/gap\n             */\n            'gap-x': [{ 'gap-x': scaleUnambiguousSpacing() }],\n            /**\n             * Gap Y\n             * @see https://tailwindcss.com/docs/gap\n             */\n            'gap-y': [{ 'gap-y': scaleUnambiguousSpacing() }],\n            /**\n             * Justify Content\n             * @see https://tailwindcss.com/docs/justify-content\n             */\n            'justify-content': [{ justify: [...scaleAlignPrimaryAxis(), 'normal'] }],\n            /**\n             * Justify Items\n             * @see https://tailwindcss.com/docs/justify-items\n             */\n            'justify-items': [{ 'justify-items': [...scaleAlignSecondaryAxis(), 'normal'] }],\n            /**\n             * Justify Self\n             * @see https://tailwindcss.com/docs/justify-self\n             */\n            'justify-self': [{ 'justify-self': ['auto', ...scaleAlignSecondaryAxis()] }],\n            /**\n             * Align Content\n             * @see https://tailwindcss.com/docs/align-content\n             */\n            'align-content': [{ content: ['normal', ...scaleAlignPrimaryAxis()] }],\n            /**\n             * Align Items\n             * @see https://tailwindcss.com/docs/align-items\n             */\n            'align-items': [{ items: [...scaleAlignSecondaryAxis(), { baseline: ['', 'last'] }] }],\n            /**\n             * Align Self\n             * @see https://tailwindcss.com/docs/align-self\n             */\n            'align-self': [\n                { self: ['auto', ...scaleAlignSecondaryAxis(), { baseline: ['', 'last'] }] },\n            ],\n            /**\n             * Place Content\n             * @see https://tailwindcss.com/docs/place-content\n             */\n            'place-content': [{ 'place-content': scaleAlignPrimaryAxis() }],\n            /**\n             * Place Items\n             * @see https://tailwindcss.com/docs/place-items\n             */\n            'place-items': [{ 'place-items': [...scaleAlignSecondaryAxis(), 'baseline'] }],\n            /**\n             * Place Self\n             * @see https://tailwindcss.com/docs/place-self\n             */\n            'place-self': [{ 'place-self': ['auto', ...scaleAlignSecondaryAxis()] }],\n            // Spacing\n            /**\n             * Padding\n             * @see https://tailwindcss.com/docs/padding\n             */\n            p: [{ p: scaleUnambiguousSpacing() }],\n            /**\n             * Padding X\n             * @see https://tailwindcss.com/docs/padding\n             */\n            px: [{ px: scaleUnambiguousSpacing() }],\n            /**\n             * Padding Y\n             * @see https://tailwindcss.com/docs/padding\n             */\n            py: [{ py: scaleUnambiguousSpacing() }],\n            /**\n             * Padding Start\n             * @see https://tailwindcss.com/docs/padding\n             */\n            ps: [{ ps: scaleUnambiguousSpacing() }],\n            /**\n             * Padding End\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pe: [{ pe: scaleUnambiguousSpacing() }],\n            /**\n             * Padding Top\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pt: [{ pt: scaleUnambiguousSpacing() }],\n            /**\n             * Padding Right\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pr: [{ pr: scaleUnambiguousSpacing() }],\n            /**\n             * Padding Bottom\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pb: [{ pb: scaleUnambiguousSpacing() }],\n            /**\n             * Padding Left\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pl: [{ pl: scaleUnambiguousSpacing() }],\n            /**\n             * Margin\n             * @see https://tailwindcss.com/docs/margin\n             */\n            m: [{ m: scaleMargin() }],\n            /**\n             * Margin X\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mx: [{ mx: scaleMargin() }],\n            /**\n             * Margin Y\n             * @see https://tailwindcss.com/docs/margin\n             */\n            my: [{ my: scaleMargin() }],\n            /**\n             * Margin Start\n             * @see https://tailwindcss.com/docs/margin\n             */\n            ms: [{ ms: scaleMargin() }],\n            /**\n             * Margin End\n             * @see https://tailwindcss.com/docs/margin\n             */\n            me: [{ me: scaleMargin() }],\n            /**\n             * Margin Top\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mt: [{ mt: scaleMargin() }],\n            /**\n             * Margin Right\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mr: [{ mr: scaleMargin() }],\n            /**\n             * Margin Bottom\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mb: [{ mb: scaleMargin() }],\n            /**\n             * Margin Left\n             * @see https://tailwindcss.com/docs/margin\n             */\n            ml: [{ ml: scaleMargin() }],\n            /**\n             * Space Between X\n             * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n             */\n            'space-x': [{ 'space-x': scaleUnambiguousSpacing() }],\n            /**\n             * Space Between X Reverse\n             * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n             */\n            'space-x-reverse': ['space-x-reverse'],\n            /**\n             * Space Between Y\n             * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n             */\n            'space-y': [{ 'space-y': scaleUnambiguousSpacing() }],\n            /**\n             * Space Between Y Reverse\n             * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n             */\n            'space-y-reverse': ['space-y-reverse'],\n\n            // --------------\n            // --- Sizing ---\n            // --------------\n\n            /**\n             * Size\n             * @see https://tailwindcss.com/docs/width#setting-both-width-and-height\n             */\n            size: [{ size: scaleSizing() }],\n            /**\n             * Width\n             * @see https://tailwindcss.com/docs/width\n             */\n            w: [{ w: [themeContainer, 'screen', ...scaleSizing()] }],\n            /**\n             * Min-Width\n             * @see https://tailwindcss.com/docs/min-width\n             */\n            'min-w': [\n                {\n                    'min-w': [\n                        themeContainer,\n                        'screen',\n                        /** Deprecated. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n                        'none',\n                        ...scaleSizing(),\n                    ],\n                },\n            ],\n            /**\n             * Max-Width\n             * @see https://tailwindcss.com/docs/max-width\n             */\n            'max-w': [\n                {\n                    'max-w': [\n                        themeContainer,\n                        'screen',\n                        'none',\n                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n                        'prose',\n                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n                        { screen: [themeBreakpoint] },\n                        ...scaleSizing(),\n                    ],\n                },\n            ],\n            /**\n             * Height\n             * @see https://tailwindcss.com/docs/height\n             */\n            h: [{ h: ['screen', 'lh', ...scaleSizing()] }],\n            /**\n             * Min-Height\n             * @see https://tailwindcss.com/docs/min-height\n             */\n            'min-h': [{ 'min-h': ['screen', 'lh', 'none', ...scaleSizing()] }],\n            /**\n             * Max-Height\n             * @see https://tailwindcss.com/docs/max-height\n             */\n            'max-h': [{ 'max-h': ['screen', 'lh', ...scaleSizing()] }],\n\n            // ------------------\n            // --- Typography ---\n            // ------------------\n\n            /**\n             * Font Size\n             * @see https://tailwindcss.com/docs/font-size\n             */\n            'font-size': [\n                { text: ['base', themeText, isArbitraryVariableLength, isArbitraryLength] },\n            ],\n            /**\n             * Font Smoothing\n             * @see https://tailwindcss.com/docs/font-smoothing\n             */\n            'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n            /**\n             * Font Style\n             * @see https://tailwindcss.com/docs/font-style\n             */\n            'font-style': ['italic', 'not-italic'],\n            /**\n             * Font Weight\n             * @see https://tailwindcss.com/docs/font-weight\n             */\n            'font-weight': [{ font: [themeFontWeight, isArbitraryVariable, isArbitraryNumber] }],\n            /**\n             * Font Stretch\n             * @see https://tailwindcss.com/docs/font-stretch\n             */\n            'font-stretch': [\n                {\n                    'font-stretch': [\n                        'ultra-condensed',\n                        'extra-condensed',\n                        'condensed',\n                        'semi-condensed',\n                        'normal',\n                        'semi-expanded',\n                        'expanded',\n                        'extra-expanded',\n                        'ultra-expanded',\n                        isPercent,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Font Family\n             * @see https://tailwindcss.com/docs/font-family\n             */\n            'font-family': [{ font: [isArbitraryVariableFamilyName, isArbitraryValue, themeFont] }],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-normal': ['normal-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-ordinal': ['ordinal'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-slashed-zero': ['slashed-zero'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-fraction': ['diagonal-fractions', 'stacked-fractions'],\n            /**\n             * Letter Spacing\n             * @see https://tailwindcss.com/docs/letter-spacing\n             */\n            tracking: [{ tracking: [themeTracking, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Line Clamp\n             * @see https://tailwindcss.com/docs/line-clamp\n             */\n            'line-clamp': [\n                { 'line-clamp': [isNumber, 'none', isArbitraryVariable, isArbitraryNumber] },\n            ],\n            /**\n             * Line Height\n             * @see https://tailwindcss.com/docs/line-height\n             */\n            leading: [\n                {\n                    leading: [\n                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n                        themeLeading,\n                        ...scaleUnambiguousSpacing(),\n                    ],\n                },\n            ],\n            /**\n             * List Style Image\n             * @see https://tailwindcss.com/docs/list-style-image\n             */\n            'list-image': [{ 'list-image': ['none', isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * List Style Position\n             * @see https://tailwindcss.com/docs/list-style-position\n             */\n            'list-style-position': [{ list: ['inside', 'outside'] }],\n            /**\n             * List Style Type\n             * @see https://tailwindcss.com/docs/list-style-type\n             */\n            'list-style-type': [\n                { list: ['disc', 'decimal', 'none', isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Text Alignment\n             * @see https://tailwindcss.com/docs/text-align\n             */\n            'text-alignment': [{ text: ['left', 'center', 'right', 'justify', 'start', 'end'] }],\n            /**\n             * Placeholder Color\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://v3.tailwindcss.com/docs/placeholder-color\n             */\n            'placeholder-color': [{ placeholder: scaleColor() }],\n            /**\n             * Text Color\n             * @see https://tailwindcss.com/docs/text-color\n             */\n            'text-color': [{ text: scaleColor() }],\n            /**\n             * Text Decoration\n             * @see https://tailwindcss.com/docs/text-decoration\n             */\n            'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n            /**\n             * Text Decoration Style\n             * @see https://tailwindcss.com/docs/text-decoration-style\n             */\n            'text-decoration-style': [{ decoration: [...scaleLineStyle(), 'wavy'] }],\n            /**\n             * Text Decoration Thickness\n             * @see https://tailwindcss.com/docs/text-decoration-thickness\n             */\n            'text-decoration-thickness': [\n                {\n                    decoration: [\n                        isNumber,\n                        'from-font',\n                        'auto',\n                        isArbitraryVariable,\n                        isArbitraryLength,\n                    ],\n                },\n            ],\n            /**\n             * Text Decoration Color\n             * @see https://tailwindcss.com/docs/text-decoration-color\n             */\n            'text-decoration-color': [{ decoration: scaleColor() }],\n            /**\n             * Text Underline Offset\n             * @see https://tailwindcss.com/docs/text-underline-offset\n             */\n            'underline-offset': [\n                { 'underline-offset': [isNumber, 'auto', isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Text Transform\n             * @see https://tailwindcss.com/docs/text-transform\n             */\n            'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n            /**\n             * Text Overflow\n             * @see https://tailwindcss.com/docs/text-overflow\n             */\n            'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n            /**\n             * Text Wrap\n             * @see https://tailwindcss.com/docs/text-wrap\n             */\n            'text-wrap': [{ text: ['wrap', 'nowrap', 'balance', 'pretty'] }],\n            /**\n             * Text Indent\n             * @see https://tailwindcss.com/docs/text-indent\n             */\n            indent: [{ indent: scaleUnambiguousSpacing() }],\n            /**\n             * Vertical Alignment\n             * @see https://tailwindcss.com/docs/vertical-align\n             */\n            'vertical-align': [\n                {\n                    align: [\n                        'baseline',\n                        'top',\n                        'middle',\n                        'bottom',\n                        'text-top',\n                        'text-bottom',\n                        'sub',\n                        'super',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Whitespace\n             * @see https://tailwindcss.com/docs/whitespace\n             */\n            whitespace: [\n                { whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces'] },\n            ],\n            /**\n             * Word Break\n             * @see https://tailwindcss.com/docs/word-break\n             */\n            break: [{ break: ['normal', 'words', 'all', 'keep'] }],\n            /**\n             * Overflow Wrap\n             * @see https://tailwindcss.com/docs/overflow-wrap\n             */\n            wrap: [{ wrap: ['break-word', 'anywhere', 'normal'] }],\n            /**\n             * Hyphens\n             * @see https://tailwindcss.com/docs/hyphens\n             */\n            hyphens: [{ hyphens: ['none', 'manual', 'auto'] }],\n            /**\n             * Content\n             * @see https://tailwindcss.com/docs/content\n             */\n            content: [{ content: ['none', isArbitraryVariable, isArbitraryValue] }],\n\n            // -------------------\n            // --- Backgrounds ---\n            // -------------------\n\n            /**\n             * Background Attachment\n             * @see https://tailwindcss.com/docs/background-attachment\n             */\n            'bg-attachment': [{ bg: ['fixed', 'local', 'scroll'] }],\n            /**\n             * Background Clip\n             * @see https://tailwindcss.com/docs/background-clip\n             */\n            'bg-clip': [{ 'bg-clip': ['border', 'padding', 'content', 'text'] }],\n            /**\n             * Background Origin\n             * @see https://tailwindcss.com/docs/background-origin\n             */\n            'bg-origin': [{ 'bg-origin': ['border', 'padding', 'content'] }],\n            /**\n             * Background Position\n             * @see https://tailwindcss.com/docs/background-position\n             */\n            'bg-position': [{ bg: scaleBgPosition() }],\n            /**\n             * Background Repeat\n             * @see https://tailwindcss.com/docs/background-repeat\n             */\n            'bg-repeat': [{ bg: scaleBgRepeat() }],\n            /**\n             * Background Size\n             * @see https://tailwindcss.com/docs/background-size\n             */\n            'bg-size': [{ bg: scaleBgSize() }],\n            /**\n             * Background Image\n             * @see https://tailwindcss.com/docs/background-image\n             */\n            'bg-image': [\n                {\n                    bg: [\n                        'none',\n                        {\n                            linear: [\n                                { to: ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl'] },\n                                isInteger,\n                                isArbitraryVariable,\n                                isArbitraryValue,\n                            ],\n                            radial: ['', isArbitraryVariable, isArbitraryValue],\n                            conic: [isInteger, isArbitraryVariable, isArbitraryValue],\n                        },\n                        isArbitraryVariableImage,\n                        isArbitraryImage,\n                    ],\n                },\n            ],\n            /**\n             * Background Color\n             * @see https://tailwindcss.com/docs/background-color\n             */\n            'bg-color': [{ bg: scaleColor() }],\n            /**\n             * Gradient Color Stops From Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-from-pos': [{ from: scaleGradientStopPosition() }],\n            /**\n             * Gradient Color Stops Via Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-via-pos': [{ via: scaleGradientStopPosition() }],\n            /**\n             * Gradient Color Stops To Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-to-pos': [{ to: scaleGradientStopPosition() }],\n            /**\n             * Gradient Color Stops From\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-from': [{ from: scaleColor() }],\n            /**\n             * Gradient Color Stops Via\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-via': [{ via: scaleColor() }],\n            /**\n             * Gradient Color Stops To\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-to': [{ to: scaleColor() }],\n\n            // ---------------\n            // --- Borders ---\n            // ---------------\n\n            /**\n             * Border Radius\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            rounded: [{ rounded: scaleRadius() }],\n            /**\n             * Border Radius Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-s': [{ 'rounded-s': scaleRadius() }],\n            /**\n             * Border Radius End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-e': [{ 'rounded-e': scaleRadius() }],\n            /**\n             * Border Radius Top\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-t': [{ 'rounded-t': scaleRadius() }],\n            /**\n             * Border Radius Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-r': [{ 'rounded-r': scaleRadius() }],\n            /**\n             * Border Radius Bottom\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-b': [{ 'rounded-b': scaleRadius() }],\n            /**\n             * Border Radius Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-l': [{ 'rounded-l': scaleRadius() }],\n            /**\n             * Border Radius Start Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-ss': [{ 'rounded-ss': scaleRadius() }],\n            /**\n             * Border Radius Start End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-se': [{ 'rounded-se': scaleRadius() }],\n            /**\n             * Border Radius End End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-ee': [{ 'rounded-ee': scaleRadius() }],\n            /**\n             * Border Radius End Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-es': [{ 'rounded-es': scaleRadius() }],\n            /**\n             * Border Radius Top Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-tl': [{ 'rounded-tl': scaleRadius() }],\n            /**\n             * Border Radius Top Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-tr': [{ 'rounded-tr': scaleRadius() }],\n            /**\n             * Border Radius Bottom Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-br': [{ 'rounded-br': scaleRadius() }],\n            /**\n             * Border Radius Bottom Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-bl': [{ 'rounded-bl': scaleRadius() }],\n            /**\n             * Border Width\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w': [{ border: scaleBorderWidth() }],\n            /**\n             * Border Width X\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-x': [{ 'border-x': scaleBorderWidth() }],\n            /**\n             * Border Width Y\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-y': [{ 'border-y': scaleBorderWidth() }],\n            /**\n             * Border Width Start\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-s': [{ 'border-s': scaleBorderWidth() }],\n            /**\n             * Border Width End\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-e': [{ 'border-e': scaleBorderWidth() }],\n            /**\n             * Border Width Top\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-t': [{ 'border-t': scaleBorderWidth() }],\n            /**\n             * Border Width Right\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-r': [{ 'border-r': scaleBorderWidth() }],\n            /**\n             * Border Width Bottom\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-b': [{ 'border-b': scaleBorderWidth() }],\n            /**\n             * Border Width Left\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-l': [{ 'border-l': scaleBorderWidth() }],\n            /**\n             * Divide Width X\n             * @see https://tailwindcss.com/docs/border-width#between-children\n             */\n            'divide-x': [{ 'divide-x': scaleBorderWidth() }],\n            /**\n             * Divide Width X Reverse\n             * @see https://tailwindcss.com/docs/border-width#between-children\n             */\n            'divide-x-reverse': ['divide-x-reverse'],\n            /**\n             * Divide Width Y\n             * @see https://tailwindcss.com/docs/border-width#between-children\n             */\n            'divide-y': [{ 'divide-y': scaleBorderWidth() }],\n            /**\n             * Divide Width Y Reverse\n             * @see https://tailwindcss.com/docs/border-width#between-children\n             */\n            'divide-y-reverse': ['divide-y-reverse'],\n            /**\n             * Border Style\n             * @see https://tailwindcss.com/docs/border-style\n             */\n            'border-style': [{ border: [...scaleLineStyle(), 'hidden', 'none'] }],\n            /**\n             * Divide Style\n             * @see https://tailwindcss.com/docs/border-style#setting-the-divider-style\n             */\n            'divide-style': [{ divide: [...scaleLineStyle(), 'hidden', 'none'] }],\n            /**\n             * Border Color\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color': [{ border: scaleColor() }],\n            /**\n             * Border Color X\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-x': [{ 'border-x': scaleColor() }],\n            /**\n             * Border Color Y\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-y': [{ 'border-y': scaleColor() }],\n            /**\n             * Border Color S\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-s': [{ 'border-s': scaleColor() }],\n            /**\n             * Border Color E\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-e': [{ 'border-e': scaleColor() }],\n            /**\n             * Border Color Top\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-t': [{ 'border-t': scaleColor() }],\n            /**\n             * Border Color Right\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-r': [{ 'border-r': scaleColor() }],\n            /**\n             * Border Color Bottom\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-b': [{ 'border-b': scaleColor() }],\n            /**\n             * Border Color Left\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-l': [{ 'border-l': scaleColor() }],\n            /**\n             * Divide Color\n             * @see https://tailwindcss.com/docs/divide-color\n             */\n            'divide-color': [{ divide: scaleColor() }],\n            /**\n             * Outline Style\n             * @see https://tailwindcss.com/docs/outline-style\n             */\n            'outline-style': [{ outline: [...scaleLineStyle(), 'none', 'hidden'] }],\n            /**\n             * Outline Offset\n             * @see https://tailwindcss.com/docs/outline-offset\n             */\n            'outline-offset': [\n                { 'outline-offset': [isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Outline Width\n             * @see https://tailwindcss.com/docs/outline-width\n             */\n            'outline-w': [\n                { outline: ['', isNumber, isArbitraryVariableLength, isArbitraryLength] },\n            ],\n            /**\n             * Outline Color\n             * @see https://tailwindcss.com/docs/outline-color\n             */\n            'outline-color': [{ outline: scaleColor() }],\n\n            // ---------------\n            // --- Effects ---\n            // ---------------\n\n            /**\n             * Box Shadow\n             * @see https://tailwindcss.com/docs/box-shadow\n             */\n            shadow: [\n                {\n                    shadow: [\n                        // Deprecated since Tailwind CSS v4.0.0\n                        '',\n                        'none',\n                        themeShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow,\n                    ],\n                },\n            ],\n            /**\n             * Box Shadow Color\n             * @see https://tailwindcss.com/docs/box-shadow#setting-the-shadow-color\n             */\n            'shadow-color': [{ shadow: scaleColor() }],\n            /**\n             * Inset Box Shadow\n             * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-shadow\n             */\n            'inset-shadow': [\n                {\n                    'inset-shadow': [\n                        'none',\n                        themeInsetShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow,\n                    ],\n                },\n            ],\n            /**\n             * Inset Box Shadow Color\n             * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-shadow-color\n             */\n            'inset-shadow-color': [{ 'inset-shadow': scaleColor() }],\n            /**\n             * Ring Width\n             * @see https://tailwindcss.com/docs/box-shadow#adding-a-ring\n             */\n            'ring-w': [{ ring: scaleBorderWidth() }],\n            /**\n             * Ring Width Inset\n             * @see https://v3.tailwindcss.com/docs/ring-width#inset-rings\n             * @deprecated since Tailwind CSS v4.0.0\n             * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n             */\n            'ring-w-inset': ['ring-inset'],\n            /**\n             * Ring Color\n             * @see https://tailwindcss.com/docs/box-shadow#setting-the-ring-color\n             */\n            'ring-color': [{ ring: scaleColor() }],\n            /**\n             * Ring Offset Width\n             * @see https://v3.tailwindcss.com/docs/ring-offset-width\n             * @deprecated since Tailwind CSS v4.0.0\n             * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n             */\n            'ring-offset-w': [{ 'ring-offset': [isNumber, isArbitraryLength] }],\n            /**\n             * Ring Offset Color\n             * @see https://v3.tailwindcss.com/docs/ring-offset-color\n             * @deprecated since Tailwind CSS v4.0.0\n             * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n             */\n            'ring-offset-color': [{ 'ring-offset': scaleColor() }],\n            /**\n             * Inset Ring Width\n             * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-ring\n             */\n            'inset-ring-w': [{ 'inset-ring': scaleBorderWidth() }],\n            /**\n             * Inset Ring Color\n             * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-ring-color\n             */\n            'inset-ring-color': [{ 'inset-ring': scaleColor() }],\n            /**\n             * Text Shadow\n             * @see https://tailwindcss.com/docs/text-shadow\n             */\n            'text-shadow': [\n                {\n                    'text-shadow': [\n                        'none',\n                        themeTextShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow,\n                    ],\n                },\n            ],\n            /**\n             * Text Shadow Color\n             * @see https://tailwindcss.com/docs/text-shadow#setting-the-shadow-color\n             */\n            'text-shadow-color': [{ 'text-shadow': scaleColor() }],\n            /**\n             * Opacity\n             * @see https://tailwindcss.com/docs/opacity\n             */\n            opacity: [{ opacity: [isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Mix Blend Mode\n             * @see https://tailwindcss.com/docs/mix-blend-mode\n             */\n            'mix-blend': [{ 'mix-blend': [...scaleBlendMode(), 'plus-darker', 'plus-lighter'] }],\n            /**\n             * Background Blend Mode\n             * @see https://tailwindcss.com/docs/background-blend-mode\n             */\n            'bg-blend': [{ 'bg-blend': scaleBlendMode() }],\n            /**\n             * Mask Clip\n             * @see https://tailwindcss.com/docs/mask-clip\n             */\n            'mask-clip': [\n                { 'mask-clip': ['border', 'padding', 'content', 'fill', 'stroke', 'view'] },\n                'mask-no-clip',\n            ],\n            /**\n             * Mask Composite\n             * @see https://tailwindcss.com/docs/mask-composite\n             */\n            'mask-composite': [{ mask: ['add', 'subtract', 'intersect', 'exclude'] }],\n            /**\n             * Mask Image\n             * @see https://tailwindcss.com/docs/mask-image\n             */\n            'mask-image-linear-pos': [{ 'mask-linear': [isNumber] }],\n            'mask-image-linear-from-pos': [{ 'mask-linear-from': scaleMaskImagePosition() }],\n            'mask-image-linear-to-pos': [{ 'mask-linear-to': scaleMaskImagePosition() }],\n            'mask-image-linear-from-color': [{ 'mask-linear-from': scaleColor() }],\n            'mask-image-linear-to-color': [{ 'mask-linear-to': scaleColor() }],\n            'mask-image-t-from-pos': [{ 'mask-t-from': scaleMaskImagePosition() }],\n            'mask-image-t-to-pos': [{ 'mask-t-to': scaleMaskImagePosition() }],\n            'mask-image-t-from-color': [{ 'mask-t-from': scaleColor() }],\n            'mask-image-t-to-color': [{ 'mask-t-to': scaleColor() }],\n            'mask-image-r-from-pos': [{ 'mask-r-from': scaleMaskImagePosition() }],\n            'mask-image-r-to-pos': [{ 'mask-r-to': scaleMaskImagePosition() }],\n            'mask-image-r-from-color': [{ 'mask-r-from': scaleColor() }],\n            'mask-image-r-to-color': [{ 'mask-r-to': scaleColor() }],\n            'mask-image-b-from-pos': [{ 'mask-b-from': scaleMaskImagePosition() }],\n            'mask-image-b-to-pos': [{ 'mask-b-to': scaleMaskImagePosition() }],\n            'mask-image-b-from-color': [{ 'mask-b-from': scaleColor() }],\n            'mask-image-b-to-color': [{ 'mask-b-to': scaleColor() }],\n            'mask-image-l-from-pos': [{ 'mask-l-from': scaleMaskImagePosition() }],\n            'mask-image-l-to-pos': [{ 'mask-l-to': scaleMaskImagePosition() }],\n            'mask-image-l-from-color': [{ 'mask-l-from': scaleColor() }],\n            'mask-image-l-to-color': [{ 'mask-l-to': scaleColor() }],\n            'mask-image-x-from-pos': [{ 'mask-x-from': scaleMaskImagePosition() }],\n            'mask-image-x-to-pos': [{ 'mask-x-to': scaleMaskImagePosition() }],\n            'mask-image-x-from-color': [{ 'mask-x-from': scaleColor() }],\n            'mask-image-x-to-color': [{ 'mask-x-to': scaleColor() }],\n            'mask-image-y-from-pos': [{ 'mask-y-from': scaleMaskImagePosition() }],\n            'mask-image-y-to-pos': [{ 'mask-y-to': scaleMaskImagePosition() }],\n            'mask-image-y-from-color': [{ 'mask-y-from': scaleColor() }],\n            'mask-image-y-to-color': [{ 'mask-y-to': scaleColor() }],\n            'mask-image-radial': [{ 'mask-radial': [isArbitraryVariable, isArbitraryValue] }],\n            'mask-image-radial-from-pos': [{ 'mask-radial-from': scaleMaskImagePosition() }],\n            'mask-image-radial-to-pos': [{ 'mask-radial-to': scaleMaskImagePosition() }],\n            'mask-image-radial-from-color': [{ 'mask-radial-from': scaleColor() }],\n            'mask-image-radial-to-color': [{ 'mask-radial-to': scaleColor() }],\n            'mask-image-radial-shape': [{ 'mask-radial': ['circle', 'ellipse'] }],\n            'mask-image-radial-size': [\n                { 'mask-radial': [{ closest: ['side', 'corner'], farthest: ['side', 'corner'] }] },\n            ],\n            'mask-image-radial-pos': [{ 'mask-radial-at': scalePosition() }],\n            'mask-image-conic-pos': [{ 'mask-conic': [isNumber] }],\n            'mask-image-conic-from-pos': [{ 'mask-conic-from': scaleMaskImagePosition() }],\n            'mask-image-conic-to-pos': [{ 'mask-conic-to': scaleMaskImagePosition() }],\n            'mask-image-conic-from-color': [{ 'mask-conic-from': scaleColor() }],\n            'mask-image-conic-to-color': [{ 'mask-conic-to': scaleColor() }],\n            /**\n             * Mask Mode\n             * @see https://tailwindcss.com/docs/mask-mode\n             */\n            'mask-mode': [{ mask: ['alpha', 'luminance', 'match'] }],\n            /**\n             * Mask Origin\n             * @see https://tailwindcss.com/docs/mask-origin\n             */\n            'mask-origin': [\n                { 'mask-origin': ['border', 'padding', 'content', 'fill', 'stroke', 'view'] },\n            ],\n            /**\n             * Mask Position\n             * @see https://tailwindcss.com/docs/mask-position\n             */\n            'mask-position': [{ mask: scaleBgPosition() }],\n            /**\n             * Mask Repeat\n             * @see https://tailwindcss.com/docs/mask-repeat\n             */\n            'mask-repeat': [{ mask: scaleBgRepeat() }],\n            /**\n             * Mask Size\n             * @see https://tailwindcss.com/docs/mask-size\n             */\n            'mask-size': [{ mask: scaleBgSize() }],\n            /**\n             * Mask Type\n             * @see https://tailwindcss.com/docs/mask-type\n             */\n            'mask-type': [{ 'mask-type': ['alpha', 'luminance'] }],\n            /**\n             * Mask Image\n             * @see https://tailwindcss.com/docs/mask-image\n             */\n            'mask-image': [{ mask: ['none', isArbitraryVariable, isArbitraryValue] }],\n\n            // ---------------\n            // --- Filters ---\n            // ---------------\n\n            /**\n             * Filter\n             * @see https://tailwindcss.com/docs/filter\n             */\n            filter: [\n                {\n                    filter: [\n                        // Deprecated since Tailwind CSS v3.0.0\n                        '',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Blur\n             * @see https://tailwindcss.com/docs/blur\n             */\n            blur: [{ blur: scaleBlur() }],\n            /**\n             * Brightness\n             * @see https://tailwindcss.com/docs/brightness\n             */\n            brightness: [{ brightness: [isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Contrast\n             * @see https://tailwindcss.com/docs/contrast\n             */\n            contrast: [{ contrast: [isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Drop Shadow\n             * @see https://tailwindcss.com/docs/drop-shadow\n             */\n            'drop-shadow': [\n                {\n                    'drop-shadow': [\n                        // Deprecated since Tailwind CSS v4.0.0\n                        '',\n                        'none',\n                        themeDropShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow,\n                    ],\n                },\n            ],\n            /**\n             * Drop Shadow Color\n             * @see https://tailwindcss.com/docs/filter-drop-shadow#setting-the-shadow-color\n             */\n            'drop-shadow-color': [{ 'drop-shadow': scaleColor() }],\n            /**\n             * Grayscale\n             * @see https://tailwindcss.com/docs/grayscale\n             */\n            grayscale: [{ grayscale: ['', isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Hue Rotate\n             * @see https://tailwindcss.com/docs/hue-rotate\n             */\n            'hue-rotate': [{ 'hue-rotate': [isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Invert\n             * @see https://tailwindcss.com/docs/invert\n             */\n            invert: [{ invert: ['', isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Saturate\n             * @see https://tailwindcss.com/docs/saturate\n             */\n            saturate: [{ saturate: [isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Sepia\n             * @see https://tailwindcss.com/docs/sepia\n             */\n            sepia: [{ sepia: ['', isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Backdrop Filter\n             * @see https://tailwindcss.com/docs/backdrop-filter\n             */\n            'backdrop-filter': [\n                {\n                    'backdrop-filter': [\n                        // Deprecated since Tailwind CSS v3.0.0\n                        '',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Backdrop Blur\n             * @see https://tailwindcss.com/docs/backdrop-blur\n             */\n            'backdrop-blur': [{ 'backdrop-blur': scaleBlur() }],\n            /**\n             * Backdrop Brightness\n             * @see https://tailwindcss.com/docs/backdrop-brightness\n             */\n            'backdrop-brightness': [\n                { 'backdrop-brightness': [isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Contrast\n             * @see https://tailwindcss.com/docs/backdrop-contrast\n             */\n            'backdrop-contrast': [\n                { 'backdrop-contrast': [isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Grayscale\n             * @see https://tailwindcss.com/docs/backdrop-grayscale\n             */\n            'backdrop-grayscale': [\n                { 'backdrop-grayscale': ['', isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Hue Rotate\n             * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n             */\n            'backdrop-hue-rotate': [\n                { 'backdrop-hue-rotate': [isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Invert\n             * @see https://tailwindcss.com/docs/backdrop-invert\n             */\n            'backdrop-invert': [\n                { 'backdrop-invert': ['', isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Opacity\n             * @see https://tailwindcss.com/docs/backdrop-opacity\n             */\n            'backdrop-opacity': [\n                { 'backdrop-opacity': [isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Saturate\n             * @see https://tailwindcss.com/docs/backdrop-saturate\n             */\n            'backdrop-saturate': [\n                { 'backdrop-saturate': [isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Sepia\n             * @see https://tailwindcss.com/docs/backdrop-sepia\n             */\n            'backdrop-sepia': [\n                { 'backdrop-sepia': ['', isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n\n            // --------------\n            // --- Tables ---\n            // --------------\n\n            /**\n             * Border Collapse\n             * @see https://tailwindcss.com/docs/border-collapse\n             */\n            'border-collapse': [{ border: ['collapse', 'separate'] }],\n            /**\n             * Border Spacing\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing': [{ 'border-spacing': scaleUnambiguousSpacing() }],\n            /**\n             * Border Spacing X\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing-x': [{ 'border-spacing-x': scaleUnambiguousSpacing() }],\n            /**\n             * Border Spacing Y\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing-y': [{ 'border-spacing-y': scaleUnambiguousSpacing() }],\n            /**\n             * Table Layout\n             * @see https://tailwindcss.com/docs/table-layout\n             */\n            'table-layout': [{ table: ['auto', 'fixed'] }],\n            /**\n             * Caption Side\n             * @see https://tailwindcss.com/docs/caption-side\n             */\n            caption: [{ caption: ['top', 'bottom'] }],\n\n            // ---------------------------------\n            // --- Transitions and Animation ---\n            // ---------------------------------\n\n            /**\n             * Transition Property\n             * @see https://tailwindcss.com/docs/transition-property\n             */\n            transition: [\n                {\n                    transition: [\n                        '',\n                        'all',\n                        'colors',\n                        'opacity',\n                        'shadow',\n                        'transform',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Transition Behavior\n             * @see https://tailwindcss.com/docs/transition-behavior\n             */\n            'transition-behavior': [{ transition: ['normal', 'discrete'] }],\n            /**\n             * Transition Duration\n             * @see https://tailwindcss.com/docs/transition-duration\n             */\n            duration: [{ duration: [isNumber, 'initial', isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Transition Timing Function\n             * @see https://tailwindcss.com/docs/transition-timing-function\n             */\n            ease: [\n                { ease: ['linear', 'initial', themeEase, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Transition Delay\n             * @see https://tailwindcss.com/docs/transition-delay\n             */\n            delay: [{ delay: [isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Animation\n             * @see https://tailwindcss.com/docs/animation\n             */\n            animate: [{ animate: ['none', themeAnimate, isArbitraryVariable, isArbitraryValue] }],\n\n            // ------------------\n            // --- Transforms ---\n            // ------------------\n\n            /**\n             * Backface Visibility\n             * @see https://tailwindcss.com/docs/backface-visibility\n             */\n            backface: [{ backface: ['hidden', 'visible'] }],\n            /**\n             * Perspective\n             * @see https://tailwindcss.com/docs/perspective\n             */\n            perspective: [\n                { perspective: [themePerspective, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Perspective Origin\n             * @see https://tailwindcss.com/docs/perspective-origin\n             */\n            'perspective-origin': [{ 'perspective-origin': scalePositionWithArbitrary() }],\n            /**\n             * Rotate\n             * @see https://tailwindcss.com/docs/rotate\n             */\n            rotate: [{ rotate: scaleRotate() }],\n            /**\n             * Rotate X\n             * @see https://tailwindcss.com/docs/rotate\n             */\n            'rotate-x': [{ 'rotate-x': scaleRotate() }],\n            /**\n             * Rotate Y\n             * @see https://tailwindcss.com/docs/rotate\n             */\n            'rotate-y': [{ 'rotate-y': scaleRotate() }],\n            /**\n             * Rotate Z\n             * @see https://tailwindcss.com/docs/rotate\n             */\n            'rotate-z': [{ 'rotate-z': scaleRotate() }],\n            /**\n             * Scale\n             * @see https://tailwindcss.com/docs/scale\n             */\n            scale: [{ scale: scaleScale() }],\n            /**\n             * Scale X\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-x': [{ 'scale-x': scaleScale() }],\n            /**\n             * Scale Y\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-y': [{ 'scale-y': scaleScale() }],\n            /**\n             * Scale Z\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-z': [{ 'scale-z': scaleScale() }],\n            /**\n             * Scale 3D\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-3d': ['scale-3d'],\n            /**\n             * Skew\n             * @see https://tailwindcss.com/docs/skew\n             */\n            skew: [{ skew: scaleSkew() }],\n            /**\n             * Skew X\n             * @see https://tailwindcss.com/docs/skew\n             */\n            'skew-x': [{ 'skew-x': scaleSkew() }],\n            /**\n             * Skew Y\n             * @see https://tailwindcss.com/docs/skew\n             */\n            'skew-y': [{ 'skew-y': scaleSkew() }],\n            /**\n             * Transform\n             * @see https://tailwindcss.com/docs/transform\n             */\n            transform: [\n                { transform: [isArbitraryVariable, isArbitraryValue, '', 'none', 'gpu', 'cpu'] },\n            ],\n            /**\n             * Transform Origin\n             * @see https://tailwindcss.com/docs/transform-origin\n             */\n            'transform-origin': [{ origin: scalePositionWithArbitrary() }],\n            /**\n             * Transform Style\n             * @see https://tailwindcss.com/docs/transform-style\n             */\n            'transform-style': [{ transform: ['3d', 'flat'] }],\n            /**\n             * Translate\n             * @see https://tailwindcss.com/docs/translate\n             */\n            translate: [{ translate: scaleTranslate() }],\n            /**\n             * Translate X\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-x': [{ 'translate-x': scaleTranslate() }],\n            /**\n             * Translate Y\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-y': [{ 'translate-y': scaleTranslate() }],\n            /**\n             * Translate Z\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-z': [{ 'translate-z': scaleTranslate() }],\n            /**\n             * Translate None\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-none': ['translate-none'],\n\n            // ---------------------\n            // --- Interactivity ---\n            // ---------------------\n\n            /**\n             * Accent Color\n             * @see https://tailwindcss.com/docs/accent-color\n             */\n            accent: [{ accent: scaleColor() }],\n            /**\n             * Appearance\n             * @see https://tailwindcss.com/docs/appearance\n             */\n            appearance: [{ appearance: ['none', 'auto'] }],\n            /**\n             * Caret Color\n             * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n             */\n            'caret-color': [{ caret: scaleColor() }],\n            /**\n             * Color Scheme\n             * @see https://tailwindcss.com/docs/color-scheme\n             */\n            'color-scheme': [\n                { scheme: ['normal', 'dark', 'light', 'light-dark', 'only-dark', 'only-light'] },\n            ],\n            /**\n             * Cursor\n             * @see https://tailwindcss.com/docs/cursor\n             */\n            cursor: [\n                {\n                    cursor: [\n                        'auto',\n                        'default',\n                        'pointer',\n                        'wait',\n                        'text',\n                        'move',\n                        'help',\n                        'not-allowed',\n                        'none',\n                        'context-menu',\n                        'progress',\n                        'cell',\n                        'crosshair',\n                        'vertical-text',\n                        'alias',\n                        'copy',\n                        'no-drop',\n                        'grab',\n                        'grabbing',\n                        'all-scroll',\n                        'col-resize',\n                        'row-resize',\n                        'n-resize',\n                        'e-resize',\n                        's-resize',\n                        'w-resize',\n                        'ne-resize',\n                        'nw-resize',\n                        'se-resize',\n                        'sw-resize',\n                        'ew-resize',\n                        'ns-resize',\n                        'nesw-resize',\n                        'nwse-resize',\n                        'zoom-in',\n                        'zoom-out',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Field Sizing\n             * @see https://tailwindcss.com/docs/field-sizing\n             */\n            'field-sizing': [{ 'field-sizing': ['fixed', 'content'] }],\n            /**\n             * Pointer Events\n             * @see https://tailwindcss.com/docs/pointer-events\n             */\n            'pointer-events': [{ 'pointer-events': ['auto', 'none'] }],\n            /**\n             * Resize\n             * @see https://tailwindcss.com/docs/resize\n             */\n            resize: [{ resize: ['none', '', 'y', 'x'] }],\n            /**\n             * Scroll Behavior\n             * @see https://tailwindcss.com/docs/scroll-behavior\n             */\n            'scroll-behavior': [{ scroll: ['auto', 'smooth'] }],\n            /**\n             * Scroll Margin\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-m': [{ 'scroll-m': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin X\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mx': [{ 'scroll-mx': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin Y\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-my': [{ 'scroll-my': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin Start\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-ms': [{ 'scroll-ms': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin End\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-me': [{ 'scroll-me': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin Top\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mt': [{ 'scroll-mt': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin Right\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mr': [{ 'scroll-mr': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin Bottom\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mb': [{ 'scroll-mb': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin Left\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-ml': [{ 'scroll-ml': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-p': [{ 'scroll-p': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding X\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-px': [{ 'scroll-px': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding Y\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-py': [{ 'scroll-py': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding Start\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-ps': [{ 'scroll-ps': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding End\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pe': [{ 'scroll-pe': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding Top\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pt': [{ 'scroll-pt': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding Right\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pr': [{ 'scroll-pr': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding Bottom\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pb': [{ 'scroll-pb': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding Left\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pl': [{ 'scroll-pl': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Snap Align\n             * @see https://tailwindcss.com/docs/scroll-snap-align\n             */\n            'snap-align': [{ snap: ['start', 'end', 'center', 'align-none'] }],\n            /**\n             * Scroll Snap Stop\n             * @see https://tailwindcss.com/docs/scroll-snap-stop\n             */\n            'snap-stop': [{ snap: ['normal', 'always'] }],\n            /**\n             * Scroll Snap Type\n             * @see https://tailwindcss.com/docs/scroll-snap-type\n             */\n            'snap-type': [{ snap: ['none', 'x', 'y', 'both'] }],\n            /**\n             * Scroll Snap Type Strictness\n             * @see https://tailwindcss.com/docs/scroll-snap-type\n             */\n            'snap-strictness': [{ snap: ['mandatory', 'proximity'] }],\n            /**\n             * Touch Action\n             * @see https://tailwindcss.com/docs/touch-action\n             */\n            touch: [{ touch: ['auto', 'none', 'manipulation'] }],\n            /**\n             * Touch Action X\n             * @see https://tailwindcss.com/docs/touch-action\n             */\n            'touch-x': [{ 'touch-pan': ['x', 'left', 'right'] }],\n            /**\n             * Touch Action Y\n             * @see https://tailwindcss.com/docs/touch-action\n             */\n            'touch-y': [{ 'touch-pan': ['y', 'up', 'down'] }],\n            /**\n             * Touch Action Pinch Zoom\n             * @see https://tailwindcss.com/docs/touch-action\n             */\n            'touch-pz': ['touch-pinch-zoom'],\n            /**\n             * User Select\n             * @see https://tailwindcss.com/docs/user-select\n             */\n            select: [{ select: ['none', 'text', 'all', 'auto'] }],\n            /**\n             * Will Change\n             * @see https://tailwindcss.com/docs/will-change\n             */\n            'will-change': [\n                {\n                    'will-change': [\n                        'auto',\n                        'scroll',\n                        'contents',\n                        'transform',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n\n            // -----------\n            // --- SVG ---\n            // -----------\n\n            /**\n             * Fill\n             * @see https://tailwindcss.com/docs/fill\n             */\n            fill: [{ fill: ['none', ...scaleColor()] }],\n            /**\n             * Stroke Width\n             * @see https://tailwindcss.com/docs/stroke-width\n             */\n            'stroke-w': [\n                {\n                    stroke: [\n                        isNumber,\n                        isArbitraryVariableLength,\n                        isArbitraryLength,\n                        isArbitraryNumber,\n                    ],\n                },\n            ],\n            /**\n             * Stroke\n             * @see https://tailwindcss.com/docs/stroke\n             */\n            stroke: [{ stroke: ['none', ...scaleColor()] }],\n\n            // ---------------------\n            // --- Accessibility ---\n            // ---------------------\n\n            /**\n             * Forced Color Adjust\n             * @see https://tailwindcss.com/docs/forced-color-adjust\n             */\n            'forced-color-adjust': [{ 'forced-color-adjust': ['auto', 'none'] }],\n        },\n        conflictingClassGroups: {\n            overflow: ['overflow-x', 'overflow-y'],\n            overscroll: ['overscroll-x', 'overscroll-y'],\n            inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n            'inset-x': ['right', 'left'],\n            'inset-y': ['top', 'bottom'],\n            flex: ['basis', 'grow', 'shrink'],\n            gap: ['gap-x', 'gap-y'],\n            p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n            px: ['pr', 'pl'],\n            py: ['pt', 'pb'],\n            m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n            mx: ['mr', 'ml'],\n            my: ['mt', 'mb'],\n            size: ['w', 'h'],\n            'font-size': ['leading'],\n            'fvn-normal': [\n                'fvn-ordinal',\n                'fvn-slashed-zero',\n                'fvn-figure',\n                'fvn-spacing',\n                'fvn-fraction',\n            ],\n            'fvn-ordinal': ['fvn-normal'],\n            'fvn-slashed-zero': ['fvn-normal'],\n            'fvn-figure': ['fvn-normal'],\n            'fvn-spacing': ['fvn-normal'],\n            'fvn-fraction': ['fvn-normal'],\n            'line-clamp': ['display', 'overflow'],\n            rounded: [\n                'rounded-s',\n                'rounded-e',\n                'rounded-t',\n                'rounded-r',\n                'rounded-b',\n                'rounded-l',\n                'rounded-ss',\n                'rounded-se',\n                'rounded-ee',\n                'rounded-es',\n                'rounded-tl',\n                'rounded-tr',\n                'rounded-br',\n                'rounded-bl',\n            ],\n            'rounded-s': ['rounded-ss', 'rounded-es'],\n            'rounded-e': ['rounded-se', 'rounded-ee'],\n            'rounded-t': ['rounded-tl', 'rounded-tr'],\n            'rounded-r': ['rounded-tr', 'rounded-br'],\n            'rounded-b': ['rounded-br', 'rounded-bl'],\n            'rounded-l': ['rounded-tl', 'rounded-bl'],\n            'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n            'border-w': [\n                'border-w-x',\n                'border-w-y',\n                'border-w-s',\n                'border-w-e',\n                'border-w-t',\n                'border-w-r',\n                'border-w-b',\n                'border-w-l',\n            ],\n            'border-w-x': ['border-w-r', 'border-w-l'],\n            'border-w-y': ['border-w-t', 'border-w-b'],\n            'border-color': [\n                'border-color-x',\n                'border-color-y',\n                'border-color-s',\n                'border-color-e',\n                'border-color-t',\n                'border-color-r',\n                'border-color-b',\n                'border-color-l',\n            ],\n            'border-color-x': ['border-color-r', 'border-color-l'],\n            'border-color-y': ['border-color-t', 'border-color-b'],\n            translate: ['translate-x', 'translate-y', 'translate-none'],\n            'translate-none': ['translate', 'translate-x', 'translate-y', 'translate-z'],\n            'scroll-m': [\n                'scroll-mx',\n                'scroll-my',\n                'scroll-ms',\n                'scroll-me',\n                'scroll-mt',\n                'scroll-mr',\n                'scroll-mb',\n                'scroll-ml',\n            ],\n            'scroll-mx': ['scroll-mr', 'scroll-ml'],\n            'scroll-my': ['scroll-mt', 'scroll-mb'],\n            'scroll-p': [\n                'scroll-px',\n                'scroll-py',\n                'scroll-ps',\n                'scroll-pe',\n                'scroll-pt',\n                'scroll-pr',\n                'scroll-pb',\n                'scroll-pl',\n            ],\n            'scroll-px': ['scroll-pr', 'scroll-pl'],\n            'scroll-py': ['scroll-pt', 'scroll-pb'],\n            touch: ['touch-x', 'touch-y', 'touch-pz'],\n            'touch-x': ['touch'],\n            'touch-y': ['touch'],\n            'touch-pz': ['touch'],\n        },\n        conflictingClassGroupModifiers: {\n            'font-size': ['leading'],\n        },\n        orderSensitiveModifiers: [\n            '*',\n            '**',\n            'after',\n            'backdrop',\n            'before',\n            'details-content',\n            'file',\n            'first-letter',\n            'first-line',\n            'marker',\n            'placeholder',\n            'selection',\n        ],\n    } as const satisfies Config<DefaultClassGroupIds, DefaultThemeGroupIds>\n}\n", "import { AnyConfig, ConfigExtension, NoInfer } from './types'\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nexport const mergeConfigs = <ClassGroupIds extends string, ThemeGroupIds extends string = never>(\n    baseConfig: AnyConfig,\n    {\n        cacheSize,\n        prefix,\n        experimentalParseClassName,\n        extend = {},\n        override = {},\n    }: ConfigExtension<ClassGroupIds, ThemeGroupIds>,\n) => {\n    overrideProperty(baseConfig, 'cacheSize', cacheSize)\n    overrideProperty(baseConfig, 'prefix', prefix)\n    overrideProperty(baseConfig, 'experimentalParseClassName', experimentalParseClassName)\n\n    overrideConfigProperties(baseConfig.theme, override.theme)\n    overrideConfigProperties(baseConfig.classGroups, override.classGroups)\n    overrideConfigProperties(baseConfig.conflictingClassGroups, override.conflictingClassGroups)\n    overrideConfigProperties(\n        baseConfig.conflictingClassGroupModifiers,\n        override.conflictingClassGroupModifiers,\n    )\n    overrideProperty(baseConfig, 'orderSensitiveModifiers', override.orderSensitiveModifiers)\n\n    mergeConfigProperties(baseConfig.theme, extend.theme)\n    mergeConfigProperties(baseConfig.classGroups, extend.classGroups)\n    mergeConfigProperties(baseConfig.conflictingClassGroups, extend.conflictingClassGroups)\n    mergeConfigProperties(\n        baseConfig.conflictingClassGroupModifiers,\n        extend.conflictingClassGroupModifiers,\n    )\n    mergeArrayProperties(baseConfig, extend, 'orderSensitiveModifiers')\n\n    return baseConfig\n}\n\nconst overrideProperty = <T extends object, K extends keyof T>(\n    baseObject: T,\n    overrideKey: K,\n    overrideValue: T[K] | undefined,\n) => {\n    if (overrideValue !== undefined) {\n        baseObject[overrideKey] = overrideValue\n    }\n}\n\nconst overrideConfigProperties = (\n    baseObject: Partial<Record<string, readonly unknown[]>>,\n    overrideObject: Partial<Record<string, readonly unknown[]>> | undefined,\n) => {\n    if (overrideObject) {\n        for (const key in overrideObject) {\n            overrideProperty(baseObject, key, overrideObject[key])\n        }\n    }\n}\n\nconst mergeConfigProperties = (\n    baseObject: Partial<Record<string, readonly unknown[]>>,\n    mergeObject: Partial<Record<string, readonly unknown[]>> | undefined,\n) => {\n    if (mergeObject) {\n        for (const key in mergeObject) {\n            mergeArrayProperties(baseObject, mergeObject, key)\n        }\n    }\n}\n\nconst mergeArrayProperties = <Key extends string>(\n    baseObject: Partial<Record<NoInfer<Key>, readonly unknown[]>>,\n    mergeObject: Partial<Record<NoInfer<Key>, readonly unknown[]>>,\n    key: Key,\n) => {\n    const mergeValue = mergeObject[key]\n\n    if (mergeValue !== undefined) {\n        baseObject[key] = baseObject[key] ? baseObject[key].concat(mergeValue) : mergeValue\n    }\n}\n", "import { createTailwindMerge } from './create-tailwind-merge'\nimport { getDefaultConfig } from './default-config'\nimport { mergeConfigs } from './merge-configs'\nimport { AnyConfig, ConfigExtension, DefaultClassGroupIds, DefaultThemeGroupIds } from './types'\n\ntype CreateConfigSubsequent = (config: AnyConfig) => AnyConfig\n\nexport const extendTailwindMerge = <\n    AdditionalClassGroupIds extends string = never,\n    AdditionalThemeGroupIds extends string = never,\n>(\n    configExtension:\n        | ConfigExtension<\n              DefaultClassGroupIds | AdditionalClassGroupIds,\n              DefaultThemeGroupIds | AdditionalThemeGroupIds\n          >\n        | CreateConfigSubsequent,\n    ...createConfig: CreateConfigSubsequent[]\n) =>\n    typeof configExtension === 'function'\n        ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig)\n        : createTailwindMerge(\n              () => mergeConfigs(getDefaultConfig(), configExtension),\n              ...createConfig,\n          )\n", "import { createTailwindMerge } from './create-tailwind-merge'\nimport { getDefaultConfig } from './default-config'\n\nexport const twMerge = createTailwindMerge(getDefaultConfig)\n"], "names": ["CLASS_PART_SEPARATOR", "createClassGroupUtils", "config", "classMap", "createClassMap", "conflictingClassGroups", "conflictingClassGroupModifiers", "getClassGroupId", "className", "classParts", "split", "length", "shift", "getGroupRecursive", "getGroupIdForArbitraryProperty", "getConflictingClassGroupIds", "classGroupId", "hasPostfixModifier", "conflicts", "classPartObject", "currentClassPart", "nextClassPartObject", "nextPart", "get", "classGroupFromNextClassPart", "slice", "undefined", "validators", "classRest", "join", "find", "validator", "arbitraryPropertyRegex", "test", "arbitraryPropertyClassName", "exec", "property", "substring", "indexOf", "theme", "classGroups", "Map", "processClassesRecursively", "classGroup", "for<PERSON>ach", "classDefinition", "classPartObjectToEdit", "get<PERSON>art", "isThemeGetter", "push", "Object", "entries", "key", "path", "currentClassPartObject", "pathPart", "has", "set", "func", "createLruCache", "maxCacheSize", "cacheSize", "cache", "previousCache", "update", "value", "IMPORTANT_MODIFIER", "MODIFIER_SEPARATOR", "MODIFIER_SEPARATOR_LENGTH", "createParseClassName", "prefix", "experimentalParseClassName", "parseClassName", "modifiers", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modifierStart", "postfixModifierPosition", "index", "currentCharacter", "baseClassNameWithImportantModifier", "baseClassName", "stripImportantModifier", "hasImportantModifier", "maybePostfixModifierPosition", "fullPrefix", "parseClassNameOriginal", "startsWith", "isExternal", "endsWith", "createSortModifiers", "orderSensitiveModifiers", "fromEntries", "map", "modifier", "sortModifiers", "sortedModifiers", "unsortedModifiers", "isPositionSensitive", "sort", "createConfigUtils", "SPLIT_CLASSES_REGEX", "mergeClassList", "classList", "configUtils", "classGroupsInConflict", "classNames", "trim", "result", "originalClassName", "variantModifier", "modifierId", "classId", "includes", "conflictGroups", "i", "group", "twJoin", "argument", "resolvedValue", "string", "arguments", "toValue", "mix", "k", "createTailwindMerge", "createConfigFirst", "createConfigRest", "cacheGet", "cacheSet", "functionToCall", "initTailwindMerge", "reduce", "previousConfig", "createConfigCurrent", "tailwindMerge", "cachedResult", "callTailwindMerge", "apply", "fromTheme", "themeGetter", "arbitraryValueRegex", "arbitraryVariableRegex", "fractionRegex", "tshirtUnitRegex", "lengthUnitRegex", "colorFunctionRegex", "shadowRegex", "imageRegex", "isFraction", "isNumber", "Number", "isNaN", "isInteger", "isPercent", "isTshirtSize", "isAny", "is<PERSON>engthOnly", "isNever", "is<PERSON><PERSON>ow", "isImage", "isAnyNonArbitrary", "isArbitraryValue", "isArbitraryVariable", "isArbitrarySize", "getIsArbitraryValue", "isLabelSize", "isArbitraryLength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isArbitraryNumber", "isLabelNumber", "isArbitraryPosition", "isLabelPosition", "isArbitraryImage", "isLabelImage", "isArbitraryShadow", "isLabel<PERSON><PERSON>ow", "isArbitraryVariableLength", "getIsArbitraryVariable", "isArbitraryVariableFamilyName", "isLabelFamilyName", "isArbitraryVariablePosition", "isArbitraryVariableSize", "isArbitraryVariableImage", "isArbitraryVariableShadow", "test<PERSON><PERSON><PERSON>", "testValue", "shouldMatchNoLabel", "label", "getDefaultConfig", "themeColor", "themeFont", "themeText", "themeFontWeight", "themeTracking", "themeLeading", "themeBreakpoint", "themeContainer", "themeSpacing", "themeRadius", "themeShadow", "themeInsetShadow", "themeTextShadow", "themeDropShadow", "themeBlur", "themePerspective", "themeAspect", "themeEase", "themeAnimate", "scaleBreak", "scalePosition", "scalePositionWithArbitrary", "scaleOverflow", "scaleOverscroll", "scaleUnambiguousSpacing", "scaleInset", "scaleGridTemplateColsRows", "scaleGridColRowStartAndEnd", "span", "scaleGridColRowStartOrEnd", "scaleGridAutoColsRows", "scaleAlignPrimaryAxis", "scaleAlignSecondaryAxis", "scaleMargin", "scaleSizing", "scaleColor", "scaleBgPosition", "position", "scaleBgRepeat", "repeat", "scaleBgSize", "size", "scaleGradientStopPosition", "scaleRadius", "scaleBorderWidth", "scaleLineStyle", "scaleBlendMode", "scaleMaskImagePosition", "scaleBlur", "scaleRotate", "scaleScale", "scaleSkew", "scaleTranslate", "animate", "aspect", "blur", "breakpoint", "color", "container", "ease", "font", "leading", "perspective", "radius", "shadow", "spacing", "text", "tracking", "columns", "box", "display", "sr", "float", "clear", "isolation", "object", "overflow", "overscroll", "inset", "start", "end", "top", "right", "bottom", "left", "visibility", "z", "basis", "flex", "grow", "shrink", "order", "col", "row", "gap", "justify", "content", "items", "baseline", "self", "p", "px", "py", "ps", "pe", "pt", "pr", "pb", "pl", "m", "mx", "my", "ms", "me", "mt", "mr", "mb", "ml", "w", "screen", "h", "list", "placeholder", "decoration", "indent", "align", "whitespace", "break", "wrap", "hyphens", "bg", "linear", "to", "radial", "conic", "from", "via", "rounded", "border", "divide", "outline", "ring", "opacity", "mask", "closest", "farthest", "filter", "brightness", "contrast", "grayscale", "invert", "saturate", "sepia", "table", "caption", "transition", "duration", "delay", "backface", "rotate", "scale", "skew", "transform", "origin", "translate", "accent", "appearance", "caret", "scheme", "cursor", "resize", "scroll", "snap", "touch", "select", "fill", "stroke", "mergeConfigs", "baseConfig", "extend", "override", "overrideProperty", "overrideConfigProperties", "mergeConfigProperties", "mergeArrayProperties", "baseObject", "override<PERSON><PERSON>", "overrideValue", "overrideObject", "mergeObject", "mergeValue", "concat", "extendTailwindMerge", "configExtension", "createConfig", "twMerge"], "mappings": ";;;;;;;;;;AAsBA,MAAMA,oBAAoB,GAAG,GAAG;AAEzB,MAAMC,qBAAqB,IAAIC,MAAiB,IAAI;IACvD,MAAMC,QAAQ,GAAGC,cAAc,CAACF,MAAM,CAAC;IACvC,MAAM,EAAEG,sBAAsB,EAAEC,8BAAAA,EAAgC,GAAGJ,MAAM;IAEzE,MAAMK,eAAe,IAAIC,SAAiB,IAAI;QAC1C,MAAMC,UAAU,GAAGD,SAAS,CAACE,KAAK,CAACV,oBAAoB,CAAC;;QAGxD,IAAIS,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,IAAIA,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;YACjDF,UAAU,CAACG,KAAK,CAAE,CAAA;;QAGtB,OAAOC,iBAAiB,CAACJ,UAAU,EAAEN,QAAQ,CAAC,IAAIW,8BAA8B,CAACN,SAAS,CAAC;IAC9F,CAAA;IAED,MAAMO,2BAA2B,GAAGA,CAChCC,YAA8B,EAC9BC,kBAA2B,KAC3B;QACA,MAAMC,SAAS,GAAGb,sBAAsB,CAACW,YAAY,CAAC,IAAI,EAAE;QAE5D,IAAIC,kBAAkB,IAAIX,8BAA8B,CAACU,YAAY,CAAC,EAAE;YACpE,OAAO,CAAC;mBAAGE,SAAS,EAAE;mBAAGZ,8BAA8B,CAACU,YAAY,CAAE;aAAC;;QAG3E,OAAOE,SAAS;IACnB,CAAA;IAED,OAAO;QACHX,eAAe;QACfQ;IACH,CAAA;AACL,CAAC;AAED,MAAMF,iBAAiB,GAAGA,CACtBJ,UAAoB,EACpBU,eAAgC,KACF;IAC9B,IAAIV,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;QACzB,OAAOQ,eAAe,CAACH,YAAY;;IAGvC,MAAMI,gBAAgB,GAAGX,UAAU,CAAC,CAAC,CAAE;IACvC,MAAMY,mBAAmB,GAAGF,eAAe,CAACG,QAAQ,CAACC,GAAG,CAACH,gBAAgB,CAAC;IAC1E,MAAMI,2BAA2B,GAAGH,mBAAA,GAC9BR,iBAAiB,CAACJ,UAAU,CAACgB,KAAK,CAAC,CAAC,CAAC,EAAEJ,mBAAmB,CAAA,GAC1DK,SAAS;IAEf,IAAIF,2BAA2B,EAAE;QAC7B,OAAOA,2BAA2B;;IAGtC,IAAIL,eAAe,CAACQ,UAAU,CAAChB,MAAM,KAAK,CAAC,EAAE;QACzC,OAAOe,SAAS;;IAGpB,MAAME,SAAS,GAAGnB,UAAU,CAACoB,IAAI,CAAC7B,oBAAoB,CAAC;IAEvD,OAAOmB,eAAe,CAACQ,UAAU,CAACG,IAAI,CAAC,CAAC,EAAEC,SAAAA,EAAW,GAAKA,SAAS,CAACH,SAAS,CAAC,CAAC,EAAEZ,YAAY;AACjG,CAAC;AAED,MAAMgB,sBAAsB,GAAG,YAAY;AAE3C,MAAMlB,8BAA8B,IAAIN,SAAiB,IAAI;IACzD,IAAIwB,sBAAsB,CAACC,IAAI,CAACzB,SAAS,CAAC,EAAE;QACxC,MAAM0B,0BAA0B,GAAGF,sBAAsB,CAACG,IAAI,CAAC3B,SAAS,CAAE,CAAC,CAAC,CAAC;QAC7E,MAAM4B,QAAQ,GAAGF,0BAA0B,EAAEG,SAAS,CAClD,CAAC,EACDH,0BAA0B,CAACI,OAAO,CAAC,GAAG,CAAC,CAC1C;QAED,IAAIF,QAAQ,EAAE;;YAEV,OAAO,aAAa,GAAGA,QAAQ;;;AAG3C,CAAC;AAED;;CAEG,GACI,MAAMhC,cAAc,IAAIF,MAAkD,IAAI;IACjF,MAAM,EAAEqC,KAAK,EAAEC,WAAAA,EAAa,GAAGtC,MAAM;IACrC,MAAMC,QAAQ,GAAoB;QAC9BmB,QAAQ,EAAE,IAAImB,GAAG,CAA2B,CAAA;QAC5Cd,UAAU,EAAE,EAAA;IACf,CAAA;IAED,IAAK,MAAMX,YAAY,IAAIwB,WAAW,CAAE;QACpCE,yBAAyB,CAACF,WAAW,CAACxB,YAAY,CAAE,EAAEb,QAAQ,EAAEa,YAAY,EAAEuB,KAAK,CAAC;;IAGxF,OAAOpC,QAAQ;AACnB,CAAC;AAED,MAAMuC,yBAAyB,GAAGA,CAC9BC,UAAwC,EACxCxB,eAAgC,EAChCH,YAA8B,EAC9BuB,KAAoC,KACpC;IACAI,UAAU,CAACC,OAAO,EAAEC,eAAe,IAAI;QACnC,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;YACrC,MAAMC,qBAAqB,GACvBD,eAAe,KAAK,EAAE,GAAG1B,eAAe,GAAG4B,OAAO,CAAC5B,eAAe,EAAE0B,eAAe,CAAC;YACxFC,qBAAqB,CAAC9B,YAAY,GAAGA,YAAY;YACjD;;QAGJ,IAAI,OAAO6B,eAAe,KAAK,UAAU,EAAE;YACvC,IAAIG,aAAa,CAACH,eAAe,CAAC,EAAE;gBAChCH,yBAAyB,CACrBG,eAAe,CAACN,KAAK,CAAC,EACtBpB,eAAe,EACfH,YAAY,EACZuB,KAAK,CACR;gBACD;;YAGJpB,eAAe,CAACQ,UAAU,CAACsB,IAAI,CAAC;gBAC5BlB,SAAS,EAAEc,eAAe;gBAC1B7B;YACH,CAAA,CAAC;YAEF;;QAGJkC,MAAM,CAACC,OAAO,CAACN,eAAe,CAAC,CAACD,OAAO,CAAC,CAAC,CAACQ,GAAG,EAAET,UAAU,CAAC,KAAI;YAC1DD,yBAAyB,CACrBC,UAAU,EACVI,OAAO,CAAC5B,eAAe,EAAEiC,GAAG,CAAC,EAC7BpC,YAAY,EACZuB,KAAK,CACR;QACL,CAAC,CAAC;IACN,CAAC,CAAC;AACN,CAAC;AAED,MAAMQ,OAAO,GAAGA,CAAC5B,eAAgC,EAAEkC,IAAY,KAAI;IAC/D,IAAIC,sBAAsB,GAAGnC,eAAe;IAE5CkC,IAAI,CAAC3C,KAAK,CAACV,oBAAoB,CAAC,CAAC4C,OAAO,EAAEW,QAAQ,IAAI;QAClD,IAAI,CAACD,sBAAsB,CAAChC,QAAQ,CAACkC,GAAG,CAACD,QAAQ,CAAC,EAAE;YAChDD,sBAAsB,CAAChC,QAAQ,CAACmC,GAAG,CAACF,QAAQ,EAAE;gBAC1CjC,QAAQ,EAAE,IAAImB,GAAG,CAAE,CAAA;gBACnBd,UAAU,EAAE,EAAA;YACf,CAAA,CAAC;;QAGN2B,sBAAsB,GAAGA,sBAAsB,CAAChC,QAAQ,CAACC,GAAG,CAACgC,QAAQ,CAAE;IAC3E,CAAC,CAAC;IAEF,OAAOD,sBAAsB;AACjC,CAAC;AAED,MAAMN,aAAa,IAAIU,IAAkC,GACpDA,IAAoB,CAACV,aAAa;AC9KvC,oJAAA;AACO,MAAMW,cAAc,IAAgBC,YAAoB,IAA0B;IACrF,IAAIA,YAAY,GAAG,CAAC,EAAE;QAClB,OAAO;YACHrC,GAAG,EAAEA,CAAA,GAAMG,SAAS;YACpB+B,GAAG,EAAEA,CAAA,IAAQ,CAAH;QACb,CAAA;;IAGL,IAAII,SAAS,GAAG,CAAC;IACjB,IAAIC,KAAK,GAAG,IAAIrB,GAAG,CAAc,CAAA;IACjC,IAAIsB,aAAa,GAAG,IAAItB,GAAG,CAAc,CAAA;IAEzC,MAAMuB,MAAM,GAAGA,CAACZ,GAAQ,EAAEa,KAAY,KAAI;QACtCH,KAAK,CAACL,GAAG,CAACL,GAAG,EAAEa,KAAK,CAAC;QACrBJ,SAAS,EAAE;QAEX,IAAIA,SAAS,GAAGD,YAAY,EAAE;YAC1BC,SAAS,GAAG,CAAC;YACbE,aAAa,GAAGD,KAAK;YACrBA,KAAK,GAAG,IAAIrB,GAAG,CAAE,CAAA;;IAExB,CAAA;IAED,OAAO;QACHlB,GAAGA,EAAC6B,GAAG,EAAA;YACH,IAAIa,KAAK,GAAGH,KAAK,CAACvC,GAAG,CAAC6B,GAAG,CAAC;YAE1B,IAAIa,KAAK,KAAKvC,SAAS,EAAE;gBACrB,OAAOuC,KAAK;;YAEhB,IAAI,CAACA,KAAK,GAAGF,aAAa,CAACxC,GAAG,CAAC6B,GAAG,CAAC,MAAM1B,SAAS,EAAE;gBAChDsC,MAAM,CAACZ,GAAG,EAAEa,KAAK,CAAC;gBAClB,OAAOA,KAAK;;QAEnB,CAAA;QACDR,GAAGA,EAACL,GAAG,EAAEa,KAAK,EAAA;YACV,IAAIH,KAAK,CAACN,GAAG,CAACJ,GAAG,CAAC,EAAE;gBAChBU,KAAK,CAACL,GAAG,CAACL,GAAG,EAAEa,KAAK,CAAC;mBAClB;gBACHD,MAAM,CAACZ,GAAG,EAAEa,KAAK,CAAC;;QAEzB;IACJ,CAAA;AACL,CAAC;ACjDM,MAAMC,kBAAkB,GAAG,GAAG;AACrC,MAAMC,kBAAkB,GAAG,GAAG;AAC9B,MAAMC,yBAAyB,GAAGD,kBAAkB,CAACxD,MAAM;AAEpD,MAAM0D,oBAAoB,IAAInE,MAAiB,IAAI;IACtD,MAAM,EAAEoE,MAAM,EAAEC,0BAAAA,EAA4B,GAAGrE,MAAM;IAErD;;;;;GAKG,GACH,IAAIsE,cAAc,IAAIhE,SAAiB,IAAqB;QACxD,MAAMiE,SAAS,GAAG,EAAE;QAEpB,IAAIC,YAAY,GAAG,CAAC;QACpB,IAAIC,UAAU,GAAG,CAAC;QAClB,IAAIC,aAAa,GAAG,CAAC;QACrB,IAAIC,uBAA2C;QAE/C,IAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGtE,SAAS,CAACG,MAAM,EAAEmE,KAAK,EAAE,CAAE;YACnD,IAAIC,gBAAgB,GAAGvE,SAAS,CAACsE,KAAK,CAAC;YAEvC,IAAIJ,YAAY,KAAK,CAAC,IAAIC,UAAU,KAAK,CAAC,EAAE;gBACxC,IAAII,gBAAgB,KAAKZ,kBAAkB,EAAE;oBACzCM,SAAS,CAACxB,IAAI,CAACzC,SAAS,CAACiB,KAAK,CAACmD,aAAa,EAAEE,KAAK,CAAC,CAAC;oBACrDF,aAAa,GAAGE,KAAK,GAAGV,yBAAyB;oBACjD;;gBAGJ,IAAIW,gBAAgB,KAAK,GAAG,EAAE;oBAC1BF,uBAAuB,GAAGC,KAAK;oBAC/B;;;YAIR,IAAIC,gBAAgB,KAAK,GAAG,EAAE;gBAC1BL,YAAY,EAAE;mBACX,IAAIK,gBAAgB,KAAK,GAAG,EAAE;gBACjCL,YAAY,EAAE;mBACX,IAAIK,gBAAgB,KAAK,GAAG,EAAE;gBACjCJ,UAAU,EAAE;mBACT,IAAII,gBAAgB,KAAK,GAAG,EAAE;gBACjCJ,UAAU,EAAE;;;QAIpB,MAAMK,kCAAkC,GACpCP,SAAS,CAAC9D,MAAM,KAAK,CAAC,GAAGH,SAAS,GAAGA,SAAS,CAAC6B,SAAS,CAACuC,aAAa,CAAC;QAC3E,MAAMK,aAAa,GAAGC,sBAAsB,CAACF,kCAAkC,CAAC;QAChF,MAAMG,oBAAoB,GAAGF,aAAa,KAAKD,kCAAkC;QACjF,MAAMI,4BAA4B,GAC9BP,uBAAuB,IAAIA,uBAAuB,GAAGD,aAAA,GAC/CC,uBAAuB,GAAGD,aAAA,GAC1BlD,SAAS;QAEnB,OAAO;YACH+C,SAAS;YACTU,oBAAoB;YACpBF,aAAa;YACbG;QACH,CAAA;IACJ,CAAA;IAED,IAAId,MAAM,EAAE;QACR,MAAMe,UAAU,GAAGf,MAAM,GAAGH,kBAAkB;QAC9C,MAAMmB,sBAAsB,GAAGd,cAAc;QAC7CA,cAAc,IAAIhE,SAAS,GACvBA,SAAS,CAAC+E,UAAU,CAACF,UAAU,CAAA,GACzBC,sBAAsB,CAAC9E,SAAS,CAAC6B,SAAS,CAACgD,UAAU,CAAC1E,MAAM,CAAC,CAAA,GAC7D;gBACI6E,UAAU,EAAE,IAAI;gBAChBf,SAAS,EAAE,EAAE;gBACbU,oBAAoB,EAAE,KAAK;gBAC3BF,aAAa,EAAEzE,SAAS;gBACxB4E,4BAA4B,EAAE1D;YACjC,CAAA;;IAGf,IAAI6C,0BAA0B,EAAE;QAC5B,MAAMe,sBAAsB,GAAGd,cAAc;QAC7CA,cAAc,IAAIhE,SAAS,GACvB+D,0BAA0B,CAAC;gBAAE/D,SAAS;gBAAEgE,cAAc,EAAEc;aAAwB,CAAC;;IAGzF,OAAOd,cAAc;AACzB,CAAC;AAED,MAAMU,sBAAsB,IAAID,aAAqB,IAAI;IACrD,IAAIA,aAAa,CAACQ,QAAQ,CAACvB,kBAAkB,CAAC,EAAE;QAC5C,OAAOe,aAAa,CAAC5C,SAAS,CAAC,CAAC,EAAE4C,aAAa,CAACtE,MAAM,GAAG,CAAC,CAAC;;IAG/D;;;GAGG,GACH,IAAIsE,aAAa,CAACM,UAAU,CAACrB,kBAAkB,CAAC,EAAE;QAC9C,OAAOe,aAAa,CAAC5C,SAAS,CAAC,CAAC,CAAC;;IAGrC,OAAO4C,aAAa;AACxB,CAAC;ACvGD;;;;CAIG,GACI,MAAMS,mBAAmB,IAAIxF,MAAiB,IAAI;IACrD,MAAMyF,uBAAuB,GAAGzC,MAAM,CAAC0C,WAAW,CAC9C1F,MAAM,CAACyF,uBAAuB,CAACE,GAAG,EAAEC,QAAQ,GAAK;YAACA,QAAQ;YAAE,IAAI;SAAC,CAAC,CACrE;IAED,MAAMC,aAAa,IAAItB,SAAmB,IAAI;QAC1C,IAAIA,SAAS,CAAC9D,MAAM,IAAI,CAAC,EAAE;YACvB,OAAO8D,SAAS;;QAGpB,MAAMuB,eAAe,GAAa,EAAE;QACpC,IAAIC,iBAAiB,GAAa,EAAE;QAEpCxB,SAAS,CAAC7B,OAAO,EAAEkD,QAAQ,IAAI;YAC3B,MAAMI,mBAAmB,GAAGJ,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIH,uBAAuB,CAACG,QAAQ,CAAC;YAEpF,IAAII,mBAAmB,EAAE;gBACrBF,eAAe,CAAC/C,IAAI,CAAC,GAAGgD,iBAAiB,CAACE,IAAI,CAAA,CAAE,EAAEL,QAAQ,CAAC;gBAC3DG,iBAAiB,GAAG,EAAE;mBACnB;gBACHA,iBAAiB,CAAChD,IAAI,CAAC6C,QAAQ,CAAC;;QAExC,CAAC,CAAC;QAEFE,eAAe,CAAC/C,IAAI,CAAC,GAAGgD,iBAAiB,CAACE,IAAI,CAAA,CAAE,CAAC;QAEjD,OAAOH,eAAe;IACzB,CAAA;IAED,OAAOD,aAAa;AACxB,CAAC;AC7BM,MAAMK,iBAAiB,IAAIlG,MAAiB,GAAA,CAAM;QACrD4D,KAAK,EAAEH,cAAc,CAAiBzD,MAAM,CAAC2D,SAAS,CAAC;QACvDW,cAAc,EAAEH,oBAAoB,CAACnE,MAAM,CAAC;QAC5C6F,aAAa,EAAEL,mBAAmB,CAACxF,MAAM,CAAC;QAC1C,GAAGD,qBAAqB,CAACC,MAAM,CAAA;IAClC,CAAA,CAAC;ACVF,MAAMmG,mBAAmB,GAAG,KAAK;AAE1B,MAAMC,cAAc,GAAGA,CAACC,SAAiB,EAAEC,WAAwB,KAAI;IAC1E,MAAM,EAAEhC,cAAc,EAAEjE,eAAe,EAAEQ,2BAA2B,EAAEgF,aAAAA,EAAe,GACjFS,WAAW;IAEf;;;;;;GAMG,GACH,MAAMC,qBAAqB,GAAa,EAAE;IAC1C,MAAMC,UAAU,GAAGH,SAAS,CAACI,IAAI,CAAA,CAAE,CAACjG,KAAK,CAAC2F,mBAAmB,CAAC;IAE9D,IAAIO,MAAM,GAAG,EAAE;IAEf,IAAK,IAAI9B,KAAK,GAAG4B,UAAU,CAAC/F,MAAM,GAAG,CAAC,EAAEmE,KAAK,IAAI,CAAC,EAAEA,KAAK,IAAI,CAAC,CAAE;QAC5D,MAAM+B,iBAAiB,GAAGH,UAAU,CAAC5B,KAAK,CAAE;QAE5C,MAAM,EACFU,UAAU,EACVf,SAAS,EACTU,oBAAoB,EACpBF,aAAa,EACbG,4BAAAA,EACH,GAAGZ,cAAc,CAACqC,iBAAiB,CAAC;QAErC,IAAIrB,UAAU,EAAE;YACZoB,MAAM,GAAGC,iBAAiB,GAAA,CAAID,MAAM,CAACjG,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGiG,MAAM,GAAGA,MAAM,CAAC;YACxE;;QAGJ,IAAI3F,kBAAkB,GAAG,CAAC,CAACmE,4BAA4B;QACvD,IAAIpE,YAAY,GAAGT,eAAe,CAC9BU,kBAAA,GACMgE,aAAa,CAAC5C,SAAS,CAAC,CAAC,EAAE+C,4BAA4B,CAAA,GACvDH,aAAa,CACtB;QAED,IAAI,CAACjE,YAAY,EAAE;YACf,IAAI,CAACC,kBAAkB,EAAE;;gBAErB2F,MAAM,GAAGC,iBAAiB,GAAA,CAAID,MAAM,CAACjG,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGiG,MAAM,GAAGA,MAAM,CAAC;gBACxE;;YAGJ5F,YAAY,GAAGT,eAAe,CAAC0E,aAAa,CAAC;YAE7C,IAAI,CAACjE,YAAY,EAAE;;gBAEf4F,MAAM,GAAGC,iBAAiB,GAAA,CAAID,MAAM,CAACjG,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGiG,MAAM,GAAGA,MAAM,CAAC;gBACxE;;YAGJ3F,kBAAkB,GAAG,KAAK;;QAG9B,MAAM6F,eAAe,GAAGf,aAAa,CAACtB,SAAS,CAAC,CAAC5C,IAAI,CAAC,GAAG,CAAC;QAE1D,MAAMkF,UAAU,GAAG5B,oBAAA,GACb2B,eAAe,GAAG5C,kBAAA,GAClB4C,eAAe;QAErB,MAAME,OAAO,GAAGD,UAAU,GAAG/F,YAAY;QAEzC,IAAIyF,qBAAqB,CAACQ,QAAQ,CAACD,OAAO,CAAC,EAAE;YAEzC;;QAGJP,qBAAqB,CAACxD,IAAI,CAAC+D,OAAO,CAAC;QAEnC,MAAME,cAAc,GAAGnG,2BAA2B,CAACC,YAAY,EAAEC,kBAAkB,CAAC;QACpF,IAAK,IAAIkG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,cAAc,CAACvG,MAAM,EAAE,EAAEwG,CAAC,CAAE;YAC5C,MAAMC,KAAK,GAAGF,cAAc,CAACC,CAAC,CAAE;YAChCV,qBAAqB,CAACxD,IAAI,CAAC8D,UAAU,GAAGK,KAAK,CAAC;;;QAIlDR,MAAM,GAAGC,iBAAiB,GAAA,CAAID,MAAM,CAACjG,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGiG,MAAM,GAAGA,MAAM,CAAC;;IAG5E,OAAOA,MAAM;AACjB,CAAC;ACxFD;;;;;;;;CAQG,YAMaS,MAAMA,CAAA,EAAA;IAClB,IAAIvC,KAAK,GAAG,CAAC;IACb,IAAIwC,QAAwB;IAC5B,IAAIC,aAAqB;IACzB,IAAIC,MAAM,GAAG,EAAE;IAEf,MAAO1C,KAAK,GAAG2C,SAAS,CAAC9G,MAAM,CAAE;QAC7B,IAAK2G,QAAQ,GAAGG,SAAS,CAAC3C,KAAK,EAAE,CAAC,EAAG;YACjC,IAAKyC,aAAa,GAAGG,OAAO,CAACJ,QAAQ,CAAC,EAAG;gBACrCE,MAAM,IAAA,CAAKA,MAAM,IAAI,GAAG,CAAC;gBACzBA,MAAM,IAAID,aAAa;;;;IAInC,OAAOC,MAAM;AACjB;AAEA,MAAME,OAAO,IAAIC,GAA4B,IAAI;IAC7C,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;QACzB,OAAOA,GAAG;;IAGd,IAAIJ,aAAqB;IACzB,IAAIC,MAAM,GAAG,EAAE;IAEf,IAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,CAAChH,MAAM,EAAEiH,CAAC,EAAE,CAAE;QACjC,IAAID,GAAG,CAACC,CAAC,CAAC,EAAE;YACR,IAAKL,aAAa,GAAGG,OAAO,CAACC,GAAG,CAACC,CAAC,CAA4B,CAAC,EAAG;gBAC9DJ,MAAM,IAAA,CAAKA,MAAM,IAAI,GAAG,CAAC;gBACzBA,MAAM,IAAID,aAAa;;;;IAKnC,OAAOC,MAAM;AACjB,CAAC;SCvCeK,mBAAmBA,CAC/BC,iBAAoC,EACpC,GAAGC,gBAA0C,EAAA;IAE7C,IAAIvB,WAAwB;IAC5B,IAAIwB,QAAqC;IACzC,IAAIC,QAAqC;IACzC,IAAIC,cAAc,GAAGC,iBAAiB;IAEtC,SAASA,iBAAiBA,CAAC5B,SAAiB,EAAA;QACxC,MAAMrG,MAAM,GAAG6H,gBAAgB,CAACK,MAAM,CAClC,CAACC,cAAc,EAAEC,mBAAmB,GAAKA,mBAAmB,CAACD,cAAc,CAAC,EAC5EP,iBAAiB,EAAe,CACnC;QAEDtB,WAAW,GAAGJ,iBAAiB,CAAClG,MAAM,CAAC;QACvC8H,QAAQ,GAAGxB,WAAW,CAAC1C,KAAK,CAACvC,GAAG;QAChC0G,QAAQ,GAAGzB,WAAW,CAAC1C,KAAK,CAACL,GAAG;QAChCyE,cAAc,GAAGK,aAAa;QAE9B,OAAOA,aAAa,CAAChC,SAAS,CAAC;;IAGnC,SAASgC,aAAaA,CAAChC,SAAiB,EAAA;QACpC,MAAMiC,YAAY,GAAGR,QAAQ,CAACzB,SAAS,CAAC;QAExC,IAAIiC,YAAY,EAAE;YACd,OAAOA,YAAY;;QAGvB,MAAM5B,MAAM,GAAGN,cAAc,CAACC,SAAS,EAAEC,WAAW,CAAC;QACrDyB,QAAQ,CAAC1B,SAAS,EAAEK,MAAM,CAAC;QAE3B,OAAOA,MAAM;;IAGjB,OAAO,SAAS6B,iBAAiBA,CAAA,EAAA;QAC7B,OAAOP,cAAc,CAACb,MAAM,CAACqB,KAAK,CAAC,IAAI,EAAEjB,SAAgB,CAAC,CAAC;IAC9D,CAAA;AACL;AC/Ca,MAAAkB,SAAS,IAGpBvF,GAAiE,IAAiB;IAChF,MAAMwF,WAAW,IAAIrG,KAAuE,GACxFA,KAAK,CAACa,GAAG,CAAC,IAAI,EAAE;IAEpBwF,WAAW,CAAC5F,aAAa,GAAG,IAAa;IAEzC,OAAO4F,WAAW;AACtB,CAAA;ACZA,MAAMC,mBAAmB,GAAG,6BAA6B;AACzD,MAAMC,sBAAsB,GAAG,6BAA6B;AAC5D,MAAMC,aAAa,GAAG,YAAY;AAClC,MAAMC,eAAe,GAAG,kCAAkC;AAC1D,MAAMC,eAAe,GACjB,2HAA2H;AAC/H,MAAMC,kBAAkB,GAAG,0CAA0C;AACrE,iGAAA;AACA,MAAMC,WAAW,GAAG,iEAAiE;AACrF,MAAMC,UAAU,GACZ,8FAA8F;AAE3F,MAAMC,UAAU,IAAIpF,KAAa,GAAK8E,aAAa,CAAC9G,IAAI,CAACgC,KAAK,CAAC;AAE/D,MAAMqF,QAAQ,IAAIrF,KAAa,GAAK,CAAC,CAACA,KAAK,IAAI,CAACsF,MAAM,CAACC,KAAK,CAACD,MAAM,CAACtF,KAAK,CAAC,CAAC;AAE3E,MAAMwF,SAAS,IAAIxF,KAAa,GAAK,CAAC,CAACA,KAAK,IAAIsF,MAAM,CAACE,SAAS,CAACF,MAAM,CAACtF,KAAK,CAAC,CAAC;AAE/E,MAAMyF,SAAS,IAAIzF,KAAa,GAAKA,KAAK,CAACwB,QAAQ,CAAC,GAAG,CAAC,IAAI6D,QAAQ,CAACrF,KAAK,CAACxC,KAAK,CAAC,CAAC,EAAE,CAAE,CAAA,CAAC,CAAC;AAExF,MAAMkI,YAAY,IAAI1F,KAAa,GAAK+E,eAAe,CAAC/G,IAAI,CAACgC,KAAK,CAAC;AAEnE,MAAM2F,KAAK,GAAGA,CAAA,GAAM,IAAI;AAE/B,MAAMC,YAAY,IAAI5F,KAAa,GAC/B,uJAAA;IACA,kFAAA;IACA,qGAAA;IACAgF,eAAe,CAAChH,IAAI,CAACgC,KAAK,CAAC,IAAI,CAACiF,kBAAkB,CAACjH,IAAI,CAACgC,KAAK,CAAC;AAElE,MAAM6F,OAAO,GAAGA,CAAA,GAAM,KAAK;AAE3B,MAAMC,QAAQ,IAAI9F,KAAa,GAAKkF,WAAW,CAAClH,IAAI,CAACgC,KAAK,CAAC;AAE3D,MAAM+F,OAAO,IAAI/F,KAAa,GAAKmF,UAAU,CAACnH,IAAI,CAACgC,KAAK,CAAC;AAElD,MAAMgG,iBAAiB,IAAIhG,KAAa,GAC3C,CAACiG,gBAAgB,CAACjG,KAAK,CAAC,IAAI,CAACkG,mBAAmB,CAAClG,KAAK,CAAC;AAEpD,MAAMmG,eAAe,IAAInG,KAAa,GAAKoG,mBAAmB,CAACpG,KAAK,EAAEqG,WAAW,EAAER,OAAO,CAAC;AAE3F,MAAMI,gBAAgB,IAAIjG,KAAa,GAAK4E,mBAAmB,CAAC5G,IAAI,CAACgC,KAAK,CAAC;AAE3E,MAAMsG,iBAAiB,IAAItG,KAAa,GAC3CoG,mBAAmB,CAACpG,KAAK,EAAEuG,aAAa,EAAEX,YAAY,CAAC;AAEpD,MAAMY,iBAAiB,IAAIxG,KAAa,GAC3CoG,mBAAmB,CAACpG,KAAK,EAAEyG,aAAa,EAAEpB,QAAQ,CAAC;AAEhD,MAAMqB,mBAAmB,IAAI1G,KAAa,GAC7CoG,mBAAmB,CAACpG,KAAK,EAAE2G,eAAe,EAAEd,OAAO,CAAC;AAEjD,MAAMe,gBAAgB,IAAI5G,KAAa,GAAKoG,mBAAmB,CAACpG,KAAK,EAAE6G,YAAY,EAAEd,OAAO,CAAC;AAE7F,MAAMe,iBAAiB,IAAI9G,KAAa,GAC3CoG,mBAAmB,CAACpG,KAAK,EAAE+G,aAAa,EAAEjB,QAAQ,CAAC;AAEhD,MAAMI,mBAAmB,IAAIlG,KAAa,GAAK6E,sBAAsB,CAAC7G,IAAI,CAACgC,KAAK,CAAC;AAEjF,MAAMgH,yBAAyB,IAAIhH,KAAa,GACnDiH,sBAAsB,CAACjH,KAAK,EAAEuG,aAAa,CAAC;AAEzC,MAAMW,6BAA6B,IAAIlH,KAAa,GACvDiH,sBAAsB,CAACjH,KAAK,EAAEmH,iBAAiB,CAAC;AAE7C,MAAMC,2BAA2B,IAAIpH,KAAa,GACrDiH,sBAAsB,CAACjH,KAAK,EAAE2G,eAAe,CAAC;AAE3C,MAAMU,uBAAuB,IAAIrH,KAAa,GAAKiH,sBAAsB,CAACjH,KAAK,EAAEqG,WAAW,CAAC;AAE7F,MAAMiB,wBAAwB,IAAItH,KAAa,GAClDiH,sBAAsB,CAACjH,KAAK,EAAE6G,YAAY,CAAC;AAExC,MAAMU,yBAAyB,IAAIvH,KAAa,GACnDiH,sBAAsB,CAACjH,KAAK,EAAE+G,aAAa,EAAE,IAAI,CAAC;AAEtD,UAAA;AAEA,MAAMX,mBAAmB,GAAGA,CACxBpG,KAAa,EACbwH,SAAqC,EACrCC,SAAqC,KACrC;IACA,MAAM9E,MAAM,GAAGiC,mBAAmB,CAAC1G,IAAI,CAAC8B,KAAK,CAAC;IAE9C,IAAI2C,MAAM,EAAE;QACR,IAAIA,MAAM,CAAC,CAAC,CAAC,EAAE;YACX,OAAO6E,SAAS,CAAC7E,MAAM,CAAC,CAAC,CAAC,CAAC;;QAG/B,OAAO8E,SAAS,CAAC9E,MAAM,CAAC,CAAC,CAAE,CAAC;;IAGhC,OAAO,KAAK;AAChB,CAAC;AAED,MAAMsE,sBAAsB,GAAGA,CAC3BjH,KAAa,EACbwH,SAAqC,EACrCE,kBAAkB,GAAG,KAAK,KAC1B;IACA,MAAM/E,MAAM,GAAGkC,sBAAsB,CAAC3G,IAAI,CAAC8B,KAAK,CAAC;IAEjD,IAAI2C,MAAM,EAAE;QACR,IAAIA,MAAM,CAAC,CAAC,CAAC,EAAE;YACX,OAAO6E,SAAS,CAAC7E,MAAM,CAAC,CAAC,CAAC,CAAC;;QAE/B,OAAO+E,kBAAkB;;IAG7B,OAAO,KAAK;AAChB,CAAC;AAED,SAAA;AAEA,MAAMf,eAAe,IAAIgB,KAAa,GAAKA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,YAAY;AAEzF,MAAMd,YAAY,IAAIc,KAAa,GAAKA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,KAAK;AAE5E,MAAMtB,WAAW,IAAIsB,KAAa,GAAKA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,SAAS;AAEpG,MAAMpB,aAAa,IAAIoB,KAAa,GAAKA,KAAK,KAAK,QAAQ;AAE3D,MAAMlB,aAAa,IAAIkB,KAAa,GAAKA,KAAK,KAAK,QAAQ;AAE3D,MAAMR,iBAAiB,IAAIQ,KAAa,GAAKA,KAAK,KAAK,aAAa;AAEpE,MAAMZ,aAAa,IAAIY,KAAa,GAAKA,KAAK,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrGpD,MAAMC,gBAAgB,GAAGA,CAAA,KAAK;IACjC;;;GAGG,SAGH,MAAMC,UAAU,GAAGnD,SAAS,CAAC,OAAO,CAAC;IACrC,MAAMoD,SAAS,GAAGpD,SAAS,CAAC,MAAM,CAAC;IACnC,MAAMqD,SAAS,GAAGrD,SAAS,CAAC,MAAM,CAAC;IACnC,MAAMsD,eAAe,GAAGtD,SAAS,CAAC,aAAa,CAAC;IAChD,MAAMuD,aAAa,GAAGvD,SAAS,CAAC,UAAU,CAAC;IAC3C,MAAMwD,YAAY,GAAGxD,SAAS,CAAC,SAAS,CAAC;IACzC,MAAMyD,eAAe,GAAGzD,SAAS,CAAC,YAAY,CAAC;IAC/C,MAAM0D,cAAc,GAAG1D,SAAS,CAAC,WAAW,CAAC;IAC7C,MAAM2D,YAAY,GAAG3D,SAAS,CAAC,SAAS,CAAC;IACzC,MAAM4D,WAAW,GAAG5D,SAAS,CAAC,QAAQ,CAAC;IACvC,MAAM6D,WAAW,GAAG7D,SAAS,CAAC,QAAQ,CAAC;IACvC,MAAM8D,gBAAgB,GAAG9D,SAAS,CAAC,cAAc,CAAC;IAClD,MAAM+D,eAAe,GAAG/D,SAAS,CAAC,aAAa,CAAC;IAChD,MAAMgE,eAAe,GAAGhE,SAAS,CAAC,aAAa,CAAC;IAChD,MAAMiE,SAAS,GAAGjE,SAAS,CAAC,MAAM,CAAC;IACnC,MAAMkE,gBAAgB,GAAGlE,SAAS,CAAC,aAAa,CAAC;IACjD,MAAMmE,WAAW,GAAGnE,SAAS,CAAC,QAAQ,CAAC;IACvC,MAAMoE,SAAS,GAAGpE,SAAS,CAAC,MAAM,CAAC;IACnC,MAAMqE,YAAY,GAAGrE,SAAS,CAAC,SAAS,CAAC;IAEzC;;;;;GAKG,SAGH,MAAMsE,UAAU,GAAGA,CAAA,GACf;YAAC,MAAM;YAAE,OAAO;YAAE,KAAK;YAAE,YAAY;YAAE,MAAM;YAAE,MAAM;YAAE,OAAO;YAAE,QAAQ;SAAU;IACtF,MAAMC,aAAa,GAAGA,CAAA,GAClB;YACI,QAAQ;YACR,KAAK;YACL,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;;YAEV,UAAU;YACV,WAAW;;YAEX,WAAW;YACX,cAAc;;YAEd,cAAc;YACd,aAAa;;YAEb,aAAa;SACP;IACd,MAAMC,0BAA0B,GAAGA,CAAA,GAC/B,CAAC;eAAGD,aAAa,CAAA,CAAE;YAAE/C,mBAAmB;YAAED,gBAAgB;SAAU;IACxE,MAAMkD,aAAa,GAAGA,CAAA,GAAM;YAAC,MAAM;YAAE,QAAQ;YAAE,MAAM;YAAE,SAAS;YAAE,QAAQ;SAAU;IACpF,MAAMC,eAAe,GAAGA,CAAA,GAAM;YAAC,MAAM;YAAE,SAAS;YAAE,MAAM;SAAU;IAClE,MAAMC,uBAAuB,GAAGA,CAAA,GAC5B;YAACnD,mBAAmB;YAAED,gBAAgB;YAAEoC,YAAY;SAAU;IAClE,MAAMiB,UAAU,GAAGA,CAAA,GAAM;YAAClE,UAAU;YAAE,MAAM;YAAE,MAAM,EAAE;eAAGiE,uBAAuB,EAAE;SAAU;IAC5F,MAAME,yBAAyB,GAAGA,CAAA,GAC9B;YAAC/D,SAAS;YAAE,MAAM;YAAE,SAAS;YAAEU,mBAAmB;YAAED,gBAAgB;SAAU;IAClF,MAAMuD,0BAA0B,GAAGA,CAAA,GAC/B;YACI,MAAM;YACN;gBAAEC,IAAI,EAAE;oBAAC,MAAM;oBAAEjE,SAAS;oBAAEU,mBAAmB;oBAAED,gBAAgB;iBAAA;YAAG,CAAA;YACpET,SAAS;YACTU,mBAAmB;YACnBD,gBAAgB;SACV;IACd,MAAMyD,yBAAyB,GAAGA,CAAA,GAC9B;YAAClE,SAAS;YAAE,MAAM;YAAEU,mBAAmB;YAAED,gBAAgB;SAAU;IACvE,MAAM0D,qBAAqB,GAAGA,CAAA,GAC1B;YAAC,MAAM;YAAE,KAAK;YAAE,KAAK;YAAE,IAAI;YAAEzD,mBAAmB;YAAED,gBAAgB;SAAU;IAChF,MAAM2D,qBAAqB,GAAGA,CAAA,GAC1B;YACI,OAAO;YACP,KAAK;YACL,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,SAAS;YACT,UAAU;YACV,aAAa;YACb,UAAU;SACJ;IACd,MAAMC,uBAAuB,GAAGA,CAAA,GAC5B;YAAC,OAAO;YAAE,KAAK;YAAE,QAAQ;YAAE,SAAS;YAAE,aAAa;YAAE,UAAU;SAAU;IAC7E,MAAMC,WAAW,GAAGA,CAAA,GAAM;YAAC,MAAM,EAAE;eAAGT,uBAAuB,CAAA,CAAE;SAAU;IACzE,MAAMU,WAAW,GAAGA,CAAA,GAChB;YACI3E,UAAU;YACV,MAAM;YACN,MAAM;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK,EACL;eAAGiE,uBAAuB,CAAE,CAAA;SACtB;IACd,MAAMW,UAAU,GAAGA,CAAA,GAAM;YAACnC,UAAU;YAAE3B,mBAAmB;YAAED,gBAAgB;SAAU;IACrF,MAAMgE,eAAe,GAAGA,CAAA,GACpB,CACI;eAAGhB,aAAa,CAAE,CAAA;YAClB7B,2BAA2B;YAC3BV,mBAAmB;YACnB;gBAAEwD,QAAQ,EAAE;oBAAChE,mBAAmB;oBAAED,gBAAgB;iBAAA;YAAG,CAAA;SAC/C;IACd,MAAMkE,aAAa,GAAGA,CAAA,GAAM;YAAC,WAAW;YAAE;gBAAEC,MAAM,EAAE;oBAAC,EAAE;oBAAE,GAAG;oBAAE,GAAG;oBAAE,OAAO;oBAAE,OAAO;iBAAA;YAAC,CAAE;SAAU;IAChG,MAAMC,WAAW,GAAGA,CAAA,GAChB;YACI,MAAM;YACN,OAAO;YACP,SAAS;YACThD,uBAAuB;YACvBlB,eAAe;YACf;gBAAEmE,IAAI,EAAE;oBAACpE,mBAAmB;oBAAED,gBAAgB;iBAAA;YAAG,CAAA;SAC3C;IACd,MAAMsE,yBAAyB,GAAGA,CAAA,GAC9B;YAAC9E,SAAS;YAAEuB,yBAAyB;YAAEV,iBAAiB;SAAU;IACtE,MAAMkE,WAAW,GAAGA,CAAA,GAChB;;YAEI,EAAE;YACF,MAAM;YACN,MAAM;YACNlC,WAAW;YACXpC,mBAAmB;YACnBD,gBAAgB;SACV;IACd,MAAMwE,gBAAgB,GAAGA,CAAA,GACrB;YAAC,EAAE;YAAEpF,QAAQ;YAAE2B,yBAAyB;YAAEV,iBAAiB;SAAU;IACzE,MAAMoE,cAAc,GAAGA,CAAA,GAAM;YAAC,OAAO;YAAE,QAAQ;YAAE,QAAQ;YAAE,QAAQ;SAAU;IAC7E,MAAMC,cAAc,GAAGA,CAAA,GACnB;YACI,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,SAAS;YACT,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,WAAW;YACX,KAAK;YACL,YAAY;YACZ,OAAO;YACP,YAAY;SACN;IACd,MAAMC,sBAAsB,GAAGA,CAAA,GAC3B;YAACvF,QAAQ;YAAEI,SAAS;YAAE2B,2BAA2B;YAAEV,mBAAmB;SAAU;IACpF,MAAMmE,SAAS,GAAGA,CAAA,GACd;;YAEI,EAAE;YACF,MAAM;YACNlC,SAAS;YACTzC,mBAAmB;YACnBD,gBAAgB;SACV;IACd,MAAM6E,WAAW,GAAGA,CAAA,GAAM;YAAC,MAAM;YAAEzF,QAAQ;YAAEa,mBAAmB;YAAED,gBAAgB;SAAU;IAC5F,MAAM8E,UAAU,GAAGA,CAAA,GAAM;YAAC,MAAM;YAAE1F,QAAQ;YAAEa,mBAAmB;YAAED,gBAAgB;SAAU;IAC3F,MAAM+E,SAAS,GAAGA,CAAA,GAAM;YAAC3F,QAAQ;YAAEa,mBAAmB;YAAED,gBAAgB;SAAU;IAClF,MAAMgF,cAAc,GAAGA,CAAA,GAAM;YAAC7F,UAAU;YAAE,MAAM,EAAE;eAAGiE,uBAAuB,CAAA,CAAE;SAAU;IAExF,OAAO;QACHzJ,SAAS,EAAE,GAAG;QACdtB,KAAK,EAAE;YACH4M,OAAO,EAAE;gBAAC,MAAM;gBAAE,MAAM;gBAAE,OAAO;gBAAE,QAAQ;aAAC;YAC5CC,MAAM,EAAE;gBAAC,OAAO;aAAC;YACjBC,IAAI,EAAE;gBAAC1F,YAAY;aAAC;YACpB2F,UAAU,EAAE;gBAAC3F,YAAY;aAAC;YAC1B4F,KAAK,EAAE;gBAAC3F,KAAK;aAAC;YACd4F,SAAS,EAAE;gBAAC7F,YAAY;aAAC;YACzB,aAAa,EAAE;gBAACA,YAAY;aAAC;YAC7B8F,IAAI,EAAE;gBAAC,IAAI;gBAAE,KAAK;gBAAE,QAAQ;aAAC;YAC7BC,IAAI,EAAE;gBAACzF,iBAAiB;aAAC;YACzB,aAAa,EAAE;gBACX,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,QAAQ;gBACR,QAAQ;gBACR,UAAU;gBACV,MAAM;gBACN,WAAW;gBACX,OAAO;aACV;YACD,cAAc,EAAE;gBAACN,YAAY;aAAC;YAC9BgG,OAAO,EAAE;gBAAC,MAAM;gBAAE,OAAO;gBAAE,MAAM;gBAAE,QAAQ;gBAAE,SAAS;gBAAE,OAAO;aAAC;YAChEC,WAAW,EAAE;gBAAC,UAAU;gBAAE,MAAM;gBAAE,QAAQ;gBAAE,UAAU;gBAAE,SAAS;gBAAE,MAAM;aAAC;YAC1EC,MAAM,EAAE;gBAAClG,YAAY;aAAC;YACtBmG,MAAM,EAAE;gBAACnG,YAAY;aAAC;YACtBoG,OAAO,EAAE;gBAAC,IAAI;gBAAEzG,QAAQ;aAAC;YACzB0G,IAAI,EAAE;gBAACrG,YAAY;aAAC;YACpB,aAAa,EAAE;gBAACA,YAAY;aAAC;YAC7BsG,QAAQ,EAAE;gBAAC,SAAS;gBAAE,OAAO;gBAAE,QAAQ;gBAAE,MAAM;gBAAE,OAAO;gBAAE,QAAQ;aAAA;QACrE,CAAA;QACDzN,WAAW,EAAE;;;;YAKT;;;OAGG,GACH4M,MAAM,EAAE;gBACJ;oBACIA,MAAM,EAAE;wBACJ,MAAM;wBACN,QAAQ;wBACR/F,UAAU;wBACVa,gBAAgB;wBAChBC,mBAAmB;wBACnB2C,WAAW;qBAAA;gBAElB,CAAA;aACJ;YACD;;;;OAIG,GACH0C,SAAS,EAAE;gBAAC,WAAW;aAAC;YACxB;;;OAGG,GACHU,OAAO,EAAE;gBACL;oBAAEA,OAAO,EAAE;wBAAC5G,QAAQ;wBAAEY,gBAAgB;wBAAEC,mBAAmB;wBAAEkC,cAAc;qBAAA;gBAAG,CAAA;aACjF;YACD;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAE,aAAa,EAAEY,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,cAAc,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAClD;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,cAAc,EAAE;wBAAC,MAAM;wBAAE,OAAO;wBAAE,YAAY;wBAAE,cAAc;qBAAA;iBAAG;aAAC;YACrF;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,gBAAgB,EAAE;wBAAC,OAAO;wBAAE,OAAO;qBAAA;gBAAC,CAAE;aAAC;YAC5D;;;OAGG,GACHkD,GAAG,EAAE;gBAAC;oBAAEA,GAAG,EAAE;wBAAC,QAAQ;wBAAE,SAAS;qBAAA;gBAAC,CAAE;aAAC;YACrC;;;OAGG,GACHC,OAAO,EAAE;gBACL,OAAO;gBACP,cAAc;gBACd,QAAQ;gBACR,MAAM;gBACN,aAAa;gBACb,OAAO;gBACP,cAAc;gBACd,eAAe;gBACf,YAAY;gBACZ,cAAc;gBACd,oBAAoB;gBACpB,oBAAoB;gBACpB,oBAAoB;gBACpB,iBAAiB;gBACjB,WAAW;gBACX,WAAW;gBACX,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,WAAW;gBACX,QAAQ;aACX;YACD;;;OAGG,GACHC,EAAE,EAAE;gBAAC,SAAS;gBAAE,aAAa;aAAC;YAC9B;;;OAGG,GACHC,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAAC,OAAO;wBAAE,MAAM;wBAAE,MAAM;wBAAE,OAAO;wBAAE,KAAK;qBAAA;iBAAG;aAAC;YAC7D;;;OAGG,GACHC,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAAC,MAAM;wBAAE,OAAO;wBAAE,MAAM;wBAAE,MAAM;wBAAE,OAAO;wBAAE,KAAK;qBAAA;iBAAG;aAAC;YACrE;;;OAGG,GACHC,SAAS,EAAE;gBAAC,SAAS;gBAAE,gBAAgB;aAAC;YACxC;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAEC,MAAM,EAAE;wBAAC,SAAS;wBAAE,OAAO;wBAAE,MAAM;wBAAE,MAAM;wBAAE,YAAY;qBAAA;iBAAG;aAAC;YAC9E;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAEA,MAAM,EAAEtD,0BAA0B,CAAE;gBAAA,CAAE;aAAC;YAC7D;;;OAGG,GACHuD,QAAQ,EAAE;gBAAC;oBAAEA,QAAQ,EAAEtD,aAAa,CAAE;gBAAA,CAAE;aAAC;YACzC;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,aAAa,CAAE;gBAAA,CAAE;aAAC;YACjD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,aAAa,CAAE;gBAAA,CAAE;aAAC;YACjD;;;OAGG,GACHuD,UAAU,EAAE;gBAAC;oBAAEA,UAAU,EAAEtD,eAAe,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,cAAc,EAAEA,eAAe,CAAE;gBAAA,CAAE;aAAC;YACvD;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,cAAc,EAAEA,eAAe,CAAE;gBAAA,CAAE;aAAC;YACvD;;;OAGG,GACHc,QAAQ,EAAE;gBAAC,QAAQ;gBAAE,OAAO;gBAAE,UAAU;gBAAE,UAAU;gBAAE,QAAQ;aAAC;YAC/D;;;OAGG,GACHyC,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAErD,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChC;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxC;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxC;;;OAGG,GACHsD,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAEtD,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChC;;;OAGG,GACHuD,GAAG,EAAE;gBAAC;oBAAEA,GAAG,EAAEvD,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC5B;;;OAGG,GACHwD,GAAG,EAAE;gBAAC;oBAAEA,GAAG,EAAExD,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC5B;;;OAGG,GACHyD,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAEzD,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChC;;;OAGG,GACH0D,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE1D,UAAU,CAAE;gBAAA,CAAE;aAAC;YAClC;;;OAGG,GACH2D,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAE3D,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC9B;;;OAGG,GACH4D,UAAU,EAAE;gBAAC,SAAS;gBAAE,WAAW;gBAAE,UAAU;aAAC;YAChD;;;OAGG,GACHC,CAAC,EAAE;gBAAC;oBAAEA,CAAC,EAAE;wBAAC3H,SAAS;wBAAE,MAAM;wBAAEU,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;;;;YAMtE;;;OAGG,GACHmH,KAAK,EAAE;gBACH;oBACIA,KAAK,EAAE;wBACHhI,UAAU;wBACV,MAAM;wBACN,MAAM;wBACNgD,cAAc,EACd;2BAAGiB,uBAAuB,CAAE,CAAA;qBAAA;gBAEnC,CAAA;aACJ;YACD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAEgE,IAAI,EAAE;wBAAC,KAAK;wBAAE,aAAa;wBAAE,KAAK;wBAAE,aAAa;qBAAA;iBAAG;aAAC;YAC1E;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC,QAAQ;wBAAE,MAAM;wBAAE,cAAc;qBAAA;iBAAG;aAAC;YAC3D;;;OAGG,GACHA,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAChI,QAAQ;wBAAED,UAAU;wBAAE,MAAM;wBAAE,SAAS;wBAAE,MAAM;wBAAEa,gBAAgB;qBAAA;iBAAG;aAAC;YACrF;;;OAGG,GACHqH,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC,EAAE;wBAAEjI,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YACvE;;;OAGG,GACHsH,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE;wBAAC,EAAE;wBAAElI,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YAC3E;;;OAGG,GACHuH,KAAK,EAAE;gBACH;oBACIA,KAAK,EAAE;wBACHhI,SAAS;wBACT,OAAO;wBACP,MAAM;wBACN,MAAM;wBACNU,mBAAmB;wBACnBD,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEsD,yBAAyB,CAAE;gBAAA,CAAE;aAAC;YAC3D;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAEkE,GAAG,EAAEjE,0BAA0B,CAAE;gBAAA,CAAE;aAAC;YACxD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEE,yBAAyB,CAAE;gBAAA,CAAE;aAAC;YAC3D;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAEA,yBAAyB,CAAE;gBAAA,CAAE;aAAC;YACvD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEH,yBAAyB,CAAE;gBAAA,CAAE;aAAC;YAC3D;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAEmE,GAAG,EAAElE,0BAA0B,CAAE;gBAAA,CAAE;aAAC;YACxD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEE,yBAAyB,CAAE;gBAAA,CAAE;aAAC;YAC3D;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAEA,yBAAyB,CAAE;gBAAA,CAAE;aAAC;YACvD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAAC,KAAK;wBAAE,KAAK;wBAAE,OAAO;wBAAE,WAAW;wBAAE,WAAW;qBAAA;iBAAG;aAAC;YACjF;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEC,qBAAqB,CAAE;gBAAA,CAAE;aAAC;YACvD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,qBAAqB,CAAE;gBAAA,CAAE;aAAC;YACvD;;;OAGG,GACHgE,GAAG,EAAE;gBAAC;oBAAEA,GAAG,EAAEtE,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzC;;;OAGG,GACH,OAAO,EAAE;gBAAC;oBAAE,OAAO,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACjD;;;OAGG,GACH,OAAO,EAAE;gBAAC;oBAAE,OAAO,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACjD;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAEuE,OAAO,EAAE,CAAC;2BAAGhE,qBAAqB,CAAE,CAAA;wBAAE,QAAQ;qBAAA;iBAAG;aAAC;YACxE;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAE,eAAe,EAAE,CAAC;2BAAGC,uBAAuB,CAAE,CAAA;wBAAE,QAAQ;qBAAA;iBAAG;aAAC;YAChF;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,cAAc,EAAE;wBAAC,MAAM,EAAE;2BAAGA,uBAAuB,CAAE,CAAA;qBAAA;iBAAG;aAAC;YAC5E;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAEgE,OAAO,EAAE;wBAAC,QAAQ,EAAE;2BAAGjE,qBAAqB,CAAE,CAAA;qBAAA;iBAAG;aAAC;YACtE;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAEkE,KAAK,EAAE,CAAC;2BAAGjE,uBAAuB,CAAE,CAAA;wBAAE;4BAAEkE,QAAQ,EAAE;gCAAC,EAAE;gCAAE,MAAM;6BAAA;wBAAC,CAAE;qBAAA;gBAAC,CAAE;aAAC;YACtF;;;OAGG,GACH,YAAY,EAAE;gBACV;oBAAEC,IAAI,EAAE;wBAAC,MAAM,EAAE;2BAAGnE,uBAAuB,CAAE,CAAA;wBAAE;4BAAEkE,QAAQ,EAAE;gCAAC,EAAE;gCAAE,MAAM;6BAAA;wBAAC,CAAE;qBAAA;gBAAG,CAAA;aAC/E;YACD;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAE,eAAe,EAAEnE,qBAAqB,CAAE;gBAAA,CAAE;aAAC;YAC/D;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAE,aAAa,EAAE,CAAC;2BAAGC,uBAAuB,CAAE,CAAA;wBAAE,UAAU;qBAAA;iBAAG;aAAC;YAC9E;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAAC,MAAM,EAAE;2BAAGA,uBAAuB,CAAE,CAAA;qBAAA;iBAAG;aAAC;;YAExE;;;OAGG,GACHoE,CAAC,EAAE;gBAAC;oBAAEA,CAAC,EAAE5E,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACrC;;;OAGG,GACH6E,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE7E,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvC;;;OAGG,GACH8E,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE9E,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvC;;;OAGG,GACH+E,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE/E,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvC;;;OAGG,GACHgF,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAEhF,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvC;;;OAGG,GACHiF,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAEjF,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvC;;;OAGG,GACHkF,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAElF,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvC;;;OAGG,GACHmF,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAEnF,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvC;;;OAGG,GACHoF,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAEpF,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvC;;;OAGG,GACHqF,CAAC,EAAE;gBAAC;oBAAEA,CAAC,EAAE5E,WAAW,CAAE;gBAAA,CAAE;aAAC;YACzB;;;OAGG,GACH6E,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE7E,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3B;;;OAGG,GACH8E,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE9E,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3B;;;OAGG,GACH+E,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE/E,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3B;;;OAGG,GACHgF,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAEhF,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3B;;;OAGG,GACHiF,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAEjF,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3B;;;OAGG,GACHkF,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAElF,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3B;;;OAGG,GACHmF,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAEnF,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3B;;;OAGG,GACHoF,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAEpF,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3B;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAET,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACrD;;;OAGG,GACH,iBAAiB,EAAE;gBAAC,iBAAiB;aAAC;YACtC;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACrD;;;OAGG,GACH,iBAAiB,EAAE;gBAAC,iBAAiB;aAAC;;;;YAMtC;;;OAGG,GACHiB,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAEP,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC/B;;;OAGG,GACHoF,CAAC,EAAE;gBAAC;oBAAEA,CAAC,EAAE;wBAAC/G,cAAc;wBAAE,QAAQ,EAAE;2BAAG2B,WAAW,CAAE,CAAA;qBAAA;iBAAG;aAAC;YACxD;;;OAGG,GACH,OAAO,EAAE;gBACL;oBACI,OAAO,EAAE;wBACL3B,cAAc;wBACd,QAAQ;wBAAA,yGAAA,GAER,MAAM,EACN;2BAAG2B,WAAW,CAAE,CAAA;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH,OAAO,EAAE;gBACL;oBACI,OAAO,EAAE;wBACL3B,cAAc;wBACd,QAAQ;wBACR,MAAM;wBAAA,mIAAA,GAEN,OAAO;wBAAA,mIAAA,GAEP;4BAAEgH,MAAM,EAAE;gCAACjH,eAAe;6BAAA;wBAAG,CAAA,EAC7B;2BAAG4B,WAAW,CAAE,CAAA;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACHsF,CAAC,EAAE;gBAAC;oBAAEA,CAAC,EAAE;wBAAC,QAAQ;wBAAE,IAAI,EAAE;2BAAGtF,WAAW,CAAE,CAAA;qBAAA;iBAAG;aAAC;YAC9C;;;OAGG,GACH,OAAO,EAAE;gBAAC;oBAAE,OAAO,EAAE;wBAAC,QAAQ;wBAAE,IAAI;wBAAE,MAAM,EAAE;2BAAGA,WAAW,CAAE,CAAA;qBAAA;iBAAG;aAAC;YAClE;;;OAGG,GACH,OAAO,EAAE;gBAAC;oBAAE,OAAO,EAAE;wBAAC,QAAQ;wBAAE,IAAI,EAAE;2BAAGA,WAAW,CAAE,CAAA;qBAAA;iBAAG;aAAC;;;;YAM1D;;;OAGG,GACH,WAAW,EAAE;gBACT;oBAAEgC,IAAI,EAAE;wBAAC,MAAM;wBAAEhE,SAAS;wBAAEf,yBAAyB;wBAAEV,iBAAiB;qBAAA;gBAAG,CAAA;aAC9E;YACD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC,aAAa;gBAAE,sBAAsB;aAAC;YACzD;;;OAGG,GACH,YAAY,EAAE;gBAAC,QAAQ;gBAAE,YAAY;aAAC;YACtC;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAEmF,IAAI,EAAE;wBAACzD,eAAe;wBAAE9B,mBAAmB;wBAAEM,iBAAiB;qBAAA;iBAAG;aAAC;YACpF;;;OAGG,GACH,cAAc,EAAE;gBACZ;oBACI,cAAc,EAAE;wBACZ,iBAAiB;wBACjB,iBAAiB;wBACjB,WAAW;wBACX,gBAAgB;wBAChB,QAAQ;wBACR,eAAe;wBACf,UAAU;wBACV,gBAAgB;wBAChB,gBAAgB;wBAChBf,SAAS;wBACTQ,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAEwF,IAAI,EAAE;wBAACvE,6BAA6B;wBAAEjB,gBAAgB;wBAAE6B,SAAS;qBAAA;iBAAG;aAAC;YACvF;;;OAGG,GACH,YAAY,EAAE;gBAAC,aAAa;aAAC;YAC7B;;;OAGG,GACH,aAAa,EAAE;gBAAC,SAAS;aAAC;YAC1B;;;OAGG,GACH,kBAAkB,EAAE;gBAAC,cAAc;aAAC;YACpC;;;OAGG,GACH,YAAY,EAAE;gBAAC,aAAa;gBAAE,eAAe;aAAC;YAC9C;;;OAGG,GACH,aAAa,EAAE;gBAAC,mBAAmB;gBAAE,cAAc;aAAC;YACpD;;;OAGG,GACH,cAAc,EAAE;gBAAC,oBAAoB;gBAAE,mBAAmB;aAAC;YAC3D;;;OAGG,GACHkE,QAAQ,EAAE;gBAAC;oBAAEA,QAAQ,EAAE;wBAAC/D,aAAa;wBAAE/B,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YAChF;;;OAGG,GACH,YAAY,EAAE;gBACV;oBAAE,YAAY,EAAE;wBAACZ,QAAQ;wBAAE,MAAM;wBAAEa,mBAAmB;wBAAEM,iBAAiB;qBAAA;gBAAG,CAAA;aAC/E;YACD;;;OAGG,GACHkF,OAAO,EAAE;gBACL;oBACIA,OAAO,EAAE;wBAAA,mIAAA,GAELxD,YAAY,EACZ;2BAAGmB,uBAAuB,CAAE,CAAA;qBAAA;gBAEnC,CAAA;aACJ;YACD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAAC,MAAM;wBAAEnD,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YACjF;;;OAGG,GACH,qBAAqB,EAAE;gBAAC;oBAAEqJ,IAAI,EAAE;wBAAC,QAAQ;wBAAE,SAAS;qBAAA;gBAAC,CAAE;aAAC;YACxD;;;OAGG,GACH,iBAAiB,EAAE;gBACf;oBAAEA,IAAI,EAAE;wBAAC,MAAM;wBAAE,SAAS;wBAAE,MAAM;wBAAEpJ,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAC/E;YACD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE8F,IAAI,EAAE;wBAAC,MAAM;wBAAE,QAAQ;wBAAE,OAAO;wBAAE,SAAS;wBAAE,OAAO;wBAAE,KAAK;qBAAA;iBAAG;aAAC;YACpF;;;;OAIG,GACH,mBAAmB,EAAE;gBAAC;oBAAEwD,WAAW,EAAEvF,UAAU,CAAE;gBAAA,CAAE;aAAC;YACpD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE+B,IAAI,EAAE/B,UAAU,CAAE;gBAAA,CAAE;aAAC;YACtC;;;OAGG,GACH,iBAAiB,EAAE;gBAAC,WAAW;gBAAE,UAAU;gBAAE,cAAc;gBAAE,cAAc;aAAC;YAC5E;;;OAGG,GACH,uBAAuB,EAAE;gBAAC;oBAAEwF,UAAU,EAAE,CAAC;2BAAG9E,cAAc,CAAE,CAAA;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACxE;;;OAGG,GACH,2BAA2B,EAAE;gBACzB;oBACI8E,UAAU,EAAE;wBACRnK,QAAQ;wBACR,WAAW;wBACX,MAAM;wBACNa,mBAAmB;wBACnBI,iBAAiB;qBAAA;gBAExB,CAAA;aACJ;YACD;;;OAGG,GACH,uBAAuB,EAAE;gBAAC;oBAAEkJ,UAAU,EAAExF,UAAU,CAAE;gBAAA,CAAE;aAAC;YACvD;;;OAGG,GACH,kBAAkB,EAAE;gBAChB;oBAAE,kBAAkB,EAAE;wBAAC3E,QAAQ;wBAAE,MAAM;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aACpF;YACD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC,WAAW;gBAAE,WAAW;gBAAE,YAAY;gBAAE,aAAa;aAAC;YACzE;;;OAGG,GACH,eAAe,EAAE;gBAAC,UAAU;gBAAE,eAAe;gBAAE,WAAW;aAAC;YAC3D;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE8F,IAAI,EAAE;wBAAC,MAAM;wBAAE,QAAQ;wBAAE,SAAS;wBAAE,QAAQ;qBAAA;iBAAG;aAAC;YAChE;;;OAGG,GACH0D,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAEpG,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,gBAAgB,EAAE;gBACd;oBACIqG,KAAK,EAAE;wBACH,UAAU;wBACV,KAAK;wBACL,QAAQ;wBACR,QAAQ;wBACR,UAAU;wBACV,aAAa;wBACb,KAAK;wBACL,OAAO;wBACPxJ,mBAAmB;wBACnBD,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH0J,UAAU,EAAE;gBACR;oBAAEA,UAAU,EAAE;wBAAC,QAAQ;wBAAE,QAAQ;wBAAE,KAAK;wBAAE,UAAU;wBAAE,UAAU;wBAAE,cAAc;qBAAA;gBAAG,CAAA;aACtF;YACD;;;OAGG,GACHC,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAAC,QAAQ;wBAAE,OAAO;wBAAE,KAAK;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACtD;;;OAGG,GACHC,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC,YAAY;wBAAE,UAAU;wBAAE,QAAQ;qBAAA;iBAAG;aAAC;YACtD;;;OAGG,GACHC,OAAO,EAAE;gBAAC;oBAAEA,OAAO,EAAE;wBAAC,MAAM;wBAAE,QAAQ;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YAClD;;;OAGG,GACHjC,OAAO,EAAE;gBAAC;oBAAEA,OAAO,EAAE;wBAAC,MAAM;wBAAE3H,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;;;;YAMvE;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAE8J,EAAE,EAAE;wBAAC,OAAO;wBAAE,OAAO;wBAAE,QAAQ;qBAAA;iBAAG;aAAC;YACvD;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAE;wBAAC,QAAQ;wBAAE,SAAS;wBAAE,SAAS;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACpE;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAAC,QAAQ;wBAAE,SAAS;wBAAE,SAAS;qBAAA;iBAAG;aAAC;YAChE;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAEA,EAAE,EAAE9F,eAAe,CAAE;gBAAA,CAAE;aAAC;YAC1C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE8F,EAAE,EAAE5F,aAAa,CAAE;gBAAA,CAAE;aAAC;YACtC;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE4F,EAAE,EAAE1F,WAAW,CAAE;gBAAA,CAAE;aAAC;YAClC;;;OAGG,GACH,UAAU,EAAE;gBACR;oBACI0F,EAAE,EAAE;wBACA,MAAM;wBACN;4BACIC,MAAM,EAAE;gCACJ;oCAAEC,EAAE,EAAE;wCAAC,GAAG;wCAAE,IAAI;wCAAE,GAAG;wCAAE,IAAI;wCAAE,GAAG;wCAAE,IAAI;wCAAE,GAAG;wCAAE,IAAI;qCAAA;gCAAG,CAAA;gCACpDzK,SAAS;gCACTU,mBAAmB;gCACnBD,gBAAgB;6BACnB;4BACDiK,MAAM,EAAE;gCAAC,EAAE;gCAAEhK,mBAAmB;gCAAED,gBAAgB;6BAAC;4BACnDkK,KAAK,EAAE;gCAAC3K,SAAS;gCAAEU,mBAAmB;gCAAED,gBAAgB;6BAAA;wBAC3D,CAAA;wBACDqB,wBAAwB;wBACxBV,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAEmJ,EAAE,EAAE/F,UAAU,CAAE;gBAAA,CAAE;aAAC;YAClC;;;OAGG,GACH,mBAAmB,EAAE;gBAAC;oBAAEoG,IAAI,EAAE7F,yBAAyB,CAAE;gBAAA,CAAE;aAAC;YAC5D;;;OAGG,GACH,kBAAkB,EAAE;gBAAC;oBAAE8F,GAAG,EAAE9F,yBAAyB,CAAE;gBAAA,CAAE;aAAC;YAC1D;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAE0F,EAAE,EAAE1F,yBAAyB,CAAE;gBAAA,CAAE;aAAC;YACxD;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAE6F,IAAI,EAAEpG,UAAU,CAAE;gBAAA,CAAE;aAAC;YACzC;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAEqG,GAAG,EAAErG,UAAU,CAAE;gBAAA,CAAE;aAAC;YACvC;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAEiG,EAAE,EAAEjG,UAAU,CAAE;gBAAA,CAAE;aAAC;;;;YAMrC;;;OAGG,GACHsG,OAAO,EAAE;gBAAC;oBAAEA,OAAO,EAAE9F,WAAW,CAAE;gBAAA,CAAE;aAAC;YACrC;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC7C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC7C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC7C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC7C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC7C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC7C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE+F,MAAM,EAAE9F,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAC5C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAEA,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAClD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAEA,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAClD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAEA,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAClD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAEA,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAClD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAEA,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAClD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAEA,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAClD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAEA,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAClD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAEA,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAClD;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAEA,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,kBAAkB,EAAE;gBAAC,kBAAkB;aAAC;YACxC;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAEA,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,kBAAkB,EAAE;gBAAC,kBAAkB;aAAC;YACxC;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE8F,MAAM,EAAE,CAAC;2BAAG7F,cAAc,CAAA,CAAE;wBAAE,QAAQ;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACrE;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE8F,MAAM,EAAE,CAAC;2BAAG9F,cAAc,CAAA,CAAE;wBAAE,QAAQ;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACrE;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE6F,MAAM,EAAEvG,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC1C;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChD;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAEwG,MAAM,EAAExG,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC1C;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAEyG,OAAO,EAAE,CAAC;2BAAG/F,cAAc,CAAA,CAAE;wBAAE,MAAM;wBAAE,QAAQ;qBAAA;iBAAG;aAAC;YACvE;;;OAGG,GACH,gBAAgB,EAAE;gBACd;oBAAE,gBAAgB,EAAE;wBAACrF,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAC1E;YACD;;;OAGG,GACH,WAAW,EAAE;gBACT;oBAAEwK,OAAO,EAAE;wBAAC,EAAE;wBAAEpL,QAAQ;wBAAE2B,yBAAyB;wBAAEV,iBAAiB;qBAAA;gBAAG,CAAA;aAC5E;YACD;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAEmK,OAAO,EAAEzG,UAAU,CAAE;gBAAA,CAAE;aAAC;;;;YAM5C;;;OAGG,GACH6B,MAAM,EAAE;gBACJ;oBACIA,MAAM,EAAE;;wBAEJ,EAAE;wBACF,MAAM;wBACNtD,WAAW;wBACXhB,yBAAyB;wBACzBT,iBAAiB;qBAAA;gBAExB,CAAA;aACJ;YACD;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE+E,MAAM,EAAE7B,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC1C;;;OAGG,GACH,cAAc,EAAE;gBACZ;oBACI,cAAc,EAAE;wBACZ,MAAM;wBACNxB,gBAAgB;wBAChBjB,yBAAyB;wBACzBT,iBAAiB;qBAAA;gBAExB,CAAA;aACJ;YACD;;;OAGG,GACH,oBAAoB,EAAE;gBAAC;oBAAE,cAAc,EAAEkD,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxD;;;OAGG,GACH,QAAQ,EAAE;gBAAC;oBAAE0G,IAAI,EAAEjG,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YACxC;;;;;OAKG,GACH,cAAc,EAAE;gBAAC,YAAY;aAAC;YAC9B;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAEiG,IAAI,EAAE1G,UAAU,CAAE;gBAAA,CAAE;aAAC;YACtC;;;;;OAKG,GACH,eAAe,EAAE;gBAAC;oBAAE,aAAa,EAAE;wBAAC3E,QAAQ;wBAAEiB,iBAAiB;qBAAA;gBAAC,CAAE;aAAC;YACnE;;;;;OAKG,GACH,mBAAmB,EAAE;gBAAC;oBAAE,aAAa,EAAE0D,UAAU,CAAE;gBAAA,CAAE;aAAC;YACtD;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,YAAY,EAAES,gBAAgB,CAAE;gBAAA,CAAE;aAAC;YACtD;;;OAGG,GACH,kBAAkB,EAAE;gBAAC;oBAAE,YAAY,EAAET,UAAU,CAAE;gBAAA,CAAE;aAAC;YACpD;;;OAGG,GACH,aAAa,EAAE;gBACX;oBACI,aAAa,EAAE;wBACX,MAAM;wBACNvB,eAAe;wBACflB,yBAAyB;wBACzBT,iBAAiB;qBAAA;gBAExB,CAAA;aACJ;YACD;;;OAGG,GACH,mBAAmB,EAAE;gBAAC;oBAAE,aAAa,EAAEkD,UAAU,CAAE;gBAAA,CAAE;aAAC;YACtD;;;OAGG,GACH2G,OAAO,EAAE;gBAAC;oBAAEA,OAAO,EAAE;wBAACtL,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YACzE;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE,CAAC;2BAAG0E,cAAc,CAAA,CAAE;wBAAE,aAAa;wBAAE,cAAc;qBAAA;iBAAG;aAAC;YACpF;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAEA,cAAc,CAAE;gBAAA,CAAE;aAAC;YAC9C;;;OAGG,GACH,WAAW,EAAE;gBACT;oBAAE,WAAW,EAAE;wBAAC,QAAQ;wBAAE,SAAS;wBAAE,SAAS;wBAAE,MAAM;wBAAE,QAAQ;wBAAE,MAAM;qBAAA;gBAAG,CAAA;gBAC3E,cAAc;aACjB;YACD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAEiG,IAAI,EAAE;wBAAC,KAAK;wBAAE,UAAU;wBAAE,WAAW;wBAAE,SAAS;qBAAA;iBAAG;aAAC;YACzE;;;OAGG,GACH,uBAAuB,EAAE;gBAAC;oBAAE,aAAa,EAAE;wBAACvL,QAAQ;qBAAA;gBAAC,CAAE;aAAC;YACxD,4BAA4B,EAAE;gBAAC;oBAAE,kBAAkB,EAAEuF,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAChF,0BAA0B,EAAE;gBAAC;oBAAE,gBAAgB,EAAEA,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAC5E,8BAA8B,EAAE;gBAAC;oBAAE,kBAAkB,EAAEZ,UAAU,CAAE;gBAAA,CAAE;aAAC;YACtE,4BAA4B,EAAE;gBAAC;oBAAE,gBAAgB,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAClE,uBAAuB,EAAE;gBAAC;oBAAE,aAAa,EAAEY,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YACtE,qBAAqB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAClE,yBAAyB,EAAE;gBAAC;oBAAE,aAAa,EAAEZ,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC5D,uBAAuB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxD,uBAAuB,EAAE;gBAAC;oBAAE,aAAa,EAAEY,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YACtE,qBAAqB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAClE,yBAAyB,EAAE;gBAAC;oBAAE,aAAa,EAAEZ,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC5D,uBAAuB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxD,uBAAuB,EAAE;gBAAC;oBAAE,aAAa,EAAEY,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YACtE,qBAAqB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAClE,yBAAyB,EAAE;gBAAC;oBAAE,aAAa,EAAEZ,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC5D,uBAAuB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxD,uBAAuB,EAAE;gBAAC;oBAAE,aAAa,EAAEY,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YACtE,qBAAqB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAClE,yBAAyB,EAAE;gBAAC;oBAAE,aAAa,EAAEZ,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC5D,uBAAuB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxD,uBAAuB,EAAE;gBAAC;oBAAE,aAAa,EAAEY,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YACtE,qBAAqB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAClE,yBAAyB,EAAE;gBAAC;oBAAE,aAAa,EAAEZ,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC5D,uBAAuB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxD,uBAAuB,EAAE;gBAAC;oBAAE,aAAa,EAAEY,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YACtE,qBAAqB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAClE,yBAAyB,EAAE;gBAAC;oBAAE,aAAa,EAAEZ,UAAU,CAAE;gBAAA,CAAE;aAAC;YAC5D,uBAAuB,EAAE;gBAAC;oBAAE,WAAW,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxD,mBAAmB,EAAE;gBAAC;oBAAE,aAAa,EAAE;wBAAC9D,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAC,CAAE;aAAC;YACjF,4BAA4B,EAAE;gBAAC;oBAAE,kBAAkB,EAAE2E,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAChF,0BAA0B,EAAE;gBAAC;oBAAE,gBAAgB,EAAEA,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAC5E,8BAA8B,EAAE;gBAAC;oBAAE,kBAAkB,EAAEZ,UAAU,CAAE;gBAAA,CAAE;aAAC;YACtE,4BAA4B,EAAE;gBAAC;oBAAE,gBAAgB,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAClE,yBAAyB,EAAE;gBAAC;oBAAE,aAAa,EAAE;wBAAC,QAAQ;wBAAE,SAAS;qBAAA;gBAAC,CAAE;aAAC;YACrE,wBAAwB,EAAE;gBACtB;oBAAE,aAAa,EAAE;wBAAC;4BAAE6G,OAAO,EAAE;gCAAC,MAAM;gCAAE,QAAQ;6BAAC;4BAAEC,QAAQ,EAAE;gCAAC,MAAM;gCAAE,QAAQ;6BAAA;wBAAG,CAAA;qBAAA;gBAAG,CAAA;aACrF;YACD,uBAAuB,EAAE;gBAAC;oBAAE,gBAAgB,EAAE7H,aAAa,CAAE;gBAAA,CAAE;aAAC;YAChE,sBAAsB,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAAC5D,QAAQ;qBAAA;gBAAC,CAAE;aAAC;YACtD,2BAA2B,EAAE;gBAAC;oBAAE,iBAAiB,EAAEuF,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAC9E,yBAAyB,EAAE;gBAAC;oBAAE,eAAe,EAAEA,sBAAsB,CAAE;gBAAA,CAAE;aAAC;YAC1E,6BAA6B,EAAE;gBAAC;oBAAE,iBAAiB,EAAEZ,UAAU,CAAE;gBAAA,CAAE;aAAC;YACpE,2BAA2B,EAAE;gBAAC;oBAAE,eAAe,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChE;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE4G,IAAI,EAAE;wBAAC,OAAO;wBAAE,WAAW;wBAAE,OAAO;qBAAA;iBAAG;aAAC;YACxD;;;OAGG,GACH,aAAa,EAAE;gBACX;oBAAE,aAAa,EAAE;wBAAC,QAAQ;wBAAE,SAAS;wBAAE,SAAS;wBAAE,MAAM;wBAAE,QAAQ;wBAAE,MAAM;qBAAA;gBAAG,CAAA;aAChF;YACD;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAEA,IAAI,EAAE3G,eAAe,CAAE;gBAAA,CAAE;aAAC;YAC9C;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAE2G,IAAI,EAAEzG,aAAa,CAAE;gBAAA,CAAE;aAAC;YAC1C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAEyG,IAAI,EAAEvG,WAAW,CAAE;gBAAA,CAAE;aAAC;YACtC;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAAC,OAAO;wBAAE,WAAW;qBAAA;gBAAC,CAAE;aAAC;YACtD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAEuG,IAAI,EAAE;wBAAC,MAAM;wBAAE1K,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;;;;YAMzE;;;OAGG,GACH8K,MAAM,EAAE;gBACJ;oBACIA,MAAM,EAAE;;wBAEJ,EAAE;wBACF,MAAM;wBACN7K,mBAAmB;wBACnBD,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACHmF,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAEP,SAAS,CAAE;gBAAA,CAAE;aAAC;YAC7B;;;OAGG,GACHmG,UAAU,EAAE;gBAAC;oBAAEA,UAAU,EAAE;wBAAC3L,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YAC/E;;;OAGG,GACHgL,QAAQ,EAAE;gBAAC;oBAAEA,QAAQ,EAAE;wBAAC5L,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YAC3E;;;OAGG,GACH,aAAa,EAAE;gBACX;oBACI,aAAa,EAAE;;wBAEX,EAAE;wBACF,MAAM;wBACNyC,eAAe;wBACfnB,yBAAyB;wBACzBT,iBAAiB;qBAAA;gBAExB,CAAA;aACJ;YACD;;;OAGG,GACH,mBAAmB,EAAE;gBAAC;oBAAE,aAAa,EAAEkD,UAAU,CAAE;gBAAA,CAAE;aAAC;YACtD;;;OAGG,GACHkH,SAAS,EAAE;gBAAC;oBAAEA,SAAS,EAAE;wBAAC,EAAE;wBAAE7L,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YACjF;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAACZ,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YACnF;;;OAGG,GACHkL,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE;wBAAC,EAAE;wBAAE9L,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YAC3E;;;OAGG,GACHmL,QAAQ,EAAE;gBAAC;oBAAEA,QAAQ,EAAE;wBAAC/L,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YAC3E;;;OAGG,GACHoL,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAAC,EAAE;wBAAEhM,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YACzE;;;OAGG,GACH,iBAAiB,EAAE;gBACf;oBACI,iBAAiB,EAAE;;wBAEf,EAAE;wBACF,MAAM;wBACNC,mBAAmB;wBACnBD,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAE,eAAe,EAAE4E,SAAS,CAAE;gBAAA,CAAE;aAAC;YACnD;;;OAGG,GACH,qBAAqB,EAAE;gBACnB;oBAAE,qBAAqB,EAAE;wBAACxF,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAC/E;YACD;;;OAGG,GACH,mBAAmB,EAAE;gBACjB;oBAAE,mBAAmB,EAAE;wBAACZ,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAC7E;YACD;;;OAGG,GACH,oBAAoB,EAAE;gBAClB;oBAAE,oBAAoB,EAAE;wBAAC,EAAE;wBAAEZ,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAClF;YACD;;;OAGG,GACH,qBAAqB,EAAE;gBACnB;oBAAE,qBAAqB,EAAE;wBAACZ,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAC/E;YACD;;;OAGG,GACH,iBAAiB,EAAE;gBACf;oBAAE,iBAAiB,EAAE;wBAAC,EAAE;wBAAEZ,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAC/E;YACD;;;OAGG,GACH,kBAAkB,EAAE;gBAChB;oBAAE,kBAAkB,EAAE;wBAACZ,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAC5E;YACD;;;OAGG,GACH,mBAAmB,EAAE;gBACjB;oBAAE,mBAAmB,EAAE;wBAACZ,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAC7E;YACD;;;OAGG,GACH,gBAAgB,EAAE;gBACd;oBAAE,gBAAgB,EAAE;wBAAC,EAAE;wBAAEZ,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAC9E;;;;YAMD;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAEsK,MAAM,EAAE;wBAAC,UAAU;wBAAE,UAAU;qBAAA;gBAAC,CAAE;aAAC;YACzD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,gBAAgB,EAAElH,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACnE;;;OAGG,GACH,kBAAkB,EAAE;gBAAC;oBAAE,kBAAkB,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvE;;;OAGG,GACH,kBAAkB,EAAE;gBAAC;oBAAE,kBAAkB,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvE;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAEiI,KAAK,EAAE;wBAAC,MAAM;wBAAE,OAAO;qBAAA;gBAAC,CAAE;aAAC;YAC9C;;;OAGG,GACHC,OAAO,EAAE;gBAAC;oBAAEA,OAAO,EAAE;wBAAC,KAAK;wBAAE,QAAQ;qBAAA;gBAAC,CAAE;aAAC;;;;YAMzC;;;OAGG,GACHC,UAAU,EAAE;gBACR;oBACIA,UAAU,EAAE;wBACR,EAAE;wBACF,KAAK;wBACL,QAAQ;wBACR,SAAS;wBACT,QAAQ;wBACR,WAAW;wBACX,MAAM;wBACNtL,mBAAmB;wBACnBD,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH,qBAAqB,EAAE;gBAAC;oBAAEuL,UAAU,EAAE;wBAAC,QAAQ;wBAAE,UAAU;qBAAA;gBAAC,CAAE;aAAC;YAC/D;;;OAGG,GACHC,QAAQ,EAAE;gBAAC;oBAAEA,QAAQ,EAAE;wBAACpM,QAAQ;wBAAE,SAAS;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YACtF;;;OAGG,GACHuF,IAAI,EAAE;gBACF;oBAAEA,IAAI,EAAE;wBAAC,QAAQ;wBAAE,SAAS;wBAAE1C,SAAS;wBAAE5C,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aACpF;YACD;;;OAGG,GACHyL,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAACrM,QAAQ;wBAAEa,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YACrE;;;OAGG,GACHiF,OAAO,EAAE;gBAAC;oBAAEA,OAAO,EAAE;wBAAC,MAAM;wBAAEnC,YAAY;wBAAE7C,mBAAmB;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;;;;YAMrF;;;OAGG,GACH0L,QAAQ,EAAE;gBAAC;oBAAEA,QAAQ,EAAE;wBAAC,QAAQ;wBAAE,SAAS;qBAAA;gBAAC,CAAE;aAAC;YAC/C;;;OAGG,GACHhG,WAAW,EAAE;gBACT;oBAAEA,WAAW,EAAE;wBAAC/C,gBAAgB;wBAAE1C,mBAAmB;wBAAED,gBAAgB;qBAAA;gBAAG,CAAA;aAC7E;YACD;;;OAGG,GACH,oBAAoB,EAAE;gBAAC;oBAAE,oBAAoB,EAAEiD,0BAA0B,CAAE;gBAAA,CAAE;aAAC;YAC9E;;;OAGG,GACH0I,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE9G,WAAW,CAAE;gBAAA,CAAE;aAAC;YACnC;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3C;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3C;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC3C;;;OAGG,GACH+G,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE9G,UAAU,CAAE;gBAAA,CAAE;aAAC;YAChC;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxC;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxC;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAEA,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxC;;;OAGG,GACH,UAAU,EAAE;gBAAC,UAAU;aAAC;YACxB;;;OAGG,GACH+G,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAE9G,SAAS,CAAE;gBAAA,CAAE;aAAC;YAC7B;;;OAGG,GACH,QAAQ,EAAE;gBAAC;oBAAE,QAAQ,EAAEA,SAAS,CAAE;gBAAA,CAAE;aAAC;YACrC;;;OAGG,GACH,QAAQ,EAAE;gBAAC;oBAAE,QAAQ,EAAEA,SAAS,CAAE;gBAAA,CAAE;aAAC;YACrC;;;OAGG,GACH+G,SAAS,EAAE;gBACP;oBAAEA,SAAS,EAAE;wBAAC7L,mBAAmB;wBAAED,gBAAgB;wBAAE,EAAE;wBAAE,MAAM;wBAAE,KAAK;wBAAE,KAAK;qBAAA;gBAAG,CAAA;aACnF;YACD;;;OAGG,GACH,kBAAkB,EAAE;gBAAC;oBAAE+L,MAAM,EAAE9I,0BAA0B,CAAE;gBAAA,CAAE;aAAC;YAC9D;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAE6I,SAAS,EAAE;wBAAC,IAAI;wBAAE,MAAM;qBAAA;gBAAC,CAAE;aAAC;YAClD;;;OAGG,GACHE,SAAS,EAAE;gBAAC;oBAAEA,SAAS,EAAEhH,cAAc,CAAE;gBAAA,CAAE;aAAC;YAC5C;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAE,aAAa,EAAEA,cAAc,CAAE;gBAAA,CAAE;aAAC;YACpD;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAE,aAAa,EAAEA,cAAc,CAAE;gBAAA,CAAE;aAAC;YACpD;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAE,aAAa,EAAEA,cAAc,CAAE;gBAAA,CAAE;aAAC;YACpD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC,gBAAgB;aAAC;;;;YAMpC;;;OAGG,GACHiH,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAElI,UAAU,CAAE;gBAAA,CAAE;aAAC;YAClC;;;OAGG,GACHmI,UAAU,EAAE;gBAAC;oBAAEA,UAAU,EAAE;wBAAC,MAAM;wBAAE,MAAM;qBAAA;gBAAC,CAAE;aAAC;YAC9C;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAEC,KAAK,EAAEpI,UAAU,CAAE;gBAAA,CAAE;aAAC;YACxC;;;OAGG,GACH,cAAc,EAAE;gBACZ;oBAAEqI,MAAM,EAAE;wBAAC,QAAQ;wBAAE,MAAM;wBAAE,OAAO;wBAAE,YAAY;wBAAE,WAAW;wBAAE,YAAY;qBAAA;gBAAG,CAAA;aACnF;YACD;;;OAGG,GACHC,MAAM,EAAE;gBACJ;oBACIA,MAAM,EAAE;wBACJ,MAAM;wBACN,SAAS;wBACT,SAAS;wBACT,MAAM;wBACN,MAAM;wBACN,MAAM;wBACN,MAAM;wBACN,aAAa;wBACb,MAAM;wBACN,cAAc;wBACd,UAAU;wBACV,MAAM;wBACN,WAAW;wBACX,eAAe;wBACf,OAAO;wBACP,MAAM;wBACN,SAAS;wBACT,MAAM;wBACN,UAAU;wBACV,YAAY;wBACZ,YAAY;wBACZ,YAAY;wBACZ,UAAU;wBACV,UAAU;wBACV,UAAU;wBACV,UAAU;wBACV,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,aAAa;wBACb,aAAa;wBACb,SAAS;wBACT,UAAU;wBACVpM,mBAAmB;wBACnBD,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,cAAc,EAAE;wBAAC,OAAO;wBAAE,SAAS;qBAAA;gBAAC,CAAE;aAAC;YAC1D;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,gBAAgB,EAAE;wBAAC,MAAM;wBAAE,MAAM;qBAAA;gBAAC,CAAE;aAAC;YAC1D;;;OAGG,GACHsM,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE;wBAAC,MAAM;wBAAE,EAAE;wBAAE,GAAG;wBAAE,GAAG;qBAAA;iBAAG;aAAC;YAC5C;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAEC,MAAM,EAAE;wBAAC,MAAM;wBAAE,QAAQ;qBAAA;gBAAC,CAAE;aAAC;YACnD;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAEnJ,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAEoJ,IAAI,EAAE;wBAAC,OAAO;wBAAE,KAAK;wBAAE,QAAQ;wBAAE,YAAY;qBAAA;iBAAG;aAAC;YAClE;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC,QAAQ;wBAAE,QAAQ;qBAAA;gBAAC,CAAE;aAAC;YAC7C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC,MAAM;wBAAE,GAAG;wBAAE,GAAG;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACnD;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC,WAAW;wBAAE,WAAW;qBAAA;gBAAC,CAAE;aAAC;YACzD;;;OAGG,GACHC,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAAC,MAAM;wBAAE,MAAM;wBAAE,cAAc;qBAAA;iBAAG;aAAC;YACpD;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAAC,GAAG;wBAAE,MAAM;wBAAE,OAAO;qBAAA;iBAAG;aAAC;YACpD;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAAC,GAAG;wBAAE,IAAI;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACjD;;;OAGG,GACH,UAAU,EAAE;gBAAC,kBAAkB;aAAC;YAChC;;;OAGG,GACHC,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE;wBAAC,MAAM;wBAAE,MAAM;wBAAE,KAAK;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACrD;;;OAGG,GACH,aAAa,EAAE;gBACX;oBACI,aAAa,EAAE;wBACX,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,WAAW;wBACXzM,mBAAmB;wBACnBD,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;;;;YAMD;;;OAGG,GACH2M,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC,MAAM,EAAE;2BAAG5I,UAAU,CAAE,CAAA;qBAAA;iBAAG;aAAC;YAC3C;;;OAGG,GACH,UAAU,EAAE;gBACR;oBACI6I,MAAM,EAAE;wBACJxN,QAAQ;wBACR2B,yBAAyB;wBACzBV,iBAAiB;wBACjBE,iBAAiB;qBAAA;gBAExB,CAAA;aACJ;YACD;;;OAGG,GACHqM,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE;wBAAC,MAAM,EAAE;2BAAG7I,UAAU,CAAE,CAAA;qBAAA;iBAAG;aAAC;;;;YAM/C;;;OAGG,GACH,qBAAqB,EAAE;gBAAC;oBAAE,qBAAqB,EAAE;wBAAC,MAAM;wBAAE,MAAM;qBAAA;gBAAC,CAAE;aAAA;QACtE,CAAA;QACD5N,sBAAsB,EAAE;YACpBqQ,QAAQ,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACtCC,UAAU,EAAE;gBAAC,cAAc;gBAAE,cAAc;aAAC;YAC5CC,KAAK,EAAE;gBAAC,SAAS;gBAAE,SAAS;gBAAE,OAAO;gBAAE,KAAK;gBAAE,KAAK;gBAAE,OAAO;gBAAE,QAAQ;gBAAE,MAAM;aAAC;YAC/E,SAAS,EAAE;gBAAC,OAAO;gBAAE,MAAM;aAAC;YAC5B,SAAS,EAAE;gBAAC,KAAK;gBAAE,QAAQ;aAAC;YAC5BU,IAAI,EAAE;gBAAC,OAAO;gBAAE,MAAM;gBAAE,QAAQ;aAAC;YACjCM,GAAG,EAAE;gBAAC,OAAO;gBAAE,OAAO;aAAC;YACvBM,CAAC,EAAE;gBAAC,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;aAAC;YACnDC,EAAE,EAAE;gBAAC,IAAI;gBAAE,IAAI;aAAC;YAChBC,EAAE,EAAE;gBAAC,IAAI;gBAAE,IAAI;aAAC;YAChBO,CAAC,EAAE;gBAAC,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;aAAC;YACnDC,EAAE,EAAE;gBAAC,IAAI;gBAAE,IAAI;aAAC;YAChBC,EAAE,EAAE;gBAAC,IAAI;gBAAE,IAAI;aAAC;YAChBtE,IAAI,EAAE;gBAAC,GAAG;gBAAE,GAAG;aAAC;YAChB,WAAW,EAAE;gBAAC,SAAS;aAAC;YACxB,YAAY,EAAE;gBACV,aAAa;gBACb,kBAAkB;gBAClB,YAAY;gBACZ,aAAa;gBACb,cAAc;aACjB;YACD,aAAa,EAAE;gBAAC,YAAY;aAAC;YAC7B,kBAAkB,EAAE;gBAAC,YAAY;aAAC;YAClC,YAAY,EAAE;gBAAC,YAAY;aAAC;YAC5B,aAAa,EAAE;gBAAC,YAAY;aAAC;YAC7B,cAAc,EAAE;gBAAC,YAAY;aAAC;YAC9B,YAAY,EAAE;gBAAC,SAAS;gBAAE,UAAU;aAAC;YACrCgG,OAAO,EAAE;gBACL,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;aACf;YACD,WAAW,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACzC,WAAW,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACzC,WAAW,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACzC,WAAW,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACzC,WAAW,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACzC,WAAW,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACzC,gBAAgB,EAAE;gBAAC,kBAAkB;gBAAE,kBAAkB;aAAC;YAC1D,UAAU,EAAE;gBACR,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;aACf;YACD,YAAY,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YAC1C,YAAY,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YAC1C,cAAc,EAAE;gBACZ,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;aACnB;YACD,gBAAgB,EAAE;gBAAC,gBAAgB;gBAAE,gBAAgB;aAAC;YACtD,gBAAgB,EAAE;gBAAC,gBAAgB;gBAAE,gBAAgB;aAAC;YACtD2B,SAAS,EAAE;gBAAC,aAAa;gBAAE,aAAa;gBAAE,gBAAgB;aAAC;YAC3D,gBAAgB,EAAE;gBAAC,WAAW;gBAAE,aAAa;gBAAE,aAAa;gBAAE,aAAa;aAAC;YAC5E,UAAU,EAAE;gBACR,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;aACd;YACD,WAAW,EAAE;gBAAC,WAAW;gBAAE,WAAW;aAAC;YACvC,WAAW,EAAE;gBAAC,WAAW;gBAAE,WAAW;aAAC;YACvC,UAAU,EAAE;gBACR,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;aACd;YACD,WAAW,EAAE;gBAAC,WAAW;gBAAE,WAAW;aAAC;YACvC,WAAW,EAAE;gBAAC,WAAW;gBAAE,WAAW;aAAC;YACvCS,KAAK,EAAE;gBAAC,SAAS;gBAAE,SAAS;gBAAE,UAAU;aAAC;YACzC,SAAS,EAAE;gBAAC,OAAO;aAAC;YACpB,SAAS,EAAE;gBAAC,OAAO;aAAC;YACpB,UAAU,EAAE;gBAAC,OAAO;aAAA;QACvB,CAAA;QACDrW,8BAA8B,EAAE;YAC5B,WAAW,EAAE;gBAAC,SAAS;aAAA;QAC1B,CAAA;QACDqF,uBAAuB,EAAE;YACrB,GAAG;YACH,IAAI;YACJ,OAAO;YACP,UAAU;YACV,QAAQ;YACR,iBAAiB;YACjB,MAAM;YACN,cAAc;YACd,YAAY;YACZ,QAAQ;YACR,aAAa;YACb,WAAW;SAAA;IAEoD,CAAA;AAC3E,CAAA;ACpzEA;;;CAGG,SACUoR,YAAY,GAAGA,CACxBC,UAAqB,EACrB,EACInT,SAAS,EACTS,MAAM,EACNC,0BAA0B,EAC1B0S,MAAM,GAAG,CAAE,CAAA,EACXC,QAAQ,GAAG,CAAA,CAAA,EACiC,KAChD;IACAC,gBAAgB,CAACH,UAAU,EAAE,WAAW,EAAEnT,SAAS,CAAC;IACpDsT,gBAAgB,CAACH,UAAU,EAAE,QAAQ,EAAE1S,MAAM,CAAC;IAC9C6S,gBAAgB,CAACH,UAAU,EAAE,4BAA4B,EAAEzS,0BAA0B,CAAC;IAEtF6S,wBAAwB,CAACJ,UAAU,CAACzU,KAAK,EAAE2U,QAAQ,CAAC3U,KAAK,CAAC;IAC1D6U,wBAAwB,CAACJ,UAAU,CAACxU,WAAW,EAAE0U,QAAQ,CAAC1U,WAAW,CAAC;IACtE4U,wBAAwB,CAACJ,UAAU,CAAC3W,sBAAsB,EAAE6W,QAAQ,CAAC7W,sBAAsB,CAAC;IAC5F+W,wBAAwB,CACpBJ,UAAU,CAAC1W,8BAA8B,EACzC4W,QAAQ,CAAC5W,8BAA8B,CAC1C;IACD6W,gBAAgB,CAACH,UAAU,EAAE,yBAAyB,EAAEE,QAAQ,CAACvR,uBAAuB,CAAC;IAEzF0R,qBAAqB,CAACL,UAAU,CAACzU,KAAK,EAAE0U,MAAM,CAAC1U,KAAK,CAAC;IACrD8U,qBAAqB,CAACL,UAAU,CAACxU,WAAW,EAAEyU,MAAM,CAACzU,WAAW,CAAC;IACjE6U,qBAAqB,CAACL,UAAU,CAAC3W,sBAAsB,EAAE4W,MAAM,CAAC5W,sBAAsB,CAAC;IACvFgX,qBAAqB,CACjBL,UAAU,CAAC1W,8BAA8B,EACzC2W,MAAM,CAAC3W,8BAA8B,CACxC;IACDgX,oBAAoB,CAACN,UAAU,EAAEC,MAAM,EAAE,yBAAyB,CAAC;IAEnE,OAAOD,UAAU;AACrB,CAAA;AAEA,MAAMG,gBAAgB,GAAGA,CACrBI,UAAa,EACbC,WAAc,EACdC,aAA+B,KAC/B;IACA,IAAIA,aAAa,KAAK/V,SAAS,EAAE;QAC7B6V,UAAU,CAACC,WAAW,CAAC,GAAGC,aAAa;;AAE/C,CAAC;AAED,MAAML,wBAAwB,GAAGA,CAC7BG,UAAuD,EACvDG,cAAuE,KACvE;IACA,IAAIA,cAAc,EAAE;QAChB,IAAK,MAAMtU,GAAG,IAAIsU,cAAc,CAAE;YAC9BP,gBAAgB,CAACI,UAAU,EAAEnU,GAAG,EAAEsU,cAAc,CAACtU,GAAG,CAAC,CAAC;;;AAGlE,CAAC;AAED,MAAMiU,qBAAqB,GAAGA,CAC1BE,UAAuD,EACvDI,WAAoE,KACpE;IACA,IAAIA,WAAW,EAAE;QACb,IAAK,MAAMvU,GAAG,IAAIuU,WAAW,CAAE;YAC3BL,oBAAoB,CAACC,UAAU,EAAEI,WAAW,EAAEvU,GAAG,CAAC;;;AAG9D,CAAC;AAED,MAAMkU,oBAAoB,GAAGA,CACzBC,UAA6D,EAC7DI,WAA8D,EAC9DvU,GAAQ,KACR;IACA,MAAMwU,UAAU,GAAGD,WAAW,CAACvU,GAAG,CAAC;IAEnC,IAAIwU,UAAU,KAAKlW,SAAS,EAAE;QAC1B6V,UAAU,CAACnU,GAAG,CAAC,GAAGmU,UAAU,CAACnU,GAAG,CAAC,GAAGmU,UAAU,CAACnU,GAAG,CAAC,CAACyU,MAAM,CAACD,UAAU,CAAC,GAAGA,UAAU;;AAE3F,CAAC;AC5EM,MAAME,mBAAmB,GAAGA,CAI/BC,eAK4B,EAC5B,GAAGC,YAAsC,GAEzC,OAAOD,eAAe,KAAK,UAAA,GACrBlQ,mBAAmB,CAACgE,gBAAgB,EAAEkM,eAAe,EAAE,GAAGC,YAAY,CAAA,GACtEnQ,mBAAmB,CACf,IAAMkP,YAAY,CAAClL,gBAAgB,CAAE,CAAA,EAAEkM,eAAe,CAAC,EACvD,GAAGC,YAAY,CAAA;MCpBhBC,OAAO,GAAA,WAAA,GAAGpQ,mBAAmB,CAACgE,gBAAgB,CAAA", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13], "debugId": null}}, {"offset": {"line": 8831, "column": 0}, "map": {"version": 3, "file": "file-text.js", "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/lucide-react/src/icons/file-text.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z', key: '1rqfz7' }],\n  ['path', { d: 'M14 2v4a2 2 0 0 0 2 2h4', key: 'tnqrlb' }],\n  ['path', { d: 'M10 9H8', key: 'b1mrlr' }],\n  ['path', { d: 'M16 13H8', key: 't4e002' }],\n  ['path', { d: 'M16 17H8', key: 'z1uh3a' }],\n];\n\n/**\n * @component @name FileText\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMkg2YTIgMiAwIDAgMC0yIDJ2MTZhMiAyIDAgMCAwIDIgMmgxMmEyIDIgMCAwIDAgMi0yVjdaIiAvPgogIDxwYXRoIGQ9Ik0xNCAydjRhMiAyIDAgMCAwIDIgMmg0IiAvPgogIDxwYXRoIGQ9Ik0xMCA5SDgiIC8+CiAgPHBhdGggZD0iTTE2IDEzSDgiIC8+CiAgPHBhdGggZD0iTTE2IDE3SDgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/file-text\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileText = createLucideIcon('file-text', __iconNode);\n\nexport default FileText;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3F;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}