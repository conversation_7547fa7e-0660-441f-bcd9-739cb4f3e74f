(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@blocknote/core/dist/en-qGo6sk9V.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "e": (()=>t)
});
const t = {
    slash_menu: {
        heading: {
            title: "Heading 1",
            subtext: "Top-level heading",
            aliases: [
                "h",
                "heading1",
                "h1"
            ],
            group: "Headings"
        },
        heading_2: {
            title: "Heading 2",
            subtext: "Key section heading",
            aliases: [
                "h2",
                "heading2",
                "subheading"
            ],
            group: "Headings"
        },
        heading_3: {
            title: "Heading 3",
            subtext: "Subsection and group heading",
            aliases: [
                "h3",
                "heading3",
                "subheading"
            ],
            group: "Headings"
        },
        quote: {
            title: "Quote",
            subtext: "Quote or excerpt",
            aliases: [
                "quotation",
                "blockquote",
                "bq"
            ],
            group: "Basic blocks"
        },
        numbered_list: {
            title: "Numbered List",
            subtext: "List with ordered items",
            aliases: [
                "ol",
                "li",
                "list",
                "numberedlist",
                "numbered list"
            ],
            group: "Basic blocks"
        },
        bullet_list: {
            title: "Bullet List",
            subtext: "List with unordered items",
            aliases: [
                "ul",
                "li",
                "list",
                "bulletlist",
                "bullet list"
            ],
            group: "Basic blocks"
        },
        check_list: {
            title: "Check List",
            subtext: "List with checkboxes",
            aliases: [
                "ul",
                "li",
                "list",
                "checklist",
                "check list",
                "checked list",
                "checkbox"
            ],
            group: "Basic blocks"
        },
        paragraph: {
            title: "Paragraph",
            subtext: "The body of your document",
            aliases: [
                "p",
                "paragraph"
            ],
            group: "Basic blocks"
        },
        code_block: {
            title: "Code Block",
            subtext: "Code block with syntax highlighting",
            aliases: [
                "code",
                "pre"
            ],
            group: "Basic blocks"
        },
        page_break: {
            title: "Page Break",
            subtext: "Page separator",
            aliases: [
                "page",
                "break",
                "separator"
            ],
            group: "Basic blocks"
        },
        table: {
            title: "Table",
            subtext: "Table with editable cells",
            aliases: [
                "table"
            ],
            group: "Advanced"
        },
        image: {
            title: "Image",
            subtext: "Resizable image with caption",
            aliases: [
                "image",
                "imageUpload",
                "upload",
                "img",
                "picture",
                "media",
                "url"
            ],
            group: "Media"
        },
        video: {
            title: "Video",
            subtext: "Resizable video with caption",
            aliases: [
                "video",
                "videoUpload",
                "upload",
                "mp4",
                "film",
                "media",
                "url"
            ],
            group: "Media"
        },
        audio: {
            title: "Audio",
            subtext: "Embedded audio with caption",
            aliases: [
                "audio",
                "audioUpload",
                "upload",
                "mp3",
                "sound",
                "media",
                "url"
            ],
            group: "Media"
        },
        file: {
            title: "File",
            subtext: "Embedded file",
            aliases: [
                "file",
                "upload",
                "embed",
                "media",
                "url"
            ],
            group: "Media"
        },
        emoji: {
            title: "Emoji",
            subtext: "Search for and insert an emoji",
            aliases: [
                "emoji",
                "emote",
                "emotion",
                "face"
            ],
            group: "Others"
        }
    },
    placeholders: {
        default: "Enter text or type '/' for commands",
        heading: "Heading",
        bulletListItem: "List",
        numberedListItem: "List",
        checkListItem: "List",
        emptyDocument: void 0,
        new_comment: "Write a comment...",
        edit_comment: "Edit comment...",
        comment_reply: "Add comment..."
    },
    file_blocks: {
        image: {
            add_button_text: "Add image"
        },
        video: {
            add_button_text: "Add video"
        },
        audio: {
            add_button_text: "Add audio"
        },
        file: {
            add_button_text: "Add file"
        }
    },
    // from react package:
    side_menu: {
        add_block_label: "Add block",
        drag_handle_label: "Open block menu"
    },
    drag_handle: {
        delete_menuitem: "Delete",
        colors_menuitem: "Colors",
        header_row_menuitem: "Header row",
        header_column_menuitem: "Header column"
    },
    table_handle: {
        delete_column_menuitem: "Delete column",
        delete_row_menuitem: "Delete row",
        add_left_menuitem: "Add column left",
        add_right_menuitem: "Add column right",
        add_above_menuitem: "Add row above",
        add_below_menuitem: "Add row below",
        split_cell_menuitem: "Split cell",
        merge_cells_menuitem: "Merge cells",
        background_color_menuitem: "Background color"
    },
    suggestion_menu: {
        no_items_title: "No items found"
    },
    color_picker: {
        text_title: "Text",
        background_title: "Background",
        colors: {
            default: "Default",
            gray: "Gray",
            brown: "Brown",
            red: "Red",
            orange: "Orange",
            yellow: "Yellow",
            green: "Green",
            blue: "Blue",
            purple: "Purple",
            pink: "Pink"
        }
    },
    formatting_toolbar: {
        bold: {
            tooltip: "Bold",
            secondary_tooltip: "Mod+B"
        },
        italic: {
            tooltip: "Italic",
            secondary_tooltip: "Mod+I"
        },
        underline: {
            tooltip: "Underline",
            secondary_tooltip: "Mod+U"
        },
        strike: {
            tooltip: "Strike",
            secondary_tooltip: "Mod+Shift+S"
        },
        code: {
            tooltip: "Code",
            secondary_tooltip: ""
        },
        colors: {
            tooltip: "Colors"
        },
        link: {
            tooltip: "Create link",
            secondary_tooltip: "Mod+K"
        },
        file_caption: {
            tooltip: "Edit caption",
            input_placeholder: "Edit caption"
        },
        file_replace: {
            tooltip: {
                image: "Replace image",
                video: "Replace video",
                audio: "Replace audio",
                file: "Replace file"
            }
        },
        file_rename: {
            tooltip: {
                image: "Rename image",
                video: "Rename video",
                audio: "Rename audio",
                file: "Rename file"
            },
            input_placeholder: {
                image: "Rename image",
                video: "Rename video",
                audio: "Rename audio",
                file: "Rename file"
            }
        },
        file_download: {
            tooltip: {
                image: "Download image",
                video: "Download video",
                audio: "Download audio",
                file: "Download file"
            }
        },
        file_delete: {
            tooltip: {
                image: "Delete image",
                video: "Delete video",
                audio: "Delete audio",
                file: "Delete file"
            }
        },
        file_preview_toggle: {
            tooltip: "Toggle preview"
        },
        nest: {
            tooltip: "Nest block",
            secondary_tooltip: "Tab"
        },
        unnest: {
            tooltip: "Unnest block",
            secondary_tooltip: "Shift+Tab"
        },
        align_left: {
            tooltip: "Align text left"
        },
        align_center: {
            tooltip: "Align text center"
        },
        align_right: {
            tooltip: "Align text right"
        },
        align_justify: {
            tooltip: "Justify text"
        },
        table_cell_merge: {
            tooltip: "Merge cells"
        },
        comment: {
            tooltip: "Add comment"
        }
    },
    file_panel: {
        upload: {
            title: "Upload",
            file_placeholder: {
                image: "Upload image",
                video: "Upload video",
                audio: "Upload audio",
                file: "Upload file"
            },
            upload_error: "Error: Upload failed"
        },
        embed: {
            title: "Embed",
            embed_button: {
                image: "Embed image",
                video: "Embed video",
                audio: "Embed audio",
                file: "Embed file"
            },
            url_placeholder: "Enter URL"
        }
    },
    link_toolbar: {
        delete: {
            tooltip: "Remove link"
        },
        edit: {
            text: "Edit link",
            tooltip: "Edit"
        },
        open: {
            tooltip: "Open in new tab"
        },
        form: {
            title_placeholder: "Edit title",
            url_placeholder: "Edit URL"
        }
    },
    comments: {
        actions: {
            add_reaction: "Add reaction",
            resolve: "Resolve",
            edit_comment: "Edit comment",
            delete_comment: "Delete comment",
            more_actions: "More actions"
        },
        reactions: {
            reacted_by: "Reacted by"
        },
        sidebar: {
            marked_as_resolved: "Marked as resolved",
            more_replies: (e)=>`${e} more replies`
        }
    },
    generic: {
        ctrl_shortcut: "Ctrl"
    }
};
;
 //# sourceMappingURL=en-qGo6sk9V.js.map
}}),
"[project]/node_modules/@blocknote/core/dist/blocknote.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AudioBlock": (()=>zr),
    "BlockNoteEditor": (()=>ro),
    "BlockNoteExtension": (()=>L),
    "BlockNoteSchema": (()=>Pe),
    "COLORS_DARK_MODE_DEFAULT": (()=>jc),
    "COLORS_DEFAULT": (()=>Wc),
    "CodeBlock": (()=>Gr),
    "DEFAULT_LINK_PROTOCOL": (()=>pa),
    "EMPTY_CELL_HEIGHT": (()=>Ac),
    "EMPTY_CELL_WIDTH": (()=>Rn),
    "EventEmitter": (()=>It),
    "Exporter": (()=>Gc),
    "FILE_AUDIO_ICON_SVG": (()=>Rr),
    "FILE_ICON_SVG": (()=>Dr),
    "FILE_IMAGE_ICON_SVG": (()=>ss),
    "FILE_VIDEO_ICON_SVG": (()=>Hs),
    "FileBlock": (()=>ns),
    "FilePanelProsemirrorPlugin": (()=>ea),
    "FilePanelView": (()=>Qi),
    "FormattingToolbarProsemirrorPlugin": (()=>oa),
    "FormattingToolbarView": (()=>ta),
    "HTMLToBlocks": (()=>Jn),
    "ImageBlock": (()=>us),
    "LinkToolbarProsemirrorPlugin": (()=>da),
    "PageBreak": (()=>Ys),
    "SideMenuProsemirrorPlugin": (()=>Ba),
    "SideMenuView": (()=>Ea),
    "SuggestionMenuProseMirrorPlugin": (()=>Ia),
    "TableHandlesProsemirrorPlugin": (()=>Va),
    "TableHandlesView": (()=>Ra),
    "UniqueID": (()=>We),
    "UnreachableCaseError": (()=>j),
    "VALID_LINK_PROTOCOLS": (()=>ua),
    "VideoBlock": (()=>_s),
    "addInlineContentAttributes": (()=>Vt),
    "addInlineContentKeyboardShortcuts": (()=>ur),
    "addStyleAttributes": (()=>gr),
    "applyNonSelectableBlockFix": (()=>ar),
    "assertEmpty": (()=>xc),
    "audioBlockConfig": (()=>Ur),
    "audioParse": (()=>$r),
    "audioPropSchema": (()=>Vr),
    "audioRender": (()=>_r),
    "audioToExternalHTML": (()=>Fr),
    "blockToNode": (()=>pe),
    "blocksToMarkdown": (()=>ki),
    "camelToDataKebab": (()=>Ve),
    "checkBlockHasDefaultProp": (()=>Rc),
    "checkBlockIsDefaultType": (()=>Ws),
    "checkBlockIsFileBlock": (()=>Hc),
    "checkBlockIsFileBlockWithPlaceholder": (()=>Oc),
    "checkBlockIsFileBlockWithPreview": (()=>Dc),
    "checkBlockTypeHasDefaultProp": (()=>js),
    "checkDefaultBlockTypeInSchema": (()=>O),
    "checkDefaultInlineContentTypeInSchema": (()=>zs),
    "checkPageBreakBlocksInSchema": (()=>Zs),
    "cleanHTMLToMarkdown": (()=>Mt),
    "combineByGroup": (()=>Jc),
    "contentNodeToInlineContent": (()=>Ge),
    "contentNodeToTableContent": (()=>kn),
    "createAddFileButton": (()=>Hr),
    "createBlockSpec": (()=>xe),
    "createBlockSpecFromStronglyTypedTiptapNode": (()=>oe),
    "createDefaultBlockDOMOutputSpec": (()=>G),
    "createExternalHTMLExporter": (()=>Ke),
    "createFigureWithCaption": (()=>St),
    "createFileBlockWrapper": (()=>Et),
    "createFileNameWithIcon": (()=>Or),
    "createInlineContentSpec": (()=>Ic),
    "createInlineContentSpecFromTipTapNode": (()=>hr),
    "createInternalBlockSpec": (()=>mn),
    "createInternalHTMLSerializer": (()=>Nr),
    "createInternalInlineContentSpec": (()=>pr),
    "createInternalStyleSpec": (()=>yn),
    "createLinkWithCaption": (()=>Xe),
    "createResizableFileBlockWrapper": (()=>Nn),
    "createStronglyTypedTiptapNode": (()=>q),
    "createStyleSpec": (()=>Lc),
    "createStyleSpecFromTipTapMark": (()=>ie),
    "createSuggestionMenu": (()=>zc),
    "defaultBlockSchema": (()=>$s),
    "defaultBlockSpecs": (()=>Vn),
    "defaultBlockToHTML": (()=>Rt),
    "defaultCodeBlockPropSchema": (()=>Wr),
    "defaultInlineContentSchema": (()=>Fs),
    "defaultInlineContentSpecs": (()=>_n),
    "defaultProps": (()=>T),
    "defaultStyleSchema": (()=>Nc),
    "defaultStyleSpecs": (()=>Un),
    "docToBlocks": (()=>lr),
    "esmDependencies": (()=>fe),
    "fileBlockConfig": (()=>Zr),
    "fileParse": (()=>es),
    "filePropSchema": (()=>Yr),
    "fileRender": (()=>Qr),
    "fileToExternalHTML": (()=>ts),
    "filenameFromURL": (()=>Pc),
    "filterSuggestionItems": (()=>_c),
    "formatKeyboardShortcut": (()=>Z),
    "formattingToolbarPluginKey": (()=>na),
    "getBlock": (()=>ai),
    "getBlockCache": (()=>Ct),
    "getBlockFromPos": (()=>ir),
    "getBlockInfo": (()=>ee),
    "getBlockInfoFromResolvedPos": (()=>Te),
    "getBlockInfoFromSelection": (()=>v),
    "getBlockInfoFromTransaction": (()=>je),
    "getBlockInfoWithManualOffset": (()=>kt),
    "getBlockNoteExtensions": (()=>qa),
    "getBlockNoteSchema": (()=>Me),
    "getBlockSchema": (()=>wt),
    "getBlockSchemaFromSpecs": (()=>gn),
    "getBlocksChangedByTransaction": (()=>Br),
    "getColspan": (()=>ve),
    "getDefaultEmojiPickerItems": (()=>Kc),
    "getDefaultSlashMenuItems": (()=>Uc),
    "getInlineContentParseRules": (()=>fr),
    "getInlineContentSchema": (()=>yt),
    "getInlineContentSchemaFromSpecs": (()=>wn),
    "getNearestBlockPos": (()=>X),
    "getNextBlock": (()=>li),
    "getNodeById": (()=>_),
    "getPageBreakSlashMenuItems": (()=>Fc),
    "getParentBlock": (()=>di),
    "getParseRules": (()=>cr),
    "getPmSchema": (()=>M),
    "getPrevBlock": (()=>ci),
    "getRowspan": (()=>st),
    "getStyleParseRules": (()=>br),
    "getStyleSchema": (()=>be),
    "getStyleSchemaFromSpecs": (()=>Cn),
    "imageBlockConfig": (()=>as),
    "imageParse": (()=>ls),
    "imagePropSchema": (()=>is),
    "imageRender": (()=>cs),
    "imageToExternalHTML": (()=>ds),
    "inheritedProps": (()=>fn),
    "initializeESMDependencies": (()=>Tt),
    "inlineContentToNodes": (()=>F),
    "insertBlocks": (()=>xr),
    "insertOrUpdateBlock": (()=>N),
    "isAppleOS": (()=>sr),
    "isLinkInlineContent": (()=>Ot),
    "isNodeBlock": (()=>ut),
    "isPartialLinkInlineContent": (()=>hn),
    "isPartialTableCell": (()=>Ce),
    "isSafari": (()=>Tc),
    "isStyledTextInlineContent": (()=>de),
    "isTableCell": (()=>bt),
    "isTableCellSelection": (()=>Kt),
    "linkToolbarPluginKey": (()=>la),
    "mapTableCell": (()=>rt),
    "mappingFactory": (()=>qc),
    "markdownToBlocks": (()=>Bi),
    "markdownToHTML": (()=>Xn),
    "mergeCSSClasses": (()=>Q),
    "mergeParagraphs": (()=>Mc),
    "nodeToBlock": (()=>E),
    "nodeToCustomInlineContent": (()=>it),
    "pageBreakConfig": (()=>qs),
    "pageBreakParse": (()=>Js),
    "pageBreakRender": (()=>Ks),
    "pageBreakSchema": (()=>$n),
    "pageBreakToExternalHTML": (()=>Xs),
    "parseEmbedElement": (()=>Wt),
    "parseFigureElement": (()=>Je),
    "propsToAttributes": (()=>Be),
    "prosemirrorSliceToSlicedBlocks": (()=>dr),
    "removeAndInsertBlocks": (()=>_t),
    "selectedFragmentToHTML": (()=>Qn),
    "shikiHighlighterPromiseSymbol": (()=>Ye),
    "shikiParserSymbol": (()=>zt),
    "sideMenuPluginKey": (()=>Sa),
    "stylePropsToAttributes": (()=>mr),
    "tableContentToNodes": (()=>qe),
    "tableHandlesPluginKey": (()=>ye),
    "trackPosition": (()=>Ta),
    "updateBlock": (()=>Mr),
    "updateBlockCommand": (()=>I),
    "updateBlockTr": (()=>xn),
    "uploadToTmpFilesDotOrg_DEV_ONLY": (()=>Vc),
    "videoBlockConfig": (()=>Os),
    "videoParse": (()=>Vs),
    "videoPropSchema": (()=>Ds),
    "videoRender": (()=>Rs),
    "videoToExternalHTML": (()=>Us),
    "withPageBreak": (()=>$c),
    "wrapInBlockStructure": (()=>Ie)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-model/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$transform$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-transform/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/core/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-state/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm-browser/v4.js [app-client] (ecmascript) <export default as v4>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-highlight/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$highlight$2f$dist$2f$shiki$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-highlight/dist/shiki.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$bold$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-bold/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$code$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-code/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$italic$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-italic/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$strike$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-strike/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$underline$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-underline/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$table$2d$cell$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-table-cell/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$table$2d$header$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-table-header/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-tables/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$gapcursor$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-gapcursor/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$history$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-history/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$link$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-link/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$text$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/extension-text/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$cursor$2d$plugin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/y-prosemirror/src/plugins/cursor-plugin.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$sync$2d$plugin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/y-prosemirror/src/plugins/sync-plugin.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$undo$2d$plugin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/y-prosemirror/src/plugins/undo-plugin.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$keys$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/y-prosemirror/src/plugins/keys.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$lib$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/y-prosemirror/src/lib.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-view/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yjs$2f$dist$2f$yjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/yjs/dist/yjs.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$history$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-history/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$dropcursor$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prosemirror-dropcursor/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$en$2d$qGo6sk9V$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@blocknote/core/dist/en-qGo6sk9V.js [app-client] (ecmascript)");
var so = Object.defineProperty;
var io = (e, n, t)=>n in e ? so(e, n, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: t
    }) : e[n] = t;
var p = (e, n, t)=>io(e, typeof n != "symbol" ? n + "" : n, t);
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function or(e, n = JSON.stringify) {
    const t = {};
    return e.filter((o)=>{
        const r = n(o);
        return Object.prototype.hasOwnProperty.call(t, r) ? !1 : t[r] = !0;
    });
}
function rr(e) {
    const n = e.filter((o, r)=>e.indexOf(o) !== r);
    return or(n);
}
const We = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Extension"].create({
    name: "uniqueID",
    // we’ll set a very high priority to make sure this runs first
    // and is compatible with `appendTransaction` hooks of other extensions
    priority: 1e4,
    addOptions () {
        return {
            attributeName: "id",
            types: [],
            setIdAttribute: !1,
            generateID: ()=>{
                if (typeof window < "u" && window.__TEST_OPTIONS) {
                    const e = window.__TEST_OPTIONS;
                    return e.mockID === void 0 ? e.mockID = 0 : e.mockID++, e.mockID.toString();
                }
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])();
            },
            filterTransaction: null
        };
    },
    addGlobalAttributes () {
        return [
            {
                types: this.options.types,
                attributes: {
                    [this.options.attributeName]: {
                        default: null,
                        parseHTML: (e)=>e.getAttribute(`data-${this.options.attributeName}`),
                        renderHTML: (e)=>{
                            const n = {
                                [`data-${this.options.attributeName}`]: e[this.options.attributeName]
                            };
                            return this.options.setIdAttribute ? {
                                ...n,
                                id: e[this.options.attributeName]
                            } : n;
                        }
                    }
                }
            }
        ];
    },
    // check initial content for missing ids
    // onCreate() {
    //   // Don’t do this when the collaboration extension is active
    //   // because this may update the content, so Y.js tries to merge these changes.
    //   // This leads to empty block nodes.
    //   // See: https://github.com/ueberdosis/tiptap/issues/2400
    //   if (
    //     this.editor.extensionManager.extensions.find(
    //       (extension) => extension.name === "collaboration"
    //     )
    //   ) {
    //     return;
    //   }
    //   const { view, state } = this.editor;
    //   const { tr, doc } = state;
    //   const { types, attributeName, generateID } = this.options;
    //   const nodesWithoutId = findChildren(doc, (node) => {
    //     return (
    //       types.includes(node.type.name) && node.attrs[attributeName] === null
    //     );
    //   });
    //   nodesWithoutId.forEach(({ node, pos }) => {
    //     tr.setNodeMarkup(pos, undefined, {
    //       ...node.attrs,
    //       [attributeName]: generateID(),
    //     });
    //   });
    //   tr.setMeta("addToHistory", false);
    //   view.dispatch(tr);
    // },
    addProseMirrorPlugins () {
        let e = null, n = !1;
        return [
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Plugin"]({
                key: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PluginKey"]("uniqueID"),
                appendTransaction: (t, o, r)=>{
                    const s = t.some((m)=>m.docChanged) && !o.doc.eq(r.doc), i = this.options.filterTransaction && t.some((m)=>{
                        let g, b;
                        return !(!((b = (g = this.options).filterTransaction) === null || b === void 0) && b.call(g, m));
                    });
                    if (!s || i) return;
                    const { tr: a } = r, { types: c, attributeName: l, generateID: d } = this.options, u = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["combineTransactionSteps"])(o.doc, t), { mapping: h } = u;
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getChangedRanges"])(u).forEach(({ newRange: m })=>{
                        const g = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findChildrenInRange"])(r.doc, m, (w)=>c.includes(w.type.name)), b = g.map(({ node: w })=>w.attrs[l]).filter((w)=>w !== null), k = rr(b);
                        g.forEach(({ node: w, pos: C })=>{
                            let B;
                            const P = (B = a.doc.nodeAt(C)) === null || B === void 0 ? void 0 : B.attrs[l];
                            if (P === null) {
                                const y = o.doc.type.createAndFill().content;
                                if (o.doc.content.findDiffStart(y) === null) {
                                    const se = JSON.parse(JSON.stringify(r.doc.toJSON()));
                                    if (se.content[0].content[0].attrs.id = "initialBlockId", JSON.stringify(se.content) === JSON.stringify(y.toJSON())) {
                                        a.setNodeMarkup(C, void 0, {
                                            ...w.attrs,
                                            [l]: "initialBlockId"
                                        });
                                        return;
                                    }
                                }
                                a.setNodeMarkup(C, void 0, {
                                    ...w.attrs,
                                    [l]: d()
                                });
                                return;
                            }
                            const { deleted: $ } = h.invert().mapResult(C);
                            $ && k.includes(P) && a.setNodeMarkup(C, void 0, {
                                ...w.attrs,
                                [l]: d()
                            });
                        });
                    }), !!a.steps.length) return a;
                },
                // we register a global drag handler to track the current drag source element
                view (t) {
                    const o = (r)=>{
                        let s;
                        e = !((s = t.dom.parentElement) === null || s === void 0) && s.contains(r.target) ? t.dom.parentElement : null;
                    };
                    return window.addEventListener("dragstart", o), {
                        destroy () {
                            window.removeEventListener("dragstart", o);
                        }
                    };
                },
                props: {
                    // `handleDOMEvents` is called before `transformPasted` so we can do
                    // some checks before. However, `transformPasted` only runs when
                    // editor content is pasted - not external content.
                    handleDOMEvents: {
                        // only create new ids for dropped content while holding `alt`
                        // or content is dragged from another editor
                        drop: (t, o)=>{
                            let r;
                            return e !== t.dom.parentElement || ((r = o.dataTransfer) === null || r === void 0 ? void 0 : r.effectAllowed) === "copy" ? n = !0 : n = !1, e = null, !1;
                        },
                        // always create new ids on pasted content
                        paste: ()=>(n = !0, !1)
                    },
                    // we’ll remove ids for every pasted node
                    // so we can create a new one within `appendTransaction`
                    transformPasted: (t)=>{
                        if (!n) return t;
                        const { types: o, attributeName: r } = this.options, s = (i)=>{
                            const a = [];
                            return i.forEach((c)=>{
                                if (c.isText) {
                                    a.push(c);
                                    return;
                                }
                                if (!o.includes(c.type.name)) {
                                    a.push(c.copy(s(c.content)));
                                    return;
                                }
                                const l = c.type.create({
                                    ...c.attrs,
                                    [r]: null
                                }, s(c.content), c.marks);
                                a.push(l);
                            }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"].from(a);
                        };
                        return n = !1, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Slice"](s(t.content), t.openStart, t.openEnd);
                    }
                }
            })
        ];
    }
});
function Ot(e) {
    return e.type === "link";
}
function hn(e) {
    return typeof e != "string" && e.type === "link";
}
function de(e) {
    return typeof e != "string" && e.type === "text";
}
function rt(e) {
    var n, t, o, r, s;
    return bt(e) ? {
        ...e
    } : Ce(e) ? {
        type: "tableCell",
        content: [].concat(e.content),
        props: {
            backgroundColor: ((n = e.props) == null ? void 0 : n.backgroundColor) ?? "default",
            textColor: ((t = e.props) == null ? void 0 : t.textColor) ?? "default",
            textAlignment: ((o = e.props) == null ? void 0 : o.textAlignment) ?? "left",
            colspan: ((r = e.props) == null ? void 0 : r.colspan) ?? 1,
            rowspan: ((s = e.props) == null ? void 0 : s.rowspan) ?? 1
        }
    } : {
        type: "tableCell",
        content: [].concat(e),
        props: {
            backgroundColor: "default",
            textColor: "default",
            textAlignment: "left",
            colspan: 1,
            rowspan: 1
        }
    };
}
function Ce(e) {
    return e != null && typeof e != "string" && !Array.isArray(e) && e.type === "tableCell";
}
function bt(e) {
    return Ce(e) && e.props !== void 0 && e.content !== void 0;
}
function ve(e) {
    return bt(e) ? e.props.colspan ?? 1 : 1;
}
function st(e) {
    return bt(e) ? e.props.rowspan ?? 1 : 1;
}
class j extends Error {
    constructor(n){
        super(`Unreachable case: ${n}`);
    }
}
function xc(e, n = !0) {
    const { "data-test": t, ...o } = e;
    if (Object.keys(o).length > 0 && n) throw new Error("Object must be empty " + JSON.stringify(e));
}
const sr = ()=>typeof navigator < "u" && (/Mac/.test(navigator.platform) || /AppleWebKit/.test(navigator.userAgent) && /Mobile\/\w+/.test(navigator.userAgent));
function Z(e, n = "Ctrl") {
    return sr() ? e.replace("Mod", "⌘") : e.replace("Mod", n);
}
function Q(...e) {
    return e.filter((n)=>n).join(" ");
}
const Tc = ()=>/^((?!chrome|android).)*safari/i.test(navigator.userAgent);
function G(e, n, t, o) {
    const r = document.createElement("div");
    r.className = Q("bn-block-content", t.class), r.setAttribute("data-content-type", e);
    for (const [i, a] of Object.entries(t))i !== "class" && r.setAttribute(i, a);
    const s = document.createElement(n);
    s.className = Q("bn-inline-content", o.class);
    for (const [i, a] of Object.entries(o))i !== "class" && s.setAttribute(i, a);
    return r.appendChild(s), {
        dom: r,
        contentDOM: s
    };
}
const Rt = (e, n)=>{
    let t = pe(e, n.pmSchema);
    t.type.name === "blockContainer" && (t = t.firstChild);
    const o = n.pmSchema.nodes[t.type.name].spec.toDOM;
    if (o === void 0) throw new Error("This block has no default HTML serialization as its corresponding TipTap node doesn't implement `renderHTML`.");
    const r = o(t);
    if (typeof r != "object" || !("dom" in r)) throw new Error("Cannot use this block's default HTML serialization as its corresponding TipTap node's `renderHTML` function does not return an object with the `dom` property.");
    return r;
};
function Mc(e) {
    const n = e.querySelectorAll("p");
    if (n.length > 1) {
        const t = n[0];
        for(let o = 1; o < n.length; o++){
            const r = n[o];
            t.innerHTML += "<br>" + r.innerHTML, r.remove();
        }
    }
}
const T = {
    backgroundColor: {
        default: "default"
    },
    textColor: {
        default: "default"
    },
    textAlignment: {
        default: "left",
        values: [
            "left",
            "center",
            "right",
            "justify"
        ]
    }
}, fn = [
    "backgroundColor",
    "textColor"
];
function Ve(e) {
    return "data-" + e.replace(/([a-z])([A-Z])/g, "$1-$2").toLowerCase();
}
function Pc(e) {
    const n = e.split("/");
    return !n.length || // invalid?
    n[n.length - 1] === "" ? e : n[n.length - 1];
}
function Be(e) {
    const n = {};
    return Object.entries(e).filter(([t, o])=>!fn.includes(t)).forEach(([t, o])=>{
        n[t] = {
            default: o.default,
            keepOnSplit: !0,
            // Props are displayed in kebab-case as HTML attributes. If a prop's
            // value is the same as its default, we don't display an HTML
            // attribute for it.
            parseHTML: (r)=>{
                const s = r.getAttribute(Ve(t));
                if (s === null) return null;
                if (o.default === void 0 && o.type === "boolean" || o.default !== void 0 && typeof o.default == "boolean") return s === "true" ? !0 : s === "false" ? !1 : null;
                if (o.default === void 0 && o.type === "number" || o.default !== void 0 && typeof o.default == "number") {
                    const i = parseFloat(s);
                    return !Number.isNaN(i) && Number.isFinite(i) ? i : null;
                }
                return s;
            },
            renderHTML: (r)=>r[t] !== o.default ? {
                    [Ve(t)]: r[t]
                } : {}
        };
    }), n;
}
function ir(e, n, t, o) {
    if (typeof e == "boolean") throw new Error("Cannot find node position as getPos is a boolean, not a function.");
    const r = e(), i = t.state.doc.resolve(r).node().attrs.id;
    if (!i) throw new Error("Block doesn't have id");
    const a = n.getBlock(i);
    if (a.type !== o) throw new Error("Block type does not match");
    return a;
}
function Ie(e, n, t, o, r = !1, s) {
    const i = document.createElement("div");
    if (s !== void 0) for (const [a, c] of Object.entries(s))a !== "class" && i.setAttribute(a, c);
    i.className = Q("bn-block-content", (s == null ? void 0 : s.class) || ""), i.setAttribute("data-content-type", n);
    for (const [a, c] of Object.entries(t)){
        const d = o[a].default;
        !fn.includes(a) && c !== d && i.setAttribute(Ve(a), c);
    }
    return r && i.setAttribute("data-file-block", ""), i.appendChild(e.dom), e.contentDOM !== void 0 && (e.contentDOM.className = Q("bn-inline-content", e.contentDOM.className)), {
        ...e,
        dom: i
    };
}
function q(e) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Node"].create(e);
}
function mn(e, n) {
    return {
        config: e,
        implementation: n
    };
}
function oe(e, n, t) {
    return mn({
        type: e.name,
        content: e.config.content === "inline*" ? "inline" : e.config.content === "tableRow+" ? "table" : "none",
        propSchema: n
    }, {
        node: e,
        requiredExtensions: t,
        toInternalHTML: Rt,
        toExternalHTML: Rt
    });
}
function gn(e) {
    return Object.fromEntries(Object.entries(e).map(([n, t])=>[
            n,
            t.config
        ]));
}
function ar(e, n) {
    e.stopEvent = (t)=>(t.type === "mousedown" && setTimeout(()=>{
            n.view.dom.blur();
        }, 10), !0);
}
function cr(e, n) {
    const t = [
        {
            tag: "[data-content-type=" + e.type + "]",
            contentElement: ".bn-inline-content"
        }
    ];
    return n && t.push({
        tag: "*",
        getAttrs (o) {
            if (typeof o == "string") return !1;
            const r = n == null ? void 0 : n(o);
            return r === void 0 ? !1 : r;
        }
    }), t;
}
function xe(e, n) {
    const t = q({
        name: e.type,
        content: e.content === "inline" ? "inline*" : "",
        group: "blockContent",
        selectable: e.isSelectable ?? !0,
        isolating: !0,
        addAttributes () {
            return Be(e.propSchema);
        },
        parseHTML () {
            return cr(e, n.parse);
        },
        renderHTML ({ HTMLAttributes: o }) {
            const r = document.createElement("div");
            return Ie({
                dom: r,
                contentDOM: e.content === "inline" ? r : void 0
            }, e.type, {}, e.propSchema, e.isFileBlock, o);
        },
        addNodeView () {
            return ({ getPos: o })=>{
                var l;
                const r = this.options.editor, s = ir(o, r, this.editor, e.type), i = ((l = this.options.domAttributes) == null ? void 0 : l.blockContent) || {}, a = n.render(s, r), c = Ie(a, s.type, s.props, e.propSchema, i);
                return e.isSelectable === !1 && ar(c, this.editor), c;
            };
        }
    });
    if (t.name !== e.type) throw new Error("Node name does not match block type. This is a bug in BlockNote.");
    return mn(e, {
        node: t,
        toInternalHTML: (o, r)=>{
            var a;
            const s = ((a = t.options.domAttributes) == null ? void 0 : a.blockContent) || {}, i = n.render(o, r);
            return Ie(i, o.type, o.props, e.propSchema, e.isFileBlock, s);
        },
        // TODO: this should not have wrapInBlockStructure and generally be a lot simpler
        // post-processing in externalHTMLExporter should not be necessary
        toExternalHTML: (o, r)=>{
            var a, c;
            const s = ((a = t.options.domAttributes) == null ? void 0 : a.blockContent) || {};
            let i = (c = n.toExternalHTML) == null ? void 0 : c.call(n, o, r);
            return i === void 0 && (i = n.render(o, r)), Ie(i, o.type, o.props, e.propSchema, s);
        }
    });
}
function X(e, n) {
    const t = e.resolve(n);
    if (t.nodeAfter && t.nodeAfter.type.isInGroup("bnBlock")) return {
        posBeforeNode: t.pos,
        node: t.nodeAfter
    };
    let o = t.depth, r = t.node(o);
    for(; o > 0;){
        if (r.type.isInGroup("bnBlock")) return {
            posBeforeNode: t.before(o),
            node: r
        };
        o--, r = t.node(o);
    }
    const s = [];
    e.descendants((a, c)=>{
        a.type.isInGroup("bnBlock") && s.push(c);
    }), console.warn(`Position ${n} is not within a blockContainer node.`);
    const i = e.resolve(s.find((a)=>a >= n) || s[s.length - 1]);
    return {
        posBeforeNode: i.pos,
        node: i.nodeAfter
    };
}
function kt(e, n) {
    if (!e.type.isInGroup("bnBlock")) throw new Error(`Attempted to get bnBlock node at position but found node of different type ${e.type.name}`);
    const t = e, o = n, r = o + t.nodeSize, s = {
        node: t,
        beforePos: o,
        afterPos: r
    };
    if (t.type.name === "blockContainer") {
        let i, a;
        if (t.forEach((c, l)=>{
            if (c.type.spec.group === "blockContent") {
                const d = c, u = o + l + 1, h = u + c.nodeSize;
                i = {
                    node: d,
                    beforePos: u,
                    afterPos: h
                };
            } else if (c.type.name === "blockGroup") {
                const d = c, u = o + l + 1, h = u + c.nodeSize;
                a = {
                    node: d,
                    beforePos: u,
                    afterPos: h
                };
            }
        }), !i) throw new Error(`blockContainer node does not contain a blockContent node in its children: ${t}`);
        return {
            isBlockContainer: !0,
            bnBlock: s,
            blockContent: i,
            childContainer: a,
            blockNoteType: i.node.type.name
        };
    } else {
        if (!s.node.type.isInGroup("childContainer")) throw new Error(`bnBlock node is not in the childContainer group: ${s.node}`);
        return {
            isBlockContainer: !1,
            bnBlock: s,
            childContainer: s,
            blockNoteType: s.node.type.name
        };
    }
}
function ee(e) {
    return kt(e.node, e.posBeforeNode);
}
function Te(e) {
    if (!e.nodeAfter) throw new Error(`Attempted to get blockContainer node at position ${e.pos} but a node at this position does not exist`);
    return kt(e.nodeAfter, e.pos);
}
function v(e) {
    const n = X(e.doc, e.selection.anchor);
    return ee(n);
}
function je(e) {
    const n = X(e.doc, e.selection.anchor);
    return ee(n);
}
function M(e) {
    return "doc" in e ? e.doc.type.schema : e.type.schema;
}
function bn(e) {
    return e.cached.blockNoteEditor;
}
function Me(e) {
    return bn(e).schema;
}
function wt(e) {
    return Me(e).blockSchema;
}
function yt(e) {
    return Me(e).inlineContentSchema;
}
function be(e) {
    return Me(e).styleSchema;
}
function Ct(e) {
    return bn(e).blockCache;
}
function kn(e, n, t) {
    var s, i;
    const o = {
        type: "tableContent",
        columnWidths: [],
        headerRows: void 0,
        headerCols: void 0,
        rows: []
    }, r = [];
    e.content.forEach((a, c, l)=>{
        const d = {
            cells: []
        };
        l === 0 && a.content.forEach((u)=>{
            let h = u.attrs.colwidth;
            h == null && (h = new Array(u.attrs.colspan ?? 1).fill(void 0)), o.columnWidths.push(...h);
        }), d.cells = a.content.content.map((u, h)=>(r[l] || (r[l] = []), r[l][h] = u.type.name === "tableHeader", {
                type: "tableCell",
                content: u.content.content.map((m)=>Ge(m, n, t)).reduce((m, g)=>{
                    if (!m.length) return g;
                    const b = m[m.length - 1], k = g[0];
                    return k && de(b) && de(k) && JSON.stringify(b.styles) === JSON.stringify(k.styles) ? (b.text += `
` + k.text, m.push(...g.slice(1)), m) : (m.push(...g), m);
                }, []),
                props: {
                    colspan: u.attrs.colspan,
                    rowspan: u.attrs.rowspan,
                    backgroundColor: u.attrs.backgroundColor,
                    textColor: u.attrs.textColor,
                    textAlignment: u.attrs.textAlignment
                }
            })), o.rows.push(d);
    });
    for(let a = 0; a < r.length; a++)(s = r[a]) != null && s.every((c)=>c) && (o.headerRows = (o.headerRows ?? 0) + 1);
    for(let a = 0; a < ((i = r[0]) == null ? void 0 : i.length); a++)r != null && r.every((c)=>c[a]) && (o.headerCols = (o.headerCols ?? 0) + 1);
    return o;
}
function Ge(e, n, t) {
    const o = [];
    let r;
    return e.content.forEach((s)=>{
        if (s.type.name === "hardBreak") {
            if (r) if (de(r)) r.text += `
`;
            else if (Ot(r)) r.content[r.content.length - 1].text += `
`;
            else throw new Error("unexpected");
            else r = {
                type: "text",
                text: `
`,
                styles: {}
            };
            return;
        }
        if (s.type.name !== "link" && s.type.name !== "text") {
            if (!n[s.type.name]) {
                console.warn("unrecognized inline content type", s.type.name);
                return;
            }
            r && (o.push(r), r = void 0), o.push(it(s, n, t));
            return;
        }
        const i = {};
        let a;
        for (const c of s.marks)if (c.type.name === "link") a = c;
        else {
            const l = t[c.type.name];
            if (!l) {
                if (c.type.spec.blocknoteIgnore) continue;
                throw new Error(`style ${c.type.name} not found in styleSchema`);
            }
            if (l.propSchema === "boolean") i[l.type] = !0;
            else if (l.propSchema === "string") i[l.type] = c.attrs.stringValue;
            else throw new j(l.propSchema);
        }
        r ? de(r) ? a ? (o.push(r), r = {
            type: "link",
            href: a.attrs.href,
            content: [
                {
                    type: "text",
                    text: s.textContent,
                    styles: i
                }
            ]
        }) : JSON.stringify(r.styles) === JSON.stringify(i) ? r.text += s.textContent : (o.push(r), r = {
            type: "text",
            text: s.textContent,
            styles: i
        }) : Ot(r) && (a ? r.href === a.attrs.href ? JSON.stringify(r.content[r.content.length - 1].styles) === JSON.stringify(i) ? r.content[r.content.length - 1].text += s.textContent : r.content.push({
            type: "text",
            text: s.textContent,
            styles: i
        }) : (o.push(r), r = {
            type: "link",
            href: a.attrs.href,
            content: [
                {
                    type: "text",
                    text: s.textContent,
                    styles: i
                }
            ]
        }) : (o.push(r), r = {
            type: "text",
            text: s.textContent,
            styles: i
        })) : a ? r = {
            type: "link",
            href: a.attrs.href,
            content: [
                {
                    type: "text",
                    text: s.textContent,
                    styles: i
                }
            ]
        } : r = {
            type: "text",
            text: s.textContent,
            styles: i
        };
    }), r && o.push(r), o;
}
function it(e, n, t) {
    if (e.type.name === "text" || e.type.name === "link") throw new Error("unexpected");
    const o = {}, r = n[e.type.name];
    for (const [a, c] of Object.entries(e.attrs)){
        if (!r) throw Error("ic node is of an unrecognized type: " + e.type.name);
        const l = r.propSchema;
        a in l && (o[a] = c);
    }
    let s;
    return r.content === "styled" ? s = Ge(e, n, t) : s = void 0, {
        type: e.type.name,
        props: o,
        content: s
    };
}
function E(e, n, t = wt(n), o = yt(n), r = be(n), s = Ct(n)) {
    var g;
    if (!e.type.isInGroup("bnBlock")) throw Error("Node should be a bnBlock, but is instead: " + e.type.name);
    const i = s == null ? void 0 : s.get(e);
    if (i) return i;
    const a = kt(e, 0);
    let c = a.bnBlock.node.attrs.id;
    c === null && (c = We.options.generateID());
    const l = t[a.blockNoteType];
    if (!l) throw Error("Block is of an unrecognized type: " + a.blockNoteType);
    const d = {};
    for (const [b, k] of Object.entries({
        ...e.attrs,
        ...a.isBlockContainer ? a.blockContent.node.attrs : {}
    })){
        const w = l.propSchema;
        b in w && !(w[b].default === void 0 && k === void 0) && (d[b] = k);
    }
    const u = t[a.blockNoteType], h = [];
    (g = a.childContainer) == null || g.node.forEach((b)=>{
        h.push(E(b, n, t, o, r, s));
    });
    let f;
    if (u.content === "inline") {
        if (!a.isBlockContainer) throw new Error("impossible");
        f = Ge(a.blockContent.node, o, r);
    } else if (u.content === "table") {
        if (!a.isBlockContainer) throw new Error("impossible");
        f = kn(a.blockContent.node, o, r);
    } else if (u.content === "none") f = void 0;
    else throw new j(u.content);
    const m = {
        id: c,
        type: u.type,
        props: d,
        content: f,
        children: h
    };
    return s == null || s.set(e, m), m;
}
function lr(e, n, t = wt(n), o = yt(n), r = be(n), s = Ct(n)) {
    const i = [];
    return e.firstChild.descendants((a)=>(i.push(E(a, n, t, o, r, s)), !1)), i;
}
function dr(e, n, t = wt(n), o = yt(n), r = be(n), s = Ct(n)) {
    function i(a, c, l) {
        if (a.type.name !== "blockGroup") throw new Error("unexpected");
        const d = [];
        let u, h;
        return a.forEach((f, m, g)=>{
            if (f.type.name !== "blockContainer") throw new Error("unexpected");
            if (f.childCount === 0) return;
            if (f.childCount === 0 || f.childCount > 2) throw new Error("unexpected, blockContainer.childCount: " + f.childCount);
            const b = g === 0, k = g === a.childCount - 1;
            if (f.firstChild.type.name === "blockGroup") {
                if (!b) throw new Error("unexpected");
                const P = i(f.firstChild, Math.max(0, c - 1), k ? Math.max(0, l - 1) : 0);
                u = P.blockCutAtStart, k && (h = P.blockCutAtEnd), d.push(...P.blocks);
                return;
            }
            const w = E(f, n, t, o, r, s), C = f.childCount > 1 ? f.child(1) : void 0;
            let B = [];
            if (C) {
                const P = i(C, 0, // TODO: can this be anything other than 0?
                k ? Math.max(0, l - 1) : 0);
                B = P.blocks, k && (h = P.blockCutAtEnd);
            }
            k && !C && l > 1 && (h = w.id), b && c > 1 && (u = w.id), d.push({
                ...w,
                children: B
            });
        }), {
            blocks: d,
            blockCutAtStart: u,
            blockCutAtEnd: h
        };
    }
    if (e.content.childCount === 0) return {
        blocks: [],
        blockCutAtStart: void 0,
        blockCutAtEnd: void 0
    };
    if (e.content.childCount !== 1) throw new Error("slice must be a single block, did you forget includeParents=true?");
    return i(e.content.firstChild, Math.max(e.openStart - 1, 0), Math.max(e.openEnd - 1, 0));
}
function Vt(e, n, t, o) {
    return e.dom.setAttribute("data-inline-content-type", n), Object.entries(t).filter(([r, s])=>{
        const i = o[r];
        return s !== i.default;
    }).map(([r, s])=>[
            Ve(r),
            s
        ]).forEach(([r, s])=>e.dom.setAttribute(r, s)), e.contentDOM !== void 0 && e.contentDOM.setAttribute("data-editable", ""), e;
}
function ur(e) {
    return {
        Backspace: ({ editor: n })=>{
            const t = n.state.selection.$from;
            return n.state.selection.empty && t.node().type.name === e.type && t.parentOffset === 0;
        }
    };
}
function pr(e, n) {
    return {
        config: e,
        implementation: n
    };
}
function hr(e, n) {
    return pr({
        type: e.name,
        propSchema: n,
        content: e.config.content === "inline*" ? "styled" : "none"
    }, {
        node: e
    });
}
function wn(e) {
    return Object.fromEntries(Object.entries(e).map(([n, t])=>[
            n,
            t.config
        ]));
}
function fr(e) {
    return [
        {
            tag: `[data-inline-content-type="${e.type}"]`,
            contentElement: (n)=>{
                const t = n;
                return t.matches("[data-editable]") ? t : t.querySelector("[data-editable]") || t;
            }
        }
    ];
}
function Ic(e, n) {
    const t = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Node"].create({
        name: e.type,
        inline: !0,
        group: "inline",
        selectable: e.content === "styled",
        atom: e.content === "none",
        content: e.content === "styled" ? "inline*" : "",
        addAttributes () {
            return Be(e.propSchema);
        },
        addKeyboardShortcuts () {
            return ur(e);
        },
        parseHTML () {
            return fr(e);
        },
        renderHTML ({ node: o }) {
            const r = this.options.editor, s = n.render(it(o, r.schema.inlineContentSchema, r.schema.styleSchema), // TODO: fix cast
            ()=>{}, r);
            return Vt(s, e.type, o.attrs, e.propSchema);
        },
        addNodeView () {
            return ({ node: o, getPos: r })=>{
                const s = this.options.editor, i = n.render(it(o, s.schema.inlineContentSchema, s.schema.styleSchema), // TODO: fix cast
                (a)=>{
                    if (typeof r == "boolean") return;
                    const c = F([
                        a
                    ], s.pmSchema);
                    s.transact((l)=>l.replaceWith(r(), r() + o.nodeSize, c));
                }, s);
                return Vt(i, e.type, o.attrs, e.propSchema);
            };
        }
    });
    return hr(t, e.propSchema);
}
function mr(e) {
    return e === "boolean" ? {} : {
        stringValue: {
            default: void 0,
            keepOnSplit: !0,
            parseHTML: (n)=>n.getAttribute("data-value"),
            renderHTML: (n)=>n.stringValue !== void 0 ? {
                    "data-value": n.stringValue
                } : {}
        }
    };
}
function gr(e, n, t, o) {
    return e.dom.setAttribute("data-style-type", n), o === "string" && e.dom.setAttribute("data-value", t), e.contentDOM !== void 0 && e.contentDOM.setAttribute("data-editable", ""), e;
}
function yn(e, n) {
    return {
        config: e,
        implementation: n
    };
}
function ie(e, n) {
    return yn({
        type: e.name,
        propSchema: n
    }, {
        mark: e
    });
}
function Cn(e) {
    return Object.fromEntries(Object.entries(e).map(([n, t])=>[
            n,
            t.config
        ]));
}
function br(e) {
    return [
        {
            tag: `[data-style-type="${e.type}"]`,
            contentElement: (n)=>{
                const t = n;
                return t.matches("[data-editable]") ? t : t.querySelector("[data-editable]") || t;
            }
        }
    ];
}
function Lc(e, n) {
    const t = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Mark"].create({
        name: e.type,
        addAttributes () {
            return mr(e.propSchema);
        },
        parseHTML () {
            return br(e);
        },
        renderHTML ({ mark: o }) {
            let r;
            if (e.propSchema === "boolean") r = n.render();
            else if (e.propSchema === "string") r = n.render(o.attrs.stringValue);
            else throw new j(e.propSchema);
            return gr(r, e.type, o.attrs.stringValue, e.propSchema);
        }
    });
    return yn(e, {
        mark: t
    });
}
function re(e) {
    const { height: n, width: t } = vt(e), o = new Array(n).fill(!1).map(()=>new Array(t).fill(null)), r = (s, i)=>{
        for(let a = s; a < n; a++)for(let c = i; c < t; c++)if (!o[a][c]) return {
            row: a,
            col: c
        };
        throw new Error("Unable to create occupancy grid for table, no more available cells");
    };
    for(let s = 0; s < e.content.rows.length; s++)for(let i = 0; i < e.content.rows[s].cells.length; i++){
        const a = rt(e.content.rows[s].cells[i]), c = st(a), l = ve(a), { row: d, col: u } = r(s, i);
        for(let h = d; h < d + c; h++)for(let f = u; f < u + l; f++){
            if (o[h][f]) throw new Error(`Unable to create occupancy grid for table, cell at ${h},${f} is already occupied`);
            o[h][f] = {
                row: s,
                col: i,
                rowspan: c,
                colspan: l,
                cell: a
            };
        }
    }
    return o;
}
function Ee(e) {
    const n = /* @__PURE__ */ new Set();
    return e.map((t)=>({
            cells: t.map((o)=>n.has(o.row + ":" + o.col) ? !1 : (n.add(o.row + ":" + o.col), o.cell)).filter((o)=>o !== !1)
        }));
}
function ue(e, n, t = re(n)) {
    for(let o = 0; o < t.length; o++)for(let r = 0; r < t[o].length; r++){
        const s = t[o][r];
        if (s.row === e.row && s.col === e.col) return {
            row: o,
            col: r,
            cell: s.cell
        };
    }
    throw new Error(`Unable to resolve relative table cell indices for table, cell at ${e.row},${e.col} is not occupied`);
}
function vt(e) {
    const n = e.content.rows.length;
    let t = 0;
    return e.content.rows.forEach((o)=>{
        let r = 0;
        o.cells.forEach((s)=>{
            r += ve(s);
        }), t = Math.max(t, r);
    }), {
        height: n,
        width: t
    };
}
function vn(e, n, t = re(n)) {
    var r;
    const o = (r = t[e.row]) == null ? void 0 : r[e.col];
    if (o) return {
        row: o.row,
        col: o.col,
        cell: o.cell
    };
}
function at(e, n) {
    var s;
    const t = re(e);
    if (n < 0 || n >= t.length) return [];
    let o = 0;
    for(let i = 0; i < n; i++){
        const a = (s = t[o]) == null ? void 0 : s[0];
        if (!a) return [];
        o += a.rowspan;
    }
    const r = new Array(t[0].length).fill(!1).map((i, a)=>vn({
            row: o,
            col: a
        }, e, t)).filter((i)=>i !== void 0);
    return r.filter((i, a)=>r.findIndex((c)=>c.row === i.row && c.col === i.col) === a);
}
function ct(e, n) {
    var s;
    const t = re(e);
    if (n < 0 || n >= t[0].length) return [];
    let o = 0;
    for(let i = 0; i < n; i++){
        const a = (s = t[0]) == null ? void 0 : s[o];
        if (!a) return [];
        o += a.colspan;
    }
    const r = new Array(t.length).fill(!1).map((i, a)=>vn({
            row: a,
            col: o
        }, e, t)).filter((i)=>i !== void 0);
    return r.filter((i, a)=>r.findIndex((c)=>c.row === i.row && c.col === i.col) === a);
}
function kr(e, n, t, o = re(e)) {
    const { col: r } = ue({
        row: 0,
        col: n
    }, e, o), { col: s } = ue({
        row: 0,
        col: t
    }, e, o);
    return o.forEach((i)=>{
        const [a] = i.splice(r, 1);
        i.splice(s, 0, a);
    }), Ee(o);
}
function wr(e, n, t, o = re(e)) {
    const { row: r } = ue({
        row: n,
        col: 0
    }, e, o), { row: s } = ue({
        row: t,
        col: 0
    }, e, o), [i] = o.splice(r, 1);
    return o.splice(s, 0, i), Ee(o);
}
function lt(e) {
    return e ? Ce(e) ? lt(e.content) : typeof e == "string" ? e.length === 0 : Array.isArray(e) ? e.every((n)=>typeof n == "string" ? n.length === 0 : de(n) ? n.text.length === 0 : hn(n) ? typeof n.content == "string" ? n.content.length === 0 : n.content.every((t)=>t.text.length === 0) : !1) : !1 : !0;
}
function yr(e, n, t = re(e)) {
    if (n === "columns") {
        let s = 0;
        for(let i = t[0].length - 1; i >= 0 && t.every((c)=>lt(c[i].cell) && c[i].colspan === 1); i--)s++;
        for(let i = t.length - 1; i >= 0; i--){
            const a = Math.max(t[i].length - s, 1);
            t[i] = t[i].slice(0, a);
        }
        return Ee(t);
    }
    let o = 0;
    for(let s = t.length - 1; s >= 0 && t[s].every((a)=>lt(a.cell) && a.rowspan === 1); s--)o++;
    const r = Math.min(o, t.length - 1);
    return t.splice(t.length - r, r), Ee(t);
}
function Cr(e, n, t, o = re(e)) {
    const { width: r, height: s } = vt(e);
    if (n === "columns") o.forEach((i, a)=>{
        if (t >= 0) for(let c = 0; c < t; c++)i.push({
            row: a,
            col: Math.max(...i.map((l)=>l.col)) + 1,
            rowspan: 1,
            colspan: 1,
            cell: rt("")
        });
        else i.splice(r + t, -1 * t);
    });
    else if (t > 0) for(let i = 0; i < t; i++){
        const a = new Array(r).fill(null).map((c, l)=>({
                row: s + i,
                col: l,
                rowspan: 1,
                colspan: 1,
                cell: rt("")
            }));
        o.push(a);
    }
    else t < 0 && o.splice(s + t, -1 * t);
    return Ee(o);
}
function En(e, n, t) {
    const o = at(e, t);
    if (!o.some((c)=>st(c.cell) > 1)) return !0;
    let s = t, i = t;
    return o.forEach((c)=>{
        const l = st(c.cell);
        s = Math.max(s, c.row + l - 1), i = Math.min(i, c.row);
    }), n < t ? t === s : t === i;
}
function Sn(e, n, t) {
    const o = ct(e, t);
    if (!o.some((c)=>ve(c.cell) > 1)) return !0;
    let s = t, i = t;
    return o.forEach((c)=>{
        const l = ve(c.cell);
        s = Math.max(s, c.col + l - 1), i = Math.min(i, c.col);
    }), n < t ? t === s : t === i;
}
function vr(e, n, t) {
    const o = ue(e, t), r = ue(n, t);
    return o.col === r.col;
}
function Ut(e, n, t, o) {
    const r = [];
    for (const [i, a] of Object.entries(e.styles || {})){
        const c = t[i];
        if (!c) throw new Error(`style ${i} not found in styleSchema`);
        if (c.propSchema === "boolean") a && r.push(n.mark(i));
        else if (c.propSchema === "string") a && r.push(n.mark(i, {
            stringValue: a
        }));
        else throw new j(c.propSchema);
    }
    return !o || !n.nodes[o].spec.code ? e.text.split(/(\n)/g).filter((i)=>i.length > 0).map((i)=>i === `
` ? n.nodes.hardBreak.createChecked() : n.text(i, r)) : e.text.length > 0 ? [
        n.text(e.text, r)
    ] : [];
}
function Er(e, n, t) {
    const o = n.marks.link.create({
        href: e.href
    });
    return dt(e.content, n, t).map((r)=>{
        if (r.type.name === "text") return r.mark([
            ...r.marks,
            o
        ]);
        if (r.type.name === "hardBreak") return r;
        throw new Error("unexpected node type");
    });
}
function dt(e, n, t, o) {
    const r = [];
    if (typeof e == "string") return r.push(...Ut({
        text: e,
        styles: {}
    }, n, t, o)), r;
    for (const s of e)r.push(...Ut(s, n, t, o));
    return r;
}
function F(e, n, t, o = be(n)) {
    const r = [];
    for (const s of e)typeof s == "string" ? r.push(...dt(s, n, o, t)) : hn(s) ? r.push(...Er(s, n, o)) : de(s) ? r.push(...dt([
        s
    ], n, o, t)) : r.push(Bn(s, n, o));
    return r;
}
function qe(e, n, t = be(n)) {
    const o = [], r = new Array(e.headerRows ?? 0).fill(!0), s = new Array(e.headerCols ?? 0).fill(!0), i = e.columnWidths ?? [];
    for(let a = 0; a < e.rows.length; a++){
        const c = e.rows[a], l = [], d = r[a];
        for(let h = 0; h < c.cells.length; h++){
            const f = c.cells[h], m = s[h], g = void 0;
            let b = null;
            const k = ue({
                row: a,
                col: h
            }, {
                content: e
            });
            let w = i[k.col] ? [
                i[k.col]
            ] : null;
            if (f) if (typeof f == "string") b = n.text(f);
            else if (Ce(f)) {
                f.content && (b = F(f.content, n, "tableParagraph", t));
                const B = ve(f);
                B > 1 && (w = new Array(B).fill(!1).map((P, $)=>i[k.col + $] ?? void 0));
            } else b = F(f, n, "tableParagraph", t);
            const C = n.nodes[m || d ? "tableHeader" : "tableCell"].createChecked({
                ...Ce(f) ? f.props : {},
                colwidth: w
            }, n.nodes.tableParagraph.createChecked(g, b));
            l.push(C);
        }
        const u = n.nodes.tableRow.createChecked({}, l);
        o.push(u);
    }
    return o;
}
function Bn(e, n, t) {
    let o, r = e.type;
    if (r === void 0 && (r = "paragraph"), !n.nodes[r]) throw new Error(`node type ${r} not found in schema`);
    if (!e.content) o = n.nodes[r].createChecked(e.props);
    else if (typeof e.content == "string") {
        const s = F([
            e.content
        ], n, r, t);
        o = n.nodes[r].createChecked(e.props, s);
    } else if (Array.isArray(e.content)) {
        const s = F(e.content, n, r, t);
        o = n.nodes[r].createChecked(e.props, s);
    } else if (e.content.type === "tableContent") {
        const s = qe(e.content, n, t);
        o = n.nodes[r].createChecked(e.props, s);
    } else throw new j(e.content.type);
    return o;
}
function pe(e, n, t = be(n)) {
    let o = e.id;
    o === void 0 && (o = We.options.generateID());
    const r = [];
    if (e.children) for (const i of e.children)r.push(pe(i, n, t));
    if (!e.type || // can happen if block.type is not defined (this should create the default node)
    n.nodes[e.type].isInGroup("blockContent")) {
        const i = Bn(e, n, t), a = r.length > 0 ? n.nodes.blockGroup.createChecked({}, r) : void 0;
        return n.nodes.blockContainer.createChecked({
            id: o,
            ...e.props
        }, a ? [
            i,
            a
        ] : i);
    } else {
        if (n.nodes[e.type].isInGroup("bnBlock")) return n.nodes[e.type].createChecked({
            id: o,
            ...e.props
        }, r);
        throw new Error(`block type ${e.type} doesn't match blockContent or bnBlock group`);
    }
}
function _(e, n) {
    let t, o;
    if (n.firstChild.descendants((r, s)=>t ? !1 : !ut(r) || r.attrs.id !== e ? !0 : (t = r, o = s + 1, !1)), !(t === void 0 || o === void 0)) return {
        node: t,
        posBeforeNode: o
    };
}
function ut(e) {
    return e.type.isInGroup("bnBlock");
}
function Sr(e, n) {
    return e.id !== n.id || e.type !== n.type || JSON.stringify(e.props) !== JSON.stringify(n.props) || JSON.stringify(e.content) !== JSON.stringify(n.content);
}
function Br(e, n = []) {
    let t = {
        type: "local"
    };
    e.getMeta("paste") ? t = {
        type: "paste"
    } : e.getMeta("uiEvent") === "drop" ? t = {
        type: "drop"
    } : e.getMeta("history$") ? t = {
        type: e.getMeta("history$").redo ? "redo" : "undo"
    } : e.getMeta("y-sync$") && (e.getMeta("y-sync$").isUndoRedoOperation ? t = {
        type: "undo-redo"
    } : t = {
        type: "yjs-remote"
    });
    const o = M(e), r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["combineTransactionSteps"])(e.before, [
        e,
        ...n
    ]), s = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getChangedRanges"])(r), i = s.flatMap((u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findChildrenInRange"])(r.before, u.oldRange, ut)).map(({ node: u })=>E(u, o)), a = s.flatMap((u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findChildrenInRange"])(r.doc, u.newRange, ut)).map(({ node: u })=>E(u, o)), c = new Map(a.map((u)=>[
            u.id,
            u
        ])), l = new Map(i.map((u)=>[
            u.id,
            u
        ])), d = [];
    for (const [u, h] of c)l.has(u) || d.push({
        type: "insert",
        block: h,
        source: t,
        prevBlock: void 0
    });
    for (const [u, h] of l)c.has(u) || d.push({
        type: "delete",
        block: h,
        source: t,
        prevBlock: void 0
    });
    for (const [u, h] of c)if (l.has(u)) {
        const f = l.get(u);
        Sr(f, h) && d.push({
            type: "update",
            block: h,
            prevBlock: f,
            source: t
        });
    }
    return d;
}
function xr(e, n, t, o = "before") {
    const r = typeof t == "string" ? t : t.id, s = M(e), i = n.map((d)=>pe(d, s)), a = _(r, e.doc);
    if (!a) throw new Error(`Block with ID ${r} not found`);
    let c = a.posBeforeNode;
    return o === "after" && (c += a.node.nodeSize), e.step(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$transform$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReplaceStep"](c, c, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Slice"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"].from(i), 0, 0))), i.map((d)=>E(d, s));
}
function _t(e, n, t) {
    const o = M(e), r = t.map((d)=>pe(d, o)), s = new Set(n.map((d)=>typeof d == "string" ? d : d.id)), i = [], a = typeof n[0] == "string" ? n[0] : n[0].id;
    let c = 0;
    if (e.doc.descendants((d, u)=>{
        if (s.size === 0) return !1;
        if (!d.type.isInGroup("bnBlock") || !s.has(d.attrs.id)) return !0;
        if (i.push(E(d, o)), s.delete(d.attrs.id), t.length > 0 && d.attrs.id === a) {
            const g = e.doc.nodeSize;
            e.insert(u, r);
            const b = e.doc.nodeSize;
            c += g - b;
        }
        const h = e.doc.nodeSize, f = e.doc.resolve(u - c);
        f.node().type.name === "blockGroup" && f.node(f.depth - 1).type.name !== "doc" && f.node().childCount === 1 ? e.delete(f.before(), f.after()) : e.delete(u - c, u - c + d.nodeSize);
        const m = e.doc.nodeSize;
        return c += h - m, !1;
    }), s.size > 0) {
        const d = [
            ...s
        ].join(`
`);
        throw Error("Blocks with the following IDs could not be found in the editor: " + d);
    }
    return {
        insertedBlocks: r.map((d)=>E(d, o)),
        removedBlocks: i
    };
}
const I = (e, n)=>({ tr: t, dispatch: o })=>(o && xn(t, e, n), !0);
function xn(e, n, t, o, r) {
    const s = Te(e.doc.resolve(n)), i = M(e);
    if (o !== void 0 && r !== void 0 && o > r) throw new Error("Invalid replaceFromPos or replaceToPos");
    const a = i.nodes[s.blockNoteType], c = i.nodes[t.type || s.blockNoteType], l = c.isInGroup("bnBlock") ? c : i.nodes.blockContainer;
    if (s.isBlockContainer && c.isInGroup("blockContent")) {
        const d = o !== void 0 && o > s.blockContent.beforePos && o < s.blockContent.afterPos ? o - s.blockContent.beforePos - 1 : void 0, u = r !== void 0 && r > s.blockContent.beforePos && r < s.blockContent.afterPos ? r - s.blockContent.beforePos - 1 : void 0;
        $t(t, e, s), Tr(t, e, a, c, s, d, u);
    } else if (!s.isBlockContainer && c.isInGroup("bnBlock")) $t(t, e, s);
    else {
        const d = E(s.bnBlock.node, i);
        e.replaceWith(s.bnBlock.beforePos, s.bnBlock.afterPos, pe({
            children: d.children,
            // if no children are passed in, use existing children
            ...t
        }, i));
        return;
    }
    e.setNodeMarkup(s.bnBlock.beforePos, l, {
        ...s.bnBlock.node.attrs,
        ...t.props
    });
}
function Tr(e, n, t, o, r, s, i) {
    const a = M(n);
    let c = "keep";
    if (e.content) if (typeof e.content == "string") c = F([
        e.content
    ], a, o.name);
    else if (Array.isArray(e.content)) c = F(e.content, a, o.name);
    else if (e.content.type === "tableContent") c = qe(e.content, a);
    else throw new j(e.content.type);
    else t.spec.content === "" || o.spec.content !== t.spec.content && (c = []);
    if (c === "keep") n.setNodeMarkup(r.blockContent.beforePos, o, {
        ...r.blockContent.node.attrs,
        ...e.props
    });
    else if (s !== void 0 || i !== void 0) {
        n.setNodeMarkup(r.blockContent.beforePos, o, {
            ...r.blockContent.node.attrs,
            ...e.props
        });
        const l = r.blockContent.beforePos + 1 + (s ?? 0), d = r.blockContent.beforePos + 1 + (i ?? r.blockContent.node.content.size), u = n.doc.resolve(r.blockContent.beforePos).depth, h = n.doc.resolve(l).depth, f = n.doc.resolve(d).depth;
        n.replace(l, d, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Slice"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"].from(c), h - u - 1, f - u - 1));
    } else n.replaceWith(r.blockContent.beforePos, r.blockContent.afterPos, o.createChecked({
        ...r.blockContent.node.attrs,
        ...e.props
    }, c));
}
function $t(e, n, t) {
    const o = M(n);
    if (e.children !== void 0 && e.children.length > 0) {
        const r = e.children.map((s)=>pe(s, o));
        if (t.childContainer) n.step(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$transform$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReplaceStep"](t.childContainer.beforePos + 1, t.childContainer.afterPos - 1, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Slice"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"].from(r), 0, 0)));
        else {
            if (!t.isBlockContainer) throw new Error("impossible");
            n.insert(t.blockContent.afterPos, o.nodes.blockGroup.createChecked({}, r));
        }
    }
}
function Mr(e, n, t, o, r) {
    const s = typeof n == "string" ? n : n.id, i = _(s, e.doc);
    if (!i) throw new Error(`Block with ID ${s} not found`);
    xn(e, i.posBeforeNode, t, o, r);
    const a = e.doc.resolve(i.posBeforeNode + 1).node(), c = M(e);
    return E(a, c);
}
function Tn(e) {
    const n = Array.from(e.classList).filter((t)=>!t.startsWith("bn-")) || [];
    n.length > 0 ? e.className = n.join(" ") : e.removeAttribute("class");
}
function Mn(e, n, t, o) {
    let r;
    if (n) if (typeof n == "string") r = F([
        n
    ], e.pmSchema);
    else if (Array.isArray(n)) r = F(n, e.pmSchema);
    else if (n.type === "tableContent") r = qe(n, e.pmSchema);
    else throw new j(n.type);
    else throw new Error("blockContent is required");
    const s = t.serializeFragment(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"].from(r), o);
    return s.nodeType === 1 && Tn(s), s;
}
function Pr(e, n, t, o, r, s, i) {
    var g, b, k, w, C, B, P, $;
    const a = (i == null ? void 0 : i.document) ?? document, c = n.pmSchema.nodes.blockContainer;
    let l = t.props;
    if (!t.props) {
        l = {};
        for (const [S, y] of Object.entries(n.schema.blockSchema[t.type].propSchema))y.default !== void 0 && (l[S] = y.default);
    }
    const d = (b = (g = c.spec) == null ? void 0 : g.toDOM) == null ? void 0 : b.call(g, c.create({
        id: t.id,
        ...l
    })), u = Array.from(d.dom.attributes), h = n.blockImplementations[t.type].implementation.toExternalHTML({
        ...t,
        props: l
    }, n), f = a.createDocumentFragment();
    if (h.dom.classList.contains("bn-block-content")) {
        const S = [
            ...u,
            ...Array.from(h.dom.attributes)
        ].filter((y)=>y.name.startsWith("data") && y.name !== "data-content-type" && y.name !== "data-file-block" && y.name !== "data-node-view-wrapper" && y.name !== "data-node-type" && y.name !== "data-id" && y.name !== "data-index" && y.name !== "data-editable");
        for (const y of S)h.dom.firstChild.setAttribute(y.name, y.value);
        Tn(h.dom.firstChild), f.append(...Array.from(h.dom.childNodes));
    } else f.append(h.dom);
    if (h.contentDOM && t.content) {
        const S = Mn(n, t.content, // TODO
        o, i);
        h.contentDOM.appendChild(S);
    }
    let m;
    if (r.has(t.type) ? m = "OL" : s.has(t.type) && (m = "UL"), m) {
        if (((k = e.lastChild) == null ? void 0 : k.nodeName) !== m) {
            const y = a.createElement(m);
            m === "OL" && l != null && l.start && (l == null ? void 0 : l.start) !== 1 && y.setAttribute("start", l.start + ""), e.append(y);
        }
        const S = a.createElement("li");
        S.append(f), e.lastChild.appendChild(S);
    } else e.append(f);
    if (t.children && t.children.length > 0) {
        const S = a.createDocumentFragment();
        if (Pn(S, n, t.children, o, r, s, i), ((w = e.lastChild) == null ? void 0 : w.nodeName) === "UL" || ((C = e.lastChild) == null ? void 0 : C.nodeName) === "OL") for(; ((B = S.firstChild) == null ? void 0 : B.nodeName) === "UL" || ((P = S.firstChild) == null ? void 0 : P.nodeName) === "OL";)e.lastChild.lastChild.appendChild(S.firstChild);
        n.pmSchema.nodes[t.type].isInGroup("blockContent") ? e.append(S) : ($ = h.contentDOM) == null || $.append(S);
    }
}
const Pn = (e, n, t, o, r, s, i)=>{
    for (const a of t)Pr(e, n, a, o, r, s, i);
}, Ir = (e, n, t, o, r, s)=>{
    const a = ((s == null ? void 0 : s.document) ?? document).createDocumentFragment();
    return Pn(a, e, n, t, o, r, s), a;
}, Ke = (e, n)=>{
    const t = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DOMSerializer"].fromSchema(e);
    return {
        exportBlocks: (o, r)=>{
            const s = Ir(n, o, t, /* @__PURE__ */ new Set([
                "numberedListItem"
            ]), /* @__PURE__ */ new Set([
                "bulletListItem",
                "checkListItem"
            ]), r), i = document.createElement("div");
            return i.append(s), i.innerHTML;
        },
        exportInlineContent: (o, r)=>{
            const s = Mn(n, o, t, r), i = document.createElement("div");
            return i.append(s.cloneNode(!0)), i.innerHTML;
        }
    };
};
function Lr(e, n, t, o, r) {
    let s;
    if (n) if (typeof n == "string") s = F([
        n
    ], e.pmSchema, o);
    else if (Array.isArray(n)) s = F(n, e.pmSchema, o);
    else if (n.type === "tableContent") s = qe(n, e.pmSchema);
    else throw new j(n.type);
    else throw new Error("blockContent is required");
    return t.serializeFragment(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"].from(s), r);
}
function Ar(e, n, t, o, r) {
    var u, h, f, m, g;
    const s = e.pmSchema.nodes.blockContainer;
    let i = n.props;
    if (!n.props) {
        i = {};
        for (const [b, k] of Object.entries(e.schema.blockSchema[n.type].propSchema))k.default !== void 0 && (i[b] = k.default);
    }
    const c = e.blockImplementations[n.type].implementation.toInternalHTML({
        ...n,
        props: i
    }, e);
    if (n.type === "numberedListItem" && c.dom.setAttribute("data-index", o.toString()), c.contentDOM && n.content) {
        const b = Lr(e, n.content, // TODO
        t, n.type, r);
        c.contentDOM.appendChild(b);
    }
    if (e.pmSchema.nodes[n.type].isInGroup("bnBlock")) {
        if (n.children && n.children.length > 0) {
            const b = In(e, n.children, t, r);
            (u = c.contentDOM) == null || u.append(b);
        }
        return c.dom;
    }
    const d = (f = (h = s.spec) == null ? void 0 : h.toDOM) == null ? void 0 : f.call(h, s.create({
        id: n.id,
        ...i
    }));
    return (m = d.contentDOM) == null || m.appendChild(c.dom), n.children && n.children.length > 0 && ((g = d.contentDOM) == null || g.appendChild(Ln(e, n.children, t, r))), d.dom;
}
function In(e, n, t, o) {
    const s = ((o == null ? void 0 : o.document) ?? document).createDocumentFragment();
    let i = 0;
    for (const a of n){
        a.type === "numberedListItem" ? i++ : i = 0;
        const c = Ar(e, a, t, i, o);
        s.appendChild(c);
    }
    return s;
}
const Ln = (e, n, t, o)=>{
    var a;
    const r = e.pmSchema.nodes.blockGroup, s = r.spec.toDOM(r.create({})), i = In(e, n, t, o);
    return (a = s.contentDOM) == null || a.appendChild(i), s.dom;
}, Nr = (e, n)=>{
    const t = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DOMSerializer"].fromSchema(e);
    return {
        serializeBlocks: (o, r)=>Ln(n, o, t, r).outerHTML
    };
}, Je = (e, n)=>{
    const t = e.querySelector(n);
    if (!t) return;
    const o = e.querySelector("figcaption"), r = (o == null ? void 0 : o.textContent) ?? void 0;
    return {
        targetElement: t,
        caption: r
    };
}, Hr = (e, n, t, o)=>{
    const r = document.createElement("div");
    r.className = "bn-add-file-button";
    const s = document.createElement("div");
    s.className = "bn-add-file-button-icon", o ? s.appendChild(o) : s.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M3 8L9.00319 2H19.9978C20.5513 2 21 2.45531 21 2.9918V21.0082C21 21.556 20.5551 22 20.0066 22H3.9934C3.44476 22 3 21.5501 3 20.9932V8ZM10 4V9H5V20H19V4H10Z"></path></svg>', r.appendChild(s);
    const i = document.createElement("p");
    i.className = "bn-add-file-button-text", i.innerHTML = t || n.dictionary.file_blocks.file.add_button_text, r.appendChild(i);
    const a = (l)=>{
        l.preventDefault();
    }, c = ()=>{
        n.transact((l)=>l.setMeta(n.filePanel.plugins[0], {
                block: e
            }));
    };
    return r.addEventListener("mousedown", a, !0), r.addEventListener("click", c, !0), {
        dom: r,
        destroy: ()=>{
            r.removeEventListener("mousedown", a, !0), r.removeEventListener("click", c, !0);
        }
    };
}, Dr = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M3 8L9.00319 2H19.9978C20.5513 2 21 2.45531 21 2.9918V21.0082C21 21.556 20.5551 22 20.0066 22H3.9934C3.44476 22 3 21.5501 3 20.9932V8ZM10 4V9H5V20H19V4H10Z"></path></svg>', Or = (e)=>{
    const n = document.createElement("div");
    n.className = "bn-file-name-with-icon";
    const t = document.createElement("div");
    t.className = "bn-file-icon", t.innerHTML = Dr, n.appendChild(t);
    const o = document.createElement("p");
    return o.className = "bn-file-name", o.textContent = e.props.name, n.appendChild(o), {
        dom: n
    };
}, Et = (e, n, t, o, r)=>{
    const s = document.createElement("div");
    if (s.className = "bn-file-block-content-wrapper", e.props.url === "") {
        const a = Hr(e, n, o, r);
        s.appendChild(a.dom);
        const c = n.onUploadStart((l)=>{
            if (l === e.id) {
                s.removeChild(a.dom);
                const d = document.createElement("div");
                d.className = "bn-file-loading-preview", d.textContent = "Loading...", s.appendChild(d);
            }
        });
        return {
            dom: s,
            destroy: ()=>{
                c(), a.destroy();
            }
        };
    }
    const i = {
        dom: s
    };
    if (e.props.showPreview === !1 || !t) {
        const a = Or(e);
        s.appendChild(a.dom), i.destroy = ()=>{
            var c;
            (c = a.destroy) == null || c.call(a);
        };
    } else s.appendChild(t.dom);
    if (e.props.caption) {
        const a = document.createElement("p");
        a.className = "bn-file-caption", a.textContent = e.props.caption, s.appendChild(a);
    }
    return i;
}, St = (e, n)=>{
    const t = document.createElement("figure"), o = document.createElement("figcaption");
    return o.textContent = n, t.appendChild(e), t.appendChild(o), {
        dom: t
    };
}, Xe = (e, n)=>{
    const t = document.createElement("div"), o = document.createElement("p");
    return o.textContent = n, t.appendChild(e), t.appendChild(o), {
        dom: t
    };
}, Ft = (e)=>({
        url: e.src || void 0
    }), Rr = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M2 16.0001H5.88889L11.1834 20.3319C11.2727 20.405 11.3846 20.4449 11.5 20.4449C11.7761 20.4449 12 20.2211 12 19.9449V4.05519C12 3.93977 11.9601 3.8279 11.887 3.73857C11.7121 3.52485 11.3971 3.49335 11.1834 3.66821L5.88889 8.00007H2C1.44772 8.00007 1 8.44778 1 9.00007V15.0001C1 15.5524 1.44772 16.0001 2 16.0001ZM23 12C23 15.292 21.5539 18.2463 19.2622 20.2622L17.8445 18.8444C19.7758 17.1937 21 14.7398 21 12C21 9.26016 19.7758 6.80629 17.8445 5.15557L19.2622 3.73779C21.5539 5.75368 23 8.70795 23 12ZM18 12C18 10.0883 17.106 8.38548 15.7133 7.28673L14.2842 8.71584C15.3213 9.43855 16 10.64 16 12C16 13.36 15.3213 14.5614 14.2842 15.2841L15.7133 16.7132C17.106 15.6145 18 13.9116 18 12Z"></path></svg>', Vr = {
    backgroundColor: T.backgroundColor,
    // File name.
    name: {
        default: ""
    },
    // File url.
    url: {
        default: ""
    },
    // File caption.
    caption: {
        default: ""
    },
    showPreview: {
        default: !0
    }
}, Ur = {
    type: "audio",
    propSchema: Vr,
    content: "none",
    isFileBlock: !0,
    fileBlockAccept: [
        "audio/*"
    ]
}, _r = (e, n)=>{
    const t = document.createElement("div");
    t.innerHTML = Rr;
    const o = document.createElement("audio");
    return o.className = "bn-audio", n.resolveFileUrl ? n.resolveFileUrl(e.props.url).then((r)=>{
        o.src = r;
    }) : o.src = e.props.url, o.controls = !0, o.contentEditable = "false", o.draggable = !1, Et(e, n, {
        dom: o
    }, n.dictionary.file_blocks.audio.add_button_text, t.firstElementChild);
}, $r = (e)=>{
    if (e.tagName === "AUDIO") return e.closest("figure") ? void 0 : Ft(e);
    if (e.tagName === "FIGURE") {
        const n = Je(e, "audio");
        if (!n) return;
        const { targetElement: t, caption: o } = n;
        return {
            ...Ft(t),
            caption: o
        };
    }
}, Fr = (e)=>{
    if (!e.props.url) {
        const t = document.createElement("p");
        return t.textContent = "Add audio", {
            dom: t
        };
    }
    let n;
    return e.props.showPreview ? (n = document.createElement("audio"), n.src = e.props.url) : (n = document.createElement("a"), n.href = e.props.url, n.textContent = e.props.name || e.props.url), e.props.caption ? e.props.showPreview ? St(n, e.props.caption) : Xe(n, e.props.caption) : {
        dom: n
    };
}, zr = xe(Ur, {
    render: _r,
    parse: $r,
    toExternalHTML: Fr
}), zt = Symbol.for("blocknote.shikiParser"), Ye = Symbol.for("blocknote.shikiHighlighterPromise"), Wr = {
    language: {
        default: "text"
    }
}, jr = q({
    name: "codeBlock",
    content: "inline*",
    group: "blockContent",
    marks: "insertion deletion modification",
    code: !0,
    defining: !0,
    addOptions () {
        return {
            defaultLanguage: "text",
            indentLineWithTab: !0,
            supportedLanguages: {}
        };
    },
    addAttributes () {
        const e = this.options;
        return {
            language: {
                default: e.editor.settings.codeBlock.defaultLanguage,
                parseHTML: (n)=>{
                    let t = n, o = null;
                    (t == null ? void 0 : t.tagName) === "DIV" && (t == null ? void 0 : t.dataset.contentType) === "codeBlock" && (t = t.children[0]), (t == null ? void 0 : t.tagName) === "PRE" && (t = t == null ? void 0 : t.children[0]);
                    const r = t == null ? void 0 : t.getAttribute("data-language");
                    if (r) o = r.toLowerCase();
                    else {
                        const i = [
                            ...(t == null ? void 0 : t.className.split(" ")) || []
                        ].filter((a)=>a.startsWith("language-")).map((a)=>a.replace("language-", ""));
                        i.length > 0 && (o = i[0].toLowerCase());
                    }
                    return o ? Ze(e.editor.settings.codeBlock, o) ?? o : null;
                },
                renderHTML: (n)=>n.language ? {
                        class: `language-${n.language}`,
                        "data-language": n.language
                    } : {}
            }
        };
    },
    parseHTML () {
        return [
            // Parse from internal HTML.
            {
                tag: "div[data-content-type=" + this.name + "]",
                contentElement: ".bn-inline-content"
            },
            // Parse from external HTML.
            {
                tag: "pre",
                // contentElement: "code",
                preserveWhitespace: "full"
            }
        ];
    },
    renderHTML ({ HTMLAttributes: e }) {
        var r, s;
        const n = document.createElement("pre"), { dom: t, contentDOM: o } = G(this.name, "code", ((r = this.options.domAttributes) == null ? void 0 : r.blockContent) || {}, {
            ...((s = this.options.domAttributes) == null ? void 0 : s.inlineContent) || {},
            ...e
        });
        return t.removeChild(o), t.appendChild(n), n.appendChild(o), {
            dom: t,
            contentDOM: o
        };
    },
    addNodeView () {
        const e = this.options;
        return ({ editor: n, node: t, getPos: o, HTMLAttributes: r })=>{
            var u, h;
            const s = document.createElement("pre"), i = document.createElement("select"), a = document.createElement("div"), { dom: c, contentDOM: l } = G(this.name, "code", {
                ...((u = this.options.domAttributes) == null ? void 0 : u.blockContent) || {},
                ...r
            }, ((h = this.options.domAttributes) == null ? void 0 : h.inlineContent) || {}), d = (f)=>{
                const m = f.target.value;
                n.commands.command(({ tr: g })=>(g.setNodeAttribute(o(), "language", m), !0));
            };
            return Object.entries(e.editor.settings.codeBlock.supportedLanguages).forEach(([f, { name: m }])=>{
                const g = document.createElement("option");
                g.value = f, g.text = m, i.appendChild(g);
            }), a.contentEditable = "false", i.value = t.attrs.language || e.editor.settings.codeBlock.defaultLanguage, c.removeChild(l), c.appendChild(a), c.appendChild(s), s.appendChild(l), a.appendChild(i), i.addEventListener("change", d), {
                dom: c,
                contentDOM: l,
                update: (f)=>f.type === this.type,
                destroy: ()=>{
                    i.removeEventListener("change", d);
                }
            };
        };
    },
    addProseMirrorPlugins () {
        const e = this.options, n = globalThis;
        let t, o, r = !1;
        return [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createHighlightPlugin"])({
                parser: (a)=>{
                    if (!e.editor.settings.codeBlock.createHighlighter) return ("TURBOPACK compile-time value", "development") === "development" && !r && (console.log("For syntax highlighting of code blocks, you must provide a `codeBlock.createHighlighter` function"), r = !0), [];
                    if (!t) return n[Ye] = n[Ye] || e.editor.settings.codeBlock.createHighlighter(), n[Ye].then((l)=>{
                        t = l;
                    });
                    const c = Ze(e.editor.settings.codeBlock, a.language);
                    return !c || c === "text" || c === "none" || c === "plaintext" || c === "txt" ? [] : t.getLoadedLanguages().includes(c) ? (o || (o = n[zt] || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$highlight$2f$dist$2f$shiki$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createParser"])(t), n[zt] = o), o(a)) : t.loadLanguage(c);
                },
                languageExtractor: (a)=>a.attrs.language,
                nodeTypes: [
                    this.name
                ]
            })
        ];
    },
    addInputRules () {
        const e = this.options;
        return [
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputRule"]({
                find: /^```(.*?)\s$/,
                handler: ({ state: n, range: t, match: o })=>{
                    const r = n.doc.resolve(t.from), s = o[1].trim(), i = {
                        language: Ze(e.editor.settings.codeBlock, s) ?? s
                    };
                    if (!r.node(-1).canReplaceWith(r.index(-1), r.indexAfter(-1), this.type)) return null;
                    n.tr.delete(t.from, t.to).setBlockType(t.from, t.from, this.type, i).setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextSelection"].create(n.tr.doc, t.from));
                }
            })
        ];
    },
    addKeyboardShortcuts () {
        return {
            Delete: ({ editor: e })=>{
                const { selection: n } = e.state, { $from: t } = n;
                if (e.isActive(this.name) && !t.parent.textContent && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isTextSelection"])(n)) {
                    const o = t.pos - t.parentOffset - 2;
                    return e.chain().setNodeSelection(o).deleteSelection().run(), !0;
                }
                return !1;
            },
            Tab: ({ editor: e })=>this.options.indentLineWithTab && e.isActive(this.name) ? (e.commands.insertContent("  "), !0) : !1,
            Enter: ({ editor: e })=>{
                const { $from: n } = e.state.selection;
                if (!e.isActive(this.name)) return !1;
                const t = n.parentOffset === n.parent.nodeSize - 2, o = n.parent.textContent.endsWith(`

`);
                return !t || !o ? (e.commands.insertContent(`
`), !0) : e.chain().command(({ tr: r })=>(r.delete(n.pos - 2, n.pos), !0)).exitCode().run();
            },
            "Shift-Enter": ({ editor: e })=>{
                const { $from: n } = e.state.selection;
                return e.isActive(this.name) ? (e.chain().insertContentAt(n.pos - n.parentOffset + n.parent.nodeSize, {
                    type: "paragraph"
                }).run(), !0) : !1;
            }
        };
    }
}), Gr = oe(jr, Wr);
function Ze(e, n) {
    var t;
    return (t = Object.entries(e.supportedLanguages).find(([o, { aliases: r }])=>(r == null ? void 0 : r.includes(n)) || o === n)) == null ? void 0 : t[0];
}
const qr = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Mark"].create({
    name: "backgroundColor",
    addAttributes () {
        return {
            stringValue: {
                default: void 0,
                parseHTML: (e)=>e.getAttribute("data-background-color"),
                renderHTML: (e)=>({
                        "data-background-color": e.stringValue
                    })
            }
        };
    },
    parseHTML () {
        return [
            {
                tag: "span",
                getAttrs: (e)=>typeof e == "string" ? !1 : e.hasAttribute("data-background-color") ? {
                        stringValue: e.getAttribute("data-background-color")
                    } : !1
            }
        ];
    },
    renderHTML ({ HTMLAttributes: e }) {
        return [
            "span",
            e,
            0
        ];
    }
}), Kr = ie(qr, "string"), Jr = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Mark"].create({
    name: "textColor",
    addAttributes () {
        return {
            stringValue: {
                default: void 0,
                parseHTML: (e)=>e.getAttribute("data-text-color"),
                renderHTML: (e)=>({
                        "data-text-color": e.stringValue
                    })
            }
        };
    },
    parseHTML () {
        return [
            {
                tag: "span",
                getAttrs: (e)=>typeof e == "string" ? !1 : e.hasAttribute("data-text-color") ? {
                        stringValue: e.getAttribute("data-text-color")
                    } : !1
            }
        ];
    },
    renderHTML ({ HTMLAttributes: e }) {
        return [
            "span",
            e,
            0
        ];
    }
}), Xr = ie(Jr, "string"), Wt = (e)=>({
        url: e.src || void 0
    }), Yr = {
    backgroundColor: T.backgroundColor,
    // File name.
    name: {
        default: ""
    },
    // File url.
    url: {
        default: ""
    },
    // File caption.
    caption: {
        default: ""
    }
}, Zr = {
    type: "file",
    propSchema: Yr,
    content: "none",
    isFileBlock: !0
}, Qr = (e, n)=>Et(e, n), es = (e)=>{
    if (e.tagName === "EMBED") return e.closest("figure") ? void 0 : Wt(e);
    if (e.tagName === "FIGURE") {
        const n = Je(e, "embed");
        if (!n) return;
        const { targetElement: t, caption: o } = n;
        return {
            ...Wt(t),
            caption: o
        };
    }
}, ts = (e)=>{
    if (!e.props.url) {
        const t = document.createElement("p");
        return t.textContent = "Add file", {
            dom: t
        };
    }
    const n = document.createElement("a");
    return n.href = e.props.url, n.textContent = e.props.name || e.props.url, e.props.caption ? Xe(n, e.props.caption) : {
        dom: n
    };
}, ns = xe(Zr, {
    render: Qr,
    parse: es,
    toExternalHTML: ts
}), An = {
    ...T,
    level: {
        default: 1,
        values: [
            1,
            2,
            3
        ]
    }
}, os = q({
    name: "heading",
    content: "inline*",
    group: "blockContent",
    addAttributes () {
        return Be(An);
    },
    addInputRules () {
        return [
            ...[
                1,
                2,
                3
            ].map((e)=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputRule"]({
                    find: new RegExp(`^(#{${e}})\\s$`),
                    handler: ({ state: n, chain: t, range: o })=>{
                        const r = v(n);
                        !r.isBlockContainer || r.blockContent.node.type.spec.content !== "inline*" || t().command(I(r.bnBlock.beforePos, {
                            type: "heading",
                            props: {
                                level: e
                            }
                        })).deleteRange({
                            from: o.from,
                            to: o.to
                        }).run();
                    }
                }))
        ];
    },
    addKeyboardShortcuts () {
        return {
            "Mod-Alt-1": ()=>{
                const e = v(this.editor.state);
                return !e.isBlockContainer || e.blockContent.node.type.spec.content !== "inline*" ? !0 : this.editor.commands.command(I(e.bnBlock.beforePos, {
                    type: "heading",
                    props: {
                        level: 1
                    }
                }));
            },
            "Mod-Alt-2": ()=>{
                const e = v(this.editor.state);
                return !e.isBlockContainer || e.blockContent.node.type.spec.content !== "inline*" ? !0 : this.editor.commands.command(I(e.bnBlock.beforePos, {
                    type: "heading",
                    props: {
                        level: 2
                    }
                }));
            },
            "Mod-Alt-3": ()=>{
                const e = v(this.editor.state);
                return !e.isBlockContainer || e.blockContent.node.type.spec.content !== "inline*" ? !0 : this.editor.commands.command(I(e.bnBlock.beforePos, {
                    type: "heading",
                    props: {
                        level: 3
                    }
                }));
            }
        };
    },
    parseHTML () {
        return [
            // Parse from internal HTML.
            {
                tag: "div[data-content-type=" + this.name + "]",
                contentElement: ".bn-inline-content"
            },
            // Parse from external HTML.
            {
                tag: "h1",
                attrs: {
                    level: 1
                },
                node: "heading"
            },
            {
                tag: "h2",
                attrs: {
                    level: 2
                },
                node: "heading"
            },
            {
                tag: "h3",
                attrs: {
                    level: 3
                },
                node: "heading"
            }
        ];
    },
    renderHTML ({ node: e, HTMLAttributes: n }) {
        var t, o;
        return G(this.name, `h${e.attrs.level}`, {
            ...((t = this.options.domAttributes) == null ? void 0 : t.blockContent) || {},
            ...n
        }, ((o = this.options.domAttributes) == null ? void 0 : o.inlineContent) || {});
    }
}), rs = oe(os, An), Nn = (e, n, t, o, r, s)=>{
    const { dom: i, destroy: a } = Et(e, n, t, r, s), c = i;
    e.props.url && e.props.showPreview && (e.props.previewWidth ? c.style.width = `${e.props.previewWidth}px` : c.style.width = "fit-content");
    const l = document.createElement("div");
    l.className = "bn-resize-handle", l.style.left = "4px";
    const d = document.createElement("div");
    d.className = "bn-resize-handle", d.style.right = "4px";
    let u, h = e.props.previewWidth;
    const f = (C)=>{
        var $, S;
        if (!u) {
            !n.isEditable && o.contains(l) && o.contains(d) && (o.removeChild(l), o.removeChild(d));
            return;
        }
        let B;
        e.props.textAlignment === "center" ? u.handleUsed === "left" ? B = u.initialWidth + (u.initialClientX - C.clientX) * 2 : B = u.initialWidth + (C.clientX - u.initialClientX) * 2 : u.handleUsed === "left" ? B = u.initialWidth + u.initialClientX - C.clientX : B = u.initialWidth + C.clientX - u.initialClientX, h = Math.min(Math.max(B, 64), ((S = ($ = n.domElement) == null ? void 0 : $.firstElementChild) == null ? void 0 : S.clientWidth) || Number.MAX_VALUE), c.style.width = `${h}px`;
    }, m = (C)=>{
        (!C.target || !c.contains(C.target) || !n.isEditable) && o.contains(l) && o.contains(d) && (o.removeChild(l), o.removeChild(d)), u && (u = void 0, n.updateBlock(e, {
            props: {
                previewWidth: h
            }
        }));
    }, g = ()=>{
        n.isEditable && (o.appendChild(l), o.appendChild(d));
    }, b = (C)=>{
        C.relatedTarget === l || C.relatedTarget === d || u || n.isEditable && o.contains(l) && o.contains(d) && (o.removeChild(l), o.removeChild(d));
    }, k = (C)=>{
        C.preventDefault(), u = {
            handleUsed: "left",
            initialWidth: c.clientWidth,
            initialClientX: C.clientX
        };
    }, w = (C)=>{
        C.preventDefault(), u = {
            handleUsed: "right",
            initialWidth: c.clientWidth,
            initialClientX: C.clientX
        };
    };
    return window.addEventListener("mousemove", f), window.addEventListener("mouseup", m), c.addEventListener("mouseenter", g), c.addEventListener("mouseleave", b), l.addEventListener("mousedown", k), d.addEventListener("mousedown", w), {
        dom: c,
        destroy: ()=>{
            a == null || a(), window.removeEventListener("mousemove", f), window.removeEventListener("mouseup", m), c.removeEventListener("mouseenter", g), c.removeEventListener("mouseleave", b), l.removeEventListener("mousedown", k), d.removeEventListener("mousedown", w);
        }
    };
}, jt = (e)=>{
    const n = e.src || void 0, t = e.width || void 0;
    return {
        url: n,
        previewWidth: t
    };
}, ss = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M5 11.1005L7 9.1005L12.5 14.6005L16 11.1005L19 14.1005V5H5V11.1005ZM4 3H20C20.5523 3 21 3.44772 21 4V20C21 20.5523 20.5523 21 20 21H4C3.44772 21 3 20.5523 3 20V4C3 3.44772 3.44772 3 4 3ZM15.5 10C14.6716 10 14 9.32843 14 8.5C14 7.67157 14.6716 7 15.5 7C16.3284 7 17 7.67157 17 8.5C17 9.32843 16.3284 10 15.5 10Z"></path></svg>', is = {
    textAlignment: T.textAlignment,
    backgroundColor: T.backgroundColor,
    // File name.
    name: {
        default: ""
    },
    // File url.
    url: {
        default: ""
    },
    // File caption.
    caption: {
        default: ""
    },
    showPreview: {
        default: !0
    },
    // File preview width in px.
    previewWidth: {
        default: void 0,
        type: "number"
    }
}, as = {
    type: "image",
    propSchema: is,
    content: "none",
    isFileBlock: !0,
    fileBlockAccept: [
        "image/*"
    ]
}, cs = (e, n)=>{
    const t = document.createElement("div");
    t.innerHTML = ss;
    const o = document.createElement("div");
    o.className = "bn-visual-media-wrapper";
    const r = document.createElement("img");
    return r.className = "bn-visual-media", n.resolveFileUrl ? n.resolveFileUrl(e.props.url).then((s)=>{
        r.src = s;
    }) : r.src = e.props.url, r.alt = e.props.name || e.props.caption || "BlockNote image", r.contentEditable = "false", r.draggable = !1, o.appendChild(r), Nn(e, n, {
        dom: o
    }, o, n.dictionary.file_blocks.image.add_button_text, t.firstElementChild);
}, ls = (e)=>{
    if (e.tagName === "IMG") return e.closest("figure") ? void 0 : jt(e);
    if (e.tagName === "FIGURE") {
        const n = Je(e, "img");
        if (!n) return;
        const { targetElement: t, caption: o } = n;
        return {
            ...jt(t),
            caption: o
        };
    }
}, ds = (e)=>{
    if (!e.props.url) {
        const t = document.createElement("p");
        return t.textContent = "Add image", {
            dom: t
        };
    }
    let n;
    return e.props.showPreview ? (n = document.createElement("img"), n.src = e.props.url, n.alt = e.props.name || e.props.caption || "BlockNote image", e.props.previewWidth && (n.width = e.props.previewWidth)) : (n = document.createElement("a"), n.href = e.props.url, n.textContent = e.props.name || e.props.url), e.props.caption ? e.props.showPreview ? St(n, e.props.caption) : Xe(n, e.props.caption) : {
        dom: n
    };
}, us = xe(as, {
    render: cs,
    parse: ls,
    toExternalHTML: ds
});
function Bt(e, n, t) {
    var u, h, f;
    const o = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DOMParser"].fromSchema(n), r = e, s = document.createElement("div");
    s.setAttribute("data-node-type", "blockGroup");
    for (const m of Array.from(r.childNodes))s.appendChild(m.cloneNode(!0));
    let i = o.parse(s, {
        topNode: n.nodes.blockGroup.create()
    });
    ((h = (u = i.firstChild) == null ? void 0 : u.firstChild) == null ? void 0 : h.type.name) === "checkListItem" && (i = i.copy(i.content.cut(i.firstChild.firstChild.nodeSize + 2)));
    const a = (f = i.firstChild) == null ? void 0 : f.firstChild;
    if (!(a != null && a.isTextblock)) return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"].from(i);
    const c = n.nodes[t].create({}, a.content), l = i.content.cut(// +2 for the `blockGroup` node's start and end markers
    a.nodeSize + 2);
    if (l.size > 0) {
        const m = i.copy(l);
        return c.content.addToEnd(m);
    }
    return c.content;
}
const Hn = (e, n, t)=>({ state: o, dispatch: r })=>{
        const s = X(o.doc, e), i = ee(s);
        if (!i.isBlockContainer) throw new Error(`BlockContainer expected when calling splitBlock, position ${e}`);
        const a = [
            {
                type: i.bnBlock.node.type,
                // always keep blockcontainer type
                attrs: t ? {
                    ...i.bnBlock.node.attrs,
                    id: void 0
                } : {}
            },
            {
                type: n ? i.blockContent.node.type : o.schema.nodes.paragraph,
                attrs: t ? {
                    ...i.blockContent.node.attrs
                } : {}
            }
        ];
        return r && o.tr.split(e, 2, a), !0;
    }, xt = (e)=>{
    const { blockInfo: n, selectionEmpty: t } = e.transact((s)=>({
            blockInfo: je(s),
            selectionEmpty: s.selection.anchor === s.selection.head
        }));
    if (!n.isBlockContainer) return !1;
    const { bnBlock: o, blockContent: r } = n;
    return !(r.node.type.name === "bulletListItem" || r.node.type.name === "numberedListItem" || r.node.type.name === "checkListItem") || !t ? !1 : e._tiptapEditor.commands.first(({ state: s, chain: i, commands: a })=>[
            ()=>// Changes list item block to a paragraph block if the content is empty.
                a.command(()=>r.node.childCount === 0 ? a.command(I(o.beforePos, {
                        type: "paragraph",
                        props: {}
                    })) : !1),
            ()=>// Splits the current block, moving content inside that's after the cursor
                // to a new block of the same type below.
                a.command(()=>r.node.childCount > 0 ? (i().deleteSelection().command(Hn(s.selection.from, !0)).run(), !0) : !1)
        ]);
}, ps = {
    ...T
}, hs = q({
    name: "bulletListItem",
    content: "inline*",
    group: "blockContent",
    // This is to make sure that check list parse rules run before, since they
    // both parse `li` elements but check lists are more specific.
    priority: 90,
    addInputRules () {
        return [
            // Creates an unordered list when starting with "-", "+", or "*".
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputRule"]({
                find: new RegExp("^[-+*]\\s$"),
                handler: ({ state: e, chain: n, range: t })=>{
                    const o = v(e);
                    !o.isBlockContainer || o.blockContent.node.type.spec.content !== "inline*" || n().command(I(o.bnBlock.beforePos, {
                        type: "bulletListItem",
                        props: {}
                    })).deleteRange({
                        from: t.from,
                        to: t.to
                    });
                }
            })
        ];
    },
    addKeyboardShortcuts () {
        return {
            Enter: ()=>xt(this.options.editor),
            "Mod-Shift-8": ()=>{
                const e = v(this.editor.state);
                return !e.isBlockContainer || e.blockContent.node.type.spec.content !== "inline*" ? !0 : this.editor.commands.command(I(e.bnBlock.beforePos, {
                    type: "bulletListItem",
                    props: {}
                }));
            }
        };
    },
    parseHTML () {
        return [
            // Parse from internal HTML.
            {
                tag: "div[data-content-type=" + this.name + "]",
                contentElement: ".bn-inline-content"
            },
            // Parse from external HTML.
            {
                tag: "li",
                getAttrs: (e)=>{
                    var t;
                    if (typeof e == "string") return !1;
                    const n = e.parentElement;
                    return n === null ? !1 : n.tagName === "UL" || n.tagName === "DIV" && ((t = n.parentElement) == null ? void 0 : t.tagName) === "UL" ? {} : !1;
                },
                // As `li` elements can contain multiple paragraphs, we need to merge their contents
                // into a single one so that ProseMirror can parse everything correctly.
                getContent: (e, n)=>Bt(e, n, this.name),
                node: "bulletListItem"
            }
        ];
    },
    renderHTML ({ HTMLAttributes: e }) {
        var n, t;
        return G(this.name, // We use a <p> tag, because for <li> tags we'd need a <ul> element to put
        // them in to be semantically correct, which we can't have due to the
        // schema.
        "p", {
            ...((n = this.options.domAttributes) == null ? void 0 : n.blockContent) || {},
            ...e
        }, ((t = this.options.domAttributes) == null ? void 0 : t.inlineContent) || {});
    }
}), fs = oe(hs, ps), Dn = {
    ...T,
    checked: {
        default: !1
    }
}, ms = q({
    name: "checkListItem",
    content: "inline*",
    group: "blockContent",
    addAttributes () {
        return Be(Dn);
    },
    addInputRules () {
        return [
            // Creates a checklist when starting with "[]" or "[X]".
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputRule"]({
                find: new RegExp("\\[\\s*\\]\\s$"),
                handler: ({ state: e, chain: n, range: t })=>{
                    const o = v(e);
                    !o.isBlockContainer || o.blockContent.node.type.spec.content !== "inline*" || n().command(I(o.bnBlock.beforePos, {
                        type: "checkListItem",
                        props: {
                            checked: !1
                        }
                    })).deleteRange({
                        from: t.from,
                        to: t.to
                    });
                }
            }),
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputRule"]({
                find: new RegExp("\\[[Xx]\\]\\s$"),
                handler: ({ state: e, chain: n, range: t })=>{
                    const o = v(e);
                    !o.isBlockContainer || o.blockContent.node.type.spec.content !== "inline*" || n().command(I(o.bnBlock.beforePos, {
                        type: "checkListItem",
                        props: {
                            checked: !0
                        }
                    })).deleteRange({
                        from: t.from,
                        to: t.to
                    });
                }
            })
        ];
    },
    addKeyboardShortcuts () {
        return {
            Enter: ()=>xt(this.options.editor),
            "Mod-Shift-9": ()=>{
                const e = v(this.editor.state);
                return !e.isBlockContainer || e.blockContent.node.type.spec.content !== "inline*" ? !0 : this.editor.commands.command(I(e.bnBlock.beforePos, {
                    type: "checkListItem",
                    props: {}
                }));
            }
        };
    },
    parseHTML () {
        return [
            // Parse from internal HTML.
            {
                tag: "div[data-content-type=" + this.name + "]",
                contentElement: ".bn-inline-content"
            },
            // Parse from external HTML.
            {
                tag: "input",
                getAttrs: (e)=>typeof e == "string" || e.closest("[data-content-type]") || e.closest("li") ? !1 : e.type === "checkbox" ? {
                        checked: e.checked
                    } : !1,
                node: "checkListItem"
            },
            {
                tag: "li",
                getAttrs: (e)=>{
                    var t;
                    if (typeof e == "string") return !1;
                    const n = e.parentElement;
                    if (n === null) return !1;
                    if (n.tagName === "UL" || n.tagName === "DIV" && ((t = n.parentElement) == null ? void 0 : t.tagName) === "UL") {
                        const o = e.querySelector("input[type=checkbox]") || null;
                        return o === null ? !1 : {
                            checked: o.checked
                        };
                    }
                    return !1;
                },
                // As `li` elements can contain multiple paragraphs, we need to merge their contents
                // into a single one so that ProseMirror can parse everything correctly.
                getContent: (e, n)=>Bt(e, n, this.name),
                node: "checkListItem"
            }
        ];
    },
    // Since there is no HTML checklist element, there isn't really any
    // standardization for what checklists should look like in the DOM. GDocs'
    // and Notion's aren't cross compatible, for example. This implementation
    // has a semantically correct DOM structure (though missing a label for the
    // checkbox) which is also converted correctly to Markdown by remark.
    renderHTML ({ node: e, HTMLAttributes: n }) {
        var s, i;
        const t = document.createElement("input");
        t.type = "checkbox", t.checked = e.attrs.checked, e.attrs.checked && t.setAttribute("checked", "");
        const { dom: o, contentDOM: r } = G(this.name, "p", {
            ...((s = this.options.domAttributes) == null ? void 0 : s.blockContent) || {},
            ...n
        }, ((i = this.options.domAttributes) == null ? void 0 : i.inlineContent) || {});
        return o.insertBefore(t, r), {
            dom: o,
            contentDOM: r
        };
    },
    // Need to render node view since the checkbox needs to be able to update the
    // node. This is only possible with a node view as it exposes `getPos`.
    addNodeView () {
        return ({ node: e, getPos: n, editor: t, HTMLAttributes: o })=>{
            var d, u;
            const r = document.createElement("div"), s = document.createElement("div");
            s.contentEditable = "false";
            const i = document.createElement("input");
            i.type = "checkbox", i.checked = e.attrs.checked, e.attrs.checked && i.setAttribute("checked", "");
            const a = ()=>{
                if (!t.isEditable) {
                    i.checked = !i.checked;
                    return;
                }
                if (typeof n != "boolean") {
                    const h = X(t.state.doc, n());
                    if (h.node.type.name !== "blockContainer") throw new Error(`Expected blockContainer node, got ${h.node.type.name}`);
                    this.editor.commands.command(I(h.posBeforeNode, {
                        type: "checkListItem",
                        props: {
                            checked: i.checked
                        }
                    }));
                }
            };
            i.addEventListener("change", a);
            const { dom: c, contentDOM: l } = G(this.name, "p", {
                ...((d = this.options.domAttributes) == null ? void 0 : d.blockContent) || {},
                ...o
            }, ((u = this.options.domAttributes) == null ? void 0 : u.inlineContent) || {});
            if (typeof n != "boolean") {
                const f = "label-" + this.editor.state.doc.resolve(n()).node().attrs.id;
                i.setAttribute("aria-labelledby", f), l.id = f;
            }
            return c.removeChild(l), c.appendChild(r), r.appendChild(s), r.appendChild(l), s.appendChild(i), {
                dom: c,
                contentDOM: l,
                destroy: ()=>{
                    i.removeEventListener("change", a);
                }
            };
        };
    }
}), gs = oe(ms, Dn), bs = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PluginKey"]("numbered-list-indexing"), ks = ()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Plugin"]({
        key: bs,
        appendTransaction: (e, n, t)=>{
            const o = t.tr;
            o.setMeta("numberedListIndexing", !0);
            let r = !1;
            return t.doc.descendants((s, i)=>{
                var a;
                if (s.type.name === "blockContainer" && s.firstChild.type.name === "numberedListItem") {
                    let c = `${s.firstChild.attrs.start || 1}`;
                    const l = ee({
                        posBeforeNode: i,
                        node: s
                    });
                    if (!l.isBlockContainer) throw new Error("impossible");
                    const d = o.doc.resolve(l.bnBlock.beforePos).nodeBefore;
                    if (d) {
                        const m = ee({
                            posBeforeNode: l.bnBlock.beforePos - d.nodeSize,
                            node: d
                        });
                        if (m.blockNoteType === "numberedListItem") {
                            if (!m.isBlockContainer) throw new Error("impossible");
                            const b = m.blockContent.node.attrs.index;
                            c = (parseInt(b) + 1).toString();
                        }
                    }
                    const u = l.blockContent.node, h = u.attrs.index, f = ((a = d == null ? void 0 : d.firstChild) == null ? void 0 : a.type.name) !== "numberedListItem";
                    if (h !== c || u.attrs.start && !f) {
                        r = !0;
                        const { start: m, ...g } = u.attrs;
                        o.setNodeMarkup(l.blockContent.beforePos, void 0, {
                            ...g,
                            index: c,
                            ...typeof m == "number" && f && {
                                start: m
                            }
                        });
                    }
                }
            }), r ? o : null;
        }
    }), On = {
    ...T,
    start: {
        default: void 0,
        type: "number"
    }
}, ws = q({
    name: "numberedListItem",
    content: "inline*",
    group: "blockContent",
    priority: 90,
    addAttributes () {
        return {
            ...Be(On),
            // the index attribute is only used internally (it's not part of the blocknote schema)
            // that's why it's defined explicitly here, and not part of the prop schema
            index: {
                default: null,
                parseHTML: (e)=>e.getAttribute("data-index"),
                renderHTML: (e)=>({
                        "data-index": e.index
                    })
            }
        };
    },
    addInputRules () {
        return [
            // Creates an ordered list when starting with "1.".
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputRule"]({
                find: new RegExp("^(\\d+)\\.\\s$"),
                handler: ({ state: e, chain: n, range: t, match: o })=>{
                    const r = v(e);
                    if (!r.isBlockContainer || r.blockContent.node.type.spec.content !== "inline*" || r.blockNoteType === "numberedListItem") return;
                    const s = parseInt(o[1]);
                    n().command(I(r.bnBlock.beforePos, {
                        type: "numberedListItem",
                        props: s === 1 && {} || {
                            start: s
                        }
                    })).deleteRange({
                        from: t.from,
                        to: t.to
                    });
                }
            })
        ];
    },
    addKeyboardShortcuts () {
        return {
            Enter: ()=>xt(this.options.editor),
            "Mod-Shift-7": ()=>{
                const e = v(this.editor.state);
                return !e.isBlockContainer || e.blockContent.node.type.spec.content !== "inline*" ? !0 : this.editor.commands.command(I(e.bnBlock.beforePos, {
                    type: "numberedListItem",
                    props: {}
                }));
            }
        };
    },
    addProseMirrorPlugins () {
        return [
            ks()
        ];
    },
    parseHTML () {
        return [
            // Parse from internal HTML.
            {
                tag: "div[data-content-type=" + this.name + "]",
                contentElement: ".bn-inline-content"
            },
            // Parse from external HTML.
            {
                tag: "li",
                getAttrs: (e)=>{
                    var t;
                    if (typeof e == "string") return !1;
                    const n = e.parentElement;
                    if (n === null) return !1;
                    if (n.tagName === "OL" || n.tagName === "DIV" && ((t = n.parentElement) == null ? void 0 : t.tagName) === "OL") {
                        const o = parseInt(n.getAttribute("start") || "1") || 1;
                        return e.previousSibling || o === 1 ? {} : {
                            start: o
                        };
                    }
                    return !1;
                },
                // As `li` elements can contain multiple paragraphs, we need to merge their contents
                // into a single one so that ProseMirror can parse everything correctly.
                getContent: (e, n)=>Bt(e, n, this.name),
                priority: 300,
                node: "numberedListItem"
            }
        ];
    },
    renderHTML ({ HTMLAttributes: e }) {
        var n, t;
        return G(this.name, // We use a <p> tag, because for <li> tags we'd need an <ol> element to
        // put them in to be semantically correct, which we can't have due to the
        // schema.
        "p", {
            ...((n = this.options.domAttributes) == null ? void 0 : n.blockContent) || {},
            ...e
        }, ((t = this.options.domAttributes) == null ? void 0 : t.inlineContent) || {});
    }
}), ys = oe(ws, On), Cs = {
    ...T
}, vs = q({
    name: "paragraph",
    content: "inline*",
    group: "blockContent",
    addKeyboardShortcuts () {
        return {
            "Mod-Alt-0": ()=>{
                const e = v(this.editor.state);
                return !e.isBlockContainer || e.blockContent.node.type.spec.content !== "inline*" ? !0 : this.editor.commands.command(I(e.bnBlock.beforePos, {
                    type: "paragraph",
                    props: {}
                }));
            }
        };
    },
    parseHTML () {
        return [
            // Parse from internal HTML.
            {
                tag: "div[data-content-type=" + this.name + "]",
                contentElement: ".bn-inline-content"
            },
            // Parse from external HTML.
            {
                tag: "p",
                getAttrs: (e)=>{
                    var n;
                    return typeof e == "string" || !((n = e.textContent) != null && n.trim()) ? !1 : {};
                },
                node: "paragraph"
            }
        ];
    },
    renderHTML ({ HTMLAttributes: e }) {
        var n, t;
        return G(this.name, "p", {
            ...((n = this.options.domAttributes) == null ? void 0 : n.blockContent) || {},
            ...e
        }, ((t = this.options.domAttributes) == null ? void 0 : t.inlineContent) || {});
    }
}), Es = oe(vs, Cs), Ss = {
    ...T
}, Bs = q({
    name: "quote",
    content: "inline*",
    group: "blockContent",
    addInputRules () {
        return [
            // Creates a block quote when starting with ">".
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputRule"]({
                find: new RegExp("^>\\s$"),
                handler: ({ state: e, chain: n, range: t })=>{
                    const o = v(e);
                    !o.isBlockContainer || o.blockContent.node.type.spec.content !== "inline*" || n().command(I(o.bnBlock.beforePos, {
                        type: "quote",
                        props: {}
                    })).deleteRange({
                        from: t.from,
                        to: t.to
                    });
                }
            })
        ];
    },
    addKeyboardShortcuts () {
        return {
            "Mod-Alt-q": ()=>{
                const e = v(this.editor.state);
                return !e.isBlockContainer || e.blockContent.node.type.spec.content !== "inline*" ? !0 : this.editor.commands.command(I(e.bnBlock.beforePos, {
                    type: "quote"
                }));
            }
        };
    },
    parseHTML () {
        return [
            // Parse from internal HTML.
            {
                tag: "div[data-content-type=" + this.name + "]",
                contentElement: ".bn-inline-content"
            },
            // Parse from external HTML.
            {
                tag: "blockquote",
                node: "quote"
            }
        ];
    },
    renderHTML ({ HTMLAttributes: e }) {
        var n, t;
        return G(this.name, "blockquote", {
            ...((n = this.options.domAttributes) == null ? void 0 : n.blockContent) || {},
            ...e
        }, ((t = this.options.domAttributes) == null ? void 0 : t.inlineContent) || {});
    }
}), xs = oe(Bs, Ss), Ts = 35, Rn = 120, Ac = 31, Ms = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Extension"].create({
    name: "BlockNoteTableExtension",
    addProseMirrorPlugins: ()=>[
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["columnResizing"])({
                cellMinWidth: Ts,
                defaultCellMinWidth: Rn,
                // We set this to null as we implement our own node view in the table
                // block content. This node view is the same as what's used by default,
                // but is wrapped in a `blockContent` HTML element.
                View: null
            }),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tableEditing"])()
        ],
    addKeyboardShortcuts () {
        return {
            // Makes enter create a new line within the cell.
            Enter: ()=>this.editor.state.selection.empty && this.editor.state.selection.$head.parent.type.name === "tableParagraph" ? (this.editor.commands.insertContent({
                    type: "hardBreak"
                }), !0) : !1,
            // Ensures that backspace won't delete the table if the text cursor is at
            // the start of a cell and the selection is empty.
            Backspace: ()=>{
                const e = this.editor.state.selection, n = e.empty, t = e.$head.parentOffset === 0, o = e.$head.node().type.name === "tableParagraph";
                return n && t && o;
            },
            // Enables navigating cells using the tab key.
            Tab: ()=>this.editor.commands.command(({ state: e, dispatch: n, view: t })=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["goToNextCell"])(1)(e, n, t)),
            "Shift-Tab": ()=>this.editor.commands.command(({ state: e, dispatch: n, view: t })=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["goToNextCell"])(-1)(e, n, t))
        };
    },
    extendNodeSchema (e) {
        const n = {
            name: e.name,
            options: e.options,
            storage: e.storage
        };
        return {
            tableRole: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callOrReturn"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getExtensionField"])(e, "tableRole", n))
        };
    }
}), Ps = {
    textColor: T.textColor
}, Is = q({
    name: "table",
    content: "tableRow+",
    group: "blockContent",
    tableRole: "table",
    marks: "deletion insertion modification",
    isolating: !0,
    parseHTML () {
        return [
            {
                tag: "table"
            }
        ];
    },
    renderHTML ({ HTMLAttributes: e }) {
        var n, t;
        return G(this.name, "table", {
            ...((n = this.options.domAttributes) == null ? void 0 : n.blockContent) || {},
            ...e
        }, ((t = this.options.domAttributes) == null ? void 0 : t.inlineContent) || {});
    },
    // This node view is needed for the `columnResizing` plugin. By default, the
    // plugin adds its own node view, which overrides how the node is rendered vs
    // `renderHTML`. This means that the wrapping `blockContent` HTML element is
    // no longer rendered. The `columnResizing` plugin uses the `TableView` as its
    // default node view. `BlockNoteTableView` extends it by wrapping it in a
    // `blockContent` element, so the DOM structure is consistent with other block
    // types.
    addNodeView () {
        return ({ node: e, HTMLAttributes: n })=>{
            var o;
            class t extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableView"] {
                constructor(s, i, a){
                    super(s, i), this.node = s, this.cellMinWidth = i, this.blockContentHTMLAttributes = a;
                    const c = document.createElement("div");
                    c.className = Q("bn-block-content", a.class), c.setAttribute("data-content-type", "table");
                    for (const [h, f] of Object.entries(a))h !== "class" && c.setAttribute(h, f);
                    const l = this.dom, d = document.createElement("div");
                    d.className = "tableWrapper-inner", d.appendChild(l.firstChild), l.appendChild(d), c.appendChild(l);
                    const u = document.createElement("div");
                    u.className = "table-widgets-container", u.style.position = "relative", l.appendChild(u), this.dom = c;
                }
                ignoreMutation(s) {
                    return !s.target.closest(".tableWrapper-inner") || super.ignoreMutation(s);
                }
            }
            return new t(e, Rn, {
                ...((o = this.options.domAttributes) == null ? void 0 : o.blockContent) || {},
                ...n
            });
        };
    }
}), Ls = q({
    name: "tableParagraph",
    group: "tableContent",
    content: "inline*",
    parseHTML () {
        return [
            {
                tag: "p",
                getAttrs: (e)=>{
                    if (typeof e == "string" || !e.textContent || !e.closest("[data-content-type]")) return !1;
                    const n = e.parentElement;
                    return n === null ? !1 : n.tagName === "TD" || n.tagName === "TH" ? {} : !1;
                },
                node: "tableParagraph"
            }
        ];
    },
    renderHTML ({ HTMLAttributes: e }) {
        return [
            "p",
            e,
            0
        ];
    }
}), As = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Node"].create({
    name: "tableRow",
    addOptions () {
        return {
            HTMLAttributes: {}
        };
    },
    content: "(tableCell | tableHeader)+",
    tableRole: "row",
    marks: "deletion insertion modification",
    parseHTML () {
        return [
            {
                tag: "tr"
            }
        ];
    },
    renderHTML ({ HTMLAttributes: e }) {
        return [
            "tr",
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, e),
            0
        ];
    }
});
function Gt(e, n) {
    const o = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DOMParser"].fromSchema(n).parse(e, {
        topNode: n.nodes.blockGroup.create()
    }), r = [];
    return o.content.descendants((s)=>{
        if (s.isInline) return r.push(s), !1;
    }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"].fromArray(r);
}
const Ns = oe(Is, Ps, [
    Ms,
    Ls,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$table$2d$header$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHeader"].extend({
        /**
       * We allow table headers and cells to have multiple tableContent nodes because
       * when merging cells, prosemirror-tables will concat the contents of the cells naively.
       * This would cause that content to overflow into other cells when prosemirror tries to enforce the cell structure.
       *
       * So, we manually fix this up when reading back in the `nodeToBlock` and only ever place a single tableContent back into the cell.
       */ content: "tableContent+",
        parseHTML () {
            return [
                {
                    tag: "th",
                    // As `th` elements can contain multiple paragraphs, we need to merge their contents
                    // into a single one so that ProseMirror can parse everything correctly.
                    getContent: (e, n)=>Gt(e, n)
                }
            ];
        }
    }),
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$table$2d$cell$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"].extend({
        content: "tableContent+",
        parseHTML () {
            return [
                {
                    tag: "td",
                    // As `td` elements can contain multiple paragraphs, we need to merge their contents
                    // into a single one so that ProseMirror can parse everything correctly.
                    getContent: (e, n)=>Gt(e, n)
                }
            ];
        }
    }),
    As
]), qt = (e)=>{
    const n = e.src || void 0, t = e.width || void 0;
    return {
        url: n,
        previewWidth: t
    };
}, Hs = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M2 3.9934C2 3.44476 2.45531 3 2.9918 3H21.0082C21.556 3 22 3.44495 22 3.9934V20.0066C22 20.5552 21.5447 21 21.0082 21H2.9918C2.44405 21 2 20.5551 2 20.0066V3.9934ZM8 5V19H16V5H8ZM4 5V7H6V5H4ZM18 5V7H20V5H18ZM4 9V11H6V9H4ZM18 9V11H20V9H18ZM4 13V15H6V13H4ZM18 13V15H20V13H18ZM4 17V19H6V17H4ZM18 17V19H20V17H18Z"></path></svg>', Ds = {
    textAlignment: T.textAlignment,
    backgroundColor: T.backgroundColor,
    // File name.
    name: {
        default: ""
    },
    // File url.
    url: {
        default: ""
    },
    // File caption.
    caption: {
        default: ""
    },
    showPreview: {
        default: !0
    },
    // File preview width in px.
    previewWidth: {
        default: void 0,
        type: "number"
    }
}, Os = {
    type: "video",
    propSchema: Ds,
    content: "none",
    isFileBlock: !0,
    fileBlockAccept: [
        "video/*"
    ]
}, Rs = (e, n)=>{
    const t = document.createElement("div");
    t.innerHTML = Hs;
    const o = document.createElement("div");
    o.className = "bn-visual-media-wrapper";
    const r = document.createElement("video");
    return r.className = "bn-visual-media", n.resolveFileUrl ? n.resolveFileUrl(e.props.url).then((s)=>{
        r.src = s;
    }) : r.src = e.props.url, r.controls = !0, r.contentEditable = "false", r.draggable = !1, r.width = e.props.previewWidth, o.appendChild(r), Nn(e, n, {
        dom: o
    }, o, n.dictionary.file_blocks.video.add_button_text, t.firstElementChild);
}, Vs = (e)=>{
    if (e.tagName === "VIDEO") return e.closest("figure") ? void 0 : qt(e);
    if (e.tagName === "FIGURE") {
        const n = Je(e, "video");
        if (!n) return;
        const { targetElement: t, caption: o } = n;
        return {
            ...qt(t),
            caption: o
        };
    }
}, Us = (e)=>{
    if (!e.props.url) {
        const t = document.createElement("p");
        return t.textContent = "Add video", {
            dom: t
        };
    }
    let n;
    return e.props.showPreview ? (n = document.createElement("video"), n.src = e.props.url, e.props.previewWidth && (n.width = e.props.previewWidth)) : (n = document.createElement("a"), n.href = e.props.url, n.textContent = e.props.name || e.props.url), e.props.caption ? e.props.showPreview ? St(n, e.props.caption) : Xe(n, e.props.caption) : {
        dom: n
    };
}, _s = xe(Os, {
    render: Rs,
    parse: Vs,
    toExternalHTML: Us
}), Vn = {
    paragraph: Es,
    heading: rs,
    quote: xs,
    codeBlock: Gr,
    bulletListItem: fs,
    numberedListItem: ys,
    checkListItem: gs,
    table: Ns,
    file: ns,
    image: us,
    video: _s,
    audio: zr
}, $s = gn(Vn), Un = {
    bold: ie(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$bold$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], "boolean"),
    italic: ie(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$italic$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], "boolean"),
    underline: ie(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$underline$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], "boolean"),
    strike: ie(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$strike$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], "boolean"),
    code: ie(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$code$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], "boolean"),
    textColor: Xr,
    backgroundColor: Kr
}, Nc = Cn(Un), _n = {
    text: {
        config: "text",
        implementation: {}
    },
    link: {
        config: "link",
        implementation: {}
    }
}, Fs = wn(_n);
function O(e, n) {
    return e in n.schema.blockSchema && n.schema.blockSchema[e] === $s[e];
}
function zs(e, n) {
    return e in n.schema.inlineContentSchema && n.schema.inlineContentSchema[e] === Fs[e];
}
function Ws(e, n, t) {
    return n.type === e && n.type in t.schema.blockSchema && O(n.type, t);
}
function Hc(e, n) {
    return e.type in n.schema.blockSchema && n.schema.blockSchema[e.type].isFileBlock || !1;
}
function Dc(e, n) {
    return e.type in n.schema.blockSchema && n.schema.blockSchema[e.type].isFileBlock && "showPreview" in n.schema.blockSchema[e.type].propSchema || !1;
}
function Oc(e, n) {
    return n.schema.blockSchema[e.type].isFileBlock && !e.props.url;
}
function js(e, n, t) {
    return n in t.schema.blockSchema && e in t.schema.blockSchema[n].propSchema && t.schema.blockSchema[n].propSchema[e] === T[e];
}
function Rc(e, n, t) {
    return js(e, n.type, t);
}
function Kt(e) {
    return e instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CellSelection"];
}
const Vc = async (e)=>{
    const n = new FormData();
    return n.append("file", e), (await (await fetch("https://tmpfiles.org/api/v1/upload", {
        method: "POST",
        body: n
    })).json()).data.url.replace("tmpfiles.org/", "tmpfiles.org/dl/");
};
function Gs(e) {
    let n = e.getTextCursorPosition().block, t = e.schema.blockSchema[n.type].content;
    for(; t === "none";){
        if (n = e.getTextCursorPosition().nextBlock, n === void 0) return;
        t = e.schema.blockSchema[n.type].content, e.setTextCursorPosition(n, "end");
    }
}
function N(e, n) {
    const t = e.getTextCursorPosition().block;
    if (t.content === void 0) throw new Error("Slash Menu open in a block that doesn't contain content.");
    let o;
    return Array.isArray(t.content) && (t.content.length === 1 && de(t.content[0]) && t.content[0].type === "text" && t.content[0].text === "/" || t.content.length === 0) ? (o = e.updateBlock(t, n), e.setTextCursorPosition(o)) : (o = e.insertBlocks([
        n
    ], t, "after")[0], e.setTextCursorPosition(e.getTextCursorPosition().nextBlock)), Gs(e), o;
}
function Uc(e) {
    const n = [];
    return O("heading", e) && n.push({
        onItemClick: ()=>{
            N(e, {
                type: "heading",
                props: {
                    level: 1
                }
            });
        },
        badge: Z("Mod-Alt-1"),
        key: "heading",
        ...e.dictionary.slash_menu.heading
    }, {
        onItemClick: ()=>{
            N(e, {
                type: "heading",
                props: {
                    level: 2
                }
            });
        },
        badge: Z("Mod-Alt-2"),
        key: "heading_2",
        ...e.dictionary.slash_menu.heading_2
    }, {
        onItemClick: ()=>{
            N(e, {
                type: "heading",
                props: {
                    level: 3
                }
            });
        },
        badge: Z("Mod-Alt-3"),
        key: "heading_3",
        ...e.dictionary.slash_menu.heading_3
    }), O("quote", e) && n.push({
        onItemClick: ()=>{
            N(e, {
                type: "quote"
            });
        },
        key: "quote",
        ...e.dictionary.slash_menu.quote
    }), O("numberedListItem", e) && n.push({
        onItemClick: ()=>{
            N(e, {
                type: "numberedListItem"
            });
        },
        badge: Z("Mod-Shift-7"),
        key: "numbered_list",
        ...e.dictionary.slash_menu.numbered_list
    }), O("bulletListItem", e) && n.push({
        onItemClick: ()=>{
            N(e, {
                type: "bulletListItem"
            });
        },
        badge: Z("Mod-Shift-8"),
        key: "bullet_list",
        ...e.dictionary.slash_menu.bullet_list
    }), O("checkListItem", e) && n.push({
        onItemClick: ()=>{
            N(e, {
                type: "checkListItem"
            });
        },
        badge: Z("Mod-Shift-9"),
        key: "check_list",
        ...e.dictionary.slash_menu.check_list
    }), O("paragraph", e) && n.push({
        onItemClick: ()=>{
            N(e, {
                type: "paragraph"
            });
        },
        badge: Z("Mod-Alt-0"),
        key: "paragraph",
        ...e.dictionary.slash_menu.paragraph
    }), O("codeBlock", e) && n.push({
        onItemClick: ()=>{
            N(e, {
                type: "codeBlock"
            });
        },
        badge: Z("Mod-Alt-c"),
        key: "code_block",
        ...e.dictionary.slash_menu.code_block
    }), O("table", e) && n.push({
        onItemClick: ()=>{
            N(e, {
                type: "table",
                content: {
                    type: "tableContent",
                    rows: [
                        {
                            cells: [
                                "",
                                "",
                                ""
                            ]
                        },
                        {
                            cells: [
                                "",
                                "",
                                ""
                            ]
                        }
                    ]
                }
            });
        },
        badge: void 0,
        key: "table",
        ...e.dictionary.slash_menu.table
    }), O("image", e) && n.push({
        onItemClick: ()=>{
            const t = N(e, {
                type: "image"
            });
            e.transact((o)=>o.setMeta(e.filePanel.plugins[0], {
                    block: t
                }));
        },
        key: "image",
        ...e.dictionary.slash_menu.image
    }), O("video", e) && n.push({
        onItemClick: ()=>{
            const t = N(e, {
                type: "video"
            });
            e.transact((o)=>o.setMeta(e.filePanel.plugins[0], {
                    block: t
                }));
        },
        key: "video",
        ...e.dictionary.slash_menu.video
    }), O("audio", e) && n.push({
        onItemClick: ()=>{
            const t = N(e, {
                type: "audio"
            });
            e.transact((o)=>o.setMeta(e.filePanel.plugins[0], {
                    block: t
                }));
        },
        key: "audio",
        ...e.dictionary.slash_menu.audio
    }), O("file", e) && n.push({
        onItemClick: ()=>{
            const t = N(e, {
                type: "file"
            });
            e.transact((o)=>o.setMeta(e.filePanel.plugins[0], {
                    block: t
                }));
        },
        key: "file",
        ...e.dictionary.slash_menu.file
    }), n.push({
        onItemClick: ()=>{
            e.openSuggestionMenu(":", {
                deleteTriggerCharacter: !0,
                ignoreQueryLength: !0
            });
        },
        key: "emoji",
        ...e.dictionary.slash_menu.emoji
    }), n;
}
function _c(e, n) {
    return e.filter(({ title: t, aliases: o })=>t.toLowerCase().includes(n.toLowerCase()) || o && o.filter((r)=>r.toLowerCase().includes(n.toLowerCase())).length !== 0);
}
function Qe(e) {
    return e && Object.fromEntries(Object.entries(e).filter(([, n])=>n !== void 0));
}
class Pe {
    constructor(n){
        p(this, "blockSpecs");
        p(this, "inlineContentSpecs");
        p(this, "styleSpecs");
        p(this, "blockSchema");
        p(this, "inlineContentSchema");
        p(this, "styleSchema");
        // Helper so that you can use typeof schema.BlockNoteEditor
        p(this, "BlockNoteEditor", "only for types");
        p(this, "Block", "only for types");
        p(this, "PartialBlock", "only for types");
        this.blockSpecs = Qe(n == null ? void 0 : n.blockSpecs) || Vn, this.inlineContentSpecs = Qe(n == null ? void 0 : n.inlineContentSpecs) || _n, this.styleSpecs = Qe(n == null ? void 0 : n.styleSpecs) || Un, this.blockSchema = gn(this.blockSpecs), this.inlineContentSchema = wn(this.inlineContentSpecs), this.styleSchema = Cn(this.styleSpecs);
    }
    static create(n) {
        return new Pe(n);
    }
}
const qs = {
    type: "pageBreak",
    propSchema: {},
    content: "none",
    isFileBlock: !1,
    isSelectable: !1
}, Ks = ()=>{
    const e = document.createElement("div");
    return e.className = "bn-page-break", e.setAttribute("data-page-break", ""), {
        dom: e
    };
}, Js = (e)=>{
    if (e.tagName === "DIV" && e.hasAttribute("data-page-break")) return {
        type: "pageBreak"
    };
}, Xs = ()=>{
    const e = document.createElement("div");
    return e.setAttribute("data-page-break", ""), {
        dom: e
    };
}, Ys = xe(qs, {
    render: Ks,
    parse: Js,
    toExternalHTML: Xs
}), $n = Pe.create({
    blockSpecs: {
        pageBreak: Ys
    }
}), $c = (e)=>Pe.create({
        blockSpecs: {
            ...e.blockSpecs,
            ...$n.blockSpecs
        },
        inlineContentSpecs: e.inlineContentSpecs,
        styleSpecs: e.styleSpecs
    });
function Zs(e) {
    return "pageBreak" in e.schema.blockSchema && e.schema.blockSchema.pageBreak === $n.blockSchema.pageBreak;
}
function Fc(e) {
    const n = [];
    return Zs(e) && n.push({
        ...e.dictionary.slash_menu.page_break,
        onItemClick: ()=>{
            N(e, {
                type: "pageBreak"
            });
        },
        key: "page_break"
    }), n;
}
function Qs(e) {
    return e.transact((n)=>{
        const t = X(n.doc, n.selection.anchor);
        if (n.selection instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CellSelection"]) return {
            type: "cell",
            anchorBlockId: t.node.attrs.id,
            anchorCellOffset: n.selection.$anchorCell.pos - t.posBeforeNode,
            headCellOffset: n.selection.$headCell.pos - t.posBeforeNode
        };
        if (n.selection instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeSelection"]) return {
            type: "node",
            anchorBlockId: t.node.attrs.id
        };
        {
            const o = X(n.doc, n.selection.head);
            return {
                type: "text",
                anchorBlockId: t.node.attrs.id,
                headBlockId: o.node.attrs.id,
                anchorOffset: n.selection.anchor - t.posBeforeNode,
                headOffset: n.selection.head - o.posBeforeNode
            };
        }
    });
}
function ei(e, n) {
    var r, s;
    const t = (r = _(n.anchorBlockId, e.doc)) == null ? void 0 : r.posBeforeNode;
    if (t === void 0) throw new Error(`Could not find block with ID ${n.anchorBlockId} to update selection`);
    let o;
    if (n.type === "cell") o = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CellSelection"].create(e.doc, t + n.anchorCellOffset, t + n.headCellOffset);
    else if (n.type === "node") o = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeSelection"].create(e.doc, t + 1);
    else {
        const i = (s = _(n.headBlockId, e.doc)) == null ? void 0 : s.posBeforeNode;
        if (i === void 0) throw new Error(`Could not find block with ID ${n.headBlockId} to update selection`);
        o = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextSelection"].create(e.doc, t + n.anchorOffset, i + n.headOffset);
    }
    e.setSelection(o);
}
function pt(e) {
    return e.map((n)=>n.type === "columnList" ? n.children.map((t)=>pt(t.children)).flat() : {
            ...n,
            children: pt(n.children)
        }).flat();
}
function Fn(e, n, t) {
    e.transact((o)=>{
        var i;
        const r = ((i = e.getSelection()) == null ? void 0 : i.blocks) || [
            e.getTextCursorPosition().block
        ], s = Qs(e);
        e.removeBlocks(r), e.insertBlocks(pt(r), n, t), ei(o, s);
    });
}
function zn(e) {
    return !e || e.type !== "columnList";
}
function Wn(e, n, t) {
    let o, r;
    if (n ? n.children.length > 0 ? (o = n.children[n.children.length - 1], r = "after") : (o = n, r = "before") : t && (o = t, r = "before"), !o || !r) return;
    const s = e.getParentBlock(o);
    return zn(s) ? {
        referenceBlock: o,
        placement: r
    } : Wn(e, r === "after" ? o : e.getPrevBlock(o), s);
}
function jn(e, n, t) {
    let o, r;
    if (n ? n.children.length > 0 ? (o = n.children[0], r = "before") : (o = n, r = "after") : t && (o = t, r = "after"), !o || !r) return;
    const s = e.getParentBlock(o);
    return zn(s) ? {
        referenceBlock: o,
        placement: r
    } : jn(e, r === "before" ? o : e.getNextBlock(o), s);
}
function ti(e) {
    e.transact(()=>{
        const n = e.getSelection(), t = (n == null ? void 0 : n.blocks[0]) || e.getTextCursorPosition().block, o = Wn(e, e.getPrevBlock(t), e.getParentBlock(t));
        o && Fn(e, o.referenceBlock, o.placement);
    });
}
function ni(e) {
    e.transact(()=>{
        const n = e.getSelection(), t = (n == null ? void 0 : n.blocks[(n == null ? void 0 : n.blocks.length) - 1]) || e.getTextCursorPosition().block, o = jn(e, e.getNextBlock(t), e.getParentBlock(t));
        o && Fn(e, o.referenceBlock, o.placement);
    });
}
function oi(e, n) {
    return function(t, o) {
        const { $from: r, $to: s } = t.selection, i = r.blockRange(s, (d)=>d.childCount > 0 && (d.type.name === "blockGroup" || d.type.name === "column"));
        if (!i) return !1;
        const a = i.startIndex;
        if (a === 0) return !1;
        const l = i.parent.child(a - 1);
        if (l.type !== e) return !1;
        if (o) {
            const d = l.lastChild && l.lastChild.type === n, u = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"].from(d ? e.create() : null), h = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Slice"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"].from(e.create(null, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"].from(n.create(null, u)))), d ? 3 : 1, 0), f = i.start, m = i.end;
            o(t.tr.step(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$transform$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReplaceAroundStep"](f - (d ? 3 : 1), m, f, m, h, 1, !0)).scrollIntoView());
        }
        return !0;
    };
}
function Gn(e) {
    return e.exec((n, t)=>oi(n.schema.nodes.blockContainer, n.schema.nodes.blockGroup)(n, t));
}
function ri(e) {
    e._tiptapEditor.commands.liftListItem("blockContainer");
}
function si(e) {
    return e.transact((n)=>{
        const { bnBlock: t } = je(n);
        return n.doc.resolve(t.beforePos).nodeBefore !== null;
    });
}
function ii(e) {
    return e.transact((n)=>{
        const { bnBlock: t } = je(n);
        return n.doc.resolve(t.beforePos).depth > 1;
    });
}
function ai(e, n) {
    const t = typeof n == "string" ? n : n.id, o = M(e), r = _(t, e);
    if (r) return E(r.node, o);
}
function ci(e, n) {
    const t = typeof n == "string" ? n : n.id, o = _(t, e), r = M(e);
    if (!o) return;
    const i = e.resolve(o.posBeforeNode).nodeBefore;
    if (i) return E(i, r);
}
function li(e, n) {
    const t = typeof n == "string" ? n : n.id, o = _(t, e), r = M(e);
    if (!o) return;
    const i = e.resolve(o.posBeforeNode + o.node.nodeSize).nodeAfter;
    if (i) return E(i, r);
}
function di(e, n) {
    const t = typeof n == "string" ? n : n.id, o = M(e), r = _(t, e);
    if (!r) return;
    const s = e.resolve(r.posBeforeNode), i = s.node(), a = s.node(-1), c = a.type.name !== "doc" ? i.type.name === "blockGroup" ? a : i : void 0;
    if (c) return E(c, o);
}
function ui(e, n, t, o = {
    updateSelection: !0
}) {
    let { from: r, to: s } = typeof n == "number" ? {
        from: n,
        to: n
    } : {
        from: n.from,
        to: n.to
    }, i = !0, a = !0, c = "";
    if (t.forEach((l)=>{
        l.check(), i && l.isText && l.marks.length === 0 ? c += l.text : i = !1, a = a ? l.isBlock : !1;
    }), r === s && a) {
        const { parent: l } = e.doc.resolve(r);
        l.isTextblock && !l.type.spec.code && !l.childCount && (r -= 1, s += 1);
    }
    return i ? e.insertText(c, r, s) : e.replaceWith(r, s, t), o.updateSelection && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["selectionToInsertionEnd"])(e, e.steps.length - 1, -1), !0;
}
function pi(e) {
    const n = M(e);
    if (e.selection.empty || "node" in e.selection) return;
    const t = e.doc.resolve(X(e.doc, e.selection.from).posBeforeNode), o = e.doc.resolve(X(e.doc, e.selection.to).posBeforeNode), r = (l, d)=>{
        const u = t.posAtIndex(l, d), h = e.doc.resolve(u).nodeAfter;
        if (!h) throw new Error(`Error getting selection - node not found at position ${u}`);
        return E(h, n);
    }, s = [], i = t.sharedDepth(o.pos), a = t.index(i), c = o.index(i);
    if (t.depth > i) {
        s.push(E(t.nodeAfter, n));
        for(let l = t.depth; l > i; l--)if (t.node(l).type.isInGroup("childContainer")) {
            const u = t.index(l) + 1, h = t.node(l).childCount;
            for(let f = u; f < h; f++)s.push(r(f, l));
        }
    } else s.push(r(a, i));
    for(let l = a + 1; l <= c; l++)s.push(r(l, i));
    if (s.length === 0) throw new Error(`Error getting selection - selection doesn't span any blocks (${e.selection})`);
    return {
        blocks: s
    };
}
function hi(e, n, t) {
    const o = typeof n == "string" ? n : n.id, r = typeof t == "string" ? t : t.id, s = M(e), i = Me(s);
    if (o === r) throw new Error(`Attempting to set selection with the same anchor and head blocks (id ${o})`);
    const a = _(o, e.doc);
    if (!a) throw new Error(`Block with ID ${o} not found`);
    const c = _(r, e.doc);
    if (!c) throw new Error(`Block with ID ${r} not found`);
    const l = ee(a), d = ee(c), u = i.blockSchema[l.blockNoteType], h = i.blockSchema[d.blockNoteType];
    if (!l.isBlockContainer || u.content === "none") throw new Error(`Attempting to set selection anchor in block without content (id ${o})`);
    if (!d.isBlockContainer || h.content === "none") throw new Error(`Attempting to set selection anchor in block without content (id ${r})`);
    let f, m;
    if (u.content === "table") {
        const g = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableMap"].get(l.blockContent.node);
        f = l.blockContent.beforePos + g.positionAt(0, 0, l.blockContent.node) + 1 + 2;
    } else f = l.blockContent.beforePos + 1;
    if (h.content === "table") {
        const g = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableMap"].get(d.blockContent.node), b = d.blockContent.beforePos + g.positionAt(g.height - 1, g.width - 1, d.blockContent.node) + 1, k = e.doc.resolve(b).nodeAfter.nodeSize;
        m = b + k - 2;
    } else m = d.blockContent.afterPos - 1;
    e.setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextSelection"].create(e.doc, f, m));
}
function fi(e) {
    const n = M(e);
    let t = e.selection.$from, o = e.selection.$to;
    for(; o.parentOffset >= o.parent.nodeSize - 2 && o.depth > 0;)o = e.doc.resolve(o.pos + 1);
    for(; o.parentOffset === 0 && o.depth > 0;)o = e.doc.resolve(o.pos - 1);
    for(; t.parentOffset === 0 && t.depth > 0;)t = e.doc.resolve(t.pos - 1);
    for(; t.parentOffset >= t.parent.nodeSize - 2 && t.depth > 0;)t = e.doc.resolve(t.pos + 1);
    const r = dr(e.doc.slice(t.pos, o.pos, !0), n);
    return {
        _meta: {
            startPos: t.pos,
            endPos: o.pos
        },
        ...r
    };
}
function mi(e) {
    const { bnBlock: n } = je(e), t = M(e.doc), o = e.doc.resolve(n.beforePos), r = o.nodeBefore, s = e.doc.resolve(n.afterPos).nodeAfter;
    let i;
    return o.depth > 1 && (i = o.node(), i.type.isInGroup("bnBlock") || (i = o.node(o.depth - 1))), {
        block: E(n.node, t),
        prevBlock: r === null ? void 0 : E(r, t),
        nextBlock: s === null ? void 0 : E(s, t),
        parentBlock: i === void 0 ? void 0 : E(i, t)
    };
}
function qn(e, n, t = "start") {
    const o = typeof n == "string" ? n : n.id, r = M(e.doc), s = Me(r), i = _(o, e.doc);
    if (!i) throw new Error(`Block with ID ${o} not found`);
    const a = ee(i), c = s.blockSchema[a.blockNoteType].content;
    if (a.isBlockContainer) {
        const l = a.blockContent;
        if (c === "none") {
            e.setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeSelection"].create(e.doc, l.beforePos));
            return;
        }
        if (c === "inline") t === "start" ? e.setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextSelection"].create(e.doc, l.beforePos + 1)) : e.setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextSelection"].create(e.doc, l.afterPos - 1));
        else if (c === "table") t === "start" ? e.setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextSelection"].create(e.doc, l.beforePos + 4)) : e.setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextSelection"].create(e.doc, l.afterPos - 4));
        else throw new j(c);
    } else {
        const l = t === "start" ? a.childContainer.node.firstChild : a.childContainer.node.lastChild;
        qn(e, l.attrs.id, t);
    }
}
let fe;
async function Tt() {
    if (fe) return fe;
    const e = await Promise.all([
        __turbopack_context__.r("[project]/node_modules/rehype-parse/index.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i),
        __turbopack_context__.r("[project]/node_modules/rehype-stringify/index.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i),
        __turbopack_context__.r("[project]/node_modules/unified/index.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i),
        __turbopack_context__.r("[project]/node_modules/hast-util-from-dom/index.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i),
        __turbopack_context__.r("[project]/node_modules/rehype-remark/index.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i),
        __turbopack_context__.r("[project]/node_modules/remark-gfm/index.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i),
        __turbopack_context__.r("[project]/node_modules/remark-stringify/index.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i),
        __turbopack_context__.r("[project]/node_modules/remark-parse/index.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i),
        __turbopack_context__.r("[project]/node_modules/remark-rehype/index.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i),
        __turbopack_context__.r("[project]/node_modules/rehype-format/index.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i)
    ]);
    return fe = {
        rehypeParse: e[0],
        rehypeStringify: e[1],
        unified: e[2],
        hastUtilFromDom: e[3],
        rehypeRemark: e[4],
        remarkGfm: e[5],
        remarkStringify: e[6],
        remarkParse: e[7],
        remarkRehype: e[8],
        rehypeFormat: e[9]
    }, fe;
}
function gi() {
    const e = (n)=>{
        let t = n.children.length;
        for(let o = 0; o < t; o++){
            const r = n.children[o];
            if (r.type === "element" && (e(r), r.tagName === "u")) if (r.children.length > 0) {
                n.children.splice(o, 1, ...r.children);
                const s = r.children.length - 1;
                t += s, o += s;
            } else n.children.splice(o, 1), t--, o--;
        }
    };
    return e;
}
function bi() {
    const e = fe;
    if (!e) throw new Error("addSpacesToCheckboxes requires ESM dependencies to be initialized");
    const n = (t)=>{
        var o;
        if (t.children && "length" in t.children && t.children.length) for(let r = t.children.length - 1; r >= 0; r--){
            const s = t.children[r], i = r + 1 < t.children.length ? t.children[r + 1] : void 0;
            s.type === "element" && s.tagName === "input" && ((o = s.properties) == null ? void 0 : o.type) === "checkbox" && (i == null ? void 0 : i.type) === "element" && i.tagName === "p" ? (i.tagName = "span", i.children.splice(0, 0, e.hastUtilFromDom.fromDom(document.createTextNode(" ")))) : n(s);
        }
    };
    return n;
}
function Mt(e) {
    const n = fe;
    if (!n) throw new Error("cleanHTMLToMarkdown requires ESM dependencies to be initialized");
    return n.unified.unified().use(n.rehypeParse.default, {
        fragment: !0
    }).use(gi).use(bi).use(n.rehypeRemark.default).use(n.remarkGfm.default).use(n.remarkStringify.default, {
        handlers: {
            text: {
                "Mt.use": (o)=>o.value
            }["Mt.use"]
        }
    }).processSync(e).value;
}
async function ki(e, n, t, o) {
    await Tt();
    const s = Ke(n, t).exportBlocks(e, o);
    return Mt(s);
}
function wi(e) {
    return Array.prototype.indexOf.call(e.parentElement.childNodes, e);
}
function yi(e) {
    return e.nodeType === 3 && !/\S/.test(e.nodeValue || "");
}
function Ci(e) {
    e.querySelectorAll("li > ul, li > ol").forEach((n)=>{
        const t = wi(n), o = n.parentElement, r = Array.from(o.childNodes).slice(t + 1);
        n.remove(), r.forEach((s)=>{
            s.remove();
        }), o.insertAdjacentElement("afterend", n), r.reverse().forEach((s)=>{
            if (yi(s)) return;
            const i = document.createElement("li");
            i.append(s), n.insertAdjacentElement("afterend", i);
        }), o.childNodes.length === 0 && o.remove();
    });
}
function vi(e) {
    e.querySelectorAll("li + ul, li + ol").forEach((n)=>{
        var s, i;
        const t = n.previousElementSibling, o = document.createElement("div");
        t.insertAdjacentElement("afterend", o), o.append(t);
        const r = document.createElement("div");
        for(r.setAttribute("data-node-type", "blockGroup"), o.append(r); ((s = o.nextElementSibling) == null ? void 0 : s.nodeName) === "UL" || ((i = o.nextElementSibling) == null ? void 0 : i.nodeName) === "OL";)r.append(o.nextElementSibling);
    });
}
let Jt = null;
function Ei() {
    return Jt || (Jt = document.implementation.createHTMLDocument("title"));
}
function Kn(e) {
    if (typeof e == "string") {
        const n = Ei().createElement("div");
        n.innerHTML = e, e = n;
    }
    return Ci(e), vi(e), e;
}
async function Jn(e, n) {
    const t = Kn(e), r = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DOMParser"].fromSchema(n).parse(t, {
        topNode: n.nodes.blockGroup.create()
    }), s = [];
    for(let i = 0; i < r.childCount; i++)s.push(E(r.child(i), n));
    return s;
}
function Si(e, n) {
    const t = n.value ? n.value : "", o = {};
    n.lang && (o["data-language"] = n.lang);
    let r = {
        type: "element",
        tagName: "code",
        properties: o,
        children: [
            {
                type: "text",
                value: t
            }
        ]
    };
    return n.meta && (r.data = {
        meta: n.meta
    }), e.patch(n, r), r = e.applyData(n, r), r = {
        type: "element",
        tagName: "pre",
        properties: {},
        children: [
            r
        ]
    }, e.patch(n, r), r;
}
async function Xn(e) {
    const n = await Tt();
    return n.unified.unified().use(n.remarkParse.default).use(n.remarkGfm.default).use(n.remarkRehype.default, {
        handlers: {
            ...n.remarkRehype.defaultHandlers,
            code: Si
        }
    }).use(n.rehypeStringify.default).processSync(e).value;
}
async function Bi(e, n) {
    const t = await Xn(e);
    return Jn(t, n);
}
const Pt = [
    "vscode-editor-data",
    "blocknote/html",
    "text/markdown",
    "text/html",
    "text/plain",
    "Files"
];
function xi(e, n) {
    if (!e.startsWith(".") || !n.startsWith(".")) throw new Error("The strings provided are not valid file extensions.");
    return e === n;
}
function Ti(e, n) {
    const t = e.split("/"), o = n.split("/");
    if (t.length !== 2) throw new Error(`The string ${e} is not a valid MIME type.`);
    if (o.length !== 2) throw new Error(`The string ${n} is not a valid MIME type.`);
    return t[1] === "*" || o[1] === "*" ? t[0] === o[0] : (t[0] === "*" || o[0] === "*" || t[0] === o[0]) && t[1] === o[1];
}
function Xt(e, n, t) {
    let o;
    return Array.isArray(n.content) && n.content.length === 0 ? o = e.updateBlock(n, t).id : o = e.insertBlocks([
        t
    ], n, "after")[0].id, o;
}
async function Yn(e, n) {
    var i;
    if (!n.uploadFile) {
        console.warn("Attempted ot insert file, but uploadFile is not set in the BlockNote editor options");
        return;
    }
    const t = "dataTransfer" in e ? e.dataTransfer : e.clipboardData;
    if (t === null) return;
    let o = null;
    for (const a of Pt)if (t.types.includes(a)) {
        o = a;
        break;
    }
    if (o !== "Files") return;
    const r = t.items;
    if (!r) return;
    e.preventDefault();
    const s = Object.values(n.schema.blockSchema).filter((a)=>a.isFileBlock);
    for(let a = 0; a < r.length; a++){
        let c = "file";
        for (const d of s)for (const u of d.fileBlockAccept || []){
            const h = u.startsWith("."), f = r[a].getAsFile();
            if (f && (!h && f.type && Ti(r[a].type, u) || h && xi("." + f.name.split(".").pop(), u))) {
                c = d.type;
                break;
            }
        }
        const l = r[a].getAsFile();
        if (l) {
            const d = {
                type: c,
                props: {
                    name: l.name
                }
            };
            let u;
            if (e.type === "paste") {
                const m = n.getTextCursorPosition().block;
                u = Xt(n, m, d);
            } else if (e.type === "drop") {
                const m = {
                    left: e.clientX,
                    top: e.clientY
                }, g = (i = n.prosemirrorView) == null ? void 0 : i.posAtCoords(m);
                if (!g) return;
                u = n.transact((b)=>{
                    const k = X(b.doc, g.pos);
                    return Xt(n, n.getBlock(k.node.attrs.id), d);
                });
            } else return;
            const h = await n.uploadFile(l, u), f = typeof h == "string" ? {
                props: {
                    url: h
                }
            } : {
                ...h
            };
            n.updateBlock(u, f);
        }
    }
}
const Mi = (e)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Extension"].create({
        name: "dropFile",
        addProseMirrorPlugins () {
            return [
                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Plugin"]({
                    props: {
                        handleDOMEvents: {
                            drop (n, t) {
                                if (!e.isEditable) return;
                                let o = null;
                                for (const r of Pt)if (t.dataTransfer.types.includes(r)) {
                                    o = r;
                                    break;
                                }
                                return o === null ? !0 : o === "Files" ? (Yn(t, e), !0) : !1;
                            }
                        }
                    }
                })
            ];
        }
    }), Pi = /(^|\n) {0,3}#{1,6} {1,8}[^\n]{1,64}\r?\n\r?\n\s{0,32}\S/, Ii = new RegExp("(?:\\s|^)(_|__|\\*|\\*\\*|~~|==|\\+\\+)(?!\\s).{1,64}(?<!\\s)(?=\\1)"), Li = /\[[^\]]{1,128}\]\(https?:\/\/\S{1,999}\)/, Ai = new RegExp("(?:\\s|^)`(?!\\s)[^`]{1,48}(?<!\\s)`([^\\w]|$)"), Ni = /(?:^|\n)\s{0,5}-\s{1}[^\n]+\n\s{0,15}-\s/, Hi = /(?:^|\n)\s{0,5}\d+\.\s{1}[^\n]+\n\s{0,15}\d+\.\s/, Di = /\n{2} {0,3}-{2,48}\n{2}/, Oi = /(?:\n|^)(```|~~~|\$\$)(?!`|~)[^\s]{0,64} {0,64}[^\n]{0,64}\n[\s\S]{0,9999}?\s*\1 {0,64}(?:\n+|$)/, Ri = /(?:\n|^)(?!\s)\w[^\n]{0,64}\r?\n(-|=)\1{0,64}\n\n\s{0,64}(\w|$)/, Vi = /(?:^|(\r?\n\r?\n))( {0,3}>[^\n]{1,333}\n){1,999}($|(\r?\n))/, Ui = /^\s*\|(.+\|)+\s*$/m, _i = /^\s*\|(\s*[-:]+[-:]\s*\|)+\s*$/m, $i = /^\s*\|(.+\|)+\s*$/m, Fi = (e)=>Pi.test(e) || Ii.test(e) || Li.test(e) || Ai.test(e) || Ni.test(e) || Hi.test(e) || Di.test(e) || Oi.test(e) || Ri.test(e) || Vi.test(e) || Ui.test(e) || _i.test(e) || $i.test(e);
async function zi(e, n) {
    const { schema: t } = n.state;
    if (!e.clipboardData) return !1;
    const o = e.clipboardData.getData("text/plain");
    if (!o) return !1;
    if (!t.nodes.codeBlock) return n.pasteText(o), !0;
    const r = e.clipboardData.getData("vscode-editor-data"), s = r ? JSON.parse(r) : void 0, i = s == null ? void 0 : s.mode;
    return i ? (n.pasteHTML(`<pre><code class="language-${i}">${o.replace(/\r\n?/g, `
`)}</code></pre>`), !0) : !1;
}
function Wi({ event: e, editor: n, prioritizeMarkdownOverHTML: t, plainTextAsMarkdown: o }) {
    var a;
    if (n.transact((c)=>c.selection.$from.parent.type.spec.code && c.selection.$to.parent.type.spec.code)) {
        const c = (a = e.clipboardData) == null ? void 0 : a.getData("text/plain");
        if (c) return n.pasteText(c), !0;
    }
    let s;
    for (const c of Pt)if (e.clipboardData.types.includes(c)) {
        s = c;
        break;
    }
    if (!s) return !0;
    if (s === "vscode-editor-data") return zi(e, n.prosemirrorView), !0;
    if (s === "Files") return Yn(e, n), !0;
    const i = e.clipboardData.getData(s);
    if (s === "blocknote/html") return n.pasteHTML(i, !0), !0;
    if (s === "text/markdown") return n.pasteMarkdown(i), !0;
    if (t) {
        const c = e.clipboardData.getData("text/plain");
        if (Fi(c)) return n.pasteMarkdown(c), !0;
    }
    return s === "text/html" ? (n.pasteHTML(i), !0) : o ? (n.pasteMarkdown(i), !0) : (n.pasteText(i), !0);
}
const ji = (e, n)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Extension"].create({
        name: "pasteFromClipboard",
        addProseMirrorPlugins () {
            return [
                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Plugin"]({
                    props: {
                        handleDOMEvents: {
                            paste (t, o) {
                                if (o.preventDefault(), !!e.isEditable) return n({
                                    event: o,
                                    editor: e,
                                    defaultPasteHandler: ({ prioritizeMarkdownOverHTML: r = !0, plainTextAsMarkdown: s = !0 } = {})=>Wi({
                                            event: o,
                                            editor: e,
                                            prioritizeMarkdownOverHTML: r,
                                            plainTextAsMarkdown: s
                                        })
                                });
                            }
                        }
                    }
                })
            ];
        }
    });
function Zn(e) {
    const n = [];
    return e.descendants((t)=>{
        var r, s;
        const o = M(t);
        return t.type.name === "blockContainer" && ((r = t.firstChild) == null ? void 0 : r.type.name) === "blockGroup" ? !0 : t.type.name === "columnList" && t.childCount === 1 ? ((s = t.firstChild) == null || s.forEach((i)=>{
            n.push(E(i, o));
        }), !1) : t.type.isInGroup("bnBlock") ? (n.push(E(t, o)), !1) : !0;
    }), n;
}
function Gi(e, n, t) {
    var a;
    let o = !1;
    const r = e.state.selection instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CellSelection"];
    if (!r) {
        const c = e.state.doc.slice(e.state.selection.from, e.state.selection.to, !1).content, l = [];
        for(let d = 0; d < c.childCount; d++)l.push(c.child(d));
        o = l.find((d)=>d.type.isInGroup("bnBlock") || d.type.name === "blockGroup" || d.type.spec.group === "blockContent") === void 0, o && (n = c);
    }
    let s;
    const i = Ke(e.state.schema, t);
    if (r) {
        ((a = n.firstChild) == null ? void 0 : a.type.name) === "table" && (n = n.firstChild.content);
        const c = kn(n, t.schema.inlineContentSchema, t.schema.styleSchema);
        s = `<table>${i.exportInlineContent(c, {})}</table>`;
    } else if (o) {
        const c = Ge(n, t.schema.inlineContentSchema, t.schema.styleSchema);
        s = i.exportInlineContent(c, {});
    } else {
        const c = Zn(n);
        s = i.exportBlocks(c, {});
    }
    return s;
}
function Qn(e, n) {
    "node" in e.state.selection && e.state.selection.node.type.spec.group === "blockContent" && n.transact((i)=>i.setSelection(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeSelection"](i.doc.resolve(e.state.selection.from - 1))));
    const t = e.serializeForClipboard(e.state.selection.content()).dom.innerHTML, o = e.state.selection.content().content, r = Gi(e, o, n), s = Mt(r);
    return {
        clipboardHTML: t,
        externalHTML: r,
        markdown: s
    };
}
const Yt = ()=>{
    const e = window.getSelection();
    if (!e || e.isCollapsed) return !0;
    let n = e.focusNode;
    for(; n;){
        if (n instanceof HTMLElement && n.getAttribute("contenteditable") === "false") return !0;
        n = n.parentElement;
    }
    return !1;
}, Zt = (e, n, t)=>{
    t.preventDefault(), t.clipboardData.clearData();
    const { clipboardHTML: o, externalHTML: r, markdown: s } = Qn(n, e);
    t.clipboardData.setData("blocknote/html", o), t.clipboardData.setData("text/html", r), t.clipboardData.setData("text/plain", s);
}, qi = (e)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Extension"].create({
        name: "copyToClipboard",
        addProseMirrorPlugins () {
            return [
                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Plugin"]({
                    props: {
                        handleDOMEvents: {
                            copy (n, t) {
                                return Yt() || Zt(e, n, t), !0;
                            },
                            cut (n, t) {
                                return Yt() || (Zt(e, n, t), n.editable && n.dispatch(n.state.tr.deleteSelection())), !0;
                            },
                            // This is for the use-case in which only a block without content
                            // is selected, e.g. an image block, and dragged (not using the
                            // drag handle).
                            dragstart (n, t) {
                                if (!("node" in n.state.selection) || n.state.selection.node.type.spec.group !== "blockContent") return;
                                e.transact((i)=>i.setSelection(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeSelection"](i.doc.resolve(n.state.selection.from - 1)))), t.preventDefault(), t.dataTransfer.clearData();
                                const { clipboardHTML: o, externalHTML: r, markdown: s } = Qn(n, e);
                                return t.dataTransfer.setData("blocknote/html", o), t.dataTransfer.setData("text/html", r), t.dataTransfer.setData("text/plain", s), !0;
                            }
                        }
                    }
                })
            ];
        }
    }), Ki = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Extension"].create({
    name: "blockBackgroundColor",
    addGlobalAttributes () {
        return [
            {
                types: [
                    "blockContainer",
                    "tableCell",
                    "tableHeader"
                ],
                attributes: {
                    backgroundColor: {
                        default: T.backgroundColor.default,
                        parseHTML: (e)=>e.hasAttribute("data-background-color") ? e.getAttribute("data-background-color") : T.backgroundColor.default,
                        renderHTML: (e)=>e.backgroundColor === T.backgroundColor.default ? {} : {
                                "data-background-color": e.backgroundColor
                            }
                    }
                }
            }
        ];
    }
});
class It {
    constructor(){
        // eslint-disable-next-line @typescript-eslint/ban-types
        p(this, "callbacks", {});
    }
    on(n, t) {
        return this.callbacks[n] || (this.callbacks[n] = []), this.callbacks[n].push(t), ()=>this.off(n, t);
    }
    emit(n, ...t) {
        const o = this.callbacks[n];
        o && o.forEach((r)=>r.apply(this, t));
    }
    off(n, t) {
        const o = this.callbacks[n];
        o && (t ? this.callbacks[n] = o.filter((r)=>r !== t) : delete this.callbacks[n]);
    }
    removeAllListeners() {
        this.callbacks = {};
    }
}
class L extends It {
    // eslint-disable-next-line
    constructor(...t){
        super();
        p(this, "plugins", []);
    }
    static key() {
        throw new Error("You must implement the key method in your extension");
    }
    addProsemirrorPlugin(t) {
        this.plugins.push(t);
    }
    get priority() {}
}
const $e = class $e extends L {
    constructor(t){
        super();
        p(this, "provider");
        p(this, "recentlyUpdatedCursors");
        p(this, "renderCursor", (t, o)=>{
            let r = this.recentlyUpdatedCursors.get(o);
            if (!r) {
                const s = (this.collaboration.renderCursor ?? $e.defaultCursorRender)(t);
                this.collaboration.showCursorLabels !== "always" && (s.addEventListener("mouseenter", ()=>{
                    const i = this.recentlyUpdatedCursors.get(o);
                    i.element.setAttribute("data-active", ""), i.hideTimeout && (clearTimeout(i.hideTimeout), this.recentlyUpdatedCursors.set(o, {
                        element: i.element,
                        hideTimeout: void 0
                    }));
                }), s.addEventListener("mouseleave", ()=>{
                    const i = this.recentlyUpdatedCursors.get(o);
                    this.recentlyUpdatedCursors.set(o, {
                        element: i.element,
                        hideTimeout: setTimeout(()=>{
                            i.element.removeAttribute("data-active");
                        }, 2e3)
                    });
                })), r = {
                    element: s,
                    hideTimeout: void 0
                }, this.recentlyUpdatedCursors.set(o, r);
            }
            return r.element;
        });
        p(this, "updateUser", (t)=>{
            this.provider.awareness.setLocalStateField("user", t);
        });
        this.collaboration = t, this.provider = t.provider, this.recentlyUpdatedCursors = /* @__PURE__ */ new Map(), this.provider.awareness.setLocalStateField("user", t.user), t.showCursorLabels !== "always" && this.provider.awareness.on("change", ({ updated: o })=>{
            for (const r of o){
                const s = this.recentlyUpdatedCursors.get(r);
                s && (s.element.setAttribute("data-active", ""), s.hideTimeout && clearTimeout(s.hideTimeout), this.recentlyUpdatedCursors.set(r, {
                    element: s.element,
                    hideTimeout: setTimeout(()=>{
                        s.element.removeAttribute("data-active");
                    }, 2e3)
                }));
            }
        }), this.addProsemirrorPlugin((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$cursor$2d$plugin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["yCursorPlugin"])(this.provider.awareness, {
            selectionBuilder: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$cursor$2d$plugin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["defaultSelectionBuilder"],
            cursorBuilder: this.renderCursor
        }));
    }
    static key() {
        return "yCursorPlugin";
    }
    get priority() {
        return 999;
    }
};
p($e, "defaultCursorRender", (t)=>{
    const o = document.createElement("span");
    o.classList.add("bn-collaboration-cursor__base");
    const r = document.createElement("span");
    r.setAttribute("contentedEditable", "false"), r.classList.add("bn-collaboration-cursor__caret"), r.setAttribute("style", `background-color: ${t.color}`);
    const s = document.createElement("span");
    return s.classList.add("bn-collaboration-cursor__label"), s.setAttribute("style", `background-color: ${t.color}`), s.insertBefore(document.createTextNode(t.name), null), r.insertBefore(s, null), o.insertBefore(document.createTextNode("⁠"), null), o.insertBefore(r, null), o.insertBefore(document.createTextNode("⁠"), null), o;
});
let Ue = $e;
class ht extends L {
    static key() {
        return "ySyncPlugin";
    }
    constructor(n){
        super(), this.addProsemirrorPlugin((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$sync$2d$plugin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ySyncPlugin"])(n));
    }
    get priority() {
        return 1001;
    }
}
class ft extends L {
    static key() {
        return "yUndoPlugin";
    }
    constructor(){
        super(), this.addProsemirrorPlugin((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$undo$2d$plugin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["yUndoPlugin"])());
    }
    get priority() {
        return 1e3;
    }
}
const eo = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Mark"].create({
    name: "comment",
    excludes: "",
    inclusive: !1,
    keepOnSplit: !0,
    addAttributes () {
        return {
            // orphans are marks that currently don't have an active thread. It could be
            // that users have resolved the thread. Resolved threads by default are not shown in the document,
            // but we need to keep the mark (positioning) data so we can still "revive" it when the thread is unresolved
            // or we enter a "comments" view that includes resolved threads.
            orphan: {
                parseHTML: (e)=>!!e.getAttribute("data-orphan"),
                renderHTML: (e)=>e.orphan ? {
                        "data-orphan": "true"
                    } : {},
                default: !1
            },
            threadId: {
                parseHTML: (e)=>e.getAttribute("data-bn-thread-id"),
                renderHTML: (e)=>({
                        "data-bn-thread-id": e.threadId
                    }),
                default: ""
            }
        };
    },
    renderHTML ({ HTMLAttributes: e }) {
        return [
            "span",
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeAttributes"])(e, {
                class: "bn-thread-mark"
            })
        ];
    },
    parseHTML () {
        return [
            {
                tag: "span.bn-thread-mark"
            }
        ];
    },
    extendMarkSchema (e) {
        return e.name === "comment" ? {
            blocknoteIgnore: !0
        } : {};
    }
});
class Ji extends It {
    constructor(t){
        super();
        p(this, "userCache", /* @__PURE__ */ new Map());
        // avoid duplicate loads
        p(this, "loadingUsers", /* @__PURE__ */ new Set());
        this.resolveUsers = t;
    }
    /**
   * Load information about users based on an array of user ids.
   */ async loadUsers(t) {
        const o = t.filter((r)=>!this.userCache.has(r) && !this.loadingUsers.has(r));
        if (o.length !== 0) {
            for (const r of o)this.loadingUsers.add(r);
            try {
                const r = await this.resolveUsers(o);
                for (const s of r)this.userCache.set(s.id, s);
                this.emit("update", this.userCache);
            } finally{
                for (const r of o)this.loadingUsers.delete(r);
            }
        }
    }
    /**
   * Retrieve information about a user based on their id, if cached.
   *
   * The user will have to be loaded via `loadUsers` first
   */ getUser(t) {
        return this.userCache.get(t);
    }
    /**
   * Subscribe to changes in the user store.
   *
   * @param cb - The callback to call when the user store changes.
   * @returns A function to unsubscribe from the user store.
   */ subscribe(t) {
        return this.on("update", t);
    }
}
const Le = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PluginKey"]("blocknote-comments"), Xi = "SET_SELECTED_THREAD_ID";
function Yi(e, n) {
    const t = /* @__PURE__ */ new Map();
    return e.descendants((o, r)=>{
        o.marks.forEach((s)=>{
            if (s.type.name === n) {
                const i = s.attrs.threadId;
                if (!i) return;
                const a = r, c = a + o.nodeSize, l = t.get(i) ?? {
                    from: 1 / 0,
                    to: 0
                };
                t.set(i, {
                    from: Math.min(a, l.from),
                    to: Math.max(c, l.to)
                });
            }
        });
    }), t;
}
class Zi extends L {
    constructor(t, o, r){
        super();
        p(this, "userStore");
        /**
     * Whether a comment is currently being composed
     */ p(this, "pendingComment", !1);
        /**
     * The currently selected thread id
     */ p(this, "selectedThreadId");
        /**
     * Store the positions of all threads in the document.
     * this can be used later to implement a floating sidebar
     */ p(this, "threadPositions", /* @__PURE__ */ new Map());
        /**
     * when a thread is resolved or deleted, we need to update the marks to reflect the new state
     */ p(this, "updateMarksFromThreads", (t)=>{
            this.editor.transact((o)=>{
                o.doc.descendants((r, s)=>{
                    r.marks.forEach((i)=>{
                        if (i.type.name === this.markType) {
                            const a = i.type, c = i.attrs.threadId, l = t.get(c), d = !!(!l || l.resolved || l.deletedAt);
                            if (d !== i.attrs.orphan) {
                                const u = Math.max(s, 0), h = Math.min(s + r.nodeSize, o.doc.content.size - 1, o.doc.content.size - 1);
                                o.removeMark(u, h, i), o.addMark(u, h, a.create({
                                    ...i.attrs,
                                    orphan: d
                                })), d && this.selectedThreadId === c && (this.selectedThreadId = void 0, this.emitStateUpdate());
                            }
                        }
                    });
                });
            });
        });
        if (this.editor = t, this.threadStore = o, this.markType = r, !t.resolveUsers) throw new Error("resolveUsers is required for comments");
        this.userStore = new Ji(t.resolveUsers), this.threadStore.subscribe(this.updateMarksFromThreads), t.onCreate(()=>{
            this.updateMarksFromThreads(this.threadStore.getThreads()), t.onSelectionChange(()=>{
                this.pendingComment && (this.pendingComment = !1, this.emitStateUpdate());
            });
        });
        const s = this;
        this.addProsemirrorPlugin(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Plugin"]({
            key: Le,
            state: {
                init () {
                    return {
                        decorations: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecorationSet"].empty
                    };
                },
                apply (i, a) {
                    const c = i.getMeta(Le);
                    if (!i.docChanged && !c) return a;
                    const l = i.docChanged ? Yi(i.doc, s.markType) : s.threadPositions;
                    (l.size > 0 || s.threadPositions.size > 0) && (s.threadPositions = l, s.emitStateUpdate());
                    const d = [];
                    if (s.selectedThreadId) {
                        const u = l.get(s.selectedThreadId);
                        u && d.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Decoration"].inline(u.from, u.to, {
                            class: "bn-thread-mark-selected"
                        }));
                    }
                    return {
                        decorations: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecorationSet"].create(i.doc, d)
                    };
                }
            },
            props: {
                decorations (i) {
                    var a;
                    return ((a = Le.getState(i)) == null ? void 0 : a.decorations) ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecorationSet"].empty;
                },
                /**
           * Handle click on a thread mark and mark it as selected
           */ handleClick: (i, a, c)=>{
                    if (c.button !== 0) return;
                    const l = i.state.doc.nodeAt(a);
                    if (!l) {
                        s.selectThread(void 0);
                        return;
                    }
                    const d = l.marks.find((h)=>h.type.name === r && h.attrs.orphan !== !0), u = d == null ? void 0 : d.attrs.threadId;
                    s.selectThread(u, !1);
                }
            }
        }));
    }
    static key() {
        return "comments";
    }
    emitStateUpdate() {
        this.emit("update", {
            selectedThreadId: this.selectedThreadId,
            pendingComment: this.pendingComment,
            threadPositions: this.threadPositions
        });
    }
    /**
   * Subscribe to state updates
   */ onUpdate(t) {
        return this.on("update", t);
    }
    /**
   * Set the selected thread
   */ selectThread(t, o = !0) {
        var r, s;
        if (this.selectedThreadId !== t && (this.selectedThreadId = t, this.emitStateUpdate(), this.editor.transact((i)=>i.setMeta(Le, {
                name: Xi
            })), t && o)) {
            const i = this.threadPositions.get(t);
            if (!i) return;
            (s = (r = this.editor.prosemirrorView) == null ? void 0 : r.domAtPos(i.from).node) == null || s.scrollIntoView({
                behavior: "smooth",
                block: "center"
            });
        }
    }
    /**
   * Start a pending comment (e.g.: when clicking the "Add comment" button)
   */ startPendingComment() {
        this.pendingComment = !0, this.emitStateUpdate();
    }
    /**
   * Stop a pending comment (e.g.: user closes the comment composer)
   */ stopPendingComment() {
        this.pendingComment = !1, this.emitStateUpdate();
    }
    /**
   * Create a thread at the current selection
   */ async createThread(t) {
        const o = await this.threadStore.createThread(t);
        if (this.threadStore.addThreadToDocument) {
            const r = this.editor.prosemirrorView, s = r.state.selection, i = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$keys$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ySyncPluginKey"].getState(r.state), a = {
                prosemirror: {
                    head: s.head,
                    anchor: s.anchor
                },
                yjs: i ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$sync$2d$plugin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getRelativeSelection"])(i.binding, r.state) : void 0
            };
            await this.threadStore.addThreadToDocument({
                threadId: o.id,
                selection: a
            });
        } else this.editor._tiptapEditor.commands.setMark(this.markType, {
            orphan: !1,
            threadId: o.id
        });
    }
}
class Qi {
    constructor(n, t, o, r){
        p(this, "state");
        p(this, "emitUpdate");
        p(this, "mouseDownHandler", ()=>{
            var n;
            (n = this.state) != null && n.show && (this.state.show = !1, this.emitUpdate());
        });
        // For dragging the whole editor.
        p(this, "dragstartHandler", ()=>{
            var n;
            (n = this.state) != null && n.show && (this.state.show = !1, this.emitUpdate());
        });
        p(this, "scrollHandler", ()=>{
            var n;
            if ((n = this.state) != null && n.show) {
                const t = this.pmView.root.querySelector(`[data-node-type="blockContainer"][data-id="${this.state.block.id}"]`);
                if (!t) return;
                this.state.referencePos = t.getBoundingClientRect(), this.emitUpdate();
            }
        });
        p(this, "closeMenu", ()=>{
            var n;
            (n = this.state) != null && n.show && (this.state.show = !1, this.emitUpdate());
        });
        this.editor = n, this.pluginKey = t, this.pmView = o, this.emitUpdate = ()=>{
            if (!this.state) throw new Error("Attempting to update uninitialized file panel");
            r(this.state);
        }, o.dom.addEventListener("mousedown", this.mouseDownHandler), o.dom.addEventListener("dragstart", this.dragstartHandler), o.root.addEventListener("scroll", this.scrollHandler, !0);
    }
    update(n, t) {
        var a, c;
        const o = this.pluginKey.getState(n.state), r = this.pluginKey.getState(t);
        if (!((a = this.state) != null && a.show) && o != null && o.block && this.editor.isEditable) {
            const l = this.pmView.root.querySelector(`[data-node-type="blockContainer"][data-id="${o.block.id}"]`);
            if (!l) return;
            this.state = {
                show: !0,
                referencePos: l.getBoundingClientRect(),
                block: o.block
            }, this.emitUpdate();
            return;
        }
        const s = (o == null ? void 0 : o.block) && !(r != null && r.block), i = !(o != null && o.block) && (r == null ? void 0 : r.block);
        s && this.state && !this.state.show && (this.state.show = !0, this.emitUpdate()), i && (c = this.state) != null && c.show && (this.state.show = !1, this.emitUpdate());
    }
    destroy() {
        this.pmView.dom.removeEventListener("mousedown", this.mouseDownHandler), this.pmView.dom.removeEventListener("dragstart", this.dragstartHandler), this.pmView.root.removeEventListener("scroll", this.scrollHandler, !0);
    }
}
const et = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PluginKey"]("FilePanelPlugin");
class ea extends L {
    constructor(t){
        super();
        p(this, "view");
        p(this, "closeMenu", ()=>{
            var t;
            return (t = this.view) == null ? void 0 : t.closeMenu();
        });
        this.addProsemirrorPlugin(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Plugin"]({
            key: et,
            view: (o)=>(this.view = new Qi(t, et, o, (r)=>{
                    this.emit("update", r);
                }), this.view),
            props: {
                handleKeyDown: (o, r)=>{
                    var s;
                    return r.key === "Escape" && this.shown ? ((s = this.view) == null || s.closeMenu(), !0) : !1;
                }
            },
            state: {
                init: ()=>({
                        block: void 0
                    }),
                apply: (o, r)=>{
                    const s = o.getMeta(et);
                    return s || (!o.getMeta(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$keys$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ySyncPluginKey"]) && (o.selectionSet || o.docChanged) ? {
                        block: void 0
                    } : r);
                }
            }
        }));
    }
    static key() {
        return "filePanel";
    }
    get shown() {
        var t, o;
        return ((o = (t = this.view) == null ? void 0 : t.state) == null ? void 0 : o.show) || !1;
    }
    onUpdate(t) {
        return this.on("update", t);
    }
}
class ta {
    constructor(n, t, o){
        p(this, "state");
        p(this, "emitUpdate");
        p(this, "preventHide", !1);
        p(this, "preventShow", !1);
        p(this, "shouldShow", ({ view: n, state: t, from: o, to: r })=>{
            const { doc: s, selection: i } = t, { empty: a } = i, c = !s.textBetween(o, r).length && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isTextSelection"])(t.selection);
            if (i.$from.parent.type.spec.code || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNodeSelection"])(i) && i.node.type.spec.code || a || c) return !1;
            const l = document.activeElement;
            return !(!this.isElementWithinEditorWrapper(l) && n.editable);
        });
        p(this, "blurHandler", (n)=>{
            var o;
            if (this.preventHide) {
                this.preventHide = !1;
                return;
            }
            const t = this.pmView.dom.parentElement;
            // An element is clicked.
            n && n.relatedTarget && // Element is inside the editor.
            (t === n.relatedTarget || t.contains(n.relatedTarget) || n.relatedTarget.matches(".bn-ui-container, .bn-ui-container *")) || (o = this.state) != null && o.show && (this.state.show = !1, this.emitUpdate());
        });
        p(this, "isElementWithinEditorWrapper", (n)=>{
            if (!n) return !1;
            const t = this.pmView.dom.parentElement;
            return t ? t.contains(n) : !1;
        });
        p(this, "viewMousedownHandler", (n)=>{
            this.isElementWithinEditorWrapper(n.target) || (this.preventShow = !0);
        });
        p(this, "mouseupHandler", ()=>{
            this.preventShow && (this.preventShow = !1, setTimeout(()=>this.update(this.pmView)));
        });
        // For dragging the whole editor.
        p(this, "dragHandler", ()=>{
            var n;
            (n = this.state) != null && n.show && (this.state.show = !1, this.emitUpdate());
        });
        p(this, "scrollHandler", ()=>{
            var n;
            (n = this.state) != null && n.show && (this.state.referencePos = this.getSelectionBoundingBox(), this.emitUpdate());
        });
        p(this, "closeMenu", ()=>{
            var n;
            (n = this.state) != null && n.show && (this.state.show = !1, this.emitUpdate());
        });
        this.editor = n, this.pmView = t, this.emitUpdate = ()=>{
            if (!this.state) throw new Error("Attempting to update uninitialized formatting toolbar");
            o(this.state);
        }, t.dom.addEventListener("mousedown", this.viewMousedownHandler), t.root.addEventListener("mouseup", this.mouseupHandler), t.dom.addEventListener("dragstart", this.dragHandler), t.dom.addEventListener("dragover", this.dragHandler), t.dom.addEventListener("blur", this.blurHandler), t.root.addEventListener("scroll", this.scrollHandler, !0);
    }
    update(n, t) {
        var f, m, g;
        const { state: o, composing: r } = n, { doc: s, selection: i } = o, a = t && t.doc.eq(s) && t.selection.eq(i);
        if (r || a) return;
        const { ranges: c } = i, l = Math.min(...c.map((b)=>b.$from.pos)), d = Math.max(...c.map((b)=>b.$to.pos)), u = this.shouldShow({
            view: n,
            state: o,
            from: l,
            to: d
        }), h = typeof Range.prototype.getClientRects > "u";
        if (!this.preventShow && (u || this.preventHide) && !h) {
            const b = {
                show: !0,
                referencePos: this.getSelectionBoundingBox()
            };
            (b.show !== ((f = this.state) == null ? void 0 : f.show) || b.referencePos.toJSON() !== ((m = this.state) == null ? void 0 : m.referencePos.toJSON())) && (this.state = b, this.emitUpdate());
            return;
        }
        if ((g = this.state) != null && g.show && !this.preventHide && (!u || this.preventShow || !this.editor.isEditable)) {
            this.state.show = !1, this.emitUpdate();
            return;
        }
    }
    destroy() {
        this.pmView.dom.removeEventListener("mousedown", this.viewMousedownHandler), this.pmView.root.removeEventListener("mouseup", this.mouseupHandler), this.pmView.dom.removeEventListener("dragstart", this.dragHandler), this.pmView.dom.removeEventListener("dragover", this.dragHandler), this.pmView.dom.removeEventListener("blur", this.blurHandler), this.pmView.root.removeEventListener("scroll", this.scrollHandler, !0);
    }
    getSelectionBoundingBox() {
        const { state: n } = this.pmView, { selection: t } = n, { ranges: o } = t, r = Math.min(...o.map((i)=>i.$from.pos)), s = Math.max(...o.map((i)=>i.$to.pos));
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNodeSelection"])(t)) {
            const i = this.pmView.nodeDOM(r);
            if (i) return i.getBoundingClientRect();
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["posToDOMRect"])(this.pmView, r, s);
    }
}
const na = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PluginKey"]("FormattingToolbarPlugin");
class oa extends L {
    constructor(t){
        super();
        p(this, "view");
        p(this, "closeMenu", ()=>this.view.closeMenu());
        this.addProsemirrorPlugin(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Plugin"]({
            key: na,
            view: (o)=>(this.view = new ta(t, o, (r)=>{
                    this.emit("update", r);
                }), this.view),
            props: {
                handleKeyDown: (o, r)=>r.key === "Escape" && this.shown ? (this.view.closeMenu(), !0) : !1
            }
        }));
    }
    static key() {
        return "formattingToolbar";
    }
    get shown() {
        var t, o;
        return ((o = (t = this.view) == null ? void 0 : t.state) == null ? void 0 : o.show) || !1;
    }
    onUpdate(t) {
        return this.on("update", t);
    }
}
const ra = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Node"].create({
    name: "hardBreak",
    inline: !0,
    group: "inline",
    selectable: !1,
    linebreakReplacement: !0,
    priority: 10,
    parseHTML () {
        return [
            {
                tag: "br"
            }
        ];
    },
    renderHTML ({ HTMLAttributes: e }) {
        return [
            "br",
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeAttributes"])(this.options.HTMLAttributes, e)
        ];
    },
    renderText () {
        return `
`;
    }
}), Qt = (e, n)=>{
    const t = e.resolve(n);
    if (t.depth <= 1) return;
    const o = t.posAtIndex(t.index(t.depth - 1), t.depth - 1);
    return Te(e.resolve(o));
}, Oe = (e, n)=>{
    const t = e.resolve(n), o = t.index();
    if (o === 0) return;
    const r = t.posAtIndex(o - 1);
    return Te(e.resolve(r));
}, to = (e, n)=>{
    for(; n.childContainer;){
        const t = n.childContainer.node, o = e.resolve(n.childContainer.beforePos + 1).posAtIndex(t.childCount - 1);
        n = Te(e.resolve(o));
    }
    return n;
}, sa = (e, n)=>e.isBlockContainer && e.blockContent.node.type.spec.content === "inline*" && e.blockContent.node.childCount > 0 && n.isBlockContainer && n.blockContent.node.type.spec.content === "inline*", ia = (e, n, t, o)=>{
    if (!o.isBlockContainer) throw new Error(`Attempted to merge block at position ${o.bnBlock.beforePos} into previous block at position ${t.bnBlock.beforePos}, but next block is not a block container`);
    if (o.childContainer) {
        const r = e.doc.resolve(o.childContainer.beforePos + 1), s = e.doc.resolve(o.childContainer.afterPos - 1), i = r.blockRange(s);
        if (n) {
            const a = e.doc.resolve(o.bnBlock.beforePos);
            e.tr.lift(i, a.depth);
        }
    }
    if (n) {
        if (!t.isBlockContainer) throw new Error(`Attempted to merge block at position ${o.bnBlock.beforePos} into previous block at position ${t.bnBlock.beforePos}, but previous block is not a block container`);
        n(e.tr.delete(t.blockContent.afterPos - 1, o.blockContent.beforePos + 1));
    }
    return !0;
}, en = (e)=>({ state: n, dispatch: t })=>{
        const o = n.doc.resolve(e), r = Te(o), s = Oe(n.doc, r.bnBlock.beforePos);
        if (!s) return !1;
        const i = to(n.doc, s);
        return sa(i, r) ? ia(n, t, i, r) : !1;
    }, aa = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Extension"].create({
    priority: 50,
    // TODO: The shortcuts need a refactor. Do we want to use a command priority
    //  design as there is now, or clump the logic into a single function?
    addKeyboardShortcuts () {
        const e = ()=>this.editor.commands.first(({ chain: o, commands: r })=>[
                    // Deletes the selection if it's not empty.
                    ()=>r.deleteSelection(),
                    // Undoes an input rule if one was triggered in the last editor state change.
                    ()=>r.undoInputRule(),
                    // Reverts block content type to a paragraph if the selection is at the start of the block.
                    ()=>r.command(({ state: s })=>{
                            const i = v(s);
                            if (!i.isBlockContainer) return !1;
                            const a = s.selection.from === i.blockContent.beforePos + 1, c = i.blockContent.node.type.name === "paragraph";
                            return a && !c ? r.command(I(i.bnBlock.beforePos, {
                                type: "paragraph",
                                props: {}
                            })) : !1;
                        }),
                    // Removes a level of nesting if the block is indented if the selection is at the start of the block.
                    ()=>r.command(({ state: s })=>{
                            const i = v(s);
                            if (!i.isBlockContainer) return !1;
                            const { blockContent: a } = i;
                            return s.selection.from === a.beforePos + 1 ? r.liftListItem("blockContainer") : !1;
                        }),
                    // Merges block with the previous one if it isn't indented, and the selection is at the start of the
                    // block. The target block for merging must contain inline content.
                    ()=>r.command(({ state: s })=>{
                            const i = v(s);
                            if (!i.isBlockContainer) return !1;
                            const { bnBlock: a, blockContent: c } = i, l = s.selection.from === c.beforePos + 1, d = s.selection.empty, u = a.beforePos;
                            return l && d ? o().command(en(u)).scrollIntoView().run() : !1;
                        }),
                    ()=>r.command(({ state: s, dispatch: i })=>{
                            const a = v(s);
                            if (!a.isBlockContainer || !(s.selection.from === a.blockContent.beforePos + 1) || Oe(s.doc, a.bnBlock.beforePos)) return !1;
                            const d = Qt(s.doc, a.bnBlock.beforePos);
                            if ((d == null ? void 0 : d.blockNoteType) !== "column") return !1;
                            const u = d, h = Qt(s.doc, u.bnBlock.beforePos);
                            if ((h == null ? void 0 : h.blockNoteType) !== "columnList") throw new Error("parent of column is not a column list");
                            const f = u.childContainer.node.childCount === 1, m = f && h.childContainer.node.childCount === 2, g = h.childContainer.node.firstChild === u.bnBlock.node;
                            if (i) {
                                const b = s.doc.slice(a.bnBlock.beforePos, a.bnBlock.afterPos, !1);
                                if (m) if (g) {
                                    s.tr.step(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$transform$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReplaceAroundStep"](// replace entire column list
                                    h.bnBlock.beforePos, h.bnBlock.afterPos, // select content of remaining column:
                                    u.bnBlock.afterPos + 1, h.bnBlock.afterPos - 2, b, b.size, // append existing content to blockToMove
                                    !1));
                                    const k = s.tr.doc.resolve(u.bnBlock.beforePos);
                                    s.tr.setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextSelection"].between(k, k));
                                } else {
                                    s.tr.step(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$transform$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReplaceAroundStep"](// replace entire column list
                                    h.bnBlock.beforePos, h.bnBlock.afterPos, // select content of existing column:
                                    h.bnBlock.beforePos + 2, u.bnBlock.beforePos - 1, b, 0, // prepend existing content to blockToMove
                                    !1));
                                    const k = s.tr.doc.resolve(s.tr.mapping.map(u.bnBlock.beforePos - 1));
                                    s.tr.setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextSelection"].between(k, k));
                                }
                                else if (f) if (g) {
                                    s.tr.delete(u.bnBlock.beforePos, u.bnBlock.afterPos), s.tr.insert(h.bnBlock.beforePos, b.content);
                                    const k = s.tr.doc.resolve(h.bnBlock.beforePos);
                                    s.tr.setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextSelection"].between(k, k));
                                } else s.tr.delete(u.bnBlock.beforePos - 1, u.bnBlock.beforePos + 1);
                                else {
                                    s.tr.delete(a.bnBlock.beforePos, a.bnBlock.afterPos), g ? s.tr.insert(h.bnBlock.beforePos - 1, b.content) : s.tr.insert(u.bnBlock.beforePos - 1, b.content);
                                    const k = s.tr.doc.resolve(u.bnBlock.beforePos - 1);
                                    s.tr.setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextSelection"].between(k, k));
                                }
                            }
                            return !0;
                        }),
                    // Deletes the current block if it's an empty block with inline content,
                    // and moves the selection to the previous block.
                    ()=>r.command(({ state: s })=>{
                            const i = v(s);
                            if (!i.isBlockContainer) return !1;
                            if (i.blockContent.node.childCount === 0 && i.blockContent.node.type.spec.content === "inline*") {
                                const c = Oe(s.doc, i.bnBlock.beforePos);
                                if (!c || !c.isBlockContainer) return !1;
                                let l = o();
                                if (c.blockContent.node.type.spec.content === "tableRow+") {
                                    const m = i.bnBlock.beforePos - 1 - 1 - 1 - 1 - 1;
                                    l = l.setTextSelection(m);
                                } else if (c.blockContent.node.type.spec.content === "") {
                                    const d = c.blockContent.afterPos - c.blockContent.node.nodeSize;
                                    l = l.setNodeSelection(d);
                                } else {
                                    const d = c.blockContent.afterPos - c.blockContent.node.nodeSize;
                                    l = l.setTextSelection(d);
                                }
                                return l.deleteRange({
                                    from: i.bnBlock.beforePos,
                                    to: i.bnBlock.afterPos
                                }).scrollIntoView().run();
                            }
                            return !1;
                        }),
                    // Deletes previous block if it contains no content and isn't a table,
                    // when the selection is empty and at the start of the block. Moves the
                    // current block into the deleted block's place.
                    ()=>r.command(({ state: s })=>{
                            const i = v(s);
                            if (!i.isBlockContainer) throw new Error("todo");
                            const a = s.selection.from === i.blockContent.beforePos + 1, c = s.selection.empty, l = Oe(s.doc, i.bnBlock.beforePos);
                            if (l && a && c) {
                                const d = to(s.doc, l);
                                if (!d.isBlockContainer) throw new Error("todo");
                                if (d.blockContent.node.type.spec.content === "" || d.blockContent.node.type.spec.content === "inline*" && d.blockContent.node.childCount === 0) return o().cut({
                                    from: i.bnBlock.beforePos,
                                    to: i.bnBlock.afterPos
                                }, d.bnBlock.afterPos).deleteRange({
                                    from: d.bnBlock.beforePos,
                                    to: d.bnBlock.afterPos
                                }).run();
                            }
                            return !1;
                        })
                ]), n = ()=>this.editor.commands.first(({ commands: o })=>[
                    // Deletes the selection if it's not empty.
                    ()=>o.deleteSelection(),
                    // Merges block with the next one (at the same nesting level or lower),
                    // if one exists, the block has no children, and the selection is at the
                    // end of the block.
                    ()=>o.command(({ state: r })=>{
                            const s = v(r);
                            if (!s.isBlockContainer) return !1;
                            const { bnBlock: i, blockContent: a, childContainer: c } = s, { depth: l } = r.doc.resolve(i.beforePos), d = i.afterPos === r.doc.nodeSize - 3, u = r.selection.from === a.afterPos - 1, h = r.selection.empty;
                            if (!d && u && h && !(c !== void 0)) {
                                let m = l, g = i.afterPos + 1, b = r.doc.resolve(g).depth;
                                for(; b < m;)m = b, g += 2, b = r.doc.resolve(g).depth;
                                return o.command(en(g - 1));
                            }
                            return !1;
                        })
                ]), t = (o = !1)=>this.editor.commands.first(({ commands: r })=>[
                    // Removes a level of nesting if the block is empty & indented, while the selection is also empty & at the start
                    // of the block.
                    ()=>r.command(({ state: s })=>{
                            const i = v(s);
                            if (!i.isBlockContainer) return !1;
                            const { bnBlock: a, blockContent: c } = i, { depth: l } = s.doc.resolve(a.beforePos), d = s.selection.$anchor.parentOffset === 0, u = s.selection.anchor === s.selection.head, h = c.node.childCount === 0, f = l > 1;
                            return d && u && h && f ? r.liftListItem("blockContainer") : !1;
                        }),
                    // Creates a hard break if block is configured to do so.
                    ()=>r.command(({ state: s })=>{
                            const i = v(s), a = this.options.editor.schema.blockSchema[i.blockNoteType].hardBreakShortcut ?? "shift+enter";
                            return a === "none" ? !1 : // If shortcut is not configured, or is configured as "shift+enter",
                            // create a hard break for shift+enter, but not for enter.
                            a === "shift+enter" && o || // If shortcut is configured as "enter", create a hard break for
                            // both enter and shift+enter.
                            a === "enter" ? r.insertContent({
                                type: "hardBreak"
                            }) : !1;
                        }),
                    // Creates a new block and moves the selection to it if the current one is empty, while the selection is also
                    // empty & at the start of the block.
                    ()=>r.command(({ state: s, dispatch: i })=>{
                            const a = v(s);
                            if (!a.isBlockContainer) return !1;
                            const { bnBlock: c, blockContent: l } = a, d = s.selection.$anchor.parentOffset === 0, u = s.selection.anchor === s.selection.head, h = l.node.childCount === 0;
                            if (d && u && h) {
                                const f = c.afterPos, m = f + 2;
                                if (i) {
                                    const g = s.schema.nodes.blockContainer.createAndFill();
                                    s.tr.insert(f, g).scrollIntoView(), s.tr.setSelection(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextSelection"](s.doc.resolve(m)));
                                }
                                return !0;
                            }
                            return !1;
                        }),
                    // Splits the current block, moving content inside that's after the cursor to a new text block below. Also
                    // deletes the selection beforehand, if it's not empty.
                    ()=>r.command(({ state: s, chain: i })=>{
                            const a = v(s);
                            if (!a.isBlockContainer) return !1;
                            const { blockContent: c } = a, l = s.selection.$anchor.parentOffset === 0;
                            return c.node.childCount === 0 ? !1 : (i().deleteSelection().command(Hn(s.selection.from, l, l)).run(), !0);
                        })
                ]);
        return {
            Backspace: e,
            Delete: n,
            Enter: ()=>t(),
            "Shift-Enter": ()=>t(!0),
            // Always returning true for tab key presses ensures they're not captured by the browser. Otherwise, they blur the
            // editor since the browser will try to use tab for keyboard navigation.
            Tab: ()=>{
                var o, r, s;
                return this.options.tabBehavior !== "prefer-indent" && ((o = this.options.editor.formattingToolbar) != null && o.shown || (r = this.options.editor.linkToolbar) != null && r.shown || (s = this.options.editor.filePanel) != null && s.shown) ? !1 : Gn(this.options.editor);
            },
            "Shift-Tab": ()=>{
                var o, r, s;
                return this.options.tabBehavior !== "prefer-indent" && ((o = this.options.editor.formattingToolbar) != null && o.shown || (r = this.options.editor.linkToolbar) != null && r.shown || (s = this.options.editor.filePanel) != null && s.shown) ? !1 : (this.editor.commands.liftListItem("blockContainer"), !0);
            },
            "Shift-Mod-ArrowUp": ()=>(this.options.editor.moveBlocksUp(), !0),
            "Shift-Mod-ArrowDown": ()=>(this.options.editor.moveBlocksDown(), !0),
            "Mod-z": ()=>this.options.editor.undo(),
            "Mod-y": ()=>this.options.editor.redo(),
            "Shift-Mod-z": ()=>this.options.editor.redo()
        };
    }
});
class ca {
    constructor(n, t, o){
        p(this, "state");
        p(this, "emitUpdate");
        p(this, "menuUpdateTimer");
        p(this, "startMenuUpdateTimer");
        p(this, "stopMenuUpdateTimer");
        p(this, "mouseHoveredLinkMark");
        p(this, "mouseHoveredLinkMarkRange");
        p(this, "keyboardHoveredLinkMark");
        p(this, "keyboardHoveredLinkMarkRange");
        p(this, "linkMark");
        p(this, "linkMarkRange");
        p(this, "mouseOverHandler", (n)=>{
            if (this.mouseHoveredLinkMark = void 0, this.mouseHoveredLinkMarkRange = void 0, this.stopMenuUpdateTimer(), n.target instanceof HTMLAnchorElement && n.target.nodeName === "A") {
                const t = n.target, o = this.pmView.posAtDOM(t, 0) + 1, r = this.pmView.state.doc.resolve(o), s = r.marks();
                for (const i of s)if (i.type.name === this.pmView.state.schema.mark("link").type.name) {
                    this.mouseHoveredLinkMark = i, this.mouseHoveredLinkMarkRange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getMarkRange"])(r, i.type, i.attrs) || void 0;
                    break;
                }
            }
            return this.startMenuUpdateTimer(), !1;
        });
        p(this, "clickHandler", (n)=>{
            var o;
            const t = this.pmView.dom.parentElement;
            // Toolbar is open.
            this.linkMark && // An element is clicked.
            n && n.target && // The clicked element is not the editor.
            !(t === n.target || t.contains(n.target)) && (o = this.state) != null && o.show && (this.state.show = !1, this.emitUpdate());
        });
        p(this, "scrollHandler", ()=>{
            var n;
            this.linkMark !== void 0 && (n = this.state) != null && n.show && (this.state.referencePos = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["posToDOMRect"])(this.pmView, this.linkMarkRange.from, this.linkMarkRange.to), this.emitUpdate());
        });
        p(this, "closeMenu", ()=>{
            var n;
            (n = this.state) != null && n.show && (this.state.show = !1, this.emitUpdate());
        });
        this.editor = n, this.pmView = t, this.emitUpdate = ()=>{
            if (!this.state) throw new Error("Attempting to update uninitialized link toolbar");
            o(this.state);
        }, this.startMenuUpdateTimer = ()=>{
            this.menuUpdateTimer = setTimeout(()=>{
                this.update(this.pmView, void 0, !0);
            }, 250);
        }, this.stopMenuUpdateTimer = ()=>(this.menuUpdateTimer && (clearTimeout(this.menuUpdateTimer), this.menuUpdateTimer = void 0), !1), this.pmView.dom.addEventListener("mouseover", this.mouseOverHandler), this.pmView.root.addEventListener("click", this.clickHandler, !0), this.pmView.root.addEventListener("scroll", this.scrollHandler, !0);
    }
    editLink(n, t) {
        var o;
        this.editor.transact((r)=>{
            const s = M(r);
            r.insertText(t, this.linkMarkRange.from, this.linkMarkRange.to), r.addMark(this.linkMarkRange.from, this.linkMarkRange.from + t.length, s.mark("link", {
                href: n
            }));
        }), this.pmView.focus(), (o = this.state) != null && o.show && (this.state.show = !1, this.emitUpdate());
    }
    deleteLink() {
        var n;
        this.editor.transact((t)=>t.removeMark(this.linkMarkRange.from, this.linkMarkRange.to, this.linkMark.type).setMeta("preventAutolink", !0)), this.pmView.focus(), (n = this.state) != null && n.show && (this.state.show = !1, this.emitUpdate());
    }
    update(n, t, o = !1) {
        var a;
        const { state: r } = n;
        if (t && t.selection.from === r.selection.from && t.selection.to === r.selection.to || !this.pmView.hasFocus()) return;
        const i = this.linkMark;
        if (this.linkMark = void 0, this.linkMarkRange = void 0, this.keyboardHoveredLinkMark = void 0, this.keyboardHoveredLinkMarkRange = void 0, this.pmView.state.selection.empty) {
            const c = this.pmView.state.selection.$from.marks();
            for (const l of c)if (l.type.name === this.pmView.state.schema.mark("link").type.name) {
                this.keyboardHoveredLinkMark = l, this.keyboardHoveredLinkMarkRange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getMarkRange"])(this.pmView.state.selection.$from, l.type, l.attrs) || void 0;
                break;
            }
        }
        if (this.mouseHoveredLinkMark && o && (this.linkMark = this.mouseHoveredLinkMark, this.linkMarkRange = this.mouseHoveredLinkMarkRange), this.keyboardHoveredLinkMark && (this.linkMark = this.keyboardHoveredLinkMark, this.linkMarkRange = this.keyboardHoveredLinkMarkRange), this.linkMark && this.editor.isEditable) {
            this.state = {
                show: !0,
                referencePos: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["posToDOMRect"])(this.pmView, this.linkMarkRange.from, this.linkMarkRange.to),
                url: this.linkMark.attrs.href,
                text: this.pmView.state.doc.textBetween(this.linkMarkRange.from, this.linkMarkRange.to)
            }, this.emitUpdate();
            return;
        }
        if ((a = this.state) != null && a.show && i && (!this.linkMark || !this.editor.isEditable)) {
            this.state.show = !1, this.emitUpdate();
            return;
        }
    }
    destroy() {
        this.pmView.dom.removeEventListener("mouseover", this.mouseOverHandler), this.pmView.root.removeEventListener("scroll", this.scrollHandler, !0), this.pmView.root.removeEventListener("click", this.clickHandler, !0);
    }
}
const la = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PluginKey"]("LinkToolbarPlugin");
class da extends L {
    constructor(t){
        super();
        p(this, "view");
        /**
     * Edit the currently hovered link.
     */ p(this, "editLink", (t, o)=>{
            this.view.editLink(t, o);
        });
        /**
     * Delete the currently hovered link.
     */ p(this, "deleteLink", ()=>{
            this.view.deleteLink();
        });
        /**
     * When hovering on/off links using the mouse cursor, the link toolbar will
     * open & close with a delay.
     *
     * This function starts the delay timer, and should be used for when the mouse
     * cursor enters the link toolbar.
     */ p(this, "startHideTimer", ()=>{
            this.view.startMenuUpdateTimer();
        });
        /**
     * When hovering on/off links using the mouse cursor, the link toolbar will
     * open & close with a delay.
     *
     * This function stops the delay timer, and should be used for when the mouse
     * cursor exits the link toolbar.
     */ p(this, "stopHideTimer", ()=>{
            this.view.stopMenuUpdateTimer();
        });
        p(this, "closeMenu", ()=>this.view.closeMenu());
        this.addProsemirrorPlugin(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Plugin"]({
            key: la,
            view: (o)=>(this.view = new ca(t, o, (r)=>{
                    this.emit("update", r);
                }), this.view),
            props: {
                handleKeyDown: (o, r)=>r.key === "Escape" && this.shown ? (this.view.closeMenu(), !0) : !1
            }
        }));
    }
    static key() {
        return "linkToolbar";
    }
    onUpdate(t) {
        return this.on("update", t);
    }
    get shown() {
        var t, o;
        return ((o = (t = this.view) == null ? void 0 : t.state) == null ? void 0 : o.show) || !1;
    }
}
const ua = [
    "http",
    "https",
    "ftp",
    "ftps",
    "mailto",
    "tel",
    "callto",
    "sms",
    "cid",
    "xmpp"
], pa = "https", ha = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PluginKey"]("node-selection-keyboard");
class fa extends L {
    static key() {
        return "nodeSelectionKeyboard";
    }
    constructor(){
        super(), this.addProsemirrorPlugin(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Plugin"]({
            key: ha,
            props: {
                handleKeyDown: (n, t)=>{
                    if ("node" in n.state.selection) {
                        if (t.ctrlKey || t.metaKey) return !1;
                        if (t.key.length === 1) return t.preventDefault(), !0;
                        if (t.key === "Enter" && !t.shiftKey && !t.altKey && !t.ctrlKey && !t.metaKey) {
                            const o = n.state.tr;
                            return n.dispatch(o.insert(n.state.tr.selection.$to.after(), n.state.schema.nodes.paragraph.createChecked()).setSelection(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextSelection"](o.doc.resolve(n.state.tr.selection.$to.after() + 1)))), !0;
                        }
                    }
                    return !1;
                }
            }
        }));
    }
}
const ma = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PluginKey"]("blocknote-placeholder");
class ga extends L {
    static key() {
        return "placeholder";
    }
    constructor(n, t){
        super(), this.addProsemirrorPlugin(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Plugin"]({
            key: ma,
            view: (o)=>{
                var l, d;
                const r = `placeholder-selector-${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])()}`;
                o.dom.classList.add(r);
                const s = document.createElement("style"), i = n._tiptapEditor.options.injectNonce;
                i && s.setAttribute("nonce", i), ((l = n.prosemirrorView) == null ? void 0 : l.root) instanceof ShadowRoot ? n.prosemirrorView.root.append(s) : (d = n.prosemirrorView) == null || d.root.head.appendChild(s);
                const a = s.sheet, c = (u = "")=>`.${r} .bn-block-content${u} .bn-inline-content:has(> .ProseMirror-trailingBreak:only-child):before`;
                try {
                    const { default: u, emptyDocument: h, ...f } = t;
                    for (const [b, k] of Object.entries(f)){
                        const w = `[data-content-type="${b}"]`;
                        a.insertRule(`${c(w)} { content: ${JSON.stringify(k)}; }`);
                    }
                    const m = "[data-is-only-empty-block]", g = "[data-is-empty-and-focused]";
                    a.insertRule(`${c(m)} { content: ${JSON.stringify(h)}; }`), a.insertRule(`${c(g)} { content: ${JSON.stringify(u)}; }`);
                } catch (u) {
                    console.warn("Failed to insert placeholder CSS rule - this is likely due to the browser not supporting certain CSS pseudo-element selectors (:has, :only-child:, or :before)", u);
                }
                return {
                    destroy: ()=>{
                        var u, h;
                        ((u = n.prosemirrorView) == null ? void 0 : u.root) instanceof ShadowRoot ? n.prosemirrorView.root.removeChild(s) : (h = n.prosemirrorView) == null || h.root.head.removeChild(s);
                    }
                };
            },
            props: {
                decorations: (o)=>{
                    const { doc: r, selection: s } = o;
                    if (!n.isEditable || !s.empty || s.$from.parent.type.spec.code) return;
                    const i = [];
                    o.doc.content.size === 6 && i.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Decoration"].node(2, 4, {
                        "data-is-only-empty-block": "true"
                    }));
                    const a = s.$anchor, c = a.parent;
                    if (c.content.size === 0) {
                        const l = a.before();
                        i.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Decoration"].node(l, l + c.nodeSize, {
                            "data-is-empty-and-focused": "true"
                        }));
                    }
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecorationSet"].create(r, i);
                }
            }
        }));
    }
}
const tn = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PluginKey"]("previous-blocks"), ba = {
    // Numbered List Items
    index: "index",
    // Headings
    level: "level",
    // All Blocks
    type: "type",
    depth: "depth",
    "depth-change": "depth-change"
};
class ka extends L {
    static key() {
        return "previousBlockType";
    }
    constructor(){
        super();
        let n;
        this.addProsemirrorPlugin(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Plugin"]({
            key: tn,
            view (t) {
                return {
                    update: async (o, r)=>{
                        var s;
                        ((s = this.key) == null ? void 0 : s.getState(o.state).updatedBlocks.size) > 0 && (n = setTimeout(()=>{
                            o.dispatch(o.state.tr.setMeta(tn, {
                                clearUpdate: !0
                            }));
                        }, 0));
                    },
                    destroy: ()=>{
                        n && clearTimeout(n);
                    }
                };
            },
            state: {
                init () {
                    return {
                        // Block attributes, by block ID, from just before the previous transaction.
                        prevTransactionOldBlockAttrs: {},
                        // Block attributes, by block ID, from just before the current transaction.
                        currentTransactionOldBlockAttrs: {},
                        // Set of IDs of blocks whose attributes changed from the current transaction.
                        updatedBlocks: /* @__PURE__ */ new Set()
                    };
                },
                apply (t, o, r, s) {
                    if (o.currentTransactionOldBlockAttrs = {}, o.updatedBlocks.clear(), !t.docChanged || r.doc.eq(s.doc)) return o;
                    const i = {}, a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findChildren"])(r.doc, (d)=>d.attrs.id), c = new Map(a.map((d)=>[
                            d.node.attrs.id,
                            d
                        ])), l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findChildren"])(s.doc, (d)=>d.attrs.id);
                    for (const d of l){
                        const u = c.get(d.node.attrs.id), h = u == null ? void 0 : u.node.firstChild, f = d.node.firstChild;
                        if (u && h && f) {
                            const m = {
                                index: f.attrs.index,
                                level: f.attrs.level,
                                type: f.type.name,
                                depth: s.doc.resolve(d.pos).depth
                            };
                            let g = {
                                index: h.attrs.index,
                                level: h.attrs.level,
                                type: h.type.name,
                                depth: r.doc.resolve(u.pos).depth
                            };
                            i[d.node.attrs.id] = g, t.getMeta("numberedListIndexing") && (d.node.attrs.id in o.prevTransactionOldBlockAttrs && (g = o.prevTransactionOldBlockAttrs[d.node.attrs.id]), m.type === "numberedListItem" && (g.index = m.index)), o.currentTransactionOldBlockAttrs[d.node.attrs.id] = g, JSON.stringify(g) !== JSON.stringify(m) && (g["depth-change"] = g.depth - m.depth, o.updatedBlocks.add(d.node.attrs.id));
                        }
                    }
                    return o.prevTransactionOldBlockAttrs = i, o;
                }
            },
            props: {
                decorations (t) {
                    const o = this.getState(t);
                    if (o.updatedBlocks.size === 0) return;
                    const r = [];
                    return t.doc.descendants((s, i)=>{
                        if (!s.attrs.id || !o.updatedBlocks.has(s.attrs.id)) return;
                        const a = o.currentTransactionOldBlockAttrs[s.attrs.id], c = {};
                        for (const [d, u] of Object.entries(a))c["data-prev-" + ba[d]] = u || "none";
                        const l = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Decoration"].node(i, i + s.nodeSize, {
                            ...c
                        });
                        r.push(l);
                    }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecorationSet"].create(t.doc, r);
                }
            }
        }));
    }
}
const nn = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PluginKey"]("blocknote-show-selection");
class wa extends L {
    constructor(t){
        super();
        p(this, "enabled", !1);
        this.editor = t, this.addProsemirrorPlugin(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Plugin"]({
            key: nn,
            props: {
                decorations: (o)=>{
                    const { doc: r, selection: s } = o;
                    if (!this.enabled) return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecorationSet"].empty;
                    const i = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Decoration"].inline(s.from, s.to, {
                        "data-show-selection": "true"
                    });
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecorationSet"].create(r, [
                        i
                    ]);
                }
            }
        }));
    }
    static key() {
        return "showSelection";
    }
    setEnabled(t) {
        this.enabled !== t && (this.enabled = t, this.editor.transact((o)=>o.setMeta(nn, {})));
    }
    getEnabled() {
        return this.enabled;
    }
}
function no(e, n) {
    var t, o;
    for(; e && e.parentElement && e.parentElement !== n.dom && ((t = e.getAttribute) == null ? void 0 : t.call(e, "data-node-type")) !== "blockContainer";)e = e.parentElement;
    if (((o = e.getAttribute) == null ? void 0 : o.call(e, "data-node-type")) === "blockContainer") return {
        node: e,
        id: e.getAttribute("data-id")
    };
}
class ae extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Selection"] {
    constructor(t, o){
        super(t, o);
        p(this, "nodes");
        const r = t.node();
        this.nodes = [], t.doc.nodesBetween(t.pos, o.pos, (s, i, a)=>{
            if (a !== null && a.eq(r)) return this.nodes.push(s), !1;
        });
    }
    static create(t, o, r = o) {
        return new ae(t.resolve(o), t.resolve(r));
    }
    content() {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Slice"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"].from(this.nodes), 0, 0);
    }
    eq(t) {
        if (!(t instanceof ae) || this.nodes.length !== t.nodes.length || this.from !== t.from || this.to !== t.to) return !1;
        for(let o = 0; o < this.nodes.length; o++)if (!this.nodes[o].eq(t.nodes[o])) return !1;
        return !0;
    }
    map(t, o) {
        const r = o.mapResult(this.from), s = o.mapResult(this.to);
        return s.deleted ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Selection"].near(t.resolve(r.pos)) : r.deleted ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Selection"].near(t.resolve(s.pos)) : new ae(t.resolve(r.pos), t.resolve(s.pos));
    }
    toJSON() {
        return {
            type: "multiple-node",
            anchor: this.anchor,
            head: this.head
        };
    }
}
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Selection"].jsonID("multiple-node", ae);
let z;
function ya(e, n) {
    let t, o;
    const r = n.resolve(e.from).node().type.spec.group === "blockContent", s = n.resolve(e.to).node().type.spec.group === "blockContent", i = Math.min(e.$anchor.depth, e.$head.depth);
    if (r && s) {
        const a = e.$from.start(i - 1), c = e.$to.end(i - 1);
        t = n.resolve(a - 1).pos, o = n.resolve(c + 1).pos;
    } else t = e.from, o = e.to;
    return {
        from: t,
        to: o
    };
}
function on(e, n, t = n) {
    n === t && (t += e.state.doc.resolve(n + 1).node().nodeSize);
    const o = e.domAtPos(n).node.cloneNode(!0), r = e.domAtPos(n).node, s = (u, h)=>Array.prototype.indexOf.call(u.children, h), i = s(r, // Expects from position to be just before the first selected block.
    e.domAtPos(n + 1).node.parentElement), a = s(r, // Expects to position to be just after the last selected block.
    e.domAtPos(t - 1).node.parentElement);
    for(let u = r.childElementCount - 1; u >= 0; u--)(u > a || u < i) && o.removeChild(o.children[u]);
    oo(e.root), z = o;
    const c = z.getElementsByTagName("iframe");
    for(let u = 0; u < c.length; u++){
        const h = c[u], f = h.parentElement;
        f && f.removeChild(h);
    }
    const d = e.dom.className.split(" ").filter((u)=>u !== "ProseMirror" && u !== "bn-root" && u !== "bn-editor").join(" ");
    z.className = z.className + " bn-drag-preview " + d, e.root instanceof ShadowRoot ? e.root.appendChild(z) : e.root.body.appendChild(z);
}
function oo(e) {
    z !== void 0 && (e instanceof ShadowRoot ? e.removeChild(z) : e.body.removeChild(z), z = void 0);
}
function Ca(e, n, t) {
    if (!e.dataTransfer) return;
    const o = t.prosemirrorView;
    if (!o) return;
    const r = _(n.id, o.state.doc);
    if (!r) throw new Error(`Block with ID ${n.id} not found`);
    const s = r.posBeforeNode;
    if (s != null) {
        const i = o.state.selection, a = o.state.doc, { from: c, to: l } = ya(i, a), d = c <= s && s < l, u = i.$anchor.node() !== i.$head.node() || i instanceof ae;
        d && u ? (o.dispatch(o.state.tr.setSelection(ae.create(a, c, l))), on(o, c, l)) : (o.dispatch(o.state.tr.setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeSelection"].create(o.state.doc, s))), on(o, s));
        const h = o.state.selection.content(), f = t.pmSchema, m = o.serializeForClipboard(h).dom.innerHTML, g = Ke(f, t), b = Zn(h.content), k = g.exportBlocks(b, {}), w = Mt(k);
        e.dataTransfer.clearData(), e.dataTransfer.setData("blocknote/html", m), e.dataTransfer.setData("text/html", k), e.dataTransfer.setData("text/plain", w), e.dataTransfer.effectAllowed = "move", e.dataTransfer.setDragImage(z, 0, 0);
    }
}
const we = 0.1;
function mt(e, n, t, o = !0) {
    const r = e.root.elementsFromPoint(// bit hacky - offset x position to right to account for the width of sidemenu itself
    n.left + (t === "editor" ? 50 : 0), n.top);
    for (const s of r)if (e.dom.contains(s)) return o && s.closest("[data-node-type=columnList]") ? mt(e, {
        left: n.left + 50,
        // bit hacky, but if we're inside a column, offset x position to right to account for the width of sidemenu itself
        top: n.top
    }, t, !1) : no(s, e);
}
function va(e, n, t) {
    if (!n.dom.firstChild) return;
    const o = n.dom.firstChild.getBoundingClientRect(), r = {
        left: e.x,
        top: e.y
    }, s = r.left < o.left, i = r.left > o.right;
    t === "viewport" && (s && (r.left = o.left + 10), i && (r.left = o.right - 10));
    let a = mt(n, r, t);
    if (!i && a) {
        const c = a.node.getBoundingClientRect();
        r.left = c.right - 10, a = mt(n, r, "viewport", !1);
    }
    return a;
}
class Ea {
    constructor(n, t, o, r){
        p(this, "state");
        p(this, "emitUpdate");
        p(this, "mousePos");
        p(this, "hoveredBlock");
        p(this, "menuFrozen", !1);
        p(this, "isDragOrigin", !1);
        p(this, "updateState", (n)=>{
            this.state = n, this.emitUpdate(this.state);
        });
        p(this, "updateStateFromMousePos", ()=>{
            var o, r, s, i;
            if (this.menuFrozen || !this.mousePos) return;
            const n = va(this.mousePos, this.pmView, this.sideMenuDetection);
            if (!n || !this.editor.isEditable) {
                (o = this.state) != null && o.show && (this.state.show = !1, this.updateState(this.state));
                return;
            }
            if ((r = this.state) != null && r.show && (s = this.hoveredBlock) != null && s.hasAttribute("data-id") && ((i = this.hoveredBlock) == null ? void 0 : i.getAttribute("data-id")) === n.id) return;
            this.hoveredBlock = n.node;
            const t = n.node.firstChild;
            if (t && this.editor.isEditable) {
                const a = t.getBoundingClientRect(), c = n.node.closest("[data-node-type=column]");
                this.updateState({
                    show: !0,
                    referencePos: new DOMRect(c ? // We take the first child as column elements have some default
                    // padding. This is a little weird since this child element will
                    // be the first block, but since it's always non-nested and we
                    // only take the x coordinate, it's ok.
                    c.firstElementChild.getBoundingClientRect().x : this.pmView.dom.firstChild.getBoundingClientRect().x, a.y, a.width, a.height),
                    block: this.editor.getBlock(this.hoveredBlock.getAttribute("data-id"))
                });
            }
        });
        p(this, "onDrop", (n)=>{
            var r, s;
            if (this.pmView.dragging === null) return;
            this.editor._tiptapEditor.commands.blur();
            const t = n.target instanceof Node && ((r = n.target instanceof HTMLElement ? n.target : n.target.parentElement) == null ? void 0 : r.closest(".bn-editor")) || null;
            if (t && (!this.isDragOrigin && this.pmView.dom === t ? this.pmView.dispatch(this.pmView.state.tr.setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextSelection"].create(this.pmView.state.tr.doc, this.pmView.state.tr.selection.to))) : this.isDragOrigin && this.pmView.dom !== t && setTimeout(()=>this.pmView.dispatch(this.pmView.state.tr.deleteSelection()), 0)), this.sideMenuDetection === "editor" || n.synthetic || !((s = n.dataTransfer) != null && s.types.includes("blocknote/html"))) return;
            const o = this.pmView.posAtCoords({
                left: n.clientX,
                top: n.clientY
            });
            if (!o || o.inside === -1) {
                const i = this.createSyntheticEvent(n);
                this.pmView.dom.dispatchEvent(i);
            }
        });
        p(this, "onDragEnd", ()=>{
            this.pmView.dragging = null;
        });
        /**
     * If a block is being dragged, ProseMirror usually gets the context of what's
     * being dragged from `view.dragging`, which is automatically set when a
     * `dragstart` event fires in the editor. However, if the user tries to drag
     * and drop blocks between multiple editors, only the one in which the drag
     * began has that context, so we need to set it on the others manually. This
     * ensures that PM always drops the blocks in between other blocks, and not
     * inside them.
     *
     * After the `dragstart` event fires on the drag handle, it sets
     * `blocknote/html` data on the clipboard. This handler fires right after,
     * parsing the `blocknote/html` data into nodes and setting them on
     * `view.dragging`.
     *
     * Note: Setting `view.dragging` on `dragover` would be better as the user
     * could then drag between editors in different windows, but you can only
     * access `dataTransfer` contents on `dragstart` and `drop` events.
     */ p(this, "onDragStart", (n)=>{
            var i;
            const t = (i = n.dataTransfer) == null ? void 0 : i.getData("blocknote/html");
            if (!t) return;
            if (this.pmView.dragging) throw new Error("New drag was started while an existing drag is ongoing");
            const o = document.createElement("div");
            o.innerHTML = t;
            const s = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DOMParser"].fromSchema(this.pmView.state.schema).parse(o, {
                topNode: this.pmView.state.schema.nodes.blockGroup.create()
            });
            this.pmView.dragging = {
                slice: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Slice"](s.content, 0, 0),
                move: !0
            };
        });
        /**
     * If the event is outside the editor contents,
     * we dispatch a fake event, so that we can still drop the content
     * when dragging / dropping to the side of the editor
     */ p(this, "onDragOver", (n)=>{
            var o;
            if (this.sideMenuDetection === "editor" || n.synthetic || !((o = n.dataTransfer) != null && o.types.includes("blocknote/html"))) return;
            const t = this.pmView.posAtCoords({
                left: n.clientX,
                top: n.clientY
            });
            if (!t || t.inside === -1 && this.pmView.dom.firstChild) {
                const r = this.createSyntheticEvent(n);
                this.pmView.dom.dispatchEvent(r);
            }
        });
        p(this, "onKeyDown", (n)=>{
            var t;
            (t = this.state) != null && t.show && this.editor.isFocused() && (this.state.show = !1, this.emitUpdate(this.state));
        });
        p(this, "onMouseMove", (n)=>{
            var s;
            if (this.menuFrozen) return;
            this.mousePos = {
                x: n.clientX,
                y: n.clientY
            };
            const t = this.pmView.dom.getBoundingClientRect(), o = this.mousePos.x > t.left && this.mousePos.x < t.right && this.mousePos.y > t.top && this.mousePos.y < t.bottom, r = this.pmView.dom.parentElement;
            if (// Cursor is within the editor area
            o && // An element is hovered
            n && n.target && // Element is outside the editor
            !(r === n.target || r.contains(n.target))) {
                (s = this.state) != null && s.show && (this.state.show = !1, this.emitUpdate(this.state));
                return;
            }
            this.updateStateFromMousePos();
        });
        p(this, "onScroll", ()=>{
            var n;
            (n = this.state) != null && n.show && (this.state.referencePos = this.hoveredBlock.getBoundingClientRect(), this.emitUpdate(this.state));
        });
        this.editor = n, this.sideMenuDetection = t, this.pmView = o, this.emitUpdate = ()=>{
            if (!this.state) throw new Error("Attempting to update uninitialized side menu");
            r(this.state);
        }, this.pmView.root.addEventListener("dragstart", this.onDragStart), this.pmView.root.addEventListener("dragover", this.onDragOver), this.pmView.root.addEventListener("drop", this.onDrop, !0), this.pmView.root.addEventListener("dragend", this.onDragEnd, !0), Tt(), this.pmView.root.addEventListener("mousemove", this.onMouseMove, !0), this.pmView.root.addEventListener("keydown", this.onKeyDown, !0), o.root.addEventListener("scroll", this.onScroll, !0);
    }
    createSyntheticEvent(n) {
        const t = new Event(n.type, n), o = this.pmView.dom.firstChild.getBoundingClientRect();
        return t.clientX = n.clientX, t.clientY = n.clientY, n.clientX < o.left && n.clientX > o.left - o.width * we ? t.clientX = o.left + o.width * we / 2 : n.clientX > o.right && n.clientX < o.right + o.width * we ? t.clientX = o.right - o.width * we / 2 : (n.clientX < o.left || n.clientX > o.right) && (t.clientX = o.left + we * o.width * 2), t.clientY = Math.min(Math.max(n.clientY, o.top), o.top + o.height), t.dataTransfer = n.dataTransfer, t.preventDefault = ()=>n.preventDefault(), t.synthetic = !0, t;
    }
    // Needed in cases where the editor state updates without the mouse cursor
    // moving, as some state updates can require a side menu update. For example,
    // adding a button to the side menu which removes the block can cause the
    // block below to jump up into the place of the removed block when clicked,
    // allowing the user to click the button again without moving the cursor. This
    // would otherwise not update the side menu, and so clicking the button again
    // would attempt to remove the same block again, causing an error.
    update(n, t) {
        var r;
        !t.doc.eq(this.pmView.state.doc) && (r = this.state) != null && r.show && this.updateStateFromMousePos();
    }
    destroy() {
        var n;
        (n = this.state) != null && n.show && (this.state.show = !1, this.emitUpdate(this.state)), this.pmView.root.removeEventListener("mousemove", this.onMouseMove, !0), this.pmView.root.removeEventListener("dragstart", this.onDragStart), this.pmView.root.removeEventListener("dragover", this.onDragOver), this.pmView.root.removeEventListener("drop", this.onDrop, !0), this.pmView.root.removeEventListener("dragend", this.onDragEnd, !0), this.pmView.root.removeEventListener("keydown", this.onKeyDown, !0), this.pmView.root.removeEventListener("scroll", this.onScroll, !0);
    }
}
const Sa = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PluginKey"]("SideMenuPlugin");
class Ba extends L {
    constructor(t, o){
        super();
        p(this, "view");
        /**
     * Handles drag & drop events for blocks.
     */ p(this, "blockDragStart", (t, o)=>{
            this.view && (this.view.isDragOrigin = !0), Ca(t, o, this.editor);
        });
        /**
     * Handles drag & drop events for blocks.
     */ p(this, "blockDragEnd", ()=>{
            this.editor.prosemirrorView && oo(this.editor.prosemirrorView.root), this.view && (this.view.isDragOrigin = !1);
        });
        /**
     * Freezes the side menu. When frozen, the side menu will stay
     * attached to the same block regardless of which block is hovered by the
     * mouse cursor.
     */ p(this, "freezeMenu", ()=>{
            this.view.menuFrozen = !0, this.view.state.show = !0, this.view.emitUpdate(this.view.state);
        });
        /**
     * Unfreezes the side menu. When frozen, the side menu will stay
     * attached to the same block regardless of which block is hovered by the
     * mouse cursor.
     */ p(this, "unfreezeMenu", ()=>{
            this.view.menuFrozen = !1, this.view.state.show = !1, this.view.emitUpdate(this.view.state);
        });
        this.editor = t, this.addProsemirrorPlugin(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Plugin"]({
            key: Sa,
            view: (r)=>(this.view = new Ea(t, o, r, (s)=>{
                    this.emit("update", s);
                }), this.view)
        }));
    }
    static key() {
        return "sideMenu";
    }
    onUpdate(t) {
        return this.on("update", t);
    }
}
const Ae = /* @__PURE__ */ new Map();
function xa(e) {
    if (Ae.has(e)) return Ae.get(e);
    const n = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$transform$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Mapping"]();
    return e._tiptapEditor.on("transaction", ({ transaction: t })=>{
        n.appendMapping(t.mapping);
    }), e._tiptapEditor.on("destroy", ()=>{
        Ae.delete(e);
    }), Ae.set(e, n), n;
}
function Ta(e, n, t = "left") {
    const o = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$keys$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ySyncPluginKey"].getState(e._tiptapEditor.state);
    if (!o) {
        const s = xa(e), i = s.maps.length;
        return ()=>s.slice(i).map(n, t === "left" ? -1 : 1);
    }
    const r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$lib$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["absolutePositionToRelativePosition"])(// Track the position after the position if we are on the right side
    n + (t === "right" ? 1 : 0), o.binding.type, o.binding.mapping);
    return ()=>{
        const s = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$keys$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ySyncPluginKey"].getState(e._tiptapEditor.state), i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$lib$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["relativePositionToAbsolutePosition"])(s.doc, s.binding.type, r, s.binding.mapping);
        if (i === null) throw new Error("Position not found, cannot track positions");
        return i + (t === "right" ? -1 : 0);
    };
}
const Ma = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findParentNode"])((e)=>e.type.name === "blockContainer");
class Pa {
    constructor(n, t){
        p(this, "state");
        p(this, "emitUpdate");
        p(this, "rootEl");
        p(this, "pluginState");
        p(this, "handleScroll", ()=>{
            var n, t;
            if ((n = this.state) != null && n.show) {
                const o = (t = this.rootEl) == null ? void 0 : t.querySelector(`[data-decoration-id="${this.pluginState.decorationId}"]`);
                if (!o) return;
                this.state.referencePos = o.getBoundingClientRect(), this.emitUpdate(this.pluginState.triggerCharacter);
            }
        });
        p(this, "closeMenu", ()=>{
            this.editor.transact((n)=>n.setMeta(me, null));
        });
        p(this, "clearQuery", ()=>{
            this.pluginState !== void 0 && this.editor._tiptapEditor.chain().focus().deleteRange({
                from: this.pluginState.queryStartPos() - (this.pluginState.deleteTriggerCharacter ? this.pluginState.triggerCharacter.length : 0),
                to: this.editor.transact((n)=>n.selection.from)
            }).run();
        });
        var o, r;
        this.editor = n, this.pluginState = void 0, this.emitUpdate = (s)=>{
            var i;
            if (!this.state) throw new Error("Attempting to update uninitialized suggestions menu");
            t(s, {
                ...this.state,
                ignoreQueryLength: (i = this.pluginState) == null ? void 0 : i.ignoreQueryLength
            });
        }, this.rootEl = (o = this.editor.prosemirrorView) == null ? void 0 : o.root, (r = this.rootEl) == null || r.addEventListener("scroll", this.handleScroll, !0);
    }
    update(n, t) {
        var l;
        const o = me.getState(t), r = me.getState(n.state), s = o === void 0 && r !== void 0, i = o !== void 0 && r === void 0;
        if (!s && !(o !== void 0 && r !== void 0) && !i) return;
        if (this.pluginState = i ? o : r, i || !this.editor.isEditable) {
            this.state && (this.state.show = !1), this.emitUpdate(this.pluginState.triggerCharacter);
            return;
        }
        const c = (l = this.rootEl) == null ? void 0 : l.querySelector(`[data-decoration-id="${this.pluginState.decorationId}"]`);
        this.editor.isEditable && c && (this.state = {
            show: !0,
            referencePos: c.getBoundingClientRect(),
            query: this.pluginState.query
        }, this.emitUpdate(this.pluginState.triggerCharacter));
    }
    destroy() {
        var n;
        (n = this.rootEl) == null || n.removeEventListener("scroll", this.handleScroll, !0);
    }
}
const me = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PluginKey"]("SuggestionMenuPlugin");
class Ia extends L {
    constructor(t){
        super();
        p(this, "view");
        p(this, "triggerCharacters", []);
        p(this, "addTriggerCharacter", (t)=>{
            this.triggerCharacters.push(t);
        });
        // TODO: Should this be called automatically when listeners are removed?
        p(this, "removeTriggerCharacter", (t)=>{
            this.triggerCharacters = this.triggerCharacters.filter((o)=>o !== t);
        });
        p(this, "closeMenu", ()=>this.view.closeMenu());
        p(this, "clearQuery", ()=>this.view.clearQuery());
        const o = this.triggerCharacters;
        this.addProsemirrorPlugin(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Plugin"]({
            key: me,
            view: ()=>(this.view = new Pa(t, (r, s)=>{
                    this.emit(`update ${r}`, s);
                }), this.view),
            state: {
                // Initialize the plugin's internal state.
                init () {},
                // Apply changes to the plugin state from an editor transaction.
                apply: (r, s, i, a)=>{
                    if (r.getMeta("orderedListIndexing") !== void 0 || r.selection.$from.parent.type.spec.code) return s;
                    const c = r.getMeta(me);
                    if (typeof c == "object" && c !== null) {
                        s && this.closeMenu();
                        const d = Ta(t, a.selection.from - // Need to account for the trigger char that was inserted, so we offset the position by the length of the trigger character.
                        c.triggerCharacter.length);
                        return {
                            triggerCharacter: c.triggerCharacter,
                            deleteTriggerCharacter: c.deleteTriggerCharacter !== !1,
                            // When reading the queryStartPos, we offset the result by the length of the trigger character, to make it easy on the caller
                            queryStartPos: ()=>d() + c.triggerCharacter.length,
                            query: "",
                            decorationId: `id_${Math.floor(Math.random() * **********)}`,
                            ignoreQueryLength: c == null ? void 0 : c.ignoreQueryLength
                        };
                    }
                    if (s === void 0) return s;
                    if (// Highlighting text should hide the menu.
                    a.selection.from !== a.selection.to || // Transactions with plugin metadata should hide the menu.
                    c === null || // Certain mouse events should hide the menu.
                    // TODO: Change to global mousedown listener.
                    r.getMeta("focus") || r.getMeta("blur") || r.getMeta("pointer") || // Moving the caret before the character which triggered the menu should hide it.
                    s.triggerCharacter !== void 0 && a.selection.from < s.queryStartPos() || // Moving the caret to a new block should hide the menu.
                    !a.selection.$from.sameParent(a.doc.resolve(s.queryStartPos()))) return;
                    const l = {
                        ...s
                    };
                    return l.query = a.doc.textBetween(s.queryStartPos(), a.selection.from), l;
                }
            },
            props: {
                handleTextInput (r, s, i, a) {
                    if (s === i) {
                        const c = r.state.doc;
                        for (const l of o){
                            const d = l.length > 1 ? c.textBetween(s - l.length, s) + a : a;
                            if (l === d) return r.dispatch(r.state.tr.insertText(a)), r.dispatch(r.state.tr.setMeta(me, {
                                triggerCharacter: d
                            }).scrollIntoView()), !0;
                        }
                    }
                    return !1;
                },
                // Setup decorator on the currently active suggestion.
                decorations (r) {
                    const s = this.getState(r);
                    if (s === void 0) return null;
                    if (!s.deleteTriggerCharacter) {
                        const i = Ma(r.selection);
                        if (i) return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecorationSet"].create(r.doc, [
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Decoration"].node(i.pos, i.pos + i.node.nodeSize, {
                                nodeName: "span",
                                class: "bn-suggestion-decorator",
                                "data-decoration-id": s.decorationId
                            })
                        ]);
                    }
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecorationSet"].create(r.doc, [
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Decoration"].inline(s.queryStartPos() - s.triggerCharacter.length, s.queryStartPos(), {
                            nodeName: "span",
                            class: "bn-suggestion-decorator",
                            "data-decoration-id": s.decorationId
                        })
                    ]);
                }
            }
        }));
    }
    static key() {
        return "suggestionMenu";
    }
    onUpdate(t, o) {
        return this.triggerCharacters.includes(t) || this.addTriggerCharacter(t), this.on(`update ${t}`, o);
    }
    get shown() {
        var t, o;
        return ((o = (t = this.view) == null ? void 0 : t.state) == null ? void 0 : o.show) || !1;
    }
}
function zc(e, n) {
    e.suggestionMenus.addTriggerCharacter(n);
}
const La = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Mark"].create({
    name: "insertion",
    inclusive: !1,
    excludes: "deletion modification insertion",
    addAttributes () {
        return {
            id: {
                default: null,
                validate: "number"
            }
        };
    },
    extendMarkSchema (e) {
        return e.name !== "insertion" ? {} : {
            blocknoteIgnore: !0,
            inclusive: !1,
            toDOM (n, t) {
                return [
                    "ins",
                    {
                        "data-id": String(n.attrs.id),
                        "data-inline": String(t),
                        ...!t && {
                            style: "display: contents"
                        }
                    },
                    0
                ];
            },
            parseDOM: [
                {
                    tag: "ins",
                    getAttrs (n) {
                        return n.dataset.id ? {
                            id: parseInt(n.dataset.id, 10)
                        } : !1;
                    }
                }
            ]
        };
    }
}), Aa = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Mark"].create({
    name: "deletion",
    inclusive: !1,
    excludes: "insertion modification deletion",
    addAttributes () {
        return {
            id: {
                default: null,
                validate: "number"
            }
        };
    },
    extendMarkSchema (e) {
        return e.name !== "deletion" ? {} : {
            blocknoteIgnore: !0,
            inclusive: !1,
            // attrs: {
            //   id: { validate: "number" },
            // },
            toDOM (n, t) {
                return [
                    "del",
                    {
                        "data-id": String(n.attrs.id),
                        "data-inline": String(t),
                        ...!t && {
                            style: "display: contents"
                        }
                    },
                    0
                ];
            },
            parseDOM: [
                {
                    tag: "del",
                    getAttrs (n) {
                        return n.dataset.id ? {
                            id: parseInt(n.dataset.id, 10)
                        } : !1;
                    }
                }
            ]
        };
    }
}), Na = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Mark"].create({
    name: "modification",
    inclusive: !1,
    excludes: "deletion insertion",
    addAttributes () {
        return {
            id: {
                default: null,
                validate: "number"
            },
            type: {
                validate: "string"
            },
            attrName: {
                default: null,
                validate: "string|null"
            },
            previousValue: {
                default: null
            },
            newValue: {
                default: null
            }
        };
    },
    extendMarkSchema (e) {
        return e.name !== "modification" ? {} : {
            blocknoteIgnore: !0,
            inclusive: !1,
            // attrs: {
            //   id: { validate: "number" },
            //   type: { validate: "string" },
            //   attrName: { default: null, validate: "string|null" },
            //   previousValue: { default: null },
            //   newValue: { default: null },
            // },
            toDOM (n, t) {
                return [
                    t ? "span" : "div",
                    {
                        "data-type": "modification",
                        "data-id": String(n.attrs.id),
                        "data-mod-type": n.attrs.type,
                        "data-mod-prev-val": JSON.stringify(n.attrs.previousValue),
                        // TODO: Try to serialize marks with toJSON?
                        "data-mod-new-val": JSON.stringify(n.attrs.newValue)
                    },
                    0
                ];
            },
            parseDOM: [
                {
                    tag: "span[data-type='modification']",
                    getAttrs (n) {
                        return n.dataset.id ? {
                            id: parseInt(n.dataset.id, 10),
                            type: n.dataset.modType,
                            previousValue: n.dataset.modPrevVal,
                            newValue: n.dataset.modNewVal
                        } : !1;
                    }
                },
                {
                    tag: "div[data-type='modification']",
                    getAttrs (n) {
                        return n.dataset.id ? {
                            id: parseInt(n.dataset.id, 10),
                            type: n.dataset.modType,
                            previousValue: n.dataset.modPrevVal
                        } : !1;
                    }
                }
            ]
        };
    }
});
let R;
function rn(e) {
    R || (R = document.createElement("div"), R.innerHTML = "_", R.style.opacity = "0", R.style.height = "1px", R.style.width = "1px", e instanceof Document ? e.body.appendChild(R) : e.appendChild(R));
}
function Ha(e) {
    R && (e instanceof Document ? e.body.removeChild(R) : e.removeChild(R), R = void 0);
}
function Ne(e) {
    return Array.prototype.indexOf.call(e.parentElement.childNodes, e);
}
function Da(e) {
    let n = e;
    for(; n && n.nodeName !== "TD" && n.nodeName !== "TH" && !n.classList.contains("tableWrapper");){
        if (n.classList.contains("ProseMirror")) return;
        const t = n.parentNode;
        if (!t || !(t instanceof Element)) return;
        n = t;
    }
    return n.nodeName === "TD" || n.nodeName === "TH" ? {
        type: "cell",
        domNode: n,
        tbodyNode: n.closest("tbody")
    } : {
        type: "wrapper",
        domNode: n,
        tbodyNode: n.querySelector("tbody")
    };
}
function Oa(e, n) {
    const t = n.querySelectorAll(e);
    for(let o = 0; o < t.length; o++)t[o].style.visibility = "hidden";
}
class Ra {
    constructor(n, t, o){
        p(this, "state");
        p(this, "emitUpdate");
        p(this, "tableId");
        p(this, "tablePos");
        p(this, "tableElement");
        p(this, "menuFrozen", !1);
        p(this, "mouseState", "up");
        p(this, "prevWasEditable", null);
        p(this, "viewMousedownHandler", ()=>{
            this.mouseState = "down";
        });
        p(this, "mouseUpHandler", (n)=>{
            this.mouseState = "up", this.mouseMoveHandler(n);
        });
        p(this, "mouseMoveHandler", (n)=>{
            var l, d, u, h, f, m, g;
            if (this.menuFrozen || this.mouseState === "selecting" || !(n.target instanceof Element) || !this.pmView.dom.contains(n.target)) return;
            const t = Da(n.target);
            if ((t == null ? void 0 : t.type) === "cell" && this.mouseState === "down" && !((l = this.state) != null && l.draggingState)) {
                this.mouseState = "selecting", (d = this.state) != null && d.show && (this.state.show = !1, this.state.showAddOrRemoveRowsButton = !1, this.state.showAddOrRemoveColumnsButton = !1, this.emitUpdate());
                return;
            }
            if (!t || !this.editor.isEditable) {
                (u = this.state) != null && u.show && (this.state.show = !1, this.state.showAddOrRemoveRowsButton = !1, this.state.showAddOrRemoveColumnsButton = !1, this.emitUpdate());
                return;
            }
            if (!t.tbodyNode) return;
            const o = t.tbodyNode.getBoundingClientRect(), r = no(t.domNode, this.pmView);
            if (!r) return;
            this.tableElement = r.node;
            let s;
            const i = this.editor.transact((b)=>_(r.id, b.doc));
            if (!i) throw new Error(`Block with ID ${r.id} not found`);
            const a = E(i.node, this.editor.pmSchema, this.editor.schema.blockSchema, this.editor.schema.inlineContentSchema, this.editor.schema.styleSchema);
            if (Ws("table", a, this.editor) && (this.tablePos = i.posBeforeNode + 1, s = a), !s) return;
            this.tableId = r.id;
            const c = (h = t.domNode.closest(".tableWrapper")) == null ? void 0 : h.querySelector(".table-widgets-container");
            if ((t == null ? void 0 : t.type) === "wrapper") {
                const b = n.clientY >= o.bottom - 1 && // -1 to account for fractions of pixels in "bottom"
                n.clientY < o.bottom + 20, k = n.clientX >= o.right - 1 && n.clientX < o.right + 20, w = n.clientX > o.right || n.clientY > o.bottom;
                this.state = {
                    ...this.state,
                    show: !0,
                    showAddOrRemoveRowsButton: b,
                    showAddOrRemoveColumnsButton: k,
                    referencePosTable: o,
                    block: s,
                    widgetContainer: c,
                    colIndex: w || (f = this.state) == null ? void 0 : f.colIndex,
                    rowIndex: w || (m = this.state) == null ? void 0 : m.rowIndex,
                    referencePosCell: w || (g = this.state) == null ? void 0 : g.referencePosCell
                };
            } else {
                const b = Ne(t.domNode), k = Ne(t.domNode.parentElement), w = t.domNode.getBoundingClientRect();
                if (this.state !== void 0 && this.state.show && this.tableId === r.id && this.state.rowIndex === k && this.state.colIndex === b) return;
                this.state = {
                    show: !0,
                    showAddOrRemoveColumnsButton: b === s.content.rows[0].cells.length - 1,
                    showAddOrRemoveRowsButton: k === s.content.rows.length - 1,
                    referencePosTable: o,
                    block: s,
                    draggingState: void 0,
                    referencePosCell: w,
                    colIndex: b,
                    rowIndex: k,
                    widgetContainer: c
                };
            }
            return this.emitUpdate(), !1;
        });
        p(this, "dragOverHandler", (n)=>{
            var h;
            if (((h = this.state) == null ? void 0 : h.draggingState) === void 0) return;
            n.preventDefault(), n.dataTransfer.dropEffect = "move", Oa(".prosemirror-dropcursor-block, .prosemirror-dropcursor-inline", this.pmView.root);
            const t = {
                left: Math.min(Math.max(n.clientX, this.state.referencePosTable.left + 1), this.state.referencePosTable.right - 1),
                top: Math.min(Math.max(n.clientY, this.state.referencePosTable.top + 1), this.state.referencePosTable.bottom - 1)
            }, o = this.pmView.root.elementsFromPoint(t.left, t.top).filter((f)=>f.tagName === "TD" || f.tagName === "TH");
            if (o.length === 0) return;
            const r = o[0];
            let s = !1;
            const i = Ne(r.parentElement), a = Ne(r), c = this.state.draggingState.draggedCellOrientation === "row" ? this.state.rowIndex : this.state.colIndex, d = (this.state.draggingState.draggedCellOrientation === "row" ? i : a) !== c;
            (this.state.rowIndex !== i || this.state.colIndex !== a) && (this.state.rowIndex = i, this.state.colIndex = a, this.state.referencePosCell = r.getBoundingClientRect(), s = !0);
            const u = this.state.draggingState.draggedCellOrientation === "row" ? t.top : t.left;
            this.state.draggingState.mousePos !== u && (this.state.draggingState.mousePos = u, s = !0), s && this.emitUpdate(), d && this.editor.transact((f)=>f.setMeta(ye, !0));
        });
        p(this, "dropHandler", (n)=>{
            if (this.mouseState = "up", this.state === void 0 || this.state.draggingState === void 0) return !1;
            if (this.state.rowIndex === void 0 || this.state.colIndex === void 0) throw new Error("Attempted to drop table row or column, but no table block was hovered prior.");
            n.preventDefault();
            const { draggingState: t, colIndex: o, rowIndex: r } = this.state, s = this.state.block.content.columnWidths;
            if (t.draggedCellOrientation === "row") {
                if (!En(this.state.block, t.originalIndex, r)) return !1;
                const i = wr(this.state.block, t.originalIndex, r);
                this.editor.updateBlock(this.state.block, {
                    type: "table",
                    content: {
                        ...this.state.block.content,
                        rows: i
                    }
                });
            } else {
                if (!Sn(this.state.block, t.originalIndex, o)) return !1;
                const i = kr(this.state.block, t.originalIndex, o), [a] = s.splice(t.originalIndex, 1);
                s.splice(o, 0, a), this.editor.updateBlock(this.state.block, {
                    type: "table",
                    content: {
                        ...this.state.block.content,
                        columnWidths: s,
                        rows: i
                    }
                });
            }
            return this.editor.setTextCursorPosition(this.state.block.id), !0;
        });
        this.editor = n, this.pmView = t, this.emitUpdate = ()=>{
            if (!this.state) throw new Error("Attempting to update uninitialized image toolbar");
            o(this.state);
        }, t.dom.addEventListener("mousemove", this.mouseMoveHandler), t.dom.addEventListener("mousedown", this.viewMousedownHandler), window.addEventListener("mouseup", this.mouseUpHandler), t.root.addEventListener("dragover", this.dragOverHandler), t.root.addEventListener("drop", this.dropHandler);
    }
    // Updates drag handles when the table is modified or removed.
    update() {
        var r;
        if (!this.state || !this.state.show) return;
        if (this.state.block = this.editor.getBlock(this.state.block.id), !this.state.block || this.state.block.type !== "table" || // when collaborating, the table element might be replaced and out of date
        // because yjs replaces the element when for example you change the color via the side menu
        !((r = this.tableElement) != null && r.isConnected)) {
            this.state.show = !1, this.state.showAddOrRemoveRowsButton = !1, this.state.showAddOrRemoveColumnsButton = !1, this.emitUpdate();
            return;
        }
        const { height: n, width: t } = vt(this.state.block);
        this.state.rowIndex !== void 0 && this.state.colIndex !== void 0 && (this.state.rowIndex >= n && (this.state.rowIndex = n - 1), this.state.colIndex >= t && (this.state.colIndex = t - 1));
        const o = this.tableElement.querySelector("tbody");
        if (!o) throw new Error("Table block does not contain a 'tbody' HTML element. This should never happen.");
        if (this.state.rowIndex !== void 0 && this.state.colIndex !== void 0) {
            const i = o.children[this.state.rowIndex].children[this.state.colIndex];
            i ? this.state.referencePosCell = i.getBoundingClientRect() : (this.state.rowIndex = void 0, this.state.colIndex = void 0);
        }
        this.state.referencePosTable = o.getBoundingClientRect(), this.emitUpdate();
    }
    destroy() {
        this.pmView.dom.removeEventListener("mousemove", this.mouseMoveHandler), window.removeEventListener("mouseup", this.mouseUpHandler), this.pmView.dom.removeEventListener("mousedown", this.viewMousedownHandler), this.pmView.root.removeEventListener("dragover", this.dragOverHandler), this.pmView.root.removeEventListener("drop", this.dropHandler);
    }
}
const ye = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PluginKey"]("TableHandlesPlugin");
class Va extends L {
    constructor(t){
        super();
        p(this, "view");
        /**
     * Callback that should be set on the `dragStart` event for whichever element
     * is used as the column drag handle.
     */ p(this, "colDragStart", (t)=>{
            if (this.view.state === void 0 || this.view.state.colIndex === void 0) throw new Error("Attempted to drag table column, but no table block was hovered prior.");
            if (this.view.state.draggingState = {
                draggedCellOrientation: "col",
                originalIndex: this.view.state.colIndex,
                mousePos: t.clientX
            }, this.view.emitUpdate(), this.editor.transact((o)=>o.setMeta(ye, {
                    draggedCellOrientation: this.view.state.draggingState.draggedCellOrientation,
                    originalIndex: this.view.state.colIndex,
                    newIndex: this.view.state.colIndex,
                    tablePos: this.view.tablePos
                })), !this.editor.prosemirrorView) throw new Error("Editor view not initialized.");
            rn(this.editor.prosemirrorView.root), t.dataTransfer.setDragImage(R, 0, 0), t.dataTransfer.effectAllowed = "move";
        });
        /**
     * Callback that should be set on the `dragStart` event for whichever element
     * is used as the row drag handle.
     */ p(this, "rowDragStart", (t)=>{
            if (this.view.state === void 0 || this.view.state.rowIndex === void 0) throw new Error("Attempted to drag table row, but no table block was hovered prior.");
            if (this.view.state.draggingState = {
                draggedCellOrientation: "row",
                originalIndex: this.view.state.rowIndex,
                mousePos: t.clientY
            }, this.view.emitUpdate(), this.editor.transact((o)=>o.setMeta(ye, {
                    draggedCellOrientation: this.view.state.draggingState.draggedCellOrientation,
                    originalIndex: this.view.state.rowIndex,
                    newIndex: this.view.state.rowIndex,
                    tablePos: this.view.tablePos
                })), !this.editor.prosemirrorView) throw new Error("Editor view not initialized.");
            rn(this.editor.prosemirrorView.root), t.dataTransfer.setDragImage(R, 0, 0), t.dataTransfer.effectAllowed = "copyMove";
        });
        /**
     * Callback that should be set on the `dragEnd` event for both the element
     * used as the row drag handle, and the one used as the column drag handle.
     */ p(this, "dragEnd", ()=>{
            if (this.view.state === void 0) throw new Error("Attempted to drag table row, but no table block was hovered prior.");
            if (this.view.state.draggingState = void 0, this.view.emitUpdate(), this.editor.transact((t)=>t.setMeta(ye, null)), !this.editor.prosemirrorView) throw new Error("Editor view not initialized.");
            Ha(this.editor.prosemirrorView.root);
        });
        /**
     * Freezes the drag handles. When frozen, they will stay attached to the same
     * cell regardless of which cell is hovered by the mouse cursor.
     */ p(this, "freezeHandles", ()=>{
            this.view.menuFrozen = !0;
        });
        /**
     * Unfreezes the drag handles. When frozen, they will stay attached to the
     * same cell regardless of which cell is hovered by the mouse cursor.
     */ p(this, "unfreezeHandles", ()=>{
            this.view.menuFrozen = !1;
        });
        p(this, "getCellsAtRowHandle", (t, o)=>at(t, o));
        /**
     * Get all the cells in a column of the table block.
     */ p(this, "getCellsAtColumnHandle", (t, o)=>ct(t, o));
        /**
     * Sets the selection to the given cell or a range of cells.
     * @returns The new state after the selection has been set.
     */ p(this, "setCellSelection", (t, o, r = o)=>{
            const s = this.view;
            if (!s) throw new Error("Table handles view not initialized");
            const i = t.doc.resolve(s.tablePos + 1), a = t.doc.resolve(i.posAtIndex(o.row) + 1), c = t.doc.resolve(// No need for +1, since CellSelection expects the position before the cell
            a.posAtIndex(o.col)), l = t.doc.resolve(i.posAtIndex(r.row) + 1), d = t.doc.resolve(// No need for +1, since CellSelection expects the position before the cell
            l.posAtIndex(r.col)), u = t.tr;
            return u.setSelection(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CellSelection"](c, d)), t.apply(u);
        });
        /**
     * Adds a row or column to the table using prosemirror-table commands
     */ p(this, "addRowOrColumn", (t, o)=>{
            this.editor.exec((r, s)=>{
                const i = this.setCellSelection(r, o.orientation === "row" ? {
                    row: t,
                    col: 0
                } : {
                    row: 0,
                    col: t
                });
                return o.orientation === "row" ? o.side === "above" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addRowBefore"])(i, s) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addRowAfter"])(i, s) : o.side === "left" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addColumnBefore"])(i, s) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addColumnAfter"])(i, s);
            });
        });
        /**
     * Removes a row or column from the table using prosemirror-table commands
     */ p(this, "removeRowOrColumn", (t, o)=>o === "row" ? this.editor.exec((r, s)=>{
                const i = this.setCellSelection(r, {
                    row: t,
                    col: 0
                });
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["deleteRow"])(i, s);
            }) : this.editor.exec((r, s)=>{
                const i = this.setCellSelection(r, {
                    row: 0,
                    col: t
                });
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["deleteColumn"])(i, s);
            }));
        /**
     * Merges the cells in the table block.
     */ p(this, "mergeCells", (t)=>this.editor.exec((o, r)=>{
                const s = t ? this.setCellSelection(o, t.relativeStartCell, t.relativeEndCell) : o;
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeCells"])(s, r);
            }));
        /**
     * Splits the cell in the table block.
     * If no cell is provided, the current cell selected will be split.
     */ p(this, "splitCell", (t)=>this.editor.exec((o, r)=>{
                const s = t ? this.setCellSelection(o, t) : o;
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$tables$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["splitCell"])(s, r);
            }));
        /**
     * Gets the start and end cells of the current cell selection.
     * @returns The start and end cells of the current cell selection.
     */ p(this, "getCellSelection", ()=>this.editor.transact((t)=>{
                const o = t.selection;
                let r = o.$from, s = o.$to;
                if (Kt(o)) {
                    const { ranges: m } = o;
                    m.forEach((g)=>{
                        r = g.$from.min(r ?? g.$from), s = g.$to.max(s ?? g.$to);
                    });
                } else if (r = t.doc.resolve(o.$from.pos - o.$from.parentOffset - 1), s = t.doc.resolve(o.$to.pos - o.$to.parentOffset - 1), r.pos === 0 || s.pos === 0) return;
                const i = t.doc.resolve(r.pos - r.parentOffset - 1), a = t.doc.resolve(s.pos - s.parentOffset - 1), c = t.doc.resolve(i.pos - i.parentOffset - 1), l = r.index(i.depth), d = i.index(c.depth), u = s.index(a.depth), h = a.index(c.depth), f = [];
                for(let m = d; m <= h; m++)for(let g = l; g <= u; g++)f.push({
                    row: m,
                    col: g
                });
                return {
                    from: {
                        row: d,
                        col: l
                    },
                    to: {
                        row: h,
                        col: u
                    },
                    cells: f
                };
            }));
        /**
     * Gets the direction of the merge based on the current cell selection.
     *
     * Returns undefined when there is no cell selection, or the selection is not within a table.
     */ p(this, "getMergeDirection", (t)=>this.editor.transact((o)=>{
                const r = Kt(o.selection) ? o.selection : void 0;
                if (!r || !t || // Only offer the merge button if there is more than one cell selected.
                r.ranges.length <= 1) return;
                const s = this.getCellSelection();
                if (s) return vr(s.from, s.to, t) ? "vertical" : "horizontal";
            }));
        p(this, "cropEmptyRowsOrColumns", (t, o)=>yr(t, o));
        p(this, "addRowsOrColumns", (t, o, r)=>Cr(t, o, r));
        this.editor = t, this.addProsemirrorPlugin(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Plugin"]({
            key: ye,
            view: (o)=>(this.view = new Ra(t, o, (r)=>{
                    this.emit("update", r);
                }), this.view),
            // We use decorations to render the drop cursor when dragging a table row
            // or column. The decorations are updated in the `dragOverHandler` method.
            props: {
                decorations: (o)=>{
                    if (this.view === void 0 || this.view.state === void 0 || this.view.state.draggingState === void 0 || this.view.tablePos === void 0) return;
                    const r = this.view.state.draggingState.draggedCellOrientation === "row" ? this.view.state.rowIndex : this.view.state.colIndex;
                    if (r === void 0) return;
                    const s = [], { block: i, draggingState: a } = this.view.state, { originalIndex: c, draggedCellOrientation: l } = a;
                    if (r === c || !i || l === "row" && !En(i, c, r) || l === "col" && !Sn(i, c, r)) return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecorationSet"].create(o.doc, s);
                    const d = o.doc.resolve(this.view.tablePos + 1);
                    return this.view.state.draggingState.draggedCellOrientation === "row" ? at(this.view.state.block, r).forEach(({ row: h, col: f })=>{
                        const m = o.doc.resolve(d.posAtIndex(h) + 1), g = o.doc.resolve(m.posAtIndex(f) + 1), b = g.node(), k = g.pos + (r > c ? b.nodeSize - 2 : 0);
                        s.push(// The widget is a small bar which spans the width of the cell.
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Decoration"].widget(k, ()=>{
                            const w = document.createElement("div");
                            return w.className = "bn-table-drop-cursor", w.style.left = "0", w.style.right = "0", r > c ? w.style.bottom = "-2px" : w.style.top = "-3px", w.style.height = "4px", w;
                        }));
                    }) : ct(this.view.state.block, r).forEach(({ row: h, col: f })=>{
                        const m = o.doc.resolve(d.posAtIndex(h) + 1), g = o.doc.resolve(m.posAtIndex(f) + 1), b = g.node(), k = g.pos + (r > c ? b.nodeSize - 2 : 0);
                        s.push(// The widget is a small bar which spans the height of the cell.
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Decoration"].widget(k, ()=>{
                            const w = document.createElement("div");
                            return w.className = "bn-table-drop-cursor", w.style.top = "0", w.style.bottom = "0", r > c ? w.style.right = "-2px" : w.style.left = "-3px", w.style.width = "4px", w;
                        }));
                    }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecorationSet"].create(o.doc, s);
                }
            }
        }));
    }
    static key() {
        return "tableHandles";
    }
    onUpdate(t) {
        return this.on("update", t);
    }
}
const Ua = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Extension"].create({
    name: "textAlignment",
    addGlobalAttributes () {
        return [
            {
                // Attribute is applied to block content instead of container so that child blocks don't inherit the text
                // alignment styling.
                types: [
                    "paragraph",
                    "heading",
                    "bulletListItem",
                    "numberedListItem",
                    "checkListItem",
                    "tableCell",
                    "tableHeader"
                ],
                attributes: {
                    textAlignment: {
                        default: "left",
                        parseHTML: (e)=>e.getAttribute("data-text-alignment"),
                        renderHTML: (e)=>e.textAlignment === "left" ? {} : {
                                "data-text-alignment": e.textAlignment
                            }
                    }
                }
            }
        ];
    }
}), _a = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Extension"].create({
    name: "blockTextColor",
    addGlobalAttributes () {
        return [
            {
                types: [
                    "blockContainer",
                    "tableCell",
                    "tableHeader"
                ],
                attributes: {
                    textColor: {
                        default: T.textColor.default,
                        parseHTML: (e)=>e.hasAttribute("data-text-color") ? e.getAttribute("data-text-color") : T.textColor.default,
                        renderHTML: (e)=>e.textColor === T.textColor.default ? {} : {
                                "data-text-color": e.textColor
                            }
                    }
                }
            }
        ];
    }
}), $a = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Extension"].create({
    name: "trailingNode",
    addProseMirrorPlugins () {
        const e = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PluginKey"](this.name);
        return [
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Plugin"]({
                key: e,
                appendTransaction: (n, t, o)=>{
                    const { doc: r, tr: s, schema: i } = o, a = e.getState(o), c = r.content.size - 2, l = i.nodes.blockContainer, d = i.nodes.paragraph;
                    if (a) return s.insert(c, l.create(void 0, d.create()));
                },
                state: {
                    init: (n, t)=>{},
                    apply: (n, t)=>{
                        if (!n.docChanged) return t;
                        let o = n.doc.lastChild;
                        if (!o || o.type.name !== "blockGroup") throw new Error("Expected blockGroup");
                        if (o = o.lastChild, !o || o.type.name !== "blockContainer") return !0;
                        const r = o.firstChild;
                        if (!r) throw new Error("Expected blockContent");
                        return o.nodeSize > 4 || r.type.spec.content !== "inline*";
                    }
                }
            })
        ];
    }
}), Fa = {
    blockColor: "data-block-color",
    blockStyle: "data-block-style",
    id: "data-id",
    depth: "data-depth",
    depthChange: "data-depth-change"
}, za = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Node"].create({
    name: "blockContainer",
    group: "blockGroupChild bnBlock",
    // A block always contains content, and optionally a blockGroup which contains nested blocks
    content: "blockContent blockGroup?",
    // Ensures content-specific keyboard handlers trigger first.
    priority: 50,
    defining: !0,
    marks: "insertion modification deletion",
    parseHTML () {
        return [
            {
                tag: "div[data-node-type=" + this.name + "]",
                getAttrs: (e)=>{
                    if (typeof e == "string") return !1;
                    const n = {};
                    for (const [t, o] of Object.entries(Fa))e.getAttribute(o) && (n[t] = e.getAttribute(o));
                    return n;
                }
            },
            // Ignore `blockOuter` divs, but parse the `blockContainer` divs inside them.
            {
                tag: 'div[data-node-type="blockOuter"]',
                skip: !0
            }
        ];
    },
    renderHTML ({ HTMLAttributes: e }) {
        var r;
        const n = document.createElement("div");
        n.className = "bn-block-outer", n.setAttribute("data-node-type", "blockOuter");
        for (const [s, i] of Object.entries(e))s !== "class" && n.setAttribute(s, i);
        const t = {
            ...((r = this.options.domAttributes) == null ? void 0 : r.block) || {},
            ...e
        }, o = document.createElement("div");
        o.className = Q("bn-block", t.class), o.setAttribute("data-node-type", this.name);
        for (const [s, i] of Object.entries(t))s !== "class" && o.setAttribute(s, i);
        return n.appendChild(o), {
            dom: n,
            contentDOM: o
        };
    }
}), Wa = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Node"].create({
    name: "blockGroup",
    group: "childContainer",
    content: "blockGroupChild+",
    marks: "deletion insertion modification",
    parseHTML () {
        return [
            {
                tag: "div",
                getAttrs: (e)=>typeof e == "string" ? !1 : e.getAttribute("data-node-type") === "blockGroup" ? null : !1
            }
        ];
    },
    renderHTML ({ HTMLAttributes: e }) {
        var o;
        const n = {
            ...((o = this.options.domAttributes) == null ? void 0 : o.blockGroup) || {},
            ...e
        }, t = document.createElement("div");
        t.className = Q("bn-block-group", n.class), t.setAttribute("data-node-type", "blockGroup");
        for (const [r, s] of Object.entries(n))r !== "class" && t.setAttribute(r, s);
        return {
            dom: t,
            contentDOM: t
        };
    }
}), ja = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Node"].create({
    name: "doc",
    topNode: !0,
    content: "blockGroup",
    marks: "insertion modification deletion"
});
class Ga extends L {
    constructor({ editor: t, collaboration: o }){
        super(t);
        p(this, "editor");
        p(this, "collaboration");
        /**
     * Stores whether the editor is editing a forked document,
     * preserving a reference to the original document and the forked document.
     */ p(this, "forkedState");
        this.editor = t, this.collaboration = o;
    }
    static key() {
        return "ForkYDocPlugin";
    }
    /**
   * To find a fragment in another ydoc, we need to search for it.
   */ findTypeInOtherYdoc(t, o) {
        const r = t.doc;
        if (t._item === null) {
            const s = Array.from(r.share.keys()).find((i)=>r.share.get(i) === t);
            if (s == null) throw new Error("type does not exist in other ydoc");
            return o.get(s, t.constructor);
        } else {
            const s = t._item, i = o.store.clients.get(s.id.client) ?? [], a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yjs$2f$dist$2f$yjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findIndexSS"])(i, s.id.clock);
            return i[a].content.type;
        }
    }
    /**
   * Whether the editor is editing a forked document,
   * preserving a reference to the original document and the forked document.
   */ get isForkedFromRemote() {
        return this.forkedState !== void 0;
    }
    /**
   * Fork the Y.js document from syncing to the remote,
   * allowing modifications to the document without affecting the remote.
   * These changes can later be rolled back or applied to the remote.
   */ fork() {
        if (this.isForkedFromRemote) return;
        const t = this.collaboration.fragment;
        if (!t) throw new Error("No fragment to fork from");
        const o = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yjs$2f$dist$2f$yjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Doc"]();
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yjs$2f$dist$2f$yjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applyUpdate"])(o, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yjs$2f$dist$2f$yjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["encodeStateAsUpdate"])(t.doc));
        const r = this.findTypeInOtherYdoc(t, o);
        this.forkedState = {
            originalFragment: t,
            forkedFragment: r
        }, this.editor._tiptapEditor.unregisterPlugin([
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$keys$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["yCursorPluginKey"],
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$keys$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["yUndoPluginKey"],
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$keys$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ySyncPluginKey"]
        ]), this.editor._tiptapEditor.registerPlugin(new ht(r).plugins[0]), this.editor._tiptapEditor.registerPlugin(new ft().plugins[0]), this.emit("forked", !0);
    }
    /**
   * Resume syncing the Y.js document to the remote
   * If `keepChanges` is true, any changes that have been made to the forked document will be applied to the original document.
   * Otherwise, the original document will be restored and the changes will be discarded.
   */ merge({ keepChanges: t }) {
        if (!this.forkedState) return;
        this.editor._tiptapEditor.unregisterPlugin(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$keys$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ySyncPluginKey"]), this.editor._tiptapEditor.unregisterPlugin(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$keys$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["yUndoPluginKey"]);
        const { originalFragment: o, forkedFragment: r } = this.forkedState;
        if (t) {
            const s = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yjs$2f$dist$2f$yjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["encodeStateAsUpdate"])(r.doc);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yjs$2f$dist$2f$yjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applyUpdate"])(o.doc, s);
        }
        this.editor.extensions.ySyncPlugin = new ht(o), this.editor.extensions.yCursorPlugin = new Ue(this.collaboration), this.editor.extensions.yUndoPlugin = new ft(), this.editor._tiptapEditor.registerPlugin(this.editor.extensions.ySyncPlugin.plugins[0]), this.editor._tiptapEditor.registerPlugin(this.editor.extensions.yCursorPlugin.plugins[0]), this.editor._tiptapEditor.registerPlugin(this.editor.extensions.yUndoPlugin.plugins[0]), this.forkedState = void 0, this.emit("forked", !1);
    }
}
const qa = (e)=>{
    var r;
    const n = {}, t = Ka(e);
    for (const s of t)n[s.name] = s;
    e.collaboration && (n.ySyncPlugin = new ht(e.collaboration.fragment), n.yUndoPlugin = new ft(), (r = e.collaboration.provider) != null && r.awareness && (n.yCursorPlugin = new Ue(e.collaboration)), n.forkYDocPlugin = new Ga({
        editor: e.editor,
        collaboration: e.collaboration
    })), n.formattingToolbar = new oa(e.editor), n.linkToolbar = new da(e.editor), n.sideMenu = new Ba(e.editor, e.sideMenuDetection), n.suggestionMenus = new Ia(e.editor), n.filePanel = new ea(e.editor), n.placeholder = new ga(e.editor, e.placeholders), (e.animations ?? !0) && (n.animations = new ka()), e.tableHandles && (n.tableHandles = new Va(e.editor)), n.nodeSelectionKeyboard = new fa(), n.showSelection = new wa(e.editor), e.comments && (n.comments = new Zi(e.editor, e.comments.threadStore, eo.name));
    const o = e.disableExtensions || [];
    for (const s of o)delete n[s];
    return n;
};
let sn = !1;
const Ka = (e)=>{
    const n = [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extensions"].ClipboardTextSerializer,
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extensions"].Commands,
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extensions"].Editable,
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extensions"].FocusEvents,
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extensions"].Tabindex,
        // DevTools,
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$gapcursor$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Gapcursor"],
        // DropCursor,
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Extension"].create({
            name: "dropCursor",
            addProseMirrorPlugins: ()=>[
                    e.dropCursor({
                        width: 5,
                        color: "#ddeeff",
                        editor: e.editor
                    })
                ]
        }),
        We.configure({
            // everything from bnBlock group (nodes that represent a BlockNote block should have an id)
            types: [
                "blockContainer",
                "columnList",
                "column"
            ],
            setIdAttribute: e.setIdAttribute
        }),
        ra,
        // Comments,
        // basics:
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$text$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Text"],
        // marks:
        La,
        Aa,
        Na,
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$link$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Link"].extend({
            inclusive: !1
        }).configure({
            defaultProtocol: pa,
            // only call this once if we have multiple editors installed. Or fix https://github.com/ueberdosis/tiptap/issues/5450
            protocols: sn ? [] : ua
        }),
        ...Object.values(e.styleSpecs).map((t)=>t.implementation.mark.configure({
                editor: e.editor
            })),
        _a,
        Ki,
        Ua,
        // make sure escape blurs editor, so that we can tab to other elements in the host page (accessibility)
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Extension"].create({
            name: "OverrideEscape",
            addKeyboardShortcuts () {
                return {
                    Escape: ()=>e.editor.suggestionMenus.shown ? !1 : this.editor.commands.blur()
                };
            }
        }),
        // nodes
        ja,
        za.configure({
            editor: e.editor,
            domAttributes: e.domAttributes
        }),
        aa.configure({
            editor: e.editor,
            tabBehavior: e.tabBehavior
        }),
        Wa.configure({
            domAttributes: e.domAttributes
        }),
        ...Object.values(e.inlineContentSpecs).filter((t)=>t.config !== "link" && t.config !== "text").map((t)=>t.implementation.node.configure({
                editor: e.editor
            })),
        ...Object.values(e.blockSpecs).flatMap((t)=>[
                // dependent nodes (e.g.: tablecell / row)
                ...(t.implementation.requiredExtensions || []).map((o)=>o.configure({
                        editor: e.editor,
                        domAttributes: e.domAttributes
                    })),
                // the actual node itself
                t.implementation.node.configure({
                    editor: e.editor,
                    domAttributes: e.domAttributes
                })
            ]),
        qi(e.editor),
        ji(e.editor, e.pasteHandler || ((t)=>t.defaultPasteHandler())),
        Mi(e.editor),
        // This needs to be at the bottom of this list, because Key events (such as enter, when selecting a /command),
        // should be handled before Enter handlers in other components like splitListItem
        ...e.trailingBlock === void 0 || e.trailingBlock ? [
            $a
        ] : [],
        ...e.comments ? [
            eo
        ] : []
    ];
    return sn = !0, e.collaboration || n.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$extension$2d$history$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["History"]), n;
};
function Ja(e, n) {
    const t = [];
    return e.forEach((o, r, s)=>{
        s !== n && t.push(o);
    }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"].from(t);
}
function Xa(e, n) {
    const t = [];
    for(let o = 0; o < e.childCount; o++)if (e.child(o).type.name === "tableRow") if (t.length > 0 && t[t.length - 1].type.name === "table") {
        const r = t[t.length - 1], s = r.copy(r.content.addToEnd(e.child(o)));
        t[t.length - 1] = s;
    } else {
        const r = n.nodes.table.createChecked(void 0, e.child(o));
        t.push(r);
    }
    else t.push(e.child(o));
    return e = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"].from(t), e;
}
function Ya(e, n) {
    let t = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"].from(e.content);
    if (t = Xa(t, n.state.schema), !Za(t, n)) return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Slice"](t, e.openStart, e.openEnd);
    for(let o = 0; o < t.childCount; o++)if (t.child(o).type.spec.group === "blockContent") {
        const r = [
            t.child(o)
        ];
        if (o + 1 < t.childCount && t.child(o + 1).type.name === "blockGroup") {
            const i = t.child(o + 1).child(0).child(0);
            (i.type.name === "bulletListItem" || i.type.name === "numberedListItem" || i.type.name === "checkListItem") && (r.push(t.child(o + 1)), t = Ja(t, o + 1));
        }
        const s = n.state.schema.nodes.blockContainer.createChecked(void 0, r);
        t = t.replaceChild(o, s);
    }
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Slice"](t, e.openStart, e.openEnd);
}
function Za(e, n) {
    var s, i;
    const t = e.childCount === 1, o = ((s = e.firstChild) == null ? void 0 : s.type.spec.content) === "inline*", r = ((i = e.firstChild) == null ? void 0 : i.type.spec.content) === "tableRow+";
    if (t) {
        if (o) return !1;
        if (r) {
            const a = v(n.state);
            if (a.isBlockContainer) return !(a.blockContent.node.type.spec.content === "tableRow+");
        }
    }
    return !0;
}
const Fe = class Fe extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Editor"] {
    constructor(t, o){
        super({
            ...t,
            content: void 0
        });
        p(this, "_state");
        /**
     * Mounts / unmounts the editor to a dom element
     *
     * @param element DOM element to mount to, ur null / undefined to destroy
     */ p(this, "mount", (t, o, r)=>{
            o ? (this.options.element = o, this.createViewAlternative(t, r)) : this.destroy();
        });
        const r = this.schema;
        let s;
        const i = r.nodes.doc.createAndFill;
        r.nodes.doc.createAndFill = (...c)=>{
            if (s) return s;
            const l = i.apply(r.nodes.doc, c), d = JSON.parse(JSON.stringify(l.toJSON()));
            return d.content[0].content[0].attrs.id = "initialBlockId", s = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$model$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Node"].fromJSON(r, d), s;
        };
        let a;
        try {
            const c = t == null ? void 0 : t.content.map((l)=>pe(l, this.schema, o).toJSON());
            a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createDocument"])({
                type: "doc",
                content: [
                    {
                        type: "blockGroup",
                        content: c
                    }
                ]
            }, this.schema, this.options.parseOptions);
        } catch (c) {
            throw console.error("Error creating document from blocks passed as `initialContent`. Caused by exception: ", c), new Error("Error creating document from blocks passed as `initialContent`:\n" + +JSON.stringify(t.content));
        }
        this._state = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EditorState"].create({
            doc: a,
            schema: this.schema
        });
    }
    get state() {
        return this.view && (this._state = this.view.state), this._state;
    }
    dispatch(t) {
        if (!this.view) {
            this._state = this.state.apply(t), this.emit("transaction", {
                editor: this,
                transaction: t
            });
            return;
        }
        if (this.view.isDestroyed) return;
        if (this.isCapturingTransaction) {
            this.dispatchTransaction(t);
            return;
        }
        const { state: o, transactions: r } = this.state.applyTransaction(t), s = !this.state.selection.eq(o.selection);
        this.emit("beforeTransaction", {
            editor: this,
            transaction: t,
            nextState: o
        }), this.view.updateState(o), this.emit("transaction", {
            editor: this,
            transaction: t
        }), s && this.emit("selectionUpdate", {
            editor: this,
            transaction: t
        });
        const i = t.getMeta("focus"), a = t.getMeta("blur");
        i && this.emit("focus", {
            editor: this,
            event: i.event,
            transaction: t
        }), a && this.emit("blur", {
            editor: this,
            event: a.event,
            transaction: t
        }), !(!t.docChanged || t.getMeta("preventUpdate")) && (this.emit("update", {
            editor: this,
            transaction: t
        }), this.emit("v3-update", {
            editor: this,
            transaction: t,
            appendedTransactions: r.slice(1)
        }));
    }
    // a helper method that can enable plugins before the view has been initialized
    // currently only used for testing
    forceEnablePlugins() {
        if (this.view) throw new Error("forcePluginsEnabled called after view has been initialized");
        this._state = this.state.reconfigure({
            plugins: this.extensionManager.plugins
        });
    }
    /**
   * Replace the default `createView` method with a custom one - which we call on mount
   */ createViewAlternative(t, o) {
        this.contentComponent = o;
        const r = {};
        this.extensionManager.extensions.forEach((i)=>{
            i.type === "mark" && i.config.addMarkView && (r[i.name] = i.config.addMarkView(t));
        }), this.view = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EditorView"]({
            mount: this.options.element
        }, // use mount option so that we reuse the existing element instead of creating a new one
        {
            ...this.options.editorProps,
            // @ts-ignore
            dispatchTransaction: this.dispatch.bind(this),
            state: this.state,
            markViews: r,
            nodeViews: this.extensionManager.nodeViews
        });
        const s = this.state.reconfigure({
            plugins: this.extensionManager.plugins
        });
        this.view.updateState(s), this.commands.focus(this.options.autofocus || this.options.element.getAttribute("data-bn-autofocus") === "true", {
            scrollIntoView: !1
        }), this.emit("create", {
            editor: this
        }), this.isInitialized = !0;
    }
};
p(Fe, "create", (t, o)=>{
    var s, i;
    const r = (s = globalThis == null ? void 0 : globalThis.window) == null ? void 0 : s.setTimeout;
    typeof ((i = globalThis == null ? void 0 : globalThis.window) == null ? void 0 : i.setTimeout) < "u" && (globalThis.window.setTimeout = ()=>0);
    try {
        return new Fe(t, o);
    } finally{
        r && (globalThis.window.setTimeout = r);
    }
});
let _e = Fe;
_e.prototype.createView = function() {
    this.options.onPaste = this.options.onDrop = void 0;
};
const Qa = {
    enableInputRules: !0,
    enablePasteRules: !0,
    enableCoreExtensions: !1
};
class ro extends It {
    constructor(t){
        var l, d, u, h, f, m, g, b, k, w, C, B, P, $, S;
        super();
        /**
     * The underlying prosemirror schema
     */ p(this, "pmSchema");
        /**
     * extensions that are added to the editor, can be tiptap extensions or prosemirror plugins
     */ p(this, "extensions", {});
        /**
     * Boolean indicating whether the editor is in headless mode.
     * Headless mode means we can use features like importing / exporting blocks,
     * but there's no underlying editor (UI) instantiated.
     *
     * You probably don't need to set this manually, but use the `server-util` package instead that uses this option internally
     */ p(this, "headless", !1);
        p(this, "_tiptapEditor");
        // TODO: Type should actually reflect that it can be `undefined` in headless mode
        /**
     * Used by React to store a reference to an `ElementRenderer` helper utility to make sure we can render React elements
     * in the correct context (used by `ReactRenderUtil`)
     */ p(this, "elementRenderer", null);
        /**
     * Cache of all blocks. This makes sure we don't have to "recompute" blocks if underlying Prosemirror Nodes haven't changed.
     * This is especially useful when we want to keep track of the same block across multiple operations,
     * with this cache, blocks stay the same object reference (referential equality with ===).
     */ p(this, "blockCache", /* @__PURE__ */ new WeakMap());
        /**
     * The dictionary contains translations for the editor.
     */ p(this, "dictionary");
        /**
     * The schema of the editor. The schema defines which Blocks, InlineContent, and Styles are available in the editor.
     */ p(this, "schema");
        p(this, "blockImplementations");
        p(this, "inlineContentImplementations");
        p(this, "styleImplementations");
        p(this, "formattingToolbar");
        p(this, "linkToolbar");
        p(this, "sideMenu");
        p(this, "suggestionMenus");
        p(this, "filePanel");
        p(this, "tableHandles");
        p(this, "comments");
        p(this, "showSelectionPlugin");
        /**
     * The plugin for forking a document, only defined if in collaboration mode
     */ p(this, "forkYDocPlugin");
        /**
     * The `uploadFile` method is what the editor uses when files need to be uploaded (for example when selecting an image to upload).
     * This method should set when creating the editor as this is application-specific.
     *
     * `undefined` means the application doesn't support file uploads.
     *
     * @param file The file that should be uploaded.
     * @returns The URL of the uploaded file OR an object containing props that should be set on the file block (such as an id)
     */ p(this, "uploadFile");
        p(this, "onUploadStartCallbacks", []);
        p(this, "onUploadEndCallbacks", []);
        p(this, "resolveFileUrl");
        p(this, "resolveUsers");
        /**
     * Editor settings
     */ p(this, "settings");
        /**
     * Stores the currently active transaction, which is the accumulated transaction from all {@link dispatch} calls during a {@link transact} calls
     */ p(this, "activeTransaction", null);
        /**
     * Mount the editor to a parent DOM element. Call mount(undefined) to clean up
     *
     * @warning Not needed to call manually when using React, use BlockNoteView to take care of mounting
     */ p(this, "mount", (t, o)=>{
            this._tiptapEditor.mount(this, t, o);
        });
        this.options = t;
        const o = t;
        if (o.onEditorContentChange) throw new Error("onEditorContentChange initialization option is deprecated, use <BlockNoteView onChange={...} />, the useEditorChange(...) hook, or editor.onChange(...)");
        if (o.onTextCursorPositionChange) throw new Error("onTextCursorPositionChange initialization option is deprecated, use <BlockNoteView onSelectionChange={...} />, the useEditorSelectionChange(...) hook, or editor.onSelectionChange(...)");
        if (o.onEditorReady) throw new Error("onEditorReady is deprecated. Editor is immediately ready for use after creation.");
        if (o.editable) throw new Error("editable initialization option is deprecated, use <BlockNoteView editable={true/false} />, or alternatively editor.isEditable = true/false");
        this.dictionary = t.dictionary || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$blocknote$2f$core$2f$dist$2f$en$2d$qGo6sk9V$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["e"], this.settings = {
            tables: {
                splitCells: ((l = t == null ? void 0 : t.tables) == null ? void 0 : l.splitCells) ?? !1,
                cellBackgroundColor: ((d = t == null ? void 0 : t.tables) == null ? void 0 : d.cellBackgroundColor) ?? !1,
                cellTextColor: ((u = t == null ? void 0 : t.tables) == null ? void 0 : u.cellTextColor) ?? !1,
                headers: ((h = t == null ? void 0 : t.tables) == null ? void 0 : h.headers) ?? !1
            },
            codeBlock: {
                indentLineWithTab: ((f = t == null ? void 0 : t.codeBlock) == null ? void 0 : f.indentLineWithTab) ?? !0,
                defaultLanguage: ((m = t == null ? void 0 : t.codeBlock) == null ? void 0 : m.defaultLanguage) ?? "text",
                supportedLanguages: ((g = t == null ? void 0 : t.codeBlock) == null ? void 0 : g.supportedLanguages) ?? {},
                createHighlighter: ((b = t == null ? void 0 : t.codeBlock) == null ? void 0 : b.createHighlighter) ?? void 0
            }
        };
        const r = {
            defaultStyles: !0,
            schema: t.schema || Pe.create(),
            _headless: !1,
            ...t,
            placeholders: {
                ...this.dictionary.placeholders,
                ...t.placeholders
            }
        };
        if (r.comments && !r.resolveUsers) throw new Error("resolveUsers is required when using comments");
        this.resolveUsers = r.resolveUsers, this.schema = r.schema, this.blockImplementations = r.schema.blockSpecs, this.inlineContentImplementations = r.schema.inlineContentSpecs, this.styleImplementations = r.schema.styleSpecs, this.extensions = qa({
            editor: this,
            domAttributes: r.domAttributes || {},
            blockSpecs: this.schema.blockSpecs,
            styleSpecs: this.schema.styleSpecs,
            inlineContentSpecs: this.schema.inlineContentSpecs,
            collaboration: r.collaboration,
            trailingBlock: r.trailingBlock,
            disableExtensions: r.disableExtensions,
            setIdAttribute: r.setIdAttribute,
            animations: r.animations ?? !0,
            tableHandles: O("table", this),
            dropCursor: this.options.dropCursor ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$dropcursor$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dropCursor"],
            placeholders: r.placeholders,
            tabBehavior: r.tabBehavior,
            sideMenuDetection: r.sideMenuDetection || "viewport",
            comments: r.comments,
            pasteHandler: r.pasteHandler
        }), (((k = r._tiptapOptions) == null ? void 0 : k.extensions) || []).forEach((y)=>{
            this.extensions[y.name] = y;
        });
        for (let y of r.extensions || []){
            typeof y == "function" && (y = y(this));
            const x = y.constructor.key();
            if (!x) throw new Error(`Extension ${y.constructor.name} does not have a key method`);
            if (this.extensions[x]) throw new Error(`Extension ${y.constructor.name} already exists with key ${x}`);
            this.extensions[x] = y;
        }
        if (Object.entries(r._extensions || {}).forEach(([y, x])=>{
            const Y = typeof x == "function" ? x(this) : x;
            if (!("plugin" in Y)) {
                this.extensions[y] = Y;
                return;
            }
            this.extensions[y] = new class extends L {
                static key() {
                    return y;
                }
                constructor(){
                    super(), this.addProsemirrorPlugin(Y.plugin);
                }
                get priority() {
                    return Y.priority;
                }
            }();
        }), this.formattingToolbar = this.extensions.formattingToolbar, this.linkToolbar = this.extensions.linkToolbar, this.sideMenu = this.extensions.sideMenu, this.suggestionMenus = this.extensions.suggestionMenus, this.filePanel = this.extensions.filePanel, this.tableHandles = this.extensions.tableHandles, this.comments = this.extensions.comments, this.showSelectionPlugin = this.extensions.showSelection, this.forkYDocPlugin = this.extensions.forkYDocPlugin, r.uploadFile) {
            const y = r.uploadFile;
            this.uploadFile = async (x, se)=>{
                this.onUploadStartCallbacks.forEach((Y)=>Y.apply(this, [
                        se
                    ]));
                try {
                    return await y(x, se);
                } finally{
                    this.onUploadEndCallbacks.forEach((Y)=>Y.apply(this, [
                            se
                        ]));
                }
            };
        }
        this.resolveFileUrl = r.resolveFileUrl, this.headless = r._headless;
        const s = "ySyncPlugin" in this.extensions || "liveblocksExtension" in this.extensions;
        s && r.initialContent && console.warn("When using Collaboration, initialContent might cause conflicts, because changes should come from the collaboration provider");
        const i = r.initialContent || (s ? [
            {
                type: "paragraph",
                id: "initialBlockId"
            }
        ] : [
            {
                type: "paragraph",
                id: We.options.generateID()
            }
        ]);
        if (!Array.isArray(i) || i.length === 0) throw new Error("initialContent must be a non-empty array of blocks, received: " + i);
        const a = [
            ...Object.entries(this.extensions).map(([y, x])=>{
                if (x instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Extension"] || x instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Node"] || x instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Mark"]) return x;
                if (!(x instanceof L && !x.plugins.length)) return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Extension"].create({
                    name: y,
                    priority: x.priority,
                    addProseMirrorPlugins: ()=>x.plugins
                });
            })
        ].filter((y)=>y !== void 0), c = {
            ...Qa,
            ...r._tiptapOptions,
            content: i,
            extensions: a,
            editorProps: {
                ...(w = r._tiptapOptions) == null ? void 0 : w.editorProps,
                attributes: {
                    // As of TipTap v2.5.0 the tabIndex is removed when the editor is not
                    // editable, so you can't focus it. We want to revert this as we have
                    // UI behaviour that relies on it.
                    tabIndex: "0",
                    ...(B = (C = r._tiptapOptions) == null ? void 0 : C.editorProps) == null ? void 0 : B.attributes,
                    ...(P = r.domAttributes) == null ? void 0 : P.editor,
                    class: Q("bn-editor", r.defaultStyles ? "bn-default-styles" : "", ((S = ($ = r.domAttributes) == null ? void 0 : $.editor) == null ? void 0 : S.class) || "")
                },
                transformPasted: Ya
            }
        };
        this.headless ? this.pmSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSchema"])(c.extensions) : (this._tiptapEditor = _e.create(c, this.schema.styleSchema), this.pmSchema = this._tiptapEditor.schema), this.pmSchema.cached.blockNoteEditor = this, this.emit("create");
    }
    static create(t = {}) {
        return new ro(t);
    }
    /**
   * Execute a prosemirror command. This is mostly for backwards compatibility with older code.
   *
   * @note You should prefer the {@link transact} method when possible, as it will automatically handle the dispatching of the transaction and work across blocknote transactions.
   *
   * @example
   * ```ts
   * editor.exec((state, dispatch, view) => {
   *   dispatch(state.tr.insertText("Hello, world!"));
   * });
   * ```
   */ exec(t) {
        if (this.activeTransaction) throw new Error("`exec` should not be called within a `transact` call, move the `exec` call outside of the `transact` call");
        const o = this._tiptapEditor.state, r = this._tiptapEditor.view;
        return t(o, (i)=>this._tiptapEditor.dispatch(i), r);
    }
    /**
   * Check if a command can be executed. A command should return `false` if it is not valid in the current state.
   *
   * @example
   * ```ts
   * if (editor.canExec(command)) {
   *   // show button
   * } else {
   *   // hide button
   * }
   * ```
   */ canExec(t) {
        if (this.activeTransaction) throw new Error("`canExec` should not be called within a `transact` call, move the `canExec` call outside of the `transact` call");
        const o = this._tiptapEditor.state, r = this._tiptapEditor.view;
        return t(o, void 0, r);
    }
    /**
   * Execute a function within a "blocknote transaction".
   * All changes to the editor within the transaction will be grouped together, so that
   * we can dispatch them as a single operation (thus creating only a single undo step)
   *
   * @note There is no need to dispatch the transaction, as it will be automatically dispatched when the callback is complete.
   *
   * @example
   * ```ts
   * // All changes to the editor will be grouped together
   * editor.transact((tr) => {
   *   tr.insertText("Hello, world!");
   * // These two operations will be grouped together in a single undo step
   *   editor.transact((tr) => {
   *     tr.insertText("Hello, world!");
   *   });
   * });
   * ```
   */ transact(t) {
        if (this.activeTransaction) return t(this.activeTransaction);
        try {
            this.activeTransaction = this._tiptapEditor.state.tr;
            const o = t(this.activeTransaction), r = this.activeTransaction;
            return this.activeTransaction = null, r && // Only dispatch if the transaction was actually modified in some way
            (r.docChanged || r.selectionSet || r.scrolledIntoView || r.storedMarksSet || !r.isGeneric) && this._tiptapEditor.dispatch(r), o;
        } finally{
            this.activeTransaction = null;
        }
    }
    // TO DISCUSS
    /**
   * Shorthand to get a typed extension from the editor, by
   * just passing in the extension class.
   *
   * @param ext - The extension class to get
   * @param key - optional, the key of the extension in the extensions object (defaults to the extension name)
   * @returns The extension instance
   */ extension(t, o = t.key()) {
        const r = this.extensions[o];
        if (!r) throw new Error(`Extension ${o} not found`);
        return r;
    }
    /**
   * Get the underlying prosemirror state
   * @note Prefer using `editor.transact` to read the current editor state, as that will ensure the state is up to date
   * @see https://prosemirror.net/docs/ref/#state.EditorState
   */ get prosemirrorState() {
        if (this.activeTransaction) throw new Error("`prosemirrorState` should not be called within a `transact` call, move the `prosemirrorState` call outside of the `transact` call or use `editor.transact` to read the current editor state");
        return this._tiptapEditor.state;
    }
    /**
   * Get the underlying prosemirror view
   * @see https://prosemirror.net/docs/ref/#view.EditorView
   */ get prosemirrorView() {
        return this._tiptapEditor.view;
    }
    get domElement() {
        var t;
        return (t = this.prosemirrorView) == null ? void 0 : t.dom;
    }
    isFocused() {
        var t;
        return ((t = this.prosemirrorView) == null ? void 0 : t.hasFocus()) || !1;
    }
    focus() {
        var t;
        (t = this.prosemirrorView) == null || t.focus();
    }
    onUploadStart(t) {
        return this.onUploadStartCallbacks.push(t), ()=>{
            const o = this.onUploadStartCallbacks.indexOf(t);
            o > -1 && this.onUploadStartCallbacks.splice(o, 1);
        };
    }
    onUploadEnd(t) {
        return this.onUploadEndCallbacks.push(t), ()=>{
            const o = this.onUploadEndCallbacks.indexOf(t);
            o > -1 && this.onUploadEndCallbacks.splice(o, 1);
        };
    }
    /**
   * @deprecated, use `editor.document` instead
   */ get topLevelBlocks() {
        return this.document;
    }
    /**
   * Gets a snapshot of all top-level (non-nested) blocks in the editor.
   * @returns A snapshot of all top-level (non-nested) blocks in the editor.
   */ get document() {
        return this.transact((t)=>lr(t.doc, this.pmSchema));
    }
    /**
   * Gets a snapshot of an existing block from the editor.
   * @param blockIdentifier The identifier of an existing block that should be
   * retrieved.
   * @returns The block that matches the identifier, or `undefined` if no
   * matching block was found.
   */ getBlock(t) {
        return this.transact((o)=>ai(o.doc, t));
    }
    /**
   * Gets a snapshot of the previous sibling of an existing block from the
   * editor.
   * @param blockIdentifier The identifier of an existing block for which the
   * previous sibling should be retrieved.
   * @returns The previous sibling of the block that matches the identifier.
   * `undefined` if no matching block was found, or it's the first child/block
   * in the document.
   */ getPrevBlock(t) {
        return this.transact((o)=>ci(o.doc, t));
    }
    /**
   * Gets a snapshot of the next sibling of an existing block from the editor.
   * @param blockIdentifier The identifier of an existing block for which the
   * next sibling should be retrieved.
   * @returns The next sibling of the block that matches the identifier.
   * `undefined` if no matching block was found, or it's the last child/block in
   * the document.
   */ getNextBlock(t) {
        return this.transact((o)=>li(o.doc, t));
    }
    /**
   * Gets a snapshot of the parent of an existing block from the editor.
   * @param blockIdentifier The identifier of an existing block for which the
   * parent should be retrieved.
   * @returns The parent of the block that matches the identifier. `undefined`
   * if no matching block was found, or the block isn't nested.
   */ getParentBlock(t) {
        return this.transact((o)=>di(o.doc, t));
    }
    /**
   * Traverses all blocks in the editor depth-first, and executes a callback for each.
   * @param callback The callback to execute for each block. Returning `false` stops the traversal.
   * @param reverse Whether the blocks should be traversed in reverse order.
   */ forEachBlock(t, o = !1) {
        const r = this.document.slice();
        o && r.reverse();
        function s(i) {
            for (const a of i){
                if (t(a) === !1) return !1;
                const c = o ? a.children.slice().reverse() : a.children;
                if (!s(c)) return !1;
            }
            return !0;
        }
        s(r);
    }
    /**
   * Executes a callback whenever the editor's contents change.
   * @param callback The callback to execute.
   *
   * @deprecated use {@link BlockNoteEditor.onChange} instead
   */ onEditorContentChange(t) {
        this._tiptapEditor.on("update", t);
    }
    /**
   * Executes a callback whenever the editor's selection changes.
   * @param callback The callback to execute.
   *
   * @deprecated use `onSelectionChange` instead
   */ onEditorSelectionChange(t) {
        this._tiptapEditor.on("selectionUpdate", t);
    }
    /**
   * Gets a snapshot of the current text cursor position.
   * @returns A snapshot of the current text cursor position.
   */ getTextCursorPosition() {
        return this.transact((t)=>mi(t));
    }
    /**
   * Sets the text cursor position to the start or end of an existing block. Throws an error if the target block could
   * not be found.
   * @param targetBlock The identifier of an existing block that the text cursor should be moved to.
   * @param placement Whether the text cursor should be placed at the start or end of the block.
   */ setTextCursorPosition(t, o = "start") {
        return this.transact((r)=>qn(r, t, o));
    }
    /**
   * Gets a snapshot of the current selection. This contains all blocks (included nested blocks)
   * that the selection spans across.
   *
   * If the selection starts / ends halfway through a block, the returned data will contain the entire block.
   */ getSelection() {
        return this.transact((t)=>pi(t));
    }
    /**
   * Gets a snapshot of the current selection. This contains all blocks (included nested blocks)
   * that the selection spans across.
   *
   * If the selection starts / ends halfway through a block, the returned block will be
   * only the part of the block that is included in the selection.
   */ getSelectionCutBlocks() {
        return this.transact((t)=>fi(t));
    }
    /**
   * Sets the selection to a range of blocks.
   * @param startBlock The identifier of the block that should be the start of the selection.
   * @param endBlock The identifier of the block that should be the end of the selection.
   */ setSelection(t, o) {
        return this.transact((r)=>hi(r, t, o));
    }
    /**
   * Checks if the editor is currently editable, or if it's locked.
   * @returns True if the editor is editable, false otherwise.
   */ get isEditable() {
        if (!this._tiptapEditor) {
            if (!this.headless) throw new Error("no editor, but also not headless?");
            return !1;
        }
        return this._tiptapEditor.isEditable === void 0 ? !0 : this._tiptapEditor.isEditable;
    }
    /**
   * Makes the editor editable or locks it, depending on the argument passed.
   * @param editable True to make the editor editable, or false to lock it.
   */ set isEditable(t) {
        if (!this._tiptapEditor) {
            if (!this.headless) throw new Error("no editor, but also not headless?");
            return;
        }
        this._tiptapEditor.options.editable !== t && this._tiptapEditor.setEditable(t);
    }
    /**
   * Inserts new blocks into the editor. If a block's `id` is undefined, BlockNote generates one automatically. Throws an
   * error if the reference block could not be found.
   * @param blocksToInsert An array of partial blocks that should be inserted.
   * @param referenceBlock An identifier for an existing block, at which the new blocks should be inserted.
   * @param placement Whether the blocks should be inserted just before, just after, or nested inside the
   * `referenceBlock`.
   */ insertBlocks(t, o, r = "before") {
        return this.transact((s)=>xr(s, t, o, r));
    }
    /**
   * Updates an existing block in the editor. Since updatedBlock is a PartialBlock object, some fields might not be
   * defined. These undefined fields are kept as-is from the existing block. Throws an error if the block to update could
   * not be found.
   * @param blockToUpdate The block that should be updated.
   * @param update A partial block which defines how the existing block should be changed.
   */ updateBlock(t, o) {
        return this.transact((r)=>Mr(r, t, o));
    }
    /**
   * Removes existing blocks from the editor. Throws an error if any of the blocks could not be found.
   * @param blocksToRemove An array of identifiers for existing blocks that should be removed.
   */ removeBlocks(t) {
        return this.transact((o)=>_t(o, t, []).removedBlocks);
    }
    /**
   * Replaces existing blocks in the editor with new blocks. If the blocks that should be removed are not adjacent or
   * are at different nesting levels, `blocksToInsert` will be inserted at the position of the first block in
   * `blocksToRemove`. Throws an error if any of the blocks to remove could not be found.
   * @param blocksToRemove An array of blocks that should be replaced.
   * @param blocksToInsert An array of partial blocks to replace the old ones with.
   */ replaceBlocks(t, o) {
        return this.transact((r)=>_t(r, t, o));
    }
    /**
   * Undo the last action.
   */ undo() {
        return this.options.collaboration ? this.exec(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$undo$2d$plugin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undoCommand"]) : this.exec(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$history$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undo"]);
    }
    /**
   * Redo the last action.
   */ redo() {
        return this.options.collaboration ? this.exec(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$undo$2d$plugin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["redoCommand"]) : this.exec(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$history$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["redo"]);
    }
    /**
   * Insert a piece of content at the current cursor position.
   *
   * @param content can be a string, or array of partial inline content elements
   */ insertInlineContent(t, { updateSelection: o = !1 } = {}) {
        const r = F(t, this.pmSchema);
        this.transact((s)=>{
            ui(s, {
                from: s.selection.from,
                to: s.selection.to
            }, r, {
                updateSelection: o
            });
        });
    }
    /**
   * Gets the active text styles at the text cursor position or at the end of the current selection if it's active.
   */ getActiveStyles() {
        return this.transact((t)=>{
            const o = {}, r = t.selection.$to.marks();
            for (const s of r){
                const i = this.schema.styleSchema[s.type.name];
                if (!i) {
                    // Links are not considered styles in blocknote
                    s.type.name !== "link" && // "blocknoteIgnore" tagged marks (such as comments) are also not considered BlockNote "styles"
                    !s.type.spec.blocknoteIgnore && console.warn("mark not found in styleschema", s.type.name);
                    continue;
                }
                i.propSchema === "boolean" ? o[i.type] = !0 : o[i.type] = s.attrs.stringValue;
            }
            return o;
        });
    }
    /**
   * Adds styles to the currently selected content.
   * @param styles The styles to add.
   */ addStyles(t) {
        for (const [o, r] of Object.entries(t)){
            const s = this.schema.styleSchema[o];
            if (!s) throw new Error(`style ${o} not found in styleSchema`);
            if (s.propSchema === "boolean") this._tiptapEditor.commands.setMark(o);
            else if (s.propSchema === "string") this._tiptapEditor.commands.setMark(o, {
                stringValue: r
            });
            else throw new j(s.propSchema);
        }
    }
    /**
   * Removes styles from the currently selected content.
   * @param styles The styles to remove.
   */ removeStyles(t) {
        for (const o of Object.keys(t))this._tiptapEditor.commands.unsetMark(o);
    }
    /**
   * Toggles styles on the currently selected content.
   * @param styles The styles to toggle.
   */ toggleStyles(t) {
        for (const [o, r] of Object.entries(t)){
            const s = this.schema.styleSchema[o];
            if (!s) throw new Error(`style ${o} not found in styleSchema`);
            if (s.propSchema === "boolean") this._tiptapEditor.commands.toggleMark(o);
            else if (s.propSchema === "string") this._tiptapEditor.commands.toggleMark(o, {
                stringValue: r
            });
            else throw new j(s.propSchema);
        }
    }
    /**
   * Gets the currently selected text.
   */ getSelectedText() {
        return this.transact((t)=>t.doc.textBetween(t.selection.from, t.selection.to));
    }
    /**
   * Gets the URL of the last link in the current selection, or `undefined` if there are no links in the selection.
   */ getSelectedLinkUrl() {
        return this._tiptapEditor.getAttributes("link").href;
    }
    /**
   * Creates a new link to replace the selected content.
   * @param url The link URL.
   * @param text The text to display the link with.
   */ createLink(t, o) {
        if (t === "") return;
        const r = this.pmSchema.mark("link", {
            href: t
        });
        this.transact((s)=>{
            const { from: i, to: a } = s.selection;
            o ? s.insertText(o, i, a).addMark(i, i + o.length, r) : s.setSelection(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prosemirror$2d$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextSelection"].create(s.doc, a)).addMark(i, a, r);
        });
    }
    /**
   * Checks if the block containing the text cursor can be nested.
   */ canNestBlock() {
        return si(this);
    }
    /**
   * Nests the block containing the text cursor into the block above it.
   */ nestBlock() {
        Gn(this);
    }
    /**
   * Checks if the block containing the text cursor is nested.
   */ canUnnestBlock() {
        return ii(this);
    }
    /**
   * Lifts the block containing the text cursor out of its parent.
   */ unnestBlock() {
        ri(this);
    }
    /**
   * Moves the selected blocks up. If the previous block has children, moves
   * them to the end of its children. If there is no previous block, but the
   * current blocks share a common parent, moves them out of & before it.
   */ moveBlocksUp() {
        return ti(this);
    }
    /**
   * Moves the selected blocks down. If the next block has children, moves
   * them to the start of its children. If there is no next block, but the
   * current blocks share a common parent, moves them out of & after it.
   */ moveBlocksDown() {
        return ni(this);
    }
    /**
   * Exports blocks into a simplified HTML string. To better conform to HTML standards, children of blocks which aren't list
   * items are un-nested in the output HTML.
   *
   * @param blocks An array of blocks that should be serialized into HTML.
   * @returns The blocks, serialized as an HTML string.
   */ async blocksToHTMLLossy(t = this.document) {
        return Ke(this.pmSchema, this).exportBlocks(t, {});
    }
    /**
   * Serializes blocks into an HTML string in the format that would normally be rendered by the editor.
   *
   * Use this method if you want to server-side render HTML (for example, a blog post that has been edited in BlockNote)
   * and serve it to users without loading the editor on the client (i.e.: displaying the blog post)
   *
   * @param blocks An array of blocks that should be serialized into HTML.
   * @returns The blocks, serialized as an HTML string.
   */ async blocksToFullHTML(t) {
        return Nr(this.pmSchema, this).serializeBlocks(t, {});
    }
    /**
   * Parses blocks from an HTML string. Tries to create `Block` objects out of any HTML block-level elements, and
   * `InlineNode` objects from any HTML inline elements, though not all element types are recognized. If BlockNote
   * doesn't recognize an HTML element's tag, it will parse it as a paragraph or plain text.
   * @param html The HTML string to parse blocks from.
   * @returns The blocks parsed from the HTML string.
   */ async tryParseHTMLToBlocks(t) {
        return Jn(t, this.pmSchema);
    }
    /**
   * Serializes blocks into a Markdown string. The output is simplified as Markdown does not support all features of
   * BlockNote - children of blocks which aren't list items are un-nested and certain styles are removed.
   * @param blocks An array of blocks that should be serialized into Markdown.
   * @returns The blocks, serialized as a Markdown string.
   */ async blocksToMarkdownLossy(t = this.document) {
        return ki(t, this.pmSchema, this, {});
    }
    /**
   * Creates a list of blocks from a Markdown string. Tries to create `Block` and `InlineNode` objects based on
   * Markdown syntax, though not all symbols are recognized. If BlockNote doesn't recognize a symbol, it will parse it
   * as text.
   * @param markdown The Markdown string to parse blocks from.
   * @returns The blocks parsed from the Markdown string.
   */ async tryParseMarkdownToBlocks(t) {
        return Bi(t, this.pmSchema);
    }
    /**
   * Updates the user info for the current user that's shown to other collaborators.
   */ updateCollaborationUserInfo(t) {
        if (!this.options.collaboration) throw new Error("Cannot update collaboration user info when collaboration is disabled.");
        this.extensions.yCursorPlugin.updateUser(t);
    }
    /**
   * A callback function that runs whenever the editor's contents change.
   *
   * @param callback The callback to execute.
   * @returns A function to remove the callback.
   */ onChange(t) {
        if (this.headless) return;
        const o = ({ transaction: r, appendedTransactions: s })=>{
            t(this, {
                getChanges: ()=>Br(r, s)
            });
        };
        return this._tiptapEditor.on("v3-update", o), ()=>{
            this._tiptapEditor.off("v3-update", o);
        };
    }
    /**
   * A callback function that runs whenever the text cursor position or selection changes.
   *
   * @param callback The callback to execute.
   * @returns A function to remove the callback.
   */ onSelectionChange(t, o) {
        if (this.headless) return;
        const r = (s)=>{
            s.transaction.getMeta(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$y$2d$prosemirror$2f$src$2f$plugins$2f$keys$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ySyncPluginKey"]) && !o || t(this);
        };
        return this._tiptapEditor.on("selectionUpdate", r), ()=>{
            this._tiptapEditor.off("selectionUpdate", r);
        };
    }
    /**
   * A callback function that runs when the editor has been initialized.
   *
   * This can be useful for plugins to initialize themselves after the editor has been initialized.
   */ onCreate(t) {
        return this.on("create", t), ()=>{
            this.off("create", t);
        };
    }
    getSelectionBoundingBox() {
        if (!this.prosemirrorView) return;
        const { selection: t } = this.prosemirrorState, { ranges: o } = t, r = Math.min(...o.map((i)=>i.$from.pos)), s = Math.max(...o.map((i)=>i.$to.pos));
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNodeSelection"])(t)) {
            const i = this.prosemirrorView.nodeDOM(r);
            if (i) return i.getBoundingClientRect();
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["posToDOMRect"])(this.prosemirrorView, r, s);
    }
    get isEmpty() {
        const t = this.document;
        return t.length === 0 || t.length === 1 && t[0].type === "paragraph" && t[0].content.length === 0;
    }
    openSuggestionMenu(t, o) {
        this.prosemirrorView && (this.focus(), this.transact((r)=>{
            o != null && o.deleteTriggerCharacter && r.insertText(t), r.scrollIntoView().setMeta(this.suggestionMenus.plugins[0], {
                triggerCharacter: t,
                deleteTriggerCharacter: (o == null ? void 0 : o.deleteTriggerCharacter) || !1,
                ignoreQueryLength: (o == null ? void 0 : o.ignoreQueryLength) || !1
            });
        }));
    }
    // `forceSelectionVisible` determines whether the editor selection is shows
    // even when the editor is not focused. This is useful for e.g. creating new
    // links, so the user still sees the affected content when an input field is
    // focused.
    // TODO: Reconsider naming?
    getForceSelectionVisible() {
        return this.showSelectionPlugin.getEnabled();
    }
    setForceSelectionVisible(t) {
        this.showSelectionPlugin.setEnabled(t);
    }
    /**
   * This will convert HTML into a format that is compatible with BlockNote.
   */ convertHtmlToBlockNoteHtml(t) {
        return Kn(t.trim()).innerHTML;
    }
    /**
   * Paste HTML into the editor. Defaults to converting HTML to BlockNote HTML.
   * @param html The HTML to paste.
   * @param raw Whether to paste the HTML as is, or to convert it to BlockNote HTML.
   */ pasteHTML(t, o = !1) {
        var s;
        let r = t;
        o || (r = this.convertHtmlToBlockNoteHtml(t)), r && ((s = this.prosemirrorView) == null || s.pasteHTML(r));
    }
    /**
   * Paste text into the editor. Defaults to interpreting text as markdown.
   * @param text The text to paste.
   */ pasteText(t) {
        var o;
        return (o = this.prosemirrorView) == null ? void 0 : o.pasteText(t);
    }
    /**
   * Paste markdown into the editor.
   * @param markdown The markdown to paste.
   */ async pasteMarkdown(t) {
        return this.pasteHTML(await Xn(t));
    }
}
const Wc = {
    gray: {
        text: "#9b9a97",
        background: "#ebeced"
    },
    brown: {
        text: "#64473a",
        background: "#e9e5e3"
    },
    red: {
        text: "#e03e3e",
        background: "#fbe4e4"
    },
    orange: {
        text: "#d9730d",
        background: "#f6e9d9"
    },
    yellow: {
        text: "#dfab01",
        background: "#fbf3db"
    },
    green: {
        text: "#4d6461",
        background: "#ddedea"
    },
    blue: {
        text: "#0b6e99",
        background: "#ddebf1"
    },
    purple: {
        text: "#6940a5",
        background: "#eae4f2"
    },
    pink: {
        text: "#ad1a72",
        background: "#f4dfeb"
    }
}, jc = {
    gray: {
        text: "#bebdb8",
        background: "#9b9a97"
    },
    brown: {
        text: "#8e6552",
        background: "#64473a"
    },
    red: {
        text: "#ec4040",
        background: "#be3434"
    },
    orange: {
        text: "#e3790d",
        background: "#b7600a"
    },
    yellow: {
        text: "#dfab01",
        background: "#b58b00"
    },
    green: {
        text: "#6b8b87",
        background: "#4d6461"
    },
    blue: {
        text: "#0e87bc",
        background: "#0b6e99"
    },
    purple: {
        text: "#8552d7",
        background: "#6940a5"
    },
    pink: {
        text: "#da208f",
        background: "#ad1a72"
    }
};
class Gc {
    constructor(n, t, o){
        this.mappings = t, this.options = o;
    }
    async resolveFile(n) {
        var o;
        if (!((o = this.options) != null && o.resolveFileUrl)) return (await fetch(n)).blob();
        const t = await this.options.resolveFileUrl(n);
        return t instanceof Blob ? t : (await fetch(t)).blob();
    }
    mapStyles(n) {
        return Object.entries(n).map(([o, r])=>this.mappings.styleMapping[o](r, this));
    }
    mapInlineContent(n) {
        return this.mappings.inlineContentMapping[n.type](n, this);
    }
    transformInlineContent(n) {
        return n.map((t)=>this.mapInlineContent(t));
    }
    async mapBlock(n, t, o) {
        return this.mappings.blockMapping[n.type](n, this, t, o);
    }
}
function qc(e) {
    return {
        createBlockMapping: (n)=>n,
        createInlineContentMapping: (n)=>n,
        createStyleMapping: (n)=>n
    };
}
let He;
async function ec() {
    return He || (He = (async ()=>{
        const [e, n] = await Promise.all([
            __turbopack_context__.r("[project]/node_modules/emoji-mart/dist/module.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i),
            __turbopack_context__.r("[project]/node_modules/@emoji-mart/data/sets/15/native.json (json, async loader)")(__turbopack_context__.i)
        ]), t = "default" in e ? e.default : e, o = "default" in n ? n.default : n;
        return await t.init({
            data: o
        }), {
            emojiMart: t,
            emojiData: o
        };
    })(), He);
}
async function Kc(e, n) {
    if (!zs("text", e)) return [];
    const { emojiData: t, emojiMart: o } = await ec();
    return (n.trim() === "" ? Object.values(t.emojis) : await o.SearchIndex.search(n)).map((s)=>({
            id: s.skins[0].native,
            onItemClick: ()=>e.insertInlineContent(s.skins[0].native + " ")
        }));
}
function Jc(e, ...n) {
    const t = [
        ...e
    ];
    for (const o of n)for (const r of o){
        const s = t.findLastIndex((i)=>i.group === r.group);
        s === -1 ? t.push(r) : t.splice(s + 1, 0, r);
    }
    return t;
}
;
 //# sourceMappingURL=blocknote.js.map
}}),
}]);

//# sourceMappingURL=node_modules_%40blocknote_core_dist_5d48b505._.js.map