{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/syscall.ts"], "sourcesContent": ["import { ConvexError } from \"../../values/errors.js\";\nimport { jsonToConvex } from \"../../values/value.js\";\n\ndeclare const Convex: {\n  syscall: (op: string, jsonArgs: string) => string;\n  asyncSyscall: (op: string, jsonArgs: string) => Promise<string>;\n  jsSyscall: (op: string, args: Record<string, any>) => any;\n};\n/**\n * Perform a syscall, taking in a JSON-encodable object as an argument, serializing with\n * JSON.stringify, calling into Rust, and then parsing the response as a JSON-encodable\n * value. If one of your arguments is a Convex value, you must call `convexToJson` on it\n * before passing it to this function, and if the return value has a Convex value, you're\n * also responsible for calling `jsonToConvex`: This layer only deals in JSON.\n */\n\nexport function performSyscall(op: string, arg: Record<string, any>): any {\n  if (typeof Convex === \"undefined\" || Convex.syscall === undefined) {\n    throw new Error(\n      \"The Convex database and auth objects are being used outside of a Convex backend. \" +\n        \"Did you mean to use `useQuery` or `useMutation` to call a Convex function?\",\n    );\n  }\n  const resultStr = Convex.syscall(op, JSON.stringify(arg));\n  return JSON.parse(resultStr);\n}\n\nexport async function performAsyncSyscall(\n  op: string,\n  arg: Record<string, any>,\n): Promise<any> {\n  if (typeof Convex === \"undefined\" || Convex.asyncSyscall === undefined) {\n    throw new Error(\n      \"The Convex database and auth objects are being used outside of a Convex backend. \" +\n        \"Did you mean to use `useQuery` or `useMutation` to call a Convex function?\",\n    );\n  }\n  let resultStr;\n  try {\n    resultStr = await Convex.asyncSyscall(op, JSON.stringify(arg));\n  } catch (e: any) {\n    // Rethrow the exception to attach stack trace starting from here.\n    // If the error came from JS it will include its own stack trace in the message.\n    // If it came from Rust it won't.\n\n    // This only happens if we're propagating ConvexErrors\n    if (e.data !== undefined) {\n      const rethrown = new ConvexError(e.message);\n      rethrown.data = jsonToConvex(e.data);\n      throw rethrown;\n    }\n    throw new Error(e.message);\n  }\n  return JSON.parse(resultStr);\n}\n\n/**\n * Call into a \"JS\" syscall. Like `performSyscall`, this calls a dynamically linked\n * function set up in the Convex function execution. Unlike `performSyscall`, the\n * arguments do not need to be JSON-encodable and neither does the return value.\n *\n * @param op\n * @param arg\n * @returns\n */\nexport function performJsSyscall(op: string, arg: Record<string, any>): any {\n  if (typeof Convex === \"undefined\" || Convex.jsSyscall === undefined) {\n    throw new Error(\n      \"The Convex database and auth objects are being used outside of a Convex backend. \" +\n        \"Did you mean to use `useQuery` or `useMutation` to call a Convex function?\",\n    );\n  }\n  return Convex.jsSyscall(op, arg);\n}\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,mBAAmB;AAC5B,SAAS,oBAAoB;;;;AAetB,SAAS,eAAe,EAAA,EAAY,GAAA,EAA+B;IACxE,IAAI,OAAO,WAAW,eAAe,OAAO,OAAA,KAAY,KAAA,GAAW;QACjE,MAAM,IAAI,MACR;IAGJ;IACA,MAAM,YAAY,OAAO,OAAA,CAAQ,IAAI,KAAK,SAAA,CAAU,GAAG,CAAC;IACxD,OAAO,KAAK,KAAA,CAAM,SAAS;AAC7B;AAEA,eAAsB,oBACpB,EAAA,EACA,GAAA,EACc;IACd,IAAI,OAAO,WAAW,eAAe,OAAO,YAAA,KAAiB,KAAA,GAAW;QACtE,MAAM,IAAI,MACR;IAGJ;IACA,IAAI;IACJ,IAAI;QACF,YAAY,MAAM,OAAO,YAAA,CAAa,IAAI,KAAK,SAAA,CAAU,GAAG,CAAC;IAC/D,EAAA,OAAS,GAAQ;QAMf,IAAI,EAAE,IAAA,KAAS,KAAA,GAAW;YACxB,MAAM,WAAW,iKAAI,cAAA,CAAY,EAAE,OAAO;YAC1C,SAAS,IAAA,IAAO,8KAAA,EAAa,EAAE,IAAI;YACnC,MAAM;QACR;QACA,MAAM,IAAI,MAAM,EAAE,OAAO;IAC3B;IACA,OAAO,KAAK,KAAA,CAAM,SAAS;AAC7B;AAWO,SAAS,iBAAiB,EAAA,EAAY,GAAA,EAA+B;IAC1E,IAAI,OAAO,WAAW,eAAe,OAAO,SAAA,KAAc,KAAA,GAAW;QACnE,MAAM,IAAI,MACR;IAGJ;IACA,OAAO,OAAO,SAAA,CAAU,IAAI,GAAG;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/actions_impl.ts"], "sourcesContent": ["import { convexTo<PERSON>son, jsonToConvex, Value } from \"../../values/index.js\";\nimport { version } from \"../../index.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\nimport { parseArgs } from \"../../common/index.js\";\nimport { FunctionReference } from \"../../server/api.js\";\nimport { getFunctionAddress } from \"../components/paths.js\";\n\nfunction syscallArgs(\n  requestId: string,\n  functionReference: any,\n  args?: Record<string, Value>,\n) {\n  const address = getFunctionAddress(functionReference);\n  return {\n    ...address,\n    args: convexToJson(parseArgs(args)),\n    version,\n    requestId,\n  };\n}\n\nexport function setupActionCalls(requestId: string) {\n  return {\n    runQuery: async (\n      query: FunctionReference<\"query\", \"public\" | \"internal\">,\n      args?: Record<string, Value>,\n    ): Promise<any> => {\n      const result = await performAsyncSyscall(\n        \"1.0/actions/query\",\n        syscallArgs(requestId, query, args),\n      );\n      return jsonToConvex(result);\n    },\n    runMutation: async (\n      mutation: FunctionReference<\"mutation\", \"public\" | \"internal\">,\n      args?: Record<string, Value>,\n    ): Promise<any> => {\n      const result = await performAsyncSyscall(\n        \"1.0/actions/mutation\",\n        syscallArgs(requestId, mutation, args),\n      );\n      return jsonToConvex(result);\n    },\n    runAction: async (\n      action: FunctionReference<\"action\", \"public\" | \"internal\">,\n      args?: Record<string, Value>,\n    ): Promise<any> => {\n      const result = await performAsyncSyscall(\n        \"1.0/actions/action\",\n        syscallArgs(requestId, action, args),\n      );\n      return jsonToConvex(result);\n    },\n  };\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,cAAc,oBAA2B;;AAClD,SAAS,eAAe;AACxB,SAAS,2BAA2B;AACpC,SAAS,iBAAiB;AAE1B,SAAS,0BAA0B;;;;;;;AAEnC,SAAS,YACP,SAAA,EACA,iBAAA,EACA,IAAA,EACA;IACA,MAAM,wLAAU,qBAAA,EAAmB,iBAAiB;IACpD,OAAO;QACL,GAAG,OAAA;QACH,sKAAM,eAAA,kKAAa,YAAA,EAAU,IAAI,CAAC;QAClC,qKAAA;QACA;IACF;AACF;AAEO,SAAS,iBAAiB,SAAA,EAAmB;IAClD,OAAO;QACL,UAAU,OACR,OACA,SACiB;YACjB,MAAM,SAAS,gLAAM,sBAAA,EACnB,qBACA,YAAY,WAAW,OAAO,IAAI;YAEpC,QAAO,8KAAA,EAAa,MAAM;QAC5B;QACA,aAAa,OACX,UACA,SACiB;YACjB,MAAM,SAAS,OAAM,+LAAA,EACnB,wBACA,YAAY,WAAW,UAAU,IAAI;YAEvC,uKAAO,eAAA,EAAa,MAAM;QAC5B;QACA,WAAW,OACT,QACA,SACiB;YACjB,MAAM,SAAS,gLAAM,sBAAA,EACnB,sBACA,YAAY,WAAW,QAAQ,IAAI;YAErC,uKAAO,eAAA,EAAa,MAAM;QAC5B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/vector_search.ts"], "sourcesContent": ["import { Id, Value } from \"../values/value.js\";\nimport {\n  DocumentByInfo,\n  FieldTypeFromFieldPath,\n  GenericDataModel,\n  GenericDocument,\n  GenericTableInfo,\n  GenericVectorIndexConfig,\n  NamedTableInfo,\n  NamedVectorIndex,\n  TableNamesInDataModel,\n  VectorIndexNames,\n} from \"./data_model.js\";\n\n/**\n * An object with parameters for performing a vector search against a vector index.\n * @public\n */\nexport interface VectorSearchQuery<\n  TableInfo extends GenericTableInfo,\n  IndexName extends VectorIndexNames<TableInfo>,\n> {\n  /**\n   * The query vector.\n   *\n   * This must have the same length as the `dimensions` of the index.\n   * This vector search will return the IDs of the documents most similar to\n   * this vector.\n   */\n  vector: number[];\n  /**\n   * The number of results to return. If specified, must be between 1 and 256\n   * inclusive.\n   *\n   * @default 10\n   */\n  limit?: number;\n  /**\n   * Optional filter expression made up of `q.or` and `q.eq` operating\n   * over the filter fields of the index.\n   *\n   * e.g. `filter: q => q.or(q.eq(\"genre\", \"comedy\"), q.eq(\"genre\", \"drama\"))`\n   *\n   * @param q\n   * @returns\n   */\n  filter?: (\n    q: VectorFilterBuilder<\n      DocumentByInfo<TableInfo>,\n      NamedVectorIndex<TableInfo, IndexName>\n    >,\n  ) => FilterExpression<boolean>;\n}\n\nexport type VectorSearch<\n  DataModel extends GenericDataModel,\n  TableName extends TableNamesInDataModel<DataModel>,\n  IndexName extends VectorIndexNames<NamedTableInfo<DataModel, TableName>>,\n> = (\n  tableName: TableName,\n  indexName: IndexName,\n  query: VectorSearchQuery<NamedTableInfo<DataModel, TableName>, IndexName>,\n) => Promise<Array<{ _id: Id<TableName>; _score: number }>>;\n\n/**\n * Expressions are evaluated to produce a {@link values.Value} in the course of executing a query.\n *\n * To construct an expression, use the {@link VectorFilterBuilder} provided within\n * {@link VectorSearchQuery}.\n *\n * @typeParam T - The type that this expression evaluates to.\n * @public\n */\nexport abstract class FilterExpression<T extends Value | undefined> {\n  // Property for nominal type support.\n  private _isExpression: undefined;\n\n  // Property to distinguish expressions by the type they resolve to.\n  private _value!: T;\n\n  /**\n   * @internal\n   */\n  constructor() {\n    // only defining the constructor so we can mark it as internal and keep\n    // it out of the docs.\n  }\n}\n\n/**\n * An interface for defining filters for vector searches.\n *\n * This has a similar interface to {@link FilterBuilder}, which is used in\n * database queries, but supports only the methods that can be efficiently\n * done in a vector search.\n *\n * @public\n */\nexport interface VectorFilterBuilder<\n  Document extends GenericDocument,\n  VectorIndexConfig extends GenericVectorIndexConfig,\n> {\n  //  Comparisons  /////////////////////////////////////////////////////////////\n\n  /**\n   * Is the field at `fieldName` equal to `value`\n   *\n   * @public\n   * */\n  eq<FieldName extends VectorIndexConfig[\"filterFields\"]>(\n    fieldName: FieldName,\n    value: FieldTypeFromFieldPath<Document, FieldName>,\n  ): FilterExpression<boolean>;\n\n  //  Logic  ///////////////////////////////////////////////////////////////////\n\n  /**\n   * `exprs[0] || exprs[1] || ... || exprs[n]`\n   *\n   * @public\n   */\n  or(...exprs: Array<FilterExpression<boolean>>): FilterExpression<boolean>;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAyEO,MAAe,iBAA8C;IAAA;;GAAA,GAUlE,aAAc;QARd,qCAAA;QAAA,cAAA,IAAA,EAAQ;QAGR,mEAAA;QAAA,cAAA,IAAA,EAAQ;IAQR;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/validate.ts"], "sourcesContent": ["export function validateArg(\n  arg: any,\n  idx: number,\n  method: string,\n  argName: string,\n) {\n  if (arg === undefined) {\n    throw new TypeError(\n      `Must provide arg ${idx} \\`${argName}\\` to \\`${method}\\``,\n    );\n  }\n}\n\nexport function validateArgIsInteger(\n  arg: any,\n  idx: number,\n  method: string,\n  argName: string,\n) {\n  if (!Number.isInteger(arg)) {\n    throw new TypeError(\n      `Arg ${idx} \\`${argName}\\` to \\`${method}\\` must be an integer`,\n    );\n  }\n}\n\nexport function validateArgIsNonNegativeInteger(\n  arg: any,\n  idx: number,\n  method: string,\n  argName: string,\n) {\n  if (!Number.isInteger(arg) || arg < 0) {\n    throw new TypeError(\n      `Arg ${idx} \\`${argName}\\` to \\`${method}\\` must be a non-negative integer`,\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAO,SAAS,YACd,GAAA,EACA,GAAA,EACA,MAAA,EACA,OAAA,EACA;IACA,IAAI,QAAQ,KAAA,GAAW;QACrB,MAAM,IAAI,UACR,CAAA,iBAAA,EAAoB,GAAG,CAAA,GAAA,EAAM,OAAO,CAAA,QAAA,EAAW,MAAM,CAAA,EAAA,CAAA;IAEzD;AACF;AAEO,SAAS,qBACd,GAAA,EACA,GAAA,EACA,MAAA,EACA,OAAA,EACA;IACA,IAAI,CAAC,OAAO,SAAA,CAAU,GAAG,GAAG;QAC1B,MAAM,IAAI,UACR,CAAA,IAAA,EAAO,GAAG,CAAA,GAAA,EAAM,OAAO,CAAA,QAAA,EAAW,MAAM,CAAA,qBAAA,CAAA;IAE5C;AACF;AAEO,SAAS,gCACd,GAAA,EACA,GAAA,EACA,MAAA,EACA,OAAA,EACA;IACA,IAAI,CAAC,OAAO,SAAA,CAAU,GAAG,KAAK,MAAM,GAAG;QACrC,MAAM,IAAI,UACR,CAAA,IAAA,EAAO,GAAG,CAAA,GAAA,EAAM,OAAO,CAAA,QAAA,EAAW,MAAM,CAAA,iCAAA,CAAA;IAE5C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/vector_search_impl.ts"], "sourcesContent": ["import { J<PERSON>NValue } from \"../../values/index.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\nimport { version } from \"../../index.js\";\nimport {\n  FilterExpression,\n  VectorFilterBuilder,\n  VectorSearch,\n  VectorSearchQuery,\n} from \"../vector_search.js\";\nimport {\n  FieldTypeFromFieldPath,\n  GenericDataModel,\n  GenericDocument,\n  GenericTableInfo,\n  GenericVectorIndexConfig,\n} from \"../data_model.js\";\nimport { validateArg } from \"./validate.js\";\nimport { Value, convexOrUndefinedToJson } from \"../../values/value.js\";\n\nexport function setupActionVectorSearch(\n  requestId: string,\n): VectorSearch<GenericDataModel, string, string> {\n  return async (\n    tableName: string,\n    indexName: string,\n    query: VectorSearchQuery<GenericTableInfo, string>,\n  ) => {\n    validateArg(tableName, 1, \"vectorSearch\", \"tableName\");\n    validateArg(indexName, 2, \"vectorSearch\", \"indexName\");\n    validateArg(query, 3, \"vectorSearch\", \"query\");\n    if (\n      !query.vector ||\n      !Array.isArray(query.vector) ||\n      query.vector.length === 0\n    ) {\n      throw Error(\"`vector` must be a non-empty Array in vectorSearch\");\n    }\n\n    return await new VectorQueryImpl(\n      requestId,\n      tableName + \".\" + indexName,\n      query,\n    ).collect();\n  };\n}\n\nexport class VectorQueryImpl {\n  private requestId: string;\n  private state:\n    | { type: \"preparing\"; query: SerializedVectorQuery }\n    | { type: \"consumed\" };\n\n  constructor(\n    requestId: string,\n    indexName: string,\n    query: VectorSearchQuery<GenericTableInfo, string>,\n  ) {\n    this.requestId = requestId;\n    const filters = query.filter\n      ? serializeExpression(query.filter(filterBuilderImpl))\n      : null;\n\n    this.state = {\n      type: \"preparing\",\n      query: {\n        indexName,\n        limit: query.limit,\n        vector: query.vector,\n        expressions: filters,\n      },\n    };\n  }\n\n  async collect(): Promise<Array<any>> {\n    if (this.state.type === \"consumed\") {\n      throw new Error(\"This query is closed and can't emit any more values.\");\n    }\n    const query = this.state.query;\n    this.state = { type: \"consumed\" };\n\n    const { results } = await performAsyncSyscall(\"1.0/actions/vectorSearch\", {\n      requestId: this.requestId,\n      version,\n      query,\n    });\n    return results;\n  }\n}\n\ntype SerializedVectorQuery = {\n  indexName: string;\n  limit?: number;\n  vector: Array<number>;\n  expressions: JSONValue;\n};\n\ntype ExpressionOrValue<T extends Value | undefined> = FilterExpression<T> | T;\n\n// The `any` type parameter in `Expression<any>` allows us to use this class\n// in place of any `Expression` type in `filterBuilderImpl`.\nexport class ExpressionImpl extends FilterExpression<any> {\n  private inner: JSONValue;\n  constructor(inner: JSONValue) {\n    super();\n    this.inner = inner;\n  }\n\n  serialize(): JSONValue {\n    return this.inner;\n  }\n}\n\nexport function serializeExpression(\n  expr: ExpressionOrValue<Value | undefined>,\n): JSONValue {\n  if (expr instanceof ExpressionImpl) {\n    return expr.serialize();\n  } else {\n    // Assume that the expression is a literal Convex value, which we'll serialize\n    // to its JSON representation.\n    return { $literal: convexOrUndefinedToJson(expr as Value | undefined) };\n  }\n}\n\nexport const filterBuilderImpl: VectorFilterBuilder<\n  GenericDocument,\n  GenericVectorIndexConfig\n> = {\n  //  Comparisons  /////////////////////////////////////////////////////////////\n\n  eq<FieldName extends GenericVectorIndexConfig[\"filterFields\"]>(\n    fieldName: FieldName,\n    value: FieldTypeFromFieldPath<GenericDocument, FieldName>,\n  ): FilterExpression<boolean> {\n    if (typeof fieldName !== \"string\") {\n      throw new Error(\"The first argument to `q.eq` must be a field name.\");\n    }\n    return new ExpressionImpl({\n      $eq: [\n        serializeExpression(new ExpressionImpl({ $field: fieldName })),\n        serializeExpression(value),\n      ],\n    });\n  },\n\n  //  Logic  ///////////////////////////////////////////////////////////////////\n\n  or(...exprs: Array<ExpressionOrValue<boolean>>): FilterExpression<boolean> {\n    return new ExpressionImpl({ $or: exprs.map(serializeExpression) });\n  },\n};\n"], "names": [], "mappings": ";;;;;;;AACA,SAAS,2BAA2B;AACpC,SAAS,eAAe;AACxB;AAaA,SAAS,mBAAmB;AAC5B,SAAgB,+BAA+B;;;;;;;;;;;;;;;AAExC,SAAS,wBACd,SAAA,EACgD;IAChD,OAAO,OACL,WACA,WACA,UACG;QACH,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,WAAW,GAAG,gBAAgB,WAAW;QACrD,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,WAAW,GAAG,gBAAgB,WAAW;QACrD,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,OAAO,GAAG,gBAAgB,OAAO;QAC7C,IACE,CAAC,MAAM,MAAA,IACP,CAAC,MAAM,OAAA,CAAQ,MAAM,MAAM,KAC3B,MAAM,MAAA,CAAO,MAAA,KAAW,GACxB;YACA,MAAM,MAAM,oDAAoD;QAClE;QAEA,OAAO,MAAM,IAAI,gBACf,WACA,YAAY,MAAM,WAClB,OACA,OAAA,CAAQ;IACZ;AACF;AAEO,MAAM,gBAAgB;IAM3B,YACE,SAAA,EACA,SAAA,EACA,KAAA,CACA;QATF,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QASN,IAAA,CAAK,SAAA,GAAY;QACjB,MAAM,UAAU,MAAM,MAAA,GAClB,oBAAoB,MAAM,MAAA,CAAO,iBAAiB,CAAC,IACnD;QAEJ,IAAA,CAAK,KAAA,GAAQ;YACX,MAAM;YACN,OAAO;gBACL;gBACA,OAAO,MAAM,KAAA;gBACb,QAAQ,MAAM,MAAA;gBACd,aAAa;YACf;QACF;IACF;IAEA,MAAM,UAA+B;QACnC,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,YAAY;YAClC,MAAM,IAAI,MAAM,sDAAsD;QACxE;QACA,MAAM,QAAQ,IAAA,CAAK,KAAA,CAAM,KAAA;QACzB,IAAA,CAAK,KAAA,GAAQ;YAAE,MAAM;QAAW;QAEhC,MAAM,EAAE,OAAA,CAAQ,CAAA,GAAI,MAAM,gMAAA,EAAoB,4BAA4B;YACxE,WAAW,IAAA,CAAK,SAAA;uKAChB,UAAA;YACA;QACF,CAAC;QACD,OAAO;IACT;AACF;AAaO,MAAM,uBAAuB,uLAAA,CAAsB;IAExD,YAAY,KAAA,CAAkB;QAC5B,KAAA,CAAM;QAFR,cAAA,IAAA,EAAQ;QAGN,IAAA,CAAK,KAAA,GAAQ;IACf;IAEA,YAAuB;QACrB,OAAO,IAAA,CAAK,KAAA;IACd;AACF;AAEO,SAAS,oBACd,IAAA,EACW;IACX,IAAI,gBAAgB,gBAAgB;QAClC,OAAO,KAAK,SAAA,CAAU;IACxB,OAAO;QAGL,OAAO;YAAE,0KAAU,0BAAA,EAAwB,IAAyB;QAAE;IACxE;AACF;AAEO,MAAM,oBAGT;IAAA,8EAAA;IAGF,IACE,SAAA,EACA,KAAA,EAC2B;QAC3B,IAAI,OAAO,cAAc,UAAU;YACjC,MAAM,IAAI,MAAM,oDAAoD;QACtE;QACA,OAAO,IAAI,eAAe;YACxB,KAAK;gBACH,oBAAoB,IAAI,eAAe;oBAAE,QAAQ;gBAAU,CAAC,CAAC;gBAC7D,oBAAoB,KAAK;aAC3B;QACF,CAAC;IACH;IAAA,8EAAA;IAIA,IAAA,GAAM,KAAA,EAAqE;QACzE,OAAO,IAAI,eAAe;YAAE,KAAK,MAAM,GAAA,CAAI,mBAAmB;QAAE,CAAC;IACnE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/authentication_impl.ts"], "sourcesContent": ["import { Auth } from \"../authentication.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\n\nexport function setupAuth(requestId: string): Auth {\n  return {\n    getUserIdentity: async () => {\n      return await performAsyncSyscall(\"1.0/getUserIdentity\", {\n        requestId,\n      });\n    },\n  };\n}\n"], "names": [], "mappings": ";;;AACA,SAAS,2BAA2B;;;AAE7B,SAAS,UAAU,SAAA,EAAyB;IACjD,OAAO;QACL,iBAAiB,YAAY;YAC3B,OAAO,gLAAM,sBAAA,EAAoB,uBAAuB;gBACtD;YACF,CAAC;QACH;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/filter_builder.ts"], "sourcesContent": ["import { Value, NumericValue } from \"../values/index.js\";\nimport {\n  DocumentByInfo,\n  FieldPaths,\n  FieldTypeFromFieldPath,\n  GenericTableInfo,\n} from \"./data_model.js\";\n\n/**\n * Expressions are evaluated to produce a {@link values.Value} in the course of executing a query.\n *\n * To construct an expression, use the {@link FilterBuilder} provided within\n * {@link OrderedQuery.filter}.\n *\n * @typeParam T - The type that this expression evaluates to.\n * @public\n */\nexport abstract class Expression<T extends Value | undefined> {\n  // Property for nominal type support.\n  private _isExpression: undefined;\n\n  // Property to distinguish expressions by the type they resolve to.\n  private _value!: T;\n\n  /**\n   * @internal\n   */\n  constructor() {\n    // only defining the constructor so we can mark it as internal and keep\n    // it out of the docs.\n  }\n}\n/**\n * An {@link Expression} or a constant {@link values.Value}\n *\n * @public\n */\nexport type ExpressionOrValue<T extends Value | undefined> = Expression<T> | T;\n\n/**\n * An interface for defining filters in queries.\n *\n * `FilterBuilder` has various methods that produce {@link Expression}s.\n * These expressions can be nested together along with constants to express\n * a filter predicate.\n *\n * `FilterBuilder` is used within {@link OrderedQuery.filter} to create query\n * filters.\n *\n * Here are the available methods:\n *\n * |                               |                                               |\n * |-------------------------------|-----------------------------------------------|\n * | **Comparisons**               | Error when `l` and `r` are not the same type. |\n * | [`eq(l, r)`](#eq)             | `l === r`                                     |\n * | [`neq(l, r)`](#neq)           | `l !== r`                                     |\n * | [`lt(l, r)`](#lt)             | `l < r`                                       |\n * | [`lte(l, r)`](#lte)           | `l <= r`                                      |\n * | [`gt(l, r)`](#gt)             | `l > r`                                       |\n * | [`gte(l, r)`](#gte)           | `l >= r`                                      |\n * |                               |                                               |\n * | **Arithmetic**                | Error when `l` and `r` are not the same type. |\n * | [`add(l, r)`](#add)           | `l + r`                                       |\n * | [`sub(l, r)`](#sub)           | `l - r`                                       |\n * | [`mul(l, r)`](#mul)           | `l * r`                                       |\n * | [`div(l, r)`](#div)           | `l / r`                                       |\n * | [`mod(l, r)`](#mod)           | `l % r`                                       |\n * | [`neg(x)`](#neg)              | `-x`                                          |\n * |                               |                                               |\n * | **Logic**                     | Error if any param is not a `bool`.           |\n * | [`not(x)`](#not)              | `!x`                                          |\n * | [`and(a, b, ..., z)`](#and)   | `a && b && ... && z`                          |\n * | [`or(a, b, ..., z)`](#or)     | <code>a &#124;&#124; b &#124;&#124; ... &#124;&#124; z</code> |\n * |                               |                                               |\n * | **Other**                     |                                               |\n * | [`field(fieldPath)`](#field)  | Evaluates to the field at `fieldPath`.        |\n * @public\n */\nexport interface FilterBuilder<TableInfo extends GenericTableInfo> {\n  //  Comparisons  /////////////////////////////////////////////////////////////\n\n  /**\n   * `l === r`\n   *\n   * @public\n   * */\n  eq<T extends Value | undefined>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  /**\n   * `l !== r`\n   *\n   * @public\n   * */\n  neq<T extends Value | undefined>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  /**\n   * `l < r`\n   *\n   * @public\n   */\n  lt<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  /**\n   * `l <= r`\n   *\n   * @public\n   */\n  lte<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  /**\n   * `l > r`\n   *\n   * @public\n   */\n  gt<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  /**\n   * `l >= r`\n   *\n   * @public\n   */\n  gte<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean>;\n\n  //  Arithmetic  //////////////////////////////////////////////////////////////\n\n  /**\n   * `l + r`\n   *\n   * @public\n   */\n  add<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T>;\n\n  /**\n   * `l - r`\n   *\n   * @public\n   */\n  sub<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T>;\n\n  /**\n   * `l * r`\n   *\n   * @public\n   */\n  mul<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T>;\n\n  /**\n   * `l / r`\n   *\n   * @public\n   */\n  div<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T>;\n\n  /**\n   * `l % r`\n   *\n   * @public\n   */\n  mod<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T>;\n\n  /**\n   * `-x`\n   *\n   * @public\n   */\n  neg<T extends NumericValue>(x: ExpressionOrValue<T>): Expression<T>;\n\n  //  Logic  ///////////////////////////////////////////////////////////////////\n\n  /**\n   * `exprs[0] && exprs[1] && ... && exprs[n]`\n   *\n   * @public\n   */\n  and(...exprs: Array<ExpressionOrValue<boolean>>): Expression<boolean>;\n\n  /**\n   * `exprs[0] || exprs[1] || ... || exprs[n]`\n   *\n   * @public\n   */\n  or(...exprs: Array<ExpressionOrValue<boolean>>): Expression<boolean>;\n\n  /**\n   * `!x`\n   *\n   * @public\n   */\n  not(x: ExpressionOrValue<boolean>): Expression<boolean>;\n\n  //  Other  ///////////////////////////////////////////////////////////////////\n\n  /**\n   * Evaluates to the field at the given `fieldPath`.\n   *\n   * For example, in {@link OrderedQuery.filter} this can be used to examine the values being filtered.\n   *\n   * #### Example\n   *\n   * On this object:\n   * ```\n   * {\n   *   \"user\": {\n   *     \"isActive\": true\n   *   }\n   * }\n   * ```\n   *\n   * `field(\"user.isActive\")` evaluates to `true`.\n   *\n   * @public\n   */\n  field<FieldPath extends FieldPaths<TableInfo>>(\n    fieldPath: FieldPath,\n  ): Expression<FieldTypeFromFieldPath<DocumentByInfo<TableInfo>, FieldPath>>;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAiBO,MAAe,WAAwC;IAAA;;GAAA,GAU5D,aAAc;QARd,qCAAA;QAAA,cAAA,IAAA,EAAQ;QAGR,mEAAA;QAAA,cAAA,IAAA,EAAQ;IAQR;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/filter_builder_impl.ts"], "sourcesContent": ["import { JSONValue, Value, NumericValue } from \"../../values/index.js\";\nimport { convexOrUndefinedTo<PERSON><PERSON> } from \"../../values/value.js\";\nimport { GenericTableInfo } from \"../data_model.js\";\nimport {\n  Expression,\n  ExpressionOrValue,\n  FilterBuilder,\n} from \"../filter_builder.js\";\n\n// The `any` type parameter in `Expression<any>` allows us to use this class\n// in place of any `Expression` type in `filterBuilderImpl`.\nexport class ExpressionImpl extends Expression<any> {\n  private inner: JSONValue;\n  constructor(inner: JSONValue) {\n    super();\n    this.inner = inner;\n  }\n\n  serialize(): JSONValue {\n    return this.inner;\n  }\n}\n\nexport function serializeExpression(\n  expr: ExpressionOrValue<Value | undefined>,\n): JSONValue {\n  if (expr instanceof ExpressionImpl) {\n    return expr.serialize();\n  } else {\n    // Assume that the expression is a literal Convex value, which we'll serialize\n    // to its JSON representation.\n    return { $literal: convexOrUndefinedToJson(expr as Value | undefined) };\n  }\n}\n\nexport const filterBuilderImpl: FilterBuilder<GenericTableInfo> = {\n  //  Comparisons  /////////////////////////////////////////////////////////////\n\n  eq<T extends Value | undefined>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $eq: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  neq<T extends Value | undefined>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $neq: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  lt<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $lt: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  lte<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $lte: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  gt<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $gt: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  gte<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $gte: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  //  Arithmetic  //////////////////////////////////////////////////////////////\n\n  add<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $add: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  sub<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $sub: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  mul<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $mul: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  div<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $div: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  mod<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $mod: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  neg<T extends NumericValue>(x: ExpressionOrValue<T>): Expression<T> {\n    return new ExpressionImpl({ $neg: serializeExpression(x) });\n  },\n\n  //  Logic  ///////////////////////////////////////////////////////////////////\n\n  and(...exprs: Array<ExpressionOrValue<boolean>>): Expression<boolean> {\n    return new ExpressionImpl({ $and: exprs.map(serializeExpression) });\n  },\n\n  or(...exprs: Array<ExpressionOrValue<boolean>>): Expression<boolean> {\n    return new ExpressionImpl({ $or: exprs.map(serializeExpression) });\n  },\n\n  not(x: ExpressionOrValue<boolean>): Expression<boolean> {\n    return new ExpressionImpl({ $not: serializeExpression(x) });\n  },\n\n  //  Other  ///////////////////////////////////////////////////////////////////\n  field(fieldPath: string): Expression<any> {\n    return new ExpressionImpl({ $field: fieldPath });\n  },\n};\n"], "names": [], "mappings": ";;;;;AACA,SAAS,+BAA+B;AAExC;;;;;;;;;;;;AAQO,MAAM,4LAAuB,aAAA,CAAgB;IAElD,YAAY,KAAA,CAAkB;QAC5B,KAAA,CAAM;QAFR,cAAA,IAAA,EAAQ;QAGN,IAAA,CAAK,KAAA,GAAQ;IACf;IAEA,YAAuB;QACrB,OAAO,IAAA,CAAK,KAAA;IACd;AACF;AAEO,SAAS,oBACd,IAAA,EACW;IACX,IAAI,gBAAgB,gBAAgB;QAClC,OAAO,KAAK,SAAA,CAAU;IACxB,OAAO;QAGL,OAAO;YAAE,0KAAU,0BAAA,EAAwB,IAAyB;QAAE;IACxE;AACF;AAEO,MAAM,oBAAqD;IAAA,8EAAA;IAGhE,IACE,CAAA,EACA,CAAA,EACqB;QACrB,OAAO,IAAI,eAAe;YACxB,KAAK;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACtD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACqB;QACrB,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,IACE,CAAA,EACA,CAAA,EACqB;QACrB,OAAO,IAAI,eAAe;YACxB,KAAK;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACtD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACqB;QACrB,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,IACE,CAAA,EACA,CAAA,EACqB;QACrB,OAAO,IAAI,eAAe;YACxB,KAAK;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACtD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACqB;QACrB,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAAA,8EAAA;IAIA,KACE,CAAA,EACA,CAAA,EACe;QACf,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACe;QACf,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACe;QACf,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACe;QACf,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,KACE,CAAA,EACA,CAAA,EACe;QACf,OAAO,IAAI,eAAe;YACxB,MAAM;gBAAC,oBAAoB,CAAC;gBAAG,oBAAoB,CAAC,CAAC;aAAA;QACvD,CAAC;IACH;IAEA,KAA4B,CAAA,EAAwC;QAClE,OAAO,IAAI,eAAe;YAAE,MAAM,oBAAoB,CAAC;QAAE,CAAC;IAC5D;IAAA,8EAAA;IAIA,KAAA,GAAO,KAAA,EAA+D;QACpE,OAAO,IAAI,eAAe;YAAE,MAAM,MAAM,GAAA,CAAI,mBAAmB;QAAE,CAAC;IACpE;IAEA,IAAA,GAAM,KAAA,EAA+D;QACnE,OAAO,IAAI,eAAe;YAAE,KAAK,MAAM,GAAA,CAAI,mBAAmB;QAAE,CAAC;IACnE;IAEA,KAAI,CAAA,EAAoD;QACtD,OAAO,IAAI,eAAe;YAAE,MAAM,oBAAoB,CAAC;QAAE,CAAC;IAC5D;IAAA,8EAAA;IAGA,OAAM,SAAA,EAAoC;QACxC,OAAO,IAAI,eAAe;YAAE,QAAQ;QAAU,CAAC;IACjD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/index_range_builder.ts"], "sourcesContent": ["import {\n  GenericIndexFields,\n  GenericDocument,\n  FieldTypeFromFieldPath,\n} from \"./data_model.js\";\n\n/**\n * A type that adds 1 to a number literal type (up to 14).\n *\n * This is necessary to step through the fields in an index.\n */\ntype PlusOne<N extends number> = [\n  1,\n  2,\n  3,\n  4,\n  5,\n  6,\n  7,\n  8,\n  9,\n  10,\n  11,\n  12,\n  13,\n  14,\n  15,\n][N];\n\n/**\n * Builder to define an index range to query.\n *\n * An index range is a description of which documents Convex should consider\n * when running the query.\n *\n * An index range is always a chained list of:\n * 1. 0 or more equality expressions defined with `.eq`.\n * 2. [Optionally] A lower bound expression defined with `.gt` or `.gte`.\n * 3. [Optionally] An upper bound expression defined with `.lt` or `.lte`.\n *\n * **You must step through fields in index order.**\n *\n * Each equality expression must compare a different index field, starting from\n * the beginning and in order. The upper and lower bounds must follow the\n * equality expressions and compare the next field.\n *\n * For example, if there is an index of messages on\n * `[\"projectId\", \"priority\"]`, a range searching for \"messages in 'myProjectId'\n * with priority at least 100\" would look like:\n * ```ts\n * q.eq(\"projectId\", myProjectId)\n *  .gte(\"priority\", 100)\n * ```\n *\n * **The performance of your query is based on the specificity of the range.**\n *\n * This class is designed to only allow you to specify ranges that Convex can\n * efficiently use your index to find. For all other filtering use\n * {@link OrderedQuery.filter}.\n *\n * To learn about indexes, see [Indexes](https://docs.convex.dev/using/indexes).\n * @public\n */\nexport interface IndexRangeBuilder<\n  Document extends GenericDocument,\n  IndexFields extends GenericIndexFields,\n  FieldNum extends number = 0,\n> extends LowerBoundIndexRangeBuilder<Document, IndexFields[FieldNum]> {\n  /**\n   * Restrict this range to documents where `doc[fieldName] === value`.\n   *\n   * @param fieldName - The name of the field to compare. Must be the next field\n   * in the index.\n   * @param value - The value to compare against.\n   */\n  eq(\n    fieldName: IndexFields[FieldNum],\n    value: FieldTypeFromFieldPath<Document, IndexFields[FieldNum]>,\n  ): NextIndexRangeBuilder<Document, IndexFields, FieldNum>;\n}\n\n/**\n * An {@link IndexRangeBuilder} for the next field of the index.\n *\n * This type is careful to check if adding one to the `FieldNum` will exceed\n * the length of the `IndexFields`.\n */\ntype NextIndexRangeBuilder<\n  Document extends GenericDocument,\n  IndexFields extends GenericIndexFields,\n  FieldNum extends number,\n> =\n  PlusOne<FieldNum> extends IndexFields[\"length\"]\n    ? IndexRange\n    : IndexRangeBuilder<Document, IndexFields, PlusOne<FieldNum>>;\n\n/**\n * Builder to define the lower bound of an index range.\n *\n * See {@link IndexRangeBuilder}.\n *\n * @public\n */\nexport interface LowerBoundIndexRangeBuilder<\n  Document extends GenericDocument,\n  IndexFieldName extends string,\n> extends UpperBoundIndexRangeBuilder<Document, IndexFieldName> {\n  /**\n   * Restrict this range to documents where `doc[fieldName] > value`.\n   *\n   * @param fieldName - The name of the field to compare. Must be the next field\n   * in the index.\n   * @param value - The value to compare against.\n   */\n  gt(\n    fieldName: IndexFieldName,\n    value: FieldTypeFromFieldPath<Document, IndexFieldName>,\n  ): UpperBoundIndexRangeBuilder<Document, IndexFieldName>;\n  /**\n   * Restrict this range to documents where `doc[fieldName] >= value`.\n   *\n   * @param fieldName - The name of the field to compare. Must be the next field\n   * in the index.\n   * @param value - The value to compare against.\n   */\n  gte(\n    fieldName: IndexFieldName,\n    value: FieldTypeFromFieldPath<Document, IndexFieldName>,\n  ): UpperBoundIndexRangeBuilder<Document, IndexFieldName>;\n}\n\n/**\n * Builder to define the upper bound of an index range.\n *\n * See {@link IndexRangeBuilder}.\n *\n * @public\n */\nexport interface UpperBoundIndexRangeBuilder<\n  Document extends GenericDocument,\n  IndexFieldName extends string,\n> extends IndexRange {\n  /**\n   * Restrict this range to documents where `doc[fieldName] < value`.\n   *\n   * @param fieldName - The name of the field to compare. Must be the same index\n   * field used in the lower bound (`.gt` or `.gte`) or the next field if no\n   * lower bound was specified.\n   * @param value - The value to compare against.\n   */\n  lt(\n    fieldName: IndexFieldName,\n    value: FieldTypeFromFieldPath<Document, IndexFieldName>,\n  ): IndexRange;\n\n  /**\n   * Restrict this range to documents where `doc[fieldName] <= value`.\n   *\n   * @param fieldName - The name of the field to compare. Must be the same index\n   * field used in the lower bound (`.gt` or `.gte`) or the next field if no\n   * lower bound was specified.\n   * @param value - The value to compare against.\n   */\n  lte(\n    fieldName: IndexFieldName,\n    value: FieldTypeFromFieldPath<Document, IndexFieldName>,\n  ): IndexRange;\n}\n\n/**\n * An expression representing an index range created by\n * {@link IndexRangeBuilder}.\n * @public\n */\nexport abstract class IndexRange {\n  // Property for nominal type support.\n  private _isIndexRange: undefined;\n\n  /**\n   * @internal\n   */\n  constructor() {\n    // only defining the constructor so we can mark it as internal and keep\n    // it out of the docs.\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AA8KO,MAAe,WAAW;IAAA;;GAAA,GAO/B,aAAc;QALd,qCAAA;QAAA,cAAA,IAAA,EAAQ;IAQR;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 515, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/index_range_builder_impl.ts"], "sourcesContent": ["import { convexT<PERSON><PERSON><PERSON>, JSONValue, Value } from \"../../values/index.js\";\nimport { convexOrUndefinedToJson } from \"../../values/value.js\";\nimport { GenericDocument, GenericIndexFields } from \"../data_model.js\";\nimport {\n  IndexRange,\n  IndexRangeBuilder,\n  LowerBoundIndexRangeBuilder,\n  UpperBoundIndexRangeBuilder,\n} from \"../index_range_builder.js\";\n\nexport type SerializedRangeExpression = {\n  type: \"Eq\" | \"Gt\" | \"Gte\" | \"Lt\" | \"Lte\";\n  fieldPath: string;\n  value: JSONValue;\n};\n\nexport class IndexRangeBuilderImpl\n  extends IndexRange\n  implements\n    IndexRangeBuilder<GenericDocument, GenericIndexFields>,\n    LowerBoundIndexRangeBuilder<GenericDocument, string>,\n    UpperBoundIndexRangeBuilder<GenericDocument, string>\n{\n  private rangeExpressions: ReadonlyArray<SerializedRangeExpression>;\n  private isConsumed: boolean;\n  private constructor(\n    rangeExpressions: ReadonlyArray<SerializedRangeExpression>,\n  ) {\n    super();\n    this.rangeExpressions = rangeExpressions;\n    this.isConsumed = false;\n  }\n\n  static new(): IndexRangeBuilderImpl {\n    return new IndexRangeBuilderImpl([]);\n  }\n\n  private consume() {\n    if (this.isConsumed) {\n      throw new Error(\n        \"IndexRangeBuilder has already been used! Chain your method calls like `q => q.eq(...).eq(...)`. See https://docs.convex.dev/using/indexes\",\n      );\n    }\n    this.isConsumed = true;\n  }\n\n  eq(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Eq\",\n        fieldPath: fieldName,\n        value: convexOrUndefinedToJson(value),\n      }),\n    );\n  }\n\n  gt(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Gt\",\n        fieldPath: fieldName,\n        value: convexToJson(value),\n      }),\n    );\n  }\n  gte(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Gte\",\n        fieldPath: fieldName,\n        value: convexToJson(value),\n      }),\n    );\n  }\n  lt(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Lt\",\n        fieldPath: fieldName,\n        value: convexToJson(value),\n      }),\n    );\n  }\n  lte(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Lte\",\n        fieldPath: fieldName,\n        value: convexToJson(value),\n      }),\n    );\n  }\n\n  export() {\n    this.consume();\n    return this.rangeExpressions;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,oBAAsC;;AAG/C;;;;;;;;;;;;;AAaO,MAAM,uMACH,cAAA,CAKV;IAGU,YACN,gBAAA,CACA;QACA,KAAA,CAAM;QALR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QAKN,IAAA,CAAK,gBAAA,GAAmB;QACxB,IAAA,CAAK,UAAA,GAAa;IACpB;IAEA,OAAO,MAA6B;QAClC,OAAO,IAAI,sBAAsB,CAAC,CAAC;IACrC;IAEQ,UAAU;QAChB,IAAI,IAAA,CAAK,UAAA,EAAY;YACnB,MAAM,IAAI,MACR;QAEJ;QACA,IAAA,CAAK,UAAA,GAAa;IACpB;IAEA,GAAG,SAAA,EAAmB,KAAA,EAAc;QAClC,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,sBACT,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO;YAC3B,MAAM;YACN,WAAW;YACX,uKAAO,0BAAA,EAAwB,KAAK;QACtC,CAAC;IAEL;IAEA,GAAG,SAAA,EAAmB,KAAA,EAAc;QAClC,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,sBACT,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO;YAC3B,MAAM;YACN,WAAW;YACX,uKAAO,eAAA,EAAa,KAAK;QAC3B,CAAC;IAEL;IACA,IAAI,SAAA,EAAmB,KAAA,EAAc;QACnC,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,sBACT,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO;YAC3B,MAAM;YACN,WAAW;YACX,QAAO,8KAAA,EAAa,KAAK;QAC3B,CAAC;IAEL;IACA,GAAG,SAAA,EAAmB,KAAA,EAAc;QAClC,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,sBACT,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO;YAC3B,MAAM;YACN,WAAW;YACX,OAAO,+KAAA,EAAa,KAAK;QAC3B,CAAC;IAEL;IACA,IAAI,SAAA,EAAmB,KAAA,EAAc;QACnC,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,sBACT,IAAA,CAAK,gBAAA,CAAiB,MAAA,CAAO;YAC3B,MAAM;YACN,WAAW;YACX,uKAAO,eAAA,EAAa,KAAK;QAC3B,CAAC;IAEL;IAEA,SAAS;QACP,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAA,CAAK,gBAAA;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 601, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/search_filter_builder.ts"], "sourcesContent": ["import {\n  FieldType<PERSON><PERSON><PERSON><PERSON>Path,\n  GenericDocument,\n  GenericSearchIndexConfig,\n} from \"./data_model.js\";\n\n/**\n * Builder for defining search filters.\n *\n * A search filter is a chained list of:\n * 1. One search expression constructed with `.search`.\n * 2. Zero or more equality expressions constructed with `.eq`.\n *\n * The search expression must search for text in the index's `searchField`. The\n * filter expressions can use any of the `filterFields` defined in the index.\n *\n * For all other filtering use {@link OrderedQuery.filter}.\n *\n * To learn about full text search, see [Indexes](https://docs.convex.dev/text-search).\n * @public\n */\nexport interface SearchFilterBuilder<\n  Document extends GenericDocument,\n  SearchIndexConfig extends GenericSearchIndexConfig,\n> {\n  /**\n   * Search for the terms in `query` within `doc[fieldName]`.\n   *\n   * This will do a full text search that returns results where any word of of\n   * `query` appears in the field.\n   *\n   * Documents will be returned based on their relevance to the query. This\n   * takes into account:\n   * - How many words in the query appear in the text?\n   * - How many times do they appear?\n   * - How long is the text field?\n   *\n   * @param fieldName - The name of the field to search in. This must be listed\n   * as the index's `searchField`.\n   * @param query - The query text to search for.\n   */\n  search(\n    fieldName: SearchIndexConfig[\"searchField\"],\n    query: string,\n  ): SearchFilterFinalizer<Document, SearchIndexConfig>;\n}\n\n/**\n * Builder to define equality expressions as part of a search filter.\n *\n * See {@link SearchFilterBuilder}.\n *\n * @public\n */\nexport interface SearchFilterFinalizer<\n  Document extends GenericDocument,\n  SearchIndexConfig extends GenericSearchIndexConfig,\n> extends SearchFilter {\n  /**\n   * Restrict this query to documents where `doc[fieldName] === value`.\n   *\n   * @param fieldName - The name of the field to compare. This must be listed in\n   * the search index's `filterFields`.\n   * @param value - The value to compare against.\n   */\n  eq<FieldName extends SearchIndexConfig[\"filterFields\"]>(\n    fieldName: FieldName,\n    value: FieldTypeFromFieldPath<Document, FieldName>,\n  ): SearchFilterFinalizer<Document, SearchIndexConfig>;\n}\n\n/**\n * An expression representing a search filter created by\n * {@link SearchFilterBuilder}.\n *\n * @public\n */\nexport abstract class SearchFilter {\n  // Property for nominal type support.\n  private _isSearchFilter: undefined;\n\n  /**\n   * @internal\n   */\n  constructor() {\n    // only defining the constructor so we can mark it as internal and keep\n    // it out of the docs.\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AA6EO,MAAe,aAAa;IAAA;;GAAA,GAOjC,aAAc;QALd,qCAAA;QAAA,cAAA,IAAA,EAAQ;IAQR;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 627, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/search_filter_builder_impl.ts"], "sourcesContent": ["import { JSONValue, convexOrUndefinedToJson } from \"../../values/value.js\";\nimport {\n  FieldTypeFromFieldPath,\n  GenericDocument,\n  GenericSearchIndexConfig,\n} from \"../data_model.js\";\nimport {\n  SearchFilter,\n  SearchFilterBuilder,\n  SearchFilterFinalizer,\n} from \"../search_filter_builder.js\";\nimport { validateArg } from \"./validate.js\";\n\nexport type SerializedSearchFilter =\n  | {\n      type: \"Search\";\n      fieldPath: string;\n      value: string;\n    }\n  | {\n      type: \"Eq\";\n      fieldPath: string;\n      value: JSONValue;\n    };\n\nexport class SearchFilterBuilderImpl\n  extends SearchFilter\n  implements\n    SearchFilterBuilder<GenericDocument, GenericSearchIndexConfig>,\n    SearchFilterFinalizer<GenericDocument, GenericSearchIndexConfig>\n{\n  private filters: ReadonlyArray<SerializedSearchFilter>;\n  private isConsumed: boolean;\n  private constructor(filters: ReadonlyArray<SerializedSearchFilter>) {\n    super();\n    this.filters = filters;\n    this.isConsumed = false;\n  }\n\n  static new(): SearchFilterBuilderImpl {\n    return new SearchFilterBuilderImpl([]);\n  }\n\n  private consume() {\n    if (this.isConsumed) {\n      throw new Error(\n        \"SearchFilterBuilder has already been used! Chain your method calls like `q => q.search(...).eq(...)`.\",\n      );\n    }\n    this.isConsumed = true;\n  }\n\n  search(\n    fieldName: string,\n    query: string,\n  ): SearchFilterFinalizer<GenericDocument, GenericSearchIndexConfig> {\n    validateArg(fieldName, 1, \"search\", \"fieldName\");\n    validateArg(query, 2, \"search\", \"query\");\n    this.consume();\n    return new SearchFilterBuilderImpl(\n      this.filters.concat({\n        type: \"Search\",\n        fieldPath: fieldName,\n        value: query,\n      }),\n    );\n  }\n  eq<FieldName extends string>(\n    fieldName: FieldName,\n    value: FieldTypeFromFieldPath<GenericDocument, FieldName>,\n  ): SearchFilterFinalizer<GenericDocument, GenericSearchIndexConfig> {\n    validateArg(fieldName, 1, \"eq\", \"fieldName\");\n    // when `undefined` is passed explicitly, it is allowed.\n    if (arguments.length !== 2) {\n      validateArg(value, 2, \"search\", \"value\");\n    }\n    this.consume();\n    return new SearchFilterBuilderImpl(\n      this.filters.concat({\n        type: \"Eq\",\n        fieldPath: fieldName,\n        value: convexOrUndefinedToJson(value),\n      }),\n    );\n  }\n\n  export() {\n    this.consume();\n    return this.filters;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,SAAoB,+BAA+B;AAMnD;AAKA,SAAS,mBAAmB;;;;;;;;;;;;;AAcrB,MAAM,4MACH,eAAA,CAIV;IAGU,YAAY,OAAA,CAAgD;QAClE,KAAA,CAAM;QAHR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QAGN,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,UAAA,GAAa;IACpB;IAEA,OAAO,MAA+B;QACpC,OAAO,IAAI,wBAAwB,CAAC,CAAC;IACvC;IAEQ,UAAU;QAChB,IAAI,IAAA,CAAK,UAAA,EAAY;YACnB,MAAM,IAAI,MACR;QAEJ;QACA,IAAA,CAAK,UAAA,GAAa;IACpB;IAEA,OACE,SAAA,EACA,KAAA,EACkE;QAClE,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,WAAW,GAAG,UAAU,WAAW;QAC/C,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,OAAO,GAAG,UAAU,OAAO;QACvC,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,wBACT,IAAA,CAAK,OAAA,CAAQ,MAAA,CAAO;YAClB,MAAM;YACN,WAAW;YACX,OAAO;QACT,CAAC;IAEL;IACA,GACE,SAAA,EACA,KAAA,EACkE;QAClE,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,WAAW,GAAG,MAAM,WAAW;QAE3C,IAAI,UAAU,MAAA,KAAW,GAAG;YAC1B,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,OAAO,GAAG,UAAU,OAAO;QACzC;QACA,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAI,wBACT,IAAA,CAAK,OAAA,CAAQ,MAAA,CAAO;YAClB,MAAM;YACN,WAAW;YACX,uKAAO,0BAAA,EAAwB,KAAK;QACtC,CAAC;IAEL;IAEA,SAAS;QACP,IAAA,CAAK,OAAA,CAAQ;QACb,OAAO,IAAA,CAAK,OAAA;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 695, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/query_impl.ts"], "sourcesContent": ["import { Value, JSONValue, jsonToConvex } from \"../../values/index.js\";\nimport { PaginationResult, PaginationOptions } from \"../pagination.js\";\nimport { performAsyncSyscall, performSyscall } from \"./syscall.js\";\nimport {\n  filterBuilderImpl,\n  serializeExpression,\n} from \"./filter_builder_impl.js\";\nimport { Query, QueryInitializer } from \"../query.js\";\nimport { ExpressionOrValue, FilterBuilder } from \"../filter_builder.js\";\nimport { GenericTableInfo } from \"../data_model.js\";\nimport {\n  IndexRangeBuilderImpl,\n  SerializedRangeExpression,\n} from \"./index_range_builder_impl.js\";\nimport {\n  SearchFilterBuilderImpl,\n  SerializedSearchFilter,\n} from \"./search_filter_builder_impl.js\";\nimport { validateArg, validateArgIsNonNegativeInteger } from \"./validate.js\";\nimport { version } from \"../../index.js\";\n\nconst MAX_QUERY_OPERATORS = 256;\n\ntype QueryOperator = { filter: JSONValue } | { limit: number };\ntype Source =\n  | { type: \"FullTableScan\"; tableName: string; order: \"asc\" | \"desc\" | null }\n  | {\n      type: \"IndexRange\";\n      indexName: string;\n      range: ReadonlyArray<SerializedRangeExpression>;\n      order: \"asc\" | \"desc\" | null;\n    }\n  | {\n      type: \"Search\";\n      indexName: string;\n      filters: ReadonlyArray<SerializedSearchFilter>;\n    };\n\ntype SerializedQuery = {\n  source: Source;\n  operators: Array<QueryOperator>;\n};\n\nexport class QueryInitializerImpl\n  implements QueryInitializer<GenericTableInfo>\n{\n  private tableName: string;\n\n  constructor(tableName: string) {\n    this.tableName = tableName;\n  }\n\n  withIndex(\n    indexName: string,\n    indexRange?: (q: IndexRangeBuilderImpl) => IndexRangeBuilderImpl,\n  ): QueryImpl {\n    validateArg(indexName, 1, \"withIndex\", \"indexName\");\n    let rangeBuilder = IndexRangeBuilderImpl.new();\n    if (indexRange !== undefined) {\n      rangeBuilder = indexRange(rangeBuilder);\n    }\n    return new QueryImpl({\n      source: {\n        type: \"IndexRange\",\n        indexName: this.tableName + \".\" + indexName,\n        range: rangeBuilder.export(),\n        order: null,\n      },\n      operators: [],\n    });\n  }\n\n  withSearchIndex(\n    indexName: string,\n    searchFilter: (q: SearchFilterBuilderImpl) => SearchFilterBuilderImpl,\n  ): QueryImpl {\n    validateArg(indexName, 1, \"withSearchIndex\", \"indexName\");\n    validateArg(searchFilter, 2, \"withSearchIndex\", \"searchFilter\");\n    const searchFilterBuilder = SearchFilterBuilderImpl.new();\n    return new QueryImpl({\n      source: {\n        type: \"Search\",\n        indexName: this.tableName + \".\" + indexName,\n        filters: searchFilter(searchFilterBuilder).export(),\n      },\n      operators: [],\n    });\n  }\n\n  fullTableScan(): QueryImpl {\n    return new QueryImpl({\n      source: {\n        type: \"FullTableScan\",\n        tableName: this.tableName,\n        order: null,\n      },\n      operators: [],\n    });\n  }\n\n  order(order: \"asc\" | \"desc\"): QueryImpl {\n    return this.fullTableScan().order(order);\n  }\n\n  // This is internal API and should not be exposed to developers yet.\n  async count(): Promise<number> {\n    const syscallJSON = await performAsyncSyscall(\"1.0/count\", {\n      table: this.tableName,\n    });\n    const syscallResult = jsonToConvex(syscallJSON) as number;\n    return syscallResult;\n  }\n\n  filter(\n    predicate: (\n      q: FilterBuilder<GenericTableInfo>,\n    ) => ExpressionOrValue<boolean>,\n  ) {\n    return this.fullTableScan().filter(predicate);\n  }\n\n  limit(n: number) {\n    return this.fullTableScan().limit(n);\n  }\n\n  collect(): Promise<any[]> {\n    return this.fullTableScan().collect();\n  }\n\n  take(n: number): Promise<Array<any>> {\n    return this.fullTableScan().take(n);\n  }\n\n  paginate(paginationOpts: PaginationOptions): Promise<PaginationResult<any>> {\n    return this.fullTableScan().paginate(paginationOpts);\n  }\n\n  first(): Promise<any> {\n    return this.fullTableScan().first();\n  }\n\n  unique(): Promise<any> {\n    return this.fullTableScan().unique();\n  }\n\n  [Symbol.asyncIterator](): AsyncIterableIterator<any> {\n    return this.fullTableScan()[Symbol.asyncIterator]();\n  }\n}\n\n/**\n * @param type Whether the query was consumed or closed.\n * @throws An error indicating the query has been closed.\n */\nfunction throwClosedError(type: \"closed\" | \"consumed\"): never {\n  throw new Error(\n    type === \"consumed\"\n      ? \"This query is closed and can't emit any more values.\"\n      : \"This query has been chained with another operator and can't be reused.\",\n  );\n}\n\nexport class QueryImpl implements Query<GenericTableInfo> {\n  private state:\n    | { type: \"preparing\"; query: SerializedQuery }\n    | { type: \"executing\"; queryId: number }\n    | { type: \"closed\" }\n    | { type: \"consumed\" };\n\n  constructor(query: SerializedQuery) {\n    this.state = { type: \"preparing\", query };\n  }\n\n  private takeQuery(): SerializedQuery {\n    if (this.state.type !== \"preparing\") {\n      throw new Error(\n        \"A query can only be chained once and can't be chained after iteration begins.\",\n      );\n    }\n    const query = this.state.query;\n    this.state = { type: \"closed\" };\n    return query;\n  }\n\n  private startQuery(): number {\n    if (this.state.type === \"executing\") {\n      throw new Error(\"Iteration can only begin on a query once.\");\n    }\n    if (this.state.type === \"closed\" || this.state.type === \"consumed\") {\n      throwClosedError(this.state.type);\n    }\n    const query = this.state.query;\n    const { queryId } = performSyscall(\"1.0/queryStream\", { query, version });\n    this.state = { type: \"executing\", queryId };\n    return queryId;\n  }\n\n  private closeQuery() {\n    if (this.state.type === \"executing\") {\n      const queryId = this.state.queryId;\n      performSyscall(\"1.0/queryCleanup\", { queryId });\n    }\n    this.state = { type: \"consumed\" };\n  }\n\n  order(order: \"asc\" | \"desc\"): QueryImpl {\n    validateArg(order, 1, \"order\", \"order\");\n    const query = this.takeQuery();\n    if (query.source.type === \"Search\") {\n      throw new Error(\n        \"Search queries must always be in relevance order. Can not set order manually.\",\n      );\n    }\n    if (query.source.order !== null) {\n      throw new Error(\"Queries may only specify order at most once\");\n    }\n    query.source.order = order;\n    return new QueryImpl(query);\n  }\n\n  filter(\n    predicate: (\n      q: FilterBuilder<GenericTableInfo>,\n    ) => ExpressionOrValue<boolean>,\n  ): any {\n    validateArg(predicate, 1, \"filter\", \"predicate\");\n    const query = this.takeQuery();\n    if (query.operators.length >= MAX_QUERY_OPERATORS) {\n      throw new Error(\n        `Can't construct query with more than ${MAX_QUERY_OPERATORS} operators`,\n      );\n    }\n    query.operators.push({\n      filter: serializeExpression(predicate(filterBuilderImpl)),\n    });\n    return new QueryImpl(query);\n  }\n\n  limit(n: number): any {\n    validateArg(n, 1, \"limit\", \"n\");\n    const query = this.takeQuery();\n    query.operators.push({ limit: n });\n    return new QueryImpl(query);\n  }\n\n  [Symbol.asyncIterator](): AsyncIterableIterator<any> {\n    this.startQuery();\n    return this;\n  }\n\n  async next(): Promise<IteratorResult<any>> {\n    if (this.state.type === \"closed\" || this.state.type === \"consumed\") {\n      throwClosedError(this.state.type);\n    }\n    // Allow calling `.next()` when the query is in \"preparing\" state to implicitly start the\n    // query. This allows the developer to call `.next()` on the query without having to use\n    // a `for await` statement.\n    const queryId =\n      this.state.type === \"preparing\" ? this.startQuery() : this.state.queryId;\n    const { value, done } = await performAsyncSyscall(\"1.0/queryStreamNext\", {\n      queryId,\n    });\n    if (done) {\n      this.closeQuery();\n    }\n    const convexValue = jsonToConvex(value);\n    return { value: convexValue, done };\n  }\n\n  return() {\n    this.closeQuery();\n    return Promise.resolve({ done: true, value: undefined });\n  }\n\n  async paginate(\n    paginationOpts: PaginationOptions,\n  ): Promise<PaginationResult<any>> {\n    validateArg(paginationOpts, 1, \"paginate\", \"options\");\n    if (\n      typeof paginationOpts?.numItems !== \"number\" ||\n      paginationOpts.numItems < 0\n    ) {\n      throw new Error(\n        `\\`options.numItems\\` must be a positive number. Received \\`${paginationOpts?.numItems}\\`.`,\n      );\n    }\n    const query = this.takeQuery();\n    const pageSize = paginationOpts.numItems;\n    const cursor = paginationOpts.cursor;\n    const endCursor = paginationOpts?.endCursor ?? null;\n    const maximumRowsRead = paginationOpts.maximumRowsRead ?? null;\n    const { page, isDone, continueCursor, splitCursor, pageStatus } =\n      await performAsyncSyscall(\"1.0/queryPage\", {\n        query,\n        cursor,\n        endCursor,\n        pageSize,\n        maximumRowsRead,\n        maximumBytesRead: paginationOpts.maximumBytesRead,\n        version,\n      });\n    return {\n      page: page.map((json: string) => jsonToConvex(json)),\n      isDone,\n      continueCursor,\n      splitCursor,\n      pageStatus,\n    };\n  }\n\n  async collect(): Promise<Array<any>> {\n    const out: Value[] = [];\n    for await (const item of this) {\n      out.push(item);\n    }\n    return out;\n  }\n\n  async take(n: number): Promise<Array<any>> {\n    validateArg(n, 1, \"take\", \"n\");\n    validateArgIsNonNegativeInteger(n, 1, \"take\", \"n\");\n    return this.limit(n).collect();\n  }\n\n  async first(): Promise<any | null> {\n    const first_array = await this.take(1);\n    return first_array.length === 0 ? null : first_array[0];\n  }\n\n  async unique(): Promise<any | null> {\n    const first_two_array = await this.take(2);\n    if (first_two_array.length === 0) {\n      return null;\n    }\n    if (first_two_array.length === 2) {\n      throw new Error(`unique() query returned more than one result: \n [${first_two_array[0]._id}, ${first_two_array[1]._id}, ...]`);\n    }\n    return first_two_array[0];\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA,SAA2B,oBAAoB;AAE/C,SAAS,qBAAqB,sBAAsB;AACpD;AAOA;AAIA;AAIA,SAAS,aAAa,uCAAuC;AAC7D,SAAS,eAAe;;;;;;;;;;;;;;;;;AAExB,MAAM,sBAAsB;AAsBrB,MAAM,qBAEb;IAGE,YAAY,SAAA,CAAmB;QAF/B,cAAA,IAAA,EAAQ;QAGN,IAAA,CAAK,SAAA,GAAY;IACnB;IAEA,UACE,SAAA,EACA,UAAA,EACW;QACX,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,WAAW,GAAG,aAAa,WAAW;QAClD,IAAI,qMAAe,yBAAA,CAAsB,GAAA,CAAI;QAC7C,IAAI,eAAe,KAAA,GAAW;YAC5B,eAAe,WAAW,YAAY;QACxC;QACA,OAAO,IAAI,UAAU;YACnB,QAAQ;gBACN,MAAM;gBACN,WAAW,IAAA,CAAK,SAAA,GAAY,MAAM;gBAClC,OAAO,aAAa,MAAA,CAAO;gBAC3B,OAAO;YACT;YACA,WAAW,CAAC,CAAA;QACd,CAAC;IACH;IAEA,gBACE,SAAA,EACA,YAAA,EACW;QACX,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,WAAW,GAAG,mBAAmB,WAAW;QACxD,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,cAAc,GAAG,mBAAmB,cAAc;QAC9D,MAAM,+MAAsB,0BAAA,CAAwB,GAAA,CAAI;QACxD,OAAO,IAAI,UAAU;YACnB,QAAQ;gBACN,MAAM;gBACN,WAAW,IAAA,CAAK,SAAA,GAAY,MAAM;gBAClC,SAAS,aAAa,mBAAmB,EAAE,MAAA,CAAO;YACpD;YACA,WAAW,CAAC,CAAA;QACd,CAAC;IACH;IAEA,gBAA2B;QACzB,OAAO,IAAI,UAAU;YACnB,QAAQ;gBACN,MAAM;gBACN,WAAW,IAAA,CAAK,SAAA;gBAChB,OAAO;YACT;YACA,WAAW,CAAC,CAAA;QACd,CAAC;IACH;IAEA,MAAM,KAAA,EAAkC;QACtC,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,KAAA,CAAM,KAAK;IACzC;IAAA,oEAAA;IAGA,MAAM,QAAyB;QAC7B,MAAM,cAAc,+KAAM,uBAAA,EAAoB,aAAa;YACzD,OAAO,IAAA,CAAK,SAAA;QACd,CAAC;QACD,MAAM,gLAAgB,eAAA,EAAa,WAAW;QAC9C,OAAO;IACT;IAEA,OACE,SAAA,EAGA;QACA,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,MAAA,CAAO,SAAS;IAC9C;IAEA,MAAM,CAAA,EAAW;QACf,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,KAAA,CAAM,CAAC;IACrC;IAEA,UAA0B;QACxB,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,OAAA,CAAQ;IACtC;IAEA,KAAK,CAAA,EAAgC;QACnC,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,IAAA,CAAK,CAAC;IACpC;IAEA,SAAS,cAAA,EAAmE;QAC1E,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,QAAA,CAAS,cAAc;IACrD;IAEA,QAAsB;QACpB,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,KAAA,CAAM;IACpC;IAEA,SAAuB;QACrB,OAAO,IAAA,CAAK,aAAA,CAAc,EAAE,MAAA,CAAO;IACrC;IAEA,CAAC,OAAO,aAAa,CAAA,GAAgC;QACnD,OAAO,IAAA,CAAK,aAAA,CAAc,CAAA,CAAE,OAAO,aAAa,CAAA,CAAE;IACpD;AACF;AAMA,SAAS,iBAAiB,IAAA,EAAoC;IAC5D,MAAM,IAAI,MACR,SAAS,aACL,yDACA;AAER;AAEO,MAAM,UAA6C;IAOxD,YAAY,KAAA,CAAwB;QANpC,cAAA,IAAA,EAAQ;QAON,IAAA,CAAK,KAAA,GAAQ;YAAE,MAAM;YAAa;QAAM;IAC1C;IAEQ,YAA6B;QACnC,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,aAAa;YACnC,MAAM,IAAI,MACR;QAEJ;QACA,MAAM,QAAQ,IAAA,CAAK,KAAA,CAAM,KAAA;QACzB,IAAA,CAAK,KAAA,GAAQ;YAAE,MAAM;QAAS;QAC9B,OAAO;IACT;IAEQ,aAAqB;QAC3B,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,aAAa;YACnC,MAAM,IAAI,MAAM,2CAA2C;QAC7D;QACA,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,YAAY,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,YAAY;YAClE,iBAAiB,IAAA,CAAK,KAAA,CAAM,IAAI;QAClC;QACA,MAAM,QAAQ,IAAA,CAAK,KAAA,CAAM,KAAA;QACzB,MAAM,EAAE,OAAA,CAAQ,CAAA,6KAAI,iBAAA,EAAe,mBAAmB;YAAE;uKAAO,UAAA;QAAQ,CAAC;QACxE,IAAA,CAAK,KAAA,GAAQ;YAAE,MAAM;YAAa;QAAQ;QAC1C,OAAO;IACT;IAEQ,aAAa;QACnB,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,aAAa;YACnC,MAAM,UAAU,IAAA,CAAK,KAAA,CAAM,OAAA;YAC3B,CAAA,GAAA,qKAAA,CAAA,iBAAA,EAAe,oBAAoB;gBAAE;YAAQ,CAAC;QAChD;QACA,IAAA,CAAK,KAAA,GAAQ;YAAE,MAAM;QAAW;IAClC;IAEA,MAAM,KAAA,EAAkC;QACtC,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,OAAO,GAAG,SAAS,OAAO;QACtC,MAAM,QAAQ,IAAA,CAAK,SAAA,CAAU;QAC7B,IAAI,MAAM,MAAA,CAAO,IAAA,KAAS,UAAU;YAClC,MAAM,IAAI,MACR;QAEJ;QACA,IAAI,MAAM,MAAA,CAAO,KAAA,KAAU,MAAM;YAC/B,MAAM,IAAI,MAAM,6CAA6C;QAC/D;QACA,MAAM,MAAA,CAAO,KAAA,GAAQ;QACrB,OAAO,IAAI,UAAU,KAAK;IAC5B;IAEA,OACE,SAAA,EAGK;QACL,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,WAAW,GAAG,UAAU,WAAW;QAC/C,MAAM,QAAQ,IAAA,CAAK,SAAA,CAAU;QAC7B,IAAI,MAAM,SAAA,CAAU,MAAA,IAAU,qBAAqB;YACjD,MAAM,IAAI,MACR,CAAA,qCAAA,EAAwC,mBAAmB,CAAA,UAAA,CAAA;QAE/D;QACA,MAAM,SAAA,CAAU,IAAA,CAAK;YACnB,8LAAQ,sBAAA,EAAoB,UAAU,sMAAiB,CAAC;QAC1D,CAAC;QACD,OAAO,IAAI,UAAU,KAAK;IAC5B;IAEA,MAAM,CAAA,EAAgB;QACpB,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,GAAG,GAAG,SAAS,GAAG;QAC9B,MAAM,QAAQ,IAAA,CAAK,SAAA,CAAU;QAC7B,MAAM,SAAA,CAAU,IAAA,CAAK;YAAE,OAAO;QAAE,CAAC;QACjC,OAAO,IAAI,UAAU,KAAK;IAC5B;IAEA,CAAC,OAAO,aAAa,CAAA,GAAgC;QACnD,IAAA,CAAK,UAAA,CAAW;QAChB,OAAO,IAAA;IACT;IAEA,MAAM,OAAqC;QACzC,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,YAAY,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,YAAY;YAClE,iBAAiB,IAAA,CAAK,KAAA,CAAM,IAAI;QAClC;QAIA,MAAM,UACJ,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,cAAc,IAAA,CAAK,UAAA,CAAW,IAAI,IAAA,CAAK,KAAA,CAAM,OAAA;QACnE,MAAM,EAAE,KAAA,EAAO,IAAA,CAAK,CAAA,GAAI,gLAAM,sBAAA,EAAoB,uBAAuB;YACvE;QACF,CAAC;QACD,IAAI,MAAM;YACR,IAAA,CAAK,UAAA,CAAW;QAClB;QACA,MAAM,kBAAc,2KAAA,EAAa,KAAK;QACtC,OAAO;YAAE,OAAO;YAAa;QAAK;IACpC;IAEA,SAAS;QACP,IAAA,CAAK,UAAA,CAAW;QAChB,OAAO,QAAQ,OAAA,CAAQ;YAAE,MAAM;YAAM,OAAO,KAAA;QAAU,CAAC;IACzD;IAEA,MAAM,SACJ,cAAA,EACgC;QAChC,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,gBAAgB,GAAG,YAAY,SAAS;QACpD,IACE,OAAO,gBAAgB,aAAa,YACpC,eAAe,QAAA,GAAW,GAC1B;YACA,MAAM,IAAI,MACR,CAAA,2DAAA,EAA8D,gBAAgB,QAAQ,CAAA,GAAA,CAAA;QAE1F;QACA,MAAM,QAAQ,IAAA,CAAK,SAAA,CAAU;QAC7B,MAAM,WAAW,eAAe,QAAA;QAChC,MAAM,SAAS,eAAe,MAAA;QAC9B,MAAM,YAAY,gBAAgB,aAAa;QAC/C,MAAM,kBAAkB,eAAe,eAAA,IAAmB;QAC1D,MAAM,EAAE,IAAA,EAAM,MAAA,EAAQ,cAAA,EAAgB,WAAA,EAAa,UAAA,CAAW,CAAA,GAC5D,UAAM,4LAAA,EAAoB,iBAAiB;YACzC;YACA;YACA;YACA;YACA;YACA,kBAAkB,eAAe,gBAAA;uKACjC,UAAA;QACF,CAAC;QACH,OAAO;YACL,MAAM,KAAK,GAAA,CAAI,CAAC,QAAiB,8KAAA,EAAa,IAAI,CAAC;YACnD;YACA;YACA;YACA;QACF;IACF;IAEA,MAAM,UAA+B;QACnC,MAAM,MAAe,CAAC,CAAA;QACtB,WAAA,MAAiB,QAAQ,IAAA,CAAM;YAC7B,IAAI,IAAA,CAAK,IAAI;QACf;QACA,OAAO;IACT;IAEA,MAAM,KAAK,CAAA,EAAgC;QACzC,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,GAAG,GAAG,QAAQ,GAAG;QAC7B,CAAA,GAAA,sKAAA,CAAA,kCAAA,EAAgC,GAAG,GAAG,QAAQ,GAAG;QACjD,OAAO,IAAA,CAAK,KAAA,CAAM,CAAC,EAAE,OAAA,CAAQ;IAC/B;IAEA,MAAM,QAA6B;QACjC,MAAM,cAAc,MAAM,IAAA,CAAK,IAAA,CAAK,CAAC;QACrC,OAAO,YAAY,MAAA,KAAW,IAAI,OAAO,WAAA,CAAY,CAAC,CAAA;IACxD;IAEA,MAAM,SAA8B;QAClC,MAAM,kBAAkB,MAAM,IAAA,CAAK,IAAA,CAAK,CAAC;QACzC,IAAI,gBAAgB,MAAA,KAAW,GAAG;YAChC,OAAO;QACT;QACA,IAAI,gBAAgB,MAAA,KAAW,GAAG;YAChC,MAAM,IAAI,MAAM,CAAA;EAAA,EAClB,eAAA,CAAgB,CAAC,CAAA,CAAE,GAAG,CAAA,EAAA,EAAK,eAAA,CAAgB,CAAC,CAAA,CAAE,GAAG,CAAA,MAAA,CAAQ;QACzD;QACA,OAAO,eAAA,CAAgB,CAAC,CAAA;IAC1B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 974, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/database_impl.ts"], "sourcesContent": ["import {\n  convexTo<PERSON>son,\n  GenericId,\n  jsonToConvex,\n  Value,\n} from \"../../values/index.js\";\nimport { performAsyncSyscall, performSyscall } from \"./syscall.js\";\nimport {\n  GenericDatabaseReader,\n  GenericDatabaseReaderWithTable,\n  GenericDatabaseWriter,\n  GenericDatabaseWriterWithTable,\n} from \"../database.js\";\nimport { QueryInitializerImpl } from \"./query_impl.js\";\nimport { GenericDataModel, GenericDocument } from \"../data_model.js\";\nimport { validateArg } from \"./validate.js\";\nimport { version } from \"../../index.js\";\nimport { patchValueToJson } from \"../../values/value.js\";\n\nasync function get(id: GenericId<string>, isSystem: boolean) {\n  validateArg(id, 1, \"get\", \"id\");\n  if (typeof id !== \"string\") {\n    throw new Error(\n      `Invalid argument \\`id\\` for \\`db.get\\`, expected string but got '${typeof id}': ${\n        id as any\n      }`,\n    );\n  }\n  const args = {\n    id: convexToJson(id),\n    isSystem,\n    version,\n  };\n  const syscallJSON = await performAsyncSyscall(\"1.0/get\", args);\n\n  return jsonToConvex(syscallJSON) as GenericDocument;\n}\n\nexport function setupReader(): GenericDatabaseReader<GenericDataModel> {\n  const reader = (\n    isSystem = false,\n  ): GenericDatabaseReader<GenericDataModel> &\n    GenericDatabaseReaderWithTable<GenericDataModel> => {\n    return {\n      get: async (id: GenericId<string>) => {\n        return await get(id, isSystem);\n      },\n      query: (tableName: string) => {\n        return new TableReader(tableName, isSystem).query();\n      },\n      normalizeId: <TableName extends string>(\n        tableName: TableName,\n        id: string,\n      ): GenericId<TableName> | null => {\n        validateArg(tableName, 1, \"normalizeId\", \"tableName\");\n        validateArg(id, 2, \"normalizeId\", \"id\");\n        const accessingSystemTable = tableName.startsWith(\"_\");\n        if (accessingSystemTable !== isSystem) {\n          throw new Error(\n            `${\n              accessingSystemTable ? \"System\" : \"User\"\n            } tables can only be accessed from db.${\n              isSystem ? \"\" : \"system.\"\n            }normalizeId().`,\n          );\n        }\n        const syscallJSON = performSyscall(\"1.0/db/normalizeId\", {\n          table: tableName,\n          idString: id,\n        });\n        const syscallResult = jsonToConvex(syscallJSON) as any;\n        return syscallResult.id;\n      },\n      // We set the system reader on the next line\n      system: null as any,\n      table: (tableName) => {\n        return new TableReader(tableName, isSystem);\n      },\n    };\n  };\n  const { system: _, ...rest } = reader(true);\n  const r = reader();\n  r.system = rest as any;\n  return r;\n}\n\nasync function insert(tableName: string, value: any) {\n  if (tableName.startsWith(\"_\")) {\n    throw new Error(\"System tables (prefixed with `_`) are read-only.\");\n  }\n  validateArg(tableName, 1, \"insert\", \"table\");\n  validateArg(value, 2, \"insert\", \"value\");\n  const syscallJSON = await performAsyncSyscall(\"1.0/insert\", {\n    table: tableName,\n    value: convexToJson(value),\n  });\n  const syscallResult = jsonToConvex(syscallJSON) as any;\n  return syscallResult._id;\n}\n\nasync function patch(id: any, value: any) {\n  validateArg(id, 1, \"patch\", \"id\");\n  validateArg(value, 2, \"patch\", \"value\");\n  await performAsyncSyscall(\"1.0/shallowMerge\", {\n    id: convexToJson(id),\n    value: patchValueToJson(value as Value),\n  });\n}\n\nasync function replace(id: any, value: any) {\n  validateArg(id, 1, \"replace\", \"id\");\n  validateArg(value, 2, \"replace\", \"value\");\n  await performAsyncSyscall(\"1.0/replace\", {\n    id: convexToJson(id),\n    value: convexToJson(value),\n  });\n}\n\nasync function delete_(id: any) {\n  validateArg(id, 1, \"delete\", \"id\");\n  await performAsyncSyscall(\"1.0/remove\", { id: convexToJson(id) });\n}\n\nexport function setupWriter(): GenericDatabaseWriter<GenericDataModel> &\n  GenericDatabaseWriterWithTable<GenericDataModel> {\n  const reader = setupReader();\n  return {\n    get: reader.get,\n    query: reader.query,\n    normalizeId: reader.normalizeId,\n    system: reader.system as any,\n    insert: async (table, value) => {\n      return await insert(table, value);\n    },\n    patch: async (id, value) => {\n      return await patch(id, value);\n    },\n    replace: async (id, value) => {\n      return await replace(id, value);\n    },\n    delete: async (id) => {\n      return await delete_(id);\n    },\n    table: (tableName) => {\n      return new TableWriter(tableName, false);\n    },\n  };\n}\n\nclass TableReader {\n  constructor(\n    protected readonly tableName: string,\n    protected readonly isSystem: boolean,\n  ) {}\n\n  async get(id: GenericId<string>) {\n    return get(id, this.isSystem);\n  }\n\n  query() {\n    const accessingSystemTable = this.tableName.startsWith(\"_\");\n    if (accessingSystemTable !== this.isSystem) {\n      throw new Error(\n        `${\n          accessingSystemTable ? \"System\" : \"User\"\n        } tables can only be accessed from db.${\n          this.isSystem ? \"\" : \"system.\"\n        }query().`,\n      );\n    }\n    return new QueryInitializerImpl(this.tableName);\n  }\n}\n\nclass TableWriter extends TableReader {\n  async insert(value: any) {\n    return insert(this.tableName, value);\n  }\n  async patch(id: any, value: any) {\n    return patch(id, value);\n  }\n  async replace(id: any, value: any) {\n    return replace(id, value);\n  }\n  async delete(id: any) {\n    return delete_(id);\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAMA,SAAS,qBAAqB,sBAAsB;AAOpD,SAAS,4BAA4B;AAErC,SAAS,mBAAmB;AAC5B,SAAS,eAAe;;;;;;;;AAGxB,eAAe,IAAI,EAAA,EAAuB,QAAA,EAAmB;IAC3D,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,IAAI,GAAG,OAAO,IAAI;IAC9B,IAAI,OAAO,OAAO,UAAU;QAC1B,MAAM,IAAI,MACR,CAAA,iEAAA,EAAoE,OAAO,EAAE,CAAA,GAAA,EAC3E,EACF,EAAA;IAEJ;IACA,MAAM,OAAO;QACX,oKAAI,eAAA,EAAa,EAAE;QACnB;mKACA,UAAA;IACF;IACA,MAAM,cAAc,UAAM,4LAAA,EAAoB,WAAW,IAAI;IAE7D,uKAAO,eAAA,EAAa,WAAW;AACjC;AAEO,SAAS,cAAuD;IACrE,MAAM,SAAS,CACb,WAAW,KAAA,KAEyC;QACpD,OAAO;YACL,KAAK,OAAO,OAA0B;gBACpC,OAAO,MAAM,IAAI,IAAI,QAAQ;YAC/B;YACA,OAAO,CAAC,cAAsB;gBAC5B,OAAO,IAAI,YAAY,WAAW,QAAQ,EAAE,KAAA,CAAM;YACpD;YACA,aAAa,CACX,WACA,OACgC;gBAChC,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,WAAW,GAAG,eAAe,WAAW;gBACpD,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,IAAI,GAAG,eAAe,IAAI;gBACtC,MAAM,uBAAuB,UAAU,UAAA,CAAW,GAAG;gBACrD,IAAI,yBAAyB,UAAU;oBACrC,MAAM,IAAI,MACR,GACE,uBAAuB,WAAW,MACpC,CAAA,qCAAA,EACE,WAAW,KAAK,SAClB,CAAA,cAAA,CAAA;gBAEJ;gBACA,MAAM,cAAc,2LAAA,EAAe,sBAAsB;oBACvD,OAAO;oBACP,UAAU;gBACZ,CAAC;gBACD,MAAM,oBAAgB,2KAAA,EAAa,WAAW;gBAC9C,OAAO,cAAc,EAAA;YACvB;YAAA,4CAAA;YAEA,QAAQ;YACR,OAAO,CAAC,cAAc;gBACpB,OAAO,IAAI,YAAY,WAAW,QAAQ;YAC5C;QACF;IACF;IACA,MAAM,EAAE,QAAQ,CAAA,EAAG,GAAG,KAAK,CAAA,GAAI,OAAO,IAAI;IAC1C,MAAM,IAAI,OAAO;IACjB,EAAE,MAAA,GAAS;IACX,OAAO;AACT;AAEA,eAAe,OAAO,SAAA,EAAmB,KAAA,EAAY;IACnD,IAAI,UAAU,UAAA,CAAW,GAAG,GAAG;QAC7B,MAAM,IAAI,MAAM,kDAAkD;IACpE;IACA,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,WAAW,GAAG,UAAU,OAAO;IAC3C,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,OAAO,GAAG,UAAU,OAAO;IACvC,MAAM,cAAc,MAAM,gMAAA,EAAoB,cAAc;QAC1D,OAAO;QACP,uKAAO,eAAA,EAAa,KAAK;IAC3B,CAAC;IACD,MAAM,gBAAgB,+KAAA,EAAa,WAAW;IAC9C,OAAO,cAAc,GAAA;AACvB;AAEA,eAAe,MAAM,EAAA,EAAS,KAAA,EAAY;IACxC,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,IAAI,GAAG,SAAS,IAAI;IAChC,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,OAAO,GAAG,SAAS,OAAO;IACtC,MAAM,gMAAA,EAAoB,oBAAoB;QAC5C,oKAAI,eAAA,EAAa,EAAE;QACnB,uKAAO,mBAAA,EAAiB,KAAc;IACxC,CAAC;AACH;AAEA,eAAe,QAAQ,EAAA,EAAS,KAAA,EAAY;IAC1C,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,IAAI,GAAG,WAAW,IAAI;IAClC,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,OAAO,GAAG,WAAW,OAAO;IACxC,gLAAM,sBAAA,EAAoB,eAAe;QACvC,oKAAI,eAAA,EAAa,EAAE;QACnB,uKAAO,eAAA,EAAa,KAAK;IAC3B,CAAC;AACH;AAEA,eAAe,QAAQ,EAAA,EAAS;IAC9B,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,IAAI,GAAG,UAAU,IAAI;IACjC,MAAM,gMAAA,EAAoB,cAAc;QAAE,oKAAI,eAAA,EAAa,EAAE;IAAE,CAAC;AAClE;AAEO,SAAS,cACmC;IACjD,MAAM,SAAS,YAAY;IAC3B,OAAO;QACL,KAAK,OAAO,GAAA;QACZ,OAAO,OAAO,KAAA;QACd,aAAa,OAAO,WAAA;QACpB,QAAQ,OAAO,MAAA;QACf,QAAQ,OAAO,OAAO,UAAU;YAC9B,OAAO,MAAM,OAAO,OAAO,KAAK;QAClC;QACA,OAAO,OAAO,IAAI,UAAU;YAC1B,OAAO,MAAM,MAAM,IAAI,KAAK;QAC9B;QACA,SAAS,OAAO,IAAI,UAAU;YAC5B,OAAO,MAAM,QAAQ,IAAI,KAAK;QAChC;QACA,QAAQ,OAAO,OAAO;YACpB,OAAO,MAAM,QAAQ,EAAE;QACzB;QACA,OAAO,CAAC,cAAc;YACpB,OAAO,IAAI,YAAY,WAAW,KAAK;QACzC;IACF;AACF;AAEA,MAAM,YAAY;IAChB,YACqB,SAAA,EACA,QAAA,CACnB;QAFmB,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,QAAA,GAAA;IAClB;IAEH,MAAM,IAAI,EAAA,EAAuB;QAC/B,OAAO,IAAI,IAAI,IAAA,CAAK,QAAQ;IAC9B;IAEA,QAAQ;QACN,MAAM,uBAAuB,IAAA,CAAK,SAAA,CAAU,UAAA,CAAW,GAAG;QAC1D,IAAI,yBAAyB,IAAA,CAAK,QAAA,EAAU;YAC1C,MAAM,IAAI,MACR,GACE,uBAAuB,WAAW,MACpC,CAAA,qCAAA,EACE,IAAA,CAAK,QAAA,GAAW,KAAK,SACvB,CAAA,QAAA,CAAA;QAEJ;QACA,OAAO,6KAAI,uBAAA,CAAqB,IAAA,CAAK,SAAS;IAChD;AACF;AAEA,MAAM,oBAAoB,YAAY;IACpC,MAAM,OAAO,KAAA,EAAY;QACvB,OAAO,OAAO,IAAA,CAAK,SAAA,EAAW,KAAK;IACrC;IACA,MAAM,MAAM,EAAA,EAAS,KAAA,EAAY;QAC/B,OAAO,MAAM,IAAI,KAAK;IACxB;IACA,MAAM,QAAQ,EAAA,EAAS,KAAA,EAAY;QACjC,OAAO,QAAQ,IAAI,KAAK;IAC1B;IACA,MAAM,OAAO,EAAA,EAAS;QACpB,OAAO,QAAQ,EAAE;IACnB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1134, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/scheduler_impl.ts"], "sourcesContent": ["import { convexToJson, Value } from \"../../values/index.js\";\nimport { version } from \"../../index.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\nimport { parseArgs } from \"../../common/index.js\";\nimport { SchedulableFunctionReference, Scheduler } from \"../scheduler.js\";\nimport { Id } from \"../../values/value.js\";\nimport { validateArg } from \"./validate.js\";\nimport { getFunctionAddress } from \"../components/paths.js\";\n\nexport function setupMutationScheduler(): Scheduler {\n  return {\n    runAfter: async (\n      delayMs: number,\n      functionReference: SchedulableFunctionReference,\n      args?: Record<string, Value>,\n    ) => {\n      const syscallArgs = runAfterSyscallArgs(delayMs, functionReference, args);\n      return await performAsyncSyscall(\"1.0/schedule\", syscallArgs);\n    },\n    runAt: async (\n      ms_since_epoch_or_date: number | Date,\n      functionReference: SchedulableFunctionReference,\n      args?: Record<string, Value>,\n    ) => {\n      const syscallArgs = runAtSyscallArgs(\n        ms_since_epoch_or_date,\n        functionReference,\n        args,\n      );\n      return await performAsyncSyscall(\"1.0/schedule\", syscallArgs);\n    },\n    cancel: async (id: Id<\"_scheduled_functions\">) => {\n      validateArg(id, 1, \"cancel\", \"id\");\n      const args = { id: convexToJson(id) };\n      await performAsyncSyscall(\"1.0/cancel_job\", args);\n    },\n  };\n}\n\nexport function setupActionScheduler(requestId: string): Scheduler {\n  return {\n    runAfter: async (\n      delayMs: number,\n      functionReference: SchedulableFunctionReference,\n      args?: Record<string, Value>,\n    ) => {\n      const syscallArgs = {\n        requestId,\n        ...runAfterSyscallArgs(delayMs, functionReference, args),\n      };\n      return await performAsyncSyscall(\"1.0/actions/schedule\", syscallArgs);\n    },\n    runAt: async (\n      ms_since_epoch_or_date: number | Date,\n      functionReference: SchedulableFunctionReference,\n      args?: Record<string, Value>,\n    ) => {\n      const syscallArgs = {\n        requestId,\n        ...runAtSyscallArgs(ms_since_epoch_or_date, functionReference, args),\n      };\n      return await performAsyncSyscall(\"1.0/actions/schedule\", syscallArgs);\n    },\n    cancel: async (id: Id<\"_scheduled_functions\">) => {\n      validateArg(id, 1, \"cancel\", \"id\");\n      const syscallArgs = { id: convexToJson(id) };\n      return await performAsyncSyscall(\"1.0/actions/cancel_job\", syscallArgs);\n    },\n  };\n}\n\nfunction runAfterSyscallArgs(\n  delayMs: number,\n  functionReference: SchedulableFunctionReference,\n  args?: Record<string, Value>,\n) {\n  if (typeof delayMs !== \"number\") {\n    throw new Error(\"`delayMs` must be a number\");\n  }\n  if (!isFinite(delayMs)) {\n    throw new Error(\"`delayMs` must be a finite number\");\n  }\n  if (delayMs < 0) {\n    throw new Error(\"`delayMs` must be non-negative\");\n  }\n  const functionArgs = parseArgs(args);\n  const address = getFunctionAddress(functionReference);\n  // Note the syscall expects a unix timestamp, measured in seconds.\n  const ts = (Date.now() + delayMs) / 1000.0;\n  return {\n    ...address,\n    ts,\n    args: convexToJson(functionArgs),\n    version,\n  };\n}\n\nfunction runAtSyscallArgs(\n  ms_since_epoch_or_date: number | Date,\n  functionReference: SchedulableFunctionReference,\n  args?: Record<string, Value>,\n) {\n  let ts;\n  if (ms_since_epoch_or_date instanceof Date) {\n    ts = ms_since_epoch_or_date.valueOf() / 1000.0;\n  } else if (typeof ms_since_epoch_or_date === \"number\") {\n    // The timestamp the developer passes is in milliseconds, while the syscall\n    // accepts seconds since the epoch.\n    ts = ms_since_epoch_or_date / 1000;\n  } else {\n    throw new Error(\"The invoke time must a Date or a timestamp\");\n  }\n  const address = getFunctionAddress(functionReference);\n  const functionArgs = parseArgs(args);\n  return {\n    ...address,\n    ts,\n    args: convexToJson(functionArgs),\n    version,\n  };\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,oBAA2B;;AACpC,SAAS,eAAe;AACxB,SAAS,2BAA2B;AACpC,SAAS,iBAAiB;AAG1B,SAAS,mBAAmB;AAC5B,SAAS,0BAA0B;;;;;;;;AAE5B,SAAS,yBAAoC;IAClD,OAAO;QACL,UAAU,OACR,SACA,mBACA,SACG;YACH,MAAM,cAAc,oBAAoB,SAAS,mBAAmB,IAAI;YACxE,OAAO,gLAAM,sBAAA,EAAoB,gBAAgB,WAAW;QAC9D;QACA,OAAO,OACL,wBACA,mBACA,SACG;YACH,MAAM,cAAc,iBAClB,wBACA,mBACA;YAEF,OAAO,gLAAM,sBAAA,EAAoB,gBAAgB,WAAW;QAC9D;QACA,QAAQ,OAAO,OAAmC;YAChD,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,IAAI,GAAG,UAAU,IAAI;YACjC,MAAM,OAAO;gBAAE,oKAAI,eAAA,EAAa,EAAE;YAAE;YACpC,MAAM,gMAAA,EAAoB,kBAAkB,IAAI;QAClD;IACF;AACF;AAEO,SAAS,qBAAqB,SAAA,EAA8B;IACjE,OAAO;QACL,UAAU,OACR,SACA,mBACA,SACG;YACH,MAAM,cAAc;gBAClB;gBACA,GAAG,oBAAoB,SAAS,mBAAmB,IAAI,CAAA;YACzD;YACA,OAAO,gLAAM,sBAAA,EAAoB,wBAAwB,WAAW;QACtE;QACA,OAAO,OACL,wBACA,mBACA,SACG;YACH,MAAM,cAAc;gBAClB;gBACA,GAAG,iBAAiB,wBAAwB,mBAAmB,IAAI,CAAA;YACrE;YACA,OAAO,gLAAM,sBAAA,EAAoB,wBAAwB,WAAW;QACtE;QACA,QAAQ,OAAO,OAAmC;YAChD,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,IAAI,GAAG,UAAU,IAAI;YACjC,MAAM,cAAc;gBAAE,oKAAI,eAAA,EAAa,EAAE;YAAE;YAC3C,OAAO,gLAAM,sBAAA,EAAoB,0BAA0B,WAAW;QACxE;IACF;AACF;AAEA,SAAS,oBACP,OAAA,EACA,iBAAA,EACA,IAAA,EACA;IACA,IAAI,OAAO,YAAY,UAAU;QAC/B,MAAM,IAAI,MAAM,4BAA4B;IAC9C;IACA,IAAI,CAAC,SAAS,OAAO,GAAG;QACtB,MAAM,IAAI,MAAM,mCAAmC;IACrD;IACA,IAAI,UAAU,GAAG;QACf,MAAM,IAAI,MAAM,gCAAgC;IAClD;IACA,MAAM,+KAAe,YAAA,EAAU,IAAI;IACnC,MAAM,WAAU,kMAAA,EAAmB,iBAAiB;IAEpD,MAAM,KAAA,CAAM,KAAK,GAAA,CAAI,IAAI,OAAA,IAAW;IACpC,OAAO;QACL,GAAG,OAAA;QACH;QACA,sKAAM,eAAA,EAAa,YAAY;mKAC/B,UAAA;IACF;AACF;AAEA,SAAS,iBACP,sBAAA,EACA,iBAAA,EACA,IAAA,EACA;IACA,IAAI;IACJ,IAAI,kCAAkC,MAAM;QAC1C,KAAK,uBAAuB,OAAA,CAAQ,IAAI;IAC1C,OAAA,IAAW,OAAO,2BAA2B,UAAU;QAGrD,KAAK,yBAAyB;IAChC,OAAO;QACL,MAAM,IAAI,MAAM,4CAA4C;IAC9D;IACA,MAAM,wLAAU,qBAAA,EAAmB,iBAAiB;IACpD,MAAM,+KAAe,YAAA,EAAU,IAAI;IACnC,OAAO;QACL,GAAG,OAAA;QACH;QACA,sKAAM,eAAA,EAAa,YAAY;mKAC/B,UAAA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1240, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/storage_impl.ts"], "sourcesContent": ["import {\n  FileMetadata,\n  StorageActionWriter,\n  FileStorageId,\n  StorageReader,\n  StorageWriter,\n} from \"../storage.js\";\nimport { version } from \"../../index.js\";\nimport { performAsyncSyscall, performJsSyscall } from \"./syscall.js\";\nimport { validateArg } from \"./validate.js\";\n\nexport function setupStorageReader(requestId: string): StorageReader {\n  return {\n    getUrl: async (storageId: FileStorageId) => {\n      validateArg(storageId, 1, \"getUrl\", \"storageId\");\n      return await performAsyncSyscall(\"1.0/storageGetUrl\", {\n        requestId,\n        version,\n        storageId,\n      });\n    },\n    getMetadata: async (storageId: FileStorageId): Promise<FileMetadata> => {\n      return await performAsyncSyscall(\"1.0/storageGetMetadata\", {\n        requestId,\n        version,\n        storageId,\n      });\n    },\n  };\n}\n\nexport function setupStorageWriter(requestId: string): StorageWriter {\n  const reader = setupStorageReader(requestId);\n  return {\n    generateUploadUrl: async () => {\n      return await performAsyncSyscall(\"1.0/storageGenerateUploadUrl\", {\n        requestId,\n        version,\n      });\n    },\n    delete: async (storageId: FileStorageId) => {\n      await performAsyncSyscall(\"1.0/storageDelete\", {\n        requestId,\n        version,\n        storageId,\n      });\n    },\n    getUrl: reader.getUrl,\n    getMetadata: reader.getMetadata,\n  };\n}\n\nexport function setupStorageActionWriter(\n  requestId: string,\n): StorageActionWriter {\n  const writer = setupStorageWriter(requestId);\n  return {\n    ...writer,\n    store: async (blob: Blob, options?: { sha256?: string }) => {\n      return await performJsSyscall(\"storage/storeBlob\", {\n        requestId,\n        version,\n        blob,\n        options,\n      });\n    },\n    get: async (storageId: FileStorageId) => {\n      return await performJsSyscall(\"storage/getBlob\", {\n        requestId,\n        version,\n        storageId,\n      });\n    },\n  };\n}\n"], "names": [], "mappings": ";;;;;AAOA,SAAS,eAAe;AACxB,SAAS,qBAAqB,wBAAwB;AACtD,SAAS,mBAAmB;;;;;AAErB,SAAS,mBAAmB,SAAA,EAAkC;IACnE,OAAO;QACL,QAAQ,OAAO,cAA6B;YAC1C,CAAA,GAAA,sKAAA,CAAA,cAAA,EAAY,WAAW,GAAG,UAAU,WAAW;YAC/C,OAAO,UAAM,4LAAA,EAAoB,qBAAqB;gBACpD;2KACA,UAAA;gBACA;YACF,CAAC;QACH;QACA,aAAa,OAAO,cAAoD;YACtE,OAAO,gLAAM,sBAAA,EAAoB,0BAA0B;gBACzD;gBACA,qKAAA;gBACA;YACF,CAAC;QACH;IACF;AACF;AAEO,SAAS,mBAAmB,SAAA,EAAkC;IACnE,MAAM,SAAS,mBAAmB,SAAS;IAC3C,OAAO;QACL,mBAAmB,YAAY;YAC7B,OAAO,gLAAM,sBAAA,EAAoB,gCAAgC;gBAC/D;2KACA,UAAA;YACF,CAAC;QACH;QACA,QAAQ,OAAO,cAA6B;YAC1C,OAAM,+LAAA,EAAoB,qBAAqB;gBAC7C;2KACA,UAAA;gBACA;YACF,CAAC;QACH;QACA,QAAQ,OAAO,MAAA;QACf,aAAa,OAAO,WAAA;IACtB;AACF;AAEO,SAAS,yBACd,SAAA,EACqB;IACrB,MAAM,SAAS,mBAAmB,SAAS;IAC3C,OAAO;QACL,GAAG,MAAA;QACH,OAAO,OAAO,MAAY,YAAkC;YAC1D,OAAO,OAAM,4LAAA,EAAiB,qBAAqB;gBACjD;2KACA,UAAA;gBACA;gBACA;YACF,CAAC;QACH;QACA,KAAK,OAAO,cAA6B;YACvC,OAAO,gLAAM,mBAAA,EAAiB,mBAAmB;gBAC/C;2KACA,UAAA;gBACA;YACF,CAAC;QACH;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1318, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/impl/registration_impl.ts"], "sourcesContent": ["import {\n  ConvexError,\n  convexTo<PERSON>son,\n  GenericValidator,\n  jsonToConvex,\n  v,\n  Validator,\n  Value,\n} from \"../../values/index.js\";\nimport { GenericDataModel } from \"../data_model.js\";\nimport {\n  ActionBuilder,\n  DefaultFunctionArgs,\n  GenericActionCtx,\n  GenericMutationCtx,\n  GenericQueryCtx,\n  MutationBuilder,\n  PublicHttpAction,\n  QueryBuilder,\n  RegisteredAction,\n  RegisteredMutation,\n  RegisteredQuery,\n} from \"../registration.js\";\nimport { setupActionCalls } from \"./actions_impl.js\";\nimport { setupActionVectorSearch } from \"./vector_search_impl.js\";\nimport { setupAuth } from \"./authentication_impl.js\";\nimport { setupReader, setupWriter } from \"./database_impl.js\";\nimport { QueryImpl, QueryInitializerImpl } from \"./query_impl.js\";\nimport {\n  setupActionScheduler,\n  setupMutationScheduler,\n} from \"./scheduler_impl.js\";\nimport {\n  setupStorageActionWriter,\n  setupStorageReader,\n  setupStorageWriter,\n} from \"./storage_impl.js\";\nimport { parseArgs } from \"../../common/index.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\nimport { asObjectValidator } from \"../../values/validator.js\";\nimport { getFunctionAddress } from \"../components/paths.js\";\n\nasync function invokeMutation<\n  F extends (ctx: GenericMutationCtx<GenericDataModel>, ...args: any) => any,\n>(func: F, argsStr: string) {\n  // TODO(presley): Change the function signature and propagate the requestId from Rust.\n  // Ok, to mock it out for now, since queries are only running in V8.\n  const requestId = \"\";\n  const args = jsonToConvex(JSON.parse(argsStr));\n  const mutationCtx = {\n    db: setupWriter(),\n    auth: setupAuth(requestId),\n    storage: setupStorageWriter(requestId),\n    scheduler: setupMutationScheduler(),\n\n    runQuery: (reference: any, args?: any) => runUdf(\"query\", reference, args),\n    runMutation: (reference: any, args?: any) =>\n      runUdf(\"mutation\", reference, args),\n  };\n  const result = await invokeFunction(func, mutationCtx, args as any);\n  validateReturnValue(result);\n  return JSON.stringify(convexToJson(result === undefined ? null : result));\n}\n\nexport function validateReturnValue(v: any) {\n  if (v instanceof QueryInitializerImpl || v instanceof QueryImpl) {\n    throw new Error(\n      \"Return value is a Query. Results must be retrieved with `.collect()`, `.take(n), `.unique()`, or `.first()`.\",\n    );\n  }\n}\n\nexport async function invokeFunction<\n  Ctx,\n  Args extends any[],\n  F extends (ctx: Ctx, ...args: Args) => any,\n>(func: F, ctx: Ctx, args: Args) {\n  let result;\n  try {\n    result = await Promise.resolve(func(ctx, ...args));\n  } catch (thrown: unknown) {\n    throw serializeConvexErrorData(thrown);\n  }\n  return result;\n}\n\nfunction dontCallDirectly(\n  funcType: string,\n  handler: (ctx: any, args: any) => any,\n): unknown {\n  return (ctx: any, args: any) => {\n    globalThis.console.warn(\n      \"Convex functions should not directly call other Convex functions. Consider calling a helper function instead. \" +\n        `e.g. \\`export const foo = ${funcType}(...); await foo(ctx);\\` is not supported. ` +\n        \"See https://docs.convex.dev/production/best-practices/#use-helper-functions-to-write-shared-code\",\n    );\n    return handler(ctx, args);\n  };\n}\n\n// Keep in sync with node executor\nfunction serializeConvexErrorData(thrown: unknown) {\n  if (\n    typeof thrown === \"object\" &&\n    thrown !== null &&\n    Symbol.for(\"ConvexError\") in thrown\n  ) {\n    const error = thrown as ConvexError<any>;\n    error.data = JSON.stringify(\n      convexToJson(error.data === undefined ? null : error.data),\n    );\n    (error as any).ConvexErrorSymbol = Symbol.for(\"ConvexError\");\n    return error;\n  } else {\n    return thrown;\n  }\n}\n\n/**\n * Guard against Convex functions accidentally getting included in a browser bundle.\n * Convex functions may include secret logic or credentials that should not be\n * send to untrusted clients (browsers).\n */\nfunction assertNotBrowser() {\n  if (\n    typeof window === \"undefined\" ||\n    !(window as any).__convexAllowFunctionsInBrowser\n  ) {\n    return;\n  }\n  // JSDom doesn't count, developers are allowed to use JSDom in Convex functions.\n  const isRealBrowser =\n    Object.getOwnPropertyDescriptor(globalThis, \"window\")\n      ?.get?.toString()\n      .includes(\"[native code]\") ?? false;\n  if (isRealBrowser) {\n    throw new Error(\"Convex functions should not be imported in the browser.\");\n  }\n}\n\ntype FunctionDefinition =\n  | ((ctx: any, args: DefaultFunctionArgs) => any)\n  | {\n      args?: GenericValidator | Record<string, GenericValidator>;\n      returns?: GenericValidator | Record<string, GenericValidator>;\n      handler: (ctx: any, args: DefaultFunctionArgs) => any;\n    };\n\nfunction exportArgs(functionDefinition: FunctionDefinition) {\n  return () => {\n    let args: GenericValidator = v.any();\n    if (\n      typeof functionDefinition === \"object\" &&\n      functionDefinition.args !== undefined\n    ) {\n      args = asObjectValidator(functionDefinition.args);\n    }\n    return JSON.stringify(args.json);\n  };\n}\n\nfunction exportReturns(functionDefinition: FunctionDefinition) {\n  return () => {\n    let returns: Validator<any, any, any> | undefined;\n    if (\n      typeof functionDefinition === \"object\" &&\n      functionDefinition.returns !== undefined\n    ) {\n      returns = asObjectValidator(functionDefinition.returns);\n    }\n    return JSON.stringify(returns ? returns.json : null);\n  };\n}\n\n/**\n * Define a mutation in this Convex app's public API.\n *\n * This function will be allowed to modify your Convex database and will be accessible from the client.\n *\n * If you're using code generation, use the `mutation` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The mutation function. It receives a {@link GenericMutationCtx} as its first argument.\n * @returns The wrapped mutation. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const mutationGeneric: MutationBuilder<any, \"public\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericMutationCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"mutation\", handler) as RegisteredMutation<\n    \"public\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isMutation = true;\n  func.isPublic = true;\n  func.invokeMutation = (argsStr) => invokeMutation(handler, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as MutationBuilder<any, \"public\">;\n\n/**\n * Define a mutation that is only accessible from other Convex functions (but not from the client).\n *\n * This function will be allowed to modify your Convex database. It will not be accessible from the client.\n *\n * If you're using code generation, use the `internalMutation` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The mutation function. It receives a {@link GenericMutationCtx} as its first argument.\n * @returns The wrapped mutation. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const internalMutationGeneric: MutationBuilder<any, \"internal\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericMutationCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\n    \"internalMutation\",\n    handler,\n  ) as RegisteredMutation<\"internal\", any, any>;\n\n  assertNotBrowser();\n  func.isMutation = true;\n  func.isInternal = true;\n  func.invokeMutation = (argsStr) => invokeMutation(handler, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as MutationBuilder<any, \"internal\">;\n\nasync function invokeQuery<\n  F extends (ctx: GenericQueryCtx<GenericDataModel>, ...args: any) => any,\n>(func: F, argsStr: string) {\n  // TODO(presley): Change the function signature and propagate the requestId from Rust.\n  // Ok, to mock it out for now, since queries are only running in V8.\n  const requestId = \"\";\n  const args = jsonToConvex(JSON.parse(argsStr));\n  const queryCtx = {\n    db: setupReader(),\n    auth: setupAuth(requestId),\n    storage: setupStorageReader(requestId),\n    runQuery: (reference: any, args?: any) => runUdf(\"query\", reference, args),\n  };\n  const result = await invokeFunction(func, queryCtx, args as any);\n  validateReturnValue(result);\n  return JSON.stringify(convexToJson(result === undefined ? null : result));\n}\n\n/**\n * Define a query in this Convex app's public API.\n *\n * This function will be allowed to read your Convex database and will be accessible from the client.\n *\n * If you're using code generation, use the `query` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The query function. It receives a {@link GenericQueryCtx} as its first argument.\n * @returns The wrapped query. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const queryGeneric: QueryBuilder<any, \"public\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericQueryCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"query\", handler) as RegisteredQuery<\n    \"public\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isQuery = true;\n  func.isPublic = true;\n  func.invokeQuery = (argsStr) => invokeQuery(handler, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as QueryBuilder<any, \"public\">;\n\n/**\n * Define a query that is only accessible from other Convex functions (but not from the client).\n *\n * This function will be allowed to read from your Convex database. It will not be accessible from the client.\n *\n * If you're using code generation, use the `internalQuery` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The query function. It receives a {@link GenericQueryCtx} as its first argument.\n * @returns The wrapped query. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const internalQueryGeneric: QueryBuilder<any, \"internal\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericQueryCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"internalQuery\", handler) as RegisteredQuery<\n    \"internal\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isQuery = true;\n  func.isInternal = true;\n  func.invokeQuery = (argsStr) => invokeQuery(handler as any, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as QueryBuilder<any, \"internal\">;\n\nasync function invokeAction<\n  F extends (ctx: GenericActionCtx<GenericDataModel>, ...args: any) => any,\n>(func: F, requestId: string, argsStr: string) {\n  const args = jsonToConvex(JSON.parse(argsStr));\n  const calls = setupActionCalls(requestId);\n  const ctx = {\n    ...calls,\n    auth: setupAuth(requestId),\n    scheduler: setupActionScheduler(requestId),\n    storage: setupStorageActionWriter(requestId),\n    vectorSearch: setupActionVectorSearch(requestId) as any,\n  };\n  const result = await invokeFunction(func, ctx, args as any);\n  return JSON.stringify(convexToJson(result === undefined ? null : result));\n}\n\n/**\n * Define an action in this Convex app's public API.\n *\n * If you're using code generation, use the `action` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The function. It receives a {@link GenericActionCtx} as its first argument.\n * @returns The wrapped function. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const actionGeneric: ActionBuilder<any, \"public\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericActionCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"action\", handler) as RegisteredAction<\n    \"public\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isAction = true;\n  func.isPublic = true;\n  func.invokeAction = (requestId, argsStr) =>\n    invokeAction(handler, requestId, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as ActionBuilder<any, \"public\">;\n\n/**\n * Define an action that is only accessible from other Convex functions (but not from the client).\n *\n * If you're using code generation, use the `internalAction` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The function. It receives a {@link GenericActionCtx} as its first argument.\n * @returns The wrapped function. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const internalActionGeneric: ActionBuilder<any, \"internal\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericActionCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"internalAction\", handler) as RegisteredAction<\n    \"internal\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isAction = true;\n  func.isInternal = true;\n  func.invokeAction = (requestId, argsStr) =>\n    invokeAction(handler, requestId, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as ActionBuilder<any, \"internal\">;\n\nasync function invokeHttpAction<\n  F extends (ctx: GenericActionCtx<GenericDataModel>, request: Request) => any,\n>(func: F, request: Request) {\n  // TODO(presley): Change the function signature and propagate the requestId from Rust.\n  // Ok, to mock it out for now, since http endpoints are only running in V8.\n  const requestId = \"\";\n  const calls = setupActionCalls(requestId);\n  const ctx = {\n    ...calls,\n    auth: setupAuth(requestId),\n    storage: setupStorageActionWriter(requestId),\n    scheduler: setupActionScheduler(requestId),\n    vectorSearch: setupActionVectorSearch(requestId) as any,\n  };\n  return await invokeFunction(func, ctx, [request]);\n}\n\n/**\n * Define a Convex HTTP action.\n *\n * @param func - The function. It receives an {@link GenericActionCtx} as its first argument, and a `Request` object\n * as its second.\n * @returns The wrapped function. Route a URL path to this function in `convex/http.js`.\n *\n * @public\n */\nexport const httpActionGeneric = (\n  func: (\n    ctx: GenericActionCtx<GenericDataModel>,\n    request: Request,\n  ) => Promise<Response>,\n): PublicHttpAction => {\n  const q = dontCallDirectly(\"httpAction\", func) as PublicHttpAction;\n  assertNotBrowser();\n  q.isHttp = true;\n  q.invokeHttpAction = (request) => invokeHttpAction(func as any, request);\n  q._handler = func;\n  return q;\n};\n\nasync function runUdf(\n  udfType: \"query\" | \"mutation\",\n  f: any,\n  args?: Record<string, Value>,\n): Promise<any> {\n  const queryArgs = parseArgs(args);\n  const syscallArgs = {\n    udfType,\n    args: convexToJson(queryArgs),\n    ...getFunctionAddress(f),\n  };\n  const result = await performAsyncSyscall(\"1.0/runUdf\", syscallArgs);\n  return jsonToConvex(result);\n}\n"], "names": ["args", "v"], "mappings": ";;;;;;;;;;;;AAAA;;AAuBA,SAAS,wBAAwB;AACjC,SAAS,+BAA+B;AACxC,SAAS,iBAAiB;AAC1B,SAAS,aAAa,mBAAmB;AACzC,SAAS,WAAW,4BAA4B;AAChD;AAIA;AAKA,SAAS,iBAAiB;AAC1B,SAAS,2BAA2B;AAEpC,SAAS,0BAA0B;;;;;;;;;;;;;;AAEnC,eAAe,eAEb,IAAA,EAAS,OAAA,EAAiB;IAG1B,MAAM,YAAY;IAClB,MAAM,uKAAO,eAAA,EAAa,KAAK,KAAA,CAAM,OAAO,CAAC;IAC7C,MAAM,cAAc;QAClB,oLAAI,cAAA,CAAY;QAChB,4LAAM,YAAA,EAAU,SAAS;QACzB,uLAAS,sBAAA,EAAmB,SAAS;QACrC,4LAAW,yBAAA,CAAuB;QAElC,UAAU,CAAC,WAAgBA,QAAe,OAAO,SAAS,WAAWA,KAAI;QACzE,aAAa,CAAC,WAAgBA,QAC5B,OAAO,YAAY,WAAWA,KAAI;IACtC;IACA,MAAM,SAAS,MAAM,eAAe,MAAM,aAAa,IAAW;IAClE,oBAAoB,MAAM;IAC1B,OAAO,KAAK,SAAA,gKAAU,gBAAA,EAAa,WAAW,KAAA,IAAY,OAAO,MAAM,CAAC;AAC1E;AAEO,SAAS,oBAAoBC,EAAAA,EAAQ;IAC1C,IAAIA,uLAAa,uBAAA,IAAwBA,uLAAa,YAAA,EAAW;QAC/D,MAAM,IAAI,MACR;IAEJ;AACF;AAEA,eAAsB,eAIpB,IAAA,EAAS,GAAA,EAAU,IAAA,EAAY;IAC/B,IAAI;IACJ,IAAI;QACF,SAAS,MAAM,QAAQ,OAAA,CAAQ,KAAK,KAAK,GAAG,IAAI,CAAC;IACnD,EAAA,OAAS,QAAiB;QACxB,MAAM,yBAAyB,MAAM;IACvC;IACA,OAAO;AACT;AAEA,SAAS,iBACP,QAAA,EACA,OAAA,EACS;IACT,OAAO,CAAC,KAAU,SAAc;QAC9B,WAAW,OAAA,CAAQ,IAAA,CACjB,CAAA,wIAAA,EAC+B,QAAQ,CAAA,2IAAA,CAAA;QAGzC,OAAO,QAAQ,KAAK,IAAI;IAC1B;AACF;AAGA,SAAS,yBAAyB,MAAA,EAAiB;IACjD,IACE,OAAO,WAAW,YAClB,WAAW,QACX,OAAO,GAAA,CAAI,aAAa,KAAK,QAC7B;QACA,MAAM,QAAQ;QACd,MAAM,IAAA,GAAO,KAAK,SAAA,iKAChB,eAAA,EAAa,MAAM,IAAA,KAAS,KAAA,IAAY,OAAO,MAAM,IAAI;QAE1D,MAAc,iBAAA,GAAoB,OAAO,GAAA,CAAI,aAAa;QAC3D,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAOA,SAAS,mBAAmB;IAC1B,IACE,OAAO,WAAW,eAClB,CAAE,OAAe,+BAAA,EACjB;QACA;IACF;IAEA,MAAM,gBACJ,OAAO,wBAAA,CAAyB,YAAY,QAAQ,GAChD,KAAK,SAAS,EACf,SAAS,eAAe,KAAK;IAClC,IAAI,eAAe;QACjB,MAAM,IAAI,MAAM,yDAAyD;IAC3E;AACF;AAUA,SAAS,WAAW,kBAAA,EAAwC;IAC1D,OAAO,MAAM;QACX,IAAI,uKAAyB,IAAA,CAAE,GAAA,CAAI;QACnC,IACE,OAAO,uBAAuB,YAC9B,mBAAmB,IAAA,KAAS,KAAA,GAC5B;YACA,2KAAO,oBAAA,EAAkB,mBAAmB,IAAI;QAClD;QACA,OAAO,KAAK,SAAA,CAAU,KAAK,IAAI;IACjC;AACF;AAEA,SAAS,cAAc,kBAAA,EAAwC;IAC7D,OAAO,MAAM;QACX,IAAI;QACJ,IACE,OAAO,uBAAuB,YAC9B,mBAAmB,OAAA,KAAY,KAAA,GAC/B;YACA,8KAAU,oBAAA,EAAkB,mBAAmB,OAAO;QACxD;QACA,OAAO,KAAK,SAAA,CAAU,UAAU,QAAQ,IAAA,GAAO,IAAI;IACrD;AACF;AAeO,MAAM,kBAAmD,CAC9D,uBACG;IACH,MAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB,OAAA;IAEzB,MAAM,OAAO,iBAAiB,YAAY,OAAO;IAMjD,iBAAiB;IACjB,KAAK,UAAA,GAAa;IAClB,KAAK,QAAA,GAAW;IAChB,KAAK,cAAA,GAAiB,CAAC,UAAY,eAAe,SAAS,OAAO;IAClE,KAAK,UAAA,GAAa,WAAW,kBAAkB;IAC/C,KAAK,aAAA,GAAgB,cAAc,kBAAkB;IACrD,KAAK,QAAA,GAAW;IAChB,OAAO;AACT;AAeO,MAAM,0BAA6D,CACxE,uBACG;IACH,MAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB,OAAA;IAEzB,MAAM,OAAO,iBACX,oBACA;IAGF,iBAAiB;IACjB,KAAK,UAAA,GAAa;IAClB,KAAK,UAAA,GAAa;IAClB,KAAK,cAAA,GAAiB,CAAC,UAAY,eAAe,SAAS,OAAO;IAClE,KAAK,UAAA,GAAa,WAAW,kBAAkB;IAC/C,KAAK,aAAA,GAAgB,cAAc,kBAAkB;IACrD,KAAK,QAAA,GAAW;IAChB,OAAO;AACT;AAEA,eAAe,YAEb,IAAA,EAAS,OAAA,EAAiB;IAG1B,MAAM,YAAY;IAClB,MAAM,uKAAO,eAAA,EAAa,KAAK,KAAA,CAAM,OAAO,CAAC;IAC7C,MAAM,WAAW;QACf,KAAI,6LAAA,CAAY;QAChB,4LAAM,YAAA,EAAU,SAAS;QACzB,wLAAS,qBAAA,EAAmB,SAAS;QACrC,UAAU,CAAC,WAAgBD,QAAe,OAAO,SAAS,WAAWA,KAAI;IAC3E;IACA,MAAM,SAAS,MAAM,eAAe,MAAM,UAAU,IAAW;IAC/D,oBAAoB,MAAM;IAC1B,OAAO,KAAK,SAAA,iKAAU,eAAA,EAAa,WAAW,KAAA,IAAY,OAAO,MAAM,CAAC;AAC1E;AAeO,MAAM,eAA6C,CACxD,uBACG;IACH,MAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB,OAAA;IAEzB,MAAM,OAAO,iBAAiB,SAAS,OAAO;IAM9C,iBAAiB;IACjB,KAAK,OAAA,GAAU;IACf,KAAK,QAAA,GAAW;IAChB,KAAK,WAAA,GAAc,CAAC,UAAY,YAAY,SAAS,OAAO;IAC5D,KAAK,UAAA,GAAa,WAAW,kBAAkB;IAC/C,KAAK,aAAA,GAAgB,cAAc,kBAAkB;IACrD,KAAK,QAAA,GAAW;IAChB,OAAO;AACT;AAeO,MAAM,uBAAuD,CAClE,uBACG;IACH,MAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB,OAAA;IAEzB,MAAM,OAAO,iBAAiB,iBAAiB,OAAO;IAMtD,iBAAiB;IACjB,KAAK,OAAA,GAAU;IACf,KAAK,UAAA,GAAa;IAClB,KAAK,WAAA,GAAc,CAAC,UAAY,YAAY,SAAgB,OAAO;IACnE,KAAK,UAAA,GAAa,WAAW,kBAAkB;IAC/C,KAAK,aAAA,GAAgB,cAAc,kBAAkB;IACrD,KAAK,QAAA,GAAW;IAChB,OAAO;AACT;AAEA,eAAe,aAEb,IAAA,EAAS,SAAA,EAAmB,OAAA,EAAiB;IAC7C,MAAM,uKAAO,eAAA,EAAa,KAAK,KAAA,CAAM,OAAO,CAAC;IAC7C,MAAM,YAAQ,8LAAA,EAAiB,SAAS;IACxC,MAAM,MAAM;QACV,GAAG,KAAA;QACH,4LAAM,YAAA,EAAU,SAAS;QACzB,4LAAW,uBAAA,EAAqB,SAAS;QACzC,UAAS,yMAAA,EAAyB,SAAS;QAC3C,mMAAc,0BAAA,EAAwB,SAAS;IACjD;IACA,MAAM,SAAS,MAAM,eAAe,MAAM,KAAK,IAAW;IAC1D,OAAO,KAAK,SAAA,CAAU,+KAAA,EAAa,WAAW,KAAA,IAAY,OAAO,MAAM,CAAC;AAC1E;AAaO,MAAM,gBAA+C,CAC1D,uBACG;IACH,MAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB,OAAA;IAEzB,MAAM,OAAO,iBAAiB,UAAU,OAAO;IAM/C,iBAAiB;IACjB,KAAK,QAAA,GAAW;IAChB,KAAK,QAAA,GAAW;IAChB,KAAK,YAAA,GAAe,CAAC,WAAW,UAC9B,aAAa,SAAS,WAAW,OAAO;IAC1C,KAAK,UAAA,GAAa,WAAW,kBAAkB;IAC/C,KAAK,aAAA,GAAgB,cAAc,kBAAkB;IACrD,KAAK,QAAA,GAAW;IAChB,OAAO;AACT;AAaO,MAAM,wBAAyD,CACpE,uBACG;IACH,MAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB,OAAA;IAEzB,MAAM,OAAO,iBAAiB,kBAAkB,OAAO;IAMvD,iBAAiB;IACjB,KAAK,QAAA,GAAW;IAChB,KAAK,UAAA,GAAa;IAClB,KAAK,YAAA,GAAe,CAAC,WAAW,UAC9B,aAAa,SAAS,WAAW,OAAO;IAC1C,KAAK,UAAA,GAAa,WAAW,kBAAkB;IAC/C,KAAK,aAAA,GAAgB,cAAc,kBAAkB;IACrD,KAAK,QAAA,GAAW;IAChB,OAAO;AACT;AAEA,eAAe,iBAEb,IAAA,EAAS,OAAA,EAAkB;IAG3B,MAAM,YAAY;IAClB,MAAM,uLAAQ,mBAAA,EAAiB,SAAS;IACxC,MAAM,MAAM;QACV,GAAG,KAAA;QACH,OAAM,iMAAA,EAAU,SAAS;QACzB,wLAAS,2BAAA,EAAyB,SAAS;QAC3C,4LAAW,uBAAA,EAAqB,SAAS;QACzC,cAAc,+MAAA,EAAwB,SAAS;IACjD;IACA,OAAO,MAAM,eAAe,MAAM,KAAK;QAAC,OAAO;KAAC;AAClD;AAWO,MAAM,oBAAoB,CAC/B,SAIqB;IACrB,MAAM,IAAI,iBAAiB,cAAc,IAAI;IAC7C,iBAAiB;IACjB,EAAE,MAAA,GAAS;IACX,EAAE,gBAAA,GAAmB,CAAC,UAAY,iBAAiB,MAAa,OAAO;IACvE,EAAE,QAAA,GAAW;IACb,OAAO;AACT;AAEA,eAAe,OACb,OAAA,EACA,CAAA,EACA,IAAA,EACc;IACd,MAAM,4KAAY,YAAA,EAAU,IAAI;IAChC,MAAM,cAAc;QAClB;QACA,OAAM,8KAAA,EAAa,SAAS;QAC5B,iLAAG,qBAAA,EAAmB,CAAC,CAAA;IACzB;IACA,MAAM,SAAS,gLAAM,sBAAA,EAAoB,cAAc,WAAW;IAClE,uKAAO,eAAA,EAAa,MAAM;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1563, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/pagination.ts"], "sourcesContent": ["import { v } from \"../values/validator.js\";\n\n/**\n * An opaque identifier used for paginating a database query.\n *\n * Cursors are returned from {@link OrderedQuery.paginate} and represent the\n * point of the query where the page of results ended.\n *\n * To continue paginating, pass the cursor back into\n * {@link OrderedQuery.paginate} in the {@link PaginationOptions} object to\n * fetch another page of results.\n *\n * Note: Cursors can only be passed to _exactly_ the same database query that\n * they were generated from. You may not reuse a cursor between different\n * database queries.\n *\n * @public\n */\nexport type Cursor = string;\n\n/**\n * The result of paginating using {@link OrderedQuery.paginate}.\n *\n * @public\n */\nexport interface PaginationResult<T> {\n  /**\n   * The page of results.\n   */\n  page: T[];\n\n  /**\n   * Have we reached the end of the results?\n   */\n  isDone: boolean;\n\n  /**\n   * A {@link Cursor} to continue loading more results.\n   */\n  continueCursor: Cursor;\n\n  /**\n   * A {@link Cursor} to split the page into two, so the page from\n   * (cursor, continueCursor] can be replaced by two pages (cursor, splitCursor]\n   * and (splitCursor, continueCursor].\n   */\n  splitCursor?: Cursor | null;\n\n  /**\n   * When a query reads too much data, it may return 'SplitRecommended' to\n   * indicate that the page should be split into two with `splitCursor`.\n   * When a query reads so much data that `page` might be incomplete, its status\n   * becomes 'SplitRequired'.\n   */\n  pageStatus?: \"SplitRecommended\" | \"SplitRequired\" | null;\n}\n\n/**\n * The options passed to {@link OrderedQuery.paginate}.\n *\n * To use this type in [argument validation](https://docs.convex.dev/functions/validation),\n * use the {@link paginationOptsValidator}.\n *\n * @public\n */\nexport interface PaginationOptions {\n  /**\n   * Number of items to load in this page of results.\n   *\n   * Note: This is only an initial value!\n   *\n   * If you are running this paginated query in a reactive query function, you\n   * may receive more or less items than this if items were added to or removed\n   * from the query range.\n   */\n  numItems: number;\n\n  /**\n   * A {@link Cursor} representing the start of this page or `null` to start\n   * at the beginning of the query results.\n   */\n  cursor: Cursor | null;\n\n  /**\n   * A {@link Cursor} representing the end of this page or `null | undefined` to\n   * use `numItems` instead.\n   *\n   * @internal\n   */\n  endCursor?: Cursor | null;\n\n  /**\n   * The maximum number of rows that should be read from the database.\n   *\n   * This option is different from `numItems` in that it controls the number of rows entering a query's\n   * pipeline, where `numItems` controls the number of rows coming out. For example, a `filter`\n   * may disqualify most of the rows coming in, so setting a low `numItems` would not help\n   * bound its execution time. Instead, set a low `maximumRowsRead` to efficiently paginate\n   * through the filter.\n   *\n   * Currently this is not enforced for search queries.\n   *\n   * @internal\n   */\n  maximumRowsRead?: number;\n\n  /**\n   * The maximum number of bytes that should be read from the database.\n   *\n   * As with {@link PaginationOptions.maximumRowsRead}, this affects the number\n   * of rows entering a query's pipeline.\n   *\n   * Once a paginated query hits its bytes read budget, an incomplete page\n   * will be returned.\n   *\n   * Currently this is not enforced for search queries.\n   *\n   * @internal\n   */\n  maximumBytesRead?: number;\n}\n\n/**\n * A {@link values.Validator} for {@link PaginationOptions}.\n *\n * This includes the standard {@link PaginationOptions} properties along with\n * an optional cache-busting `id` property used by {@link react.usePaginatedQuery}.\n *\n * @public\n */\nexport const paginationOptsValidator = v.object({\n  numItems: v.number(),\n  cursor: v.union(v.string(), v.null()),\n  endCursor: v.optional(v.union(v.string(), v.null())),\n  id: v.optional(v.number()),\n  maximumRowsRead: v.optional(v.number()),\n  maximumBytesRead: v.optional(v.number()),\n});\n"], "names": [], "mappings": ";;;AAAA,SAAS,SAAS;;;AAkIX,MAAM,0LAA0B,IAAA,CAAE,MAAA,CAAO;IAC9C,0KAAU,IAAA,CAAE,MAAA,CAAO;IACnB,wKAAQ,IAAA,CAAE,KAAA,iKAAM,IAAA,CAAE,MAAA,CAAO,mKAAG,IAAA,CAAE,IAAA,CAAK,CAAC;IACpC,2KAAW,IAAA,CAAE,QAAA,iKAAS,IAAA,CAAE,KAAA,iKAAM,IAAA,CAAE,MAAA,CAAO,kKAAG,KAAA,CAAE,IAAA,CAAK,CAAC,CAAC;IACnD,oKAAI,IAAA,CAAE,QAAA,iKAAS,IAAA,CAAE,MAAA,CAAO,CAAC;IACzB,iLAAiB,IAAA,CAAE,QAAA,iKAAS,IAAA,CAAE,MAAA,CAAO,CAAC;IACtC,kLAAkB,IAAA,CAAE,QAAA,iKAAS,IAAA,CAAE,MAAA,CAAO,CAAC;AACzC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1583, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1591, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/cron.ts"], "sourcesContent": ["import { getFunctionName, OptionalRestArgs } from \"../server/api.js\";\nimport { parseArgs } from \"../common/index.js\";\nimport { convexTo<PERSON>son, JSONValue, Value } from \"../values/index.js\";\nimport { SchedulableFunctionReference } from \"./scheduler.js\";\n\ntype CronSchedule = {\n  type: \"cron\";\n  cron: string;\n};\n/** @public */\nexport type IntervalSchedule =\n  | { type: \"interval\"; seconds: number }\n  | { type: \"interval\"; minutes: number }\n  | { type: \"interval\"; hours: number };\n/** @public */\nexport type HourlySchedule = {\n  type: \"hourly\";\n  minuteUTC: number;\n};\n/** @public */\nexport type DailySchedule = {\n  type: \"daily\";\n  hourUTC: number;\n  minuteUTC: number;\n};\nconst DAYS_OF_WEEK = [\n  \"sunday\",\n  \"monday\",\n  \"tuesday\",\n  \"wednesday\",\n  \"thursday\",\n  \"friday\",\n  \"saturday\",\n] as const;\ntype DayOfWeek = (typeof DAYS_OF_WEEK)[number];\n/** @public */\nexport type WeeklySchedule = {\n  type: \"weekly\";\n  dayOfWeek: DayOfWeek;\n  hourUTC: number;\n  minuteUTC: number;\n};\n/** @public */\nexport type MonthlySchedule = {\n  type: \"monthly\";\n  day: number;\n  hourUTC: number;\n  minuteUTC: number;\n};\n\n// Duplicating types so docstrings are visible in signatures:\n// `Expand<Omit<MonthlySchedule, \"type\">>` doesn't preserve docstrings.\n// When we get to TypeScript 4.9, `satisfies` would go nicely here.\n\n/** @public */\nexport type Interval =\n  | {\n      /**\n       * Run a job every `seconds` seconds, beginning\n       * when the job is first deployed to Convex.\n       */\n      seconds: number;\n      minutes?: undefined;\n      hours?: undefined;\n    }\n  | {\n      /**\n       * Run a job every `minutes` minutes, beginning\n       * when the job is first deployed to Convex.\n       */\n      minutes: number;\n      seconds?: undefined;\n      hours?: undefined;\n    }\n  | {\n      /**\n       * Run a job every `hours` hours, beginning when\n       * when the job is first deployed to Convex.\n       */\n      hours: number;\n      seconds?: undefined;\n      minutes?: undefined;\n    };\n\n/** @public */\nexport type Hourly = {\n  /**\n   * Minutes past the hour, 0-59.\n   */\n  minuteUTC: number;\n};\n\n/** @public */\nexport type Daily = {\n  /**\n   * 0-23, hour of day. Remember, this is UTC.\n   */\n  hourUTC: number;\n  /**\n   * 0-59, minute of hour. Remember, this is UTC.\n   */\n  minuteUTC: number;\n};\n\n/** @public */\nexport type Monthly = {\n  /**\n   * 1-31, day of month. Days greater that 28 will not run every month.\n   */\n  day: number;\n  /**\n   * 0-23, hour of day. Remember to convert from your own time zone to UTC.\n   */\n  hourUTC: number;\n  /**\n   * 0-59, minute of hour. Remember to convert from your own time zone to UTC.\n   */\n  minuteUTC: number;\n};\n/** @public */\nexport type Weekly = {\n  /**\n   * \"monday\", \"tuesday\", etc.\n   */\n  dayOfWeek: DayOfWeek;\n  /**\n   * 0-23, hour of day. Remember to convert from your own time zone to UTC.\n   */\n  hourUTC: number;\n  /**\n   * 0-59, minute of hour. Remember to convert from your own time zone to UTC.\n   */\n  minuteUTC: number;\n};\n\n/** @public */\nexport type Schedule =\n  | CronSchedule\n  | IntervalSchedule\n  | HourlySchedule\n  | DailySchedule\n  | WeeklySchedule\n  | MonthlySchedule;\n\n/**\n * A schedule to run a Convex mutation or action on.\n * You can schedule Convex functions to run regularly with\n * {@link interval} and exporting it.\n *\n * @public\n **/\nexport interface CronJob {\n  name: string;\n  args: JSONValue;\n  schedule: Schedule;\n}\n\n/**\n * Create a CronJobs object to schedule recurring tasks.\n *\n * ```js\n * // convex/crons.js\n * import { cronJobs } from 'convex/server';\n * import { api } from \"./_generated/api\";\n *\n * const crons = cronJobs();\n * crons.weekly(\n *   \"weekly re-engagement email\",\n *   {\n *     hourUTC: 17, // (9:30am Pacific/10:30am Daylight Savings Pacific)\n *     minuteUTC: 30,\n *   },\n *   api.emails.send\n * )\n * export default crons;\n * ```\n *\n * @public\n */\nexport const cronJobs = () => new Crons();\n\n/**\n * @public\n *\n * This is a cron string. They're complicated!\n */\ntype CronString = string;\n\nfunction validateIntervalNumber(n: number) {\n  if (!Number.isInteger(n) || n <= 0) {\n    throw new Error(\"Interval must be an integer greater than 0\");\n  }\n}\n\nfunction validatedDayOfMonth(n: number) {\n  if (!Number.isInteger(n) || n < 1 || n > 31) {\n    throw new Error(\"Day of month must be an integer from 1 to 31\");\n  }\n  return n;\n}\n\nfunction validatedDayOfWeek(s: string) {\n  if (!DAYS_OF_WEEK.includes(s as DayOfWeek)) {\n    throw new Error('Day of week must be a string like \"monday\".');\n  }\n  return s as DayOfWeek;\n}\n\nfunction validatedHourOfDay(n: number) {\n  if (!Number.isInteger(n) || n < 0 || n > 23) {\n    throw new Error(\"Hour of day must be an integer from 0 to 23\");\n  }\n  return n;\n}\n\nfunction validatedMinuteOfHour(n: number) {\n  if (!Number.isInteger(n) || n < 0 || n > 59) {\n    throw new Error(\"Minute of hour must be an integer from 0 to 59\");\n  }\n  return n;\n}\n\nfunction validatedCronString(s: string) {\n  return s;\n}\n\nfunction validatedCronIdentifier(s: string) {\n  if (!s.match(/^[ -~]*$/)) {\n    throw new Error(\n      `Invalid cron identifier ${s}: use ASCII letters that are not control characters`,\n    );\n  }\n  return s;\n}\n\n/**\n * A class for scheduling cron jobs.\n *\n * To learn more see the documentation at https://docs.convex.dev/scheduling/cron-jobs\n *\n * @public\n */\nexport class Crons {\n  crons: Record<string, CronJob>;\n  isCrons: true;\n  constructor() {\n    this.isCrons = true;\n    this.crons = {};\n  }\n\n  /** @internal */\n  schedule(\n    cronIdentifier: string,\n    schedule: Schedule,\n    functionReference: SchedulableFunctionReference,\n    args?: Record<string, Value>,\n  ) {\n    const cronArgs = parseArgs(args);\n    validatedCronIdentifier(cronIdentifier);\n    if (cronIdentifier in this.crons) {\n      throw new Error(`Cron identifier registered twice: ${cronIdentifier}`);\n    }\n    this.crons[cronIdentifier] = {\n      name: getFunctionName(functionReference),\n      args: [convexToJson(cronArgs)],\n      schedule: schedule,\n    };\n  }\n\n  /**\n   * Schedule a mutation or action to run at some interval.\n   *\n   * ```js\n   * crons.interval(\"Clear presence data\", {seconds: 30}, api.presence.clear);\n   * ```\n   *\n   * @param identifier - A unique name for this scheduled job.\n   * @param schedule - The time between runs for this scheduled job.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - The arguments to the function.\n   */\n  interval<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    schedule: Interval,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const s = schedule;\n    const hasSeconds = +(\"seconds\" in s && s.seconds !== undefined);\n    const hasMinutes = +(\"minutes\" in s && s.minutes !== undefined);\n    const hasHours = +(\"hours\" in s && s.hours !== undefined);\n    const total = hasSeconds + hasMinutes + hasHours;\n    if (total !== 1) {\n      throw new Error(\"Must specify one of seconds, minutes, or hours\");\n    }\n    if (hasSeconds) {\n      validateIntervalNumber(schedule.seconds!);\n    } else if (hasMinutes) {\n      validateIntervalNumber(schedule.minutes!);\n    } else if (hasHours) {\n      validateIntervalNumber(schedule.hours!);\n    }\n    this.schedule(\n      cronIdentifier,\n      { ...schedule, type: \"interval\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /**\n   * Schedule a mutation or action to run on an hourly basis.\n   *\n   * ```js\n   * crons.hourly(\n   *   \"Reset high scores\",\n   *   {\n   *     minuteUTC: 30,\n   *   },\n   *   api.scores.reset\n   * )\n   * ```\n   *\n   * @param cronIdentifier - A unique name for this scheduled job.\n   * @param schedule - What time (UTC) each day to run this function.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - The arguments to the function.\n   */\n  hourly<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    schedule: Hourly,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const minuteUTC = validatedMinuteOfHour(schedule.minuteUTC);\n    this.schedule(\n      cronIdentifier,\n      { minuteUTC, type: \"hourly\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /**\n   * Schedule a mutation or action to run on a daily basis.\n   *\n   * ```js\n   * crons.daily(\n   *   \"Reset high scores\",\n   *   {\n   *     hourUTC: 17, // (9:30am Pacific/10:30am Daylight Savings Pacific)\n   *     minuteUTC: 30,\n   *   },\n   *   api.scores.reset\n   * )\n   * ```\n   *\n   * @param cronIdentifier - A unique name for this scheduled job.\n   * @param schedule - What time (UTC) each day to run this function.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - The arguments to the function.\n   */\n  daily<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    schedule: Daily,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const hourUTC = validatedHourOfDay(schedule.hourUTC);\n    const minuteUTC = validatedMinuteOfHour(schedule.minuteUTC);\n    this.schedule(\n      cronIdentifier,\n      { hourUTC, minuteUTC, type: \"daily\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /**\n   * Schedule a mutation or action to run on a weekly basis.\n   *\n   * ```js\n   * crons.weekly(\n   *   \"Weekly re-engagement email\",\n   *   {\n   *     dayOfWeek: \"Tuesday\",\n   *     hourUTC: 17, // (9:30am Pacific/10:30am Daylight Savings Pacific)\n   *     minuteUTC: 30,\n   *   },\n   *   api.emails.send\n   * )\n   * ```\n   *\n   * @param cronIdentifier - A unique name for this scheduled job.\n   * @param schedule - What day and time (UTC) each week to run this function.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   */\n  weekly<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    schedule: Weekly,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const dayOfWeek = validatedDayOfWeek(schedule.dayOfWeek);\n    const hourUTC = validatedHourOfDay(schedule.hourUTC);\n    const minuteUTC = validatedMinuteOfHour(schedule.minuteUTC);\n    this.schedule(\n      cronIdentifier,\n      { dayOfWeek, hourUTC, minuteUTC, type: \"weekly\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /**\n   * Schedule a mutation or action to run on a monthly basis.\n   *\n   * Note that some months have fewer days than others, so e.g. a function\n   * scheduled to run on the 30th will not run in February.\n   *\n   * ```js\n   * crons.monthly(\n   *   \"Bill customers at \",\n   *   {\n   *     hourUTC: 17, // (9:30am Pacific/10:30am Daylight Savings Pacific)\n   *     minuteUTC: 30,\n   *     day: 1,\n   *   },\n   *   api.billing.billCustomers\n   * )\n   * ```\n   *\n   * @param cronIdentifier - A unique name for this scheduled job.\n   * @param schedule - What day and time (UTC) each month to run this function.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - The arguments to the function.\n   */\n  monthly<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    schedule: Monthly,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const day = validatedDayOfMonth(schedule.day);\n    const hourUTC = validatedHourOfDay(schedule.hourUTC);\n    const minuteUTC = validatedMinuteOfHour(schedule.minuteUTC);\n    this.schedule(\n      cronIdentifier,\n      { day, hourUTC, minuteUTC, type: \"monthly\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /**\n   * Schedule a mutation or action to run on a recurring basis.\n   *\n   * Like the unix command `cron`, Sunday is 0, Monday is 1, etc.\n   *\n   * ```\n   *  ┌─ minute (0 - 59)\n   *  │ ┌─ hour (0 - 23)\n   *  │ │ ┌─ day of the month (1 - 31)\n   *  │ │ │ ┌─ month (1 - 12)\n   *  │ │ │ │ ┌─ day of the week (0 - 6) (Sunday to Saturday)\n   * \"* * * * *\"\n   * ```\n   *\n   * @param cronIdentifier - A unique name for this scheduled job.\n   * @param cron - Cron string like `\"15 7 * * *\"` (Every day at 7:15 UTC)\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - The arguments to the function.\n   */\n  cron<FuncRef extends SchedulableFunctionReference>(\n    cronIdentifier: string,\n    cron: CronString,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ) {\n    const c = validatedCronString(cron);\n    this.schedule(\n      cronIdentifier,\n      { cron: c, type: \"cron\" },\n      functionReference,\n      ...args,\n    );\n  }\n\n  /** @internal */\n  export() {\n    return JSON.stringify(this.crons);\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,uBAAyC;AAClD,SAAS,iBAAiB;AAC1B,SAAS,oBAAsC;;;;;;;;;;;;;;AAuB/C,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AAkJO,MAAM,WAAW,IAAM,IAAI,MAAM;AASxC,SAAS,uBAAuB,CAAA,EAAW;IACzC,IAAI,CAAC,OAAO,SAAA,CAAU,CAAC,KAAK,KAAK,GAAG;QAClC,MAAM,IAAI,MAAM,4CAA4C;IAC9D;AACF;AAEA,SAAS,oBAAoB,CAAA,EAAW;IACtC,IAAI,CAAC,OAAO,SAAA,CAAU,CAAC,KAAK,IAAI,KAAK,IAAI,IAAI;QAC3C,MAAM,IAAI,MAAM,8CAA8C;IAChE;IACA,OAAO;AACT;AAEA,SAAS,mBAAmB,CAAA,EAAW;IACrC,IAAI,CAAC,aAAa,QAAA,CAAS,CAAc,GAAG;QAC1C,MAAM,IAAI,MAAM,6CAA6C;IAC/D;IACA,OAAO;AACT;AAEA,SAAS,mBAAmB,CAAA,EAAW;IACrC,IAAI,CAAC,OAAO,SAAA,CAAU,CAAC,KAAK,IAAI,KAAK,IAAI,IAAI;QAC3C,MAAM,IAAI,MAAM,6CAA6C;IAC/D;IACA,OAAO;AACT;AAEA,SAAS,sBAAsB,CAAA,EAAW;IACxC,IAAI,CAAC,OAAO,SAAA,CAAU,CAAC,KAAK,IAAI,KAAK,IAAI,IAAI;QAC3C,MAAM,IAAI,MAAM,gDAAgD;IAClE;IACA,OAAO;AACT;AAEA,SAAS,oBAAoB,CAAA,EAAW;IACtC,OAAO;AACT;AAEA,SAAS,wBAAwB,CAAA,EAAW;IAC1C,IAAI,CAAC,EAAE,KAAA,CAAM,UAAU,GAAG;QACxB,MAAM,IAAI,MACR,CAAA,wBAAA,EAA2B,CAAC,CAAA,mDAAA,CAAA;IAEhC;IACA,OAAO;AACT;AASO,MAAM,MAAM;IAGjB,aAAc;QAFd,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEE,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,KAAA,GAAQ,CAAC;IAChB;IAAA,cAAA,GAGA,SACE,cAAA,EACA,QAAA,EACA,iBAAA,EACA,IAAA,EACA;QACA,MAAM,WAAW,4KAAA,EAAU,IAAI;QAC/B,wBAAwB,cAAc;QACtC,IAAI,kBAAkB,IAAA,CAAK,KAAA,EAAO;YAChC,MAAM,IAAI,MAAM,CAAA,kCAAA,EAAqC,cAAc,EAAE;QACvE;QACA,IAAA,CAAK,KAAA,CAAM,cAAc,CAAA,GAAI;YAC3B,MAAM,gLAAA,EAAgB,iBAAiB;YACvC,MAAM;gLAAC,eAAA,EAAa,QAAQ,CAAC;aAAA;YAC7B;QACF;IACF;IAAA;;;;;;;;;;;;GAAA,GAeA,SACE,cAAA,EACA,QAAA,EACA,iBAAA,EAAA,GACG,IAAA,EACH;QACA,MAAM,IAAI;QACV,MAAM,aAAa,CAAA,CAAE,aAAa,KAAK,EAAE,OAAA,KAAY,KAAA,CAAA;QACrD,MAAM,aAAa,CAAA,CAAE,aAAa,KAAK,EAAE,OAAA,KAAY,KAAA,CAAA;QACrD,MAAM,WAAW,CAAA,CAAE,WAAW,KAAK,EAAE,KAAA,KAAU,KAAA,CAAA;QAC/C,MAAM,QAAQ,aAAa,aAAa;QACxC,IAAI,UAAU,GAAG;YACf,MAAM,IAAI,MAAM,gDAAgD;QAClE;QACA,IAAI,YAAY;YACd,uBAAuB,SAAS,OAAQ;QAC1C,OAAA,IAAW,YAAY;YACrB,uBAAuB,SAAS,OAAQ;QAC1C,OAAA,IAAW,UAAU;YACnB,uBAAuB,SAAS,KAAM;QACxC;QACA,IAAA,CAAK,QAAA,CACH,gBACA;YAAE,GAAG,QAAA;YAAU,MAAM;QAAW,GAChC,sBACG;IAEP;IAAA;;;;;;;;;;;;;;;;;;GAAA,GAqBA,OACE,cAAA,EACA,QAAA,EACA,iBAAA,EAAA,GACG,IAAA,EACH;QACA,MAAM,YAAY,sBAAsB,SAAS,SAAS;QAC1D,IAAA,CAAK,QAAA,CACH,gBACA;YAAE;YAAW,MAAM;QAAS,GAC5B,sBACG;IAEP;IAAA;;;;;;;;;;;;;;;;;;;GAAA,GAsBA,MACE,cAAA,EACA,QAAA,EACA,iBAAA,EAAA,GACG,IAAA,EACH;QACA,MAAM,UAAU,mBAAmB,SAAS,OAAO;QACnD,MAAM,YAAY,sBAAsB,SAAS,SAAS;QAC1D,IAAA,CAAK,QAAA,CACH,gBACA;YAAE;YAAS;YAAW,MAAM;QAAQ,GACpC,sBACG;IAEP;IAAA;;;;;;;;;;;;;;;;;;;GAAA,GAsBA,OACE,cAAA,EACA,QAAA,EACA,iBAAA,EAAA,GACG,IAAA,EACH;QACA,MAAM,YAAY,mBAAmB,SAAS,SAAS;QACvD,MAAM,UAAU,mBAAmB,SAAS,OAAO;QACnD,MAAM,YAAY,sBAAsB,SAAS,SAAS;QAC1D,IAAA,CAAK,QAAA,CACH,gBACA;YAAE;YAAW;YAAS;YAAW,MAAM;QAAS,GAChD,sBACG;IAEP;IAAA;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA0BA,QACE,cAAA,EACA,QAAA,EACA,iBAAA,EAAA,GACG,IAAA,EACH;QACA,MAAM,MAAM,oBAAoB,SAAS,GAAG;QAC5C,MAAM,UAAU,mBAAmB,SAAS,OAAO;QACnD,MAAM,YAAY,sBAAsB,SAAS,SAAS;QAC1D,IAAA,CAAK,QAAA,CACH,gBACA;YAAE;YAAK;YAAS;YAAW,MAAM;QAAU,GAC3C,sBACG;IAEP;IAAA;;;;;;;;;;;;;;;;;;;GAAA,GAsBA,KACE,cAAA,EACA,IAAA,EACA,iBAAA,EAAA,GACG,IAAA,EACH;QACA,MAAM,IAAI,oBAAoB,IAAI;QAClC,IAAA,CAAK,QAAA,CACH,gBACA;YAAE,MAAM;YAAG,MAAM;QAAO,GACxB,sBACG;IAEP;IAAA,cAAA,GAGA,SAAS;QACP,OAAO,KAAK,SAAA,CAAU,IAAA,CAAK,KAAK;IAClC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1866, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/router.ts"], "sourcesContent": ["import { performJsSyscall } from \"./impl/syscall.js\";\nimport { PublicHttpAction } from \"./registration.js\";\n\n// Note: this list is duplicated in the dashboard.\n/**\n * A list of the methods supported by Convex HTTP actions.\n *\n * HEAD is handled by Convex by running GET and stripping the body.\n * CONNECT is not supported and will not be supported.\n * TRACE is not supported and will not be supported.\n *\n * @public\n */\nexport const ROUTABLE_HTTP_METHODS = [\n  \"GET\",\n  \"POST\",\n  \"PUT\",\n  \"DELETE\",\n  \"OPTIONS\",\n  \"PATCH\",\n] as const;\n/**\n * A type representing the methods supported by Convex HTTP actions.\n *\n * HEAD is handled by Convex by running GET and stripping the body.\n * CONNECT is not supported and will not be supported.\n * TRACE is not supported and will not be supported.\n *\n * @public\n */\nexport type RoutableMethod = (typeof ROUTABLE_HTTP_METHODS)[number];\n\nexport function normalizeMethod(\n  method: RoutableMethod | \"HEAD\",\n): RoutableMethod {\n  // This router routes HEAD requests as GETs, letting <PERSON><PERSON><PERSON> strip thee response\n  // bodies are response bodies afterward.\n  if (method === \"HEAD\") return \"GET\";\n  return method;\n}\n\n/**\n * Return a new {@link HttpRouter} object.\n *\n * @public\n */\nexport const httpRouter = () => new HttpRouter();\n\n/**\n * A type representing a route to an HTTP action using an exact request URL path match.\n *\n * Used by {@link HttpRouter} to route requests to HTTP actions.\n *\n * @public\n */\nexport type RouteSpecWithPath = {\n  /**\n   * Exact HTTP request path to route.\n   */\n  path: string;\n  /**\n   * HTTP method (\"GET\", \"POST\", ...) to route.\n   */\n  method: RoutableMethod;\n  /**\n   * The HTTP action to execute.\n   */\n  handler: PublicHttpAction;\n};\n\n/**\n * A type representing a route to an HTTP action using a request URL path prefix match.\n *\n * Used by {@link HttpRouter} to route requests to HTTP actions.\n *\n * @public\n */\nexport type RouteSpecWithPathPrefix = {\n  /**\n   * An HTTP request path prefix to route. Requests with a path starting with this value\n   * will be routed to the HTTP action.\n   */\n  pathPrefix: string;\n  /**\n   * HTTP method (\"GET\", \"POST\", ...) to route.\n   */\n  method: RoutableMethod;\n  /**\n   * The HTTP action to execute.\n   */\n  handler: PublicHttpAction;\n};\n\n/**\n * A type representing a route to an HTTP action.\n *\n * Used by {@link HttpRouter} to route requests to HTTP actions.\n *\n * @public\n */\nexport type RouteSpec = RouteSpecWithPath | RouteSpecWithPathPrefix;\n\n/**\n * HTTP router for specifying the paths and methods of {@link httpActionGeneric}s\n *\n * An example `convex/http.js` file might look like this.\n *\n * ```js\n * import { httpRouter } from \"convex/server\";\n * import { getMessagesByAuthor } from \"./getMessagesByAuthor\";\n * import { httpAction } from \"./_generated/server\";\n *\n * const http = httpRouter();\n *\n * // HTTP actions can be defined inline...\n * http.route({\n *   path: \"/message\",\n *   method: \"POST\",\n *   handler: httpAction(async ({ runMutation }, request) => {\n *     const { author, body } = await request.json();\n *\n *     await runMutation(api.sendMessage.default, { body, author });\n *     return new Response(null, {\n *       status: 200,\n *     });\n *   })\n * });\n *\n * // ...or they can be imported from other files.\n * http.route({\n *   path: \"/getMessagesByAuthor\",\n *   method: \"GET\",\n *   handler: getMessagesByAuthor,\n * });\n *\n * // Convex expects the router to be the default export of `convex/http.js`.\n * export default http;\n * ```\n *\n * @public\n */\nexport class HttpRouter {\n  exactRoutes: Map<string, Map<RoutableMethod, PublicHttpAction>> = new Map();\n  prefixRoutes: Map<RoutableMethod, Map<string, PublicHttpAction>> = new Map();\n  isRouter: true = true;\n\n  /**\n   * Specify an HttpAction to be used to respond to requests\n   * for an HTTP method (e.g. \"GET\") and a path or pathPrefix.\n   *\n   * Paths must begin with a slash. Path prefixes must also end in a slash.\n   *\n   * ```js\n   * // matches `/profile` (but not `/profile/`)\n   * http.route({ path: \"/profile\", method: \"GET\", handler: getProfile})\n   *\n   * // matches `/profiles/`, `/profiles/abc`, and `/profiles/a/c/b` (but not `/profile`)\n   * http.route({ pathPrefix: \"/profile/\", method: \"GET\", handler: getProfile})\n   * ```\n   */\n  route = (spec: RouteSpec) => {\n    if (!spec.handler) throw new Error(`route requires handler`);\n    if (!spec.method) throw new Error(`route requires method`);\n    const { method, handler } = spec;\n    if (!ROUTABLE_HTTP_METHODS.includes(method)) {\n      throw new Error(\n        `'${method}' is not an allowed HTTP method (like GET, POST, PUT etc.)`,\n      );\n    }\n\n    if (\"path\" in spec) {\n      if (\"pathPrefix\" in spec) {\n        throw new Error(\n          `Invalid httpRouter route: cannot contain both 'path' and 'pathPrefix'`,\n        );\n      }\n      if (!spec.path.startsWith(\"/\")) {\n        throw new Error(`path '${spec.path}' does not start with a /`);\n      }\n      const methods: Map<RoutableMethod, PublicHttpAction> =\n        this.exactRoutes.has(spec.path)\n          ? this.exactRoutes.get(spec.path)!\n          : new Map();\n      if (methods.has(method)) {\n        throw new Error(\n          `Path '${spec.path}' for method ${method} already in use`,\n        );\n      }\n      methods.set(method, handler);\n      this.exactRoutes.set(spec.path, methods);\n    } else if (\"pathPrefix\" in spec) {\n      if (!spec.pathPrefix.startsWith(\"/\")) {\n        throw new Error(\n          `pathPrefix '${spec.pathPrefix}' does not start with a /`,\n        );\n      }\n      if (!spec.pathPrefix.endsWith(\"/\")) {\n        throw new Error(`pathPrefix ${spec.pathPrefix} must end with a /`);\n      }\n      const prefixes =\n        this.prefixRoutes.get(method) || new Map<string, PublicHttpAction>();\n      if (prefixes.has(spec.pathPrefix)) {\n        throw new Error(\n          `${spec.method} pathPrefix ${spec.pathPrefix} is already defined`,\n        );\n      }\n      prefixes.set(spec.pathPrefix, handler);\n      this.prefixRoutes.set(method, prefixes);\n    } else {\n      throw new Error(\n        `Invalid httpRouter route entry: must contain either field 'path' or 'pathPrefix'`,\n      );\n    }\n  };\n\n  /**\n   * Returns a list of routed HTTP actions.\n   *\n   * These are used to populate the list of routes shown in the Functions page of the Convex dashboard.\n   *\n   * @returns - an array of [path, method, endpoint] tuples.\n   */\n  getRoutes = (): Array<\n    Readonly<[string, RoutableMethod, PublicHttpAction]>\n  > => {\n    const exactPaths: string[] = [...this.exactRoutes.keys()].sort();\n    const exact = exactPaths.flatMap((path) =>\n      [...this.exactRoutes.get(path)!.keys()]\n        .sort()\n        .map(\n          (method) =>\n            [path, method, this.exactRoutes.get(path)!.get(method)!] as const,\n        ),\n    );\n\n    const prefixPathMethods = [...this.prefixRoutes.keys()].sort();\n    const prefixes = prefixPathMethods.flatMap((method) =>\n      [...this.prefixRoutes.get(method)!.keys()]\n        .sort()\n        .map(\n          (pathPrefix) =>\n            [\n              `${pathPrefix}*`,\n              method,\n              this.prefixRoutes.get(method)!.get(pathPrefix)!,\n            ] as const,\n        ),\n    );\n\n    return [...exact, ...prefixes];\n  };\n\n  /**\n   * Returns the appropriate HTTP action and its routed request path and method.\n   *\n   * The path and method returned are used for logging and metrics, and should\n   * match up with one of the routes returned by `getRoutes`.\n   *\n   * For example,\n   *\n   * ```js\n   * http.route({ pathPrefix: \"/profile/\", method: \"GET\", handler: getProfile});\n   *\n   * http.lookup(\"/profile/abc\", \"GET\") // returns [getProfile, \"GET\", \"/profile/*\"]\n   *```\n   *\n   * @returns - a tuple [{@link PublicHttpAction}, method, path] or null.\n   */\n  lookup = (\n    path: string,\n    method: RoutableMethod | \"HEAD\",\n  ): Readonly<[PublicHttpAction, RoutableMethod, string]> | null => {\n    method = normalizeMethod(method);\n    const exactMatch = this.exactRoutes.get(path)?.get(method);\n    if (exactMatch) return [exactMatch, method, path];\n\n    const prefixes = this.prefixRoutes.get(method) || new Map();\n    const prefixesSorted = [...prefixes.entries()].sort(\n      ([prefixA, _a], [prefixB, _b]) => prefixB.length - prefixA.length,\n    );\n    for (const [pathPrefix, endpoint] of prefixesSorted) {\n      if (path.startsWith(pathPrefix)) {\n        return [endpoint, method, `${pathPrefix}*`];\n      }\n    }\n    return null;\n  };\n\n  /**\n   * Given a JSON string representation of a Request object, return a Response\n   * by routing the request and running the appropriate endpoint or returning\n   * a 404 Response.\n   *\n   * @param argsStr - a JSON string representing a Request object.\n   *\n   * @returns - a Response object.\n   */\n  runRequest = async (\n    argsStr: string,\n    requestRoute: string,\n  ): Promise<string> => {\n    const request = performJsSyscall(\"requestFromConvexJson\", {\n      convexJson: JSON.parse(argsStr),\n    });\n\n    let pathname = requestRoute;\n    if (!pathname || typeof pathname !== \"string\") {\n      pathname = new URL(request.url).pathname;\n    }\n\n    const method = request.method;\n    const match = this.lookup(pathname, method as RoutableMethod);\n    if (!match) {\n      const response = new Response(`No HttpAction routed for ${pathname}`, {\n        status: 404,\n      });\n      return JSON.stringify(\n        performJsSyscall(\"convexJsonFromResponse\", { response }),\n      );\n    }\n    const [endpoint, _method, _path] = match;\n    const response = await endpoint.invokeHttpAction(request);\n    return JSON.stringify(\n      performJsSyscall(\"convexJsonFromResponse\", { response }),\n    );\n  };\n}\n"], "names": ["response"], "mappings": ";;;;;;AAAA,SAAS,wBAAwB;;;;;;;;;;;AAa1B,MAAM,wBAAwB;IACnC;IACA;IACA;IACA;IACA;IACA;CACF;AAYO,SAAS,gBACd,MAAA,EACgB;IAGhB,IAAI,WAAW,OAAQ,CAAA,OAAO;IAC9B,OAAO;AACT;AAOO,MAAM,aAAa,IAAM,IAAI,WAAW;AA+FxC,MAAM,WAAW;IAAjB,aAAA;QACL,cAAA,IAAA,EAAA,eAAkE,aAAA,GAAA,IAAI,IAAI;QAC1E,cAAA,IAAA,EAAA,gBAAmE,aAAA,GAAA,IAAI,IAAI;QAC3E,cAAA,IAAA,EAAA,YAAiB;QAgBjB;;;;;;;;;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,SAAQ,CAAC,SAAoB;YAC3B,IAAI,CAAC,KAAK,OAAA,CAAS,CAAA,MAAM,IAAI,MAAM,CAAA,sBAAA,CAAwB;YAC3D,IAAI,CAAC,KAAK,MAAA,CAAQ,CAAA,MAAM,IAAI,MAAM,CAAA,qBAAA,CAAuB;YACzD,MAAM,EAAE,MAAA,EAAQ,OAAA,CAAQ,CAAA,GAAI;YAC5B,IAAI,CAAC,sBAAsB,QAAA,CAAS,MAAM,GAAG;gBAC3C,MAAM,IAAI,MACR,CAAA,CAAA,EAAI,MAAM,CAAA,0DAAA,CAAA;YAEd;YAEA,IAAI,UAAU,MAAM;gBAClB,IAAI,gBAAgB,MAAM;oBACxB,MAAM,IAAI,MACR,CAAA,qEAAA,CAAA;gBAEJ;gBACA,IAAI,CAAC,KAAK,IAAA,CAAK,UAAA,CAAW,GAAG,GAAG;oBAC9B,MAAM,IAAI,MAAM,CAAA,MAAA,EAAS,KAAK,IAAI,CAAA,yBAAA,CAA2B;gBAC/D;gBACA,MAAM,UACJ,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI,KAAK,IAAI,IAC1B,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI,KAAK,IAAI,IAC9B,aAAA,GAAA,IAAI,IAAI;gBACd,IAAI,QAAQ,GAAA,CAAI,MAAM,GAAG;oBACvB,MAAM,IAAI,MACR,CAAA,MAAA,EAAS,KAAK,IAAI,CAAA,aAAA,EAAgB,MAAM,CAAA,eAAA,CAAA;gBAE5C;gBACA,QAAQ,GAAA,CAAI,QAAQ,OAAO;gBAC3B,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI,KAAK,IAAA,EAAM,OAAO;YACzC,OAAA,IAAW,gBAAgB,MAAM;gBAC/B,IAAI,CAAC,KAAK,UAAA,CAAW,UAAA,CAAW,GAAG,GAAG;oBACpC,MAAM,IAAI,MACR,CAAA,YAAA,EAAe,KAAK,UAAU,CAAA,yBAAA,CAAA;gBAElC;gBACA,IAAI,CAAC,KAAK,UAAA,CAAW,QAAA,CAAS,GAAG,GAAG;oBAClC,MAAM,IAAI,MAAM,CAAA,WAAA,EAAc,KAAK,UAAU,CAAA,kBAAA,CAAoB;gBACnE;gBACA,MAAM,WACJ,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,MAAM,KAAK,aAAA,GAAA,IAAI,IAA8B;gBACrE,IAAI,SAAS,GAAA,CAAI,KAAK,UAAU,GAAG;oBACjC,MAAM,IAAI,MACR,GAAG,KAAK,MAAM,CAAA,YAAA,EAAe,KAAK,UAAU,CAAA,mBAAA,CAAA;gBAEhD;gBACA,SAAS,GAAA,CAAI,KAAK,UAAA,EAAY,OAAO;gBACrC,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,QAAQ,QAAQ;YACxC,OAAO;gBACL,MAAM,IAAI,MACR,CAAA,gFAAA,CAAA;YAEJ;QACF;QASA;;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,aAAY,MAEP;YACH,MAAM,aAAuB,CAAC;mBAAG,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,CAAC;aAAA,CAAE,IAAA,CAAK;YAC/D,MAAM,QAAQ,WAAW,OAAA,CAAQ,CAAC,OAChC,CAAC;uBAAG,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI,IAAI,EAAG,IAAA,CAAK,CAAC;iBAAA,CACnC,IAAA,CAAK,EACL,GAAA,CACC,CAAC,SACC;wBAAC;wBAAM;wBAAQ,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI,IAAI,EAAG,GAAA,CAAI,MAAM,CAAE;qBAAA;YAI/D,MAAM,oBAAoB,CAAC;mBAAG,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,CAAC;aAAA,CAAE,IAAA,CAAK;YAC7D,MAAM,WAAW,kBAAkB,OAAA,CAAQ,CAAC,SAC1C,CAAC;uBAAG,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,MAAM,EAAG,IAAA,CAAK,CAAC;iBAAA,CACtC,IAAA,CAAK,EACL,GAAA,CACC,CAAC,aACC;wBACE,GAAG,UAAU,CAAA,CAAA,CAAA;wBACb;wBACA,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,MAAM,EAAG,GAAA,CAAI,UAAU;qBAC/C;YAIR,OAAO,CAAC;mBAAG,OAAO;mBAAG,QAAQ;aAAA;QAC/B;QAkBA;;;;;;;;;;;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,UAAS,CACP,MACA,WACgE;YAChE,SAAS,gBAAgB,MAAM;YAC/B,MAAM,aAAa,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI,IAAI,GAAG,IAAI,MAAM;YACzD,IAAI,WAAY,CAAA,OAAO;gBAAC;gBAAY;gBAAQ,IAAI;aAAA;YAEhD,MAAM,WAAW,IAAA,CAAK,YAAA,CAAa,GAAA,CAAI,MAAM,KAAK,aAAA,GAAA,IAAI,IAAI;YAC1D,MAAM,iBAAiB,CAAC;mBAAG,SAAS,OAAA,CAAQ,CAAC;aAAA,CAAE,IAAA,CAC7C,CAAC,CAAC,SAAS,EAAE,CAAA,EAAG,CAAC,SAAS,EAAE,CAAA,GAAM,QAAQ,MAAA,GAAS,QAAQ,MAAA;YAE7D,KAAA,MAAW,CAAC,YAAY,QAAQ,CAAA,IAAK,eAAgB;gBACnD,IAAI,KAAK,UAAA,CAAW,UAAU,GAAG;oBAC/B,OAAO;wBAAC;wBAAU;wBAAQ,GAAG,UAAU,CAAA,CAAA,CAAG;qBAAA;gBAC5C;YACF;YACA,OAAO;QACT;QAWA;;;;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,cAAa,OACX,SACA,iBACoB;YACpB,MAAM,WAAU,4LAAA,EAAiB,yBAAyB;gBACxD,YAAY,KAAK,KAAA,CAAM,OAAO;YAChC,CAAC;YAED,IAAI,WAAW;YACf,IAAI,CAAC,YAAY,OAAO,aAAa,UAAU;gBAC7C,WAAW,IAAI,IAAI,QAAQ,GAAG,EAAE,QAAA;YAClC;YAEA,MAAM,SAAS,QAAQ,MAAA;YACvB,MAAM,QAAQ,IAAA,CAAK,MAAA,CAAO,UAAU,MAAwB;YAC5D,IAAI,CAAC,OAAO;gBACV,MAAMA,YAAW,IAAI,SAAS,CAAA,yBAAA,EAA4B,QAAQ,EAAA,EAAI;oBACpE,QAAQ;gBACV,CAAC;gBACD,OAAO,KAAK,SAAA,2KACV,mBAAA,EAAiB,0BAA0B;oBAAE,UAAAA;gBAAS,CAAC;YAE3D;YACA,MAAM,CAAC,UAAU,SAAS,KAAK,CAAA,GAAI;YACnC,MAAM,WAAW,MAAM,SAAS,gBAAA,CAAiB,OAAO;YACxD,OAAO,KAAK,SAAA,2KACV,mBAAA,EAAiB,0BAA0B;gBAAE;YAAS,CAAC;QAE3D;IAAA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2061, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/components/index.ts"], "sourcesContent": ["import { PropertyValidators, convexToJson } from \"../../values/index.js\";\nimport { version } from \"../../index.js\";\nimport {\n  AnyFunctionReference,\n  FunctionReference,\n  FunctionType,\n} from \"../api.js\";\nimport { performAsyncSyscall } from \"../impl/syscall.js\";\nimport { DefaultFunctionArgs } from \"../registration.js\";\nimport {\n  AppDefinitionAnalysis,\n  ComponentDefinitionAnalysis,\n  ComponentDefinitionType,\n} from \"./definition.js\";\nimport {\n  getFunctionAddress,\n  setReferencePath,\n  toReferencePath,\n} from \"./paths.js\";\nexport { getFunctionAddress } from \"./paths.js\";\n\n/**\n * A serializable reference to a Convex function.\n * Passing a this reference to another component allows that component to call this\n * function during the current function execution or at any later time.\n * Function handles are used like `api.folder.function` FunctionReferences,\n * e.g. `ctx.scheduler.runAfter(0, functionReference, args)`.\n *\n * A function reference is stable across code pushes but it's possible\n * the Convex function it refers to might no longer exist.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport type FunctionHandle<\n  Type extends FunctionType,\n  Args extends DefaultFunctionArgs = any,\n  ReturnType = any,\n> = string & FunctionReference<Type, \"internal\", Args, ReturnType>;\n\n/**\n * Create a serializable reference to a Convex function.\n * Passing a this reference to another component allows that component to call this\n * function during the current function execution or at any later time.\n * Function handles are used like `api.folder.function` FunctionReferences,\n * e.g. `ctx.scheduler.runAfter(0, functionReference, args)`.\n *\n * A function reference is stable across code pushes but it's possible\n * the Convex function it refers to might no longer exist.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport async function createFunctionHandle<\n  Type extends FunctionType,\n  Args extends DefaultFunctionArgs,\n  ReturnType,\n>(\n  functionReference: FunctionReference<\n    Type,\n    \"public\" | \"internal\",\n    Args,\n    ReturnType\n  >,\n): Promise<FunctionHandle<Type, Args, ReturnType>> {\n  const address = getFunctionAddress(functionReference);\n  return await performAsyncSyscall(\"1.0/createFunctionHandle\", {\n    ...address,\n    version,\n  });\n}\n\ninterface ComponentExports {\n  [key: string]: FunctionReference<any, any, any, any> | ComponentExports;\n}\n\n/**\n * An object of this type should be the default export of a\n * convex.config.ts file in a component definition directory.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport type ComponentDefinition<Exports extends ComponentExports = any> = {\n  /**\n   * Install a component with the given definition in this component definition.\n   *\n   * Takes a component definition and an optional name.\n   *\n   * For editor tooling this method expects a {@link ComponentDefinition}\n   * but at runtime the object that is imported will be a {@link ImportedComponentDefinition}\n   */\n  use<Definition extends ComponentDefinition<any>>(\n    definition: Definition,\n    options?: {\n      name?: string;\n    },\n  ): InstalledComponent<Definition>;\n\n  /**\n   * Internal type-only property tracking exports provided.\n   *\n   * @deprecated This is a type-only property, don't use it.\n   */\n  __exports: Exports;\n};\n\ntype ComponentDefinitionExports<T extends ComponentDefinition<any>> =\n  T[\"__exports\"];\n\n/**\n * An object of this type should be the default export of a\n * convex.config.ts file in a component-aware convex directory.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport type AppDefinition = {\n  /**\n   * Install a component with the given definition in this component definition.\n   *\n   * Takes a component definition and an optional name.\n   *\n   * For editor tooling this method expects a {@link ComponentDefinition}\n   * but at runtime the object that is imported will be a {@link ImportedComponentDefinition}\n   */\n  use<Definition extends ComponentDefinition<any>>(\n    definition: Definition,\n    options?: {\n      name?: string;\n    },\n  ): InstalledComponent<Definition>;\n};\n\ninterface ExportTree {\n  // Tree with serialized `Reference`s as leaves.\n  [key: string]: string | ExportTree;\n}\n\ntype CommonDefinitionData = {\n  _isRoot: boolean;\n  _childComponents: [\n    string,\n    ImportedComponentDefinition,\n    Record<string, any> | null,\n  ][];\n  _exportTree: ExportTree;\n};\n\ntype ComponentDefinitionData = CommonDefinitionData & {\n  _args: PropertyValidators;\n  _name: string;\n  _onInitCallbacks: Record<string, (argsStr: string) => string>;\n};\ntype AppDefinitionData = CommonDefinitionData;\n\n/**\n * Used to refer to an already-installed component.\n */\nclass InstalledComponent<Definition extends ComponentDefinition<any>> {\n  /**\n   * @internal\n   */\n  _definition: Definition;\n\n  /**\n   * @internal\n   */\n  _name: string;\n\n  constructor(definition: Definition, name: string) {\n    this._definition = definition;\n    this._name = name;\n    setReferencePath(this, `_reference/childComponent/${name}`);\n  }\n\n  get exports(): ComponentDefinitionExports<Definition> {\n    return createExports(this._name, []);\n  }\n}\n\nfunction createExports(name: string, pathParts: string[]): any {\n  const handler: ProxyHandler<any> = {\n    get(_, prop: string | symbol) {\n      if (typeof prop === \"string\") {\n        const newParts = [...pathParts, prop];\n        return createExports(name, newParts);\n      } else if (prop === toReferencePath) {\n        let reference = `_reference/childComponent/${name}`;\n        for (const part of pathParts) {\n          reference += `/${part}`;\n        }\n        return reference;\n      } else {\n        return undefined;\n      }\n    },\n  };\n  return new Proxy({}, handler);\n}\n\nfunction use<Definition extends ComponentDefinition<any>>(\n  this: CommonDefinitionData,\n  definition: Definition,\n  options?: {\n    name?: string;\n  },\n): InstalledComponent<Definition> {\n  // At runtime an imported component will have this shape.\n  const importedComponentDefinition =\n    definition as unknown as ImportedComponentDefinition;\n  if (typeof importedComponentDefinition.componentDefinitionPath !== \"string\") {\n    throw new Error(\n      \"Component definition does not have the required componentDefinitionPath property. This code only works in Convex runtime.\",\n    );\n  }\n  const name =\n    options?.name ||\n    // added recently\n    importedComponentDefinition.defaultName ||\n    // can be removed once backend is out\n    importedComponentDefinition.componentDefinitionPath.split(\"/\").pop()!;\n  this._childComponents.push([name, importedComponentDefinition, {}]);\n  return new InstalledComponent(definition, name);\n}\n\n/**\n * The runtime type of a ComponentDefinition. TypeScript will claim\n * the default export of a module like \"cool-component/convex.config.js\"\n * is a `@link ComponentDefinition}, but during component definition evaluation\n * this is its type instead.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport type ImportedComponentDefinition = {\n  componentDefinitionPath: string;\n  defaultName: string;\n};\n\nfunction exportAppForAnalysis(\n  this: ComponentDefinition<any> & AppDefinitionData,\n): AppDefinitionAnalysis {\n  const definitionType = { type: \"app\" as const };\n  const childComponents = serializeChildComponents(this._childComponents);\n  return {\n    definitionType,\n    childComponents: childComponents as any,\n    httpMounts: {},\n    exports: serializeExportTree(this._exportTree),\n  };\n}\n\nfunction serializeExportTree(tree: ExportTree): any {\n  const branch: any[] = [];\n  for (const [key, child] of Object.entries(tree)) {\n    let node;\n    if (typeof child === \"string\") {\n      node = { type: \"leaf\", leaf: child };\n    } else {\n      node = serializeExportTree(child);\n    }\n    branch.push([key, node]);\n  }\n  return { type: \"branch\", branch };\n}\n\nfunction serializeChildComponents(\n  childComponents: [\n    string,\n    ImportedComponentDefinition,\n    Record<string, any> | null,\n  ][],\n): {\n  name: string;\n  path: string;\n  args: [string, { type: \"value\"; value: string }][] | null;\n}[] {\n  return childComponents.map(([name, definition, p]) => {\n    let args: [string, { type: \"value\"; value: string }][] | null = null;\n    if (p !== null) {\n      args = [];\n      for (const [name, value] of Object.entries(p)) {\n        if (value !== undefined) {\n          args.push([\n            name,\n            { type: \"value\", value: JSON.stringify(convexToJson(value)) },\n          ]);\n        }\n      }\n    }\n    // we know that components carry this extra information\n    const path = definition.componentDefinitionPath;\n    if (!path)\n      throw new Error(\n        \"no .componentPath for component definition \" +\n          JSON.stringify(definition, null, 2),\n      );\n\n    return {\n      name: name!,\n      path: path!,\n      args,\n    };\n  });\n}\n\nfunction exportComponentForAnalysis(\n  this: ComponentDefinition<any> & ComponentDefinitionData,\n): ComponentDefinitionAnalysis {\n  const args: [string, { type: \"value\"; value: string }][] = Object.entries(\n    this._args,\n  ).map(([name, validator]) => [\n    name,\n    {\n      type: \"value\",\n      value: JSON.stringify(validator.json),\n    },\n  ]);\n  const definitionType: ComponentDefinitionType = {\n    type: \"childComponent\" as const,\n    name: this._name,\n    args,\n  };\n  const childComponents = serializeChildComponents(this._childComponents);\n  return {\n    name: this._name,\n    definitionType,\n    childComponents: childComponents as any,\n    httpMounts: {},\n    exports: serializeExportTree(this._exportTree),\n  };\n}\n\n// This is what is actually contained in a ComponentDefinition.\ntype RuntimeComponentDefinition = Omit<ComponentDefinition<any>, \"__exports\"> &\n  ComponentDefinitionData & {\n    export: () => ComponentDefinitionAnalysis;\n  };\ntype RuntimeAppDefinition = AppDefinition &\n  AppDefinitionData & {\n    export: () => AppDefinitionAnalysis;\n  };\n\n/**\n * Define a component, a piece of a Convex deployment with namespaced resources.\n *\n * The default\n * the default export of a module like \"cool-component/convex.config.js\"\n * is a `@link ComponentDefinition}, but during component definition evaluation\n * this is its type instead.\n *\n * @param name Name must be alphanumeric plus underscores. Typically these are\n * lowercase with underscores like `\"onboarding_flow_tracker\"`.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport function defineComponent<Exports extends ComponentExports = any>(\n  name: string,\n): ComponentDefinition<Exports> {\n  const ret: RuntimeComponentDefinition = {\n    _isRoot: false,\n    _name: name,\n    _args: {},\n    _childComponents: [],\n    _exportTree: {},\n    _onInitCallbacks: {},\n\n    export: exportComponentForAnalysis,\n    use,\n\n    // pretend to conform to ComponentDefinition, which temporarily expects __args\n    ...({} as { __args: any; __exports: any }),\n  };\n  return ret as any as ComponentDefinition<Exports>;\n}\n\n/**\n * Attach components, reuseable pieces of a Convex deployment, to this Convex app.\n *\n * This is a feature of components, which are in beta.\n * This API is unstable and may change in subsequent releases.\n */\nexport function defineApp(): AppDefinition {\n  const ret: RuntimeAppDefinition = {\n    _isRoot: true,\n    _childComponents: [],\n    _exportTree: {},\n\n    export: exportAppForAnalysis,\n    use,\n  };\n  return ret as AppDefinition;\n}\n\ntype AnyInterfaceType = {\n  [key: string]: AnyInterfaceType;\n} & AnyFunctionReference;\nexport type AnyComponentReference = Record<string, AnyInterfaceType>;\n\nexport type AnyChildComponents = Record<string, AnyComponentReference>;\n\n/**\n * @internal\n */\nexport function currentSystemUdfInComponent(\n  componentId: string,\n): AnyComponentReference {\n  return {\n    [toReferencePath]: `_reference/currentSystemUdfInComponent/${componentId}`,\n  };\n}\n\nfunction createChildComponents(\n  root: string,\n  pathParts: string[],\n): AnyChildComponents {\n  const handler: ProxyHandler<object> = {\n    get(_, prop: string | symbol) {\n      if (typeof prop === \"string\") {\n        const newParts = [...pathParts, prop];\n        return createChildComponents(root, newParts);\n      } else if (prop === toReferencePath) {\n        if (pathParts.length < 1) {\n          const found = [root, ...pathParts].join(\".\");\n          throw new Error(\n            `API path is expected to be of the form \\`${root}.childComponent.functionName\\`. Found: \\`${found}\\``,\n          );\n        }\n        return `_reference/childComponent/` + pathParts.join(\"/\");\n      } else {\n        return undefined;\n      }\n    },\n  };\n  return new Proxy({}, handler);\n}\n\nexport const componentsGeneric = () => createChildComponents(\"components\", []);\n\nexport type AnyComponents = AnyChildComponents;\n"], "names": ["name"], "mappings": ";;;;;;;AAAA,SAA6B,oBAAoB;;AACjD,SAAS,eAAe;AAMxB,SAAS,2BAA2B;AAOpC;;;;;;;;;;;;;;;AAuCA,eAAsB,qBAKpB,iBAAA,EAMiD;IACjD,MAAM,wLAAU,qBAAA,EAAmB,iBAAiB;IACpD,OAAO,MAAM,gMAAA,EAAoB,4BAA4B;QAC3D,GAAG,OAAA;mKACH,UAAA;IACF,CAAC;AACH;AAyFA,MAAM,mBAAgE;IAWpE,YAAY,UAAA,EAAwB,IAAA,CAAc;QAPlD;;KAAA,GAAA,cAAA,IAAA,EAAA;QAKA;;KAAA,GAAA,cAAA,IAAA,EAAA;QAGE,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,KAAA,GAAQ;QACb,CAAA,GAAA,yKAAA,CAAA,mBAAA,EAAiB,IAAA,EAAM,CAAA,0BAAA,EAA6B,IAAI,EAAE;IAC5D;IAEA,IAAI,UAAkD;QACpD,OAAO,cAAc,IAAA,CAAK,KAAA,EAAO,CAAC,CAAC;IACrC;AACF;AAEA,SAAS,cAAc,IAAA,EAAc,SAAA,EAA0B;IAC7D,MAAM,UAA6B;QACjC,KAAI,CAAA,EAAG,IAAA,EAAuB;YAC5B,IAAI,OAAO,SAAS,UAAU;gBAC5B,MAAM,WAAW,CAAC;uBAAG;oBAAW,IAAI;iBAAA;gBACpC,OAAO,cAAc,MAAM,QAAQ;YACrC,OAAA,IAAW,mLAAS,kBAAA,EAAiB;gBACnC,IAAI,YAAY,CAAA,0BAAA,EAA6B,IAAI,EAAA;gBACjD,KAAA,MAAW,QAAQ,UAAW;oBAC5B,aAAa,CAAA,CAAA,EAAI,IAAI,EAAA;gBACvB;gBACA,OAAO;YACT,OAAO;gBACL,OAAO,KAAA;YACT;QACF;IACF;IACA,OAAO,IAAI,MAAM,CAAC,GAAG,OAAO;AAC9B;AAEA,SAAS,IAEP,UAAA,EACA,OAAA,EAGgC;IAEhC,MAAM,8BACJ;IACF,IAAI,OAAO,4BAA4B,uBAAA,KAA4B,UAAU;QAC3E,MAAM,IAAI,MACR;IAEJ;IACA,MAAM,OACJ,SAAS,QAAA,iBAAA;IAET,4BAA4B,WAAA,IAAA,qCAAA;IAE5B,4BAA4B,uBAAA,CAAwB,KAAA,CAAM,GAAG,EAAE,GAAA,CAAI;IACrE,IAAA,CAAK,gBAAA,CAAiB,IAAA,CAAK;QAAC;QAAM;QAA6B,CAAC,CAAC;KAAC;IAClE,OAAO,IAAI,mBAAmB,YAAY,IAAI;AAChD;AAgBA,SAAS,uBAEgB;IACvB,MAAM,iBAAiB;QAAE,MAAM;IAAe;IAC9C,MAAM,kBAAkB,yBAAyB,IAAA,CAAK,gBAAgB;IACtE,OAAO;QACL;QACA;QACA,YAAY,CAAC;QACb,SAAS,oBAAoB,IAAA,CAAK,WAAW;IAC/C;AACF;AAEA,SAAS,oBAAoB,IAAA,EAAuB;IAClD,MAAM,SAAgB,CAAC,CAAA;IACvB,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,IAAI,EAAG;QAC/C,IAAI;QACJ,IAAI,OAAO,UAAU,UAAU;YAC7B,OAAO;gBAAE,MAAM;gBAAQ,MAAM;YAAM;QACrC,OAAO;YACL,OAAO,oBAAoB,KAAK;QAClC;QACA,OAAO,IAAA,CAAK;YAAC;YAAK,IAAI;SAAC;IACzB;IACA,OAAO;QAAE,MAAM;QAAU;IAAO;AAClC;AAEA,SAAS,yBACP,eAAA,EASE;IACF,OAAO,gBAAgB,GAAA,CAAI,CAAC,CAAC,MAAM,YAAY,CAAC,CAAA,KAAM;QACpD,IAAI,OAA4D;QAChE,IAAI,MAAM,MAAM;YACd,OAAO,CAAC,CAAA;YACR,KAAA,MAAW,CAACA,OAAM,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,CAAC,EAAG;gBAC7C,IAAI,UAAU,KAAA,GAAW;oBACvB,KAAK,IAAA,CAAK;wBACRA;wBACA;4BAAE,MAAM;4BAAS,OAAO,KAAK,SAAA,iKAAU,eAAA,EAAa,KAAK,CAAC;wBAAE;qBAC7D;gBACH;YACF;QACF;QAEA,MAAM,OAAO,WAAW,uBAAA;QACxB,IAAI,CAAC,MACH,MAAM,IAAI,MACR,gDACE,KAAK,SAAA,CAAU,YAAY,MAAM,CAAC;QAGxC,OAAO;YACL;YACA;YACA;QACF;IACF,CAAC;AACH;AAEA,SAAS,6BAEsB;IAC7B,MAAM,OAAqD,OAAO,OAAA,CAChE,IAAA,CAAK,KAAA,EACL,GAAA,CAAI,CAAC,CAAC,MAAM,SAAS,CAAA,GAAM;YAC3B;YACA;gBACE,MAAM;gBACN,OAAO,KAAK,SAAA,CAAU,UAAU,IAAI;YACtC;SACD;IACD,MAAM,iBAA0C;QAC9C,MAAM;QACN,MAAM,IAAA,CAAK,KAAA;QACX;IACF;IACA,MAAM,kBAAkB,yBAAyB,IAAA,CAAK,gBAAgB;IACtE,OAAO;QACL,MAAM,IAAA,CAAK,KAAA;QACX;QACA;QACA,YAAY,CAAC;QACb,SAAS,oBAAoB,IAAA,CAAK,WAAW;IAC/C;AACF;AA0BO,SAAS,gBACd,IAAA,EAC8B;IAC9B,MAAM,MAAkC;QACtC,SAAS;QACT,OAAO;QACP,OAAO,CAAC;QACR,kBAAkB,CAAC,CAAA;QACnB,aAAa,CAAC;QACd,kBAAkB,CAAC;QAEnB,QAAQ;QACR;QAAA,8EAAA;QAGA,GAAI,CAAC,CAAA;IACP;IACA,OAAO;AACT;AAQO,SAAS,YAA2B;IACzC,MAAM,MAA4B;QAChC,SAAS;QACT,kBAAkB,CAAC,CAAA;QACnB,aAAa,CAAC;QAEd,QAAQ;QACR;IACF;IACA,OAAO;AACT;AAYO,SAAS,4BACd,WAAA,EACuB;IACvB,OAAO;QACL,2KAAC,kBAAe,CAAA,EAAG,CAAA,uCAAA,EAA0C,WAAW,EAAA;IAC1E;AACF;AAEA,SAAS,sBACP,IAAA,EACA,SAAA,EACoB;IACpB,MAAM,UAAgC;QACpC,KAAI,CAAA,EAAG,IAAA,EAAuB;YAC5B,IAAI,OAAO,SAAS,UAAU;gBAC5B,MAAM,WAAW,CAAC;uBAAG;oBAAW,IAAI;iBAAA;gBACpC,OAAO,sBAAsB,MAAM,QAAQ;YAC7C,OAAA,IAAW,mLAAS,kBAAA,EAAiB;gBACnC,IAAI,UAAU,MAAA,GAAS,GAAG;oBACxB,MAAM,QAAQ;wBAAC,MAAM;2BAAG,SAAS;qBAAA,CAAE,IAAA,CAAK,GAAG;oBAC3C,MAAM,IAAI,MACR,CAAA,yCAAA,EAA4C,IAAI,CAAA,yCAAA,EAA4C,KAAK,CAAA,EAAA,CAAA;gBAErG;gBACA,OAAO,CAAA,0BAAA,CAAA,GAA+B,UAAU,IAAA,CAAK,GAAG;YAC1D,OAAO;gBACL,OAAO,KAAA;YACT;QACF;IACF;IACA,OAAO,IAAI,MAAM,CAAC,GAAG,OAAO;AAC9B;AAEO,MAAM,oBAAoB,IAAM,sBAAsB,cAAc,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2303, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/schema.ts"], "sourcesContent": ["/**\n * Utilities for defining the schema of your Convex project.\n *\n * ## Usage\n *\n * Schemas should be placed in a `schema.ts` file in your `convex/` directory.\n *\n * Schema definitions should be built using {@link defineSchema},\n * {@link defineTable}, and {@link values.v}. Make sure to export the schema as the\n * default export.\n *\n * ```ts\n * import { defineSchema, defineTable } from \"convex/server\";\n * import { v } from \"convex/values\";\n *\n *  export default defineSchema({\n *    messages: defineTable({\n *      body: v.string(),\n *      user: v.id(\"users\"),\n *    }),\n *    users: defineTable({\n *      name: v.string(),\n *    }),\n *  });\n * ```\n *\n * To learn more about schemas, see [Defining a Schema](https://docs.convex.dev/using/schemas).\n * @module\n */\nimport {\n  AnyDataModel,\n  GenericDataModel,\n  GenericTableIndexes,\n  GenericTableSearchIndexes,\n  GenericTableVectorIndexes,\n  TableNamesInDataModel,\n} from \"../server/data_model.js\";\nimport {\n  <PERSON><PERSON><PERSON><PERSON>,\n  IndexTiebreakerField,\n  SystemFields,\n  SystemIndexes,\n} from \"../server/system_fields.js\";\nimport { Expand } from \"../type_utils.js\";\nimport {\n  GenericValidator,\n  ObjectType,\n  isValidator,\n  v,\n} from \"../values/validator.js\";\nimport { VObject, Validator } from \"../values/validators.js\";\n\n/**\n * Extract all of the index field paths within a {@link Validator}.\n *\n * This is used within {@link defineTable}.\n * @public\n */\ntype ExtractFieldPaths<T extends Validator<any, any, any>> =\n  // Add in the system fields available in index definitions.\n  // This should be everything except for `_id` because thats added to indexes\n  // automatically.\n  T[\"fieldPaths\"] | keyof SystemFields;\n\n/**\n * Extract the {@link GenericDocument} within a {@link Validator} and\n * add on the system fields.\n *\n * This is used within {@link defineTable}.\n * @public\n */\ntype ExtractDocument<T extends Validator<any, any, any>> =\n  // Add the system fields to `Value` (except `_id` because it depends on\n  //the table name) and trick TypeScript into expanding them.\n  Expand<SystemFields & T[\"type\"]>;\n\n/**\n * The configuration for a full text search index.\n *\n * @public\n */\nexport interface SearchIndexConfig<\n  SearchField extends string,\n  FilterFields extends string,\n> {\n  /**\n   * The field to index for full text search.\n   *\n   * This must be a field of type `string`.\n   */\n  searchField: SearchField;\n\n  /**\n   * Additional fields to index for fast filtering when running search queries.\n   */\n  filterFields?: FilterFields[];\n}\n\n/**\n * The configuration for a vector index.\n *\n * @public\n */\nexport interface VectorIndexConfig<\n  VectorField extends string,\n  FilterFields extends string,\n> {\n  /**\n   * The field to index for vector search.\n   *\n   * This must be a field of type `v.array(v.float64())` (or a union)\n   */\n  vectorField: VectorField;\n  /**\n   * The length of the vectors indexed. This must be between 2 and 2048 inclusive.\n   */\n  dimensions: number;\n  /**\n   * Additional fields to index for fast filtering when running vector searches.\n   */\n  filterFields?: FilterFields[];\n}\n\n/**\n * @internal\n */\nexport type VectorIndex = {\n  indexDescriptor: string;\n  vectorField: string;\n  dimensions: number;\n  filterFields: string[];\n};\n\n/**\n * @internal\n */\nexport type Index = {\n  indexDescriptor: string;\n  fields: string[];\n};\n\n/**\n * @internal\n */\nexport type SearchIndex = {\n  indexDescriptor: string;\n  searchField: string;\n  filterFields: string[];\n};\n/**\n * The definition of a table within a schema.\n *\n * This should be produced by using {@link defineTable}.\n * @public\n */\nexport class TableDefinition<\n  DocumentType extends Validator<any, any, any> = Validator<any, any, any>,\n  Indexes extends GenericTableIndexes = {},\n  SearchIndexes extends GenericTableSearchIndexes = {},\n  VectorIndexes extends GenericTableVectorIndexes = {},\n> {\n  private indexes: Index[];\n  private searchIndexes: SearchIndex[];\n  private vectorIndexes: VectorIndex[];\n  // The type of documents stored in this table.\n  validator: DocumentType;\n\n  /**\n   * @internal\n   */\n  constructor(documentType: DocumentType) {\n    this.indexes = [];\n    this.searchIndexes = [];\n    this.vectorIndexes = [];\n    this.validator = documentType;\n  }\n\n  /**\n   * Define an index on this table.\n   *\n   * To learn about indexes, see [Defining Indexes](https://docs.convex.dev/using/indexes).\n   *\n   * @param name - The name of the index.\n   * @param fields - The fields to index, in order. Must specify at least one\n   * field.\n   * @returns A {@link TableDefinition} with this index included.\n   */\n  index<\n    IndexName extends string,\n    FirstFieldPath extends ExtractFieldPaths<DocumentType>,\n    RestFieldPaths extends ExtractFieldPaths<DocumentType>[],\n  >(\n    name: IndexName,\n    fields: [FirstFieldPath, ...RestFieldPaths],\n  ): TableDefinition<\n    DocumentType,\n    // Update `Indexes` to include the new index and use `Expand` to make the\n    // types look pretty in editors.\n    Expand<\n      Indexes &\n        Record<\n          IndexName,\n          [FirstFieldPath, ...RestFieldPaths, IndexTiebreakerField]\n        >\n    >,\n    SearchIndexes,\n    VectorIndexes\n  > {\n    this.indexes.push({ indexDescriptor: name, fields });\n    return this;\n  }\n\n  /**\n   * Define a search index on this table.\n   *\n   * To learn about search indexes, see [Search](https://docs.convex.dev/text-search).\n   *\n   * @param name - The name of the index.\n   * @param indexConfig - The search index configuration object.\n   * @returns A {@link TableDefinition} with this search index included.\n   */\n  searchIndex<\n    IndexName extends string,\n    SearchField extends ExtractFieldPaths<DocumentType>,\n    FilterFields extends ExtractFieldPaths<DocumentType> = never,\n  >(\n    name: IndexName,\n    indexConfig: Expand<SearchIndexConfig<SearchField, FilterFields>>,\n  ): TableDefinition<\n    DocumentType,\n    Indexes,\n    // Update `SearchIndexes` to include the new index and use `Expand` to make\n    // the types look pretty in editors.\n    Expand<\n      SearchIndexes &\n        Record<\n          IndexName,\n          {\n            searchField: SearchField;\n            filterFields: FilterFields;\n          }\n        >\n    >,\n    VectorIndexes\n  > {\n    this.searchIndexes.push({\n      indexDescriptor: name,\n      searchField: indexConfig.searchField,\n      filterFields: indexConfig.filterFields || [],\n    });\n    return this;\n  }\n\n  /**\n   * Define a vector index on this table.\n   *\n   * To learn about vector indexes, see [Vector Search](https://docs.convex.dev/vector-search).\n   *\n   * @param name - The name of the index.\n   * @param indexConfig - The vector index configuration object.\n   * @returns A {@link TableDefinition} with this vector index included.\n   */\n  vectorIndex<\n    IndexName extends string,\n    VectorField extends ExtractFieldPaths<DocumentType>,\n    FilterFields extends ExtractFieldPaths<DocumentType> = never,\n  >(\n    name: IndexName,\n    indexConfig: Expand<VectorIndexConfig<VectorField, FilterFields>>,\n  ): TableDefinition<\n    DocumentType,\n    Indexes,\n    SearchIndexes,\n    Expand<\n      VectorIndexes &\n        Record<\n          IndexName,\n          {\n            vectorField: VectorField;\n            dimensions: number;\n            filterFields: FilterFields;\n          }\n        >\n    >\n  > {\n    this.vectorIndexes.push({\n      indexDescriptor: name,\n      vectorField: indexConfig.vectorField,\n      dimensions: indexConfig.dimensions,\n      filterFields: indexConfig.filterFields || [],\n    });\n    return this;\n  }\n\n  /**\n   * Work around for https://github.com/microsoft/TypeScript/issues/57035\n   */\n  protected self(): TableDefinition<\n    DocumentType,\n    Indexes,\n    SearchIndexes,\n    VectorIndexes\n  > {\n    return this;\n  }\n  /**\n   * Export the contents of this definition.\n   *\n   * This is called internally by the Convex framework.\n   * @internal\n   */\n  export() {\n    const documentType = this.validator.json;\n    if (typeof documentType !== \"object\") {\n      throw new Error(\n        \"Invalid validator: please make sure that the parameter of `defineTable` is valid (see https://docs.convex.dev/database/schemas)\",\n      );\n    }\n\n    return {\n      indexes: this.indexes,\n      searchIndexes: this.searchIndexes,\n      vectorIndexes: this.vectorIndexes,\n      documentType,\n    };\n  }\n}\n\n/**\n * Define a table in a schema.\n *\n * You can either specify the schema of your documents as an object like\n * ```ts\n * defineTable({\n *   field: v.string()\n * });\n * ```\n *\n * or as a schema type like\n * ```ts\n * defineTable(\n *  v.union(\n *    v.object({...}),\n *    v.object({...})\n *  )\n * );\n * ```\n *\n * @param documentSchema - The type of documents stored in this table.\n * @returns A {@link TableDefinition} for the table.\n *\n * @public\n */\nexport function defineTable<\n  DocumentSchema extends Validator<Record<string, any>, \"required\", any>,\n>(documentSchema: DocumentSchema): TableDefinition<DocumentSchema>;\n/**\n * Define a table in a schema.\n *\n * You can either specify the schema of your documents as an object like\n * ```ts\n * defineTable({\n *   field: v.string()\n * });\n * ```\n *\n * or as a schema type like\n * ```ts\n * defineTable(\n *  v.union(\n *    v.object({...}),\n *    v.object({...})\n *  )\n * );\n * ```\n *\n * @param documentSchema - The type of documents stored in this table.\n * @returns A {@link TableDefinition} for the table.\n *\n * @public\n */\nexport function defineTable<\n  DocumentSchema extends Record<string, GenericValidator>,\n>(\n  documentSchema: DocumentSchema,\n): TableDefinition<VObject<ObjectType<DocumentSchema>, DocumentSchema>>;\nexport function defineTable<\n  DocumentSchema extends\n    | Validator<Record<string, any>, \"required\", any>\n    | Record<string, GenericValidator>,\n>(documentSchema: DocumentSchema): TableDefinition<any, any, any> {\n  if (isValidator(documentSchema)) {\n    return new TableDefinition(documentSchema);\n  } else {\n    return new TableDefinition(v.object(documentSchema));\n  }\n}\n\n/**\n * A type describing the schema of a Convex project.\n *\n * This should be constructed using {@link defineSchema}, {@link defineTable},\n * and {@link v}.\n * @public\n */\nexport type GenericSchema = Record<string, TableDefinition>;\n\n/**\n *\n * The definition of a Convex project schema.\n *\n * This should be produced by using {@link defineSchema}.\n * @public\n */\nexport class SchemaDefinition<\n  Schema extends GenericSchema,\n  StrictTableTypes extends boolean,\n> {\n  public tables: Schema;\n  public strictTableNameTypes!: StrictTableTypes;\n  private readonly schemaValidation: boolean;\n\n  /**\n   * @internal\n   */\n  constructor(tables: Schema, options?: DefineSchemaOptions<StrictTableTypes>) {\n    this.tables = tables;\n    this.schemaValidation =\n      options?.schemaValidation === undefined ? true : options.schemaValidation;\n  }\n\n  /**\n   * Export the contents of this definition.\n   *\n   * This is called internally by the Convex framework.\n   * @internal\n   */\n  export(): string {\n    return JSON.stringify({\n      tables: Object.entries(this.tables).map(([tableName, definition]) => {\n        const { indexes, searchIndexes, vectorIndexes, documentType } =\n          definition.export();\n        return {\n          tableName,\n          indexes,\n          searchIndexes,\n          vectorIndexes,\n          documentType,\n        };\n      }),\n      schemaValidation: this.schemaValidation,\n    });\n  }\n}\n\n/**\n * Options for {@link defineSchema}.\n *\n * @public\n */\nexport interface DefineSchemaOptions<StrictTableNameTypes extends boolean> {\n  /**\n   * Whether Convex should validate at runtime that all documents match\n   * your schema.\n   *\n   * If `schemaValidation` is `true`, Convex will:\n   * 1. Check that all existing documents match your schema when your schema\n   * is pushed.\n   * 2. Check that all insertions and updates match your schema during mutations.\n   *\n   * If `schemaValidation` is `false`, Convex will not validate that new or\n   * existing documents match your schema. You'll still get schema-specific\n   * TypeScript types, but there will be no validation at runtime that your\n   * documents match those types.\n   *\n   * By default, `schemaValidation` is `true`.\n   */\n  schemaValidation?: boolean;\n\n  /**\n   * Whether the TypeScript types should allow accessing tables not in the schema.\n   *\n   * If `strictTableNameTypes` is `true`, using tables not listed in the schema\n   * will generate a TypeScript compilation error.\n   *\n   * If `strictTableNameTypes` is `false`, you'll be able to access tables not\n   * listed in the schema and their document type will be `any`.\n   *\n   * `strictTableNameTypes: false` is useful for rapid prototyping.\n   *\n   * Regardless of the value of `strictTableNameTypes`, your schema will only\n   * validate documents in the tables listed in the schema. You can still create\n   * and modify other tables on the dashboard or in JavaScript mutations.\n   *\n   * By default, `strictTableNameTypes` is `true`.\n   */\n  strictTableNameTypes?: StrictTableNameTypes;\n}\n\n/**\n * Define the schema of this Convex project.\n *\n * This should be exported from a `schema.ts` file in your `convex/` directory\n * like:\n *\n * ```ts\n * export default defineSchema({\n *   ...\n * });\n * ```\n *\n * @param schema - A map from table name to {@link TableDefinition} for all of\n * the tables in this project.\n * @param options - Optional configuration. See {@link DefineSchemaOptions} for\n * a full description.\n * @returns The schema.\n *\n * @public\n */\nexport function defineSchema<\n  Schema extends GenericSchema,\n  StrictTableNameTypes extends boolean = true,\n>(\n  schema: Schema,\n  options?: DefineSchemaOptions<StrictTableNameTypes>,\n): SchemaDefinition<Schema, StrictTableNameTypes> {\n  return new SchemaDefinition(schema, options);\n}\n\n/**\n * Internal type used in Convex code generation!\n *\n * Convert a {@link SchemaDefinition} into a {@link server.GenericDataModel}.\n *\n * @public\n */\nexport type DataModelFromSchemaDefinition<\n  SchemaDef extends SchemaDefinition<any, boolean>,\n> = MaybeMakeLooseDataModel<\n  {\n    [TableName in keyof SchemaDef[\"tables\"] &\n      string]: SchemaDef[\"tables\"][TableName] extends TableDefinition<\n      infer DocumentType,\n      infer Indexes,\n      infer SearchIndexes,\n      infer VectorIndexes\n    >\n      ? {\n          // We've already added all of the system fields except for `_id`.\n          // Add that here.\n          document: Expand<IdField<TableName> & ExtractDocument<DocumentType>>;\n          fieldPaths:\n            | keyof IdField<TableName>\n            | ExtractFieldPaths<DocumentType>;\n          indexes: Expand<Indexes & SystemIndexes>;\n          searchIndexes: SearchIndexes;\n          vectorIndexes: VectorIndexes;\n        }\n      : never;\n  },\n  SchemaDef[\"strictTableNameTypes\"]\n>;\n\ntype MaybeMakeLooseDataModel<\n  DataModel extends GenericDataModel,\n  StrictTableNameTypes extends boolean,\n> = StrictTableNameTypes extends true\n  ? DataModel\n  : Expand<DataModel & AnyDataModel>;\n\nconst _systemSchema = defineSchema({\n  _scheduled_functions: defineTable({\n    name: v.string(),\n    args: v.array(v.any()),\n    scheduledTime: v.float64(),\n    completedTime: v.optional(v.float64()),\n    state: v.union(\n      v.object({ kind: v.literal(\"pending\") }),\n      v.object({ kind: v.literal(\"inProgress\") }),\n      v.object({ kind: v.literal(\"success\") }),\n      v.object({ kind: v.literal(\"failed\"), error: v.string() }),\n      v.object({ kind: v.literal(\"canceled\") }),\n    ),\n  }),\n  _storage: defineTable({\n    sha256: v.string(),\n    size: v.float64(),\n    contentType: v.optional(v.string()),\n  }),\n});\n\nexport interface SystemDataModel\n  extends DataModelFromSchemaDefinition<typeof _systemSchema> {}\n\nexport type SystemTableNames = TableNamesInDataModel<SystemDataModel>;\n"], "names": [], "mappings": ";;;;;;AA4CA;;;;;;;;;;;AA+GO,MAAM,gBAKX;IAAA;;GAAA,GAUA,YAAY,YAAA,CAA4B;QATxC,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QACR,cAAA,IAAA,EAAQ;QAER,8CAAA;QAAA,cAAA,IAAA,EAAA;QAME,IAAA,CAAK,OAAA,GAAU,CAAC,CAAA;QAChB,IAAA,CAAK,aAAA,GAAgB,CAAC,CAAA;QACtB,IAAA,CAAK,aAAA,GAAgB,CAAC,CAAA;QACtB,IAAA,CAAK,SAAA,GAAY;IACnB;IAAA;;;;;;;;;GAAA,GAYA,MAKE,IAAA,EACA,MAAA,EAcA;QACA,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK;YAAE,iBAAiB;YAAM;QAAO,CAAC;QACnD,OAAO,IAAA;IACT;IAAA;;;;;;;;GAAA,GAWA,YAKE,IAAA,EACA,WAAA,EAiBA;QACA,IAAA,CAAK,aAAA,CAAc,IAAA,CAAK;YACtB,iBAAiB;YACjB,aAAa,YAAY,WAAA;YACzB,cAAc,YAAY,YAAA,IAAgB,CAAC,CAAA;QAC7C,CAAC;QACD,OAAO,IAAA;IACT;IAAA;;;;;;;;GAAA,GAWA,YAKE,IAAA,EACA,WAAA,EAgBA;QACA,IAAA,CAAK,aAAA,CAAc,IAAA,CAAK;YACtB,iBAAiB;YACjB,aAAa,YAAY,WAAA;YACzB,YAAY,YAAY,UAAA;YACxB,cAAc,YAAY,YAAA,IAAgB,CAAC,CAAA;QAC7C,CAAC;QACD,OAAO,IAAA;IACT;IAAA;;GAAA,GAKU,OAKR;QACA,OAAO,IAAA;IACT;IAAA;;;;;GAAA,GAOA,SAAS;QACP,MAAM,eAAe,IAAA,CAAK,SAAA,CAAU,IAAA;QACpC,IAAI,OAAO,iBAAiB,UAAU;YACpC,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;YACL,SAAS,IAAA,CAAK,OAAA;YACd,eAAe,IAAA,CAAK,aAAA;YACpB,eAAe,IAAA,CAAK,aAAA;YACpB;QACF;IACF;AACF;AA4DO,SAAS,YAId,cAAA,EAAgE;IAChE,wKAAI,cAAA,EAAY,cAAc,GAAG;QAC/B,OAAO,IAAI,gBAAgB,cAAc;IAC3C,OAAO;QACL,OAAO,IAAI,gLAAgB,IAAA,CAAE,MAAA,CAAO,cAAc,CAAC;IACrD;AACF;AAkBO,MAAM,iBAGX;IAAA;;GAAA,GAQA,YAAY,MAAA,EAAgB,OAAA,CAAiD;QAP7E,cAAA,IAAA,EAAO;QACP,cAAA,IAAA,EAAO;QACP,cAAA,IAAA,EAAiB;QAMf,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,gBAAA,GACH,SAAS,qBAAqB,KAAA,IAAY,OAAO,QAAQ,gBAAA;IAC7D;IAAA;;;;;GAAA,GAQA,SAAiB;QACf,OAAO,KAAK,SAAA,CAAU;YACpB,QAAQ,OAAO,OAAA,CAAQ,IAAA,CAAK,MAAM,EAAE,GAAA,CAAI,CAAC,CAAC,WAAW,UAAU,CAAA,KAAM;gBACnE,MAAM,EAAE,OAAA,EAAS,aAAA,EAAe,aAAA,EAAe,YAAA,CAAa,CAAA,GAC1D,WAAW,MAAA,CAAO;gBACpB,OAAO;oBACL;oBACA;oBACA;oBACA;oBACA;gBACF;YACF,CAAC;YACD,kBAAkB,IAAA,CAAK,gBAAA;QACzB,CAAC;IACH;AACF;AAkEO,SAAS,aAId,MAAA,EACA,OAAA,EACgD;IAChD,OAAO,IAAI,iBAAiB,QAAQ,OAAO;AAC7C;AA2CA,MAAM,gBAAgB,aAAa;IACjC,sBAAsB,YAAY;QAChC,qKAAM,KAAA,CAAE,MAAA,CAAO;QACf,sKAAM,IAAA,CAAE,KAAA,iKAAM,IAAA,CAAE,GAAA,CAAI,CAAC;QACrB,+KAAe,IAAA,CAAE,OAAA,CAAQ;QACzB,+KAAe,IAAA,CAAE,QAAA,iKAAS,IAAA,CAAE,OAAA,CAAQ,CAAC;QACrC,uKAAO,IAAA,CAAE,KAAA,iKACP,IAAA,CAAE,MAAA,CAAO;YAAE,sKAAM,IAAA,CAAE,OAAA,CAAQ,SAAS;QAAE,CAAC,mKACvC,IAAA,CAAE,MAAA,CAAO;YAAE,sKAAM,IAAA,CAAE,OAAA,CAAQ,YAAY;QAAE,CAAC,mKAC1C,IAAA,CAAE,MAAA,CAAO;YAAE,sKAAM,IAAA,CAAE,OAAA,CAAQ,SAAS;QAAE,CAAC,mKACvC,IAAA,CAAE,MAAA,CAAO;YAAE,sKAAM,IAAA,CAAE,OAAA,CAAQ,QAAQ;YAAG,uKAAO,IAAA,CAAE,MAAA,CAAO;QAAE,CAAC,GACzD,oKAAA,CAAE,MAAA,CAAO;YAAE,sKAAM,IAAA,CAAE,OAAA,CAAQ,UAAU;QAAE,CAAC;IAE5C,CAAC;IACD,UAAU,YAAY;QACpB,wKAAQ,IAAA,CAAE,MAAA,CAAO;QACjB,sKAAM,IAAA,CAAE,OAAA,CAAQ;QAChB,6KAAa,IAAA,CAAE,QAAA,iKAAS,IAAA,CAAE,MAAA,CAAO,CAAC;IACpC,CAAC;AACH,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2478, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/convex/src/server/index.ts"], "sourcesContent": ["/**\n * Utilities for implementing server-side Convex query and mutation functions.\n *\n * ## Usage\n *\n * ### Code Generation\n *\n * This module is typically used alongside generated server code.\n *\n * To generate the server code, run `npx convex dev` in your Convex project.\n * This will create a `convex/_generated/server.js` file with the following\n * functions, typed for your schema:\n * - [query](https://docs.convex.dev/generated-api/server#query)\n * - [mutation](https://docs.convex.dev/generated-api/server#mutation)\n *\n * If you aren't using TypeScript and code generation, you can use these untyped\n * functions instead:\n * - {@link queryGeneric}\n * - {@link mutationGeneric}\n *\n * ### Example\n *\n * Convex functions are defined by using either the `query` or\n * `mutation` wrappers.\n *\n * Queries receive a `db` that implements the {@link GenericDatabaseReader} interface.\n *\n * ```js\n * import { query } from \"./_generated/server\";\n *\n * export default query({\n *   handler: async ({ db }, { arg1, arg2 }) => {\n *     // Your (read-only) code here!\n *   },\n * });\n * ```\n *\n * If your function needs to write to the database, such as inserting, updating,\n * or deleting documents, use `mutation` instead which provides a `db` that\n * implements the {@link GenericDatabaseWriter} interface.\n *\n * ```js\n * import { mutation } from \"./_generated/server\";\n *\n * export default mutation({\n *   handler: async ({ db }, { arg1, arg2 }) => {\n *     // Your mutation code here!\n *   },\n * });\n * ```\n * @module\n */\n\nexport type {\n  Auth,\n  UserIdentity,\n  UserIdentityAttributes,\n} from \"./authentication.js\";\nexport * from \"./database.js\";\nexport type {\n  GenericDocument,\n  GenericFieldPaths,\n  GenericIndexFields,\n  GenericTableIndexes,\n  GenericSearchIndexConfig,\n  GenericTableSearchIndexes,\n  GenericVectorIndexConfig,\n  GenericTableVectorIndexes,\n  FieldTypeFromFieldPath,\n  FieldTypeFromFieldPathInner,\n  GenericTableInfo,\n  DocumentByInfo,\n  FieldPaths,\n  Indexes,\n  IndexNames,\n  NamedIndex,\n  SearchIndexes,\n  SearchIndexNames,\n  NamedSearchIndex,\n  VectorIndexes,\n  VectorIndexNames,\n  NamedVectorIndex,\n  GenericDataModel,\n  AnyDataModel,\n  TableNamesInDataModel,\n  NamedTableInfo,\n  DocumentByName,\n} from \"./data_model.js\";\n\nexport type {\n  Expression,\n  ExpressionOrValue,\n  FilterBuilder,\n} from \"./filter_builder.js\";\nexport {\n  actionGeneric,\n  httpActionGeneric,\n  mutationGeneric,\n  queryGeneric,\n  internalActionGeneric,\n  internalMutationGeneric,\n  internalQueryGeneric,\n} from \"./impl/registration_impl.js\";\nexport type { IndexRange, IndexRangeBuilder } from \"./index_range_builder.js\";\nexport * from \"./pagination.js\";\nexport type { OrderedQuery, Query, QueryInitializer } from \"./query.js\";\nexport type {\n  ArgsArray,\n  DefaultFunctionArgs,\n  FunctionVisibility,\n  ActionBuilder,\n  MutationBuilder,\n  MutationBuilderWithTable,\n  QueryBuilder,\n  QueryBuilderWithTable,\n  HttpActionBuilder,\n  GenericActionCtx,\n  GenericMutationCtx,\n  GenericMutationCtxWithTable,\n  GenericQueryCtx,\n  GenericQueryCtxWithTable,\n  RegisteredAction,\n  RegisteredMutation,\n  RegisteredQuery,\n  PublicHttpAction,\n  UnvalidatedFunction,\n  ValidatedFunction,\n  ReturnValueForOptionalValidator,\n  ArgsArrayForOptionalValidator,\n  ArgsArrayToObject,\n  DefaultArgsForOptionalValidator,\n} from \"./registration.js\";\nexport * from \"./search_filter_builder.js\";\nexport * from \"./storage.js\";\nexport type { Scheduler, SchedulableFunctionReference } from \"./scheduler.js\";\nexport { cronJobs } from \"./cron.js\";\nexport type { CronJob, Crons } from \"./cron.js\";\nexport type {\n  SystemFields,\n  IdField,\n  WithoutSystemFields,\n  WithOptionalSystemFields,\n  SystemIndexes,\n  IndexTiebreakerField,\n} from \"./system_fields.js\";\nexport { httpRouter, HttpRouter, ROUTABLE_HTTP_METHODS } from \"./router.js\";\nexport type {\n  RoutableMethod,\n  RouteSpec,\n  RouteSpecWithPath,\n  RouteSpecWithPathPrefix,\n} from \"./router.js\";\nexport {\n  anyApi,\n  getFunctionName,\n  makeFunctionReference,\n  filterApi,\n} from \"./api.js\";\nexport type {\n  ApiFromModules,\n  AnyApi,\n  FilterApi,\n  FunctionType,\n  FunctionReference,\n  FunctionArgs,\n  OptionalRestArgs,\n  PartialApi,\n  ArgsAndOptions,\n  FunctionReturnType,\n} from \"./api.js\";\nexport {\n  defineApp,\n  defineComponent,\n  componentsGeneric,\n  createFunctionHandle,\n  type AnyChildComponents,\n} from \"./components/index.js\";\n/**\n * @internal\n */\nexport { currentSystemUdfInComponent } from \"./components/index.js\";\nexport { getFunctionAddress } from \"./components/index.js\";\nexport type {\n  ComponentDefinition,\n  AnyComponents,\n  FunctionHandle,\n} from \"./components/index.js\";\n\n/**\n * @internal\n */\nexport type { Index, SearchIndex, VectorIndex } from \"./schema.js\";\n\nexport type {\n  SearchIndexConfig,\n  VectorIndexConfig,\n  TableDefinition,\n  SchemaDefinition,\n  DefineSchemaOptions,\n  GenericSchema,\n  DataModelFromSchemaDefinition,\n  SystemDataModel,\n  SystemTableNames,\n} from \"./schema.js\";\nexport { defineTable, defineSchema } from \"./schema.js\";\n\nexport type {\n  VectorSearch,\n  VectorSearchQuery,\n  VectorFilterBuilder,\n  FilterExpression,\n} from \"./vector_search.js\";\n\n/**\n * @public\n */\nexport type { BetterOmit, Expand } from \"../type_utils.js\";\n"], "names": [], "mappings": ";AA0DA,cAAc;AAoCd;AAUA,cAAc;AA4Bd,cAAc;AACd,cAAc;AAEd,SAAS,gBAAgB;AAUzB,SAAS,YAAY,YAAY,6BAA6B;AAO9D;AAkBA;AAkCA,SAAS,aAAa,oBAAoB", "ignoreList": [0], "debugId": null}}]}