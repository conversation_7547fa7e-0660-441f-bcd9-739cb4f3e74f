/* [project]/node_modules/@blocknote/core/src/fonts/inter.css [app-client] (css) */
@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100;
  src: local(""), url("../media/inter-v12-latin-100.970d3d92.woff2") format("woff2"), url("../media/inter-v12-latin-100.eec6b976.woff") format("woff");
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 200;
  src: local(""), url("../media/inter-v12-latin-200.2b5503ed.woff2") format("woff2"), url("../media/inter-v12-latin-200.dae8ed2e.woff") format("woff");
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 300;
  src: local(""), url("../media/inter-v12-latin-300.2021a2b5.woff2") format("woff2"), url("../media/inter-v12-latin-300.8be92759.woff") format("woff");
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  src: local(""), url("../media/inter-v12-latin-regular.bd4c94fb.woff2") format("woff2"), url("../media/inter-v12-latin-regular.9c961eb3.woff") format("woff");
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 500;
  src: local(""), url("../media/inter-v12-latin-500.29876b16.woff2") format("woff2"), url("../media/inter-v12-latin-500.e2194a13.woff") format("woff");
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  src: local(""), url("../media/inter-v12-latin-600.ecaf3e73.woff2") format("woff2"), url("../media/inter-v12-latin-600.24759fca.woff") format("woff");
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 700;
  src: local(""), url("../media/inter-v12-latin-700.02b83c0d.woff2") format("woff2"), url("../media/inter-v12-latin-700.8c2cd9f1.woff") format("woff");
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 800;
  src: local(""), url("../media/inter-v12-latin-800.217cf3ff.woff2") format("woff2"), url("../media/inter-v12-latin-800.b2ea89a6.woff") format("woff");
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 900;
  src: local(""), url("../media/inter-v12-latin-900.5418055d.woff2") format("woff2"), url("../media/inter-v12-latin-900.662eac88.woff") format("woff");
}


/* [project]/node_modules/@blocknote/mantine/dist/style.css [app-client] (css) */
.bn-block-outer {
  line-height: 1.5;
  transition: margin .2s;
}

.bn-block {
  flex-direction: column;
  display: flex;
}

.bn-block-content {
  width: 100%;
  padding: 3px 0;
  transition: font-size .2s;
}

.bn-block-content:not(:has(.bn-inline-content)) {
  display: flex;
}

.bn-block-content:before {
  transition: all .2s;
}

.bn-block-content.ProseMirror-selectednode > *, .ProseMirror-selectednode > .bn-block-content > * {
  border-radius: 4px;
  outline: 4px solid #64a0ff;
}

.bn-inline-content {
  width: 100%;
}

.bn-block-group .bn-block-group {
  margin-left: 1.5em;
}

.bn-block-group .bn-block-group > .bn-block-outer {
  position: relative;
}

.bn-block-group .bn-block-group > .bn-block-outer:not([data-prev-depth-changed]):before {
  content: " ";
  height: 100%;
  transition: all .2s .1s;
  display: inline;
  position: absolute;
  left: -20px;
}

.bn-block-group .bn-block-group > .bn-block-outer[data-prev-depth-change="-2"]:before {
  height: 0;
}

.bn-inline-content code {
  font-family: monospace;
}

[data-prev-depth-change="1"] {
  --x: 1;
}

[data-prev-depth-change="2"] {
  --x: 2;
}

[data-prev-depth-change="3"] {
  --x: 3;
}

[data-prev-depth-change="4"] {
  --x: 4;
}

[data-prev-depth-change="5"] {
  --x: 5;
}

[data-prev-depth-change="-1"] {
  --x: -1;
}

[data-prev-depth-change="-2"] {
  --x: -2;
}

[data-prev-depth-change="-3"] {
  --x: -3;
}

[data-prev-depth-change="-4"] {
  --x: -4;
}

[data-prev-depth-change="-5"] {
  --x: -5;
}

.bn-block-outer[data-prev-depth-change] {
  margin-left: calc(10px * var(--x));
}

.bn-block-outer[data-prev-depth-change] .bn-block-outer[data-prev-depth-change] {
  margin-left: 0;
}

[data-content-type="heading"] {
  --level: 3em;
}

[data-content-type="heading"][data-level="2"] {
  --level: 2em;
}

[data-content-type="heading"][data-level="3"] {
  --level: 1.3em;
}

[data-prev-level="1"] {
  --prev-level: 3em;
}

[data-prev-level="2"] {
  --prev-level: 2em;
}

[data-prev-level="3"] {
  --prev-level: 1.3em;
}

.bn-block-outer[data-prev-type="heading"] > .bn-block > .bn-block-content {
  font-size: var(--prev-level);
  font-weight: 700;
}

.bn-block-outer:not([data-prev-type]) > .bn-block > .bn-block-content[data-content-type="heading"], .bn-block-outer:not([data-prev-type]) > .bn-block > div[data-type="modification"] > div[data-type="modification"] > .bn-block-content[data-content-type="heading"] {
  font-size: var(--level);
  font-weight: 700;
}

[data-content-type="quote"] blockquote {
  color: #7d797a;
  border-left: 2px solid #7d797a;
  margin: 0;
  padding-left: 1em;
}

.bn-block-content:before {
  content: "";
  margin-right: 0;
  display: inline;
}

.bn-block-content[data-content-type="numberedListItem"] {
  gap: .5em;
  display: flex;
}

[data-content-type="numberedListItem"] {
  --index: attr(data-index);
}

[data-prev-type="numberedListItem"] {
  --prev-index: attr(data-prev-index);
}

.bn-block-outer[data-prev-type="numberedListItem"]:not([data-prev-index="none"]) > .bn-block > .bn-block-content:before {
  content: var(--prev-index) ".";
}

.bn-block-outer:not([data-prev-type]) > .bn-block > .bn-block-content[data-content-type="numberedListItem"]:before, .bn-block-outer:not([data-prev-type]) > .bn-block > div[data-type="modification"] > .bn-block-content[data-content-type="numberedListItem"]:before {
  content: var(--index) ".";
}

.bn-block-content[data-content-type="bulletListItem"] {
  gap: .5em;
  display: flex;
}

.bn-block-content[data-content-type="checkListItem"] > div {
  width: 100%;
  display: flex;
}

.bn-block-content[data-content-type="checkListItem"] > div > div > input {
  margin: 0;
  cursor: pointer;
  margin-inline-end: .5em;
}

.bn-block-content[data-content-type="checkListItem"][data-checked="true"] .bn-inline-content {
  text-decoration: line-through;
}

.bn-block-content[data-text-alignment="center"] {
  justify-content: center;
}

.bn-block-content[data-text-alignment="right"] {
  justify-content: flex-end;
}

.bn-block-outer[data-prev-type="bulletListItem"] > .bn-block > .bn-block-content:before, .bn-block-outer:not([data-prev-type]) > .bn-block > .bn-block-content[data-content-type="bulletListItem"]:before, .bn-block-outer:not([data-prev-type]) > .bn-block > div[data-type="modification"] > .bn-block-content[data-content-type="bulletListItem"]:before {
  content: "•";
}

[data-content-type="bulletListItem"] ~ .bn-block-group > .bn-block-outer[data-prev-type="bulletListItem"] > .bn-block > .bn-block-content:before, [data-content-type="bulletListItem"] ~ .bn-block-group > .bn-block-outer:not([data-prev-type]) > .bn-block > .bn-block-content[data-content-type="bulletListItem"]:before, [data-content-type="bulletListItem"] ~ .bn-block-group > .bn-block-outer:not([data-prev-type]) > .bn-block > div[data-type="modification"] > .bn-block-content[data-content-type="bulletListItem"]:before {
  content: "◦";
}

[data-content-type="bulletListItem"] ~ .bn-block-group [data-content-type="bulletListItem"] ~ .bn-block-group > .bn-block-outer[data-prev-type="bulletListItem"] > .bn-block > .bn-block-content:before, [data-content-type="bulletListItem"] ~ .bn-block-group [data-content-type="bulletListItem"] ~ .bn-block-group > .bn-block-outer:not([data-prev-type]) > .bn-block > .bn-block-content[data-content-type="bulletListItem"]:before, [data-content-type="bulletListItem"] ~ .bn-block-group [data-content-type="bulletListItem"] ~ .bn-block-group > .bn-block-outer:not([data-prev-type]) > .bn-block > div[data-type="modification"] > .bn-block-content[data-content-type="bulletListItem"]:before {
  content: "▪";
}

.bn-block-content[data-content-type="codeBlock"] {
  color: #fff;
  background-color: #161616;
  border-radius: 8px;
  position: relative;
}

.bn-block-content[data-content-type="codeBlock"] > pre {
  white-space: pre;
  -moz-tab-size: 2;
  -o-tab-size: 2;
  tab-size: 2;
  width: 100%;
  margin: 0;
  padding: 24px;
  overflow-x: auto;
}

.bn-block-content[data-content-type="codeBlock"] > div {
  outline: none !important;
}

.bn-block-content[data-content-type="codeBlock"] > div > select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  cursor: pointer;
  color: #fff;
  opacity: 0;
  background-color: #0000;
  border: none;
  font-size: .8em;
  transition: opacity .3s 1s;
  position: absolute;
  top: 8px;
  left: 18px;
  outline: none !important;
}

.bn-block-content[data-content-type="codeBlock"] > div > select > option {
  color: #000;
}

.bn-block-content[data-content-type="codeBlock"]:hover > div > select, .bn-block-content[data-content-type="codeBlock"] > div > select:focus {
  opacity: .5;
  transition-delay: .1s;
}

.bn-block-content[data-content-type="pageBreak"] > div {
  border-top: 2px dotted #7d797a;
  width: 100%;
  height: 0;
  margin-block: 11px;
}

@media print {
  .bn-block-content[data-content-type="pageBreak"] > div {
    page-break-after: always;
  }
}

[data-file-block] .bn-file-block-content-wrapper {
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  flex-direction: column;
  display: flex;
}

[data-file-block] .bn-file-block-content-wrapper:has(.bn-add-file-button), [data-file-block] .bn-file-block-content-wrapper:has(.bn-file-name-with-icon) {
  width: 100%;
}

[data-file-block] .bn-add-file-button {
  color: #7d797a;
  background-color: #f2f1ee;
  border-radius: 4px;
  align-items: center;
  gap: 10px;
  padding: 12px;
  display: flex;
}

.bn-editor[contenteditable="true"] [data-file-block] .bn-add-file-button:hover, [data-file-block] .bn-file-name-with-icon:hover, .ProseMirror-selectednode .bn-file-name-with-icon {
  background-color: #e1e1e1;
}

[data-file-block] .bn-add-file-button-icon, [data-file-block] .bn-file-icon {
  width: 24px;
  height: 24px;
}

[data-file-block] .bn-add-file-button-text {
  font-size: .9rem;
}

[data-file-block] .bn-file-name-with-icon {
  border-radius: 4px;
  gap: 4px;
  padding: 4px;
  display: flex;
}

[data-file-block] .bn-file-caption {
  word-break: break-word;
  padding-block: 4px;
  font-size: .8em;
}

[data-file-block] .bn-file-caption:empty {
  padding-block: 0;
}

[data-file-block] .bn-resize-handle {
  cursor: ew-resize;
  background-color: #000;
  border: 1px solid #fff;
  border-radius: 4px;
  width: 8px;
  height: 30px;
  position: absolute;
}

[data-file-block] .bn-visual-media-wrapper {
  align-items: center;
  max-width: 100%;
  display: flex;
  position: relative;
}

[data-file-block] .bn-visual-media {
  border-radius: 4px;
  width: 100%;
}

[data-content-type="audio"] > .bn-file-block-content-wrapper, .bn-audio {
  width: 100%;
}

.bn-inline-content:has( > .ProseMirror-trailingBreak:only-child):before {
  pointer-events: none;
  height: 0;
  font-style: italic;
  position: absolute;
}

[data-text-alignment="left"] {
  text-align: left !important;
  justify-content: flex-start !important;
}

[data-text-alignment="center"] {
  text-align: center !important;
  justify-content: center !important;
}

[data-text-alignment="right"] {
  text-align: right !important;
  justify-content: flex-end !important;
}

[data-text-alignment="justify"] {
  text-align: justify !important;
  justify-content: flex-start !important;
}

.bn-block-column-list {
  flex-direction: row;
  display: flex;
}

.bn-block-column {
  flex: 1;
  padding: 12px 20px;
  overflow-x: auto;
}

.bn-block-column:first-child {
  padding-left: 0;
}

.bn-block-column:last-child {
  padding-right: 0;
}

.bn-thread-mark:not([data-orphan="true"]) {
  background: #ffc80026;
}

.bn-thread-mark .bn-thread-mark-selected {
  background: #ffc80040;
}

.ProseMirror .tableWrapper {
  overflow-x: auto;
}

.ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  overflow: hidden;
}

.ProseMirror td, .ProseMirror th {
  vertical-align: top;
  box-sizing: border-box;
  position: relative;
}

.ProseMirror td:not([data-colwidth]):not(.column-resize-dragging), .ProseMirror th:not([data-colwidth]):not(.column-resize-dragging) {
  min-width: var(--default-cell-min-width);
}

.ProseMirror .column-resize-handle {
  z-index: 20;
  pointer-events: none;
  background-color: #adf;
  width: 4px;
  position: absolute;
  top: 0;
  bottom: 0;
  right: -2px;
}

.ProseMirror.resize-cursor {
  cursor: ew-resize;
  cursor: col-resize;
}

.ProseMirror .selectedCell:after {
  z-index: 2;
  content: "";
  pointer-events: none;
  background: #c8c8ff66;
  position: absolute;
  inset: 0;
}

.bn-editor {
  --N800: #172b4d;
  --N40: #dfe1e6;
  outline: none;
  padding-inline: 54px;
}

.bn-comment-editor {
  width: 100%;
  padding: 0;
}

.bn-comment-editor .bn-editor {
  padding: 0;
}

.bn-root {
  box-sizing: border-box;
}

.bn-root *, .bn-root :before, .bn-root :after {
  box-sizing: inherit;
}

.bn-default-styles p, .bn-default-styles h1, .bn-default-styles h2, .bn-default-styles h3, .bn-default-styles li {
  font-size: inherit;
  margin: 0;
  padding: 0;
  min-width: 2px !important;
}

.bn-default-styles {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: Inter, SF Pro Display, -apple-system, BlinkMacSystemFont, Open Sans, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
  font-size: 16px;
  font-weight: 400;
}

.bn-table-drop-cursor {
  z-index: 20;
  pointer-events: none;
  background-color: #adf;
  position: absolute;
}

.bn-drag-preview {
  opacity: .001;
  padding: 10px;
  position: absolute;
  top: 0;
  left: 0;
}

.bn-editor .bn-collaboration-cursor__base {
  position: relative;
}

.bn-editor .bn-collaboration-cursor__base .bn-collaboration-cursor__caret {
  width: 2px;
  position: absolute;
  top: 1px;
  bottom: -2px;
  left: -1px;
}

.bn-editor .bn-collaboration-cursor__base .bn-collaboration-cursor__label {
  pointer-events: none;
  white-space: nowrap;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  color: #0000;
  border-radius: 0 1.5px 1.5px 0;
  max-width: 4px;
  max-height: 5px;
  padding: 0;
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  transition: all .2s;
  position: absolute;
  top: -1px;
  left: 0;
  overflow: hidden;
}

.bn-editor .bn-collaboration-cursor__base[data-active] .bn-collaboration-cursor__label {
  color: #0d0d0d;
  border-radius: 3px 3px 3px 0;
  max-width: 20rem;
  max-height: 1.1rem;
  padding: .1rem .3rem;
  transition: all .2s;
  top: -17px;
  left: 0;
}

.bn-editor [data-content-type="table"] .tableWrapper {
  min-width: calc(100% + 16px);
  padding-bottom: 16px;
  position: relative;
  top: -16px;
  left: -16px;
  overflow-y: hidden;
}

.bn-editor [data-content-type="table"] .tableWrapper-inner {
  padding: 16px;
}

.bn-editor [data-content-type="table"] table {
  word-break: break-word;
  width: auto !important;
}

.bn-editor [data-content-type="table"] th, .bn-editor [data-content-type="table"] td {
  border: 1px solid #ddd;
  padding: 5px 10px;
}

.bn-editor [data-content-type="table"] th {
  text-align: left;
  font-weight: 700;
}

.ProseMirror td, .ProseMirror th {
  min-width: auto !important;
}

.ProseMirror td:not([colwidth]):not(.column-resize-dragging), .ProseMirror th:not([colwidth]):not(.column-resize-dragging) {
  min-width: var(--default-cell-min-width) !important;
}

.prosemirror-dropcursor-block {
  transition-property: top, bottom;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.prosemirror-dropcursor-vertical {
  transition-property: left, right;
}

[data-show-selection] {
  background-color: highlight;
  padding: 2px 0;
}

.bn-container {
  --bn-colors-editor-text: #3f3f3f;
  --bn-colors-editor-background: #fff;
  --bn-colors-menu-text: #3f3f3f;
  --bn-colors-menu-background: #fff;
  --bn-colors-tooltip-text: #3f3f3f;
  --bn-colors-tooltip-background: #efefef;
  --bn-colors-hovered-text: #3f3f3f;
  --bn-colors-hovered-background: #efefef;
  --bn-colors-selected-text: #fff;
  --bn-colors-selected-background: #3f3f3f;
  --bn-colors-disabled-text: #afafaf;
  --bn-colors-disabled-background: #efefef;
  --bn-colors-shadow: #cfcfcf;
  --bn-colors-border: #efefef;
  --bn-colors-side-menu: #cfcfcf;
  --bn-colors-highlights-gray-text: #9b9a97;
  --bn-colors-highlights-gray-background: #ebeced;
  --bn-colors-highlights-brown-text: #64473a;
  --bn-colors-highlights-brown-background: #e9e5e3;
  --bn-colors-highlights-red-text: #e03e3e;
  --bn-colors-highlights-red-background: #fbe4e4;
  --bn-colors-highlights-orange-text: #d9730d;
  --bn-colors-highlights-orange-background: #f6e9d9;
  --bn-colors-highlights-yellow-text: #dfab01;
  --bn-colors-highlights-yellow-background: #fbf3db;
  --bn-colors-highlights-green-text: #4d6461;
  --bn-colors-highlights-green-background: #ddedea;
  --bn-colors-highlights-blue-text: #0b6e99;
  --bn-colors-highlights-blue-background: #ddebf1;
  --bn-colors-highlights-purple-text: #6940a5;
  --bn-colors-highlights-purple-background: #eae4f2;
  --bn-colors-highlights-pink-text: #ad1a72;
  --bn-colors-highlights-pink-background: #f4dfeb;
  --bn-font-family: "Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, "Open Sans", "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  --bn-border-radius: 6px;
  --bn-shadow-medium: 0 4px 12px var(--bn-colors-shadow);
  --bn-shadow-light: 0 2px 6px var(--bn-colors-border);
  --bn-border: 1px solid var(--bn-colors-border);
  --bn-border-radius-small: max(var(--bn-border-radius)  - 2px, 1px);
  --bn-border-radius-medium: var(--bn-border-radius);
  --bn-border-radius-large: max(var(--bn-border-radius)  + 2px, 1px);
}

.bn-container[data-color-scheme="dark"] {
  --bn-colors-editor-text: #cfcfcf;
  --bn-colors-editor-background: #1f1f1f;
  --bn-colors-menu-text: #cfcfcf;
  --bn-colors-menu-background: #1f1f1f;
  --bn-colors-tooltip-text: #cfcfcf;
  --bn-colors-tooltip-background: #161616;
  --bn-colors-hovered-text: #cfcfcf;
  --bn-colors-hovered-background: #161616;
  --bn-colors-selected-text: #cfcfcf;
  --bn-colors-selected-background: #0f0f0f;
  --bn-colors-disabled-text: #3f3f3f;
  --bn-colors-disabled-background: #161616;
  --bn-colors-shadow: #0f0f0f;
  --bn-colors-border: #161616;
  --bn-colors-side-menu: #7f7f7f;
  --bn-colors-highlights-gray-text: #bebdb8;
  --bn-colors-highlights-gray-background: #9b9a97;
  --bn-colors-highlights-brown-text: #8e6552;
  --bn-colors-highlights-brown-background: #64473a;
  --bn-colors-highlights-red-text: #ec4040;
  --bn-colors-highlights-red-background: #be3434;
  --bn-colors-highlights-orange-text: #e3790d;
  --bn-colors-highlights-orange-background: #b7600a;
  --bn-colors-highlights-yellow-text: #dfab01;
  --bn-colors-highlights-yellow-background: #b58b00;
  --bn-colors-highlights-green-text: #6b8b87;
  --bn-colors-highlights-green-background: #4d6461;
  --bn-colors-highlights-blue-text: #0e87bc;
  --bn-colors-highlights-blue-background: #0b6e99;
  --bn-colors-highlights-purple-text: #8552d7;
  --bn-colors-highlights-purple-background: #6940a5;
  --bn-colors-highlights-pink-text: #da208f;
  --bn-colors-highlights-pink-background: #ad1a72;
}

.bn-container {
  font-family: var(--bn-font-family);
}

.bn-editor {
  background-color: var(--bn-colors-editor-background);
  border-radius: var(--bn-border-radius-large);
  color: var(--bn-colors-editor-text);
}

.bn-react-node-view-renderer {
  flex-direction: column;
  width: 100%;
  display: flex;
}

.bn-block-group .bn-block-group .bn-block-outer:not([data-prev-depth-changed]):before {
  border-left: 1px solid var(--bn-colors-side-menu);
}

.bn-inline-content:has( > .ProseMirror-trailingBreak):before {
  color: var(--bn-colors-side-menu);
}

.bn-container .bn-color-icon {
  border: var(--bn-border);
  border-radius: var(--bn-border-radius-small);
  justify-content: center;
  align-items: center;
  display: flex;
}

.bn-error-text {
  color: red;
  font-size: 12px;
}

[data-text-color="gray"] {
  color: var(--bn-colors-highlights-gray-text);
}

[data-text-color="brown"] {
  color: var(--bn-colors-highlights-brown-text);
}

[data-text-color="red"] {
  color: var(--bn-colors-highlights-red-text);
}

[data-text-color="orange"] {
  color: var(--bn-colors-highlights-orange-text);
}

[data-text-color="yellow"] {
  color: var(--bn-colors-highlights-yellow-text);
}

[data-text-color="green"] {
  color: var(--bn-colors-highlights-green-text);
}

[data-text-color="blue"] {
  color: var(--bn-colors-highlights-blue-text);
}

[data-text-color="purple"] {
  color: var(--bn-colors-highlights-purple-text);
}

[data-text-color="pink"] {
  color: var(--bn-colors-highlights-pink-text);
}

[data-background-color="gray"] {
  background-color: var(--bn-colors-highlights-gray-background);
}

[data-background-color="brown"] {
  background-color: var(--bn-colors-highlights-brown-background);
}

[data-background-color="red"] {
  background-color: var(--bn-colors-highlights-red-background);
}

[data-background-color="orange"] {
  background-color: var(--bn-colors-highlights-orange-background);
}

[data-background-color="yellow"] {
  background-color: var(--bn-colors-highlights-yellow-background);
}

[data-background-color="green"] {
  background-color: var(--bn-colors-highlights-green-background);
}

[data-background-color="blue"] {
  background-color: var(--bn-colors-highlights-blue-background);
}

[data-background-color="purple"] {
  background-color: var(--bn-colors-highlights-purple-background);
}

[data-background-color="pink"] {
  background-color: var(--bn-colors-highlights-pink-background);
}

.bn-side-menu {
  height: 30px;
}

.bn-side-menu[data-block-type="heading"][data-level="1"] {
  height: 78px;
}

.bn-side-menu[data-block-type="heading"][data-level="2"] {
  height: 54px;
}

.bn-side-menu[data-block-type="heading"][data-level="3"] {
  height: 37px;
}

.bn-side-menu[data-block-type="file"] {
  height: 38px;
}

.bn-side-menu[data-block-type="audio"] {
  height: 60px;
}

.bn-side-menu[data-url="false"] {
  height: 54px;
}

.bn-threads-sidebar {
  border-radius: var(--bn-border-radius-medium);
  flex-direction: column;
  gap: 10px;
  display: flex;
  overflow: auto;
}

.bn-thread-expand-prompt .mantine-Text-root, .bn-thread .bn-header-text {
  color: var(--bn-colors-menu-text);
}

.bn-threads-sidebar .bn-thread .bn-editor {
  background-color: #0000;
}

.bn-threads-sidebar .bn-thread.selected {
  background-color: #f5f9fd;
  border: 2px solid #c2dcf8;
}

.dark .bn-threads-sidebar .bn-thread.selected {
  background-color: #20242a;
  border: 2px solid #23405b;
}

.m_d57069b5 {
  --scrollarea-scrollbar-size: 12px;
  position: relative;
  overflow: hidden;
}

.m_c0783ff9 {
  scrollbar-width: none;
  overscroll-behavior: var(--scrollarea-over-scroll-behavior);
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
  width: 100%;
  height: 100%;
}

.m_c0783ff9::-webkit-scrollbar {
  display: none;
}

.m_c0783ff9:where([data-scrollbars="xy"], [data-scrollbars="y"]):where([data-offset-scrollbars="xy"], [data-offset-scrollbars="y"], [data-offset-scrollbars="present"]):where([data-vertical-hidden]) {
  padding-inline: 0;
}

.m_c0783ff9:where([data-scrollbars="xy"], [data-scrollbars="y"]):where([data-offset-scrollbars="xy"], [data-offset-scrollbars="y"], [data-offset-scrollbars="present"]):not([data-vertical-hidden]) {
  padding-inline-start: unset;
  padding-inline-end: var(--scrollarea-scrollbar-size);
}

.m_c0783ff9:where([data-scrollbars="xy"], [data-scrollbars="x"]):where([data-offset-scrollbars="xy"], [data-offset-scrollbars="x"], [data-offset-scrollbars="present"]):where([data-horizontal-hidden]) {
  padding-bottom: 0;
}

.m_c0783ff9:where([data-scrollbars="xy"], [data-scrollbars="x"]):where([data-offset-scrollbars="xy"], [data-offset-scrollbars="x"], [data-offset-scrollbars="present"]):not([data-horizontal-hidden]) {
  padding-bottom: var(--scrollarea-scrollbar-size);
}

.m_f8f631dd {
  min-width: 100%;
  display: table;
}

.m_c44ba933 {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  touch-action: none;
  box-sizing: border-box;
  padding: calc(var(--scrollarea-scrollbar-size) / 5);
  background-color: #0000;
  flex-direction: row;
  transition: background-color .15s, opacity .15s;
  display: flex;
}

@media (hover: hover) {
  :where([data-mantine-color-scheme="light"]) .m_c44ba933:hover {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="light"]) .m_c44ba933:hover > .m_d8b5e363 {
    background-color: #00000080;
  }

  :where([data-mantine-color-scheme="dark"]) .m_c44ba933:hover {
    background-color: var(--mantine-color-dark-8);
  }

  :where([data-mantine-color-scheme="dark"]) .m_c44ba933:hover > .m_d8b5e363 {
    background-color: #ffffff80;
  }
}

@media (hover: none) {
  :where([data-mantine-color-scheme="light"]) .m_c44ba933:active {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="light"]) .m_c44ba933:active > .m_d8b5e363 {
    background-color: #00000080;
  }

  :where([data-mantine-color-scheme="dark"]) .m_c44ba933:active {
    background-color: var(--mantine-color-dark-8);
  }

  :where([data-mantine-color-scheme="dark"]) .m_c44ba933:active > .m_d8b5e363 {
    background-color: #ffffff80;
  }
}

.m_c44ba933:where([data-hidden], [data-state="hidden"]) {
  display: none;
}

.m_c44ba933:where([data-orientation="vertical"]) {
  width: var(--scrollarea-scrollbar-size);
  top: 0;
  bottom: var(--sa-corner-width);
  inset-inline-end: 0;
}

.m_c44ba933:where([data-orientation="horizontal"]) {
  height: var(--scrollarea-scrollbar-size);
  bottom: 0;
  flex-direction: column;
  inset-inline-start: 0;
  inset-inline-end: var(--sa-corner-width);
}

.m_d8b5e363 {
  border-radius: var(--scrollarea-scrollbar-size);
  opacity: var(--thumb-opacity);
  flex: 1;
  transition: background-color .15s;
  position: relative;
  overflow: hidden;
}

.m_d8b5e363:before {
  content: "";
  width: 100%;
  min-width: 44px;
  height: 100%;
  min-height: 44px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

:where([data-mantine-color-scheme="light"]) .m_d8b5e363 {
  background-color: #0006;
}

:where([data-mantine-color-scheme="dark"]) .m_d8b5e363 {
  background-color: #fff6;
}

.m_21657268 {
  opacity: 0;
  inset-inline-end: 0;
  transition: opacity .15s;
  display: block;
  position: absolute;
  bottom: 0;
}

:where([data-mantine-color-scheme="light"]) .m_21657268 {
  background-color: var(--mantine-color-gray-0);
}

:where([data-mantine-color-scheme="dark"]) .m_21657268 {
  background-color: var(--mantine-color-dark-8);
}

.m_21657268:where([data-hovered]) {
  opacity: 1;
}

.m_21657268:where([data-hidden]) {
  display: none;
}

.m_87cf2631 {
  cursor: pointer;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  font-size: var(--mantine-font-size-md);
  text-align: left;
  color: inherit;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  background-color: #0000;
  border: 0;
  padding: 0;
  text-decoration: none;
}

:where([dir="rtl"]) .m_87cf2631 {
  text-align: right;
}

.m_515a97f8 {
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  position: absolute;
  overflow: hidden;
}

.m_1b7284a3 {
  --paper-radius: var(--mantine-radius-default);
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  border-radius: var(--paper-radius);
  box-shadow: var(--paper-shadow);
  background-color: var(--mantine-color-body);
  outline: 0;
  text-decoration: none;
  display: block;
}

:where([data-mantine-color-scheme="light"]) .m_1b7284a3:where([data-with-border]) {
  border: calc(.0625rem * var(--mantine-scale)) solid var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme="dark"]) .m_1b7284a3:where([data-with-border]) {
  border: calc(.0625rem * var(--mantine-scale)) solid var(--mantine-color-dark-4);
}

.m_38a85659 {
  border: 1px solid var(--popover-border-color);
  padding: var(--mantine-spacing-sm) var(--mantine-spacing-md);
  box-shadow: var(--popover-shadow, none);
  border-radius: var(--popover-radius, var(--mantine-radius-default));
  position: absolute;
}

.m_38a85659:where([data-fixed]) {
  position: fixed;
}

.m_38a85659:focus {
  outline: none;
}

:where([data-mantine-color-scheme="light"]) .m_38a85659 {
  --popover-border-color: var(--mantine-color-gray-2);
  background-color: var(--mantine-color-white);
}

:where([data-mantine-color-scheme="dark"]) .m_38a85659 {
  --popover-border-color: var(--mantine-color-dark-4);
  background-color: var(--mantine-color-dark-6);
}

.m_a31dc6c1 {
  background-color: inherit;
  border: 1px solid var(--popover-border-color);
  z-index: 1;
}

.m_3d7bc908 {
  position: fixed;
  inset: 0;
}

.m_86a44da5 {
  --cb-size-xs: 18px;
  --cb-size-sm: 22px;
  --cb-size-md: 28px;
  --cb-size-lg: 34px;
  --cb-size-xl: 44px;
  --cb-size: var(--cb-size-md);
  --cb-icon-size: 70%;
  --cb-radius: var(--mantine-radius-default);
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  width: var(--cb-size);
  height: var(--cb-size);
  min-width: var(--cb-size);
  min-height: var(--cb-size);
  border-radius: var(--cb-radius);
  justify-content: center;
  align-items: center;
  line-height: 1;
  display: inline-flex;
  position: relative;
}

:where([data-mantine-color-scheme="light"]) .m_86a44da5 {
  color: var(--mantine-color-gray-7);
}

:where([data-mantine-color-scheme="dark"]) .m_86a44da5 {
  color: var(--mantine-color-dark-1);
}

.m_86a44da5[data-disabled], .m_86a44da5:disabled {
  cursor: not-allowed;
  opacity: .6;
}

@media (hover: hover) {
  :where([data-mantine-color-scheme="light"]) .m_220c80f2:where(:not([data-disabled], :disabled)):hover {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="dark"]) .m_220c80f2:where(:not([data-disabled], :disabled)):hover {
    background-color: var(--mantine-color-dark-6);
  }
}

@media (hover: none) {
  :where([data-mantine-color-scheme="light"]) .m_220c80f2:where(:not([data-disabled], :disabled)):active {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="dark"]) .m_220c80f2:where(:not([data-disabled], :disabled)):active {
    background-color: var(--mantine-color-dark-6);
  }
}

.m_4081bf90 {
  flex-direction: row;
  flex-wrap: var(--group-wrap, wrap);
  justify-content: var(--group-justify, flex-start);
  align-items: var(--group-align, center);
  gap: var(--group-gap, var(--mantine-spacing-md));
  display: flex;
}

.m_4081bf90:where([data-grow]) > * {
  max-width: var(--group-child-width);
  flex-grow: 1;
}

.m_5ae2e3c {
  --loader-size-xs: 18px;
  --loader-size-sm: 22px;
  --loader-size-md: 36px;
  --loader-size-lg: 44px;
  --loader-size-xl: 58px;
  --loader-size: var(--loader-size-md);
  --loader-color: var(--mantine-primary-color-filled);
}

@keyframes m_5d2b3b9d {
  0% {
    opacity: 0;
    transform: scale(.6);
  }

  50%, to {
    transform: scale(1);
  }
}

.m_7a2bd4cd {
  width: var(--loader-size);
  height: var(--loader-size);
  gap: calc(var(--loader-size) / 5);
  display: flex;
  position: relative;
}

.m_870bb79 {
  background: var(--loader-color);
  border-radius: 2px;
  flex: 1;
  animation: 1.2s cubic-bezier(0, .5, .5, 1) infinite m_5d2b3b9d;
}

.m_870bb79:first-of-type {
  animation-delay: -.24s;
}

.m_870bb79:nth-of-type(2) {
  animation-delay: -.12s;
}

.m_870bb79:nth-of-type(3) {
  animation-delay: 0;
}

@keyframes m_aac34a1 {
  0%, to {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: .5;
    transform: scale(.6);
  }
}

.m_4e3f22d7 {
  justify-content: center;
  align-items: center;
  gap: calc(var(--loader-size) / 10);
  width: var(--loader-size);
  height: var(--loader-size);
  display: flex;
  position: relative;
}

.m_870c4af {
  width: calc(var(--loader-size) / 3 - var(--loader-size) / 15);
  height: calc(var(--loader-size) / 3 - var(--loader-size) / 15);
  background: var(--loader-color);
  border-radius: 50%;
  animation: .8s linear infinite m_aac34a1;
}

.m_870c4af:nth-child(2) {
  animation-delay: .4s;
}

@keyframes m_f8e89c4b {
  0% {
    transform: rotate(0);
  }

  to {
    transform: rotate(360deg);
  }
}

.m_b34414df {
  width: var(--loader-size);
  height: var(--loader-size);
  display: inline-block;
}

.m_b34414df:after {
  content: "";
  width: var(--loader-size);
  height: var(--loader-size);
  border-width: calc(var(--loader-size) / 8);
  border-style: solid;
  border-color: var(--loader-color) var(--loader-color) var(--loader-color) transparent;
  border-radius: 10000px;
  animation: 1.2s linear infinite m_f8e89c4b;
  display: block;
}

.m_9814e45f {
  background: var(--overlay-bg, #0009);
  -webkit-backdrop-filter: var(--overlay-filter);
  border-radius: var(--overlay-radius, 0);
  z-index: var(--overlay-z-index);
  position: absolute;
  inset: 0;
}

.m_9814e45f:where([data-fixed]) {
  position: fixed;
}

.m_9814e45f:where([data-center]) {
  justify-content: center;
  align-items: center;
  display: flex;
}

.m_615af6c9 {
  font-weight: 400;
  line-height: 1;
  font-size: var(--mantine-font-size-md);
  margin: 0;
  padding: 0;
}

.m_b5489c3c {
  padding: var(--mb-padding, var(--mantine-spacing-md));
  background-color: var(--mantine-color-body);
  z-index: 1000;
  justify-content: space-between;
  align-items: center;
  min-height: 60px;
  padding-inline-end: calc(var(--mb-padding, var(--mantine-spacing-md))  - calc(.3125rem * var(--mantine-scale)));
  transition: padding-inline-end .1s;
  display: flex;
  position: sticky;
  top: 0;
}

.m_60c222c7 {
  width: 100%;
  z-index: var(--mb-z-index);
  pointer-events: none;
  position: fixed;
  top: 0;
  bottom: 0;
}

.m_fd1ab0aa {
  pointer-events: all;
  box-shadow: var(--mb-shadow, var(--mantine-shadow-xl));
}

.m_fd1ab0aa [data-mantine-scrollbar] {
  z-index: 1001;
}

.m_fd1ab0aa:has([data-mantine-scrollbar][data-state="visible"]) .m_b5489c3c {
  padding-inline-end: calc(var(--mb-padding, var(--mantine-spacing-md))  + calc(.3125rem * var(--mantine-scale)));
}

.m_606cb269 {
  margin-inline-start: auto;
}

.m_5df29311 {
  padding: var(--mb-padding, var(--mantine-spacing-md));
  padding-top: var(--mb-padding, var(--mantine-spacing-md));
}

.m_5df29311:where(:not(:only-child)) {
  padding-top: 0;
}

.m_6c018570 {
  margin-top: var(--input-margin-top, 0rem);
  margin-bottom: var(--input-margin-bottom, 0rem);
  --input-height-xs: 30px;
  --input-height-sm: 36px;
  --input-height-md: 42px;
  --input-height-lg: 50px;
  --input-height-xl: 60px;
  --input-padding-y-xs: 5px;
  --input-padding-y-sm: 6px;
  --input-padding-y-md: 8px;
  --input-padding-y-lg: 10px;
  --input-padding-y-xl: 13px;
  --input-height: var(--input-height-sm);
  --input-radius: var(--mantine-radius-default);
  --input-cursor: text;
  --input-text-align: left;
  --input-line-height: calc(var(--input-height)  - calc(.125rem * var(--mantine-scale)));
  --input-padding: calc(var(--input-height) / 3);
  --input-padding-inline-start: var(--input-padding);
  --input-padding-inline-end: var(--input-padding);
  --input-placeholder-color: var(--mantine-color-placeholder);
  --input-color: var(--mantine-color-text);
  --input-left-section-size: var(--input-left-section-width, calc(var(--input-height)  - calc(.125rem * var(--mantine-scale))));
  --input-right-section-size: var(--input-right-section-width, calc(var(--input-height)  - calc(.125rem * var(--mantine-scale))));
  --input-size: var(--input-height);
  --section-y: 1px;
  --left-section-start: 1px;
  --left-section-border-radius: var(--input-radius) 0 0 var(--input-radius);
  --right-section-end: 1px;
  --right-section-border-radius: 0 var(--input-radius) var(--input-radius) 0;
  position: relative;
}

.m_6c018570[data-variant="unstyled"] {
  --input-padding: 0;
  --input-padding-y: 0;
  --input-padding-inline-start: 0;
  --input-padding-inline-end: 0;
}

.m_6c018570[data-pointer] {
  --input-cursor: pointer;
}

.m_6c018570[data-multiline] {
  --input-padding-y-xs: 4.5px;
  --input-padding-y-sm: 5.5px;
  --input-padding-y-md: 7px;
  --input-padding-y-lg: 9.5px;
  --input-padding-y-xl: 13px;
  --input-size: auto;
  --input-line-height: var(--mantine-line-height);
  --input-padding-y: var(--input-padding-y-sm);
}

.m_6c018570[data-with-left-section] {
  --input-padding-inline-start: var(--input-left-section-size);
}

.m_6c018570[data-with-right-section] {
  --input-padding-inline-end: var(--input-right-section-size);
}

[data-mantine-color-scheme="light"] .m_6c018570 {
  --input-disabled-bg: var(--mantine-color-gray-1);
  --input-disabled-color: var(--mantine-color-gray-6);
}

[data-mantine-color-scheme="light"] .m_6c018570[data-variant="default"] {
  --input-bd: var(--mantine-color-gray-4);
  --input-bg: var(--mantine-color-white);
  --input-bd-focus: var(--mantine-primary-color-filled);
}

[data-mantine-color-scheme="light"] .m_6c018570[data-variant="filled"] {
  --input-bd: transparent;
  --input-bg: var(--mantine-color-gray-1);
  --input-bd-focus: var(--mantine-primary-color-filled);
}

[data-mantine-color-scheme="light"] .m_6c018570[data-variant="unstyled"] {
  --input-bd: transparent;
  --input-bg: transparent;
  --input-bd-focus: transparent;
}

[data-mantine-color-scheme="dark"] .m_6c018570 {
  --input-disabled-bg: var(--mantine-color-dark-6);
  --input-disabled-color: var(--mantine-color-dark-2);
}

[data-mantine-color-scheme="dark"] .m_6c018570[data-variant="default"] {
  --input-bd: var(--mantine-color-dark-4);
  --input-bg: var(--mantine-color-dark-6);
  --input-bd-focus: var(--mantine-primary-color-filled);
}

[data-mantine-color-scheme="dark"] .m_6c018570[data-variant="filled"] {
  --input-bd: transparent;
  --input-bg: var(--mantine-color-dark-5);
  --input-bd-focus: var(--mantine-primary-color-filled);
}

[data-mantine-color-scheme="dark"] .m_6c018570[data-variant="unstyled"] {
  --input-bd: transparent;
  --input-bg: transparent;
  --input-bd-focus: transparent;
}

[data-mantine-color-scheme] .m_6c018570[data-error]:not([data-variant="unstyled"]) {
  --input-bd: var(--mantine-color-error);
}

[data-mantine-color-scheme] .m_6c018570[data-error] {
  --input-color: var(--mantine-color-error);
  --input-placeholder-color: var(--mantine-color-error);
  --input-section-color: var(--mantine-color-error);
}

:where([dir="rtl"]) .m_6c018570 {
  --input-text-align: right;
  --left-section-border-radius: 0 var(--input-radius) var(--input-radius) 0;
  --right-section-border-radius: var(--input-radius) 0 0 var(--input-radius);
}

.m_8fb7ebe7 {
  -webkit-tap-highlight-color: transparent;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  resize: var(--input-resize, none);
  width: 100%;
  text-align: var(--input-text-align);
  color: var(--input-color);
  border: calc(.0625rem * var(--mantine-scale)) solid var(--input-bd);
  background-color: var(--input-bg);
  font-family: var(--input-font-family, var(--mantine-font-family));
  height: var(--input-size);
  min-height: var(--input-height);
  line-height: var(--input-line-height);
  font-size: var(--input-fz, var(--input-fz, var(--mantine-font-size-sm)));
  border-radius: var(--input-radius);
  padding-inline-start: var(--input-padding-inline-start);
  padding-inline-end: var(--input-padding-inline-end);
  padding-top: var(--input-padding-y, 0rem);
  padding-bottom: var(--input-padding-y, 0rem);
  cursor: var(--input-cursor);
  overflow: var(--input-overflow);
  transition: border-color .1s;
  display: block;
}

.m_8fb7ebe7[data-no-overflow] {
  --input-overflow: hidden;
}

.m_8fb7ebe7[data-monospace] {
  --input-font-family: var(--mantine-font-family-monospace);
  --input-fz: calc(var(--input-fz, var(--mantine-font-size-sm))  - calc(.125rem * var(--mantine-scale)));
}

.m_8fb7ebe7:focus, .m_8fb7ebe7:focus-within {
  --input-bd: var(--input-bd-focus);
  outline: none;
}

[data-error] .m_8fb7ebe7:focus, [data-error] .m_8fb7ebe7:focus-within {
  --input-bd: var(--mantine-color-error);
}

.m_8fb7ebe7::-moz-placeholder {
  color: var(--input-placeholder-color);
  opacity: 1;
}

.m_8fb7ebe7::placeholder {
  color: var(--input-placeholder-color);
  opacity: 1;
}

.m_8fb7ebe7::-webkit-inner-spin-button, .m_8fb7ebe7::-webkit-outer-spin-button, .m_8fb7ebe7::-webkit-search-decoration, .m_8fb7ebe7::-webkit-search-cancel-button, .m_8fb7ebe7::-webkit-search-results-button, .m_8fb7ebe7::-webkit-search-results-decoration {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.m_8fb7ebe7[type="number"] {
  -moz-appearance: textfield;
}

.m_8fb7ebe7:disabled, .m_8fb7ebe7[data-disabled], .m_8fb7ebe7:has(input:disabled) {
  cursor: not-allowed;
  opacity: .6;
  background-color: var(--input-disabled-bg);
  color: var(--input-disabled-color);
}

.m_82577fc2 {
  pointer-events: var(--section-pointer-events);
  z-index: 1;
  inset-inline-start: var(--section-start);
  inset-inline-end: var(--section-end);
  bottom: var(--section-y);
  top: var(--section-y);
  width: var(--section-size);
  border-radius: var(--section-border-radius);
  color: var(--input-section-color, var(--mantine-color-dimmed));
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
}

.m_82577fc2[data-position="right"] {
  --section-pointer-events: var(--input-right-section-pointer-events);
  --section-end: var(--right-section-end);
  --section-size: var(--input-right-section-size);
  --section-border-radius: var(--right-section-border-radius);
}

.m_82577fc2[data-position="left"] {
  --section-pointer-events: var(--input-left-section-pointer-events);
  --section-start: var(--left-section-start);
  --section-size: var(--input-left-section-size);
  --section-border-radius: var(--left-section-border-radius);
}

.m_88bacfd0 {
  color: var(--input-placeholder-color, var(--mantine-color-placeholder));
}

[data-error] .m_88bacfd0 {
  --input-placeholder-color: var(--input-color, var(--mantine-color-placeholder));
}

.m_46b77525 {
  line-height: var(--mantine-line-height);
}

.m_8fdc1311 {
  word-break: break-word;
  cursor: default;
  -webkit-tap-highlight-color: transparent;
  font-weight: 500;
  font-size: var(--input-label-size, var(--mantine-font-size-sm));
  display: inline-block;
}

.m_78a94662 {
  color: var(--input-asterisk-color, var(--mantine-color-error));
}

.m_8f816625, .m_fe47ce59 {
  word-wrap: break-word;
  margin: 0;
  padding: 0;
  line-height: 1.2;
  display: block;
}

.m_8f816625 {
  color: var(--mantine-color-error);
  font-size: var(--input-error-size, calc(var(--mantine-font-size-sm)  - calc(.125rem * var(--mantine-scale))));
}

.m_fe47ce59 {
  color: var(--mantine-color-dimmed);
  font-size: var(--input-description-size, calc(var(--mantine-font-size-sm)  - calc(.125rem * var(--mantine-scale))));
}

.m_8bffd616 {
  display: flex;
}

.m_9bdbb667 {
  --accordion-radius: var(--mantine-radius-default);
}

.m_df78851f {
  word-break: break-word;
}

.m_4ba554d4 {
  padding: var(--mantine-spacing-md);
  padding-top: calc(var(--mantine-spacing-xs) / 2);
}

.m_8fa820a0 {
  margin: 0;
  padding: 0;
}

.m_4ba585b8 {
  width: 100%;
  padding-inline: var(--mantine-spacing-md);
  opacity: 1;
  cursor: pointer;
  background-color: #0000;
  flex-direction: row-reverse;
  align-items: center;
  display: flex;
}

.m_4ba585b8:where([data-chevron-position="left"]) {
  flex-direction: row;
  padding-inline-start: 0;
}

:where([data-mantine-color-scheme="light"]) .m_4ba585b8 {
  color: var(--mantine-color-black);
}

:where([data-mantine-color-scheme="dark"]) .m_4ba585b8 {
  color: var(--mantine-color-dark-0);
}

.m_4ba585b8:where(:disabled, [data-disabled]) {
  opacity: .4;
  cursor: not-allowed;
}

@media (hover: hover) {
  :where([data-mantine-color-scheme="light"]) .m_6939a5e9:where(:not(:disabled, [data-disabled])):hover, :where([data-mantine-color-scheme="light"]) .m_4271d21b:where(:not(:disabled, [data-disabled])):hover {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="dark"]) .m_6939a5e9:where(:not(:disabled, [data-disabled])):hover, :where([data-mantine-color-scheme="dark"]) .m_4271d21b:where(:not(:disabled, [data-disabled])):hover {
    background-color: var(--mantine-color-dark-6);
  }
}

@media (hover: none) {
  :where([data-mantine-color-scheme="light"]) .m_6939a5e9:where(:not(:disabled, [data-disabled])):active, :where([data-mantine-color-scheme="light"]) .m_4271d21b:where(:not(:disabled, [data-disabled])):active {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="dark"]) .m_6939a5e9:where(:not(:disabled, [data-disabled])):active, :where([data-mantine-color-scheme="dark"]) .m_4271d21b:where(:not(:disabled, [data-disabled])):active {
    background-color: var(--mantine-color-dark-6);
  }
}

.m_df3ffa0f {
  color: inherit;
  text-overflow: ellipsis;
  padding-top: var(--mantine-spacing-sm);
  padding-bottom: var(--mantine-spacing-sm);
  flex: 1;
  font-weight: 400;
  overflow: hidden;
}

.m_3f35ae96 {
  transition: transform var(--accordion-transition-duration, .2s) ease;
  width: var(--accordion-chevron-size, calc(.9375rem * var(--mantine-scale)));
  min-width: var(--accordion-chevron-size, calc(.9375rem * var(--mantine-scale)));
  justify-content: flex-start;
  align-items: center;
  display: flex;
  transform: rotate(0);
}

.m_3f35ae96:where([data-rotate]) {
  transform: rotate(180deg);
}

.m_3f35ae96:where([data-position="left"]) {
  margin-inline-start: var(--mantine-spacing-md);
  margin-inline-end: var(--mantine-spacing-md);
}

.m_9bd771fe {
  justify-content: center;
  align-items: center;
  margin-inline-end: var(--mantine-spacing-sm);
  display: flex;
}

.m_9bd771fe:where([data-chevron-position="left"]) {
  margin-inline-start: var(--mantine-spacing-lg);
  margin-inline-end: 0;
}

:where([data-mantine-color-scheme="light"]) .m_9bd7b098 {
  --item-border-color: var(--mantine-color-gray-3);
  --item-filled-color: var(--mantine-color-gray-0);
}

:where([data-mantine-color-scheme="dark"]) .m_9bd7b098 {
  --item-border-color: var(--mantine-color-dark-4);
  --item-filled-color: var(--mantine-color-dark-6);
}

.m_fe19b709 {
  border-bottom: 1px solid var(--item-border-color);
}

.m_1f921b3b {
  border: 1px solid var(--item-border-color);
  transition: background-color .15s;
}

.m_1f921b3b:where([data-active]) {
  background-color: var(--item-filled-color);
}

.m_1f921b3b:first-of-type, .m_1f921b3b:first-of-type > [data-accordion-control] {
  border-start-start-radius: var(--accordion-radius);
  border-start-end-radius: var(--accordion-radius);
}

.m_1f921b3b:last-of-type, .m_1f921b3b:last-of-type > [data-accordion-control] {
  border-end-end-radius: var(--accordion-radius);
  border-end-start-radius: var(--accordion-radius);
}

.m_1f921b3b + .m_1f921b3b {
  border-top: 0;
}

.m_2cdf939a {
  border-radius: var(--accordion-radius);
}

.m_2cdf939a:where([data-active]) {
  background-color: var(--item-filled-color);
}

.m_9f59b069 {
  background-color: var(--item-filled-color);
  border-radius: var(--accordion-radius);
  border: 1px solid #0000;
  transition: background-color .15s;
}

.m_9f59b069[data-active] {
  border-color: var(--item-border-color);
}

:where([data-mantine-color-scheme="light"]) .m_9f59b069[data-active] {
  background-color: var(--mantine-color-white);
}

:where([data-mantine-color-scheme="dark"]) .m_9f59b069[data-active] {
  background-color: var(--mantine-color-dark-7);
}

.m_9f59b069 + .m_9f59b069 {
  margin-top: var(--mantine-spacing-md);
}

.m_8d3f4000 {
  --ai-size-xs: 18px;
  --ai-size-sm: 22px;
  --ai-size-md: 28px;
  --ai-size-lg: 34px;
  --ai-size-xl: 44px;
  --ai-size-input-xs: 30px;
  --ai-size-input-sm: 36px;
  --ai-size-input-md: 42px;
  --ai-size-input-lg: 50px;
  --ai-size-input-xl: 60px;
  --ai-size: var(--ai-size-md);
  --ai-color: var(--mantine-color-white);
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  width: var(--ai-size);
  height: var(--ai-size);
  min-width: var(--ai-size);
  min-height: var(--ai-size);
  border-radius: var(--ai-radius, var(--mantine-radius-default));
  background: var(--ai-bg, var(--mantine-primary-color-filled));
  color: var(--ai-color, var(--mantine-color-white));
  border: var(--ai-bd, calc(.0625rem * var(--mantine-scale)) solid transparent);
  cursor: pointer;
  justify-content: center;
  align-items: center;
  line-height: 1;
  display: inline-flex;
  position: relative;
  overflow: hidden;
}

@media (hover: hover) {
  .m_8d3f4000:hover:where(:not([data-loading], :disabled, [data-disabled])) {
    background-color: var(--ai-hover, var(--mantine-primary-color-filled-hover));
    color: var(--ai-hover-color, var(--ai-color));
  }
}

@media (hover: none) {
  .m_8d3f4000:active:where(:not([data-loading], :disabled, [data-disabled])) {
    background-color: var(--ai-hover, var(--mantine-primary-color-filled-hover));
    color: var(--ai-hover-color, var(--ai-color));
  }
}

.m_8d3f4000[data-loading] {
  cursor: not-allowed;
}

.m_8d3f4000[data-loading] .m_8d3afb97 {
  opacity: 0;
  transform: translateY(100%);
}

.m_8d3f4000:where(:disabled:not([data-loading]), [data-disabled]:not([data-loading])) {
  cursor: not-allowed;
  border: 1px solid #0000;
}

:where([data-mantine-color-scheme="light"]) .m_8d3f4000:where(:disabled:not([data-loading]), [data-disabled]:not([data-loading])) {
  background-color: var(--mantine-color-gray-1);
  color: var(--mantine-color-gray-5);
}

:where([data-mantine-color-scheme="dark"]) .m_8d3f4000:where(:disabled:not([data-loading]), [data-disabled]:not([data-loading])) {
  background-color: var(--mantine-color-dark-6);
  color: var(--mantine-color-dark-3);
}

.m_8d3f4000:where(:disabled:not([data-loading]), [data-disabled]:not([data-loading])):active {
  transform: none;
}

.m_302b9fb1 {
  border-radius: var(--ai-radius, var(--mantine-radius-default));
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
  inset: -1px;
}

:where([data-mantine-color-scheme="light"]) .m_302b9fb1 {
  background-color: #ffffff26;
}

:where([data-mantine-color-scheme="dark"]) .m_302b9fb1 {
  background-color: #00000026;
}

.m_1a0f1b21 {
  --ai-border-width: 1px;
  display: flex;
}

.m_1a0f1b21 :where(*):focus {
  z-index: 1;
  position: relative;
}

.m_1a0f1b21[data-orientation="horizontal"] {
  flex-direction: row;
}

.m_1a0f1b21[data-orientation="horizontal"] .m_8d3f4000:not(:only-child):first-child, .m_1a0f1b21[data-orientation="horizontal"] .m_437b6484:not(:only-child):first-child {
  border-inline-end-width: calc(var(--ai-border-width) / 2);
  border-start-end-radius: 0;
  border-end-end-radius: 0;
}

.m_1a0f1b21[data-orientation="horizontal"] .m_8d3f4000:not(:only-child):last-child, .m_1a0f1b21[data-orientation="horizontal"] .m_437b6484:not(:only-child):last-child {
  border-inline-start-width: calc(var(--ai-border-width) / 2);
  border-start-start-radius: 0;
  border-end-start-radius: 0;
}

.m_1a0f1b21[data-orientation="horizontal"] .m_8d3f4000:not(:only-child):not(:first-child):not(:last-child), .m_1a0f1b21[data-orientation="horizontal"] .m_437b6484:not(:only-child):not(:first-child):not(:last-child) {
  border-inline-width: calc(var(--ai-border-width) / 2);
  border-radius: 0;
}

.m_1a0f1b21[data-orientation="vertical"] {
  flex-direction: column;
}

.m_1a0f1b21[data-orientation="vertical"] .m_8d3f4000:not(:only-child):first-child, .m_1a0f1b21[data-orientation="vertical"] .m_437b6484:not(:only-child):first-child {
  border-bottom-width: calc(var(--ai-border-width) / 2);
  border-end-end-radius: 0;
  border-end-start-radius: 0;
}

.m_1a0f1b21[data-orientation="vertical"] .m_8d3f4000:not(:only-child):last-child, .m_1a0f1b21[data-orientation="vertical"] .m_437b6484:not(:only-child):last-child {
  border-top-width: calc(var(--ai-border-width) / 2);
  border-start-start-radius: 0;
  border-start-end-radius: 0;
}

.m_1a0f1b21[data-orientation="vertical"] .m_8d3f4000:not(:only-child):not(:first-child):not(:last-child), .m_1a0f1b21[data-orientation="vertical"] .m_437b6484:not(:only-child):not(:first-child):not(:last-child) {
  border-bottom-width: calc(var(--ai-border-width) / 2);
  border-top-width: calc(var(--ai-border-width) / 2);
  border-radius: 0;
}

.m_8d3afb97 {
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  transition: transform .15s, opacity .1s;
  display: flex;
}

.m_437b6484 {
  --section-height-xs: 18px;
  --section-height-sm: 22px;
  --section-height-md: 28px;
  --section-height-lg: 34px;
  --section-height-xl: 44px;
  --section-height-input-xs: 30px;
  --section-height-input-sm: 36px;
  --section-height-input-md: 42px;
  --section-height-input-lg: 50px;
  --section-height-input-xl: 60px;
  --section-padding-x-xs: 6px;
  --section-padding-x-sm: 8px;
  --section-padding-x-md: 10px;
  --section-padding-x-lg: 12px;
  --section-padding-x-xl: 16px;
  --section-height: var(--section-height-sm);
  --section-padding-x: var(--section-padding-x-sm);
  --section-color: var(--mantine-color-white);
  border-radius: var(--section-radius, var(--mantine-radius-default));
  width: auto;
  font-weight: 600;
  font-size: var(--section-fz, var(--mantine-font-size-sm));
  background: var(--section-bg, var(--mantine-primary-color-filled));
  border: var(--section-bd, calc(.0625rem * var(--mantine-scale)) solid transparent);
  color: var(--section-color, var(--mantine-color-white));
  height: var(--section-height, var(--section-height-sm));
  padding-inline: var(--section-padding-x, var(--section-padding-x-sm));
  vertical-align: middle;
  justify-content: center;
  align-items: center;
  line-height: 1;
  display: inline-flex;
}

.m_7f854edf {
  z-index: var(--affix-z-index);
  inset-inline-start: var(--affix-left);
  inset-inline-end: var(--affix-right);
  top: var(--affix-top);
  bottom: var(--affix-bottom);
  position: fixed;
}

.m_66836ed3 {
  --alert-radius: var(--mantine-radius-default);
  --alert-bg: var(--mantine-primary-color-light);
  --alert-bd: 1px solid transparent;
  --alert-color: var(--mantine-primary-color-light-color);
  padding: var(--mantine-spacing-md) var(--mantine-spacing-md);
  border-radius: var(--alert-radius);
  background-color: var(--alert-bg);
  border: var(--alert-bd);
  color: var(--alert-color);
  position: relative;
  overflow: hidden;
}

.m_a5d60502 {
  display: flex;
}

.m_667c2793 {
  gap: var(--mantine-spacing-xs);
  flex-direction: column;
  flex: 1;
  display: flex;
}

.m_6a03f287 {
  font-size: var(--mantine-font-size-sm);
  justify-content: space-between;
  align-items: center;
  font-weight: 700;
  display: flex;
}

.m_6a03f287:where([data-with-close-button]) {
  padding-inline-end: var(--mantine-spacing-md);
}

.m_698f4f23 {
  text-overflow: ellipsis;
  display: block;
  overflow: hidden;
}

.m_667f2a6a {
  width: 20px;
  height: 20px;
  margin-inline-end: var(--mantine-spacing-md);
  justify-content: flex-start;
  align-items: center;
  margin-top: 1px;
  line-height: 1;
  display: flex;
}

.m_7fa78076 {
  text-overflow: ellipsis;
  font-size: var(--mantine-font-size-sm);
  overflow: hidden;
}

:where([data-mantine-color-scheme="light"]) .m_7fa78076 {
  color: var(--mantine-color-black);
}

:where([data-mantine-color-scheme="dark"]) .m_7fa78076 {
  color: var(--mantine-color-white);
}

.m_7fa78076:where([data-variant="filled"]) {
  color: var(--alert-color);
}

.m_7fa78076:where([data-variant="white"]) {
  color: var(--mantine-color-black);
}

.m_87f54839 {
  width: 20px;
  height: 20px;
  color: var(--alert-color);
}

.m_849cf0da {
  color: var(--mantine-color-anchor);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
  background-color: #0000;
  border: none;
  margin: 0;
  padding: 0;
  text-decoration: none;
  display: inline;
}

@media (hover: hover) {
  .m_849cf0da:where([data-underline="hover"]):hover {
    text-decoration: underline;
  }
}

@media (hover: none) {
  .m_849cf0da:where([data-underline="hover"]):active {
    text-decoration: underline;
  }
}

.m_849cf0da:where([data-underline="not-hover"]) {
  text-decoration: underline;
}

@media (hover: hover) {
  .m_849cf0da:where([data-underline="not-hover"]):hover {
    text-decoration: none;
  }
}

@media (hover: none) {
  .m_849cf0da:where([data-underline="not-hover"]):active {
    text-decoration: none;
  }
}

.m_849cf0da:where([data-underline="always"]) {
  text-decoration: underline;
}

.m_849cf0da:where([data-variant="gradient"]), .m_849cf0da:where([data-variant="gradient"]):hover {
  text-decoration: none;
}

.m_849cf0da:where([data-line-clamp]) {
  display: -webkit-box;
}

.m_71ac47fc {
  --ar-ratio: 1;
  max-width: 100%;
}

.m_71ac47fc > :where(:not(style)) {
  aspect-ratio: var(--ar-ratio);
  width: 100%;
}

.m_71ac47fc > :where(img, video) {
  -o-object-fit: cover;
  object-fit: cover;
}

.m_89ab340[data-resizing] {
  --app-shell-transition-duration: 0s !important;
}

.m_89ab340[data-disabled] {
  --app-shell-header-offset: 0rem !important;
  --app-shell-navbar-offset: 0rem !important;
  --app-shell-aside-offset: 0rem !important;
  --app-shell-footer-offset: 0rem !important;
}

[data-mantine-color-scheme="light"] .m_89ab340 {
  --app-shell-border-color: var(--mantine-color-gray-3);
}

[data-mantine-color-scheme="dark"] .m_89ab340 {
  --app-shell-border-color: var(--mantine-color-dark-4);
}

.m_45252eee, .m_9cdde9a, .m_3b16f56b, .m_8983817, .m_3840c879 {
  transition-duration: var(--app-shell-transition-duration);
  transition-timing-function: var(--app-shell-transition-timing-function);
}

.m_45252eee, .m_9cdde9a {
  top: var(--app-shell-header-offset, 0rem);
  height: calc(100dvh - var(--app-shell-header-offset, 0rem)  - var(--app-shell-footer-offset, 0rem));
  background-color: var(--mantine-color-body);
  flex-direction: column;
  transition-property: transform, top, height;
  display: flex;
  position: fixed;
}

:where([data-layout="alt"]) .m_45252eee, :where([data-layout="alt"]) .m_9cdde9a {
  height: 100dvh;
  top: 0;
}

.m_45252eee {
  width: var(--app-shell-navbar-width);
  transform: var(--app-shell-navbar-transform);
  z-index: var(--app-shell-navbar-z-index);
  transition-property: transform, top, height;
  inset-inline-start: 0;
}

:where([dir="rtl"]) .m_45252eee {
  transform: var(--app-shell-navbar-transform-rtl);
}

.m_45252eee:where([data-with-border]) {
  border-inline-end: 1px solid var(--app-shell-border-color);
}

.m_9cdde9a {
  width: var(--app-shell-aside-width);
  transform: var(--app-shell-aside-transform);
  z-index: var(--app-shell-aside-z-index);
  inset-inline-end: 0;
}

:where([dir="rtl"]) .m_9cdde9a {
  transform: var(--app-shell-aside-transform-rtl);
}

.m_9cdde9a:where([data-with-border]) {
  border-inline-start: 1px solid var(--app-shell-border-color);
}

.m_8983817 {
  padding-inline-start: calc(var(--app-shell-navbar-offset, 0rem)  + var(--app-shell-padding));
  padding-inline-end: calc(var(--app-shell-aside-offset, 0rem)  + var(--app-shell-padding));
  padding-top: calc(var(--app-shell-header-offset, 0rem)  + var(--app-shell-padding));
  padding-bottom: calc(var(--app-shell-footer-offset, 0rem)  + var(--app-shell-padding));
  min-height: 100dvh;
  transition-property: padding;
}

.m_3b16f56b, .m_3840c879 {
  background-color: var(--mantine-color-body);
  transition-property: transform, left, right;
  position: fixed;
  inset-inline: 0;
}

:where([data-layout="alt"]) .m_3b16f56b, :where([data-layout="alt"]) .m_3840c879 {
  inset-inline-start: var(--app-shell-navbar-offset, 0rem);
  inset-inline-end: var(--app-shell-aside-offset, 0rem);
}

.m_3b16f56b {
  height: var(--app-shell-header-height);
  background-color: var(--mantine-color-body);
  transform: var(--app-shell-header-transform);
  z-index: var(--app-shell-header-z-index);
  top: 0;
}

.m_3b16f56b:where([data-with-border]) {
  border-bottom: 1px solid var(--app-shell-border-color);
}

.m_3840c879 {
  height: calc(var(--app-shell-footer-height)  + env(safe-area-inset-bottom));
  padding-bottom: env(safe-area-inset-bottom);
  transform: var(--app-shell-footer-transform);
  z-index: var(--app-shell-footer-z-index);
  bottom: 0;
}

.m_3840c879:where([data-with-border]) {
  border-top: 1px solid var(--app-shell-border-color);
}

.m_6dcfc7c7 {
  flex-grow: 0;
}

.m_6dcfc7c7:where([data-grow]) {
  flex-grow: 1;
}

.m_11def92b {
  --ag-spacing: var(--mantine-spacing-sm);
  --ag-offset: calc(var(--ag-spacing) * -1);
  padding-inline-start: var(--ag-spacing);
  display: flex;
}

.m_f85678b6 {
  --avatar-size-xs: 16px;
  --avatar-size-sm: 26px;
  --avatar-size-md: 38px;
  --avatar-size-lg: 56px;
  --avatar-size-xl: 84px;
  --avatar-size: var(--avatar-size-md);
  --avatar-radius: 1000px;
  --avatar-bg: var(--mantine-color-gray-light);
  --avatar-bd: 1px solid transparent;
  --avatar-color: var(--mantine-color-gray-light-color);
  --avatar-placeholder-fz: calc(var(--avatar-size) / 2.5);
  -webkit-tap-highlight-color: transparent;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border-radius: var(--avatar-radius);
  width: var(--avatar-size);
  height: var(--avatar-size);
  min-width: var(--avatar-size);
  padding: 0;
  text-decoration: none;
  display: block;
  position: relative;
  overflow: hidden;
}

.m_f85678b6:where([data-within-group]) {
  border: 2px solid var(--mantine-color-body);
  background: var(--mantine-color-body);
  margin-inline-start: var(--ag-offset);
}

.m_11f8ac07 {
  -o-object-fit: cover;
  object-fit: cover;
  width: 100%;
  height: 100%;
  display: block;
}

.m_104cd71f {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border-radius: var(--avatar-radius);
  width: 100%;
  height: 100%;
  font-weight: 700;
  font-size: var(--avatar-placeholder-fz);
  background: var(--avatar-bg);
  border: var(--avatar-bd);
  color: var(--avatar-color);
  justify-content: center;
  align-items: center;
  display: flex;
}

.m_104cd71f > [data-avatar-placeholder-icon] {
  width: 70%;
  height: 70%;
}

.m_347db0ec {
  --badge-height-xs: 16px;
  --badge-height-sm: 18px;
  --badge-height-md: 20px;
  --badge-height-lg: 26px;
  --badge-height-xl: 32px;
  --badge-fz-xs: 9px;
  --badge-fz-sm: 10px;
  --badge-fz-md: 11px;
  --badge-fz-lg: 13px;
  --badge-fz-xl: 16px;
  --badge-padding-x-xs: 6px;
  --badge-padding-x-sm: 8px;
  --badge-padding-x-md: 10px;
  --badge-padding-x-lg: 12px;
  --badge-padding-x-xl: 16px;
  --badge-height: var(--badge-height-md);
  --badge-fz: var(--badge-fz-md);
  --badge-padding-x: var(--badge-padding-x-md);
  --badge-radius: 1000px;
  --badge-lh: calc(var(--badge-height)  - calc(.125rem * var(--mantine-scale)));
  --badge-color: var(--mantine-color-white);
  --badge-bg: var(--mantine-primary-color-filled);
  --badge-border-width: 1px;
  --badge-bd: var(--badge-border-width) solid transparent;
  -webkit-tap-highlight-color: transparent;
  font-size: var(--badge-fz);
  border-radius: var(--badge-radius);
  height: var(--badge-height);
  line-height: var(--badge-lh);
  padding: 0 var(--badge-padding-x);
  text-transform: uppercase;
  letter-spacing: .25px;
  cursor: default;
  text-overflow: ellipsis;
  width: fit-content;
  color: var(--badge-color);
  background: var(--badge-bg);
  border: var(--badge-bd);
  justify-content: center;
  align-items: center;
  font-weight: 700;
  text-decoration: none;
  display: inline-grid;
  overflow: hidden;
}

.m_347db0ec:where([data-with-left-section], [data-variant="dot"]) {
  grid-template-columns: auto 1fr;
}

.m_347db0ec:where([data-with-right-section]) {
  grid-template-columns: 1fr auto;
}

.m_347db0ec:where([data-with-left-section][data-with-right-section], [data-variant="dot"][data-with-right-section]) {
  grid-template-columns: auto 1fr auto;
}

.m_347db0ec:where([data-block]) {
  width: 100%;
  display: flex;
}

.m_347db0ec:where([data-circle]) {
  width: var(--badge-height);
  padding-inline: 2px;
  display: flex;
}

.m_fbd81e3d {
  --badge-dot-size: calc(var(--badge-height) / 3.4);
}

:where([data-mantine-color-scheme="light"]) .m_fbd81e3d {
  background-color: var(--mantine-color-white);
  border-color: var(--mantine-color-gray-4);
  color: var(--mantine-color-black);
}

:where([data-mantine-color-scheme="dark"]) .m_fbd81e3d {
  background-color: var(--mantine-color-dark-5);
  border-color: var(--mantine-color-dark-5);
  color: var(--mantine-color-white);
}

.m_fbd81e3d:before {
  content: "";
  width: var(--badge-dot-size);
  height: var(--badge-dot-size);
  border-radius: var(--badge-dot-size);
  background-color: var(--badge-dot-color);
  margin-inline-end: var(--badge-dot-size);
  display: block;
}

.m_5add502a {
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: center;
  cursor: inherit;
  overflow: hidden;
}

.m_91fdda9b {
  --badge-section-margin: calc(var(--mantine-spacing-xs) / 2);
  max-height: calc(var(--badge-height)  - var(--badge-border-width) * 2);
  justify-content: center;
  align-items: center;
  display: inline-flex;
}

.m_91fdda9b:where([data-position="left"]) {
  margin-inline-end: var(--badge-section-margin);
}

.m_91fdda9b:where([data-position="right"]) {
  margin-inline-start: var(--badge-section-margin);
}

.m_2ce0de02 {
  border-radius: var(--bi-radius, 0);
  background-position: center;
  background-size: cover;
  border: 0;
  width: 100%;
  text-decoration: none;
  display: block;
}

.m_ddec01c0 {
  --blockquote-border: 3px solid var(--bq-bd);
  border-inline-start: var(--blockquote-border);
  padding: var(--mantine-spacing-xl) calc(2.375rem * var(--mantine-scale));
  border-start-end-radius: var(--bq-radius);
  border-end-end-radius: var(--bq-radius);
  margin: 0;
  position: relative;
}

:where([data-mantine-color-scheme="light"]) .m_ddec01c0 {
  background-color: var(--bq-bg-light);
}

:where([data-mantine-color-scheme="dark"]) .m_ddec01c0 {
  background-color: var(--bq-bg-dark);
}

.m_dde7bd57 {
  --blockquote-icon-offset: calc(var(--bq-icon-size) / -2);
  color: var(--bq-bd);
  background-color: var(--mantine-color-body);
  top: var(--blockquote-icon-offset);
  width: var(--bq-icon-size);
  height: var(--bq-icon-size);
  border-radius: var(--bq-icon-size);
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
  inset-inline-start: var(--blockquote-icon-offset);
}

.m_dde51a35 {
  margin-top: var(--mantine-spacing-md);
  opacity: .6;
  font-size: 85%;
  display: block;
}

.m_8b3717df {
  flex-wrap: wrap;
  align-items: center;
  display: flex;
}

.m_f678d540 {
  white-space: nowrap;
  -webkit-tap-highlight-color: transparent;
  line-height: 1;
}

.m_3b8f2208 {
  margin-inline: var(--bc-separator-margin, var(--mantine-spacing-xs));
  justify-content: center;
  align-items: center;
  line-height: 1;
  display: flex;
}

:where([data-mantine-color-scheme="light"]) .m_3b8f2208 {
  color: var(--mantine-color-gray-7);
}

:where([data-mantine-color-scheme="dark"]) .m_3b8f2208 {
  color: var(--mantine-color-dark-2);
}

.m_77c9d27d {
  --button-height-xs: 30px;
  --button-height-sm: 36px;
  --button-height-md: 42px;
  --button-height-lg: 50px;
  --button-height-xl: 60px;
  --button-height-compact-xs: 22px;
  --button-height-compact-sm: 26px;
  --button-height-compact-md: 30px;
  --button-height-compact-lg: 34px;
  --button-height-compact-xl: 40px;
  --button-padding-x-xs: 14px;
  --button-padding-x-sm: 18px;
  --button-padding-x-md: 22px;
  --button-padding-x-lg: 26px;
  --button-padding-x-xl: 32px;
  --button-padding-x-compact-xs: 7px;
  --button-padding-x-compact-sm: 8px;
  --button-padding-x-compact-md: 10px;
  --button-padding-x-compact-lg: 12px;
  --button-padding-x-compact-xl: 14px;
  --button-height: var(--button-height-sm);
  --button-padding-x: var(--button-padding-x-sm);
  --button-color: var(--mantine-color-white);
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  text-align: center;
  cursor: pointer;
  border-radius: var(--button-radius, var(--mantine-radius-default));
  width: auto;
  font-weight: 600;
  line-height: 1;
  font-size: var(--button-fz, var(--mantine-font-size-sm));
  background: var(--button-bg, var(--mantine-primary-color-filled));
  border: var(--button-bd, calc(.0625rem * var(--mantine-scale)) solid transparent);
  color: var(--button-color, var(--mantine-color-white));
  height: var(--button-height, var(--button-height-sm));
  padding-inline: var(--button-padding-x, var(--button-padding-x-sm));
  vertical-align: middle;
  display: inline-block;
  position: relative;
  overflow: hidden;
}

.m_77c9d27d:where([data-block]) {
  width: 100%;
  display: block;
}

.m_77c9d27d:where([data-with-left-section]) {
  padding-inline-start: calc(var(--button-padding-x) / 1.5);
}

.m_77c9d27d:where([data-with-right-section]) {
  padding-inline-end: calc(var(--button-padding-x) / 1.5);
}

.m_77c9d27d:where(:disabled:not([data-loading]), [data-disabled]:not([data-loading])) {
  cursor: not-allowed;
  border: 1px solid #0000;
  transform: none;
}

:where([data-mantine-color-scheme="light"]) .m_77c9d27d:where(:disabled:not([data-loading]), [data-disabled]:not([data-loading])) {
  color: var(--mantine-color-gray-5);
  background: var(--mantine-color-gray-1);
}

:where([data-mantine-color-scheme="dark"]) .m_77c9d27d:where(:disabled:not([data-loading]), [data-disabled]:not([data-loading])) {
  color: var(--mantine-color-dark-3);
  background: var(--mantine-color-dark-6);
}

.m_77c9d27d:before {
  content: "";
  pointer-events: none;
  border-radius: var(--button-radius, var(--mantine-radius-default));
  opacity: 0;
  filter: blur(12px);
  transition: transform .15s, opacity .1s;
  position: absolute;
  inset: -1px;
  transform: translateY(-100%);
}

:where([data-mantine-color-scheme="light"]) .m_77c9d27d:before {
  background-color: #ffffff26;
}

:where([data-mantine-color-scheme="dark"]) .m_77c9d27d:before {
  background-color: #00000026;
}

.m_77c9d27d:where([data-loading]) {
  cursor: not-allowed;
  transform: none;
}

.m_77c9d27d:where([data-loading]):before {
  opacity: 1;
  transform: translateY(0);
}

.m_77c9d27d:where([data-loading]) .m_80f1301b {
  opacity: 0;
  transform: translateY(100%);
}

@media (hover: hover) {
  .m_77c9d27d:hover:where(:not([data-loading], :disabled, [data-disabled])) {
    background-color: var(--button-hover, var(--mantine-primary-color-filled-hover));
    color: var(--button-hover-color, var(--button-color));
  }
}

@media (hover: none) {
  .m_77c9d27d:active:where(:not([data-loading], :disabled, [data-disabled])) {
    background-color: var(--button-hover, var(--mantine-primary-color-filled-hover));
    color: var(--button-hover-color, var(--button-color));
  }
}

.m_80f1301b {
  align-items: center;
  justify-content: var(--button-justify, center);
  height: 100%;
  transition: transform .15s, opacity .1s;
  display: flex;
  overflow: visible;
}

.m_811560b9 {
  white-space: nowrap;
  opacity: 1;
  align-items: center;
  height: 100%;
  display: flex;
  overflow: hidden;
}

.m_811560b9:where([data-loading]) {
  opacity: .2;
}

.m_a74036a {
  align-items: center;
  display: flex;
}

.m_a74036a:where([data-position="left"]) {
  margin-inline-end: var(--mantine-spacing-xs);
}

.m_a74036a:where([data-position="right"]) {
  margin-inline-start: var(--mantine-spacing-xs);
}

.m_a25b86ee {
  position: absolute;
  top: 50%;
  left: 50%;
}

.m_80d6d844 {
  --button-border-width: 1px;
  display: flex;
}

.m_80d6d844 :where(.m_77c9d27d):focus {
  z-index: 1;
  position: relative;
}

.m_80d6d844[data-orientation="horizontal"] {
  flex-direction: row;
}

.m_80d6d844[data-orientation="horizontal"] .m_77c9d27d:not(:only-child):first-child, .m_80d6d844[data-orientation="horizontal"] .m_70be2a01:not(:only-child):first-child {
  border-inline-end-width: calc(var(--button-border-width) / 2);
  border-start-end-radius: 0;
  border-end-end-radius: 0;
}

.m_80d6d844[data-orientation="horizontal"] .m_77c9d27d:not(:only-child):last-child, .m_80d6d844[data-orientation="horizontal"] .m_70be2a01:not(:only-child):last-child {
  border-inline-start-width: calc(var(--button-border-width) / 2);
  border-start-start-radius: 0;
  border-end-start-radius: 0;
}

.m_80d6d844[data-orientation="horizontal"] .m_77c9d27d:not(:only-child):not(:first-child):not(:last-child), .m_80d6d844[data-orientation="horizontal"] .m_70be2a01:not(:only-child):not(:first-child):not(:last-child) {
  border-inline-width: calc(var(--button-border-width) / 2);
  border-radius: 0;
}

.m_80d6d844[data-orientation="vertical"] {
  flex-direction: column;
}

.m_80d6d844[data-orientation="vertical"] .m_77c9d27d:not(:only-child):first-child, .m_80d6d844[data-orientation="vertical"] .m_70be2a01:not(:only-child):first-child {
  border-bottom-width: calc(var(--button-border-width) / 2);
  border-end-end-radius: 0;
  border-end-start-radius: 0;
}

.m_80d6d844[data-orientation="vertical"] .m_77c9d27d:not(:only-child):last-child, .m_80d6d844[data-orientation="vertical"] .m_70be2a01:not(:only-child):last-child {
  border-top-width: calc(var(--button-border-width) / 2);
  border-start-start-radius: 0;
  border-start-end-radius: 0;
}

.m_80d6d844[data-orientation="vertical"] .m_77c9d27d:not(:only-child):not(:first-child):not(:last-child), .m_80d6d844[data-orientation="vertical"] .m_70be2a01:not(:only-child):not(:first-child):not(:last-child) {
  border-bottom-width: calc(var(--button-border-width) / 2);
  border-top-width: calc(var(--button-border-width) / 2);
  border-radius: 0;
}

.m_70be2a01 {
  --section-height-xs: 30px;
  --section-height-sm: 36px;
  --section-height-md: 42px;
  --section-height-lg: 50px;
  --section-height-xl: 60px;
  --section-height-compact-xs: 22px;
  --section-height-compact-sm: 26px;
  --section-height-compact-md: 30px;
  --section-height-compact-lg: 34px;
  --section-height-compact-xl: 40px;
  --section-padding-x-xs: 14px;
  --section-padding-x-sm: 18px;
  --section-padding-x-md: 22px;
  --section-padding-x-lg: 26px;
  --section-padding-x-xl: 32px;
  --section-padding-x-compact-xs: 7px;
  --section-padding-x-compact-sm: 8px;
  --section-padding-x-compact-md: 10px;
  --section-padding-x-compact-lg: 12px;
  --section-padding-x-compact-xl: 14px;
  --section-height: var(--section-height-sm);
  --section-padding-x: var(--section-padding-x-sm);
  --section-color: var(--mantine-color-white);
  border-radius: var(--section-radius, var(--mantine-radius-default));
  width: auto;
  font-weight: 600;
  font-size: var(--section-fz, var(--mantine-font-size-sm));
  background: var(--section-bg, var(--mantine-primary-color-filled));
  border: var(--section-bd, calc(.0625rem * var(--mantine-scale)) solid transparent);
  color: var(--section-color, var(--mantine-color-white));
  height: var(--section-height, var(--section-height-sm));
  padding-inline: var(--section-padding-x, var(--section-padding-x-sm));
  vertical-align: middle;
  justify-content: center;
  align-items: center;
  line-height: 1;
  display: inline-flex;
}

.m_fea6bf1a {
  --burger-size-xs: 12px;
  --burger-size-sm: 18px;
  --burger-size-md: 24px;
  --burger-size-lg: 34px;
  --burger-size-xl: 42px;
  --burger-size: var(--burger-size-md);
  --burger-line-size: calc(var(--burger-size) / 12);
  width: calc(var(--burger-size)  + var(--mantine-spacing-xs));
  height: calc(var(--burger-size)  + var(--mantine-spacing-xs));
  padding: calc(var(--mantine-spacing-xs) / 2);
  cursor: pointer;
}

:where([data-mantine-color-scheme="light"]) .m_fea6bf1a {
  --burger-color: var(--mantine-color-black);
}

:where([data-mantine-color-scheme="dark"]) .m_fea6bf1a {
  --burger-color: var(--mantine-color-white);
}

.m_d4fb9cad {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  position: relative;
}

.m_d4fb9cad, .m_d4fb9cad:before, .m_d4fb9cad:after {
  width: var(--burger-size);
  height: var(--burger-line-size);
  background-color: var(--burger-color);
  transition-property: background-color, transform;
  transition-duration: var(--burger-transition-duration, .3s);
  transition-timing-function: var(--burger-transition-timing-function, ease);
  outline: 1px solid #0000;
  display: block;
}

.m_d4fb9cad:before, .m_d4fb9cad:after {
  content: "";
  position: absolute;
  inset-inline-start: 0;
}

.m_d4fb9cad:before {
  top: calc(var(--burger-size) / -3);
}

.m_d4fb9cad:after {
  top: calc(var(--burger-size) / 3);
}

.m_d4fb9cad[data-opened] {
  background-color: #0000;
}

.m_d4fb9cad[data-opened]:before {
  transform: translateY(calc(var(--burger-size) / 3)) rotate(45deg);
}

.m_d4fb9cad[data-opened]:after {
  transform: translateY(calc(var(--burger-size) / -3)) rotate(-45deg);
}

.m_e615b15f {
  --card-padding: var(--mantine-spacing-md);
  padding: var(--card-padding);
  color: var(--mantine-color-text);
  flex-direction: column;
  display: flex;
  position: relative;
  overflow: hidden;
}

:where([data-mantine-color-scheme="light"]) .m_e615b15f {
  background-color: var(--mantine-color-white);
}

:where([data-mantine-color-scheme="dark"]) .m_e615b15f {
  background-color: var(--mantine-color-dark-6);
}

.m_599a2148 {
  margin-inline: calc(var(--card-padding) * -1);
  display: block;
}

.m_599a2148:where(:first-child) {
  margin-top: calc(var(--card-padding) * -1);
  border-top: none !important;
}

.m_599a2148:where(:last-child) {
  margin-bottom: calc(var(--card-padding) * -1);
  border-bottom: none !important;
}

.m_599a2148:where([data-inherit-padding]) {
  padding-inline: var(--card-padding);
}

.m_599a2148:where([data-with-border]) {
  border-top: 1px solid;
  border-bottom: 1px solid;
}

:where([data-mantine-color-scheme="light"]) .m_599a2148 {
  border-color: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme="dark"]) .m_599a2148 {
  border-color: var(--mantine-color-dark-4);
}

.m_599a2148 + .m_599a2148 {
  border-top: none !important;
}

.m_4451eb3a {
  justify-content: center;
  align-items: center;
  display: flex;
}

.m_4451eb3a:where([data-inline]) {
  display: inline-flex;
}

.m_bf2d988c {
  --checkbox-size-xs: 16px;
  --checkbox-size-sm: 20px;
  --checkbox-size-md: 24px;
  --checkbox-size-lg: 30px;
  --checkbox-size-xl: 36px;
  --checkbox-size: var(--checkbox-size-sm);
  --checkbox-color: var(--mantine-primary-color-filled);
  --checkbox-icon-color: var(--mantine-color-white);
}

.m_26062bec {
  width: var(--checkbox-size);
  height: var(--checkbox-size);
  order: 1;
  position: relative;
}

.m_26062bec:where([data-label-position="left"]) {
  order: 2;
}

.m_26063560 {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: var(--checkbox-size);
  height: var(--checkbox-size);
  border-radius: var(--checkbox-radius, var(--mantine-radius-default));
  cursor: var(--mantine-cursor-type);
  -webkit-tap-highlight-color: transparent;
  border: 1px solid #0000;
  margin: 0;
  padding: 0;
  transition: border-color .1s, background-color .1s;
  display: block;
}

:where([data-mantine-color-scheme="light"]) .m_26063560 {
  background-color: var(--mantine-color-white);
  border-color: var(--mantine-color-gray-4);
}

:where([data-mantine-color-scheme="dark"]) .m_26063560 {
  background-color: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-dark-4);
}

.m_26063560:where([data-error]) {
  border-color: var(--mantine-color-error);
}

.m_26063560[data-indeterminate], .m_26063560:checked {
  background-color: var(--checkbox-color);
  border-color: var(--checkbox-color);
}

.m_26063560[data-indeterminate] + .m_bf295423, .m_26063560:checked + .m_bf295423 {
  opacity: 1;
  transform: none;
}

.m_26063560:disabled {
  cursor: not-allowed;
}

:where([data-mantine-color-scheme="light"]) .m_26063560:disabled {
  background-color: var(--mantine-color-gray-2);
  border-color: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme="dark"]) .m_26063560:disabled {
  background-color: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-dark-6);
}

[data-mantine-color-scheme="light"] .m_26063560:disabled + .m_bf295423 {
  color: var(--mantine-color-gray-5);
}

[data-mantine-color-scheme="dark"] .m_26063560:disabled + .m_bf295423 {
  color: var(--mantine-color-dark-3);
}

.m_215c4542 + .m_bf295423 {
  color: var(--checkbox-color);
}

.m_215c4542[data-indeterminate]:not(:disabled), .m_215c4542:checked:not(:disabled) {
  border-color: var(--checkbox-color);
  background-color: #0000;
}

.m_215c4542[data-indeterminate]:not(:disabled) + .m_bf295423, .m_215c4542:checked:not(:disabled) + .m_bf295423 {
  color: var(--checkbox-color);
  opacity: 1;
  transform: none;
}

.m_bf295423 {
  width: 60%;
  color: var(--checkbox-icon-color);
  pointer-events: none;
  transform: translateY(calc(.3125rem * var(--mantine-scale))) scale(.5);
  opacity: 0;
  margin: auto;
  transition: transform .1s, opacity .1s;
  position: absolute;
  inset: 0;
}

.m_f59ffda3 {
  --chip-size-xs: 23px;
  --chip-size-sm: 28px;
  --chip-size-md: 32px;
  --chip-size-lg: 36px;
  --chip-size-xl: 40px;
  --chip-icon-size-xs: 10px;
  --chip-icon-size-sm: 12px;
  --chip-icon-size-md: 14px;
  --chip-icon-size-lg: 16px;
  --chip-icon-size-xl: 18px;
  --chip-padding-xs: 16px;
  --chip-padding-sm: 20px;
  --chip-padding-md: 24px;
  --chip-padding-lg: 28px;
  --chip-padding-xl: 32px;
  --chip-checked-padding-xs: 7.5px;
  --chip-checked-padding-sm: 10px;
  --chip-checked-padding-md: 11.7px;
  --chip-checked-padding-lg: 13.5px;
  --chip-checked-padding-xl: 15.7px;
  --chip-spacing-xs: 10px;
  --chip-spacing-sm: 12px;
  --chip-spacing-md: 16px;
  --chip-spacing-lg: 20px;
  --chip-spacing-xl: 22px;
  --chip-size: var(--chip-size-sm);
  --chip-icon-size: var(--chip-icon-size-sm);
  --chip-padding: var(--chip-padding-sm);
  --chip-spacing: var(--chip-spacing-sm);
  --chip-checked-padding: var(--chip-checked-padding-sm);
  --chip-bg: var(--mantine-primary-color-filled);
  --chip-hover: var(--mantine-primary-color-filled-hover);
  --chip-color: var(--mantine-color-white);
  --chip-bd: 1px solid transparent;
}

.m_be049a53 {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border-radius: var(--chip-radius, 1000rem);
  height: var(--chip-size);
  font-size: var(--chip-fz, var(--mantine-font-size-sm));
  line-height: calc(var(--chip-size)  - calc(.125rem * var(--mantine-scale)));
  padding-inline: var(--chip-padding);
  cursor: pointer;
  white-space: nowrap;
  -webkit-tap-highlight-color: transparent;
  color: var(--mantine-color-text);
  border: 1px solid #0000;
  align-items: center;
  display: inline-flex;
}

.m_be049a53:where([data-checked]) {
  padding: var(--chip-checked-padding);
}

.m_be049a53:where([data-disabled]) {
  cursor: not-allowed;
}

:where([data-mantine-color-scheme="light"]) .m_be049a53:where([data-disabled]) {
  background-color: var(--mantine-color-gray-2);
  color: var(--mantine-color-gray-5);
}

:where([data-mantine-color-scheme="dark"]) .m_be049a53:where([data-disabled]) {
  background-color: var(--mantine-color-dark-6);
  color: var(--mantine-color-dark-3);
}

:where([data-mantine-color-scheme="light"]) .m_3904c1af:not([data-disabled]) {
  background-color: var(--mantine-color-white);
  border: 1px solid var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme="dark"]) .m_3904c1af:not([data-disabled]) {
  background-color: var(--mantine-color-dark-6);
  border: 1px solid var(--mantine-color-dark-4);
}

@media (hover: hover) {
  :where([data-mantine-color-scheme="light"]) .m_3904c1af:not([data-disabled]):hover {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="dark"]) .m_3904c1af:not([data-disabled]):hover {
    background-color: var(--mantine-color-dark-5);
  }
}

@media (hover: none) {
  :where([data-mantine-color-scheme="light"]) .m_3904c1af:not([data-disabled]):active {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="dark"]) .m_3904c1af:not([data-disabled]):active {
    background-color: var(--mantine-color-dark-5);
  }
}

.m_3904c1af:not([data-disabled]):where([data-checked]) {
  --chip-icon-color: var(--chip-color);
  border: var(--chip-bd);
}

@media (hover: hover) {
  .m_3904c1af:not([data-disabled]):where([data-checked]):hover {
    background-color: var(--chip-hover);
  }
}

@media (hover: none) {
  .m_3904c1af:not([data-disabled]):where([data-checked]):active {
    background-color: var(--chip-hover);
  }
}

.m_fa109255:not([data-disabled]), .m_f7e165c3:not([data-disabled]) {
  color: var(--mantine-color-text);
  border: 1px solid #0000;
}

:where([data-mantine-color-scheme="light"]) .m_fa109255:not([data-disabled]), :where([data-mantine-color-scheme="light"]) .m_f7e165c3:not([data-disabled]) {
  background-color: var(--mantine-color-gray-1);
}

:where([data-mantine-color-scheme="dark"]) .m_fa109255:not([data-disabled]), :where([data-mantine-color-scheme="dark"]) .m_f7e165c3:not([data-disabled]) {
  background-color: var(--mantine-color-dark-5);
}

@media (hover: hover) {
  :where([data-mantine-color-scheme="light"]) .m_fa109255:not([data-disabled]):hover, :where([data-mantine-color-scheme="light"]) .m_f7e165c3:not([data-disabled]):hover {
    background-color: var(--mantine-color-gray-2);
  }

  :where([data-mantine-color-scheme="dark"]) .m_fa109255:not([data-disabled]):hover, :where([data-mantine-color-scheme="dark"]) .m_f7e165c3:not([data-disabled]):hover {
    background-color: var(--mantine-color-dark-4);
  }
}

@media (hover: none) {
  :where([data-mantine-color-scheme="light"]) .m_fa109255:not([data-disabled]):active, :where([data-mantine-color-scheme="light"]) .m_f7e165c3:not([data-disabled]):active {
    background-color: var(--mantine-color-gray-2);
  }

  :where([data-mantine-color-scheme="dark"]) .m_fa109255:not([data-disabled]):active, :where([data-mantine-color-scheme="dark"]) .m_f7e165c3:not([data-disabled]):active {
    background-color: var(--mantine-color-dark-4);
  }
}

.m_fa109255:not([data-disabled]):where([data-checked]), .m_f7e165c3:not([data-disabled]):where([data-checked]) {
  --chip-icon-color: var(--chip-color);
  color: var(--chip-color);
  background-color: var(--chip-bg);
}

@media (hover: hover) {
  .m_fa109255:not([data-disabled]):where([data-checked]):hover, .m_f7e165c3:not([data-disabled]):where([data-checked]):hover {
    background-color: var(--chip-hover);
  }
}

@media (hover: none) {
  .m_fa109255:not([data-disabled]):where([data-checked]):active, .m_f7e165c3:not([data-disabled]):where([data-checked]):active {
    background-color: var(--chip-hover);
  }
}

.m_9ac86df9 {
  width: calc(var(--chip-icon-size)  + (var(--chip-spacing) / 1.5));
  max-width: calc(var(--chip-icon-size)  + (var(--chip-spacing) / 1.5));
  height: var(--chip-icon-size);
  align-items: center;
  display: flex;
  overflow: hidden;
}

.m_d6d72580 {
  width: var(--chip-icon-size);
  height: var(--chip-icon-size);
  color: var(--chip-icon-color, inherit);
  display: block;
}

.m_bde07329 {
  opacity: 0;
  width: 0;
  height: 0;
  margin: 0;
  padding: 0;
}

.m_bde07329:focus-visible + .m_be049a53 {
  outline: 2px solid var(--mantine-primary-color-filled);
  outline-offset: 2px;
}

.m_b183c0a2 {
  font-family: var(--mantine-font-family-monospace);
  line-height: var(--mantine-line-height);
  padding: 2px calc(var(--mantine-spacing-xs) / 2);
  border-radius: var(--mantine-radius-sm);
  font-size: var(--mantine-font-size-xs);
  margin: 0;
  overflow: auto;
}

:where([data-mantine-color-scheme="light"]) .m_b183c0a2 {
  background-color: var(--code-bg, var(--mantine-color-gray-1));
  color: var(--mantine-color-black);
}

:where([data-mantine-color-scheme="dark"]) .m_b183c0a2 {
  background-color: var(--code-bg, var(--mantine-color-dark-5));
  color: var(--mantine-color-white);
}

.m_b183c0a2[data-block] {
  padding: var(--mantine-spacing-xs);
}

.m_b077c2bc {
  --ci-eye-dropper-icon-size-xs: 14px;
  --ci-eye-dropper-icon-size-sm: 16px;
  --ci-eye-dropper-icon-size-md: 18px;
  --ci-eye-dropper-icon-size-lg: 20px;
  --ci-eye-dropper-icon-size-xl: 22px;
  --ci-eye-dropper-icon-size: var(--ci-eye-dropper-icon-size-sm);
}

.m_c5ccdcab {
  --ci-preview-size-xs: 16px;
  --ci-preview-size-sm: 18px;
  --ci-preview-size-md: 22px;
  --ci-preview-size-lg: 28px;
  --ci-preview-size-xl: 36px;
  --ci-preview-size: var(--ci-preview-size-sm);
}

.m_5ece2cd7 {
  padding: 8px;
}

.m_fee9c77 {
  --cp-width-xs: 180px;
  --cp-width-sm: 200px;
  --cp-width-md: 240px;
  --cp-width-lg: 280px;
  --cp-width-xl: 320px;
  --cp-preview-size-xs: 26px;
  --cp-preview-size-sm: 34px;
  --cp-preview-size-md: 42px;
  --cp-preview-size-lg: 50px;
  --cp-preview-size-xl: 54px;
  --cp-thumb-size-xs: 8px;
  --cp-thumb-size-sm: 12px;
  --cp-thumb-size-md: 16px;
  --cp-thumb-size-lg: 20px;
  --cp-thumb-size-xl: 22px;
  --cp-saturation-height-xs: 100px;
  --cp-saturation-height-sm: 110px;
  --cp-saturation-height-md: 120px;
  --cp-saturation-height-lg: 140px;
  --cp-saturation-height-xl: 160px;
  --cp-preview-size: var(--cp-preview-size-sm);
  --cp-thumb-size: var(--cp-thumb-size-sm);
  --cp-saturation-height: var(--cp-saturation-height-sm);
  --cp-width: var(--cp-width-sm);
  --cp-body-spacing: var(--mantine-spacing-sm);
  width: var(--cp-width);
  padding: 1px;
}

.m_fee9c77:where([data-full-width]) {
  width: 100%;
}

.m_9dddfbac {
  width: var(--cp-preview-size);
  height: var(--cp-preview-size);
}

.m_bffecc3e {
  padding-top: calc(var(--cp-body-spacing) / 2);
  display: flex;
}

.m_3283bb96 {
  flex: 1;
}

.m_3283bb96:not(:only-child) {
  margin-inline-end: var(--mantine-spacing-xs);
}

.m_40d572ba {
  border: 2px solid var(--mantine-color-white);
  width: var(--cp-thumb-size);
  height: var(--cp-thumb-size);
  border-radius: var(--cp-thumb-size);
  left: calc(var(--thumb-x-offset)  - var(--cp-thumb-size) / 2);
  top: calc(var(--thumb-y-offset)  - var(--cp-thumb-size) / 2);
  position: absolute;
  overflow: hidden;
  box-shadow: 0 0 1px #0009;
}

.m_d8ee6fd8 {
  cursor: pointer;
  padding-bottom: calc(var(--cp-swatch-size)  - calc(.25rem * var(--mantine-scale)));
  flex: 0 0 calc(var(--cp-swatch-size)  - calc(.25rem * var(--mantine-scale)));
  margin: 2px;
  height: unset !important;
  width: unset !important;
  min-width: 0 !important;
  min-height: 0 !important;
}

.m_5711e686 {
  margin-top: 5px;
  flex-wrap: wrap;
  margin-inline: -2px;
  display: flex;
}

.m_202a296e {
  --cp-thumb-size-xs: 8px;
  --cp-thumb-size-sm: 12px;
  --cp-thumb-size-md: 16px;
  --cp-thumb-size-lg: 20px;
  --cp-thumb-size-xl: 22px;
  -webkit-tap-highlight-color: transparent;
  height: var(--cp-saturation-height);
  border-radius: var(--mantine-radius-sm);
  margin: calc(var(--cp-thumb-size) / 2);
  position: relative;
}

.m_202a296e:where([data-focus-ring="auto"]):focus:focus-visible .m_40d572ba, .m_202a296e:where([data-focus-ring="always"]):focus .m_40d572ba {
  outline: 2px solid var(--mantine-color-blue-filled);
}

.m_11b3db02 {
  border-radius: var(--mantine-radius-sm);
  inset: calc(var(--cp-thumb-size) * -1 / 2 - calc(.0625rem * var(--mantine-scale)));
  position: absolute;
}

.m_d856d47d {
  --cp-thumb-size-xs: 8px;
  --cp-thumb-size-sm: 12px;
  --cp-thumb-size-md: 16px;
  --cp-thumb-size-lg: 20px;
  --cp-thumb-size-xl: 22px;
  --cp-thumb-size: var(--cp-thumb-size, calc(.75rem * var(--mantine-scale)));
  height: calc(var(--cp-thumb-size)  + calc(.125rem * var(--mantine-scale)));
  margin-inline: calc(var(--cp-thumb-size) / 2);
  outline: none;
  position: relative;
}

.m_d856d47d + .m_d856d47d {
  margin-top: 6px;
}

.m_d856d47d:where([data-focus-ring="auto"]):focus:focus-visible .m_40d572ba, .m_d856d47d:where([data-focus-ring="always"]):focus .m_40d572ba {
  outline: 2px solid var(--mantine-color-blue-filled);
}

:where([data-mantine-color-scheme="light"]) .m_d856d47d {
  --slider-checkers: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme="dark"]) .m_d856d47d {
  --slider-checkers: var(--mantine-color-dark-4);
}

.m_8f327113 {
  top: 0;
  bottom: 0;
  inset-inline: calc(var(--cp-thumb-size) * -1 / 2 - calc(.0625rem * var(--mantine-scale)));
  border-radius: 10000rem;
  position: absolute;
}

.m_de3d2490 {
  --cs-size: 28px;
  --cs-radius: 1000px;
  -webkit-tap-highlight-color: transparent;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: var(--cs-size);
  height: var(--cs-size);
  min-width: var(--cs-size);
  min-height: var(--cs-size);
  border-radius: var(--cs-radius);
  color: inherit;
  border: none;
  line-height: 1;
  text-decoration: none;
  display: block;
  position: relative;
}

[data-mantine-color-scheme="light"] .m_de3d2490 {
  --alpha-overlay-color: var(--mantine-color-gray-3);
  --alpha-overlay-bg: var(--mantine-color-white);
}

[data-mantine-color-scheme="dark"] .m_de3d2490 {
  --alpha-overlay-color: var(--mantine-color-dark-4);
  --alpha-overlay-bg: var(--mantine-color-dark-7);
}

.m_862f3d1b {
  border-radius: var(--cs-radius);
  position: absolute;
  inset: 0;
}

.m_98ae7f22 {
  border-radius: var(--cs-radius);
  z-index: 1;
  box-shadow: #0000001a 0 0 0 calc(.0625rem * var(--mantine-scale)) inset, #00000026 0 0 calc(.25rem * var(--mantine-scale)) inset;
  position: absolute;
  inset: 0;
}

.m_95709ac0 {
  border-radius: var(--cs-radius);
  background-position: 0 0, 0 4px, 4px -4px, -4px 0;
  background-size: 8px 8px;
  background-image: linear-gradient(45deg, var(--alpha-overlay-color) 25%, transparent 25%), linear-gradient(-45deg, var(--alpha-overlay-color) 25%, transparent 25%), linear-gradient(45deg, transparent 75%, var(--alpha-overlay-color) 75%), linear-gradient(-45deg, var(--alpha-overlay-bg) 75%, var(--alpha-overlay-color) 75%);
  position: absolute;
  inset: 0;
}

.m_93e74e3 {
  border-radius: var(--cs-radius);
  z-index: 2;
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
  inset: 0;
}

.m_88b62a41 {
  --combobox-padding: 4px;
  padding: var(--combobox-padding);
}

.m_88b62a41:has([data-mantine-scrollbar]) .m_985517d8 {
  max-width: calc(100% + var(--combobox-padding));
}

.m_88b62a41[data-composed] {
  padding-inline-end: 0;
}

.m_88b62a41[data-hidden] {
  display: none;
}

.m_88b62a41, .m_b2821a6e {
  --combobox-option-padding-xs: 4px 8px;
  --combobox-option-padding-sm: 6px 10px;
  --combobox-option-padding-md: 8px 12px;
  --combobox-option-padding-lg: 10px 16px;
  --combobox-option-padding-xl: 14px 20px;
  --combobox-option-padding: var(--combobox-option-padding-sm);
}

.m_92253aa5 {
  padding: var(--combobox-option-padding);
  font-size: var(--combobox-option-fz, var(--mantine-font-size-sm));
  border-radius: var(--mantine-radius-default);
  color: inherit;
  cursor: pointer;
  word-break: break-word;
  background-color: #0000;
}

.m_92253aa5:where([data-combobox-selected]) {
  background-color: var(--mantine-primary-color-filled);
  color: var(--mantine-color-white);
}

.m_92253aa5:where([data-combobox-disabled]) {
  cursor: not-allowed;
  opacity: .35;
}

@media (hover: hover) {
  :where([data-mantine-color-scheme="light"]) .m_92253aa5:hover:where(:not([data-combobox-selected], [data-combobox-disabled])) {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="dark"]) .m_92253aa5:hover:where(:not([data-combobox-selected], [data-combobox-disabled])) {
    background-color: var(--mantine-color-dark-7);
  }
}

@media (hover: none) {
  :where([data-mantine-color-scheme="light"]) .m_92253aa5:active:where(:not([data-combobox-selected], [data-combobox-disabled])) {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="dark"]) .m_92253aa5:active:where(:not([data-combobox-selected], [data-combobox-disabled])) {
    background-color: var(--mantine-color-dark-7);
  }
}

.m_985517d8 {
  margin-inline: calc(var(--combobox-padding) * -1);
  margin-top: calc(var(--combobox-padding) * -1);
  width: calc(100% + var(--combobox-padding) * 2);
  border-top-width: 0;
  margin-bottom: var(--combobox-padding);
  border-inline-width: 0;
  border-end-end-radius: 0;
  border-end-start-radius: 0;
  position: relative;
}

:where([data-mantine-color-scheme="light"]) .m_985517d8, :where([data-mantine-color-scheme="light"]) .m_985517d8:focus {
  border-color: var(--mantine-color-gray-2);
}

:where([data-mantine-color-scheme="dark"]) .m_985517d8, :where([data-mantine-color-scheme="dark"]) .m_985517d8:focus {
  border-color: var(--mantine-color-dark-4);
}

:where([data-mantine-color-scheme="light"]) .m_985517d8 {
  background-color: var(--mantine-color-white);
}

:where([data-mantine-color-scheme="dark"]) .m_985517d8 {
  background-color: var(--mantine-color-dark-7);
}

.m_2530cd1d {
  font-size: var(--combobox-option-fz, var(--mantine-font-size-sm));
  text-align: center;
  padding: var(--combobox-option-padding);
  color: var(--mantine-color-dimmed);
}

.m_858f94bd, .m_82b967cb {
  font-size: var(--combobox-option-fz, var(--mantine-font-size-sm));
  margin-inline: calc(var(--combobox-padding) * -1);
  padding: var(--combobox-option-padding);
  border: 0 solid #0000;
}

:where([data-mantine-color-scheme="light"]) .m_858f94bd, :where([data-mantine-color-scheme="light"]) .m_82b967cb {
  border-color: var(--mantine-color-gray-2);
}

:where([data-mantine-color-scheme="dark"]) .m_858f94bd, :where([data-mantine-color-scheme="dark"]) .m_82b967cb {
  border-color: var(--mantine-color-dark-4);
}

.m_82b967cb {
  margin-top: var(--combobox-padding);
  margin-bottom: calc(var(--combobox-padding) * -1);
  border-top-width: 1px;
}

.m_858f94bd {
  margin-bottom: var(--combobox-padding);
  margin-top: calc(var(--combobox-padding) * -1);
  border-bottom-width: 1px;
}

.m_254f3e4f:has(.m_2bb2e9e5:only-child) {
  display: none;
}

.m_2bb2e9e5 {
  color: var(--mantine-color-dimmed);
  font-size: calc(var(--combobox-option-fz, var(--mantine-font-size-sm)) * .85);
  padding: var(--combobox-option-padding);
  align-items: center;
  font-weight: 500;
  display: flex;
  position: relative;
}

.m_2bb2e9e5:after {
  content: "";
  height: 1px;
  flex: 1;
  margin-inline-start: var(--mantine-spacing-xs);
  inset-inline: 0;
}

:where([data-mantine-color-scheme="light"]) .m_2bb2e9e5:after {
  background-color: var(--mantine-color-gray-2);
}

:where([data-mantine-color-scheme="dark"]) .m_2bb2e9e5:after {
  background-color: var(--mantine-color-dark-4);
}

.m_2bb2e9e5:only-child {
  display: none;
}

.m_2943220b {
  --combobox-chevron-size-xs: 14px;
  --combobox-chevron-size-sm: 18px;
  --combobox-chevron-size-md: 20px;
  --combobox-chevron-size-lg: 24px;
  --combobox-chevron-size-xl: 28px;
  --combobox-chevron-size: var(--combobox-chevron-size-sm);
}

:where([data-mantine-color-scheme="light"]) .m_2943220b {
  --_combobox-chevron-color: var(--combobox-chevron-color, var(--mantine-color-gray-6));
}

:where([data-mantine-color-scheme="dark"]) .m_2943220b {
  --_combobox-chevron-color: var(--combobox-chevron-color, var(--mantine-color-dark-3));
}

.m_2943220b {
  width: var(--combobox-chevron-size);
  height: var(--combobox-chevron-size);
  color: var(--_combobox-chevron-color);
}

.m_2943220b:where([data-error]) {
  color: var(--combobox-chevron-color, var(--mantine-color-error));
}

.m_390b5f4 {
  align-items: center;
  gap: 8px;
  display: flex;
}

.m_390b5f4:where([data-reverse]) {
  justify-content: space-between;
}

.m_8ee53fc2 {
  opacity: .4;
  width: .8em;
  min-width: .8em;
  height: .8em;
}

:where([data-combobox-selected]) .m_8ee53fc2 {
  opacity: 1;
}

.m_7485cace {
  --container-size-xs: 540px;
  --container-size-sm: 720px;
  --container-size-md: 960px;
  --container-size-lg: 1140px;
  --container-size-xl: 1320px;
  --container-size: var(--container-size-md);
  max-width: var(--container-size);
  padding-inline: var(--mantine-spacing-md);
  margin-inline: auto;
}

.m_7485cace:where([data-fluid]) {
  max-width: 100%;
}

.m_e2125a27 {
  --dialog-size-xs: 160px;
  --dialog-size-sm: 200px;
  --dialog-size-md: 340px;
  --dialog-size-lg: 400px;
  --dialog-size-xl: 500px;
  --dialog-size: var(--dialog-size-md);
  width: var(--dialog-size);
  max-width: calc(100vw - var(--mantine-spacing-xl) * 2);
  min-height: 50px;
  position: relative;
}

.m_5abab665 {
  top: calc(var(--mantine-spacing-md) / 2);
  position: absolute;
  inset-inline-end: calc(var(--mantine-spacing-md) / 2);
}

.m_3eebeb36 {
  --divider-size-xs: 1px;
  --divider-size-sm: 2px;
  --divider-size-md: 3px;
  --divider-size-lg: 4px;
  --divider-size-xl: 5px;
  --divider-size: var(--divider-size-xs);
}

:where([data-mantine-color-scheme="light"]) .m_3eebeb36 {
  --divider-color: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme="dark"]) .m_3eebeb36 {
  --divider-color: var(--mantine-color-dark-4);
}

.m_3eebeb36:where([data-orientation="horizontal"]) {
  border-top: var(--divider-size) var(--divider-border-style, solid) var(--divider-color);
}

.m_3eebeb36:where([data-orientation="vertical"]) {
  border-inline-start: var(--divider-size) var(--divider-border-style, solid) var(--divider-color);
  align-self: stretch;
  height: auto;
}

.m_3eebeb36:where([data-with-label]) {
  border: 0;
}

.m_9e365f20 {
  font-size: var(--mantine-font-size-xs);
  color: var(--mantine-color-dimmed);
  white-space: nowrap;
  align-items: center;
  display: flex;
}

.m_9e365f20:where([data-position="left"]):before, .m_9e365f20:where([data-position="right"]):after {
  display: none;
}

.m_9e365f20:before {
  content: "";
  border-top: var(--divider-size) var(--divider-border-style, solid) var(--divider-color);
  height: 1px;
  flex: 1;
  margin-inline-end: var(--mantine-spacing-xs);
}

.m_9e365f20:after {
  content: "";
  border-top: var(--divider-size) var(--divider-border-style, solid) var(--divider-color);
  height: 1px;
  flex: 1;
  margin-inline-start: var(--mantine-spacing-xs);
}

.m_f11b401e {
  --drawer-size-xs: 320px;
  --drawer-size-sm: 380px;
  --drawer-size-md: 440px;
  --drawer-size-lg: 620px;
  --drawer-size-xl: 780px;
  --drawer-size: var(--drawer-size-md);
  --drawer-offset: 0rem;
}

.m_5a7c2c9 {
  z-index: 1000;
}

.m_b8a05bbd {
  flex: var(--drawer-flex, 0 0 var(--drawer-size));
  height: var(--drawer-height, calc(100% - var(--drawer-offset) * 2));
  margin: var(--drawer-offset);
  max-width: calc(100% - var(--drawer-offset) * 2);
  max-height: calc(100% - var(--drawer-offset) * 2);
  overflow-y: auto;
}

.m_b8a05bbd[data-hidden] {
  pointer-events: none;
  opacity: 0 !important;
}

.m_31cd769a {
  justify-content: var(--drawer-justify, flex-start);
  align-items: var(--drawer-align, flex-start);
  display: flex;
}

.m_e9408a47 {
  padding: var(--mantine-spacing-lg);
  padding-top: var(--mantine-spacing-xs);
  border-radius: var(--fieldset-radius, var(--mantine-radius-default));
  min-inline-size: auto;
}

.m_84c9523a {
  border: 1px solid;
}

:where([data-mantine-color-scheme="light"]) .m_84c9523a {
  border-color: var(--mantine-color-gray-3);
  background-color: var(--mantine-color-white);
}

:where([data-mantine-color-scheme="dark"]) .m_84c9523a {
  border-color: var(--mantine-color-dark-4);
  background-color: var(--mantine-color-dark-7);
}

.m_ef274e49 {
  border: 1px solid;
}

:where([data-mantine-color-scheme="light"]) .m_ef274e49 {
  border-color: var(--mantine-color-gray-3);
  background-color: var(--mantine-color-gray-0);
}

:where([data-mantine-color-scheme="dark"]) .m_ef274e49 {
  border-color: var(--mantine-color-dark-4);
  background-color: var(--mantine-color-dark-6);
}

.m_eda993d3 {
  border: 0;
  border-radius: 0;
  padding: 0;
}

.m_90794832 {
  font-size: var(--mantine-font-size-sm);
}

.m_74ca27fe {
  margin-bottom: var(--mantine-spacing-sm);
  padding: 0;
}

.m_8478a6da {
  container: mantine-grid / inline-size;
}

.m_410352e9 {
  --grid-overflow: visible;
  --grid-margin: calc(var(--grid-gutter) / -2);
  --grid-col-padding: calc(var(--grid-gutter) / 2);
  overflow: var(--grid-overflow);
}

.m_dee7bd2f {
  width: calc(100% + var(--grid-gutter));
  justify-content: var(--grid-justify);
  align-items: var(--grid-align);
  margin: var(--grid-margin);
  flex-wrap: wrap;
  display: flex;
}

.m_96bdd299 {
  --col-flex-grow: 0;
  --col-offset: 0rem;
  flex-shrink: 0;
  order: var(--col-order);
  flex-basis: var(--col-flex-basis);
  width: var(--col-width);
  max-width: var(--col-max-width);
  flex-grow: var(--col-flex-grow);
  padding: var(--grid-col-padding);
  margin-inline-start: var(--col-offset);
}

.m_9e117634 {
  -o-object-fit: var(--image-object-fit, cover);
  object-fit: var(--image-object-fit, cover);
  border-radius: var(--image-radius, 0);
  flex: 0;
  width: 100%;
  display: block;
}

@keyframes m_885901b1 {
  0% {
    opacity: .6;
    transform: scale(0);
  }

  to {
    opacity: 0;
    transform: scale(2.8);
  }
}

.m_e5262200 {
  --indicator-size: 10px;
  --indicator-color: var(--mantine-primary-color-filled);
  display: block;
  position: relative;
}

.m_e5262200:where([data-inline]) {
  display: inline-block;
}

.m_760d1fb1 {
  top: var(--indicator-top);
  left: var(--indicator-left);
  right: var(--indicator-right);
  bottom: var(--indicator-bottom);
  transform: translate(var(--indicator-translate-x), var(--indicator-translate-y));
  min-width: var(--indicator-size);
  height: var(--indicator-size);
  border-radius: var(--indicator-radius, 1000rem);
  z-index: var(--indicator-z-index, 200);
  font-size: var(--mantine-font-size-xs);
  background-color: var(--indicator-color);
  color: var(--indicator-text-color, var(--mantine-color-white));
  white-space: nowrap;
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
}

.m_760d1fb1:before {
  content: "";
  background-color: var(--indicator-color);
  border-radius: var(--indicator-radius, 1000rem);
  z-index: -1;
  position: absolute;
  inset: 0;
}

.m_760d1fb1:where([data-with-label]) {
  padding-inline: calc(var(--mantine-spacing-xs) / 2);
}

.m_760d1fb1:where([data-with-border]) {
  border: 2px solid var(--mantine-color-body);
}

.m_760d1fb1[data-processing]:before {
  animation: 1s linear infinite m_885901b1;
}

.m_5f75b09e {
  --label-lh-xs: 16px;
  --label-lh-sm: 20px;
  --label-lh-md: 24px;
  --label-lh-lg: 30px;
  --label-lh-xl: 36px;
  --label-lh: var(--label-lh-sm);
}

.m_5f75b09e[data-label-position="left"] {
  --label-order: 1;
  --label-offset-end: var(--mantine-spacing-sm);
  --label-offset-start: 0;
}

.m_5f75b09e[data-label-position="right"] {
  --label-order: 2;
  --label-offset-end: 0;
  --label-offset-start: var(--mantine-spacing-sm);
}

.m_5f6e695e {
  display: flex;
}

.m_d3ea56bb {
  --label-cursor: var(--mantine-cursor-type);
  -webkit-tap-highlight-color: transparent;
  font-size: var(--label-fz, var(--mantine-font-size-sm));
  line-height: var(--label-lh);
  cursor: var(--label-cursor);
  flex-direction: column;
  order: var(--label-order);
  display: inline-flex;
}

fieldset:disabled .m_d3ea56bb, .m_d3ea56bb[data-disabled] {
  --label-cursor: not-allowed;
}

.m_8ee546b8 {
  cursor: var(--label-cursor);
  color: inherit;
  padding-inline-start: var(--label-offset-start);
  padding-inline-end: var(--label-offset-end);
}

:where([data-mantine-color-scheme="light"]) fieldset:disabled .m_8ee546b8, :where([data-mantine-color-scheme="light"]) .m_8ee546b8:where([data-disabled]) {
  color: var(--mantine-color-gray-5);
}

:where([data-mantine-color-scheme="dark"]) fieldset:disabled .m_8ee546b8, :where([data-mantine-color-scheme="dark"]) .m_8ee546b8:where([data-disabled]) {
  color: var(--mantine-color-dark-3);
}

.m_328f68c0, .m_8e8a99cc {
  margin-top: calc(var(--mantine-spacing-xs) / 2);
  padding-inline-start: var(--label-offset-start);
  padding-inline-end: var(--label-offset-end);
}

.m_dc6f14e2 {
  --kbd-fz-xs: 10px;
  --kbd-fz-sm: 12px;
  --kbd-fz-md: 14px;
  --kbd-fz-lg: 16px;
  --kbd-fz-xl: 20px;
  --kbd-fz: var(--kbd-fz-sm);
  --kbd-padding-xs: 2px 4px;
  --kbd-padding-sm: 3px 5px;
  --kbd-padding-md: 4px 7px;
  --kbd-padding-lg: 5px 9px;
  --kbd-padding-xl: 8px 14px;
  --kbd-padding: var(--kbd-padding-sm);
  font-family: var(--mantine-font-family-monospace);
  line-height: var(--mantine-line-height);
  padding: var(--kbd-padding);
  font-weight: 700;
  font-size: var(--kbd-fz);
  border-radius: var(--mantine-radius-sm);
  unicode-bidi: embed;
  border: 1px solid;
  border-bottom-width: 3px;
}

:where([data-mantine-color-scheme="light"]) .m_dc6f14e2 {
  border-color: var(--mantine-color-gray-3);
  color: var(--mantine-color-gray-7);
  background-color: var(--mantine-color-gray-0);
}

:where([data-mantine-color-scheme="dark"]) .m_dc6f14e2 {
  border-color: var(--mantine-color-dark-4);
  color: var(--mantine-color-dark-0);
  background-color: var(--mantine-color-dark-6);
}

.m_abbac491 {
  --list-fz: var(--mantine-font-size-md);
  --list-lh: var(--mantine-line-height-md);
  font-size: var(--list-fz);
  line-height: var(--list-lh);
  margin: 0;
  padding: 0;
  list-style-position: inside;
}

.m_abbac491:where([data-with-padding]) {
  padding-inline-start: var(--mantine-spacing-md);
}

.m_abb6bec2 {
  white-space: nowrap;
  line-height: var(--list-lh);
}

.m_abb6bec2:where([data-with-icon]) {
  list-style: none;
}

.m_abb6bec2:where([data-with-icon]) .m_75cd9f71 {
  --li-direction: row;
  --li-align: center;
}

.m_abb6bec2:where(:not(:first-of-type)) {
  margin-top: var(--list-spacing, 0);
}

.m_abb6bec2:where([data-centered]) {
  line-height: 1;
}

.m_75cd9f71 {
  flex-direction: var(--li-direction, column);
  align-items: var(--li-align, flex-start);
  white-space: normal;
  display: inline-flex;
}

.m_60f83e5b {
  vertical-align: middle;
  margin-inline-end: var(--mantine-spacing-sm);
  display: inline-block;
}

.m_6e45937b {
  z-index: var(--lo-z-index);
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
  inset: 0;
  overflow: hidden;
}

.m_e8eb006c {
  z-index: calc(var(--lo-z-index)  + 1);
  position: relative;
}

.m_df587f17 {
  z-index: var(--lo-z-index);
}

.m_bcb3f3c2 {
  color: var(--mantine-color-black);
}

:where([data-mantine-color-scheme="light"]) .m_bcb3f3c2 {
  background-color: var(--mark-bg-light);
}

:where([data-mantine-color-scheme="dark"]) .m_bcb3f3c2 {
  background-color: var(--mark-bg-dark);
}

.m_dc9b7c9f {
  padding: 4px;
}

.m_9bfac126 {
  color: var(--mantine-color-dimmed);
  font-weight: 500;
  font-size: var(--mantine-font-size-xs);
  padding: calc(var(--mantine-spacing-xs) / 2) var(--mantine-spacing-sm);
  cursor: default;
}

.m_efdf90cb {
  border-top: 1px solid;
  margin-top: 4px;
  margin-bottom: 4px;
}

:where([data-mantine-color-scheme="light"]) .m_efdf90cb {
  border-color: var(--mantine-color-gray-2);
}

:where([data-mantine-color-scheme="dark"]) .m_efdf90cb {
  border-color: var(--mantine-color-dark-4);
}

.m_99ac2aa1 {
  font-size: var(--mantine-font-size-sm);
  width: 100%;
  padding: calc(var(--mantine-spacing-xs) / 1.5) var(--mantine-spacing-sm);
  border-radius: var(--popover-radius, var(--mantine-radius-default));
  color: var(--menu-item-color, var(--mantine-color-text));
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  align-items: center;
  display: flex;
}

.m_99ac2aa1:where([data-disabled], :disabled) {
  color: var(--mantine-color-dimmed);
  opacity: .6;
  cursor: not-allowed;
}

:where([data-mantine-color-scheme="light"]) .m_99ac2aa1:where([data-hovered]):where(:not(:disabled, [data-disabled])) {
  background-color: var(--menu-item-hover, var(--mantine-color-gray-1));
}

:where([data-mantine-color-scheme="dark"]) .m_99ac2aa1:where([data-hovered]):where(:not(:disabled, [data-disabled])) {
  background-color: var(--menu-item-hover, var(--mantine-color-dark-4));
}

.m_5476e0d3 {
  flex: 1;
}

.m_8b75e504 {
  justify-content: center;
  align-items: center;
  display: flex;
}

.m_8b75e504:where([data-position="left"]) {
  margin-inline-end: var(--mantine-spacing-xs);
}

.m_8b75e504:where([data-position="right"]) {
  margin-inline-start: var(--mantine-spacing-xs);
}

.m_9df02822 {
  --modal-size-xs: 320px;
  --modal-size-sm: 380px;
  --modal-size-md: 440px;
  --modal-size-lg: 620px;
  --modal-size-xl: 780px;
  --modal-size: var(--modal-size-md);
  --modal-y-offset: 5dvh;
  --modal-x-offset: 5vw;
}

.m_9df02822[data-full-screen] {
  --modal-border-radius: 0 !important;
}

.m_9df02822[data-full-screen] .m_54c44539 {
  --modal-content-flex: 0 0 100%;
  --modal-content-max-height: auto;
  --modal-content-height: 100dvh;
}

.m_9df02822[data-full-screen] .m_1f958f16 {
  --modal-inner-y-offset: 0;
  --modal-inner-x-offset: 0;
}

.m_9df02822[data-centered] .m_1f958f16 {
  --modal-inner-align: center;
}

.m_d0e2b9cd {
  border-start-start-radius: var(--modal-radius, var(--mantine-radius-default));
  border-start-end-radius: var(--modal-radius, var(--mantine-radius-default));
}

.m_54c44539 {
  flex: var(--modal-content-flex, 0 0 var(--modal-size));
  max-width: 100%;
  max-height: var(--modal-content-max-height, calc(100dvh - var(--modal-y-offset) * 2));
  height: var(--modal-content-height, auto);
  overflow-y: auto;
}

.m_54c44539[data-full-screen] {
  border-radius: 0;
}

.m_54c44539[data-hidden] {
  pointer-events: none;
  opacity: 0 !important;
}

.m_1f958f16 {
  justify-content: center;
  align-items: var(--modal-inner-align, flex-start);
  padding-top: var(--modal-inner-y-offset, var(--modal-y-offset));
  padding-bottom: var(--modal-inner-y-offset, var(--modal-y-offset));
  padding-inline: var(--modal-inner-x-offset, var(--modal-x-offset));
  display: flex;
}

.m_f0824112 {
  --nl-bg: var(--mantine-primary-color-light);
  --nl-hover: var(--mantine-primary-color-light-hover);
  --nl-color: var(--mantine-primary-color-light-color);
  width: 100%;
  padding: 8px var(--mantine-spacing-sm);
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  align-items: center;
  display: flex;
}

@media (hover: hover) {
  :where([data-mantine-color-scheme="light"]) .m_f0824112:hover {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="dark"]) .m_f0824112:hover {
    background-color: var(--mantine-color-dark-6);
  }
}

@media (hover: none) {
  :where([data-mantine-color-scheme="light"]) .m_f0824112:active {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="dark"]) .m_f0824112:active {
    background-color: var(--mantine-color-dark-6);
  }
}

.m_f0824112:where([data-disabled]) {
  opacity: .4;
  pointer-events: none;
}

.m_f0824112:where([data-active], [aria-current="page"]) {
  background-color: var(--nl-bg);
  color: var(--nl-color);
}

@media (hover: hover) {
  .m_f0824112:where([data-active], [aria-current="page"]):hover {
    background-color: var(--nl-hover);
  }
}

@media (hover: none) {
  .m_f0824112:where([data-active], [aria-current="page"]):active {
    background-color: var(--nl-hover);
  }
}

.m_f0824112:where([data-active], [aria-current="page"]) .m_57492dcc {
  --description-opacity: .9;
  --description-color: var(--nl-color);
}

.m_690090b5 {
  justify-content: center;
  align-items: center;
  transition: transform .15s;
  display: flex;
}

.m_690090b5 > svg {
  display: block;
}

.m_690090b5:where([data-position="left"]) {
  margin-inline-end: var(--mantine-spacing-sm);
}

.m_690090b5:where([data-position="right"]) {
  margin-inline-start: var(--mantine-spacing-sm);
}

.m_690090b5:where([data-rotate]) {
  transform: rotate(90deg);
}

.m_1f6ac4c4 {
  font-size: var(--mantine-font-size-sm);
}

.m_f07af9d2 {
  text-overflow: ellipsis;
  flex: 1;
  overflow: hidden;
}

.m_f07af9d2:where([data-no-wrap]) {
  white-space: nowrap;
}

.m_57492dcc {
  font-size: var(--mantine-font-size-xs);
  opacity: var(--description-opacity, 1);
  color: var(--description-color, var(--mantine-color-dimmed));
  text-overflow: ellipsis;
  display: block;
  overflow: hidden;
}

:where([data-no-wrap]) .m_57492dcc {
  white-space: nowrap;
}

.m_e17b862f {
  padding-inline-start: var(--nl-offset, var(--mantine-spacing-lg));
}

.m_1fd8a00b {
  transform: rotate(-90deg);
}

.m_a513464 {
  --notification-radius: var(--mantine-radius-default);
  --notification-color: var(--mantine-primary-color-filled);
  box-sizing: border-box;
  padding-inline-start: 22px;
  padding-inline-end: var(--mantine-spacing-xs);
  padding-top: var(--mantine-spacing-xs);
  padding-bottom: var(--mantine-spacing-xs);
  border-radius: var(--notification-radius);
  box-shadow: var(--mantine-shadow-lg);
  align-items: center;
  display: flex;
  position: relative;
  overflow: hidden;
}

.m_a513464:before {
  content: "";
  width: 6px;
  top: var(--notification-radius);
  bottom: var(--notification-radius);
  border-radius: var(--notification-radius);
  background-color: var(--notification-color);
  display: block;
  position: absolute;
  inset-inline-start: 4px;
}

:where([data-mantine-color-scheme="light"]) .m_a513464 {
  background-color: var(--mantine-color-white);
}

:where([data-mantine-color-scheme="dark"]) .m_a513464 {
  background-color: var(--mantine-color-dark-6);
}

.m_a513464:where([data-with-icon]) {
  padding-inline-start: var(--mantine-spacing-xs);
}

.m_a513464:where([data-with-icon]):before {
  display: none;
}

:where([data-mantine-color-scheme="light"]) .m_a513464:where([data-with-border]) {
  border: 1px solid var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme="dark"]) .m_a513464:where([data-with-border]) {
  border: 1px solid var(--mantine-color-dark-4);
}

.m_a4ceffb {
  box-sizing: border-box;
  background-color: var(--notification-color);
  width: 28px;
  height: 28px;
  color: var(--mantine-color-white);
  border-radius: 28px;
  justify-content: center;
  align-items: center;
  margin-inline-end: var(--mantine-spacing-md);
  display: flex;
}

.m_b0920b15 {
  margin-inline-end: var(--mantine-spacing-md);
}

.m_a49ed24 {
  flex: 1;
  margin-inline-end: var(--mantine-spacing-xs);
  overflow: hidden;
}

.m_3feedf16 {
  text-overflow: ellipsis;
  font-size: var(--mantine-font-size-sm);
  line-height: var(--mantine-line-height-sm);
  margin-bottom: 2px;
  font-weight: 500;
  overflow: hidden;
}

:where([data-mantine-color-scheme="light"]) .m_3feedf16 {
  color: var(--mantine-color-gray-9);
}

:where([data-mantine-color-scheme="dark"]) .m_3feedf16 {
  color: var(--mantine-color-white);
}

.m_3d733a3a {
  font-size: var(--mantine-font-size-sm);
  line-height: var(--mantine-line-height-sm);
  text-overflow: ellipsis;
  overflow: hidden;
}

:where([data-mantine-color-scheme="light"]) .m_3d733a3a {
  color: var(--mantine-color-black);
}

:where([data-mantine-color-scheme="dark"]) .m_3d733a3a {
  color: var(--mantine-color-dark-0);
}

:where([data-mantine-color-scheme="light"]) .m_3d733a3a:where([data-with-title]) {
  color: var(--mantine-color-gray-6);
}

:where([data-mantine-color-scheme="dark"]) .m_3d733a3a:where([data-with-title]) {
  color: var(--mantine-color-dark-2);
}

@media (hover: hover) {
  :where([data-mantine-color-scheme="light"]) .m_919a4d88:hover {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="dark"]) .m_919a4d88:hover {
    background-color: var(--mantine-color-dark-8);
  }
}

@media (hover: none) {
  :where([data-mantine-color-scheme="light"]) .m_919a4d88:active {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="dark"]) .m_919a4d88:active {
    background-color: var(--mantine-color-dark-8);
  }
}

.m_e2f5cd4e {
  --ni-right-section-width-xs: 17px;
  --ni-right-section-width-sm: 24px;
  --ni-right-section-width-md: 27px;
  --ni-right-section-width-lg: 31px;
  --ni-right-section-width-xl: 34px;
}

.m_95e17d22 {
  --ni-chevron-size-xs: 10px;
  --ni-chevron-size-sm: 14px;
  --ni-chevron-size-md: 16px;
  --ni-chevron-size-lg: 18px;
  --ni-chevron-size-xl: 20px;
  --ni-chevron-size: var(--ni-chevron-size-sm);
  width: 100%;
  height: calc(var(--input-height)  - calc(.125rem * var(--mantine-scale)));
  max-width: calc(var(--ni-chevron-size) * 1.7);
  flex-direction: column;
  margin-inline-start: auto;
  display: flex;
}

.m_80b4b171 {
  --control-border: 1px solid var(--input-bd);
  --control-radius: calc(var(--input-radius)  - calc(.0625rem * var(--mantine-scale)));
  width: 100%;
  height: calc(var(--input-height) / 2 - calc(.0625rem * var(--mantine-scale)));
  border-inline-start: var(--control-border);
  color: var(--mantine-color-text);
  cursor: pointer;
  background-color: #0000;
  flex: 0 0 50%;
  justify-content: center;
  align-items: center;
  padding: 0;
  display: flex;
}

.m_80b4b171:where(:disabled) {
  cursor: not-allowed;
  opacity: .6;
  background-color: #0000;
}

:where([data-mantine-color-scheme="light"]) .m_80b4b171:where(:disabled) {
  color: var(--mantine-color-gray-4);
}

:where([data-mantine-color-scheme="dark"]) .m_80b4b171:where(:disabled) {
  color: var(--mantine-color-dark-3);
}

.m_e2f5cd4e[data-error] :where(.m_80b4b171) {
  color: var(--mantine-color-error);
}

@media (hover: hover) {
  :where([data-mantine-color-scheme="light"]) .m_80b4b171:hover {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="dark"]) .m_80b4b171:hover {
    background-color: var(--mantine-color-dark-4);
  }
}

@media (hover: none) {
  :where([data-mantine-color-scheme="light"]) .m_80b4b171:active {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="dark"]) .m_80b4b171:active {
    background-color: var(--mantine-color-dark-4);
  }
}

.m_80b4b171:where(:first-of-type) {
  border-radius: 0;
  border-start-end-radius: var(--control-radius);
}

.m_80b4b171:last-of-type {
  border-radius: 0;
  border-end-end-radius: var(--control-radius);
}

.m_4addd315 {
  --pagination-control-size-xs: 22px;
  --pagination-control-size-sm: 26px;
  --pagination-control-size-md: 32px;
  --pagination-control-size-lg: 38px;
  --pagination-control-size-xl: 44px;
  --pagination-control-size: var(--pagination-control-size-md);
  --pagination-control-fz: var(--mantine-font-size-md);
  --pagination-active-bg: var(--mantine-primary-color-filled);
}

.m_326d024a {
  cursor: pointer;
  color: var(--mantine-color-text);
  height: var(--pagination-control-size);
  min-width: var(--pagination-control-size);
  font-size: var(--pagination-control-fz);
  border-radius: var(--pagination-control-radius, var(--mantine-radius-default));
  border: 1px solid;
  justify-content: center;
  align-items: center;
  line-height: 1;
  display: flex;
}

.m_326d024a:where([data-with-padding]) {
  padding: calc(var(--pagination-control-size) / 4);
}

.m_326d024a:where(:disabled, [data-disabled]) {
  cursor: not-allowed;
  opacity: .4;
}

:where([data-mantine-color-scheme="light"]) .m_326d024a {
  border-color: var(--mantine-color-gray-4);
  background-color: var(--mantine-color-white);
}

@media (hover: hover) {
  :where([data-mantine-color-scheme="light"]) .m_326d024a:hover:where(:not(:disabled, [data-disabled])) {
    background-color: var(--mantine-color-gray-0);
  }
}

@media (hover: none) {
  :where([data-mantine-color-scheme="light"]) .m_326d024a:active:where(:not(:disabled, [data-disabled])) {
    background-color: var(--mantine-color-gray-0);
  }
}

:where([data-mantine-color-scheme="dark"]) .m_326d024a {
  border-color: var(--mantine-color-dark-4);
  background-color: var(--mantine-color-dark-6);
}

@media (hover: hover) {
  :where([data-mantine-color-scheme="dark"]) .m_326d024a:hover:where(:not(:disabled, [data-disabled])) {
    background-color: var(--mantine-color-dark-5);
  }
}

@media (hover: none) {
  :where([data-mantine-color-scheme="dark"]) .m_326d024a:active:where(:not(:disabled, [data-disabled])) {
    background-color: var(--mantine-color-dark-5);
  }
}

.m_326d024a:where([data-active]) {
  background-color: var(--pagination-active-bg);
  border-color: var(--pagination-active-bg);
  color: var(--pagination-active-color, var(--mantine-color-white));
}

@media (hover: hover) {
  .m_326d024a:where([data-active]):hover {
    background-color: var(--pagination-active-bg);
  }
}

@media (hover: none) {
  .m_326d024a:where([data-active]):active {
    background-color: var(--pagination-active-bg);
  }
}

.m_4ad7767d {
  height: var(--pagination-control-size);
  min-width: var(--pagination-control-size);
  pointer-events: none;
  justify-content: center;
  align-items: center;
  display: flex;
}

.m_7cda1cd6 {
  --pill-fz-xs: 10px;
  --pill-fz-sm: 12px;
  --pill-fz-md: 14px;
  --pill-fz-lg: 16px;
  --pill-fz-xl: 18px;
  --pill-height-xs: 18px;
  --pill-height-sm: 22px;
  --pill-height-md: 25px;
  --pill-height-lg: 28px;
  --pill-height-xl: 32px;
  --pill-fz: var(--pill-fz-sm);
  --pill-height: var(--pill-height-sm);
  font-size: var(--pill-fz);
  height: var(--pill-height);
  border-radius: var(--pill-radius, 1000rem);
  white-space: nowrap;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  flex: 0;
  align-items: center;
  max-width: 100%;
  padding-inline: .8em;
  line-height: 1;
  display: inline-flex;
}

:where([data-mantine-color-scheme="dark"]) .m_7cda1cd6 {
  background-color: var(--mantine-color-dark-7);
  color: var(--mantine-color-dark-0);
}

:where([data-mantine-color-scheme="light"]) .m_7cda1cd6 {
  color: var(--mantine-color-black);
}

.m_7cda1cd6:where([data-with-remove]:not(:has(button:disabled))) {
  padding-inline-end: 0;
}

.m_7cda1cd6:where([data-disabled], :has(button:disabled)) {
  cursor: not-allowed;
}

:where([data-mantine-color-scheme="light"]) .m_44da308b {
  background-color: var(--mantine-color-gray-1);
}

:where([data-mantine-color-scheme="light"]) .m_44da308b:where([data-disabled], :has(button:disabled)) {
  background-color: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme="light"]) .m_e3a01f8 {
  background-color: var(--mantine-color-white);
}

:where([data-mantine-color-scheme="light"]) .m_e3a01f8:where([data-disabled], :has(button:disabled)) {
  background-color: var(--mantine-color-gray-3);
}

.m_1e0e6180 {
  cursor: inherit;
  height: 100%;
  line-height: var(--pill-height);
  text-overflow: ellipsis;
  overflow: hidden;
}

.m_ae386778 {
  color: inherit;
  font-size: inherit;
  height: 100%;
  min-height: unset;
  min-width: 2em;
  width: unset;
  border-radius: 0;
  border-start-end-radius: var(--pill-radius, 50%);
  border-end-end-radius: var(--pill-radius, 50%);
  flex: 0;
  padding-inline: .1em .3em;
}

.m_7cda1cd6[data-disabled] > .m_ae386778, .m_ae386778:disabled {
  cursor: not-allowed;
  background-color: #0000;
  width: .8em;
  min-width: .8em;
  padding: 0;
  display: none;
}

.m_7cda1cd6[data-disabled] > .m_ae386778 > svg, .m_ae386778:disabled > svg {
  display: none;
}

.m_ae386778 > svg {
  pointer-events: none;
}

.m_1dcfd90b {
  --pg-gap-xs: 6px;
  --pg-gap-sm: 8px;
  --pg-gap-md: 10px;
  --pg-gap-lg: 12px;
  --pg-gap-xl: 12px;
  --pg-gap: var(--pg-gap-sm);
  align-items: center;
  gap: var(--pg-gap);
  flex-wrap: wrap;
  display: flex;
}

.m_f61ca620 {
  --psi-button-size-xs: 22px;
  --psi-button-size-sm: 26px;
  --psi-button-size-md: 28px;
  --psi-button-size-lg: 32px;
  --psi-button-size-xl: 40px;
  --psi-icon-size-xs: 12px;
  --psi-icon-size-sm: 15px;
  --psi-icon-size-md: 17px;
  --psi-icon-size-lg: 19px;
  --psi-icon-size-xl: 21px;
  --psi-button-size: var(--psi-button-size-sm);
  --psi-icon-size: var(--psi-icon-size-sm);
}

.m_ccf8da4c {
  position: relative;
  overflow: hidden;
}

.m_f2d85dd2 {
  font-family: var(--mantine-font-family);
  font-size: inherit;
  line-height: var(--mantine-line-height);
  width: 100%;
  height: 100%;
  color: inherit;
  background-color: #0000;
  border: 0;
  outline: 0;
  padding-inline-start: var(--input-padding-inline-start);
  padding-inline-end: var(--input-padding-inline-end);
  position: absolute;
  inset: 0;
}

.m_ccf8da4c[data-disabled] .m_f2d85dd2, .m_f2d85dd2:disabled {
  cursor: not-allowed;
}

.m_f2d85dd2::-moz-placeholder {
  color: var(--input-placeholder-color);
  opacity: 1;
}

.m_f2d85dd2::placeholder {
  color: var(--input-placeholder-color);
  opacity: 1;
}

.m_f2d85dd2::-ms-reveal {
  display: none;
}

.m_b1072d44 {
  width: var(--psi-button-size);
  height: var(--psi-button-size);
  min-width: var(--psi-button-size);
  min-height: var(--psi-button-size);
}

.m_b1072d44:disabled {
  display: none;
}

.m_45c4369d {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  min-width: 100px;
  font-size: inherit;
  height: 1.6em;
  color: inherit;
  background-color: #0000;
  border: 0;
  flex: 1;
  padding: 0;
}

.m_45c4369d::-moz-placeholder {
  color: var(--input-placeholder-color);
  opacity: 1;
}

.m_45c4369d::placeholder {
  color: var(--input-placeholder-color);
  opacity: 1;
}

.m_45c4369d:where([data-type="hidden"], [data-type="auto"]) {
  pointer-events: none;
  opacity: 0;
  width: 1px;
  height: 1px;
  position: absolute;
  top: 0;
  left: 0;
}

.m_45c4369d:focus {
  outline: none;
}

.m_45c4369d:where([data-type="auto"]:focus) {
  visibility: visible;
  opacity: 1;
  height: 1.6em;
  position: static;
}

.m_45c4369d:where([data-pointer]:not([data-disabled], :disabled)) {
  cursor: pointer;
}

.m_45c4369d:where([data-disabled], :disabled) {
  cursor: not-allowed;
}

.m_f1cb205a {
  --pin-input-size-xs: 30px;
  --pin-input-size-sm: 36px;
  --pin-input-size-md: 42px;
  --pin-input-size-lg: 50px;
  --pin-input-size-xl: 60px;
  --pin-input-size: var(--pin-input-size-sm);
}

.m_cb288ead {
  width: var(--pin-input-size);
  height: var(--pin-input-size);
}

@keyframes m_81a374bd {
  0% {
    background-position: 0 0;
  }

  to {
    background-position: 40px 0;
  }
}

.m_db6d6462 {
  --progress-radius: var(--mantine-radius-default);
  --progress-size: var(--progress-size-md);
  --progress-size-xs: 3px;
  --progress-size-sm: 5px;
  --progress-size-md: 8px;
  --progress-size-lg: 12px;
  --progress-size-xl: 16px;
  height: var(--progress-size);
  border-radius: var(--progress-radius);
  display: flex;
  position: relative;
  overflow: hidden;
}

:where([data-mantine-color-scheme="light"]) .m_db6d6462 {
  background-color: var(--mantine-color-gray-2);
}

:where([data-mantine-color-scheme="dark"]) .m_db6d6462 {
  background-color: var(--mantine-color-dark-4);
}

.m_2242eb65 {
  background-color: var(--progress-section-color);
  height: 100%;
  width: var(--progress-section-width);
  transition: width var(--progress-transition-duration, .1s) ease;
  background-size: 20px 20px;
  justify-content: center;
  align-items: center;
  display: flex;
  overflow: hidden;
}

.m_2242eb65:where([data-striped]) {
  background-image: linear-gradient(45deg, #ffffff26 25%, #0000 25% 50%, #ffffff26 50% 75%, #0000 75%, #0000);
}

.m_2242eb65:where([data-animated]) {
  animation: 1s linear infinite m_81a374bd;
}

.m_2242eb65:where(:last-of-type) {
  border-radius: 0;
  border-start-end-radius: var(--progress-radius);
  border-end-end-radius: var(--progress-radius);
}

.m_2242eb65:where(:first-of-type) {
  border-radius: 0;
  border-start-start-radius: var(--progress-radius);
  border-end-start-radius: var(--progress-radius);
}

.m_91e40b74 {
  color: var(--progress-label-color, var(--mantine-color-white));
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-weight: 700;
  font-size: min(calc(var(--progress-size) * .65), calc(1.125rem * var(--mantine-scale)));
  padding-inline: 4px;
  line-height: 1;
  overflow: hidden;
}

.m_f3f1af94 {
  --radio-size-xs: 16px;
  --radio-size-sm: 20px;
  --radio-size-md: 24px;
  --radio-size-lg: 30px;
  --radio-size-xl: 36px;
  --radio-size: var(--radio-size-sm);
  --radio-icon-size-xs: 6px;
  --radio-icon-size-sm: 8px;
  --radio-icon-size-md: 10px;
  --radio-icon-size-lg: 14px;
  --radio-icon-size-xl: 16px;
  --radio-icon-size: var(--radio-icon-size-sm);
  --radio-icon-color: var(--mantine-color-white);
}

.m_89c4f5e4 {
  width: var(--radio-size);
  height: var(--radio-size);
  order: 1;
  position: relative;
}

.m_89c4f5e4:where([data-label-position="left"]) {
  order: 2;
}

.m_f3ed6b2b {
  color: var(--radio-icon-color);
  opacity: var(--radio-icon-opacity, 0);
  transform: var(--radio-icon-transform, scale(.2) translateY(calc(.625rem * var(--mantine-scale))));
  pointer-events: none;
  width: var(--radio-icon-size);
  height: var(--radio-icon-size);
  top: calc(50% - var(--radio-icon-size) / 2);
  left: calc(50% - var(--radio-icon-size) / 2);
  transition: opacity .1s, transform .2s;
  position: absolute;
}

.m_8a3dbb89 {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: var(--radio-size);
  height: var(--radio-size);
  border-radius: var(--radio-radius, var(--radio-size));
  cursor: var(--mantine-cursor-type);
  -webkit-tap-highlight-color: transparent;
  border: 1px solid;
  justify-content: center;
  align-items: center;
  margin: 0;
  transition-property: background-color, border-color;
  transition-duration: .1s;
  transition-timing-function: ease;
  display: flex;
  position: relative;
}

:where([data-mantine-color-scheme="light"]) .m_8a3dbb89 {
  background-color: var(--mantine-color-white);
  border-color: var(--mantine-color-gray-4);
}

:where([data-mantine-color-scheme="dark"]) .m_8a3dbb89 {
  background-color: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-dark-4);
}

.m_8a3dbb89:checked {
  background-color: var(--radio-color, var(--mantine-primary-color-filled));
  border-color: var(--radio-color, var(--mantine-primary-color-filled));
}

.m_8a3dbb89:checked + .m_f3ed6b2b {
  --radio-icon-opacity: 1;
  --radio-icon-transform: scale(1);
}

.m_8a3dbb89:disabled {
  cursor: not-allowed;
}

:where([data-mantine-color-scheme="light"]) .m_8a3dbb89:disabled {
  background-color: var(--mantine-color-gray-1);
  border-color: var(--mantine-color-gray-2);
}

:where([data-mantine-color-scheme="light"]) .m_8a3dbb89:disabled + .m_f3ed6b2b {
  --radio-icon-color: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme="dark"]) .m_8a3dbb89:disabled {
  background-color: var(--mantine-color-dark-5);
  border-color: var(--mantine-color-dark-4);
}

:where([data-mantine-color-scheme="dark"]) .m_8a3dbb89:disabled + .m_f3ed6b2b {
  --radio-icon-color: var(--mantine-color-dark-7);
}

.m_8a3dbb89:where([data-error]) {
  border-color: var(--mantine-color-error);
}

.m_1bfe9d39 + .m_f3ed6b2b {
  --radio-icon-color: var(--radio-color);
}

.m_1bfe9d39:checked:not(:disabled) {
  border-color: var(--radio-color);
  background-color: #0000;
}

.m_1bfe9d39:checked:not(:disabled) + .m_f3ed6b2b {
  --radio-icon-color: var(--radio-color);
  --radio-icon-opacity: 1;
  --radio-icon-transform: none;
}

.m_f8d312f2 {
  --rating-size-xs: 14px;
  --rating-size-sm: 18px;
  --rating-size-md: 20px;
  --rating-size-lg: 28px;
  --rating-size-xl: 32px;
  width: max-content;
  display: flex;
}

.m_f8d312f2:where(:has(input:disabled)) {
  pointer-events: none;
}

.m_61734bb7 {
  transition: transform .1s;
  position: relative;
}

.m_61734bb7:where([data-active]) {
  z-index: 1;
  transform: scale(1.1);
}

.m_5662a89a {
  width: var(--rating-size);
  height: var(--rating-size);
  display: block;
}

:where([data-mantine-color-scheme="light"]) .m_5662a89a {
  fill: var(--mantine-color-gray-3);
  stroke: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme="dark"]) .m_5662a89a {
  fill: var(--mantine-color-dark-3);
  stroke: var(--mantine-color-dark-3);
}

.m_5662a89a:where([data-filled]) {
  fill: var(--rating-color);
  stroke: var(--rating-color);
}

.m_211007ba {
  white-space: nowrap;
  opacity: 0;
  -webkit-tap-highlight-color: transparent;
  width: 0;
  height: 0;
  position: absolute;
  overflow: hidden;
}

.m_211007ba:focus-visible + label {
  outline: 2px solid var(--mantine-primary-color-filled);
  outline-offset: 2px;
}

.m_21342ee4 {
  cursor: pointer;
  z-index: var(--rating-item-z-index, 0);
  -webkit-tap-highlight-color: transparent;
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.m_21342ee4:where([data-read-only]) {
  cursor: default;
}

.m_21342ee4:where(:last-of-type) {
  position: relative;
}

.m_fae05d6a {
  clip-path: var(--rating-symbol-clip-path);
}

.m_b32e4812 {
  width: var(--rp-size);
  height: var(--rp-size);
  min-width: var(--rp-size);
  min-height: var(--rp-size);
  --rp-transition-duration: 0s;
  position: relative;
}

.m_d43b5134 {
  width: var(--rp-size);
  height: var(--rp-size);
  min-width: var(--rp-size);
  min-height: var(--rp-size);
  transform: rotate(-90deg);
}

.m_b1ca1fbf {
  stroke: var(--curve-color, var(--rp-curve-root-color));
  transition: stroke-dashoffset var(--rp-transition-duration) ease, stroke-dasharray var(--rp-transition-duration) ease, stroke var(--rp-transition-duration);
}

[data-mantine-color-scheme="light"] .m_b1ca1fbf {
  --rp-curve-root-color: var(--mantine-color-gray-2);
}

[data-mantine-color-scheme="dark"] .m_b1ca1fbf {
  --rp-curve-root-color: var(--mantine-color-dark-4);
}

.m_b23f9dc4 {
  top: 50%;
  inset-inline: var(--rp-label-offset);
  position: absolute;
  transform: translateY(-50%);
}

.m_cf365364 {
  --sc-padding-xs: 3px 6px;
  --sc-padding-sm: 5px 10px;
  --sc-padding-md: 7px 14px;
  --sc-padding-lg: 9px 16px;
  --sc-padding-xl: 12px 20px;
  --sc-transition-duration: .2s;
  --sc-padding: var(--sc-padding-sm);
  --sc-transition-timing-function: ease;
  --sc-font-size: var(--mantine-font-size-sm);
  border-radius: var(--sc-radius, var(--mantine-radius-default));
  flex-direction: row;
  width: auto;
  padding: 4px;
  display: inline-flex;
  position: relative;
  overflow: hidden;
}

.m_cf365364:where([data-full-width]) {
  display: flex;
}

.m_cf365364:where([data-orientation="vertical"]) {
  flex-direction: column;
  width: max-content;
  display: flex;
}

.m_cf365364:where([data-orientation="vertical"]):where([data-full-width]) {
  width: auto;
}

:where([data-mantine-color-scheme="light"]) .m_cf365364 {
  background-color: var(--mantine-color-gray-1);
}

:where([data-mantine-color-scheme="dark"]) .m_cf365364 {
  background-color: var(--mantine-color-dark-8);
}

.m_9e182ccd {
  z-index: 1;
  border-radius: var(--sc-radius, var(--mantine-radius-default));
  display: block;
  position: absolute;
}

:where([data-mantine-color-scheme="light"]) .m_9e182ccd {
  box-shadow: var(--sc-shadow, none);
  background-color: var(--sc-color, var(--mantine-color-white));
}

:where([data-mantine-color-scheme="dark"]) .m_9e182ccd {
  box-shadow: none;
  background-color: var(--sc-color, var(--mantine-color-dark-5));
}

.m_1738fcb2 {
  -webkit-tap-highlight-color: transparent;
  text-align: center;
  white-space: nowrap;
  text-overflow: ellipsis;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border-radius: var(--sc-radius, var(--mantine-radius-default));
  font-weight: 500;
  font-size: var(--sc-font-size);
  padding: var(--sc-padding);
  transition: color var(--sc-transition-duration) var(--sc-transition-timing-function);
  cursor: pointer;
  outline: var(--segmented-control-outline, none);
  display: block;
  overflow: hidden;
}

:where([data-mantine-color-scheme="light"]) .m_1738fcb2 {
  color: var(--mantine-color-gray-7);
}

:where([data-mantine-color-scheme="dark"]) .m_1738fcb2 {
  color: var(--mantine-color-dark-1);
}

.m_1738fcb2:where([data-read-only]) {
  cursor: default;
}

fieldset:disabled .m_1738fcb2, .m_1738fcb2:where([data-disabled]) {
  cursor: not-allowed;
}

:where([data-mantine-color-scheme="light"]) fieldset:disabled .m_1738fcb2, :where([data-mantine-color-scheme="light"]) .m_1738fcb2:where([data-disabled]) {
  color: var(--mantine-color-gray-5);
}

:where([data-mantine-color-scheme="dark"]) fieldset:disabled .m_1738fcb2, :where([data-mantine-color-scheme="dark"]) .m_1738fcb2:where([data-disabled]) {
  color: var(--mantine-color-dark-3);
}

:where([data-mantine-color-scheme="light"]) .m_1738fcb2:where([data-active]) {
  color: var(--sc-label-color, var(--mantine-color-black));
}

:where([data-mantine-color-scheme="dark"]) .m_1738fcb2:where([data-active]) {
  color: var(--sc-label-color, var(--mantine-color-white));
}

.m_cf365364:where([data-initialized]) .m_1738fcb2:where([data-active]):before {
  display: none;
}

.m_1738fcb2:where([data-active]):before {
  content: "";
  z-index: 0;
  border-radius: var(--sc-radius, var(--mantine-radius-default));
  position: absolute;
  inset: 0;
}

:where([data-mantine-color-scheme="light"]) .m_1738fcb2:where([data-active]):before {
  box-shadow: var(--sc-shadow, none);
  background-color: var(--sc-color, var(--mantine-color-white));
}

:where([data-mantine-color-scheme="dark"]) .m_1738fcb2:where([data-active]):before {
  box-shadow: none;
  background-color: var(--sc-color, var(--mantine-color-dark-5));
}

@media (hover: hover) {
  :where([data-mantine-color-scheme="light"]) .m_1738fcb2:where(:not([data-disabled], [data-active], [data-read-only])):hover {
    color: var(--mantine-color-black);
  }

  :where([data-mantine-color-scheme="dark"]) .m_1738fcb2:where(:not([data-disabled], [data-active], [data-read-only])):hover {
    color: var(--mantine-color-white);
  }
}

@media (hover: none) {
  :where([data-mantine-color-scheme="light"]) .m_1738fcb2:where(:not([data-disabled], [data-active], [data-read-only])):active {
    color: var(--mantine-color-black);
  }

  :where([data-mantine-color-scheme="dark"]) .m_1738fcb2:where(:not([data-disabled], [data-active], [data-read-only])):active {
    color: var(--mantine-color-white);
  }
}

@media (hover: hover) {
  :where([data-mantine-color-scheme="light"]) fieldset:disabled .m_1738fcb2:hover {
    color: var(--mantine-color-gray-5) !important;
  }

  :where([data-mantine-color-scheme="dark"]) fieldset:disabled .m_1738fcb2:hover {
    color: var(--mantine-color-dark-3) !important;
  }
}

@media (hover: none) {
  :where([data-mantine-color-scheme="light"]) fieldset:disabled .m_1738fcb2:active {
    color: var(--mantine-color-gray-5) !important;
  }

  :where([data-mantine-color-scheme="dark"]) fieldset:disabled .m_1738fcb2:active {
    color: var(--mantine-color-dark-3) !important;
  }
}

.m_1714d588 {
  white-space: nowrap;
  opacity: 0;
  width: 0;
  height: 0;
  position: absolute;
  overflow: hidden;
}

.m_1714d588[data-focus-ring="auto"]:focus:focus-visible + .m_1738fcb2, .m_1714d588[data-focus-ring="always"]:focus + .m_1738fcb2 {
  --segmented-control-outline: 2px solid var(--mantine-primary-color-filled);
}

.m_69686b9b {
  z-index: 2;
  transition: border-color var(--sc-transition-duration) var(--sc-transition-timing-function);
  flex: 1;
  position: relative;
}

.m_cf365364[data-with-items-borders] :where(.m_69686b9b):before {
  content: "";
  top: 0;
  bottom: 0;
  background-color: var(--separator-color);
  width: 1px;
  transition: background-color var(--sc-transition-duration) var(--sc-transition-timing-function);
  position: absolute;
  inset-inline-start: 0;
}

.m_69686b9b[data-orientation="vertical"]:before {
  top: 0;
  inset-inline: 0;
  width: auto;
  height: 1px;
  bottom: auto;
}

:where([data-mantine-color-scheme="light"]) .m_69686b9b {
  --separator-color: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme="dark"]) .m_69686b9b {
  --separator-color: var(--mantine-color-dark-4);
}

.m_69686b9b:first-of-type:before, [data-mantine-color-scheme] .m_69686b9b[data-active]:before, [data-mantine-color-scheme] .m_69686b9b[data-active] + .m_69686b9b:before {
  --separator-color: transparent;
}

.m_78882f40 {
  z-index: 2;
  position: relative;
}

.m_925c2d2c {
  container: simple-grid / inline-size;
}

.m_2415a157 {
  grid-template-columns: repeat(var(--sg-cols), minmax(0, 1fr));
  gap: var(--sg-spacing-y) var(--sg-spacing-x);
  display: grid;
}

@keyframes m_299c329c {
  0%, to {
    opacity: .4;
  }

  50% {
    opacity: 1;
  }
}

.m_18320242 {
  height: var(--skeleton-height, auto);
  width: var(--skeleton-width, 100%);
  border-radius: var(--skeleton-radius, var(--mantine-radius-default));
  position: relative;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

.m_18320242:where([data-animate]):after {
  animation: 1.5s linear infinite m_299c329c;
}

.m_18320242:where([data-visible]) {
  overflow: hidden;
}

.m_18320242:where([data-visible]):before {
  content: "";
  z-index: 10;
  background-color: var(--mantine-color-body);
  position: absolute;
  inset: 0;
}

.m_18320242:where([data-visible]):after {
  content: "";
  z-index: 11;
  position: absolute;
  inset: 0;
}

:where([data-mantine-color-scheme="light"]) .m_18320242:where([data-visible]):after {
  background-color: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme="dark"]) .m_18320242:where([data-visible]):after {
  background-color: var(--mantine-color-dark-4);
}

.m_dd36362e {
  --slider-size-xs: 4px;
  --slider-size-sm: 6px;
  --slider-size-md: 8px;
  --slider-size-lg: 10px;
  --slider-size-xl: 12px;
  --slider-size: var(--slider-size-md);
  --slider-radius: 1000px;
  --slider-color: var(--mantine-primary-color-filled);
  -webkit-tap-highlight-color: transparent;
  height: calc(var(--slider-size) * 2);
  padding-inline: var(--slider-size);
  touch-action: none;
  outline: none;
  flex-direction: column;
  align-items: center;
  display: flex;
  position: relative;
}

[data-mantine-color-scheme="light"] .m_dd36362e {
  --slider-track-bg: var(--mantine-color-gray-2);
  --slider-track-disabled-bg: var(--mantine-color-gray-4);
}

[data-mantine-color-scheme="dark"] .m_dd36362e {
  --slider-track-bg: var(--mantine-color-dark-4);
  --slider-track-disabled-bg: var(--mantine-color-dark-3);
}

.m_c9357328 {
  font-size: var(--mantine-font-size-xs);
  color: var(--mantine-color-white);
  padding: calc(var(--mantine-spacing-xs) / 2);
  border-radius: var(--mantine-radius-sm);
  white-space: nowrap;
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  touch-action: none;
  position: absolute;
  top: -36px;
}

:where([data-mantine-color-scheme="light"]) .m_c9357328 {
  background-color: var(--mantine-color-gray-9);
}

:where([data-mantine-color-scheme="dark"]) .m_c9357328 {
  background-color: var(--mantine-color-dark-4);
}

.m_c9a9a60a {
  height: var(--slider-thumb-size);
  width: var(--slider-thumb-size);
  cursor: pointer;
  border-radius: var(--slider-radius);
  z-index: 3;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  touch-action: none;
  outline-offset: 2px;
  top: 50%;
  left: var(--slider-thumb-offset);
  border: 4px solid;
  justify-content: center;
  align-items: center;
  transition: box-shadow .1s, transform .1s;
  display: flex;
  position: absolute;
  transform: translate(-50%, -50%);
}

:where([dir="rtl"]) .m_c9a9a60a {
  left: auto;
  right: calc(var(--slider-thumb-offset)  - var(--slider-thumb-size));
}

fieldset:disabled .m_c9a9a60a, .m_c9a9a60a:where([data-disabled]) {
  display: none;
}

.m_c9a9a60a:where([data-dragging]) {
  box-shadow: var(--mantine-shadow-sm);
  transform: translate(-50%, -50%)scale(1.05);
}

:where([data-mantine-color-scheme="light"]) .m_c9a9a60a {
  color: var(--slider-color);
  border-color: var(--slider-color);
  background-color: var(--mantine-color-white);
}

:where([data-mantine-color-scheme="dark"]) .m_c9a9a60a {
  color: var(--mantine-color-white);
  border-color: var(--mantine-color-white);
  background-color: var(--slider-color);
}

.m_a8645c2 {
  width: 100%;
  height: calc(var(--slider-size) * 2);
  cursor: pointer;
  align-items: center;
  display: flex;
}

fieldset:disabled .m_a8645c2, .m_a8645c2:where([data-disabled]) {
  cursor: not-allowed;
}

.m_c9ade57f {
  width: 100%;
  height: var(--slider-size);
  position: relative;
}

.m_c9ade57f:where([data-inverted]:not([data-disabled])) {
  --track-bg: var(--slider-color);
}

fieldset:disabled .m_c9ade57f:where([data-inverted]), .m_c9ade57f:where([data-inverted][data-disabled]) {
  --track-bg: var(--slider-track-disabled-bg);
}

.m_c9ade57f:before {
  content: "";
  border-radius: var(--slider-radius);
  top: 0;
  bottom: 0;
  inset-inline: calc(var(--slider-size) * -1);
  background-color: var(--track-bg, var(--slider-track-bg));
  z-index: 0;
  position: absolute;
}

.m_38aeed47 {
  z-index: 1;
  background-color: var(--slider-color);
  border-radius: var(--slider-radius);
  width: var(--slider-bar-width);
  top: 0;
  bottom: 0;
  position: absolute;
  inset-inline-start: var(--slider-bar-offset);
}

.m_38aeed47:where([data-inverted]) {
  background-color: var(--slider-track-bg);
}

:where([data-mantine-color-scheme="light"]) fieldset:disabled .m_38aeed47:where(:not([data-inverted])), :where([data-mantine-color-scheme="light"]) .m_38aeed47:where([data-disabled]:not([data-inverted])) {
  background-color: var(--mantine-color-gray-4);
}

:where([data-mantine-color-scheme="dark"]) fieldset:disabled .m_38aeed47:where(:not([data-inverted])), :where([data-mantine-color-scheme="dark"]) .m_38aeed47:where([data-disabled]:not([data-inverted])) {
  background-color: var(--mantine-color-dark-3);
}

.m_b7b0423a {
  inset-inline-start: calc(var(--mark-offset)  - var(--slider-size) / 2);
  z-index: 2;
  pointer-events: none;
  height: 0;
  position: absolute;
  top: 0;
}

.m_dd33bc19 {
  height: var(--slider-size);
  width: var(--slider-size);
  background-color: var(--mantine-color-white);
  pointer-events: none;
  border: 2px solid;
  border-radius: 1000px;
}

:where([data-mantine-color-scheme="light"]) .m_dd33bc19 {
  border-color: var(--mantine-color-gray-2);
}

:where([data-mantine-color-scheme="dark"]) .m_dd33bc19 {
  border-color: var(--mantine-color-dark-4);
}

.m_dd33bc19:where([data-filled]) {
  border-color: var(--slider-color);
}

:where([data-mantine-color-scheme="light"]) .m_dd33bc19:where([data-filled]):where([data-disabled]) {
  border-color: var(--mantine-color-gray-4);
}

:where([data-mantine-color-scheme="dark"]) .m_dd33bc19:where([data-filled]):where([data-disabled]) {
  border-color: var(--mantine-color-dark-3);
}

.m_68c77a5b {
  transform: translate(calc(-50% + var(--slider-size) / 2), calc(var(--mantine-spacing-xs) / 2));
  font-size: var(--mantine-font-size-sm);
  white-space: nowrap;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

:where([data-mantine-color-scheme="light"]) .m_68c77a5b {
  color: var(--mantine-color-gray-6);
}

:where([data-mantine-color-scheme="dark"]) .m_68c77a5b {
  color: var(--mantine-color-dark-2);
}

.m_559cce2d {
  position: relative;
}

.m_559cce2d:where([data-has-spoiler]) {
  margin-bottom: 24px;
}

.m_b912df4e {
  transition: max-height var(--spoiler-transition-duration, .2s) ease;
  flex-direction: column;
  display: flex;
  overflow: hidden;
}

.m_b9131032 {
  inset-inline-start: 0;
  height: 24px;
  position: absolute;
  top: 100%;
}

.m_6d731127 {
  align-items: var(--stack-align, stretch);
  justify-content: var(--stack-justify, flex-start);
  gap: var(--stack-gap, var(--mantine-spacing-md));
  flex-direction: column;
  display: flex;
}

.m_cbb4ea7e {
  --stepper-icon-size-xs: 34px;
  --stepper-icon-size-sm: 36px;
  --stepper-icon-size-md: 42px;
  --stepper-icon-size-lg: 48px;
  --stepper-icon-size-xl: 52px;
  --stepper-icon-size: var(--stepper-icon-size-md);
  --stepper-color: var(--mantine-primary-color-filled);
  --stepper-content-padding: var(--mantine-spacing-md);
  --stepper-spacing: var(--mantine-spacing-md);
  --stepper-radius: 1000px;
  --stepper-fz: var(--mantine-font-size-md);
}

.m_aaf89d0b {
  flex-wrap: nowrap;
  align-items: center;
  display: flex;
}

.m_aaf89d0b:where([data-wrap]) {
  gap: var(--mantine-spacing-md) 0;
  flex-wrap: wrap;
}

.m_aaf89d0b:where([data-orientation="vertical"]) {
  flex-direction: column;
}

.m_aaf89d0b:where([data-orientation="vertical"]):where([data-icon-position="left"]) {
  align-items: flex-start;
}

.m_aaf89d0b:where([data-orientation="vertical"]):where([data-icon-position="right"]) {
  align-items: flex-end;
}

.m_aaf89d0b:where([data-orientation="horizontal"]) {
  flex-direction: row;
}

.m_2a371ac9 {
  --separator-offset: calc(var(--stepper-icon-size) / 2 - calc(.0625rem * var(--mantine-scale)));
  flex: 1;
  transition: background-color .15s;
}

:where([data-mantine-color-scheme="light"]) .m_2a371ac9 {
  background-color: var(--mantine-color-gray-2);
}

:where([data-mantine-color-scheme="dark"]) .m_2a371ac9 {
  background-color: var(--mantine-color-dark-2);
}

.m_2a371ac9:where([data-active]) {
  background-color: var(--stepper-color);
}

.m_2a371ac9:where([data-orientation="horizontal"]) {
  height: 2px;
  margin-inline: var(--mantine-spacing-md);
}

.m_2a371ac9:where([data-orientation="vertical"]) {
  width: 2px;
  margin-top: calc(var(--mantine-spacing-xs) / 2);
  margin-bottom: calc(var(--mantine-spacing-xs)  - calc(.125rem * var(--mantine-scale)));
}

.m_2a371ac9:where([data-orientation="vertical"]):where([data-icon-position="left"]) {
  margin-inline-start: var(--separator-offset);
}

.m_2a371ac9:where([data-orientation="vertical"]):where([data-icon-position="right"]) {
  margin-inline-end: var(--separator-offset);
}

.m_78da155d {
  padding-top: var(--stepper-content-padding);
}

.m_cbb57068 {
  --step-color: var(--stepper-color);
  cursor: default;
  display: flex;
}

.m_cbb57068:where([data-allow-click]) {
  cursor: pointer;
}

.m_cbb57068:where([data-icon-position="left"]) {
  flex-direction: row;
}

.m_cbb57068:where([data-icon-position="right"]) {
  flex-direction: row-reverse;
}

.m_f56b1e2c {
  align-items: center;
}

.m_833edb7e {
  --separator-spacing: calc(var(--mantine-spacing-xs) / 2);
  min-height: calc(var(--stepper-icon-size)  + var(--mantine-spacing-xl)  + var(--separator-spacing));
  margin-top: var(--separator-spacing);
  justify-content: flex-start;
  overflow: hidden;
}

.m_833edb7e:where(:first-of-type) {
  margin-top: 0;
}

.m_833edb7e:where(:last-of-type) .m_6496b3f3 {
  display: none;
}

.m_818e70b {
  position: relative;
}

.m_6496b3f3 {
  top: calc(var(--stepper-icon-size)  + var(--separator-spacing));
  border-inline-start: 2px solid;
  height: 100vh;
  position: absolute;
  inset-inline-start: calc(var(--stepper-icon-size) / 2);
}

:where([data-mantine-color-scheme="light"]) .m_6496b3f3 {
  border-color: var(--mantine-color-gray-1);
}

:where([data-mantine-color-scheme="dark"]) .m_6496b3f3 {
  border-color: var(--mantine-color-dark-5);
}

.m_6496b3f3:where([data-active]) {
  border-color: var(--stepper-color);
}

.m_1959ad01 {
  height: var(--stepper-icon-size);
  width: var(--stepper-icon-size);
  min-height: var(--stepper-icon-size);
  min-width: var(--stepper-icon-size);
  border-radius: var(--stepper-radius);
  font-size: var(--stepper-fz);
  border: 2px solid;
  justify-content: center;
  align-items: center;
  font-weight: 700;
  transition: background-color .15s, border-color .15s;
  display: flex;
  position: relative;
}

:where([data-mantine-color-scheme="light"]) .m_1959ad01 {
  background-color: var(--mantine-color-gray-1);
  border-color: var(--mantine-color-gray-1);
  color: var(--mantine-color-gray-7);
}

:where([data-mantine-color-scheme="dark"]) .m_1959ad01 {
  background-color: var(--mantine-color-dark-5);
  border-color: var(--mantine-color-dark-5);
  color: var(--mantine-color-dark-1);
}

.m_1959ad01:where([data-progress]) {
  border-color: var(--step-color);
}

.m_1959ad01:where([data-completed]) {
  color: var(--stepper-icon-color, var(--mantine-color-white));
  background-color: var(--step-color);
  border-color: var(--step-color);
}

.m_a79331dc {
  color: var(--stepper-icon-color, var(--mantine-color-white));
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
  inset: 0;
}

.m_1956aa2a {
  flex-direction: column;
  display: flex;
}

.m_1956aa2a:where([data-icon-position="left"]) {
  margin-inline-start: var(--mantine-spacing-sm);
}

.m_1956aa2a:where([data-icon-position="right"]) {
  text-align: right;
  margin-inline-end: var(--mantine-spacing-sm);
}

:where([dir="rtl"]) .m_1956aa2a:where([data-icon-position="right"]) {
  text-align: left;
}

.m_12051f6c {
  font-weight: 500;
  font-size: var(--stepper-fz);
  line-height: 1;
}

.m_164eea74 {
  margin-top: calc(var(--stepper-spacing) / 3);
  margin-bottom: calc(var(--stepper-spacing) / 3);
  font-size: calc(var(--stepper-fz)  - calc(.125rem * var(--mantine-scale)));
  color: var(--mantine-color-dimmed);
  line-height: 1;
}

.m_5f93f3bb {
  --switch-height-xs: 16px;
  --switch-height-sm: 20px;
  --switch-height-md: 24px;
  --switch-height-lg: 30px;
  --switch-height-xl: 36px;
  --switch-width-xs: 32px;
  --switch-width-sm: 38px;
  --switch-width-md: 46px;
  --switch-width-lg: 56px;
  --switch-width-xl: 72px;
  --switch-thumb-size-xs: 12px;
  --switch-thumb-size-sm: 14px;
  --switch-thumb-size-md: 18px;
  --switch-thumb-size-lg: 22px;
  --switch-thumb-size-xl: 28px;
  --switch-label-font-size-xs: 5px;
  --switch-label-font-size-sm: 6px;
  --switch-label-font-size-md: 7px;
  --switch-label-font-size-lg: 9px;
  --switch-label-font-size-xl: 11px;
  --switch-track-label-padding-xs: 1px;
  --switch-track-label-padding-sm: 2px;
  --switch-track-label-padding-md: 2px;
  --switch-track-label-padding-lg: 3px;
  --switch-track-label-padding-xl: 3px;
  --switch-height: var(--switch-height-sm);
  --switch-width: var(--switch-width-sm);
  --switch-thumb-size: var(--switch-thumb-size-sm);
  --switch-label-font-size: var(--switch-label-font-size-sm);
  --switch-track-label-padding: var(--switch-track-label-padding-sm);
  --switch-radius: 1000px;
  --switch-color: var(--mantine-primary-color-filled);
  position: relative;
}

.m_926b4011 {
  opacity: 0;
  white-space: nowrap;
  width: 0;
  height: 0;
  margin: 0;
  padding: 0;
  position: absolute;
  overflow: hidden;
}

.m_9307d992 {
  -webkit-tap-highlight-color: transparent;
  cursor: var(--switch-cursor, var(--mantine-cursor-type));
  border-radius: var(--switch-radius);
  background-color: var(--switch-bg);
  border: 1px solid var(--switch-bd);
  height: var(--switch-height);
  min-width: var(--switch-width);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  font-size: var(--switch-label-font-size);
  order: var(--switch-order, 1);
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  z-index: 0;
  color: var(--switch-text-color);
  align-items: center;
  margin: 0;
  font-weight: 600;
  line-height: 0;
  transition: background-color .15s, border-color .15s;
  display: flex;
  position: relative;
  overflow: hidden;
}

.m_9307d992:where([data-without-labels]) {
  width: var(--switch-width);
}

.m_926b4011:focus-visible + .m_9307d992 {
  outline: 2px solid var(--mantine-primary-color-filled);
  outline-offset: 2px;
}

.m_926b4011:checked + .m_9307d992 {
  --switch-bg: var(--switch-color);
  --switch-bd: var(--switch-color);
  --switch-text-color: var(--mantine-color-white);
}

.m_926b4011:disabled + .m_9307d992, .m_926b4011[data-disabled] + .m_9307d992 {
  --switch-bg: var(--switch-disabled-color);
  --switch-bd: var(--switch-disabled-color);
  --switch-cursor: not-allowed;
}

[data-mantine-color-scheme="light"] .m_9307d992 {
  --switch-bg: var(--mantine-color-gray-2);
  --switch-bd: var(--mantine-color-gray-3);
  --switch-text-color: var(--mantine-color-gray-6);
  --switch-disabled-color: var(--mantine-color-gray-2);
}

[data-mantine-color-scheme="dark"] .m_9307d992 {
  --switch-bg: var(--mantine-color-dark-6);
  --switch-bd: var(--mantine-color-dark-4);
  --switch-text-color: var(--mantine-color-dark-1);
  --switch-disabled-color: var(--mantine-color-dark-4);
}

.m_9307d992[data-error] {
  --switch-bd: var(--mantine-color-error);
}

.m_9307d992[data-label-position="left"] {
  --switch-order: 2;
}

.m_93039a1d {
  z-index: 1;
  border-radius: var(--switch-radius);
  background-color: var(--switch-thumb-bg, var(--mantine-color-white));
  height: var(--switch-thumb-size);
  width: var(--switch-thumb-size);
  border: 1px solid var(--switch-thumb-bd);
  transition: inset-inline-start .15s;
  display: flex;
  position: absolute;
  inset-inline-start: var(--switch-thumb-start, var(--switch-track-label-padding));
}

.m_93039a1d > * {
  margin: auto;
}

.m_926b4011:checked + * > .m_93039a1d {
  --switch-thumb-start: calc(100% - var(--switch-thumb-size)  - var(--switch-track-label-padding));
  --switch-thumb-bd: var(--mantine-color-white);
}

.m_926b4011:disabled + * > .m_93039a1d, .m_926b4011[data-disabled] + * > .m_93039a1d {
  --switch-thumb-bd: var(--switch-thumb-bg-disabled);
  --switch-thumb-bg: var(--switch-thumb-bg-disabled);
}

[data-mantine-color-scheme="light"] .m_93039a1d {
  --switch-thumb-bd: var(--mantine-color-gray-3);
  --switch-thumb-bg-disabled: var(--mantine-color-gray-0);
}

[data-mantine-color-scheme="dark"] .m_93039a1d {
  --switch-thumb-bd: var(--mantine-color-white);
  --switch-thumb-bg-disabled: var(--mantine-color-dark-3);
}

.m_8277e082 {
  height: 100%;
  min-width: calc(var(--switch-width)  - var(--switch-thumb-size));
  padding-inline: var(--switch-track-label-padding);
  place-content: center;
  margin-inline-start: calc(var(--switch-thumb-size)  + var(--switch-track-label-padding));
  transition: margin .15s;
  display: grid;
}

.m_926b4011:checked + * > .m_8277e082 {
  margin-inline-start: 0;
  margin-inline-end: calc(var(--switch-thumb-size)  + var(--switch-track-label-padding));
}

.m_b23fa0ef {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
  line-height: var(--mantine-line-height);
  font-size: var(--mantine-font-size-sm);
  table-layout: var(--table-layout, auto);
  caption-side: var(--table-caption-side, bottom);
  border: none;
}

:where([data-mantine-color-scheme="light"]) .m_b23fa0ef {
  --table-hover-color: var(--mantine-color-gray-1);
  --table-striped-color: var(--mantine-color-gray-0);
  --table-border-color: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme="dark"]) .m_b23fa0ef {
  --table-hover-color: var(--mantine-color-dark-5);
  --table-striped-color: var(--mantine-color-dark-6);
  --table-border-color: var(--mantine-color-dark-4);
}

.m_b23fa0ef:where([data-with-table-border]) {
  border: calc(.0625rem * var(--mantine-scale)) solid var(--table-border-color);
}

.m_b23fa0ef:where([data-tabular-nums]) {
  font-variant-numeric: tabular-nums;
}

.m_b23fa0ef:where([data-variant="vertical"]) :where(.m_4e7aa4f3) {
  font-weight: 500;
}

:where([data-mantine-color-scheme="light"]) .m_b23fa0ef:where([data-variant="vertical"]) :where(.m_4e7aa4f3) {
  background-color: var(--mantine-color-gray-0);
}

:where([data-mantine-color-scheme="dark"]) .m_b23fa0ef:where([data-variant="vertical"]) :where(.m_4e7aa4f3) {
  background-color: var(--mantine-color-dark-6);
}

.m_4e7aa4f3 {
  text-align: left;
}

:where([dir="rtl"]) .m_4e7aa4f3 {
  text-align: right;
}

.m_4e7aa4fd {
  background-color: #0000;
  border-bottom: none;
}

@media (hover: hover) {
  .m_4e7aa4fd:hover:where([data-hover]) {
    background-color: var(--tr-hover-bg);
  }
}

@media (hover: none) {
  .m_4e7aa4fd:active:where([data-hover]) {
    background-color: var(--tr-hover-bg);
  }
}

.m_4e7aa4fd:where([data-with-row-border]) {
  border-bottom: calc(.0625rem * var(--mantine-scale)) solid var(--table-border-color);
}

.m_4e7aa4ef, .m_4e7aa4f3 {
  padding: var(--table-vertical-spacing) var(--table-horizontal-spacing, var(--mantine-spacing-xs));
}

.m_4e7aa4ef:where([data-with-column-border]:not(:last-child)), .m_4e7aa4f3:where([data-with-column-border]:not(:last-child)) {
  border-inline-end: calc(.0625rem * var(--mantine-scale)) solid var(--table-border-color);
}

.m_b2404537 > :where(tr):where([data-with-row-border]:last-of-type) {
  border-bottom: none;
}

.m_b2404537 > :where(tr):where([data-striped="odd"]:nth-of-type(odd)), .m_b2404537 > :where(tr):where([data-striped="even"]:nth-of-type(2n)) {
  background-color: var(--table-striped-color);
}

.m_b2404537 > :where(tr)[data-hover] {
  --tr-hover-bg: var(--table-highlight-on-hover-color, var(--table-hover-color));
}

.m_b242d975 {
  top: var(--table-sticky-header-offset, 0);
  z-index: 3;
}

.m_b242d975:where([data-sticky]) {
  background-color: var(--mantine-color-body);
  position: sticky;
}

.m_9e5a3ac7 {
  color: var(--mantine-color-dimmed);
}

.m_9e5a3ac7:where([data-side="top"]) {
  margin-bottom: var(--mantine-spacing-xs);
}

.m_9e5a3ac7:where([data-side="bottom"]) {
  margin-top: var(--mantine-spacing-xs);
}

.m_a100c15 {
  overflow-x: var(--table-overflow);
}

.m_62259741 {
  min-width: var(--table-min-width);
}

.m_89d60db1 {
  display: var(--tabs-display);
  flex-direction: var(--tabs-flex-direction);
  --tab-justify: flex-start;
  --tabs-list-direction: row;
  --tabs-panel-grow: unset;
  --tabs-display: block;
  --tabs-flex-direction: row;
  --tabs-list-border-width: 0;
  --tabs-list-border-size: 0 0 var(--tabs-list-border-width) 0;
  --tabs-list-gap: unset;
  --tabs-list-line-bottom: 0;
  --tabs-list-line-top: unset;
  --tabs-list-line-start: 0;
  --tabs-list-line-end: 0;
  --tab-radius: var(--tabs-radius) var(--tabs-radius) 0 0;
  --tab-border-width: 0 0 var(--tabs-list-border-width) 0;
}

.m_89d60db1[data-inverted] {
  --tabs-list-line-bottom: unset;
  --tabs-list-line-top: 0;
  --tab-radius: 0 0 var(--tabs-radius) var(--tabs-radius);
  --tab-border-width: var(--tabs-list-border-width) 0 0 0;
}

.m_89d60db1[data-inverted] .m_576c9d4:before {
  top: 0;
  bottom: unset;
}

.m_89d60db1[data-orientation="vertical"] {
  --tabs-list-line-start: unset;
  --tabs-list-line-end: 0;
  --tabs-list-line-top: 0;
  --tabs-list-line-bottom: 0;
  --tabs-list-border-size: 0 var(--tabs-list-border-width) 0 0;
  --tab-border-width: 0 var(--tabs-list-border-width) 0 0;
  --tab-radius: var(--tabs-radius) 0 0 var(--tabs-radius);
  --tabs-list-direction: column;
  --tabs-panel-grow: 1;
  --tabs-display: flex;
}

[dir="rtl"] .m_89d60db1[data-orientation="vertical"] {
  --tabs-list-border-size: 0 0 0 var(--tabs-list-border-width);
  --tab-border-width: 0 0 0 var(--tabs-list-border-width);
  --tab-radius: 0 var(--tabs-radius) var(--tabs-radius) 0;
}

.m_89d60db1[data-orientation="vertical"][data-placement="right"] {
  --tabs-flex-direction: row-reverse;
  --tabs-list-line-start: 0;
  --tabs-list-line-end: unset;
  --tabs-list-border-size: 0 0 0 var(--tabs-list-border-width);
  --tab-border-width: 0 0 0 var(--tabs-list-border-width);
  --tab-radius: 0 var(--tabs-radius) var(--tabs-radius) 0;
}

[dir="rtl"] .m_89d60db1[data-orientation="vertical"][data-placement="right"] {
  --tabs-list-border-size: 0 var(--tabs-list-border-width) 0 0;
  --tab-border-width: 0 var(--tabs-list-border-width) 0 0;
  --tab-radius: var(--tabs-radius) 0 0 var(--tabs-radius);
}

[data-mantine-color-scheme="light"] .m_89d60db1 {
  --tab-border-color: var(--mantine-color-gray-3);
}

[data-mantine-color-scheme="dark"] .m_89d60db1 {
  --tab-border-color: var(--mantine-color-dark-4);
}

.m_89d60db1[data-orientation="horizontal"] {
  --tab-justify: center;
}

.m_89d60db1[data-variant="default"] {
  --tabs-list-border-width: 2px;
}

[data-mantine-color-scheme="light"] .m_89d60db1[data-variant="default"] {
  --tab-hover-color: var(--mantine-color-gray-0);
}

[data-mantine-color-scheme="dark"] .m_89d60db1[data-variant="default"] {
  --tab-hover-color: var(--mantine-color-dark-6);
}

.m_89d60db1[data-variant="outline"] {
  --tabs-list-border-width: 1px;
}

.m_89d60db1[data-variant="pills"] {
  --tabs-list-gap: calc(var(--mantine-spacing-sm) / 2);
}

[data-mantine-color-scheme="light"] .m_89d60db1[data-variant="pills"] {
  --tab-hover-color: var(--mantine-color-gray-0);
}

[data-mantine-color-scheme="dark"] .m_89d60db1[data-variant="pills"] {
  --tab-hover-color: var(--mantine-color-dark-6);
}

.m_89d33d6d {
  justify-content: var(--tabs-justify, flex-start);
  flex-wrap: wrap;
  flex-direction: var(--tabs-list-direction);
  gap: var(--tabs-list-gap);
  --tab-grow: unset;
  display: flex;
}

.m_89d33d6d[data-grow] {
  --tab-grow: 1;
}

.m_b0c91715 {
  flex-grow: var(--tabs-panel-grow);
}

.m_4ec4dce6 {
  padding: var(--mantine-spacing-xs) var(--mantine-spacing-md);
  font-size: var(--mantine-font-size-sm);
  white-space: nowrap;
  z-index: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  flex-grow: var(--tab-grow);
  align-items: center;
  justify-content: var(--tab-justify);
  line-height: 1;
  display: flex;
  position: relative;
}

.m_4ec4dce6:disabled, .m_4ec4dce6[data-disabled] {
  opacity: .5;
  cursor: not-allowed;
}

.m_4ec4dce6:focus {
  z-index: 1;
}

.m_fc420b1f {
  margin-left: var(--tab-section-margin-left, 0);
  margin-right: var(--tab-section-margin-right, 0);
  justify-content: center;
  align-items: center;
  display: flex;
}

.m_fc420b1f[data-position="left"]:not(:only-child) {
  --tab-section-margin-right: var(--mantine-spacing-xs);
}

[dir="rtl"] .m_fc420b1f[data-position="left"]:not(:only-child) {
  --tab-section-margin-right: 0rem;
  --tab-section-margin-left: var(--mantine-spacing-xs);
}

.m_fc420b1f[data-position="right"]:not(:only-child) {
  --tab-section-margin-left: var(--mantine-spacing-xs);
}

[dir="rtl"] .m_fc420b1f[data-position="right"]:not(:only-child) {
  --tab-section-margin-left: 0rem;
  --tab-section-margin-right: var(--mantine-spacing-xs);
}

.m_576c9d4 {
  position: relative;
}

.m_576c9d4:before {
  content: "";
  border-color: var(--tab-border-color);
  border-width: var(--tabs-list-border-size);
  bottom: var(--tabs-list-line-bottom);
  inset-inline-start: var(--tabs-list-line-start);
  inset-inline-end: var(--tabs-list-line-end);
  top: var(--tabs-list-line-top);
  border-style: solid;
  position: absolute;
}

.m_539e827b {
  border-radius: var(--tab-radius);
  border-width: var(--tab-border-width);
  background-color: var(--tab-bg);
  --tab-bg: transparent;
  border-style: solid;
  border-color: #0000;
}

.m_539e827b:where([data-active]) {
  border-color: var(--tabs-color);
}

@media (hover: hover) {
  .m_539e827b:hover {
    --tab-bg: var(--tab-hover-color);
  }

  .m_539e827b:hover:where(:not([data-active])) {
    border-color: var(--tab-border-color);
  }
}

@media (hover: none) {
  .m_539e827b:active {
    --tab-bg: var(--tab-hover-color);
  }

  .m_539e827b:active:where(:not([data-active])) {
    border-color: var(--tab-border-color);
  }
}

@media (hover: hover) {
  .m_539e827b:disabled:hover, .m_539e827b[data-disabled]:hover {
    --tab-bg: transparent;
  }
}

@media (hover: none) {
  .m_539e827b:disabled:active, .m_539e827b[data-disabled]:active {
    --tab-bg: transparent;
  }
}

.m_6772fbd5 {
  position: relative;
}

.m_6772fbd5:before {
  content: "";
  border-color: var(--tab-border-color);
  border-width: var(--tabs-list-border-size);
  bottom: var(--tabs-list-line-bottom);
  inset-inline-start: var(--tabs-list-line-start);
  inset-inline-end: var(--tabs-list-line-end);
  top: var(--tabs-list-line-top);
  border-style: solid;
  position: absolute;
}

.m_b59ab47c {
  border: 1px solid #0000;
  border-top-color: var(--tab-border-top-color);
  border-bottom-color: var(--tab-border-bottom-color);
  border-radius: var(--tab-radius);
  --tab-border-bottom-color: transparent;
  --tab-border-top-color: transparent;
  --tab-border-inline-end-color: transparent;
  --tab-border-inline-start-color: transparent;
  position: relative;
}

.m_b59ab47c:where([data-active]):before {
  content: "";
  background-color: var(--tab-border-color);
  bottom: var(--tab-before-bottom, calc(-.0625rem * var(--mantine-scale)));
  left: var(--tab-before-left, calc(-.0625rem * var(--mantine-scale)));
  right: var(--tab-before-right, auto);
  top: var(--tab-before-top, auto);
  width: 1px;
  height: 1px;
  position: absolute;
}

.m_b59ab47c:where([data-active]):after {
  content: "";
  background-color: var(--tab-border-color);
  bottom: var(--tab-after-bottom, calc(-.0625rem * var(--mantine-scale)));
  right: var(--tab-after-right, calc(-.0625rem * var(--mantine-scale)));
  left: var(--tab-after-left, auto);
  top: var(--tab-after-top, auto);
  width: 1px;
  height: 1px;
  position: absolute;
}

.m_b59ab47c:where([data-active]) {
  border-top-color: var(--tab-border-top-color);
  border-bottom-color: var(--tab-border-bottom-color);
  border-inline-start-color: var(--tab-border-inline-start-color);
  border-inline-end-color: var(--tab-border-inline-end-color);
  --tab-border-top-color: var(--tab-border-color);
  --tab-border-inline-start-color: var(--tab-border-color);
  --tab-border-inline-end-color: var(--tab-border-color);
  --tab-border-bottom-color: var(--mantine-color-body);
}

.m_b59ab47c:where([data-active])[data-inverted] {
  --tab-border-bottom-color: var(--tab-border-color);
  --tab-border-top-color: var(--mantine-color-body);
  --tab-before-bottom: auto;
  --tab-before-top: -1px;
  --tab-after-bottom: auto;
  --tab-after-top: -1px;
}

.m_b59ab47c:where([data-active])[data-orientation="vertical"][data-placement="left"] {
  --tab-border-inline-end-color: var(--mantine-color-body);
  --tab-border-inline-start-color: var(--tab-border-color);
  --tab-border-bottom-color: var(--tab-border-color);
  --tab-before-right: -1px;
  --tab-before-left: auto;
  --tab-before-bottom: auto;
  --tab-before-top: -1px;
  --tab-after-left: auto;
  --tab-after-right: -1px;
}

[dir="rtl"] .m_b59ab47c:where([data-active])[data-orientation="vertical"][data-placement="left"] {
  --tab-before-right: auto;
  --tab-before-left: -1px;
  --tab-after-left: -1px;
  --tab-after-right: auto;
}

.m_b59ab47c:where([data-active])[data-orientation="vertical"][data-placement="right"] {
  --tab-border-inline-start-color: var(--mantine-color-body);
  --tab-border-inline-end-color: var(--tab-border-color);
  --tab-border-bottom-color: var(--tab-border-color);
  --tab-before-left: -1px;
  --tab-before-right: auto;
  --tab-before-bottom: auto;
  --tab-before-top: -1px;
  --tab-after-right: auto;
  --tab-after-left: -1px;
}

[dir="rtl"] .m_b59ab47c:where([data-active])[data-orientation="vertical"][data-placement="right"] {
  --tab-before-left: auto;
  --tab-before-right: -1px;
  --tab-after-right: -1px;
  --tab-after-left: auto;
}

.m_c3381914 {
  border-radius: var(--tabs-radius);
  background-color: var(--tab-bg);
  color: var(--tab-color);
  --tab-bg: transparent;
  --tab-color: inherit;
}

@media (hover: hover) {
  .m_c3381914:not([data-disabled]):hover {
    --tab-bg: var(--tab-hover-color);
  }
}

@media (hover: none) {
  .m_c3381914:not([data-disabled]):active {
    --tab-bg: var(--tab-hover-color);
  }
}

.m_c3381914[data-active][data-active] {
  --tab-bg: var(--tabs-color);
  --tab-color: var(--tabs-text-color, var(--mantine-color-white));
}

@media (hover: hover) {
  .m_c3381914[data-active][data-active]:hover {
    --tab-bg: var(--tabs-color);
  }
}

@media (hover: none) {
  .m_c3381914[data-active][data-active]:active {
    --tab-bg: var(--tabs-color);
  }
}

.m_b6d8b162 {
  -webkit-tap-highlight-color: transparent;
  font-size: var(--text-fz, var(--mantine-font-size-md));
  line-height: var(--text-lh, var(--mantine-line-height-md));
  color: var(--text-color);
  margin: 0;
  padding: 0;
  font-weight: 400;
  text-decoration: none;
}

.m_b6d8b162:where([data-truncate]) {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.m_b6d8b162:where([data-truncate="start"]) {
  text-align: right;
  direction: rtl;
}

:where([dir="rtl"]) .m_b6d8b162:where([data-truncate="start"]) {
  text-align: left;
  direction: ltr;
}

.m_b6d8b162:where([data-variant="gradient"]) {
  background-image: var(--text-gradient);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
}

.m_b6d8b162:where([data-line-clamp]) {
  text-overflow: ellipsis;
  -webkit-line-clamp: var(--text-line-clamp);
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.m_b6d8b162:where([data-inherit]) {
  line-height: inherit;
  font-weight: inherit;
  font-size: inherit;
}

.m_b6d8b162:where([data-inline]) {
  line-height: 1;
}

.m_7341320d {
  --ti-size-xs: 18px;
  --ti-size-sm: 22px;
  --ti-size-md: 28px;
  --ti-size-lg: 34px;
  --ti-size-xl: 44px;
  --ti-size: var(--ti-size-md);
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  width: var(--ti-size);
  height: var(--ti-size);
  min-width: var(--ti-size);
  min-height: var(--ti-size);
  border-radius: var(--ti-radius, var(--mantine-radius-default));
  background: var(--ti-bg, var(--mantine-primary-color-filled));
  color: var(--ti-color, var(--mantine-color-white));
  border: var(--ti-bd, 1px solid transparent);
  justify-content: center;
  align-items: center;
  line-height: 1;
  display: inline-flex;
  position: relative;
}

.m_43657ece {
  --offset: calc(var(--tl-bullet-size) / 2 + var(--tl-line-width) / 2);
  --tl-bullet-size: 20px;
  --tl-line-width: 4px;
  --tl-radius: 1000px;
  --tl-color: var(--mantine-primary-color-filled);
}

.m_43657ece:where([data-align="left"]) {
  padding-inline-start: var(--offset);
}

.m_43657ece:where([data-align="right"]) {
  padding-inline-end: var(--offset);
}

.m_2ebe8099 {
  margin-bottom: calc(var(--mantine-spacing-xs) / 2);
  font-weight: 500;
  line-height: 1;
}

.m_436178ff {
  --item-border: var(--tl-line-width) var(--tli-border-style, solid) var(--item-border-color);
  color: var(--mantine-color-text);
  position: relative;
}

.m_436178ff:before {
  content: "";
  pointer-events: none;
  top: 0;
  left: var(--timeline-line-left, 0);
  right: var(--timeline-line-right, 0);
  bottom: calc(var(--mantine-spacing-xl) * -1);
  border-inline-start: var(--item-border);
  display: var(--timeline-line-display, none);
  position: absolute;
}

.m_43657ece[data-align="left"] .m_436178ff:before {
  --timeline-line-left: calc(var(--tl-line-width) * -1);
  --timeline-line-right: auto;
}

[dir="rtl"] .m_43657ece[data-align="left"] .m_436178ff:before, .m_43657ece[data-align="right"] .m_436178ff:before {
  --timeline-line-left: auto;
  --timeline-line-right: calc(var(--tl-line-width) * -1);
}

[dir="rtl"] .m_43657ece[data-align="right"] .m_436178ff:before {
  --timeline-line-left: calc(var(--tl-line-width) * -1);
  --timeline-line-right: auto;
}

.m_43657ece:where([data-align="left"]) .m_436178ff {
  text-align: left;
  padding-inline-start: var(--offset);
}

.m_43657ece:where([data-align="right"]) .m_436178ff {
  text-align: right;
  padding-inline-end: var(--offset);
}

:where([data-mantine-color-scheme="light"]) .m_436178ff {
  --item-border-color: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme="dark"]) .m_436178ff {
  --item-border-color: var(--mantine-color-dark-4);
}

.m_436178ff:where([data-line-active]):before {
  border-color: var(--tli-color, var(--tl-color));
}

.m_436178ff:where(:not(:last-of-type)) {
  --timeline-line-display: block;
}

.m_436178ff:where(:not(:first-of-type)) {
  margin-top: var(--mantine-spacing-xl);
}

.m_8affcee1 {
  width: var(--tl-bullet-size);
  height: var(--tl-bullet-size);
  border-radius: var(--tli-radius, var(--tl-radius));
  border: var(--tl-line-width) solid;
  background-color: var(--mantine-color-body);
  color: var(--mantine-color-text);
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
  top: 0;
}

:where([data-mantine-color-scheme="light"]) .m_8affcee1 {
  border-color: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme="dark"]) .m_8affcee1 {
  border-color: var(--mantine-color-dark-4);
}

.m_43657ece:where([data-align="left"]) .m_8affcee1 {
  left: calc((var(--tl-bullet-size) / 2 + var(--tl-line-width) / 2) * -1);
  right: auto;
}

:where([dir="rtl"]) .m_43657ece:where([data-align="left"]) .m_8affcee1, .m_43657ece:where([data-align="right"]) .m_8affcee1 {
  left: auto;
  right: calc((var(--tl-bullet-size) / 2 + var(--tl-line-width) / 2) * -1);
}

:where([dir="rtl"]) .m_43657ece:where([data-align="right"]) .m_8affcee1 {
  left: calc((var(--tl-bullet-size) / 2 + var(--tl-line-width) / 2) * -1);
  right: auto;
}

.m_8affcee1:where([data-with-child]) {
  border-width: var(--tl-line-width);
}

:where([data-mantine-color-scheme="light"]) .m_8affcee1:where([data-with-child]) {
  background-color: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme="dark"]) .m_8affcee1:where([data-with-child]) {
  background-color: var(--mantine-color-dark-4);
}

.m_8affcee1:where([data-active]) {
  border-color: var(--tli-color, var(--tl-color));
  background-color: var(--mantine-color-white);
  color: var(--tl-icon-color, var(--mantine-color-white));
}

.m_8affcee1:where([data-active]):where([data-with-child]) {
  background-color: var(--tli-color, var(--tl-color));
  color: var(--tl-icon-color, var(--mantine-color-white));
}

.m_43657ece:where([data-align="left"]) .m_540e8f41 {
  text-align: left;
  padding-inline-start: var(--offset);
}

:where([dir="rtl"]) .m_43657ece:where([data-align="left"]) .m_540e8f41 {
  text-align: right;
}

.m_43657ece:where([data-align="right"]) .m_540e8f41 {
  text-align: right;
  padding-inline-end: var(--offset);
}

:where([dir="rtl"]) .m_43657ece:where([data-align="right"]) .m_540e8f41 {
  text-align: left;
}

.m_8a5d1357 {
  font-weight: var(--title-fw);
  font-size: var(--title-fz);
  line-height: var(--title-lh);
  font-family: var(--mantine-font-family-headings);
  text-wrap: var(--title-text-wrap, var(--mantine-heading-text-wrap));
  margin: 0;
}

.m_8a5d1357:where([data-line-clamp]) {
  text-overflow: ellipsis;
  -webkit-line-clamp: var(--title-line-clamp);
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.m_1b3c8819 {
  --tooltip-radius: var(--mantine-radius-default);
  padding: calc(var(--mantine-spacing-xs) / 2) var(--mantine-spacing-xs);
  pointer-events: none;
  font-size: var(--mantine-font-size-sm);
  white-space: nowrap;
  border-radius: var(--tooltip-radius);
  position: absolute;
}

:where([data-mantine-color-scheme="light"]) .m_1b3c8819 {
  background-color: var(--tooltip-bg, var(--mantine-color-gray-9));
  color: var(--tooltip-color, var(--mantine-color-white));
}

:where([data-mantine-color-scheme="dark"]) .m_1b3c8819 {
  background-color: var(--tooltip-bg, var(--mantine-color-gray-2));
  color: var(--tooltip-color, var(--mantine-color-black));
}

.m_1b3c8819:where([data-multiline]) {
  white-space: normal;
}

.m_1b3c8819:where([data-fixed]) {
  position: fixed;
}

.m_f898399f {
  background-color: inherit;
  z-index: 1;
  border: 0;
}

.m_d6493fad :first-child {
  margin-top: 0;
}

.m_d6493fad :last-child {
  margin-bottom: 0;
}

.m_d6493fad :where(h1, h2, h3, h4, h5, h6) {
  margin-bottom: var(--mantine-spacing-xs);
  text-wrap: var(--mantine-heading-text-wrap);
}

.m_d6493fad :where(h1) {
  margin-top: calc(1.5 * var(--mantine-spacing-xl));
  font-size: var(--mantine-h1-font-size);
  line-height: var(--mantine-h1-line-height);
  font-weight: var(--mantine-h1-font-weight);
}

.m_d6493fad :where(h2) {
  margin-top: var(--mantine-spacing-xl);
  font-size: var(--mantine-h2-font-size);
  line-height: var(--mantine-h2-line-height);
  font-weight: var(--mantine-h2-font-weight);
}

.m_d6493fad :where(h3) {
  margin-top: calc(.8 * var(--mantine-spacing-xl));
  font-size: var(--mantine-h3-font-size);
  line-height: var(--mantine-h3-line-height);
  font-weight: var(--mantine-h3-font-weight);
}

.m_d6493fad :where(h4) {
  margin-top: calc(.8 * var(--mantine-spacing-xl));
  font-size: var(--mantine-h4-font-size);
  line-height: var(--mantine-h4-line-height);
  font-weight: var(--mantine-h4-font-weight);
}

.m_d6493fad :where(h5) {
  margin-top: calc(.5 * var(--mantine-spacing-xl));
  font-size: var(--mantine-h5-font-size);
  line-height: var(--mantine-h5-line-height);
  font-weight: var(--mantine-h5-font-weight);
}

.m_d6493fad :where(h6) {
  margin-top: calc(.5 * var(--mantine-spacing-xl));
  font-size: var(--mantine-h6-font-size);
  line-height: var(--mantine-h6-line-height);
  font-weight: var(--mantine-h6-font-weight);
}

.m_d6493fad :where(img) {
  max-width: 100%;
  margin-bottom: var(--mantine-spacing-xs);
}

.m_d6493fad :where(p) {
  margin-top: 0;
  margin-bottom: var(--mantine-spacing-lg);
}

:where([data-mantine-color-scheme="light"]) .m_d6493fad :where(mark) {
  background-color: var(--mantine-color-yellow-2);
  color: inherit;
}

:where([data-mantine-color-scheme="dark"]) .m_d6493fad :where(mark) {
  background-color: var(--mantine-color-yellow-5);
  color: var(--mantine-color-black);
}

.m_d6493fad :where(a) {
  color: var(--mantine-color-anchor);
  text-decoration: none;
}

@media (hover: hover) {
  .m_d6493fad :where(a):hover {
    text-decoration: underline;
  }
}

@media (hover: none) {
  .m_d6493fad :where(a):active {
    text-decoration: underline;
  }
}

.m_d6493fad :where(hr) {
  margin-top: var(--mantine-spacing-md);
  margin-bottom: var(--mantine-spacing-md);
  border: 0;
  border-top: 1px solid;
}

:where([data-mantine-color-scheme="light"]) .m_d6493fad :where(hr) {
  border-color: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme="dark"]) .m_d6493fad :where(hr) {
  border-color: var(--mantine-color-dark-3);
}

.m_d6493fad :where(pre) {
  padding: var(--mantine-spacing-xs);
  line-height: var(--mantine-line-height);
  margin: 0;
  margin-top: var(--mantine-spacing-md);
  margin-bottom: var(--mantine-spacing-md);
  font-family: var(--mantine-font-family-monospace);
  font-size: var(--mantine-font-size-xs);
  border-radius: var(--mantine-radius-sm);
  overflow-x: auto;
}

:where([data-mantine-color-scheme="light"]) .m_d6493fad :where(pre) {
  background-color: var(--mantine-color-gray-0);
}

:where([data-mantine-color-scheme="dark"]) .m_d6493fad :where(pre) {
  background-color: var(--mantine-color-dark-8);
}

.m_d6493fad :where(pre) :where(code) {
  color: inherit;
  background-color: #0000;
  border: 0;
  border-radius: 0;
  padding: 0;
}

.m_d6493fad :where(kbd) {
  --kbd-fz: 12px;
  --kbd-padding: 3px 5px;
  font-family: var(--mantine-font-family-monospace);
  line-height: var(--mantine-line-height);
  padding: var(--kbd-padding);
  font-weight: 700;
  font-size: var(--kbd-fz);
  border-radius: var(--mantine-radius-sm);
  border: 1px solid;
  border-bottom-width: 3px;
}

:where([data-mantine-color-scheme="light"]) .m_d6493fad :where(kbd) {
  border-color: var(--mantine-color-gray-3);
  color: var(--mantine-color-gray-7);
  background-color: var(--mantine-color-gray-0);
}

:where([data-mantine-color-scheme="dark"]) .m_d6493fad :where(kbd) {
  border-color: var(--mantine-color-dark-3);
  color: var(--mantine-color-dark-0);
  background-color: var(--mantine-color-dark-5);
}

.m_d6493fad :where(code) {
  line-height: var(--mantine-line-height);
  border-radius: var(--mantine-radius-sm);
  font-family: var(--mantine-font-family-monospace);
  font-size: var(--mantine-font-size-xs);
  padding: 1px 5px;
}

:where([data-mantine-color-scheme="light"]) .m_d6493fad :where(code) {
  background-color: var(--mantine-color-gray-0);
  color: var(--mantine-color-black);
}

:where([data-mantine-color-scheme="dark"]) .m_d6493fad :where(code) {
  background-color: var(--mantine-color-dark-5);
  color: var(--mantine-color-white);
}

.m_d6493fad :where(ul, ol):not([data-type="taskList"]) {
  margin-bottom: var(--mantine-spacing-md);
  padding-inline-start: var(--mantine-spacing-xl);
  list-style-position: outside;
}

.m_d6493fad :where(table) {
  border-collapse: collapse;
  caption-side: bottom;
  width: 100%;
  margin-bottom: var(--mantine-spacing-md);
}

:where([data-mantine-color-scheme="light"]) .m_d6493fad :where(table) {
  --table-border-color: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme="dark"]) .m_d6493fad :where(table) {
  --table-border-color: var(--mantine-color-dark-4);
}

.m_d6493fad :where(table) :where(caption) {
  margin-top: var(--mantine-spacing-xs);
  font-size: var(--mantine-font-size-sm);
  color: var(--mantine-color-dimmed);
}

.m_d6493fad :where(table) :where(th) {
  text-align: left;
  font-weight: 700;
  font-size: var(--mantine-font-size-sm);
  padding: var(--mantine-spacing-xs) var(--mantine-spacing-sm);
}

.m_d6493fad :where(table) :where(thead th) {
  border-bottom: 1px solid;
  border-color: var(--table-border-color);
}

.m_d6493fad :where(table) :where(tfoot th) {
  border-top: 1px solid;
  border-color: var(--table-border-color);
}

.m_d6493fad :where(table) :where(td) {
  padding: var(--mantine-spacing-xs) var(--mantine-spacing-sm);
  border-bottom: 1px solid;
  border-color: var(--table-border-color);
  font-size: var(--mantine-font-size-sm);
}

.m_d6493fad :where(table) :where(tr:last-of-type td) {
  border-bottom: 0;
}

.m_d6493fad :where(blockquote) {
  font-size: var(--mantine-font-size-lg);
  line-height: var(--mantine-line-height);
  margin: var(--mantine-spacing-md) 0;
  border-radius: var(--mantine-radius-sm);
  padding: var(--mantine-spacing-md) var(--mantine-spacing-lg);
}

:where([data-mantine-color-scheme="light"]) .m_d6493fad :where(blockquote) {
  background-color: var(--mantine-color-gray-0);
}

:where([data-mantine-color-scheme="dark"]) .m_d6493fad :where(blockquote) {
  background-color: var(--mantine-color-dark-8);
}

.bn-mantine *, .bn-mantine :after, .bn-mantine :before {
  box-sizing: border-box;
}

.bn-mantine button, .bn-mantine select {
  text-transform: none;
}

@media screen and (device-width <= 500px) {
  .bn-mantine {
    -webkit-text-size-adjust: 100%;
  }
}

@media (prefers-reduced-motion: reduce) {
  .bn-mantine [data-respect-reduced-motion] [data-reduce-motion] {
    transition: none;
    animation: none;
  }
}

.bn-mantine [data-mantine-color-scheme="dark"] .mantine-dark-hidden, .bn-mantine [data-mantine-color-scheme="light"] .mantine-light-hidden {
  display: none;
}

.bn-mantine .mantine-focus-auto:focus-visible, .bn-mantine .mantine-focus-always:focus {
  outline: calc(.125rem * var(--mantine-scale)) solid var(--mantine-primary-color-filled);
  outline-offset: calc(.125rem * var(--mantine-scale));
}

.bn-mantine .mantine-focus-never:focus {
  outline: none;
}

.bn-mantine .mantine-active:active {
  transform: translateY(calc(.0625rem * var(--mantine-scale)));
}

.bn-mantine[dir="rtl"] .mantine-rotate-rtl {
  transform: rotate(180deg);
}

.bn-mantine .mantine-Badge-root {
  background-color: var(--bn-colors-tooltip-background);
  color: var(--bn-colors-tooltip-text);
}

.bn-mantine .mantine-FileInput-input {
  background-color: var(--bn-colors-menu-background);
  color: var(--bn-colors-menu-text);
  font-family: var(--bn-font-family);
  border: none;
  border-radius: 4px;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  display: flex;
}

.bn-mantine .mantine-FileInput-input:hover {
  background-color: var(--bn-colors-hovered-background);
}

.bn-mantine .mantine-FileInput-wrapper {
  border: solid var(--bn-colors-border) 1px;
  border-radius: 4px;
}

.bn-mantine .mantine-InputPlaceholder-placeholder {
  color: var(--bn-colors-menu-text);
  font-family: var(--bn-font-family);
  font-weight: 600;
}

.bn-mantine .mantine-Menu-dropdown {
  background-color: var(--bn-colors-menu-background);
  border: var(--bn-border);
  border-radius: var(--bn-border-radius-medium);
  box-shadow: var(--bn-shadow-medium);
  box-sizing: border-box;
  color: var(--bn-colors-menu-text);
  padding: 2px;
  overflow: auto;
}

.bn-mantine .mantine-Menu-label {
  background-color: var(--bn-colors-menu-background);
  color: var(--bn-colors-menu-text);
}

.bn-mantine .mantine-Menu-item {
  background-color: var(--bn-colors-menu-background);
  border-radius: var(--bn-border-radius-small);
  color: var(--bn-colors-menu-text);
  border: none;
}

.bn-mantine .mantine-Menu-item[aria-selected="true"], .bn-mantine .mantine-Menu-item:hover {
  background-color: var(--bn-colors-hovered-background);
  color: var(--bn-colors-hovered-text);
  border: none;
}

.bn-mantine .mantine-Popover-dropdown {
  box-shadow: none;
  background-color: #0000;
  border: none;
  border-radius: 0;
  padding: 0;
}

.bn-mantine .mantine-Tabs-root {
  background-color: var(--bn-colors-menu-background);
  width: 100%;
}

.bn-mantine .mantine-Tabs-list:before {
  border-color: var(--bn-colors-hovered-background);
}

.bn-mantine .mantine-Tabs-tab {
  color: var(--bn-colors-menu-text);
  border-color: var(--bn-colors-hovered-background);
}

.bn-mantine .mantine-Tabs-tab:hover {
  background-color: var(--bn-colors-hovered-background);
  border-color: var(--bn-colors-hovered-background);
  color: var(--bn-colors-hovered-text);
}

.bn-mantine .mantine-Tabs-tab[data-active], .bn-mantine .mantine-Tabs-tab[data-active]:hover {
  border-color: var(--bn-colors-menu-text);
  color: var(--bn-colors-menu-text);
}

.bn-mantine .mantine-Tabs-panel {
  padding: 8px;
}

.bn-mantine .mantine-TextInput-input {
  background-color: var(--bn-colors-menu-background);
  border: solid var(--bn-colors-border) 1px;
  color: var(--bn-colors-menu-text);
  font-family: var(--bn-font-family);
  border-radius: 4px;
  height: 32px;
}

.bn-mantine .bn-mt-input-large .mantine-TextInput-input {
  border: none;
  height: 52px;
  font-size: 14px;
}

.bn-mantine .mantine-Tooltip-tooltip {
  box-shadow: none;
  background-color: #0000;
  border: none;
  border-radius: 0;
  padding: 0;
}

.bn-mantine .mantine-Tooltip-tooltip p:last-child {
  white-space: pre-wrap;
}

.bn-mantine .mantine-Tooltip-tooltip p:first-child {
  white-space: unset;
}

.bn-mantine .bn-select {
  overflow: auto;
}

.bn-mantine .mantine-Button-root[aria-controls*="dropdown"] {
  min-width: fit-content;
}

.bn-mantine .bn-toolbar {
  background-color: var(--bn-colors-menu-background);
  border: var(--bn-border);
  border-radius: var(--bn-border-radius-medium);
  box-shadow: var(--bn-shadow-medium);
  flex-wrap: nowrap;
  gap: 2px;
  width: fit-content;
  max-width: 100vw;
  padding: 2px;
  overflow-x: auto;
}

.bn-mantine .bn-toolbar:empty {
  display: none;
}

.bn-toolbar .mantine-Button-root, .bn-toolbar .mantine-ActionIcon-root {
  background-color: var(--bn-colors-menu-background);
  border-radius: var(--bn-border-radius-small);
  color: var(--bn-colors-menu-text);
  border: none;
}

.bn-toolbar .mantine-Button-root:hover, .bn-toolbar .mantine-ActionIcon-root:hover {
  background-color: var(--bn-colors-hovered-background);
  color: var(--bn-colors-hovered-text);
  border: none;
}

.bn-toolbar .mantine-Button-root[data-selected], .bn-toolbar .mantine-ActionIcon-root[data-selected] {
  background-color: var(--bn-colors-selected-background);
  color: var(--bn-colors-selected-text);
  border: none;
}

.bn-toolbar .mantine-Button-root[data-disabled], .bn-toolbar .mantine-ActionIcon-root[data-disabled] {
  background-color: var(--bn-colors-disabled-background);
  color: var(--bn-colors-disabled-text);
  border: none;
}

.bn-toolbar .mantine-Menu-item {
  height: 30px;
  font-size: 12px;
}

.bn-toolbar .mantine-Menu-item:hover {
  background-color: var(--bn-colors-hovered-background);
}

.bn-mantine .bn-form-popover {
  background-color: var(--bn-colors-menu-background);
  border: var(--bn-border);
  border-radius: var(--bn-border-radius-medium);
  box-shadow: var(--bn-shadow-medium);
  color: var(--bn-colors-menu-text);
  gap: 4px;
  min-width: 145px;
  padding: 2px;
}

.bn-form-popover .mantine-TextInput-root, .bn-form-popover .mantine-FileInput-root {
  width: 300px;
}

.bn-form-popover .mantine-TextInput-wrapper, .bn-form-popover .mantine-FileInput-wrapper {
  border-radius: 4px;
  padding: 0;
}

.bn-form-popover .mantine-TextInput-wrapper:hover {
  background-color: var(--bn-colors-hovered-background);
}

.bn-form-popover .mantine-TextInput-input, .bn-form-popover .mantine-FileInput-input {
  border: none;
  font-size: 12px;
}

.bn-form-popover .mantine-FileInput-input:hover {
  background-color: var(--bn-colors-hovered-background);
}

.bn-form-popover .mantine-FileInput-section[data-position="left"], .bn-form-popover .mantine-FileInput-placeholder {
  color: var(--bn-colors-menu-text);
}

.bn-mantine .bn-suggestion-menu {
  max-height: 100%;
  box-shadow: var(--mantine-shadow-md);
  border: calc(.0625rem * var(--mantine-scale)) solid var(--mantine-color-gray-2);
  border-radius: var(--mantine-radius-default);
  padding: 4px;
  position: relative;
}

.bn-mantine .bn-suggestion-menu-label {
  color: var(--mantine-color-dimmed);
  font-weight: 500;
  font-size: var(--mantine-font-size-xs);
  padding: calc(var(--mantine-spacing-xs) / 2) var(--mantine-spacing-sm);
  cursor: default;
}

.bn-mantine .bn-suggestion-menu-item {
  font-size: var(--mantine-font-size-sm);
  width: 100%;
  padding: calc(var(--mantine-spacing-xs) / 1.5) var(--mantine-spacing-sm);
  border-radius: var(--popover-radius, var(--mantine-radius-default));
  color: var(--menu-item-color, var(--mantine-color-text));
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  align-items: center;
  display: flex;
}

.bn-mantine .bn-suggestion-menu-item:where([data-disabled], :disabled) {
  color: var(--mantine-color-dimmed);
  opacity: .6;
  pointer-events: none;
}

.bn-mt-suggestion-menu-item-body {
  flex: 1;
}

.bn-mt-suggestion-menu-item-section {
  justify-content: center;
  align-items: center;
  display: flex;
}

.bn-mt-suggestion-menu-item-section:where([data-position="left"]) {
  margin-inline-end: var(--mantine-spacing-xs);
}

.bn-mt-suggestion-menu-item-section:where([data-position="right"]) {
  margin-inline-start: var(--mantine-spacing-xs);
}

.bn-mantine .bn-suggestion-menu {
  background-color: var(--bn-colors-menu-background);
  border: var(--bn-border);
  border-radius: var(--bn-border-radius-medium);
  box-shadow: var(--bn-shadow-medium);
  box-sizing: border-box;
  color: var(--bn-colors-menu-text);
  height: fit-content;
  max-height: inherit;
  padding: 2px;
  overflow-y: auto;
}

.bn-mantine .bn-suggestion-menu-item {
  cursor: pointer;
  height: 52px;
}

.bn-mantine .bn-suggestion-menu-item-small {
  height: fit-content;
  padding: calc(var(--mantine-spacing-xs) / 2) var(--mantine-spacing-sm);
}

.bn-mantine .bn-suggestion-menu-item[aria-selected="true"], .bn-mantine .bn-suggestion-menu-item:hover {
  background-color: var(--bn-colors-hovered-background);
}

.bn-mt-suggestion-menu-item-section {
  color: var(--bn-colors-tooltip-text);
}

.bn-mt-suggestion-menu-item-section[data-position="left"] {
  background-color: var(--bn-colors-tooltip-background);
  border-radius: var(--bn-border-radius-small);
  padding: 8px;
}

.bn-suggestion-menu-item-small .bn-mt-suggestion-menu-item-section[data-position="left"] {
  background-color: #0000;
  padding: 0;
}

.bn-suggestion-menu-item-small .bn-mt-suggestion-menu-item-section[data-position="left"] svg {
  width: 14px;
  height: 14px;
}

.bn-mt-suggestion-menu-item-body {
  flex-direction: column;
  flex: 1;
  justify-content: flex-start;
  align-items: stretch;
  padding-right: 16px;
  display: flex;
}

.bn-mt-suggestion-menu-item-title {
  color: var(--bn-colors-menu-text);
  margin: 0;
  padding: 0;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}

.bn-suggestion-menu-item-small .bn-mt-suggestion-menu-item-title {
  font-size: 12px;
}

.bn-mt-suggestion-menu-item-subtitle {
  color: var(--bn-colors-menu-text);
  margin: 0;
  padding: 0;
  font-size: 10px;
  line-height: 16px;
}

.bn-suggestion-menu-item-small .bn-mt-suggestion-menu-item-subtitle {
  display: none;
}

.bn-mantine .bn-suggestion-menu-label {
  color: var(--bn-colors-hovered-text);
}

.bn-mantine .bn-suggestion-menu-loader {
  width: 100%;
  height: 20px;
}

.bn-mantine .bn-suggestion-menu-loader span {
  background-color: var(--bn-colors-side-menu);
}

.bn-mantine .bn-grid-suggestion-menu {
  background: var(--bn-colors-menu-background);
  border-radius: var(--bn-border-radius-large);
  box-shadow: var(--bn-shadow-medium);
  height: fit-content;
  max-height: inherit;
  justify-items: center;
  gap: 7px;
  padding: 20px;
  display: grid;
  overflow-y: auto;
}

.bn-mantine .bn-grid-suggestion-menu-item {
  border-radius: var(--bn-border-radius-large);
  cursor: pointer;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  margin: 2px;
  padding: 4px;
  font-size: 24px;
  display: flex;
}

.bn-mantine .bn-grid-suggestion-menu-item[aria-selected="true"], .bn-mantine .bn-grid-suggestion-menu-item:hover {
  background-color: var(--bn-colors-hovered-background);
}

.bn-mantine .bn-grid-suggestion-menu-empty-item, .bn-mantine .bn-grid-suggestion-menu-loader {
  color: var(--bn-colors-menu-text);
  justify-content: center;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
}

.bn-mantine .bn-grid-suggestion-menu-loader span {
  background-color: var(--bn-colors-side-menu);
}

.bn-mantine .bn-side-menu {
  background-color: #0000;
  overflow: visible;
}

.bn-side-menu .mantine-Menu-item, .bn-table-handle-menu .mantine-Menu-item {
  height: 30px;
  font-size: 12px;
}

.bn-side-menu .mantine-UnstyledButton-root:not(.mantine-Menu-item) {
  background-color: #0000;
}

.bn-side-menu .mantine-UnstyledButton-root:hover {
  background-color: var(--bn-colors-hovered-background);
}

.bn-side-menu .mantine-UnstyledButton-root:not(.mantine-Menu-item) svg {
  color: var(--bn-colors-side-menu);
  background-color: #0000;
  width: 22px;
  height: 22px;
}

.bn-mantine .bn-side-menu > [draggable="true"] {
  display: flex;
}

.bn-side-menu .mantine-Menu-dropdown {
  min-width: 100px;
  padding: 2px;
  position: absolute;
}

.bn-mantine .bn-panel {
  background-color: var(--bn-colors-menu-background);
  border: var(--bn-border);
  border-radius: var(--bn-border-radius-medium);
  box-shadow: var(--bn-shadow-medium);
  width: 500px;
  padding: 2px;
}

.bn-mantine .bn-panel .bn-tab-panel {
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 100%;
  display: flex;
}

.bn-panel .mantine-TextInput-root, .bn-panel .mantine-FileInput-root {
  width: 100%;
}

.bn-panel .mantine-Button-root {
  background-color: var(--bn-colors-menu-background);
  border: solid var(--bn-colors-border) 1px;
  border-radius: var(--bn-border-radius-small);
  color: var(--bn-colors-menu-text);
  width: 60%;
  height: 32px;
}

.bn-panel .mantine-Button-root:hover {
  background-color: var(--bn-colors-hovered-background);
}

.bn-panel .mantine-Text-root {
  text-align: center;
}

.bn-mantine .bn-table-handle, .bn-mantine .bn-extend-button, .bn-mantine .bn-table-cell-handle {
  background-color: var(--bn-colors-menu-background);
  border: var(--bn-border);
  border-radius: var(--bn-border-radius-small);
  box-shadow: var(--bn-shadow-light);
  color: var(--bn-colors-side-menu);
  cursor: grab;
  justify-content: center;
  align-items: center;
  height: fit-content;
  padding: 0;
  display: flex;
  overflow: visible;
}

.bn-mantine .bn-table-cell-handle {
  padding: 0 4px;
}

.bn-mantine .bn-table-handle svg {
  margin-inline: -4px;
}

.bn-mantine .bn-table-handle-not-draggable {
  cursor: pointer;
}

.bn-mantine .bn-table-handle:hover, .bn-mantine .bn-table-handle-dragging, .bn-mantine .bn-extend-button:hover, .bn-mantine .bn-extend-button-editing, .bn-mantine .bn-table-cell-handle:hover {
  background-color: var(--bn-colors-hovered-background);
}

.bn-mantine .bn-extend-button-add-remove-columns {
  cursor: col-resize;
  width: 18px;
  height: 100%;
  margin-left: 4px;
}

.bn-mantine .bn-extend-button-add-remove-rows {
  cursor: row-resize;
  width: 100%;
  height: 18px;
  margin-top: 4px;
}

.bn-mantine .bn-drag-handle-menu, .bn-mantine .bn-table-handle-menu {
  overflow: visible;
}

.bn-mantine .bn-tooltip {
  background-color: var(--bn-colors-tooltip-background);
  border: var(--bn-border);
  border-radius: var(--bn-border-radius-medium);
  box-shadow: var(--bn-shadow-medium);
  color: var(--bn-colors-tooltip-text);
  text-align: center;
  padding: 4px 10px;
}

.bn-mantine .bn-tick-space {
  width: 20px;
  padding: 0;
}

.bn-mt-sub-menu-item > .mantine-Menu-itemLabel > div:not(.mantine-Menu-dropdown) {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.bn-mantine .bn-thread {
  background-color: var(--bn-colors-menu-background);
  border: var(--bn-border);
  border-radius: var(--bn-border-radius-medium);
  box-shadow: var(--bn-shadow-medium);
  color: var(--bn-colors-menu-text);
  flex-direction: column;
  gap: 16px;
  min-width: 350px;
  display: flex;
  overflow: visible;
}

.bn-mantine .bn-threads-sidebar .bn-thread {
  box-shadow: none;
}

.bn-mantine .bn-thread:not(.selected) {
  cursor: pointer;
}

.bn-mantine .bn-thread-comments, .bn-mantine .bn-thread-composer {
  flex-direction: column;
  margin: 0;
  padding: 0;
  display: flex;
}

.bn-mantine .bn-thread-comments {
  gap: 1rem;
}

.bn-mantine .bn-thread-comment {
  flex-direction: column;
  align-items: flex-start;
  gap: .25rem;
}

.bn-mantine .bn-thread-comment > .mantine-Group-root {
  gap: 12px;
}

.bn-mantine .bn-thread-comment p {
  color: var(--bn-colors-menu-text);
}

.bn-mantine .bn-thread .bn-header-text, .bn-mantine .bn-thread-comment .bn-resolved-text {
  font-size: .8rem;
  font-style: italic;
}

.bn-mantine .bn-comment-actions-wrapper {
  justify-content: flex-end;
  width: 100%;
  display: flex;
}

.bn-mantine .bn-action-toolbar {
  background-color: var(--bn-colors-menu-background);
  border: var(--bn-border);
  border-radius: var(--bn-border-radius-medium);
  align-self: flex-end;
  gap: 0;
  padding: 2px;
}

.bn-action-toolbar .mantine-Button-root, .bn-action-toolbar .mantine-ActionIcon-root {
  background-color: var(--bn-colors-menu-background);
  border-radius: var(--bn-border-radius-small);
  color: var(--bn-colors-menu-text);
  border: none;
}

.bn-action-toolbar .mantine-Button-root:hover, .bn-action-toolbar .mantine-ActionIcon-root:hover {
  background-color: var(--bn-colors-hovered-background);
  color: var(--bn-colors-hovered-text);
  border: none;
}

.bn-action-toolbar .mantine-Button-root[data-selected], .bn-action-toolbar .mantine-ActionIcon-root[data-selected] {
  background-color: var(--bn-colors-selected-background);
  color: var(--bn-colors-selected-text);
  border: none;
}

.bn-action-toolbar .mantine-Button-root[data-disabled], .bn-action-toolbar .mantine-ActionIcon-root[data-disabled] {
  background-color: var(--bn-colors-disabled-background);
  color: var(--bn-colors-disabled-text);
  border: none;
}

.bn-mantine .bn-action-toolbar .mantine-Menu-itemLabel {
  font-size: 12px;
}

.bn-mantine .bn-badge-group {
  justify-content: flex-start;
  gap: 4px;
  width: 100%;
  display: flex;
}

.bn-mantine .bn-badge {
  flex-grow: 0;
}

.bn-mantine .bn-badge .mantine-Chip-label {
  padding: 0 8px;
}

.bn-mantine .bn-badge .mantine-Chip-label:not([data-checked="true"]) {
  background-color: var(--bn-colors-menu-background);
  border: var(--bn-border);
  color: var(--bn-colors-menu-text);
}

.bn-mantine .bn-badge .mantine-Chip-label:hover {
  border: 1px solid var(--mantine-primary-color-filled-hover);
  color: var(--mantine-primary-color-filled-hover);
}

.bn-mantine .bn-badge .mantine-Chip-label > span:not(.mantine-Chip-iconWrapper) {
  gap: 4px;
  display: inline-flex;
}

.bn-mantine .bn-badge .mantine-Chip-label > span:not(.mantine-Chip-iconWrapper) > span {
  justify-content: center;
  align-items: center;
  display: inline-flex;
}

.bn-mantine .bn-combobox-input, .bn-mantine .bn-combobox-items:not(:empty) {
  background-color: var(--bn-colors-menu-background);
  border: var(--bn-border);
  border-radius: var(--bn-border-radius-medium);
  box-shadow: var(--bn-shadow-medium);
  color: var(--bn-colors-menu-text);
  gap: 4px;
  min-width: 145px;
  padding: 2px;
}

.bn-mantine .bn-combobox-input .bn-combobox-icon, .bn-mantine .bn-combobox-input .bn-combobox-right-section {
  justify-content: center;
  align-items: center;
  display: flex;
}

.bn-mantine .bn-combobox-input .bn-combobox-error {
  color: var(--bn-colors-highlights-red-background);
}

.bn-mantine .bn-badge .mantine-Chip-iconWrapper {
  display: none;
}


/*# sourceMappingURL=node_modules_%40blocknote_05a7bb84._.css.map*/