{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/remark-rehype/index.js"], "sourcesContent": ["/**\n * @typedef {import('./lib/index.js').Options} Options\n */\n\nexport {\n  defaultFootnoteBackContent,\n  defaultFootnoteBackLabel,\n  defaultHandlers\n} from 'mdast-util-to-hast'\nexport {default} from './lib/index.js'\n"], "names": [], "mappings": "AAAA;;CAEC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40ungap/structured-clone/esm/types.js"], "sourcesContent": ["export const VOID       = -1;\nexport const PRIMITIVE  = 0;\nexport const ARRAY      = 1;\nexport const OBJECT     = 2;\nexport const DATE       = 3;\nexport const REGEXP     = 4;\nexport const MAP        = 5;\nexport const SET        = 6;\nexport const ERROR      = 7;\nexport const BIGINT     = 8;\n// export const SYMBOL = 9;\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAO,MAAM,OAAa,CAAC;AACpB,MAAM,YAAa;AACnB,MAAM,QAAa;AACnB,MAAM,SAAa;AACnB,MAAM,OAAa;AACnB,MAAM,SAAa;AACnB,MAAM,MAAa;AACnB,MAAM,MAAa;AACnB,MAAM,QAAa;AACnB,MAAM,SAAa,GAC1B,2BAA2B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40ungap/structured-clone/esm/deserialize.js"], "sourcesContent": ["import {\n  VOID, PRIMITIVE,\n  ARRAY, OBJECT,\n  DATE, REGEXP, MAP, SET,\n  ERROR, BIGINT\n} from './types.js';\n\nconst env = typeof self === 'object' ? self : globalThis;\n\nconst deserializer = ($, _) => {\n  const as = (out, index) => {\n    $.set(index, out);\n    return out;\n  };\n\n  const unpair = index => {\n    if ($.has(index))\n      return $.get(index);\n\n    const [type, value] = _[index];\n    switch (type) {\n      case PRIMITIVE:\n      case VOID:\n        return as(value, index);\n      case ARRAY: {\n        const arr = as([], index);\n        for (const index of value)\n          arr.push(unpair(index));\n        return arr;\n      }\n      case OBJECT: {\n        const object = as({}, index);\n        for (const [key, index] of value)\n          object[unpair(key)] = unpair(index);\n        return object;\n      }\n      case DATE:\n        return as(new Date(value), index);\n      case REGEXP: {\n        const {source, flags} = value;\n        return as(new RegExp(source, flags), index);\n      }\n      case MAP: {\n        const map = as(new Map, index);\n        for (const [key, index] of value)\n          map.set(unpair(key), unpair(index));\n        return map;\n      }\n      case SET: {\n        const set = as(new Set, index);\n        for (const index of value)\n          set.add(unpair(index));\n        return set;\n      }\n      case ERROR: {\n        const {name, message} = value;\n        return as(new env[name](message), index);\n      }\n      case BIGINT:\n        return as(BigInt(value), index);\n      case 'BigInt':\n        return as(Object(BigInt(value)), index);\n      case 'ArrayBuffer':\n        return as(new Uint8Array(value).buffer, value);\n      case 'DataView': {\n        const { buffer } = new Uint8Array(value);\n        return as(new DataView(buffer), value);\n      }\n    }\n    return as(new env[type](value), index);\n  };\n\n  return unpair;\n};\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns a deserialized value from a serialized array of Records.\n * @param {Record[]} serialized a previously serialized value.\n * @returns {any}\n */\nexport const deserialize = serialized => deserializer(new Map, serialized)(0);\n"], "names": [], "mappings": ";;;AAAA;;AAOA,MAAM,MAAM,OAAO,SAAS,WAAW,OAAO;AAE9C,MAAM,eAAe,CAAC,GAAG;IACvB,MAAM,KAAK,CAAC,KAAK;QACf,EAAE,GAAG,CAAC,OAAO;QACb,OAAO;IACT;IAEA,MAAM,SAAS,CAAA;QACb,IAAI,EAAE,GAAG,CAAC,QACR,OAAO,EAAE,GAAG,CAAC;QAEf,MAAM,CAAC,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM;QAC9B,OAAQ;YACN,KAAK,iKAAA,CAAA,YAAS;YACd,KAAK,iKAAA,CAAA,OAAI;gBACP,OAAO,GAAG,OAAO;YACnB,KAAK,iKAAA,CAAA,QAAK;gBAAE;oBACV,MAAM,MAAM,GAAG,EAAE,EAAE;oBACnB,KAAK,MAAM,SAAS,MAClB,IAAI,IAAI,CAAC,OAAO;oBAClB,OAAO;gBACT;YACA,KAAK,iKAAA,CAAA,SAAM;gBAAE;oBACX,MAAM,SAAS,GAAG,CAAC,GAAG;oBACtB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,MACzB,MAAM,CAAC,OAAO,KAAK,GAAG,OAAO;oBAC/B,OAAO;gBACT;YACA,KAAK,iKAAA,CAAA,OAAI;gBACP,OAAO,GAAG,IAAI,KAAK,QAAQ;YAC7B,KAAK,iKAAA,CAAA,SAAM;gBAAE;oBACX,MAAM,EAAC,MAAM,EAAE,KAAK,EAAC,GAAG;oBACxB,OAAO,GAAG,IAAI,OAAO,QAAQ,QAAQ;gBACvC;YACA,KAAK,iKAAA,CAAA,MAAG;gBAAE;oBACR,MAAM,MAAM,GAAG,IAAI,KAAK;oBACxB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,MACzB,IAAI,GAAG,CAAC,OAAO,MAAM,OAAO;oBAC9B,OAAO;gBACT;YACA,KAAK,iKAAA,CAAA,MAAG;gBAAE;oBACR,MAAM,MAAM,GAAG,IAAI,KAAK;oBACxB,KAAK,MAAM,SAAS,MAClB,IAAI,GAAG,CAAC,OAAO;oBACjB,OAAO;gBACT;YACA,KAAK,iKAAA,CAAA,QAAK;gBAAE;oBACV,MAAM,EAAC,IAAI,EAAE,OAAO,EAAC,GAAG;oBACxB,OAAO,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,UAAU;gBACpC;YACA,KAAK,iKAAA,CAAA,SAAM;gBACT,OAAO,GAAG,OAAO,QAAQ;YAC3B,KAAK;gBACH,OAAO,GAAG,OAAO,OAAO,SAAS;YACnC,KAAK;gBACH,OAAO,GAAG,IAAI,WAAW,OAAO,MAAM,EAAE;YAC1C,KAAK;gBAAY;oBACf,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,WAAW;oBAClC,OAAO,GAAG,IAAI,SAAS,SAAS;gBAClC;QACF;QACA,OAAO,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,QAAQ;IAClC;IAEA,OAAO;AACT;AAWO,MAAM,cAAc,CAAA,aAAc,aAAa,IAAI,KAAK,YAAY", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40ungap/structured-clone/esm/serialize.js"], "sourcesContent": ["import {\n  VOID, PRIMITIVE,\n  ARRAY, OBJECT,\n  DATE, REGEXP, MAP, SET,\n  ERROR, BIGINT\n} from './types.js';\n\nconst EMPTY = '';\n\nconst {toString} = {};\nconst {keys} = Object;\n\nconst typeOf = value => {\n  const type = typeof value;\n  if (type !== 'object' || !value)\n    return [PRIMITIVE, type];\n\n  const asString = toString.call(value).slice(8, -1);\n  switch (asString) {\n    case 'Array':\n      return [ARRAY, EMPTY];\n    case 'Object':\n      return [OBJECT, EMPTY];\n    case 'Date':\n      return [DATE, EMPTY];\n    case 'RegExp':\n      return [REGEXP, EMPTY];\n    case 'Map':\n      return [MAP, EMPTY];\n    case 'Set':\n      return [SET, EMPTY];\n    case 'DataView':\n      return [ARRAY, asString];\n  }\n\n  if (asString.includes('Array'))\n    return [ARRAY, asString];\n\n  if (asString.includes('Error'))\n    return [ERROR, asString];\n\n  return [OBJECT, asString];\n};\n\nconst shouldSkip = ([TYPE, type]) => (\n  TYPE === PRIMITIVE &&\n  (type === 'function' || type === 'symbol')\n);\n\nconst serializer = (strict, json, $, _) => {\n\n  const as = (out, value) => {\n    const index = _.push(out) - 1;\n    $.set(value, index);\n    return index;\n  };\n\n  const pair = value => {\n    if ($.has(value))\n      return $.get(value);\n\n    let [TYPE, type] = typeOf(value);\n    switch (TYPE) {\n      case PRIMITIVE: {\n        let entry = value;\n        switch (type) {\n          case 'bigint':\n            TYPE = BIGINT;\n            entry = value.toString();\n            break;\n          case 'function':\n          case 'symbol':\n            if (strict)\n              throw new TypeError('unable to serialize ' + type);\n            entry = null;\n            break;\n          case 'undefined':\n            return as([VOID], value);\n        }\n        return as([TYPE, entry], value);\n      }\n      case ARRAY: {\n        if (type) {\n          let spread = value;\n          if (type === 'DataView') {\n            spread = new Uint8Array(value.buffer);\n          }\n          else if (type === 'ArrayBuffer') {\n            spread = new Uint8Array(value);\n          }\n          return as([type, [...spread]], value);\n        }\n\n        const arr = [];\n        const index = as([TYPE, arr], value);\n        for (const entry of value)\n          arr.push(pair(entry));\n        return index;\n      }\n      case OBJECT: {\n        if (type) {\n          switch (type) {\n            case 'BigInt':\n              return as([type, value.toString()], value);\n            case 'Boolean':\n            case 'Number':\n            case 'String':\n              return as([type, value.valueOf()], value);\n          }\n        }\n\n        if (json && ('toJSON' in value))\n          return pair(value.toJSON());\n\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const key of keys(value)) {\n          if (strict || !shouldSkip(typeOf(value[key])))\n            entries.push([pair(key), pair(value[key])]);\n        }\n        return index;\n      }\n      case DATE:\n        return as([TYPE, value.toISOString()], value);\n      case REGEXP: {\n        const {source, flags} = value;\n        return as([TYPE, {source, flags}], value);\n      }\n      case MAP: {\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const [key, entry] of value) {\n          if (strict || !(shouldSkip(typeOf(key)) || shouldSkip(typeOf(entry))))\n            entries.push([pair(key), pair(entry)]);\n        }\n        return index;\n      }\n      case SET: {\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const entry of value) {\n          if (strict || !shouldSkip(typeOf(entry)))\n            entries.push(pair(entry));\n        }\n        return index;\n      }\n    }\n\n    const {message} = value;\n    return as([TYPE, {name: type, message}], value);\n  };\n\n  return pair;\n};\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns an array of serialized Records.\n * @param {any} value a serializable value.\n * @param {{json?: boolean, lossy?: boolean}?} options an object with a `lossy` or `json` property that,\n *  if `true`, will not throw errors on incompatible types, and behave more\n *  like JSON stringify would behave. Symbol and Function will be discarded.\n * @returns {Record[]}\n */\n export const serialize = (value, {json, lossy} = {}) => {\n  const _ = [];\n  return serializer(!(json || lossy), !!json, new Map, _)(value), _;\n};\n"], "names": [], "mappings": ";;;AAAA;;AAOA,MAAM,QAAQ;AAEd,MAAM,EAAC,QAAQ,EAAC,GAAG,CAAC;AACpB,MAAM,EAAC,IAAI,EAAC,GAAG;AAEf,MAAM,SAAS,CAAA;IACb,MAAM,OAAO,OAAO;IACpB,IAAI,SAAS,YAAY,CAAC,OACxB,OAAO;QAAC,iKAAA,CAAA,YAAS;QAAE;KAAK;IAE1B,MAAM,WAAW,SAAS,IAAI,CAAC,OAAO,KAAK,CAAC,GAAG,CAAC;IAChD,OAAQ;QACN,KAAK;YACH,OAAO;gBAAC,iKAAA,CAAA,QAAK;gBAAE;aAAM;QACvB,KAAK;YACH,OAAO;gBAAC,iKAAA,CAAA,SAAM;gBAAE;aAAM;QACxB,KAAK;YACH,OAAO;gBAAC,iKAAA,CAAA,OAAI;gBAAE;aAAM;QACtB,KAAK;YACH,OAAO;gBAAC,iKAAA,CAAA,SAAM;gBAAE;aAAM;QACxB,KAAK;YACH,OAAO;gBAAC,iKAAA,CAAA,MAAG;gBAAE;aAAM;QACrB,KAAK;YACH,OAAO;gBAAC,iKAAA,CAAA,MAAG;gBAAE;aAAM;QACrB,KAAK;YACH,OAAO;gBAAC,iKAAA,CAAA,QAAK;gBAAE;aAAS;IAC5B;IAEA,IAAI,SAAS,QAAQ,CAAC,UACpB,OAAO;QAAC,iKAAA,CAAA,QAAK;QAAE;KAAS;IAE1B,IAAI,SAAS,QAAQ,CAAC,UACpB,OAAO;QAAC,iKAAA,CAAA,QAAK;QAAE;KAAS;IAE1B,OAAO;QAAC,iKAAA,CAAA,SAAM;QAAE;KAAS;AAC3B;AAEA,MAAM,aAAa,CAAC,CAAC,MAAM,KAAK,GAC9B,SAAS,iKAAA,CAAA,YAAS,IAClB,CAAC,SAAS,cAAc,SAAS,QAAQ;AAG3C,MAAM,aAAa,CAAC,QAAQ,MAAM,GAAG;IAEnC,MAAM,KAAK,CAAC,KAAK;QACf,MAAM,QAAQ,EAAE,IAAI,CAAC,OAAO;QAC5B,EAAE,GAAG,CAAC,OAAO;QACb,OAAO;IACT;IAEA,MAAM,OAAO,CAAA;QACX,IAAI,EAAE,GAAG,CAAC,QACR,OAAO,EAAE,GAAG,CAAC;QAEf,IAAI,CAAC,MAAM,KAAK,GAAG,OAAO;QAC1B,OAAQ;YACN,KAAK,iKAAA,CAAA,YAAS;gBAAE;oBACd,IAAI,QAAQ;oBACZ,OAAQ;wBACN,KAAK;4BACH,OAAO,iKAAA,CAAA,SAAM;4BACb,QAAQ,MAAM,QAAQ;4BACtB;wBACF,KAAK;wBACL,KAAK;4BACH,IAAI,QACF,MAAM,IAAI,UAAU,yBAAyB;4BAC/C,QAAQ;4BACR;wBACF,KAAK;4BACH,OAAO,GAAG;gCAAC,iKAAA,CAAA,OAAI;6BAAC,EAAE;oBACtB;oBACA,OAAO,GAAG;wBAAC;wBAAM;qBAAM,EAAE;gBAC3B;YACA,KAAK,iKAAA,CAAA,QAAK;gBAAE;oBACV,IAAI,MAAM;wBACR,IAAI,SAAS;wBACb,IAAI,SAAS,YAAY;4BACvB,SAAS,IAAI,WAAW,MAAM,MAAM;wBACtC,OACK,IAAI,SAAS,eAAe;4BAC/B,SAAS,IAAI,WAAW;wBAC1B;wBACA,OAAO,GAAG;4BAAC;4BAAM;mCAAI;6BAAO;yBAAC,EAAE;oBACjC;oBAEA,MAAM,MAAM,EAAE;oBACd,MAAM,QAAQ,GAAG;wBAAC;wBAAM;qBAAI,EAAE;oBAC9B,KAAK,MAAM,SAAS,MAClB,IAAI,IAAI,CAAC,KAAK;oBAChB,OAAO;gBACT;YACA,KAAK,iKAAA,CAAA,SAAM;gBAAE;oBACX,IAAI,MAAM;wBACR,OAAQ;4BACN,KAAK;gCACH,OAAO,GAAG;oCAAC;oCAAM,MAAM,QAAQ;iCAAG,EAAE;4BACtC,KAAK;4BACL,KAAK;4BACL,KAAK;gCACH,OAAO,GAAG;oCAAC;oCAAM,MAAM,OAAO;iCAAG,EAAE;wBACvC;oBACF;oBAEA,IAAI,QAAS,YAAY,OACvB,OAAO,KAAK,MAAM,MAAM;oBAE1B,MAAM,UAAU,EAAE;oBAClB,MAAM,QAAQ,GAAG;wBAAC;wBAAM;qBAAQ,EAAE;oBAClC,KAAK,MAAM,OAAO,KAAK,OAAQ;wBAC7B,IAAI,UAAU,CAAC,WAAW,OAAO,KAAK,CAAC,IAAI,IACzC,QAAQ,IAAI,CAAC;4BAAC,KAAK;4BAAM,KAAK,KAAK,CAAC,IAAI;yBAAE;oBAC9C;oBACA,OAAO;gBACT;YACA,KAAK,iKAAA,CAAA,OAAI;gBACP,OAAO,GAAG;oBAAC;oBAAM,MAAM,WAAW;iBAAG,EAAE;YACzC,KAAK,iKAAA,CAAA,SAAM;gBAAE;oBACX,MAAM,EAAC,MAAM,EAAE,KAAK,EAAC,GAAG;oBACxB,OAAO,GAAG;wBAAC;wBAAM;4BAAC;4BAAQ;wBAAK;qBAAE,EAAE;gBACrC;YACA,KAAK,iKAAA,CAAA,MAAG;gBAAE;oBACR,MAAM,UAAU,EAAE;oBAClB,MAAM,QAAQ,GAAG;wBAAC;wBAAM;qBAAQ,EAAE;oBAClC,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,MAAO;wBAChC,IAAI,UAAU,CAAC,CAAC,WAAW,OAAO,SAAS,WAAW,OAAO,OAAO,GAClE,QAAQ,IAAI,CAAC;4BAAC,KAAK;4BAAM,KAAK;yBAAO;oBACzC;oBACA,OAAO;gBACT;YACA,KAAK,iKAAA,CAAA,MAAG;gBAAE;oBACR,MAAM,UAAU,EAAE;oBAClB,MAAM,QAAQ,GAAG;wBAAC;wBAAM;qBAAQ,EAAE;oBAClC,KAAK,MAAM,SAAS,MAAO;wBACzB,IAAI,UAAU,CAAC,WAAW,OAAO,SAC/B,QAAQ,IAAI,CAAC,KAAK;oBACtB;oBACA,OAAO;gBACT;QACF;QAEA,MAAM,EAAC,OAAO,EAAC,GAAG;QAClB,OAAO,GAAG;YAAC;YAAM;gBAAC,MAAM;gBAAM;YAAO;SAAE,EAAE;IAC3C;IAEA,OAAO;AACT;AAcQ,MAAM,YAAY,CAAC,OAAO,EAAC,IAAI,EAAE,KAAK,EAAC,GAAG,CAAC,CAAC;IAClD,MAAM,IAAI,EAAE;IACZ,OAAO,WAAW,CAAC,CAAC,QAAQ,KAAK,GAAG,CAAC,CAAC,MAAM,IAAI,KAAK,GAAG,QAAQ;AAClE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/%40ungap/structured-clone/esm/index.js"], "sourcesContent": ["import {deserialize} from './deserialize.js';\nimport {serialize} from './serialize.js';\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns an array of serialized Records.\n * @param {any} any a serializable value.\n * @param {{transfer?: any[], json?: boolean, lossy?: boolean}?} options an object with\n * a transfer option (ignored when polyfilled) and/or non standard fields that\n * fallback to the polyfill if present.\n * @returns {Record[]}\n */\nexport default typeof structuredClone === \"function\" ?\n  /* c8 ignore start */\n  (any, options) => (\n    options && ('json' in options || 'lossy' in options) ?\n      deserialize(serialize(any, options)) : structuredClone(any)\n  ) :\n  (any, options) => deserialize(serialize(any, options));\n  /* c8 ignore stop */\n\nexport {deserialize, serialize};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAce,OAAO,oBAAoB,aACxC,mBAAmB,GACnB,CAAC,KAAK,UACJ,WAAW,CAAC,UAAU,WAAW,WAAW,OAAO,IACjD,CAAA,GAAA,uKAAA,CAAA,cAAW,AAAD,EAAE,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD,EAAE,KAAK,YAAY,gBAAgB,OAE3D,CAAC,KAAK,UAAY,CAAA,GAAA,uKAAA,CAAA,cAAW,AAAD,EAAE,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD,EAAE,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-util-symbol/lib/codes.js"], "sourcesContent": ["/**\n * Character codes.\n *\n * This module is compiled away!\n *\n * micromark works based on character codes.\n * This module contains constants for the ASCII block and the replacement\n * character.\n * A couple of them are handled in a special way, such as the line endings\n * (CR, LF, and CR+LF, commonly known as end-of-line: EOLs), the tab (horizontal\n * tab) and its expansion based on what column it’s at (virtual space),\n * and the end-of-file (eof) character.\n * As values are preprocessed before handling them, the actual characters LF,\n * CR, HT, and NUL (which is present as the replacement character), are\n * guaranteed to not exist.\n *\n * Unicode basic latin block.\n */\nexport const codes = /** @type {const} */ ({\n  carriageReturn: -5,\n  lineFeed: -4,\n  carriageReturnLineFeed: -3,\n  horizontalTab: -2,\n  virtualSpace: -1,\n  eof: null,\n  nul: 0,\n  soh: 1,\n  stx: 2,\n  etx: 3,\n  eot: 4,\n  enq: 5,\n  ack: 6,\n  bel: 7,\n  bs: 8,\n  ht: 9, // `\\t`\n  lf: 10, // `\\n`\n  vt: 11, // `\\v`\n  ff: 12, // `\\f`\n  cr: 13, // `\\r`\n  so: 14,\n  si: 15,\n  dle: 16,\n  dc1: 17,\n  dc2: 18,\n  dc3: 19,\n  dc4: 20,\n  nak: 21,\n  syn: 22,\n  etb: 23,\n  can: 24,\n  em: 25,\n  sub: 26,\n  esc: 27,\n  fs: 28,\n  gs: 29,\n  rs: 30,\n  us: 31,\n  space: 32,\n  exclamationMark: 33, // `!`\n  quotationMark: 34, // `\"`\n  numberSign: 35, // `#`\n  dollarSign: 36, // `$`\n  percentSign: 37, // `%`\n  ampersand: 38, // `&`\n  apostrophe: 39, // `'`\n  leftParenthesis: 40, // `(`\n  rightParenthesis: 41, // `)`\n  asterisk: 42, // `*`\n  plusSign: 43, // `+`\n  comma: 44, // `,`\n  dash: 45, // `-`\n  dot: 46, // `.`\n  slash: 47, // `/`\n  digit0: 48, // `0`\n  digit1: 49, // `1`\n  digit2: 50, // `2`\n  digit3: 51, // `3`\n  digit4: 52, // `4`\n  digit5: 53, // `5`\n  digit6: 54, // `6`\n  digit7: 55, // `7`\n  digit8: 56, // `8`\n  digit9: 57, // `9`\n  colon: 58, // `:`\n  semicolon: 59, // `;`\n  lessThan: 60, // `<`\n  equalsTo: 61, // `=`\n  greaterThan: 62, // `>`\n  questionMark: 63, // `?`\n  atSign: 64, // `@`\n  uppercaseA: 65, // `A`\n  uppercaseB: 66, // `B`\n  uppercaseC: 67, // `C`\n  uppercaseD: 68, // `D`\n  uppercaseE: 69, // `E`\n  uppercaseF: 70, // `F`\n  uppercaseG: 71, // `G`\n  uppercaseH: 72, // `H`\n  uppercaseI: 73, // `I`\n  uppercaseJ: 74, // `J`\n  uppercaseK: 75, // `K`\n  uppercaseL: 76, // `L`\n  uppercaseM: 77, // `M`\n  uppercaseN: 78, // `N`\n  uppercaseO: 79, // `O`\n  uppercaseP: 80, // `P`\n  uppercaseQ: 81, // `Q`\n  uppercaseR: 82, // `R`\n  uppercaseS: 83, // `S`\n  uppercaseT: 84, // `T`\n  uppercaseU: 85, // `U`\n  uppercaseV: 86, // `V`\n  uppercaseW: 87, // `W`\n  uppercaseX: 88, // `X`\n  uppercaseY: 89, // `Y`\n  uppercaseZ: 90, // `Z`\n  leftSquareBracket: 91, // `[`\n  backslash: 92, // `\\`\n  rightSquareBracket: 93, // `]`\n  caret: 94, // `^`\n  underscore: 95, // `_`\n  graveAccent: 96, // `` ` ``\n  lowercaseA: 97, // `a`\n  lowercaseB: 98, // `b`\n  lowercaseC: 99, // `c`\n  lowercaseD: 100, // `d`\n  lowercaseE: 101, // `e`\n  lowercaseF: 102, // `f`\n  lowercaseG: 103, // `g`\n  lowercaseH: 104, // `h`\n  lowercaseI: 105, // `i`\n  lowercaseJ: 106, // `j`\n  lowercaseK: 107, // `k`\n  lowercaseL: 108, // `l`\n  lowercaseM: 109, // `m`\n  lowercaseN: 110, // `n`\n  lowercaseO: 111, // `o`\n  lowercaseP: 112, // `p`\n  lowercaseQ: 113, // `q`\n  lowercaseR: 114, // `r`\n  lowercaseS: 115, // `s`\n  lowercaseT: 116, // `t`\n  lowercaseU: 117, // `u`\n  lowercaseV: 118, // `v`\n  lowercaseW: 119, // `w`\n  lowercaseX: 120, // `x`\n  lowercaseY: 121, // `y`\n  lowercaseZ: 122, // `z`\n  leftCurlyBrace: 123, // `{`\n  verticalBar: 124, // `|`\n  rightCurlyBrace: 125, // `}`\n  tilde: 126, // `~`\n  del: 127,\n  // Unicode Specials block.\n  byteOrderMarker: 65_279,\n  // Unicode Specials block.\n  replacementCharacter: 65_533 // `�`\n})\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;CAiBC;;;AACM,MAAM,QAA8B;IACzC,gBAAgB,CAAC;IACjB,UAAU,CAAC;IACX,wBAAwB,CAAC;IACzB,eAAe,CAAC;IAChB,cAAc,CAAC;IACf,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,iBAAiB;IACjB,eAAe;IACf,YAAY;IACZ,YAAY;IACZ,aAAa;IACb,WAAW;IACX,YAAY;IACZ,iBAAiB;IACjB,kBAAkB;IAClB,UAAU;IACV,UAAU;IACV,OAAO;IACP,MAAM;IACN,KAAK;IACL,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,WAAW;IACX,UAAU;IACV,UAAU;IACV,aAAa;IACb,cAAc;IACd,QAAQ;IACR,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,mBAAmB;IACnB,WAAW;IACX,oBAAoB;IACpB,OAAO;IACP,YAAY;IACZ,aAAa;IACb,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,gBAAgB;IAChB,aAAa;IACb,iBAAiB;IACjB,OAAO;IACP,KAAK;IACL,0BAA0B;IAC1B,iBAAiB;IACjB,0BAA0B;IAC1B,sBAAsB,OAAO,MAAM;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-util-character/dev/index.js"], "sourcesContent": ["/**\n * @import {Code} from 'micromark-util-types'\n */\n\nimport {codes} from 'micromark-util-symbol'\n\n/**\n * Check whether the character code represents an ASCII alpha (`a` through `z`,\n * case insensitive).\n *\n * An **ASCII alpha** is an ASCII upper alpha or ASCII lower alpha.\n *\n * An **ASCII upper alpha** is a character in the inclusive range U+0041 (`A`)\n * to U+005A (`Z`).\n *\n * An **ASCII lower alpha** is a character in the inclusive range U+0061 (`a`)\n * to U+007A (`z`).\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiAlpha = regexCheck(/[A-Za-z]/)\n\n/**\n * Check whether the character code represents an ASCII alphanumeric (`a`\n * through `z`, case insensitive, or `0` through `9`).\n *\n * An **ASCII alphanumeric** is an ASCII digit (see `asciiDigit`) or ASCII alpha\n * (see `asciiAlpha`).\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiAlphanumeric = regexCheck(/[\\dA-Za-z]/)\n\n/**\n * Check whether the character code represents an ASCII atext.\n *\n * atext is an ASCII alphanumeric (see `asciiAlphanumeric`), or a character in\n * the inclusive ranges U+0023 NUMBER SIGN (`#`) to U+0027 APOSTROPHE (`'`),\n * U+002A ASTERISK (`*`), U+002B PLUS SIGN (`+`), U+002D DASH (`-`), U+002F\n * SLASH (`/`), U+003D EQUALS TO (`=`), U+003F QUESTION MARK (`?`), U+005E\n * CARET (`^`) to U+0060 GRAVE ACCENT (`` ` ``), or U+007B LEFT CURLY BRACE\n * (`{`) to U+007E TILDE (`~`).\n *\n * See:\n * **\\[RFC5322]**:\n * [Internet Message Format](https://tools.ietf.org/html/rfc5322).\n * P. Resnick.\n * IETF.\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiAtext = regexCheck(/[#-'*+\\--9=?A-Z^-~]/)\n\n/**\n * Check whether a character code is an ASCII control character.\n *\n * An **ASCII control** is a character in the inclusive range U+0000 NULL (NUL)\n * to U+001F (US), or U+007F (DEL).\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function asciiControl(code) {\n  return (\n    // Special whitespace codes (which have negative values), C0 and Control\n    // character DEL\n    code !== null && (code < codes.space || code === codes.del)\n  )\n}\n\n/**\n * Check whether the character code represents an ASCII digit (`0` through `9`).\n *\n * An **ASCII digit** is a character in the inclusive range U+0030 (`0`) to\n * U+0039 (`9`).\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiDigit = regexCheck(/\\d/)\n\n/**\n * Check whether the character code represents an ASCII hex digit (`a` through\n * `f`, case insensitive, or `0` through `9`).\n *\n * An **ASCII hex digit** is an ASCII digit (see `asciiDigit`), ASCII upper hex\n * digit, or an ASCII lower hex digit.\n *\n * An **ASCII upper hex digit** is a character in the inclusive range U+0041\n * (`A`) to U+0046 (`F`).\n *\n * An **ASCII lower hex digit** is a character in the inclusive range U+0061\n * (`a`) to U+0066 (`f`).\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiHexDigit = regexCheck(/[\\dA-Fa-f]/)\n\n/**\n * Check whether the character code represents ASCII punctuation.\n *\n * An **ASCII punctuation** is a character in the inclusive ranges U+0021\n * EXCLAMATION MARK (`!`) to U+002F SLASH (`/`), U+003A COLON (`:`) to U+0040 AT\n * SIGN (`@`), U+005B LEFT SQUARE BRACKET (`[`) to U+0060 GRAVE ACCENT\n * (`` ` ``), or U+007B LEFT CURLY BRACE (`{`) to U+007E TILDE (`~`).\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiPunctuation = regexCheck(/[!-/:-@[-`{-~]/)\n\n/**\n * Check whether a character code is a markdown line ending.\n *\n * A **markdown line ending** is the virtual characters M-0003 CARRIAGE RETURN\n * LINE FEED (CRLF), M-0004 LINE FEED (LF) and M-0005 CARRIAGE RETURN (CR).\n *\n * In micromark, the actual character U+000A LINE FEED (LF) and U+000D CARRIAGE\n * RETURN (CR) are replaced by these virtual characters depending on whether\n * they occurred together.\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function markdownLineEnding(code) {\n  return code !== null && code < codes.horizontalTab\n}\n\n/**\n * Check whether a character code is a markdown line ending (see\n * `markdownLineEnding`) or markdown space (see `markdownSpace`).\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function markdownLineEndingOrSpace(code) {\n  return code !== null && (code < codes.nul || code === codes.space)\n}\n\n/**\n * Check whether a character code is a markdown space.\n *\n * A **markdown space** is the concrete character U+0020 SPACE (SP) and the\n * virtual characters M-0001 VIRTUAL SPACE (VS) and M-0002 HORIZONTAL TAB (HT).\n *\n * In micromark, the actual character U+0009 CHARACTER TABULATION (HT) is\n * replaced by one M-0002 HORIZONTAL TAB (HT) and between 0 and 3 M-0001 VIRTUAL\n * SPACE (VS) characters, depending on the column at which the tab occurred.\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function markdownSpace(code) {\n  return (\n    code === codes.horizontalTab ||\n    code === codes.virtualSpace ||\n    code === codes.space\n  )\n}\n\n// Size note: removing ASCII from the regex and using `asciiPunctuation` here\n// In fact adds to the bundle size.\n/**\n * Check whether the character code represents Unicode punctuation.\n *\n * A **Unicode punctuation** is a character in the Unicode `Pc` (Punctuation,\n * Connector), `Pd` (Punctuation, Dash), `Pe` (Punctuation, Close), `Pf`\n * (Punctuation, Final quote), `Pi` (Punctuation, Initial quote), `Po`\n * (Punctuation, Other), or `Ps` (Punctuation, Open) categories, or an ASCII\n * punctuation (see `asciiPunctuation`).\n *\n * See:\n * **\\[UNICODE]**:\n * [The Unicode Standard](https://www.unicode.org/versions/).\n * Unicode Consortium.\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const unicodePunctuation = regexCheck(/\\p{P}|\\p{S}/u)\n\n/**\n * Check whether the character code represents Unicode whitespace.\n *\n * Note that this does handle micromark specific markdown whitespace characters.\n * See `markdownLineEndingOrSpace` to check that.\n *\n * A **Unicode whitespace** is a character in the Unicode `Zs` (Separator,\n * Space) category, or U+0009 CHARACTER TABULATION (HT), U+000A LINE FEED (LF),\n * U+000C (FF), or U+000D CARRIAGE RETURN (CR) (**\\[UNICODE]**).\n *\n * See:\n * **\\[UNICODE]**:\n * [The Unicode Standard](https://www.unicode.org/versions/).\n * Unicode Consortium.\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const unicodeWhitespace = regexCheck(/\\s/)\n\n/**\n * Create a code check from a regex.\n *\n * @param {RegExp} regex\n *   Expression.\n * @returns {(code: Code) => boolean}\n *   Check.\n */\nfunction regexCheck(regex) {\n  return check\n\n  /**\n   * Check whether a code matches the bound regex.\n   *\n   * @param {Code} code\n   *   Character code.\n   * @returns {boolean}\n   *   Whether the character code matches the bound regex.\n   */\n  function check(code) {\n    return code !== null && code > -1 && regex.test(String.fromCharCode(code))\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;AAED;;AAmBO,MAAM,aAAa,WAAW;AAc9B,MAAM,oBAAoB,WAAW;AAuBrC,MAAM,aAAa,WAAW;AAa9B,SAAS,aAAa,IAAI;IAC/B,OACE,wEAAwE;IACxE,gBAAgB;IAChB,SAAS,QAAQ,CAAC,OAAO,8JAAA,CAAA,QAAK,CAAC,KAAK,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG;AAE9D;AAaO,MAAM,aAAa,WAAW;AAoB9B,MAAM,gBAAgB,WAAW;AAejC,MAAM,mBAAmB,WAAW;AAiBpC,SAAS,mBAAmB,IAAI;IACrC,OAAO,SAAS,QAAQ,OAAO,8JAAA,CAAA,QAAK,CAAC,aAAa;AACpD;AAWO,SAAS,0BAA0B,IAAI;IAC5C,OAAO,SAAS,QAAQ,CAAC,OAAO,8JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK;AACnE;AAiBO,SAAS,cAAc,IAAI;IAChC,OACE,SAAS,8JAAA,CAAA,QAAK,CAAC,aAAa,IAC5B,SAAS,8JAAA,CAAA,QAAK,CAAC,YAAY,IAC3B,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK;AAExB;AAuBO,MAAM,qBAAqB,WAAW;AAsBtC,MAAM,oBAAoB,WAAW;AAE5C;;;;;;;CAOC,GACD,SAAS,WAAW,KAAK;IACvB,OAAO;;IAEP;;;;;;;GAOC,GACD,SAAS,MAAM,IAAI;QACjB,OAAO,SAAS,QAAQ,OAAO,CAAC,KAAK,MAAM,IAAI,CAAC,OAAO,YAAY,CAAC;IACtE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 599, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-util-encode/index.js"], "sourcesContent": ["const characterReferences = {'\"': 'quot', '&': 'amp', '<': 'lt', '>': 'gt'}\n\n/**\n * Encode only the dangerous HTML characters.\n *\n * This ensures that certain characters which have special meaning in HTML are\n * dealt with.\n * Technically, we can skip `>` and `\"` in many cases, but CM includes them.\n *\n * @param {string} value\n *   Value to encode.\n * @returns {string}\n *   Encoded value.\n */\nexport function encode(value) {\n  return value.replace(/[\"&<>]/g, replace)\n\n  /**\n   * @param {string} value\n   *   Value to replace.\n   * @returns {string}\n   *   Encoded value.\n   */\n  function replace(value) {\n    return (\n      '&' +\n      characterReferences[\n        /** @type {keyof typeof characterReferences} */ (value)\n      ] +\n      ';'\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,MAAM,sBAAsB;IAAC,KAAK;IAAQ,KAAK;IAAO,KAAK;IAAM,KAAK;AAAI;AAcnE,SAAS,OAAO,KAAK;IAC1B,OAAO,MAAM,OAAO,CAAC,WAAW;;IAEhC;;;;;GAKC,GACD,SAAS,QAAQ,KAAK;QACpB,OACE,MACA,mBAAmB,CACgC,MAClD,GACD;IAEJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 626, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-util-symbol/lib/values.js"], "sourcesContent": ["/**\n * This module is compiled away!\n *\n * While micromark works based on character codes, this module includes the\n * string versions of ’em.\n * The C0 block, except for LF, CR, HT, and w/ the replacement character added,\n * are available here.\n */\nexport const values = /** @type {const} */ ({\n  ht: '\\t',\n  lf: '\\n',\n  cr: '\\r',\n  space: ' ',\n  exclamationMark: '!',\n  quotationMark: '\"',\n  numberSign: '#',\n  dollarSign: '$',\n  percentSign: '%',\n  ampersand: '&',\n  apostrophe: \"'\",\n  leftParenthesis: '(',\n  rightParenthesis: ')',\n  asterisk: '*',\n  plusSign: '+',\n  comma: ',',\n  dash: '-',\n  dot: '.',\n  slash: '/',\n  digit0: '0',\n  digit1: '1',\n  digit2: '2',\n  digit3: '3',\n  digit4: '4',\n  digit5: '5',\n  digit6: '6',\n  digit7: '7',\n  digit8: '8',\n  digit9: '9',\n  colon: ':',\n  semicolon: ';',\n  lessThan: '<',\n  equalsTo: '=',\n  greaterThan: '>',\n  questionMark: '?',\n  atSign: '@',\n  uppercaseA: 'A',\n  uppercaseB: 'B',\n  uppercaseC: 'C',\n  uppercaseD: 'D',\n  uppercaseE: 'E',\n  uppercaseF: 'F',\n  uppercaseG: 'G',\n  uppercaseH: 'H',\n  uppercaseI: 'I',\n  uppercaseJ: 'J',\n  uppercaseK: 'K',\n  uppercaseL: 'L',\n  uppercaseM: 'M',\n  uppercaseN: 'N',\n  uppercaseO: 'O',\n  uppercaseP: 'P',\n  uppercaseQ: 'Q',\n  uppercaseR: 'R',\n  uppercaseS: 'S',\n  uppercaseT: 'T',\n  uppercaseU: 'U',\n  uppercaseV: 'V',\n  uppercaseW: 'W',\n  uppercaseX: 'X',\n  uppercaseY: 'Y',\n  uppercaseZ: 'Z',\n  leftSquareBracket: '[',\n  backslash: '\\\\',\n  rightSquareBracket: ']',\n  caret: '^',\n  underscore: '_',\n  graveAccent: '`',\n  lowercaseA: 'a',\n  lowercaseB: 'b',\n  lowercaseC: 'c',\n  lowercaseD: 'd',\n  lowercaseE: 'e',\n  lowercaseF: 'f',\n  lowercaseG: 'g',\n  lowercaseH: 'h',\n  lowercaseI: 'i',\n  lowercaseJ: 'j',\n  lowercaseK: 'k',\n  lowercaseL: 'l',\n  lowercaseM: 'm',\n  lowercaseN: 'n',\n  lowercaseO: 'o',\n  lowercaseP: 'p',\n  lowercaseQ: 'q',\n  lowercaseR: 'r',\n  lowercaseS: 's',\n  lowercaseT: 't',\n  lowercaseU: 'u',\n  lowercaseV: 'v',\n  lowercaseW: 'w',\n  lowercaseX: 'x',\n  lowercaseY: 'y',\n  lowercaseZ: 'z',\n  leftCurlyBrace: '{',\n  verticalBar: '|',\n  rightCurlyBrace: '}',\n  tilde: '~',\n  replacementCharacter: '�'\n})\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACM,MAAM,SAA+B;IAC1C,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,iBAAiB;IACjB,eAAe;IACf,YAAY;IACZ,YAAY;IACZ,aAAa;IACb,WAAW;IACX,YAAY;IACZ,iBAAiB;IACjB,kBAAkB;IAClB,UAAU;IACV,UAAU;IACV,OAAO;IACP,MAAM;IACN,KAAK;IACL,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,WAAW;IACX,UAAU;IACV,UAAU;IACV,aAAa;IACb,cAAc;IACd,QAAQ;IACR,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,mBAAmB;IACnB,WAAW;IACX,oBAAoB;IACpB,OAAO;IACP,YAAY;IACZ,aAAa;IACb,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,gBAAgB;IAChB,aAAa;IACb,iBAAiB;IACjB,OAAO;IACP,sBAAsB;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 743, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-util-sanitize-uri/dev/index.js"], "sourcesContent": ["import {asciiAlphanumeric} from 'micromark-util-character'\nimport {encode} from 'micromark-util-encode'\nimport {codes, values} from 'micromark-util-symbol'\n\n/**\n * Make a value safe for injection as a URL.\n *\n * This encodes unsafe characters with percent-encoding and skips already\n * encoded sequences (see `normalizeUri`).\n * Further unsafe characters are encoded as character references (see\n * `micromark-util-encode`).\n *\n * A regex of allowed protocols can be given, in which case the URL is\n * sanitized.\n * For example, `/^(https?|ircs?|mailto|xmpp)$/i` can be used for `a[href]`, or\n * `/^https?$/i` for `img[src]` (this is what `github.com` allows).\n * If the URL includes an unknown protocol (one not matched by `protocol`, such\n * as a dangerous example, `javascript:`), the value is ignored.\n *\n * @param {string | null | undefined} url\n *   URI to sanitize.\n * @param {RegExp | null | undefined} [protocol]\n *   Allowed protocols.\n * @returns {string}\n *   Sanitized URI.\n */\nexport function sanitizeUri(url, protocol) {\n  const value = encode(normalizeUri(url || ''))\n\n  if (!protocol) {\n    return value\n  }\n\n  const colon = value.indexOf(':')\n  const questionMark = value.indexOf('?')\n  const numberSign = value.indexOf('#')\n  const slash = value.indexOf('/')\n\n  if (\n    // If there is no protocol, it’s relative.\n    colon < 0 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash > -1 && colon > slash) ||\n    (questionMark > -1 && colon > questionMark) ||\n    (numberSign > -1 && colon > numberSign) ||\n    // It is a protocol, it should be allowed.\n    protocol.test(value.slice(0, colon))\n  ) {\n    return value\n  }\n\n  return ''\n}\n\n/**\n * Normalize a URL.\n *\n * Encode unsafe characters with percent-encoding, skipping already encoded\n * sequences.\n *\n * @param {string} value\n *   URI to normalize.\n * @returns {string}\n *   Normalized URI.\n */\nexport function normalizeUri(value) {\n  /** @type {Array<string>} */\n  const result = []\n  let index = -1\n  let start = 0\n  let skip = 0\n\n  while (++index < value.length) {\n    const code = value.charCodeAt(index)\n    /** @type {string} */\n    let replace = ''\n\n    // A correct percent encoded value.\n    if (\n      code === codes.percentSign &&\n      asciiAlphanumeric(value.charCodeAt(index + 1)) &&\n      asciiAlphanumeric(value.charCodeAt(index + 2))\n    ) {\n      skip = 2\n    }\n    // ASCII.\n    else if (code < 128) {\n      if (!/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(code))) {\n        replace = String.fromCharCode(code)\n      }\n    }\n    // Astral.\n    else if (code > 55_295 && code < 57_344) {\n      const next = value.charCodeAt(index + 1)\n\n      // A correct surrogate pair.\n      if (code < 56_320 && next > 56_319 && next < 57_344) {\n        replace = String.fromCharCode(code, next)\n        skip = 1\n      }\n      // Lone surrogate.\n      else {\n        replace = values.replacementCharacter\n      }\n    }\n    // Unicode.\n    else {\n      replace = String.fromCharCode(code)\n    }\n\n    if (replace) {\n      result.push(value.slice(start, index), encodeURIComponent(replace))\n      start = index + skip + 1\n      replace = ''\n    }\n\n    if (skip) {\n      index += skip\n      skip = 0\n    }\n  }\n\n  return result.join('') + value.slice(start)\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;;;;AAwBO,SAAS,YAAY,GAAG,EAAE,QAAQ;IACvC,MAAM,QAAQ,CAAA,GAAA,uJAAA,CAAA,SAAM,AAAD,EAAE,aAAa,OAAO;IAEzC,IAAI,CAAC,UAAU;QACb,OAAO;IACT;IAEA,MAAM,QAAQ,MAAM,OAAO,CAAC;IAC5B,MAAM,eAAe,MAAM,OAAO,CAAC;IACnC,MAAM,aAAa,MAAM,OAAO,CAAC;IACjC,MAAM,QAAQ,MAAM,OAAO,CAAC;IAE5B,IACE,0CAA0C;IAC1C,QAAQ,KAEP,QAAQ,CAAC,KAAK,QAAQ,SACtB,eAAe,CAAC,KAAK,QAAQ,gBAC7B,aAAa,CAAC,KAAK,QAAQ,cAC5B,0CAA0C;IAC1C,SAAS,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,SAC7B;QACA,OAAO;IACT;IAEA,OAAO;AACT;AAaO,SAAS,aAAa,KAAK;IAChC,0BAA0B,GAC1B,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,CAAC;IACb,IAAI,QAAQ;IACZ,IAAI,OAAO;IAEX,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,MAAM,OAAO,MAAM,UAAU,CAAC;QAC9B,mBAAmB,GACnB,IAAI,UAAU;QAEd,mCAAmC;QACnC,IACE,SAAS,8JAAA,CAAA,QAAK,CAAC,WAAW,IAC1B,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,UAAU,CAAC,QAAQ,OAC3C,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,UAAU,CAAC,QAAQ,KAC3C;YACA,OAAO;QACT,OAEK,IAAI,OAAO,KAAK;YACnB,IAAI,CAAC,oBAAoB,IAAI,CAAC,OAAO,YAAY,CAAC,QAAQ;gBACxD,UAAU,OAAO,YAAY,CAAC;YAChC;QACF,OAEK,IAAI,OAAO,UAAU,OAAO,QAAQ;YACvC,MAAM,OAAO,MAAM,UAAU,CAAC,QAAQ;YAEtC,4BAA4B;YAC5B,IAAI,OAAO,UAAU,OAAO,UAAU,OAAO,QAAQ;gBACnD,UAAU,OAAO,YAAY,CAAC,MAAM;gBACpC,OAAO;YACT,OAEK;gBACH,UAAU,+JAAA,CAAA,SAAM,CAAC,oBAAoB;YACvC;QACF,OAEK;YACH,UAAU,OAAO,YAAY,CAAC;QAChC;QAEA,IAAI,SAAS;YACX,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,OAAO,QAAQ,mBAAmB;YAC1D,QAAQ,QAAQ,OAAO;YACvB,UAAU;QACZ;QAEA,IAAI,MAAM;YACR,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO,OAAO,IAAI,CAAC,MAAM,MAAM,KAAK,CAAC;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 815, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/footer.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('./state.js').State} State\n */\n\n/**\n * @callback FootnoteBackContentTemplate\n *   Generate content for the backreference dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent> | ElementContent | string}\n *   Content for the backreference when linking back from definitions to their\n *   reference.\n *\n * @callback FootnoteBackLabelTemplate\n *   Generate a back label dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Back label to use when linking back from definitions to their reference.\n */\n\nimport structuredClone from '@ungap/structured-clone'\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Generate the default content that GitHub uses on backreferences.\n *\n * @param {number} _\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent>}\n *   Content.\n */\nexport function defaultFootnoteBackContent(_, rereferenceIndex) {\n  /** @type {Array<ElementContent>} */\n  const result = [{type: 'text', value: '↩'}]\n\n  if (rereferenceIndex > 1) {\n    result.push({\n      type: 'element',\n      tagName: 'sup',\n      properties: {},\n      children: [{type: 'text', value: String(rereferenceIndex)}]\n    })\n  }\n\n  return result\n}\n\n/**\n * Generate the default label that GitHub uses on backreferences.\n *\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Label.\n */\nexport function defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n  return (\n    'Back to reference ' +\n    (referenceIndex + 1) +\n    (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n  )\n}\n\n/**\n * Generate a hast footer for called footnote definitions.\n *\n * @param {State} state\n *   Info passed around.\n * @returns {Element | undefined}\n *   `section` element or `undefined`.\n */\n// eslint-disable-next-line complexity\nexport function footer(state) {\n  const clobberPrefix =\n    typeof state.options.clobberPrefix === 'string'\n      ? state.options.clobberPrefix\n      : 'user-content-'\n  const footnoteBackContent =\n    state.options.footnoteBackContent || defaultFootnoteBackContent\n  const footnoteBackLabel =\n    state.options.footnoteBackLabel || defaultFootnoteBackLabel\n  const footnoteLabel = state.options.footnoteLabel || 'Footnotes'\n  const footnoteLabelTagName = state.options.footnoteLabelTagName || 'h2'\n  const footnoteLabelProperties = state.options.footnoteLabelProperties || {\n    className: ['sr-only']\n  }\n  /** @type {Array<ElementContent>} */\n  const listItems = []\n  let referenceIndex = -1\n\n  while (++referenceIndex < state.footnoteOrder.length) {\n    const definition = state.footnoteById.get(\n      state.footnoteOrder[referenceIndex]\n    )\n\n    if (!definition) {\n      continue\n    }\n\n    const content = state.all(definition)\n    const id = String(definition.identifier).toUpperCase()\n    const safeId = normalizeUri(id.toLowerCase())\n    let rereferenceIndex = 0\n    /** @type {Array<ElementContent>} */\n    const backReferences = []\n    const counts = state.footnoteCounts.get(id)\n\n    // eslint-disable-next-line no-unmodified-loop-condition\n    while (counts !== undefined && ++rereferenceIndex <= counts) {\n      if (backReferences.length > 0) {\n        backReferences.push({type: 'text', value: ' '})\n      }\n\n      let children =\n        typeof footnoteBackContent === 'string'\n          ? footnoteBackContent\n          : footnoteBackContent(referenceIndex, rereferenceIndex)\n\n      if (typeof children === 'string') {\n        children = {type: 'text', value: children}\n      }\n\n      backReferences.push({\n        type: 'element',\n        tagName: 'a',\n        properties: {\n          href:\n            '#' +\n            clobberPrefix +\n            'fnref-' +\n            safeId +\n            (rereferenceIndex > 1 ? '-' + rereferenceIndex : ''),\n          dataFootnoteBackref: '',\n          ariaLabel:\n            typeof footnoteBackLabel === 'string'\n              ? footnoteBackLabel\n              : footnoteBackLabel(referenceIndex, rereferenceIndex),\n          className: ['data-footnote-backref']\n        },\n        children: Array.isArray(children) ? children : [children]\n      })\n    }\n\n    const tail = content[content.length - 1]\n\n    if (tail && tail.type === 'element' && tail.tagName === 'p') {\n      const tailTail = tail.children[tail.children.length - 1]\n      if (tailTail && tailTail.type === 'text') {\n        tailTail.value += ' '\n      } else {\n        tail.children.push({type: 'text', value: ' '})\n      }\n\n      tail.children.push(...backReferences)\n    } else {\n      content.push(...backReferences)\n    }\n\n    /** @type {Element} */\n    const listItem = {\n      type: 'element',\n      tagName: 'li',\n      properties: {id: clobberPrefix + 'fn-' + safeId},\n      children: state.wrap(content, true)\n    }\n\n    state.patch(definition, listItem)\n\n    listItems.push(listItem)\n  }\n\n  if (listItems.length === 0) {\n    return\n  }\n\n  return {\n    type: 'element',\n    tagName: 'section',\n    properties: {dataFootnotes: true, className: ['footnotes']},\n    children: [\n      {\n        type: 'element',\n        tagName: footnoteLabelTagName,\n        properties: {\n          ...structuredClone(footnoteLabelProperties),\n          id: 'footnote-label'\n        },\n        children: [{type: 'text', value: footnoteLabel}]\n      },\n      {type: 'text', value: '\\n'},\n      {\n        type: 'element',\n        tagName: 'ol',\n        properties: {},\n        children: state.wrap(listItems, true)\n      },\n      {type: 'text', value: '\\n'}\n    ]\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAyDC;;;;;AAED;AACA;;;AAaO,SAAS,2BAA2B,CAAC,EAAE,gBAAgB;IAC5D,kCAAkC,GAClC,MAAM,SAAS;QAAC;YAAC,MAAM;YAAQ,OAAO;QAAG;KAAE;IAE3C,IAAI,mBAAmB,GAAG;QACxB,OAAO,IAAI,CAAC;YACV,MAAM;YACN,SAAS;YACT,YAAY,CAAC;YACb,UAAU;gBAAC;oBAAC,MAAM;oBAAQ,OAAO,OAAO;gBAAiB;aAAE;QAC7D;IACF;IAEA,OAAO;AACT;AAaO,SAAS,yBAAyB,cAAc,EAAE,gBAAgB;IACvE,OACE,uBACA,CAAC,iBAAiB,CAAC,IACnB,CAAC,mBAAmB,IAAI,MAAM,mBAAmB,EAAE;AAEvD;AAWO,SAAS,OAAO,KAAK;IAC1B,MAAM,gBACJ,OAAO,MAAM,OAAO,CAAC,aAAa,KAAK,WACnC,MAAM,OAAO,CAAC,aAAa,GAC3B;IACN,MAAM,sBACJ,MAAM,OAAO,CAAC,mBAAmB,IAAI;IACvC,MAAM,oBACJ,MAAM,OAAO,CAAC,iBAAiB,IAAI;IACrC,MAAM,gBAAgB,MAAM,OAAO,CAAC,aAAa,IAAI;IACrD,MAAM,uBAAuB,MAAM,OAAO,CAAC,oBAAoB,IAAI;IACnE,MAAM,0BAA0B,MAAM,OAAO,CAAC,uBAAuB,IAAI;QACvE,WAAW;YAAC;SAAU;IACxB;IACA,kCAAkC,GAClC,MAAM,YAAY,EAAE;IACpB,IAAI,iBAAiB,CAAC;IAEtB,MAAO,EAAE,iBAAiB,MAAM,aAAa,CAAC,MAAM,CAAE;QACpD,MAAM,aAAa,MAAM,YAAY,CAAC,GAAG,CACvC,MAAM,aAAa,CAAC,eAAe;QAGrC,IAAI,CAAC,YAAY;YACf;QACF;QAEA,MAAM,UAAU,MAAM,GAAG,CAAC;QAC1B,MAAM,KAAK,OAAO,WAAW,UAAU,EAAE,WAAW;QACpD,MAAM,SAAS,CAAA,GAAA,uKAAA,CAAA,eAAY,AAAD,EAAE,GAAG,WAAW;QAC1C,IAAI,mBAAmB;QACvB,kCAAkC,GAClC,MAAM,iBAAiB,EAAE;QACzB,MAAM,SAAS,MAAM,cAAc,CAAC,GAAG,CAAC;QAExC,wDAAwD;QACxD,MAAO,WAAW,aAAa,EAAE,oBAAoB,OAAQ;YAC3D,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,eAAe,IAAI,CAAC;oBAAC,MAAM;oBAAQ,OAAO;gBAAG;YAC/C;YAEA,IAAI,WACF,OAAO,wBAAwB,WAC3B,sBACA,oBAAoB,gBAAgB;YAE1C,IAAI,OAAO,aAAa,UAAU;gBAChC,WAAW;oBAAC,MAAM;oBAAQ,OAAO;gBAAQ;YAC3C;YAEA,eAAe,IAAI,CAAC;gBAClB,MAAM;gBACN,SAAS;gBACT,YAAY;oBACV,MACE,MACA,gBACA,WACA,SACA,CAAC,mBAAmB,IAAI,MAAM,mBAAmB,EAAE;oBACrD,qBAAqB;oBACrB,WACE,OAAO,sBAAsB,WACzB,oBACA,kBAAkB,gBAAgB;oBACxC,WAAW;wBAAC;qBAAwB;gBACtC;gBACA,UAAU,MAAM,OAAO,CAAC,YAAY,WAAW;oBAAC;iBAAS;YAC3D;QACF;QAEA,MAAM,OAAO,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;QAExC,IAAI,QAAQ,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK,KAAK;YAC3D,MAAM,WAAW,KAAK,QAAQ,CAAC,KAAK,QAAQ,CAAC,MAAM,GAAG,EAAE;YACxD,IAAI,YAAY,SAAS,IAAI,KAAK,QAAQ;gBACxC,SAAS,KAAK,IAAI;YACpB,OAAO;gBACL,KAAK,QAAQ,CAAC,IAAI,CAAC;oBAAC,MAAM;oBAAQ,OAAO;gBAAG;YAC9C;YAEA,KAAK,QAAQ,CAAC,IAAI,IAAI;QACxB,OAAO;YACL,QAAQ,IAAI,IAAI;QAClB;QAEA,oBAAoB,GACpB,MAAM,WAAW;YACf,MAAM;YACN,SAAS;YACT,YAAY;gBAAC,IAAI,gBAAgB,QAAQ;YAAM;YAC/C,UAAU,MAAM,IAAI,CAAC,SAAS;QAChC;QAEA,MAAM,KAAK,CAAC,YAAY;QAExB,UAAU,IAAI,CAAC;IACjB;IAEA,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B;IACF;IAEA,OAAO;QACL,MAAM;QACN,SAAS;QACT,YAAY;YAAC,eAAe;YAAM,WAAW;gBAAC;aAAY;QAAA;QAC1D,UAAU;YACR;gBACE,MAAM;gBACN,SAAS;gBACT,YAAY;oBACV,GAAG,CAAA,GAAA,iLAAA,CAAA,UAAe,AAAD,EAAE,wBAAwB;oBAC3C,IAAI;gBACN;gBACA,UAAU;oBAAC;wBAAC,MAAM;wBAAQ,OAAO;oBAAa;iBAAE;YAClD;YACA;gBAAC,MAAM;gBAAQ,OAAO;YAAI;YAC1B;gBACE,MAAM;gBACN,SAAS;gBACT,YAAY,CAAC;gBACb,UAAU,MAAM,IAAI,CAAC,WAAW;YAClC;YACA;gBAAC,MAAM;gBAAQ,OAAO;YAAI;SAC3B;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1042, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/blockquote.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Blockquote} Blockquote\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `blockquote` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Blockquote} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function blockquote(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'blockquote',\n    properties: {},\n    children: state.wrap(state.all(node), true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED,mDAAmD;;;;AACnD;AAYO,SAAS,WAAW,KAAK,EAAE,IAAI;IACpC,oBAAoB,GACpB,MAAM,SAAS;QACb,MAAM;QACN,SAAS;QACT,YAAY,CAAC;QACb,UAAU,MAAM,IAAI,CAAC,MAAM,GAAG,CAAC,OAAO;IACxC;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1067, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/break.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').Break} Break\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `break` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Break} node\n *   mdast node.\n * @returns {Array<Element | Text>}\n *   hast element content.\n */\nexport function hardBreak(state, node) {\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'br', properties: {}, children: []}\n  state.patch(node, result)\n  return [state.applyData(node, result), {type: 'text', value: '\\n'}]\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,mDAAmD;;;;AACnD;AAYO,SAAS,UAAU,KAAK,EAAE,IAAI;IACnC,oBAAoB,GACpB,MAAM,SAAS;QAAC,MAAM;QAAW,SAAS;QAAM,YAAY,CAAC;QAAG,UAAU,EAAE;IAAA;IAC5E,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;QAAC,MAAM,SAAS,CAAC,MAAM;QAAS;YAAC,MAAM;YAAQ,OAAO;QAAI;KAAE;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1099, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/code.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Code} Code\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `code` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Code} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function code(state, node) {\n  const value = node.value ? node.value + '\\n' : ''\n  /** @type {Properties} */\n  const properties = {}\n\n  if (node.lang) {\n    properties.className = ['language-' + node.lang]\n  }\n\n  // Create `<code>`.\n  /** @type {Element} */\n  let result = {\n    type: 'element',\n    tagName: 'code',\n    properties,\n    children: [{type: 'text', value}]\n  }\n\n  if (node.meta) {\n    result.data = {meta: node.meta}\n  }\n\n  state.patch(node, result)\n  result = state.applyData(node, result)\n\n  // Create `<pre>`.\n  result = {type: 'element', tagName: 'pre', properties: {}, children: [result]}\n  state.patch(node, result)\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,mDAAmD;;;;AACnD;AAYO,SAAS,KAAK,KAAK,EAAE,IAAI;IAC9B,MAAM,QAAQ,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,OAAO;IAC/C,uBAAuB,GACvB,MAAM,aAAa,CAAC;IAEpB,IAAI,KAAK,IAAI,EAAE;QACb,WAAW,SAAS,GAAG;YAAC,cAAc,KAAK,IAAI;SAAC;IAClD;IAEA,mBAAmB;IACnB,oBAAoB,GACpB,IAAI,SAAS;QACX,MAAM;QACN,SAAS;QACT;QACA,UAAU;YAAC;gBAAC,MAAM;gBAAQ;YAAK;SAAE;IACnC;IAEA,IAAI,KAAK,IAAI,EAAE;QACb,OAAO,IAAI,GAAG;YAAC,MAAM,KAAK,IAAI;QAAA;IAChC;IAEA,MAAM,KAAK,CAAC,MAAM;IAClB,SAAS,MAAM,SAAS,CAAC,MAAM;IAE/B,kBAAkB;IAClB,SAAS;QAAC,MAAM;QAAW,SAAS;QAAO,YAAY,CAAC;QAAG,UAAU;YAAC;SAAO;IAAA;IAC7E,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/delete.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Delete} Delete\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `delete` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Delete} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function strikethrough(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'del',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED,mDAAmD;;;;AACnD;AAYO,SAAS,cAAc,KAAK,EAAE,IAAI;IACvC,oBAAoB,GACpB,MAAM,SAAS;QACb,MAAM;QACN,SAAS;QACT,YAAY,CAAC;QACb,UAAU,MAAM,GAAG,CAAC;IACtB;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1179, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/emphasis.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Emphasis} Emphasis\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `emphasis` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Emphasis} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function emphasis(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'em',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED,mDAAmD;;;;AACnD;AAYO,SAAS,SAAS,KAAK,EAAE,IAAI;IAClC,oBAAoB,GACpB,MAAM,SAAS;QACb,MAAM;QACN,SAAS;QACT,YAAY,CAAC;QACb,UAAU,MAAM,GAAG,CAAC;IACtB;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1204, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').FootnoteReference} FootnoteReference\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Turn an mdast `footnoteReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {FootnoteReference} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function footnoteReference(state, node) {\n  const clobberPrefix =\n    typeof state.options.clobberPrefix === 'string'\n      ? state.options.clobberPrefix\n      : 'user-content-'\n  const id = String(node.identifier).toUpperCase()\n  const safeId = normalizeUri(id.toLowerCase())\n  const index = state.footnoteOrder.indexOf(id)\n  /** @type {number} */\n  let counter\n\n  let reuseCounter = state.footnoteCounts.get(id)\n\n  if (reuseCounter === undefined) {\n    reuseCounter = 0\n    state.footnoteOrder.push(id)\n    counter = state.footnoteOrder.length\n  } else {\n    counter = index + 1\n  }\n\n  reuseCounter += 1\n  state.footnoteCounts.set(id, reuseCounter)\n\n  /** @type {Element} */\n  const link = {\n    type: 'element',\n    tagName: 'a',\n    properties: {\n      href: '#' + clobberPrefix + 'fn-' + safeId,\n      id:\n        clobberPrefix +\n        'fnref-' +\n        safeId +\n        (reuseCounter > 1 ? '-' + reuseCounter : ''),\n      dataFootnoteRef: true,\n      ariaDescribedBy: ['footnote-label']\n    },\n    children: [{type: 'text', value: String(counter)}]\n  }\n  state.patch(node, link)\n\n  /** @type {Element} */\n  const sup = {\n    type: 'element',\n    tagName: 'sup',\n    properties: {},\n    children: [link]\n  }\n  state.patch(node, sup)\n  return state.applyData(node, sup)\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;;AAYO,SAAS,kBAAkB,KAAK,EAAE,IAAI;IAC3C,MAAM,gBACJ,OAAO,MAAM,OAAO,CAAC,aAAa,KAAK,WACnC,MAAM,OAAO,CAAC,aAAa,GAC3B;IACN,MAAM,KAAK,OAAO,KAAK,UAAU,EAAE,WAAW;IAC9C,MAAM,SAAS,CAAA,GAAA,uKAAA,CAAA,eAAY,AAAD,EAAE,GAAG,WAAW;IAC1C,MAAM,QAAQ,MAAM,aAAa,CAAC,OAAO,CAAC;IAC1C,mBAAmB,GACnB,IAAI;IAEJ,IAAI,eAAe,MAAM,cAAc,CAAC,GAAG,CAAC;IAE5C,IAAI,iBAAiB,WAAW;QAC9B,eAAe;QACf,MAAM,aAAa,CAAC,IAAI,CAAC;QACzB,UAAU,MAAM,aAAa,CAAC,MAAM;IACtC,OAAO;QACL,UAAU,QAAQ;IACpB;IAEA,gBAAgB;IAChB,MAAM,cAAc,CAAC,GAAG,CAAC,IAAI;IAE7B,oBAAoB,GACpB,MAAM,OAAO;QACX,MAAM;QACN,SAAS;QACT,YAAY;YACV,MAAM,MAAM,gBAAgB,QAAQ;YACpC,IACE,gBACA,WACA,SACA,CAAC,eAAe,IAAI,MAAM,eAAe,EAAE;YAC7C,iBAAiB;YACjB,iBAAiB;gBAAC;aAAiB;QACrC;QACA,UAAU;YAAC;gBAAC,MAAM;gBAAQ,OAAO,OAAO;YAAQ;SAAE;IACpD;IACA,MAAM,KAAK,CAAC,MAAM;IAElB,oBAAoB,GACpB,MAAM,MAAM;QACV,MAAM;QACN,SAAS;QACT,YAAY,CAAC;QACb,UAAU;YAAC;SAAK;IAClB;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1265, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/heading.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Heading} Heading\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `heading` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Heading} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function heading(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'h' + node.depth,\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED,mDAAmD;;;;AACnD;AAYO,SAAS,QAAQ,KAAK,EAAE,IAAI;IACjC,oBAAoB,GACpB,MAAM,SAAS;QACb,MAAM;QACN,SAAS,MAAM,KAAK,KAAK;QACzB,YAAY,CAAC;QACb,UAAU,MAAM,GAAG,CAAC;IACtB;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1290, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/html.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Html} Html\n * @typedef {import('../state.js').State} State\n * @typedef {import('../../index.js').Raw} Raw\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `html` node into hast (`raw` node in dangerous mode, otherwise\n * nothing).\n *\n * @param {State} state\n *   Info passed around.\n * @param {Html} node\n *   mdast node.\n * @returns {Element | Raw | undefined}\n *   hast node.\n */\nexport function html(state, node) {\n  if (state.options.allowDangerousHtml) {\n    /** @type {Raw} */\n    const result = {type: 'raw', value: node.value}\n    state.patch(node, result)\n    return state.applyData(node, result)\n  }\n\n  return undefined\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,mDAAmD;;;;AACnD;AAaO,SAAS,KAAK,KAAK,EAAE,IAAI;IAC9B,IAAI,MAAM,OAAO,CAAC,kBAAkB,EAAE;QACpC,gBAAgB,GAChB,MAAM,SAAS;YAAC,MAAM;YAAO,OAAO,KAAK,KAAK;QAAA;QAC9C,MAAM,KAAK,CAAC,MAAM;QAClB,OAAO,MAAM,SAAS,CAAC,MAAM;IAC/B;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1317, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/revert.js"], "sourcesContent": ["/**\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('mdast').Nodes} Nodes\n * @typedef {import('mdast').Reference} Reference\n *\n * @typedef {import('./state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Return the content of a reference without definition as plain text.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Extract<Nodes, Reference>} node\n *   Reference node (image, link).\n * @returns {Array<ElementContent>}\n *   hast content.\n */\nexport function revert(state, node) {\n  const subtype = node.referenceType\n  let suffix = ']'\n\n  if (subtype === 'collapsed') {\n    suffix += '[]'\n  } else if (subtype === 'full') {\n    suffix += '[' + (node.label || node.identifier) + ']'\n  }\n\n  if (node.type === 'imageReference') {\n    return [{type: 'text', value: '![' + node.alt + suffix}]\n  }\n\n  const contents = state.all(node)\n  const head = contents[0]\n\n  if (head && head.type === 'text') {\n    head.value = '[' + head.value\n  } else {\n    contents.unshift({type: 'text', value: '['})\n  }\n\n  const tail = contents[contents.length - 1]\n\n  if (tail && tail.type === 'text') {\n    tail.value += suffix\n  } else {\n    contents.push({type: 'text', value: suffix})\n  }\n\n  return contents\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED,mDAAmD;;;;AACnD;AAYO,SAAS,OAAO,KAAK,EAAE,IAAI;IAChC,MAAM,UAAU,KAAK,aAAa;IAClC,IAAI,SAAS;IAEb,IAAI,YAAY,aAAa;QAC3B,UAAU;IACZ,OAAO,IAAI,YAAY,QAAQ;QAC7B,UAAU,MAAM,CAAC,KAAK,KAAK,IAAI,KAAK,UAAU,IAAI;IACpD;IAEA,IAAI,KAAK,IAAI,KAAK,kBAAkB;QAClC,OAAO;YAAC;gBAAC,MAAM;gBAAQ,OAAO,OAAO,KAAK,GAAG,GAAG;YAAM;SAAE;IAC1D;IAEA,MAAM,WAAW,MAAM,GAAG,CAAC;IAC3B,MAAM,OAAO,QAAQ,CAAC,EAAE;IAExB,IAAI,QAAQ,KAAK,IAAI,KAAK,QAAQ;QAChC,KAAK,KAAK,GAAG,MAAM,KAAK,KAAK;IAC/B,OAAO;QACL,SAAS,OAAO,CAAC;YAAC,MAAM;YAAQ,OAAO;QAAG;IAC5C;IAEA,MAAM,OAAO,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;IAE1C,IAAI,QAAQ,KAAK,IAAI,KAAK,QAAQ;QAChC,KAAK,KAAK,IAAI;IAChB,OAAO;QACL,SAAS,IAAI,CAAC;YAAC,MAAM;YAAQ,OAAO;QAAM;IAC5C;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1372, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/image-reference.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\nimport {revert} from '../revert.js'\n\n/**\n * Turn an mdast `imageReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ImageReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */\nexport function imageReference(state, node) {\n  const id = String(node.identifier).toUpperCase()\n  const definition = state.definitionById.get(id)\n\n  if (!definition) {\n    return revert(state, node)\n  }\n\n  /** @type {Properties} */\n  const properties = {src: normalizeUri(definition.url || ''), alt: node.alt}\n\n  if (definition.title !== null && definition.title !== undefined) {\n    properties.title = definition.title\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'img', properties, children: []}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AAED;AACA;;;AAYO,SAAS,eAAe,KAAK,EAAE,IAAI;IACxC,MAAM,KAAK,OAAO,KAAK,UAAU,EAAE,WAAW;IAC9C,MAAM,aAAa,MAAM,cAAc,CAAC,GAAG,CAAC;IAE5C,IAAI,CAAC,YAAY;QACf,OAAO,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,OAAO;IACvB;IAEA,uBAAuB,GACvB,MAAM,aAAa;QAAC,KAAK,CAAA,GAAA,uKAAA,CAAA,eAAY,AAAD,EAAE,WAAW,GAAG,IAAI;QAAK,KAAK,KAAK,GAAG;IAAA;IAE1E,IAAI,WAAW,KAAK,KAAK,QAAQ,WAAW,KAAK,KAAK,WAAW;QAC/D,WAAW,KAAK,GAAG,WAAW,KAAK;IACrC;IAEA,oBAAoB,GACpB,MAAM,SAAS;QAAC,MAAM;QAAW,SAAS;QAAO;QAAY,UAAU,EAAE;IAAA;IACzE,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1413, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/image.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Image} Image\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Turn an mdast `image` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Image} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function image(state, node) {\n  /** @type {Properties} */\n  const properties = {src: normalizeUri(node.url)}\n\n  if (node.alt !== null && node.alt !== undefined) {\n    properties.alt = node.alt\n  }\n\n  if (node.title !== null && node.title !== undefined) {\n    properties.title = node.title\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'img', properties, children: []}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAED;;AAYO,SAAS,MAAM,KAAK,EAAE,IAAI;IAC/B,uBAAuB,GACvB,MAAM,aAAa;QAAC,KAAK,CAAA,GAAA,uKAAA,CAAA,eAAY,AAAD,EAAE,KAAK,GAAG;IAAC;IAE/C,IAAI,KAAK,GAAG,KAAK,QAAQ,KAAK,GAAG,KAAK,WAAW;QAC/C,WAAW,GAAG,GAAG,KAAK,GAAG;IAC3B;IAEA,IAAI,KAAK,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK,WAAW;QACnD,WAAW,KAAK,GAAG,KAAK,KAAK;IAC/B;IAEA,oBAAoB,GACpB,MAAM,SAAS;QAAC,MAAM;QAAW,SAAS;QAAO;QAAY,UAAU,EAAE;IAAA;IACzE,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1448, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/inline-code.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `inlineCode` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {InlineCode} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function inlineCode(state, node) {\n  /** @type {Text} */\n  const text = {type: 'text', value: node.value.replace(/\\r?\\n|\\r/g, ' ')}\n  state.patch(node, text)\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'code',\n    properties: {},\n    children: [text]\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,mDAAmD;;;;AACnD;AAYO,SAAS,WAAW,KAAK,EAAE,IAAI;IACpC,iBAAiB,GACjB,MAAM,OAAO;QAAC,MAAM;QAAQ,OAAO,KAAK,KAAK,CAAC,OAAO,CAAC,aAAa;IAAI;IACvE,MAAM,KAAK,CAAC,MAAM;IAElB,oBAAoB,GACpB,MAAM,SAAS;QACb,MAAM;QACN,SAAS;QACT,YAAY,CAAC;QACb,UAAU;YAAC;SAAK;IAClB;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1481, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/link-reference.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\nimport {revert} from '../revert.js'\n\n/**\n * Turn an mdast `linkReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {LinkReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */\nexport function linkReference(state, node) {\n  const id = String(node.identifier).toUpperCase()\n  const definition = state.definitionById.get(id)\n\n  if (!definition) {\n    return revert(state, node)\n  }\n\n  /** @type {Properties} */\n  const properties = {href: normalizeUri(definition.url || '')}\n\n  if (definition.title !== null && definition.title !== undefined) {\n    properties.title = definition.title\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AAED;AACA;;;AAYO,SAAS,cAAc,KAAK,EAAE,IAAI;IACvC,MAAM,KAAK,OAAO,KAAK,UAAU,EAAE,WAAW;IAC9C,MAAM,aAAa,MAAM,cAAc,CAAC,GAAG,CAAC;IAE5C,IAAI,CAAC,YAAY;QACf,OAAO,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,OAAO;IACvB;IAEA,uBAAuB,GACvB,MAAM,aAAa;QAAC,MAAM,CAAA,GAAA,uKAAA,CAAA,eAAY,AAAD,EAAE,WAAW,GAAG,IAAI;IAAG;IAE5D,IAAI,WAAW,KAAK,KAAK,QAAQ,WAAW,KAAK,KAAK,WAAW;QAC/D,WAAW,KAAK,GAAG,WAAW,KAAK;IACrC;IAEA,oBAAoB,GACpB,MAAM,SAAS;QACb,MAAM;QACN,SAAS;QACT;QACA,UAAU,MAAM,GAAG,CAAC;IACtB;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1521, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/link.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Link} Link\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Turn an mdast `link` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Link} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function link(state, node) {\n  /** @type {Properties} */\n  const properties = {href: normalizeUri(node.url)}\n\n  if (node.title !== null && node.title !== undefined) {\n    properties.title = node.title\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAED;;AAYO,SAAS,KAAK,KAAK,EAAE,IAAI;IAC9B,uBAAuB,GACvB,MAAM,aAAa;QAAC,MAAM,CAAA,GAAA,uKAAA,CAAA,eAAY,AAAD,EAAE,KAAK,GAAG;IAAC;IAEhD,IAAI,KAAK,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK,WAAW;QACnD,WAAW,KAAK,GAAG,KAAK,KAAK;IAC/B;IAEA,oBAAoB,GACpB,MAAM,SAAS;QACb,MAAM;QACN,SAAS;QACT;QACA,UAAU,MAAM,GAAG,CAAC;IACtB;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1553, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/list-item.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Parents} Parents\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `listItem` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ListItem} node\n *   mdast node.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */\nexport function listItem(state, node, parent) {\n  const results = state.all(node)\n  const loose = parent ? listLoose(parent) : listItemLoose(node)\n  /** @type {Properties} */\n  const properties = {}\n  /** @type {Array<ElementContent>} */\n  const children = []\n\n  if (typeof node.checked === 'boolean') {\n    const head = results[0]\n    /** @type {Element} */\n    let paragraph\n\n    if (head && head.type === 'element' && head.tagName === 'p') {\n      paragraph = head\n    } else {\n      paragraph = {type: 'element', tagName: 'p', properties: {}, children: []}\n      results.unshift(paragraph)\n    }\n\n    if (paragraph.children.length > 0) {\n      paragraph.children.unshift({type: 'text', value: ' '})\n    }\n\n    paragraph.children.unshift({\n      type: 'element',\n      tagName: 'input',\n      properties: {type: 'checkbox', checked: node.checked, disabled: true},\n      children: []\n    })\n\n    // According to github-markdown-css, this class hides bullet.\n    // See: <https://github.com/sindresorhus/github-markdown-css>.\n    properties.className = ['task-list-item']\n  }\n\n  let index = -1\n\n  while (++index < results.length) {\n    const child = results[index]\n\n    // Add eols before nodes, except if this is a loose, first paragraph.\n    if (\n      loose ||\n      index !== 0 ||\n      child.type !== 'element' ||\n      child.tagName !== 'p'\n    ) {\n      children.push({type: 'text', value: '\\n'})\n    }\n\n    if (child.type === 'element' && child.tagName === 'p' && !loose) {\n      children.push(...child.children)\n    } else {\n      children.push(child)\n    }\n  }\n\n  const tail = results[results.length - 1]\n\n  // Add a final eol.\n  if (tail && (loose || tail.type !== 'element' || tail.tagName !== 'p')) {\n    children.push({type: 'text', value: '\\n'})\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'li', properties, children}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n\n/**\n * @param {Parents} node\n * @return {Boolean}\n */\nfunction listLoose(node) {\n  let loose = false\n  if (node.type === 'list') {\n    loose = node.spread || false\n    const children = node.children\n    let index = -1\n\n    while (!loose && ++index < children.length) {\n      loose = listItemLoose(children[index])\n    }\n  }\n\n  return loose\n}\n\n/**\n * @param {ListItem} node\n * @return {Boolean}\n */\nfunction listItemLoose(node) {\n  const spread = node.spread\n\n  return spread === null || spread === undefined\n    ? node.children.length > 1\n    : spread\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED,mDAAmD;;;;AACnD;AAcO,SAAS,SAAS,KAAK,EAAE,IAAI,EAAE,MAAM;IAC1C,MAAM,UAAU,MAAM,GAAG,CAAC;IAC1B,MAAM,QAAQ,SAAS,UAAU,UAAU,cAAc;IACzD,uBAAuB,GACvB,MAAM,aAAa,CAAC;IACpB,kCAAkC,GAClC,MAAM,WAAW,EAAE;IAEnB,IAAI,OAAO,KAAK,OAAO,KAAK,WAAW;QACrC,MAAM,OAAO,OAAO,CAAC,EAAE;QACvB,oBAAoB,GACpB,IAAI;QAEJ,IAAI,QAAQ,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK,KAAK;YAC3D,YAAY;QACd,OAAO;YACL,YAAY;gBAAC,MAAM;gBAAW,SAAS;gBAAK,YAAY,CAAC;gBAAG,UAAU,EAAE;YAAA;YACxE,QAAQ,OAAO,CAAC;QAClB;QAEA,IAAI,UAAU,QAAQ,CAAC,MAAM,GAAG,GAAG;YACjC,UAAU,QAAQ,CAAC,OAAO,CAAC;gBAAC,MAAM;gBAAQ,OAAO;YAAG;QACtD;QAEA,UAAU,QAAQ,CAAC,OAAO,CAAC;YACzB,MAAM;YACN,SAAS;YACT,YAAY;gBAAC,MAAM;gBAAY,SAAS,KAAK,OAAO;gBAAE,UAAU;YAAI;YACpE,UAAU,EAAE;QACd;QAEA,6DAA6D;QAC7D,8DAA8D;QAC9D,WAAW,SAAS,GAAG;YAAC;SAAiB;IAC3C;IAEA,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,QAAQ,MAAM,CAAE;QAC/B,MAAM,QAAQ,OAAO,CAAC,MAAM;QAE5B,qEAAqE;QACrE,IACE,SACA,UAAU,KACV,MAAM,IAAI,KAAK,aACf,MAAM,OAAO,KAAK,KAClB;YACA,SAAS,IAAI,CAAC;gBAAC,MAAM;gBAAQ,OAAO;YAAI;QAC1C;QAEA,IAAI,MAAM,IAAI,KAAK,aAAa,MAAM,OAAO,KAAK,OAAO,CAAC,OAAO;YAC/D,SAAS,IAAI,IAAI,MAAM,QAAQ;QACjC,OAAO;YACL,SAAS,IAAI,CAAC;QAChB;IACF;IAEA,MAAM,OAAO,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;IAExC,mBAAmB;IACnB,IAAI,QAAQ,CAAC,SAAS,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK,GAAG,GAAG;QACtE,SAAS,IAAI,CAAC;YAAC,MAAM;YAAQ,OAAO;QAAI;IAC1C;IAEA,oBAAoB,GACpB,MAAM,SAAS;QAAC,MAAM;QAAW,SAAS;QAAM;QAAY;IAAQ;IACpE,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B;AAEA;;;CAGC,GACD,SAAS,UAAU,IAAI;IACrB,IAAI,QAAQ;IACZ,IAAI,KAAK,IAAI,KAAK,QAAQ;QACxB,QAAQ,KAAK,MAAM,IAAI;QACvB,MAAM,WAAW,KAAK,QAAQ;QAC9B,IAAI,QAAQ,CAAC;QAEb,MAAO,CAAC,SAAS,EAAE,QAAQ,SAAS,MAAM,CAAE;YAC1C,QAAQ,cAAc,QAAQ,CAAC,MAAM;QACvC;IACF;IAEA,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,cAAc,IAAI;IACzB,MAAM,SAAS,KAAK,MAAM;IAE1B,OAAO,WAAW,QAAQ,WAAW,YACjC,KAAK,QAAQ,CAAC,MAAM,GAAG,IACvB;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1667, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/list.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').List} List\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `list` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {List} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function list(state, node) {\n  /** @type {Properties} */\n  const properties = {}\n  const results = state.all(node)\n  let index = -1\n\n  if (typeof node.start === 'number' && node.start !== 1) {\n    properties.start = node.start\n  }\n\n  // Like GitHub, add a class for custom styling.\n  while (++index < results.length) {\n    const child = results[index]\n\n    if (\n      child.type === 'element' &&\n      child.tagName === 'li' &&\n      child.properties &&\n      Array.isArray(child.properties.className) &&\n      child.properties.className.includes('task-list-item')\n    ) {\n      properties.className = ['contains-task-list']\n      break\n    }\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: node.ordered ? 'ol' : 'ul',\n    properties,\n    children: state.wrap(results, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,mDAAmD;;;;AACnD;AAYO,SAAS,KAAK,KAAK,EAAE,IAAI;IAC9B,uBAAuB,GACvB,MAAM,aAAa,CAAC;IACpB,MAAM,UAAU,MAAM,GAAG,CAAC;IAC1B,IAAI,QAAQ,CAAC;IAEb,IAAI,OAAO,KAAK,KAAK,KAAK,YAAY,KAAK,KAAK,KAAK,GAAG;QACtD,WAAW,KAAK,GAAG,KAAK,KAAK;IAC/B;IAEA,+CAA+C;IAC/C,MAAO,EAAE,QAAQ,QAAQ,MAAM,CAAE;QAC/B,MAAM,QAAQ,OAAO,CAAC,MAAM;QAE5B,IACE,MAAM,IAAI,KAAK,aACf,MAAM,OAAO,KAAK,QAClB,MAAM,UAAU,IAChB,MAAM,OAAO,CAAC,MAAM,UAAU,CAAC,SAAS,KACxC,MAAM,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,mBACpC;YACA,WAAW,SAAS,GAAG;gBAAC;aAAqB;YAC7C;QACF;IACF;IAEA,oBAAoB,GACpB,MAAM,SAAS;QACb,MAAM;QACN,SAAS,KAAK,OAAO,GAAG,OAAO;QAC/B;QACA,UAAU,MAAM,IAAI,CAAC,SAAS;IAChC;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1709, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/paragraph.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `paragraph` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Paragraph} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function paragraph(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'p',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED,mDAAmD;;;;AACnD;AAYO,SAAS,UAAU,KAAK,EAAE,IAAI;IACnC,oBAAoB,GACpB,MAAM,SAAS;QACb,MAAM;QACN,SAAS;QACT,YAAY,CAAC;QACb,UAAU,MAAM,GAAG,CAAC;IACtB;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1734, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/root.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Parents} HastParents\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('mdast').Root} MdastRoot\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `root` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastRoot} node\n *   mdast node.\n * @returns {HastParents}\n *   hast node.\n */\nexport function root(state, node) {\n  /** @type {HastRoot} */\n  const result = {type: 'root', children: state.wrap(state.all(node))}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,mDAAmD;;;;AACnD;AAYO,SAAS,KAAK,KAAK,EAAE,IAAI;IAC9B,qBAAqB,GACrB,MAAM,SAAS;QAAC,MAAM;QAAQ,UAAU,MAAM,IAAI,CAAC,MAAM,GAAG,CAAC;IAAM;IACnE,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1758, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/strong.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Strong} Strong\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `strong` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Strong} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function strong(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'strong',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED,mDAAmD;;;;AACnD;AAYO,SAAS,OAAO,KAAK,EAAE,IAAI;IAChC,oBAAoB,GACpB,MAAM,SAAS;QACb,MAAM;QACN,SAAS;QACT,YAAY,CAAC;QACb,UAAU,MAAM,GAAG,CAAC;IACtB;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1783, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/unist-util-position/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n */\n\n/**\n * Get the ending point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nexport const pointEnd = point('end')\n\n/**\n * Get the starting point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nexport const pointStart = point('start')\n\n/**\n * Get the positional info of `node`.\n *\n * @param {'end' | 'start'} type\n *   Side.\n * @returns\n *   Getter.\n */\nfunction point(type) {\n  return point\n\n  /**\n   * Get the point info of `node` at a bound side.\n   *\n   * @param {Node | NodeLike | null | undefined} [node]\n   * @returns {Point | undefined}\n   */\n  function point(node) {\n    const point = (node && node.position && node.position[type]) || {}\n\n    if (\n      typeof point.line === 'number' &&\n      point.line > 0 &&\n      typeof point.column === 'number' &&\n      point.column > 0\n    ) {\n      return {\n        line: point.line,\n        column: point.column,\n        offset:\n          typeof point.offset === 'number' && point.offset > -1\n            ? point.offset\n            : undefined\n      }\n    }\n  }\n}\n\n/**\n * Get the positional info of `node`.\n *\n * @param {Node | NodeLike | null | undefined} [node]\n *   Node.\n * @returns {Position | undefined}\n *   Position.\n */\nexport function position(node) {\n  const start = pointStart(node)\n  const end = pointEnd(node)\n\n  if (start && end) {\n    return {start, end}\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;;;;;;;CAaC,GAED;;;;;;;CAOC;;;;;AACM,MAAM,WAAW,MAAM;AAUvB,MAAM,aAAa,MAAM;AAEhC;;;;;;;CAOC,GACD,SAAS,MAAM,IAAI;IACjB,OAAO;;IAEP;;;;;GAKC,GACD,SAAS,MAAM,IAAI;QACjB,MAAM,QAAQ,AAAC,QAAQ,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,KAAK,IAAK,CAAC;QAEjE,IACE,OAAO,MAAM,IAAI,KAAK,YACtB,MAAM,IAAI,GAAG,KACb,OAAO,MAAM,MAAM,KAAK,YACxB,MAAM,MAAM,GAAG,GACf;YACA,OAAO;gBACL,MAAM,MAAM,IAAI;gBAChB,QAAQ,MAAM,MAAM;gBACpB,QACE,OAAO,MAAM,MAAM,KAAK,YAAY,MAAM,MAAM,GAAG,CAAC,IAChD,MAAM,MAAM,GACZ;YACR;QACF;IACF;AACF;AAUO,SAAS,SAAS,IAAI;IAC3B,MAAM,QAAQ,WAAW;IACzB,MAAM,MAAM,SAAS;IAErB,IAAI,SAAS,KAAK;QAChB,OAAO;YAAC;YAAO;QAAG;IACpB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1856, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/table.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Table} Table\n * @typedef {import('../state.js').State} State\n */\n\nimport {pointEnd, pointStart} from 'unist-util-position'\n\n/**\n * Turn an mdast `table` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Table} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function table(state, node) {\n  const rows = state.all(node)\n  const firstRow = rows.shift()\n  /** @type {Array<Element>} */\n  const tableContent = []\n\n  if (firstRow) {\n    /** @type {Element} */\n    const head = {\n      type: 'element',\n      tagName: 'thead',\n      properties: {},\n      children: state.wrap([firstRow], true)\n    }\n    state.patch(node.children[0], head)\n    tableContent.push(head)\n  }\n\n  if (rows.length > 0) {\n    /** @type {Element} */\n    const body = {\n      type: 'element',\n      tagName: 'tbody',\n      properties: {},\n      children: state.wrap(rows, true)\n    }\n\n    const start = pointStart(node.children[1])\n    const end = pointEnd(node.children[node.children.length - 1])\n    if (start && end) body.position = {start, end}\n    tableContent.push(body)\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'table',\n    properties: {},\n    children: state.wrap(tableContent, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;;AAYO,SAAS,MAAM,KAAK,EAAE,IAAI;IAC/B,MAAM,OAAO,MAAM,GAAG,CAAC;IACvB,MAAM,WAAW,KAAK,KAAK;IAC3B,2BAA2B,GAC3B,MAAM,eAAe,EAAE;IAEvB,IAAI,UAAU;QACZ,oBAAoB,GACpB,MAAM,OAAO;YACX,MAAM;YACN,SAAS;YACT,YAAY,CAAC;YACb,UAAU,MAAM,IAAI,CAAC;gBAAC;aAAS,EAAE;QACnC;QACA,MAAM,KAAK,CAAC,KAAK,QAAQ,CAAC,EAAE,EAAE;QAC9B,aAAa,IAAI,CAAC;IACpB;IAEA,IAAI,KAAK,MAAM,GAAG,GAAG;QACnB,oBAAoB,GACpB,MAAM,OAAO;YACX,MAAM;YACN,SAAS;YACT,YAAY,CAAC;YACb,UAAU,MAAM,IAAI,CAAC,MAAM;QAC7B;QAEA,MAAM,QAAQ,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,KAAK,QAAQ,CAAC,EAAE;QACzC,MAAM,MAAM,CAAA,GAAA,4JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,QAAQ,CAAC,KAAK,QAAQ,CAAC,MAAM,GAAG,EAAE;QAC5D,IAAI,SAAS,KAAK,KAAK,QAAQ,GAAG;YAAC;YAAO;QAAG;QAC7C,aAAa,IAAI,CAAC;IACpB;IAEA,oBAAoB,GACpB,MAAM,SAAS;QACb,MAAM;QACN,SAAS;QACT,YAAY,CAAC;QACb,UAAU,MAAM,IAAI,CAAC,cAAc;IACrC;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1911, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/table-row.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Parents} Parents\n * @typedef {import('mdast').TableRow} TableRow\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `tableRow` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableRow} node\n *   mdast node.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */\nexport function tableRow(state, node, parent) {\n  const siblings = parent ? parent.children : undefined\n  // Generate a body row when without parent.\n  const rowIndex = siblings ? siblings.indexOf(node) : 1\n  const tagName = rowIndex === 0 ? 'th' : 'td'\n  // To do: option to use `style`?\n  const align = parent && parent.type === 'table' ? parent.align : undefined\n  const length = align ? align.length : node.children.length\n  let cellIndex = -1\n  /** @type {Array<ElementContent>} */\n  const cells = []\n\n  while (++cellIndex < length) {\n    // Note: can also be undefined.\n    const cell = node.children[cellIndex]\n    /** @type {Properties} */\n    const properties = {}\n    const alignValue = align ? align[cellIndex] : undefined\n\n    if (alignValue) {\n      properties.align = alignValue\n    }\n\n    /** @type {Element} */\n    let result = {type: 'element', tagName, properties, children: []}\n\n    if (cell) {\n      result.children = state.all(cell)\n      state.patch(cell, result)\n      result = state.applyData(cell, result)\n    }\n\n    cells.push(result)\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'tr',\n    properties: {},\n    children: state.wrap(cells, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED,mDAAmD;;;;AACnD;AAcO,SAAS,SAAS,KAAK,EAAE,IAAI,EAAE,MAAM;IAC1C,MAAM,WAAW,SAAS,OAAO,QAAQ,GAAG;IAC5C,2CAA2C;IAC3C,MAAM,WAAW,WAAW,SAAS,OAAO,CAAC,QAAQ;IACrD,MAAM,UAAU,aAAa,IAAI,OAAO;IACxC,gCAAgC;IAChC,MAAM,QAAQ,UAAU,OAAO,IAAI,KAAK,UAAU,OAAO,KAAK,GAAG;IACjE,MAAM,SAAS,QAAQ,MAAM,MAAM,GAAG,KAAK,QAAQ,CAAC,MAAM;IAC1D,IAAI,YAAY,CAAC;IACjB,kCAAkC,GAClC,MAAM,QAAQ,EAAE;IAEhB,MAAO,EAAE,YAAY,OAAQ;QAC3B,+BAA+B;QAC/B,MAAM,OAAO,KAAK,QAAQ,CAAC,UAAU;QACrC,uBAAuB,GACvB,MAAM,aAAa,CAAC;QACpB,MAAM,aAAa,QAAQ,KAAK,CAAC,UAAU,GAAG;QAE9C,IAAI,YAAY;YACd,WAAW,KAAK,GAAG;QACrB;QAEA,oBAAoB,GACpB,IAAI,SAAS;YAAC,MAAM;YAAW;YAAS;YAAY,UAAU,EAAE;QAAA;QAEhE,IAAI,MAAM;YACR,OAAO,QAAQ,GAAG,MAAM,GAAG,CAAC;YAC5B,MAAM,KAAK,CAAC,MAAM;YAClB,SAAS,MAAM,SAAS,CAAC,MAAM;QACjC;QAEA,MAAM,IAAI,CAAC;IACb;IAEA,oBAAoB,GACpB,MAAM,SAAS;QACb,MAAM;QACN,SAAS;QACT,YAAY,CAAC;QACb,UAAU,MAAM,IAAI,CAAC,OAAO;IAC9B;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1969, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/table-cell.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').TableCell} TableCell\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `tableCell` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableCell} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function tableCell(state, node) {\n  // Note: this function is normally not called: see `table-row` for how rows\n  // and their cells are compiled.\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'td', // Assume body cell.\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED,mDAAmD;;;;AACnD;AAYO,SAAS,UAAU,KAAK,EAAE,IAAI;IACnC,2EAA2E;IAC3E,gCAAgC;IAChC,oBAAoB,GACpB,MAAM,SAAS;QACb,MAAM;QACN,SAAS;QACT,YAAY,CAAC;QACb,UAAU,MAAM,GAAG,CAAC;IACtB;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1996, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/trim-lines/index.js"], "sourcesContent": ["const tab = 9 /* `\\t` */\nconst space = 32 /* ` ` */\n\n/**\n * Remove initial and final spaces and tabs at the line breaks in `value`.\n * Does not trim initial and final spaces and tabs of the value itself.\n *\n * @param {string} value\n *   Value to trim.\n * @returns {string}\n *   Trimmed value.\n */\nexport function trimLines(value) {\n  const source = String(value)\n  const search = /\\r?\\n|\\r/g\n  let match = search.exec(source)\n  let last = 0\n  /** @type {Array<string>} */\n  const lines = []\n\n  while (match) {\n    lines.push(\n      trimLine(source.slice(last, match.index), last > 0, true),\n      match[0]\n    )\n\n    last = match.index + match[0].length\n    match = search.exec(source)\n  }\n\n  lines.push(trimLine(source.slice(last), last > 0, false))\n\n  return lines.join('')\n}\n\n/**\n * @param {string} value\n *   Line to trim.\n * @param {boolean} start\n *   Whether to trim the start of the line.\n * @param {boolean} end\n *   Whether to trim the end of the line.\n * @returns {string}\n *   Trimmed line.\n */\nfunction trimLine(value, start, end) {\n  let startIndex = 0\n  let endIndex = value.length\n\n  if (start) {\n    let code = value.codePointAt(startIndex)\n\n    while (code === tab || code === space) {\n      startIndex++\n      code = value.codePointAt(startIndex)\n    }\n  }\n\n  if (end) {\n    let code = value.codePointAt(endIndex - 1)\n\n    while (code === tab || code === space) {\n      endIndex--\n      code = value.codePointAt(endIndex - 1)\n    }\n  }\n\n  return endIndex > startIndex ? value.slice(startIndex, endIndex) : ''\n}\n"], "names": [], "mappings": ";;;AAAA,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,QAAQ,GAAG,OAAO;AAWjB,SAAS,UAAU,KAAK;IAC7B,MAAM,SAAS,OAAO;IACtB,MAAM,SAAS;IACf,IAAI,QAAQ,OAAO,IAAI,CAAC;IACxB,IAAI,OAAO;IACX,0BAA0B,GAC1B,MAAM,QAAQ,EAAE;IAEhB,MAAO,MAAO;QACZ,MAAM,IAAI,CACR,SAAS,OAAO,KAAK,CAAC,MAAM,MAAM,KAAK,GAAG,OAAO,GAAG,OACpD,KAAK,CAAC,EAAE;QAGV,OAAO,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;QACpC,QAAQ,OAAO,IAAI,CAAC;IACtB;IAEA,MAAM,IAAI,CAAC,SAAS,OAAO,KAAK,CAAC,OAAO,OAAO,GAAG;IAElD,OAAO,MAAM,IAAI,CAAC;AACpB;AAEA;;;;;;;;;CASC,GACD,SAAS,SAAS,KAAK,EAAE,KAAK,EAAE,GAAG;IACjC,IAAI,aAAa;IACjB,IAAI,WAAW,MAAM,MAAM;IAE3B,IAAI,OAAO;QACT,IAAI,OAAO,MAAM,WAAW,CAAC;QAE7B,MAAO,SAAS,OAAO,SAAS,MAAO;YACrC;YACA,OAAO,MAAM,WAAW,CAAC;QAC3B;IACF;IAEA,IAAI,KAAK;QACP,IAAI,OAAO,MAAM,WAAW,CAAC,WAAW;QAExC,MAAO,SAAS,OAAO,SAAS,MAAO;YACrC;YACA,OAAO,MAAM,WAAW,CAAC,WAAW;QACtC;IACF;IAEA,OAAO,WAAW,aAAa,MAAM,KAAK,CAAC,YAAY,YAAY;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2049, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/text.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').Text} HastText\n * @typedef {import('mdast').Text} MdastText\n * @typedef {import('../state.js').State} State\n */\n\nimport {trimLines} from 'trim-lines'\n\n/**\n * Turn an mdast `text` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastText} node\n *   mdast node.\n * @returns {HastElement | HastText}\n *   hast node.\n */\nexport function text(state, node) {\n  /** @type {HastText} */\n  const result = {type: 'text', value: trimLines(String(node.value))}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAED;;AAYO,SAAS,KAAK,KAAK,EAAE,IAAI;IAC9B,qBAAqB,GACrB,MAAM,SAAS;QAAC,MAAM;QAAQ,OAAO,CAAA,GAAA,yIAAA,CAAA,YAAS,AAAD,EAAE,OAAO,KAAK,KAAK;IAAE;IAClE,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2073, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').ThematicBreak} ThematicBreak\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `thematicBreak` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ThematicBreak} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function thematicBreak(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'hr',\n    properties: {},\n    children: []\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED,mDAAmD;;;;AACnD;AAYO,SAAS,cAAc,KAAK,EAAE,IAAI;IACvC,oBAAoB,GACpB,MAAM,SAAS;QACb,MAAM;QACN,SAAS;QACT,YAAY,CAAC;QACb,UAAU,EAAE;IACd;IACA,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2098, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/handlers/index.js"], "sourcesContent": ["import {blockquote} from './blockquote.js'\nimport {hardBreak} from './break.js'\nimport {code} from './code.js'\nimport {strikethrough} from './delete.js'\nimport {emphasis} from './emphasis.js'\nimport {footnoteReference} from './footnote-reference.js'\nimport {heading} from './heading.js'\nimport {html} from './html.js'\nimport {imageReference} from './image-reference.js'\nimport {image} from './image.js'\nimport {inlineCode} from './inline-code.js'\nimport {linkReference} from './link-reference.js'\nimport {link} from './link.js'\nimport {listItem} from './list-item.js'\nimport {list} from './list.js'\nimport {paragraph} from './paragraph.js'\nimport {root} from './root.js'\nimport {strong} from './strong.js'\nimport {table} from './table.js'\nimport {tableRow} from './table-row.js'\nimport {tableCell} from './table-cell.js'\nimport {text} from './text.js'\nimport {thematicBreak} from './thematic-break.js'\n\n/**\n * Default handlers for nodes.\n *\n * @satisfies {import('../state.js').Handlers}\n */\nexport const handlers = {\n  blockquote,\n  break: hardBreak,\n  code,\n  delete: strikethrough,\n  emphasis,\n  footnoteReference,\n  heading,\n  html,\n  imageReference,\n  image,\n  inlineCode,\n  linkReference,\n  link,\n  listItem,\n  list,\n  paragraph,\n  // @ts-expect-error: root is different, but hard to type.\n  root,\n  strong,\n  table,\n  tableCell,\n  tableRow,\n  text,\n  thematicBreak,\n  toml: ignore,\n  yaml: ignore,\n  definition: ignore,\n  footnoteDefinition: ignore\n}\n\n// Return nothing for nodes that are ignored.\nfunction ignore() {\n  return undefined\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;AAOO,MAAM,WAAW;IACtB,YAAA,+KAAA,CAAA,aAAU;IACV,OAAO,0KAAA,CAAA,YAAS;IAChB,MAAA,yKAAA,CAAA,OAAI;IACJ,QAAQ,2KAAA,CAAA,gBAAa;IACrB,UAAA,6KAAA,CAAA,WAAQ;IACR,mBAAA,0LAAA,CAAA,oBAAiB;IACjB,SAAA,4KAAA,CAAA,UAAO;IACP,MAAA,yKAAA,CAAA,OAAI;IACJ,gBAAA,uLAAA,CAAA,iBAAc;IACd,OAAA,0KAAA,CAAA,QAAK;IACL,YAAA,mLAAA,CAAA,aAAU;IACV,eAAA,sLAAA,CAAA,gBAAa;IACb,MAAA,yKAAA,CAAA,OAAI;IACJ,UAAA,iLAAA,CAAA,WAAQ;IACR,MAAA,yKAAA,CAAA,OAAI;IACJ,WAAA,8KAAA,CAAA,YAAS;IACT,yDAAyD;IACzD,MAAA,yKAAA,CAAA,OAAI;IACJ,QAAA,2KAAA,CAAA,SAAM;IACN,OAAA,0KAAA,CAAA,QAAK;IACL,WAAA,kLAAA,CAAA,YAAS;IACT,UAAA,iLAAA,CAAA,WAAQ;IACR,MAAA,yKAAA,CAAA,OAAI;IACJ,eAAA,sLAAA,CAAA,gBAAa;IACb,MAAM;IACN,MAAM;IACN,YAAY;IACZ,oBAAoB;AACtB;AAEA,6CAA6C;AAC7C,SAAS;IACP,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2197, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/dequal/dist/index.mjs"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nfunction find(iter, tar, key) {\n\tfor (key of iter.keys()) {\n\t\tif (dequal(key, tar)) return key;\n\t}\n}\n\nexport function dequal(foo, bar) {\n\tvar ctor, len, tmp;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ctor === Set) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len;\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!bar.has(tmp)) return false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === Map) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len[0];\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!dequal(len[1], bar.get(tmp))) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === ArrayBuffer) {\n\t\t\tfoo = new Uint8Array(foo);\n\t\t\tbar = new Uint8Array(bar);\n\t\t} else if (ctor === DataView) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo.getInt8(len) === bar.getInt8(len));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ArrayBuffer.isView(foo)) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo[len] === bar[len]);\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n"], "names": [], "mappings": ";;;AAAA,IAAI,MAAM,OAAO,SAAS,CAAC,cAAc;AAEzC,SAAS,KAAK,IAAI,EAAE,GAAG,EAAE,GAAG;IAC3B,KAAK,OAAO,KAAK,IAAI,GAAI;QACxB,IAAI,OAAO,KAAK,MAAM,OAAO;IAC9B;AACD;AAEO,SAAS,OAAO,GAAG,EAAE,GAAG;IAC9B,IAAI,MAAM,KAAK;IACf,IAAI,QAAQ,KAAK,OAAO;IAExB,IAAI,OAAO,OAAO,CAAC,OAAK,IAAI,WAAW,MAAM,IAAI,WAAW,EAAE;QAC7D,IAAI,SAAS,MAAM,OAAO,IAAI,OAAO,OAAO,IAAI,OAAO;QACvD,IAAI,SAAS,QAAQ,OAAO,IAAI,QAAQ,OAAO,IAAI,QAAQ;QAE3D,IAAI,SAAS,OAAO;YACnB,IAAI,CAAC,MAAI,IAAI,MAAM,MAAM,IAAI,MAAM,EAAE;gBACpC,MAAO,SAAS,OAAO,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI;YAC1C;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,SAAS,KAAK;YACjB,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;gBAC1B,OAAO;YACR;YACA,KAAK,OAAO,IAAK;gBAChB,MAAM;gBACN,IAAI,OAAO,OAAO,QAAQ,UAAU;oBACnC,MAAM,KAAK,KAAK;oBAChB,IAAI,CAAC,KAAK,OAAO;gBAClB;gBACA,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,OAAO;YAC3B;YACA,OAAO;QACR;QAEA,IAAI,SAAS,KAAK;YACjB,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;gBAC1B,OAAO;YACR;YACA,KAAK,OAAO,IAAK;gBAChB,MAAM,GAAG,CAAC,EAAE;gBACZ,IAAI,OAAO,OAAO,QAAQ,UAAU;oBACnC,MAAM,KAAK,KAAK;oBAChB,IAAI,CAAC,KAAK,OAAO;gBAClB;gBACA,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,OAAO;oBAClC,OAAO;gBACR;YACD;YACA,OAAO;QACR;QAEA,IAAI,SAAS,aAAa;YACzB,MAAM,IAAI,WAAW;YACrB,MAAM,IAAI,WAAW;QACtB,OAAO,IAAI,SAAS,UAAU;YAC7B,IAAI,CAAC,MAAI,IAAI,UAAU,MAAM,IAAI,UAAU,EAAE;gBAC5C,MAAO,SAAS,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC;YAClD;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,YAAY,MAAM,CAAC,MAAM;YAC5B,IAAI,CAAC,MAAI,IAAI,UAAU,MAAM,IAAI,UAAU,EAAE;gBAC5C,MAAO,SAAS,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI;YACtC;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,CAAC,QAAQ,OAAO,QAAQ,UAAU;YACrC,MAAM;YACN,IAAK,QAAQ,IAAK;gBACjB,IAAI,IAAI,IAAI,CAAC,KAAK,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,KAAK,OAAO,OAAO;gBACjE,IAAI,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG,OAAO;YAC7D;YACA,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK;QACpC;IACD;IAEA,OAAO,QAAQ,OAAO,QAAQ;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2280, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/devlop/lib/development.js"], "sourcesContent": ["import {dequal} from 'dequal'\n\n/**\n * @type {Set<string>}\n */\nconst codesWarned = new Set()\n\nclass AssertionError extends Error {\n  name = /** @type {const} */ ('Assertion')\n  code = /** @type {const} */ ('ERR_ASSERTION')\n\n  /**\n   * Create an assertion error.\n   *\n   * @param {string} message\n   *   Message explaining error.\n   * @param {unknown} actual\n   *   Value.\n   * @param {unknown} expected\n   *   Baseline.\n   * @param {string} operator\n   *   Name of equality operation.\n   * @param {boolean} generated\n   *   Whether `message` is a custom message or not\n   * @returns\n   *   Instance.\n   */\n  // eslint-disable-next-line max-params\n  constructor(message, actual, expected, operator, generated) {\n    super(message)\n\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor)\n    }\n\n    /**\n     * @type {unknown}\n     */\n    this.actual = actual\n\n    /**\n     * @type {unknown}\n     */\n    this.expected = expected\n\n    /**\n     * @type {boolean}\n     */\n    this.generated = generated\n\n    /**\n     * @type {string}\n     */\n    this.operator = operator\n  }\n}\n\nclass DeprecationE<PERSON>r extends Error {\n  name = /** @type {const} */ ('DeprecationWarning')\n\n  /**\n   * Create a deprecation message.\n   *\n   * @param {string} message\n   *   Message explaining deprecation.\n   * @param {string | undefined} code\n   *   Deprecation identifier; deprecation messages will be generated only once per code.\n   * @returns\n   *   Instance.\n   */\n  constructor(message, code) {\n    super(message)\n\n    /**\n     * @type {string | undefined}\n     */\n    this.code = code\n  }\n}\n\n/**\n * Wrap a function or class to show a deprecation message when first called.\n *\n * > 👉 **Important**: only shows a message when the `development` condition is\n * > used, does nothing in production.\n *\n * When the resulting wrapped `fn` is called, emits a warning once to\n * `console.error` (`stderr`).\n * If a code is given, one warning message will be emitted in total per code.\n *\n * @template {Function} T\n *   Function or class kind.\n * @param {T} fn\n *   Function or class.\n * @param {string} message\n *   Message explaining deprecation.\n * @param {string | null | undefined} [code]\n *   Deprecation identifier (optional); deprecation messages will be generated\n *   only once per code.\n * @returns {T}\n *   Wrapped `fn`.\n */\nexport function deprecate(fn, message, code) {\n  let warned = false\n\n  // The wrapper will keep the same prototype as fn to maintain prototype chain\n  Object.setPrototypeOf(deprecated, fn)\n\n  // @ts-expect-error: it’s perfect, typescript…\n  return deprecated\n\n  /**\n   * @this {unknown}\n   * @param  {...Array<unknown>} args\n   * @returns {unknown}\n   */\n  function deprecated(...args) {\n    if (!warned) {\n      warned = true\n\n      if (typeof code === 'string' && codesWarned.has(code)) {\n        // Empty.\n      } else {\n        console.error(new DeprecationError(message, code || undefined))\n\n        if (typeof code === 'string') codesWarned.add(code)\n      }\n    }\n\n    return new.target\n      ? Reflect.construct(fn, args, new.target)\n      : Reflect.apply(fn, this, args)\n  }\n}\n\n/**\n * Assert deep strict equivalence.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @template {unknown} T\n *   Expected kind.\n * @param {unknown} actual\n *   Value.\n * @param {T} expected\n *   Baseline.\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Expected values to be deeply equal'`).\n * @returns {asserts actual is T}\n *   Nothing; throws when `actual` is not deep strict equal to `expected`.\n * @throws {AssertionError}\n *   Throws when `actual` is not deep strict equal to `expected`.\n */\nexport function equal(actual, expected, message) {\n  assert(\n    dequal(actual, expected),\n    actual,\n    expected,\n    'equal',\n    'Expected values to be deeply equal',\n    message\n  )\n}\n\n/**\n * Assert if `value` is truthy.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @param {unknown} value\n *   Value to assert.\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Expected value to be truthy'`).\n * @returns {asserts value}\n *   Nothing; throws when `value` is falsey.\n * @throws {AssertionError}\n *   Throws when `value` is falsey.\n */\nexport function ok(value, message) {\n  assert(\n    Boolean(value),\n    false,\n    true,\n    'ok',\n    'Expected value to be truthy',\n    message\n  )\n}\n\n/**\n * Assert that a code path never happens.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Unreachable'`).\n * @returns {never}\n *   Nothing; always throws.\n * @throws {AssertionError}\n *   Throws when `value` is falsey.\n */\nexport function unreachable(message) {\n  assert(false, false, true, 'ok', 'Unreachable', message)\n}\n\n/**\n * @param {boolean} bool\n *   Whether to skip this operation.\n * @param {unknown} actual\n *   Actual value.\n * @param {unknown} expected\n *   Expected value.\n * @param {string} operator\n *   Operator.\n * @param {string} defaultMessage\n *   Default message for operation.\n * @param {Error | string | null | undefined} userMessage\n *   User-provided message.\n * @returns {asserts bool}\n *   Nothing; throws when falsey.\n */\n// eslint-disable-next-line max-params\nfunction assert(bool, actual, expected, operator, defaultMessage, userMessage) {\n  if (!bool) {\n    throw userMessage instanceof Error\n      ? userMessage\n      : new AssertionError(\n          userMessage || defaultMessage,\n          actual,\n          expected,\n          operator,\n          !userMessage\n        )\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA;;CAEC,GACD,MAAM,cAAc,IAAI;AAExB,MAAM,uBAAuB;IAC3B,OAA6B,YAAY;IACzC,OAA6B,gBAAgB;IAE7C;;;;;;;;;;;;;;;GAeC,GACD,sCAAsC;IACtC,YAAY,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAE;QAC1D,KAAK,CAAC;QAEN,IAAI,MAAM,iBAAiB,EAAE;YAC3B,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;QAChD;QAEA;;KAEC,GACD,IAAI,CAAC,MAAM,GAAG;QAEd;;KAEC,GACD,IAAI,CAAC,QAAQ,GAAG;QAEhB;;KAEC,GACD,IAAI,CAAC,SAAS,GAAG;QAEjB;;KAEC,GACD,IAAI,CAAC,QAAQ,GAAG;IAClB;AACF;AAEA,MAAM,yBAAyB;IAC7B,OAA6B,qBAAqB;IAElD;;;;;;;;;GASC,GACD,YAAY,OAAO,EAAE,IAAI,CAAE;QACzB,KAAK,CAAC;QAEN;;KAEC,GACD,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAwBO,SAAS,UAAU,EAAE,EAAE,OAAO,EAAE,IAAI;IACzC,IAAI,SAAS;IAEb,6EAA6E;IAC7E,OAAO,cAAc,CAAC,YAAY;IAElC,8CAA8C;IAC9C,OAAO;;IAEP;;;;GAIC,GACD,SAAS,WAAW,GAAG,IAAI;QACzB,IAAI,CAAC,QAAQ;YACX,SAAS;YAET,IAAI,OAAO,SAAS,YAAY,YAAY,GAAG,CAAC,OAAO;YACrD,SAAS;YACX,OAAO;gBACL,QAAQ,KAAK,CAAC,IAAI,iBAAiB,SAAS,QAAQ;gBAEpD,IAAI,OAAO,SAAS,UAAU,YAAY,GAAG,CAAC;YAChD;QACF;QAEA,OAAO,aACH,QAAQ,SAAS,CAAC,IAAI,MAAM,cAC5B,QAAQ,KAAK,CAAC,IAAI,IAAI,EAAE;IAC9B;AACF;AAqBO,SAAS,MAAM,MAAM,EAAE,QAAQ,EAAE,OAAO;IAC7C,OACE,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,WACf,QACA,UACA,SACA,sCACA;AAEJ;AAiBO,SAAS,GAAG,KAAK,EAAE,OAAO;IAC/B,OACE,QAAQ,QACR,OACA,MACA,MACA,+BACA;AAEJ;AAeO,SAAS,YAAY,OAAO;IACjC,OAAO,OAAO,OAAO,MAAM,MAAM,eAAe;AAClD;AAEA;;;;;;;;;;;;;;;CAeC,GACD,sCAAsC;AACtC,SAAS,OAAO,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAE,WAAW;IAC3E,IAAI,CAAC,MAAM;QACT,MAAM,uBAAuB,QACzB,cACA,IAAI,eACF,eAAe,gBACf,QACA,UACA,UACA,CAAC;IAET;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2407, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/unist-util-is/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n */\n\n/**\n * @template Fn\n * @template Fallback\n * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate\n */\n\n/**\n * @callback Check\n *   Check that an arbitrary value is a node.\n * @param {unknown} this\n *   The given context.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is a node and passes a test.\n *\n * @typedef {Record<string, unknown> | Node} Props\n *   Object to check for equivalence.\n *\n *   Note: `Node` is included as it is common but is not indexable.\n *\n * @typedef {Array<Props | TestFunction | string> | Props | TestFunction | string | null | undefined} Test\n *   Check for an arbitrary node.\n *\n * @callback TestFunction\n *   Check if a node passes a test.\n * @param {unknown} this\n *   The given context.\n * @param {Node} node\n *   A node.\n * @param {number | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean | undefined | void}\n *   Whether this node passes the test.\n *\n *   Note: `void` is included until TS sees no return as `undefined`.\n */\n\n/**\n * Check if `node` is a `Node` and whether it passes the given test.\n *\n * @param {unknown} node\n *   Thing to check, typically `Node`.\n * @param {Test} test\n *   A check for a specific node.\n * @param {number | null | undefined} index\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} parent\n *   The node’s parent.\n * @param {unknown} context\n *   Context object (`this`) to pass to `test` functions.\n * @returns {boolean}\n *   Whether `node` is a node and passes a test.\n */\nexport const is =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends string>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n   *   (<Condition extends Props>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n   *   (<Condition extends TestFunction>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n   *   ((node?: null | undefined) => false) &\n   *   ((node: unknown, test?: null | undefined, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n   *   ((node: unknown, test?: Test, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function (node, test, index, parent, context) {\n      const check = convert(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!is(parent) || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      return looksLikeANode(node)\n        ? check.call(context, node, index, parent)\n        : false\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param {Test} test\n *   *   when nullish, checks if `node` is a `Node`.\n *   *   when `string`, works like passing `(node) => node.type === test`.\n *   *   when `function` checks if function passed the node is true.\n *   *   when `object`, checks that all keys in test are in node, and that they have (strictly) equal values.\n *   *   when `array`, checks if any one of the subtests pass.\n * @returns {Check}\n *   An assertion.\n */\nexport const convert =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends string>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n   *   (<Condition extends Props>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n   *   (<Condition extends TestFunction>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n   *   ((test?: null | undefined) => (node?: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n   *   ((test?: Test) => Check)\n   * )}\n   */\n  (\n    /**\n     * @param {Test} [test]\n     * @returns {Check}\n     */\n    function (test) {\n      if (test === null || test === undefined) {\n        return ok\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return Array.isArray(test) ? anyFactory(test) : propsFactory(test)\n      }\n\n      if (typeof test === 'string') {\n        return typeFactory(test)\n      }\n\n      throw new Error('Expected function, string, or object as test')\n    }\n  )\n\n/**\n * @param {Array<Props | TestFunction | string>} tests\n * @returns {Check}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<Check>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convert(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @type {TestFunction}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].apply(this, parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn an object into a test for a node with a certain fields.\n *\n * @param {Props} check\n * @returns {Check}\n */\nfunction propsFactory(check) {\n  const checkAsRecord = /** @type {Record<string, unknown>} */ (check)\n\n  return castFactory(all)\n\n  /**\n   * @param {Node} node\n   * @returns {boolean}\n   */\n  function all(node) {\n    const nodeAsRecord = /** @type {Record<string, unknown>} */ (\n      /** @type {unknown} */ (node)\n    )\n\n    /** @type {string} */\n    let key\n\n    for (key in check) {\n      if (nodeAsRecord[key] !== checkAsRecord[key]) return false\n    }\n\n    return true\n  }\n}\n\n/**\n * Turn a string into a test for a node with a certain type.\n *\n * @param {string} check\n * @returns {Check}\n */\nfunction typeFactory(check) {\n  return castFactory(type)\n\n  /**\n   * @param {Node} node\n   */\n  function type(node) {\n    return node && node.type === check\n  }\n}\n\n/**\n * Turn a custom test into a test for a node that passes that test.\n *\n * @param {TestFunction} testFunction\n * @returns {Check}\n */\nfunction castFactory(testFunction) {\n  return check\n\n  /**\n   * @this {unknown}\n   * @type {Check}\n   */\n  function check(value, index, parent) {\n    return Boolean(\n      looksLikeANode(value) &&\n        testFunction.call(\n          this,\n          value,\n          typeof index === 'number' ? index : undefined,\n          parent || undefined\n        )\n    )\n  }\n}\n\nfunction ok() {\n  return true\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Node}\n */\nfunction looksLikeANode(value) {\n  return value !== null && typeof value === 'object' && 'type' in value\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;CAIC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoCC,GAED;;;;;;;;;;;;;;;CAeC;;;;AACM,MAAM,KAaT;;;;;;;KAOC,GACD,sCAAsC;AACtC,SAAU,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO;IAC1C,MAAM,QAAQ,QAAQ;IAEtB,IACE,UAAU,aACV,UAAU,QACV,CAAC,OAAO,UAAU,YAChB,QAAQ,KACR,UAAU,OAAO,iBAAiB,GACpC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,IACE,WAAW,aACX,WAAW,QACX,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,QAAQ,GAChC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,IACE,CAAC,WAAW,aAAa,WAAW,IAAI,MACxC,CAAC,UAAU,aAAa,UAAU,IAAI,GACtC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,eAAe,QAClB,MAAM,IAAI,CAAC,SAAS,MAAM,OAAO,UACjC;AACN;AAqBG,MAAM,UAYT;;;KAGC,GACD,SAAU,IAAI;IACZ,IAAI,SAAS,QAAQ,SAAS,WAAW;QACvC,OAAO;IACT;IAEA,IAAI,OAAO,SAAS,YAAY;QAC9B,OAAO,YAAY;IACrB;IAEA,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,MAAM,OAAO,CAAC,QAAQ,WAAW,QAAQ,aAAa;IAC/D;IAEA,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,YAAY;IACrB;IAEA,MAAM,IAAI,MAAM;AAClB;AAGJ;;;CAGC,GACD,SAAS,WAAW,KAAK;IACvB,yBAAyB,GACzB,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,MAAM,CAAC,MAAM,GAAG,QAAQ,KAAK,CAAC,MAAM;IACtC;IAEA,OAAO,YAAY;;IAEnB;;;GAGC,GACD,SAAS,IAAI,GAAG,UAAU;QACxB,IAAI,QAAQ,CAAC;QAEb,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;YAC9B,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,aAAa,OAAO;QACpD;QAEA,OAAO;IACT;AACF;AAEA;;;;;CAKC,GACD,SAAS,aAAa,KAAK;IACzB,MAAM,gBAAwD;IAE9D,OAAO,YAAY;;IAEnB;;;GAGC,GACD,SAAS,IAAI,IAAI;QACf,MAAM,eACoB;QAG1B,mBAAmB,GACnB,IAAI;QAEJ,IAAK,OAAO,MAAO;YACjB,IAAI,YAAY,CAAC,IAAI,KAAK,aAAa,CAAC,IAAI,EAAE,OAAO;QACvD;QAEA,OAAO;IACT;AACF;AAEA;;;;;CAKC,GACD,SAAS,YAAY,KAAK;IACxB,OAAO,YAAY;;IAEnB;;GAEC,GACD,SAAS,KAAK,IAAI;QAChB,OAAO,QAAQ,KAAK,IAAI,KAAK;IAC/B;AACF;AAEA;;;;;CAKC,GACD,SAAS,YAAY,YAAY;IAC/B,OAAO;;IAEP;;;GAGC,GACD,SAAS,MAAM,KAAK,EAAE,KAAK,EAAE,MAAM;QACjC,OAAO,QACL,eAAe,UACb,aAAa,IAAI,CACf,IAAI,EACJ,OACA,OAAO,UAAU,WAAW,QAAQ,WACpC,UAAU;IAGlB;AACF;AAEA,SAAS;IACP,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,eAAe,KAAK;IAC3B,OAAO,UAAU,QAAQ,OAAO,UAAU,YAAY,UAAU;AAClE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2595, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/unist-util-visit-parents/lib/color.js"], "sourcesContent": ["/**\n * @param {string} d\n * @returns {string}\n */\nexport function color(d) {\n  return d\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AACM,SAAS,MAAM,CAAC;IACrB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2610, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/unist-util-visit-parents/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} UnistNode\n * @typedef {import('unist').Parent} UnistParent\n */\n\n/**\n * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test\n *   Test from `unist-util-is`.\n *\n *   Note: we have remove and add `undefined`, because otherwise when generating\n *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,\n *   which doesn’t work when publishing on npm.\n */\n\n/**\n * @typedef {(\n *   Fn extends (value: any) => value is infer Thing\n *   ? Thing\n *   : Fallback\n * )} Predicate\n *   Get the value of a type guard `Fn`.\n * @template Fn\n *   Value; typically function that is a type guard (such as `(x): x is Y`).\n * @template Fallback\n *   Value to yield if `Fn` is not a type guard.\n */\n\n/**\n * @typedef {(\n *   Check extends null | undefined // No test.\n *   ? Value\n *   : Value extends {type: Check} // String (type) test.\n *   ? Value\n *   : Value extends Check // Partial test.\n *   ? Value\n *   : Check extends Function // Function test.\n *   ? Predicate<Check, Value> extends Value\n *     ? Predicate<Check, Value>\n *     : never\n *   : never // Some other test?\n * )} MatchesOne\n *   Check whether a node matches a primitive check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test, but not arrays.\n */\n\n/**\n * @typedef {(\n *   Check extends Array<any>\n *   ? MatchesOne<Value, Check[keyof Check]>\n *   : MatchesOne<Value, Check>\n * )} Matches\n *   Check whether a node matches a check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test.\n */\n\n/**\n * @typedef {0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10} Uint\n *   Number; capped reasonably.\n */\n\n/**\n * @typedef {I extends 0 ? 1 : I extends 1 ? 2 : I extends 2 ? 3 : I extends 3 ? 4 : I extends 4 ? 5 : I extends 5 ? 6 : I extends 6 ? 7 : I extends 7 ? 8 : I extends 8 ? 9 : 10} Increment\n *   Increment a number in the type system.\n * @template {Uint} [I=0]\n *   Index.\n */\n\n/**\n * @typedef {(\n *   Node extends UnistParent\n *   ? Node extends {children: Array<infer Children>}\n *     ? Child extends Children ? Node : never\n *     : never\n *   : never\n * )} InternalParent\n *   Collect nodes that can be parents of `Child`.\n * @template {UnistNode} Node\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {InternalParent<InclusiveDescendant<Tree>, Child>} Parent\n *   Collect nodes in `Tree` that can be parents of `Child`.\n * @template {UnistNode} Tree\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {(\n *   Depth extends Max\n *   ? never\n *   :\n *     | InternalParent<Node, Child>\n *     | InternalAncestor<Node, InternalParent<Node, Child>, Max, Increment<Depth>>\n * )} InternalAncestor\n *   Collect nodes in `Tree` that can be ancestors of `Child`.\n * @template {UnistNode} Node\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n * @template {Uint} [Max=10]\n *   Max; searches up to this depth.\n * @template {Uint} [Depth=0]\n *   Current depth.\n */\n\n/**\n * @typedef {InternalAncestor<InclusiveDescendant<Tree>, Child>} Ancestor\n *   Collect nodes in `Tree` that can be ancestors of `Child`.\n * @template {UnistNode} Tree\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {(\n *   Tree extends UnistParent\n *     ? Depth extends Max\n *       ? Tree\n *       : Tree | InclusiveDescendant<Tree['children'][number], Max, Increment<Depth>>\n *     : Tree\n * )} InclusiveDescendant\n *   Collect all (inclusive) descendants of `Tree`.\n *\n *   > 👉 **Note**: for performance reasons, this seems to be the fastest way to\n *   > recurse without actually running into an infinite loop, which the\n *   > previous version did.\n *   >\n *   > Practically, a max of `2` is typically enough assuming a `Root` is\n *   > passed, but it doesn’t improve performance.\n *   > It gets higher with `List > ListItem > Table > TableRow > TableCell`.\n *   > Using up to `10` doesn’t hurt or help either.\n * @template {UnistNode} Tree\n *   Tree type.\n * @template {Uint} [Max=10]\n *   Max; searches up to this depth.\n * @template {Uint} [Depth=0]\n *   Current depth.\n */\n\n/**\n * @typedef {'skip' | boolean} Action\n *   Union of the action types.\n *\n * @typedef {number} Index\n *   Move to the sibling at `index` next (after node itself is completely\n *   traversed).\n *\n *   Useful if mutating the tree, such as removing the node the visitor is\n *   currently on, or any of its previous siblings.\n *   Results less than 0 or greater than or equal to `children.length` stop\n *   traversing the parent.\n *\n * @typedef {[(Action | null | undefined | void)?, (Index | null | undefined)?]} ActionTuple\n *   List with one or two values, the first an action, the second an index.\n *\n * @typedef {Action | ActionTuple | Index | null | undefined | void} VisitorResult\n *   Any value that can be returned from a visitor.\n */\n\n/**\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform the parent of node (the last of `ancestors`).\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of an ancestor still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Array<VisitedParents>} ancestors\n *   Ancestors of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n * @template {UnistNode} [Visited=UnistNode]\n *   Visited node type.\n * @template {UnistParent} [VisitedParents=UnistParent]\n *   Ancestor type.\n */\n\n/**\n * @typedef {Visitor<Matches<InclusiveDescendant<Tree>, Check>, Ancestor<Tree, Matches<InclusiveDescendant<Tree>, Check>>>} BuildVisitor\n *   Build a typed `Visitor` function from a tree and a test.\n *\n *   It will infer which values are passed as `node` and which as `parents`.\n * @template {UnistNode} [Tree=UnistNode]\n *   Tree type.\n * @template {Test} [Check=Test]\n *   Test type.\n */\n\nimport {convert} from 'unist-util-is'\nimport {color} from 'unist-util-visit-parents/do-not-use-color'\n\n/** @type {Readonly<ActionTuple>} */\nconst empty = []\n\n/**\n * Continue traversing as normal.\n */\nexport const CONTINUE = true\n\n/**\n * Stop traversing immediately.\n */\nexport const EXIT = false\n\n/**\n * Do not traverse this node’s children.\n */\nexport const SKIP = 'skip'\n\n/**\n * Visit nodes, with ancestral information.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @overload\n * @param {Tree} tree\n * @param {Check} check\n * @param {BuildVisitor<Tree, Check>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {undefined}\n *\n * @overload\n * @param {Tree} tree\n * @param {BuildVisitor<Tree>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {undefined}\n *\n * @param {UnistNode} tree\n *   Tree to traverse.\n * @param {Visitor | Test} test\n *   `unist-util-is`-compatible test\n * @param {Visitor | boolean | null | undefined} [visitor]\n *   Handle each node.\n * @param {boolean | null | undefined} [reverse]\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns {undefined}\n *   Nothing.\n *\n * @template {UnistNode} Tree\n *   Node type.\n * @template {Test} Check\n *   `unist-util-is`-compatible test.\n */\nexport function visitParents(tree, test, visitor, reverse) {\n  /** @type {Test} */\n  let check\n\n  if (typeof test === 'function' && typeof visitor !== 'function') {\n    reverse = visitor\n    // @ts-expect-error no visitor given, so `visitor` is test.\n    visitor = test\n  } else {\n    // @ts-expect-error visitor given, so `test` isn’t a visitor.\n    check = test\n  }\n\n  const is = convert(check)\n  const step = reverse ? -1 : 1\n\n  factory(tree, undefined, [])()\n\n  /**\n   * @param {UnistNode} node\n   * @param {number | undefined} index\n   * @param {Array<UnistParent>} parents\n   */\n  function factory(node, index, parents) {\n    const value = /** @type {Record<string, unknown>} */ (\n      node && typeof node === 'object' ? node : {}\n    )\n\n    if (typeof value.type === 'string') {\n      const name =\n        // `hast`\n        typeof value.tagName === 'string'\n          ? value.tagName\n          : // `xast`\n          typeof value.name === 'string'\n          ? value.name\n          : undefined\n\n      Object.defineProperty(visit, 'name', {\n        value:\n          'node (' + color(node.type + (name ? '<' + name + '>' : '')) + ')'\n      })\n    }\n\n    return visit\n\n    function visit() {\n      /** @type {Readonly<ActionTuple>} */\n      let result = empty\n      /** @type {Readonly<ActionTuple>} */\n      let subresult\n      /** @type {number} */\n      let offset\n      /** @type {Array<UnistParent>} */\n      let grandparents\n\n      if (!test || is(node, index, parents[parents.length - 1] || undefined)) {\n        // @ts-expect-error: `visitor` is now a visitor.\n        result = toResult(visitor(node, parents))\n\n        if (result[0] === EXIT) {\n          return result\n        }\n      }\n\n      if ('children' in node && node.children) {\n        const nodeAsParent = /** @type {UnistParent} */ (node)\n\n        if (nodeAsParent.children && result[0] !== SKIP) {\n          offset = (reverse ? nodeAsParent.children.length : -1) + step\n          grandparents = parents.concat(nodeAsParent)\n\n          while (offset > -1 && offset < nodeAsParent.children.length) {\n            const child = nodeAsParent.children[offset]\n\n            subresult = factory(child, offset, grandparents)()\n\n            if (subresult[0] === EXIT) {\n              return subresult\n            }\n\n            offset =\n              typeof subresult[1] === 'number' ? subresult[1] : offset + step\n          }\n        }\n      }\n\n      return result\n    }\n  }\n}\n\n/**\n * Turn a return value into a clean result.\n *\n * @param {VisitorResult} value\n *   Valid return values from visitors.\n * @returns {Readonly<ActionTuple>}\n *   Clean result.\n */\nfunction toResult(value) {\n  if (Array.isArray(value)) {\n    return value\n  }\n\n  if (typeof value === 'number') {\n    return [CONTINUE, value]\n  }\n\n  return value === null || value === undefined ? empty : [value]\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;CAWC,GAED;;;;;;;;;;;;;;;;;;;CAmBC,GAED;;;;;;;;;;;CAWC,GAED;;;CAGC,GAED;;;;;CAKC,GAED;;;;;;;;;;;;;CAaC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;;;;;;;CAiBC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GAED;;;;;;;;;;;;;;;;;;CAkBC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmCC,GAED;;;;;;;;;CASC;;;;;;AAED;AACA;;;AAEA,kCAAkC,GAClC,MAAM,QAAQ,EAAE;AAKT,MAAM,WAAW;AAKjB,MAAM,OAAO;AAKb,MAAM,OAAO;AAiDb,SAAS,aAAa,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO;IACvD,iBAAiB,GACjB,IAAI;IAEJ,IAAI,OAAO,SAAS,cAAc,OAAO,YAAY,YAAY;QAC/D,UAAU;QACV,2DAA2D;QAC3D,UAAU;IACZ,OAAO;QACL,6DAA6D;QAC7D,QAAQ;IACV;IAEA,MAAM,KAAK,CAAA,GAAA,sJAAA,CAAA,UAAO,AAAD,EAAE;IACnB,MAAM,OAAO,UAAU,CAAC,IAAI;IAE5B,QAAQ,MAAM,WAAW,EAAE;IAE3B;;;;GAIC,GACD,SAAS,QAAQ,IAAI,EAAE,KAAK,EAAE,OAAO;QACnC,MAAM,QACJ,QAAQ,OAAO,SAAS,WAAW,OAAO,CAAC;QAG7C,IAAI,OAAO,MAAM,IAAI,KAAK,UAAU;YAClC,MAAM,OACJ,SAAS;YACT,OAAO,MAAM,OAAO,KAAK,WACrB,MAAM,OAAO,GAEf,OAAO,MAAM,IAAI,KAAK,WACpB,MAAM,IAAI,GACV;YAEN,OAAO,cAAc,CAAC,OAAO,QAAQ;gBACnC,OACE,WAAW,CAAA,GAAA,oKAAA,CAAA,QAAK,AAAD,EAAE,KAAK,IAAI,GAAG,CAAC,OAAO,MAAM,OAAO,MAAM,EAAE,KAAK;YACnE;QACF;QAEA,OAAO;;QAEP,SAAS;YACP,kCAAkC,GAClC,IAAI,SAAS;YACb,kCAAkC,GAClC,IAAI;YACJ,mBAAmB,GACnB,IAAI;YACJ,+BAA+B,GAC/B,IAAI;YAEJ,IAAI,CAAC,QAAQ,GAAG,MAAM,OAAO,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,IAAI,YAAY;gBACtE,gDAAgD;gBAChD,SAAS,SAAS,QAAQ,MAAM;gBAEhC,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM;oBACtB,OAAO;gBACT;YACF;YAEA,IAAI,cAAc,QAAQ,KAAK,QAAQ,EAAE;gBACvC,MAAM,eAA2C;gBAEjD,IAAI,aAAa,QAAQ,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM;oBAC/C,SAAS,CAAC,UAAU,aAAa,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI;oBACzD,eAAe,QAAQ,MAAM,CAAC;oBAE9B,MAAO,SAAS,CAAC,KAAK,SAAS,aAAa,QAAQ,CAAC,MAAM,CAAE;wBAC3D,MAAM,QAAQ,aAAa,QAAQ,CAAC,OAAO;wBAE3C,YAAY,QAAQ,OAAO,QAAQ;wBAEnC,IAAI,SAAS,CAAC,EAAE,KAAK,MAAM;4BACzB,OAAO;wBACT;wBAEA,SACE,OAAO,SAAS,CAAC,EAAE,KAAK,WAAW,SAAS,CAAC,EAAE,GAAG,SAAS;oBAC/D;gBACF;YACF;YAEA,OAAO;QACT;IACF;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,SAAS,KAAK;IACrB,IAAI,MAAM,OAAO,CAAC,QAAQ;QACxB,OAAO;IACT;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;YAAC;YAAU;SAAM;IAC1B;IAEA,OAAO,UAAU,QAAQ,UAAU,YAAY,QAAQ;QAAC;KAAM;AAChE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2899, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/unist-util-visit/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} UnistNode\n * @typedef {import('unist').Parent} UnistParent\n * @typedef {import('unist-util-visit-parents').VisitorResult} VisitorResult\n */\n\n/**\n * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test\n *   Test from `unist-util-is`.\n *\n *   Note: we have remove and add `undefined`, because otherwise when generating\n *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,\n *   which doesn’t work when publishing on npm.\n */\n\n// To do: use types from `unist-util-visit-parents` when it’s released.\n\n/**\n * @typedef {(\n *   Fn extends (value: any) => value is infer Thing\n *   ? Thing\n *   : Fallback\n * )} Predicate\n *   Get the value of a type guard `Fn`.\n * @template Fn\n *   Value; typically function that is a type guard (such as `(x): x is Y`).\n * @template Fallback\n *   Value to yield if `Fn` is not a type guard.\n */\n\n/**\n * @typedef {(\n *   Check extends null | undefined // No test.\n *   ? Value\n *   : Value extends {type: Check} // String (type) test.\n *   ? Value\n *   : Value extends Check // Partial test.\n *   ? Value\n *   : Check extends Function // Function test.\n *   ? Predicate<Check, Value> extends Value\n *     ? Predicate<Check, Value>\n *     : never\n *   : never // Some other test?\n * )} MatchesOne\n *   Check whether a node matches a primitive check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test, but not arrays.\n */\n\n/**\n * @typedef {(\n *   Check extends Array<any>\n *   ? MatchesOne<Value, Check[keyof Check]>\n *   : MatchesOne<Value, Check>\n * )} Matches\n *   Check whether a node matches a check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test.\n */\n\n/**\n * @typedef {0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10} Uint\n *   Number; capped reasonably.\n */\n\n/**\n * @typedef {I extends 0 ? 1 : I extends 1 ? 2 : I extends 2 ? 3 : I extends 3 ? 4 : I extends 4 ? 5 : I extends 5 ? 6 : I extends 6 ? 7 : I extends 7 ? 8 : I extends 8 ? 9 : 10} Increment\n *   Increment a number in the type system.\n * @template {Uint} [I=0]\n *   Index.\n */\n\n/**\n * @typedef {(\n *   Node extends UnistParent\n *   ? Node extends {children: Array<infer Children>}\n *     ? Child extends Children ? Node : never\n *     : never\n *   : never\n * )} InternalParent\n *   Collect nodes that can be parents of `Child`.\n * @template {UnistNode} Node\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {InternalParent<InclusiveDescendant<Tree>, Child>} Parent\n *   Collect nodes in `Tree` that can be parents of `Child`.\n * @template {UnistNode} Tree\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {(\n *   Depth extends Max\n *   ? never\n *   :\n *     | InternalParent<Node, Child>\n *     | InternalAncestor<Node, InternalParent<Node, Child>, Max, Increment<Depth>>\n * )} InternalAncestor\n *   Collect nodes in `Tree` that can be ancestors of `Child`.\n * @template {UnistNode} Node\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n * @template {Uint} [Max=10]\n *   Max; searches up to this depth.\n * @template {Uint} [Depth=0]\n *   Current depth.\n */\n\n/**\n * @typedef {(\n *   Tree extends UnistParent\n *     ? Depth extends Max\n *       ? Tree\n *       : Tree | InclusiveDescendant<Tree['children'][number], Max, Increment<Depth>>\n *     : Tree\n * )} InclusiveDescendant\n *   Collect all (inclusive) descendants of `Tree`.\n *\n *   > 👉 **Note**: for performance reasons, this seems to be the fastest way to\n *   > recurse without actually running into an infinite loop, which the\n *   > previous version did.\n *   >\n *   > Practically, a max of `2` is typically enough assuming a `Root` is\n *   > passed, but it doesn’t improve performance.\n *   > It gets higher with `List > ListItem > Table > TableRow > TableCell`.\n *   > Using up to `10` doesn’t hurt or help either.\n * @template {UnistNode} Tree\n *   Tree type.\n * @template {Uint} [Max=10]\n *   Max; searches up to this depth.\n * @template {Uint} [Depth=0]\n *   Current depth.\n */\n\n/**\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform `parent`.\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of `parent` still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Visited extends UnistNode ? number | undefined : never} index\n *   Index of `node` in `parent`.\n * @param {Ancestor extends UnistParent ? Ancestor | undefined : never} parent\n *   Parent of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n * @template {UnistNode} [Visited=UnistNode]\n *   Visited node type.\n * @template {UnistParent} [Ancestor=UnistParent]\n *   Ancestor type.\n */\n\n/**\n * @typedef {Visitor<Visited, Parent<Ancestor, Visited>>} BuildVisitorFromMatch\n *   Build a typed `Visitor` function from a node and all possible parents.\n *\n *   It will infer which values are passed as `node` and which as `parent`.\n * @template {UnistNode} Visited\n *   Node type.\n * @template {UnistParent} Ancestor\n *   Parent type.\n */\n\n/**\n * @typedef {(\n *   BuildVisitorFromMatch<\n *     Matches<Descendant, Check>,\n *     Extract<Descendant, UnistParent>\n *   >\n * )} BuildVisitorFromDescendants\n *   Build a typed `Visitor` function from a list of descendants and a test.\n *\n *   It will infer which values are passed as `node` and which as `parent`.\n * @template {UnistNode} Descendant\n *   Node type.\n * @template {Test} Check\n *   Test type.\n */\n\n/**\n * @typedef {(\n *   BuildVisitorFromDescendants<\n *     InclusiveDescendant<Tree>,\n *     Check\n *   >\n * )} BuildVisitor\n *   Build a typed `Visitor` function from a tree and a test.\n *\n *   It will infer which values are passed as `node` and which as `parent`.\n * @template {UnistNode} [Tree=UnistNode]\n *   Node type.\n * @template {Test} [Check=Test]\n *   Test type.\n */\n\nimport {visitParents} from 'unist-util-visit-parents'\n\nexport {CONTINUE, EXIT, SKIP} from 'unist-util-visit-parents'\n\n/**\n * Visit nodes.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @overload\n * @param {Tree} tree\n * @param {Check} check\n * @param {BuildVisitor<Tree, Check>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {undefined}\n *\n * @overload\n * @param {Tree} tree\n * @param {BuildVisitor<Tree>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {undefined}\n *\n * @param {UnistNode} tree\n *   Tree to traverse.\n * @param {Visitor | Test} testOrVisitor\n *   `unist-util-is`-compatible test (optional, omit to pass a visitor).\n * @param {Visitor | boolean | null | undefined} [visitorOrReverse]\n *   Handle each node (when test is omitted, pass `reverse`).\n * @param {boolean | null | undefined} [maybeReverse=false]\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns {undefined}\n *   Nothing.\n *\n * @template {UnistNode} Tree\n *   Node type.\n * @template {Test} Check\n *   `unist-util-is`-compatible test.\n */\nexport function visit(tree, testOrVisitor, visitorOrReverse, maybeReverse) {\n  /** @type {boolean | null | undefined} */\n  let reverse\n  /** @type {Test} */\n  let test\n  /** @type {Visitor} */\n  let visitor\n\n  if (\n    typeof testOrVisitor === 'function' &&\n    typeof visitorOrReverse !== 'function'\n  ) {\n    test = undefined\n    visitor = testOrVisitor\n    reverse = visitorOrReverse\n  } else {\n    // @ts-expect-error: assume the overload with test was given.\n    test = testOrVisitor\n    // @ts-expect-error: assume the overload with test was given.\n    visitor = visitorOrReverse\n    reverse = maybeReverse\n  }\n\n  visitParents(tree, test, overload, reverse)\n\n  /**\n   * @param {UnistNode} node\n   * @param {Array<UnistParent>} parents\n   */\n  function overload(node, parents) {\n    const parent = parents[parents.length - 1]\n    const index = parent ? parent.children.indexOf(node) : undefined\n    return visitor(node, index, parent)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC,GAED,uEAAuE;AAEvE;;;;;;;;;;;CAWC,GAED;;;;;;;;;;;;;;;;;;;CAmBC,GAED;;;;;;;;;;;CAWC,GAED;;;CAGC,GAED;;;;;CAKC,GAED;;;;;;;;;;;;;CAaC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;;;;;;;CAiBC,GAED;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqCC,GAED;;;;;;;;;CASC,GAED;;;;;;;;;;;;;;CAcC,GAED;;;;;;;;;;;;;;CAcC;;;AAED;;;AAmDO,SAAS,MAAM,IAAI,EAAE,aAAa,EAAE,gBAAgB,EAAE,YAAY;IACvE,uCAAuC,GACvC,IAAI;IACJ,iBAAiB,GACjB,IAAI;IACJ,oBAAoB,GACpB,IAAI;IAEJ,IACE,OAAO,kBAAkB,cACzB,OAAO,qBAAqB,YAC5B;QACA,OAAO;QACP,UAAU;QACV,UAAU;IACZ,OAAO;QACL,6DAA6D;QAC7D,OAAO;QACP,6DAA6D;QAC7D,UAAU;QACV,UAAU;IACZ;IAEA,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,EAAE,MAAM,MAAM,UAAU;IAEnC;;;GAGC,GACD,SAAS,SAAS,IAAI,EAAE,OAAO;QAC7B,MAAM,SAAS,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;QAC1C,MAAM,QAAQ,SAAS,OAAO,QAAQ,CAAC,OAAO,CAAC,QAAQ;QACvD,OAAO,QAAQ,MAAM,OAAO;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3132, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/state.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').ElementContent} HastElementContent\n * @typedef {import('hast').Nodes} HastNodes\n * @typedef {import('hast').Properties} HastProperties\n * @typedef {import('hast').RootContent} HastRootContent\n * @typedef {import('hast').Text} HastText\n *\n * @typedef {import('mdast').Definition} MdastDefinition\n * @typedef {import('mdast').FootnoteDefinition} MdastFootnoteDefinition\n * @typedef {import('mdast').Nodes} MdastNodes\n * @typedef {import('mdast').Parents} MdastParents\n *\n * @typedef {import('vfile').VFile} VFile\n *\n * @typedef {import('./footer.js').FootnoteBackContentTemplate} FootnoteBackContentTemplate\n * @typedef {import('./footer.js').FootnoteBackLabelTemplate} FootnoteBackLabelTemplate\n */\n\n/**\n * @callback Handler\n *   Handle a node.\n * @param {State} state\n *   Info passed around.\n * @param {any} node\n *   mdast node to handle.\n * @param {MdastParents | undefined} parent\n *   Parent of `node`.\n * @returns {Array<HastElementContent> | HastElementContent | undefined}\n *   hast node.\n *\n * @typedef {Partial<Record<MdastNodes['type'], Handler>>} Handlers\n *   Handle nodes.\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [allowDangerousHtml=false]\n *   Whether to persist raw HTML in markdown in the hast tree (default:\n *   `false`).\n * @property {string | null | undefined} [clobberPrefix='user-content-']\n *   Prefix to use before the `id` property on footnotes to prevent them from\n *   *clobbering* (default: `'user-content-'`).\n *\n *   Pass `''` for trusted markdown and when you are careful with\n *   polyfilling.\n *   You could pass a different prefix.\n *\n *   DOM clobbering is this:\n *\n *   ```html\n *   <p id=\"x\"></p>\n *   <script>alert(x) // `x` now refers to the `p#x` DOM element</script>\n *   ```\n *\n *   The above example shows that elements are made available by browsers, by\n *   their ID, on the `window` object.\n *   This is a security risk because you might be expecting some other variable\n *   at that place.\n *   It can also break polyfills.\n *   Using a prefix solves these problems.\n * @property {VFile | null | undefined} [file]\n *   Corresponding virtual file representing the input document (optional).\n * @property {FootnoteBackContentTemplate | string | null | undefined} [footnoteBackContent]\n *   Content of the backreference back to references (default: `defaultFootnoteBackContent`).\n *\n *   The default value is:\n *\n *   ```js\n *   function defaultFootnoteBackContent(_, rereferenceIndex) {\n *     const result = [{type: 'text', value: '↩'}]\n *\n *     if (rereferenceIndex > 1) {\n *       result.push({\n *         type: 'element',\n *         tagName: 'sup',\n *         properties: {},\n *         children: [{type: 'text', value: String(rereferenceIndex)}]\n *       })\n *     }\n *\n *     return result\n *   }\n *   ```\n *\n *   This content is used in the `a` element of each backreference (the `↩`\n *   links).\n * @property {FootnoteBackLabelTemplate | string | null | undefined} [footnoteBackLabel]\n *   Label to describe the backreference back to references (default:\n *   `defaultFootnoteBackLabel`).\n *\n *   The default value is:\n *\n *   ```js\n *   function defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n *    return (\n *      'Back to reference ' +\n *      (referenceIndex + 1) +\n *      (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n *    )\n *   }\n *   ```\n *\n *   Change it when the markdown is not in English.\n *\n *   This label is used in the `ariaLabel` property on each backreference\n *   (the `↩` links).\n *   It affects users of assistive technology.\n * @property {string | null | undefined} [footnoteLabel='Footnotes']\n *   Textual label to use for the footnotes section (default: `'Footnotes'`).\n *\n *   Change it when the markdown is not in English.\n *\n *   This label is typically hidden visually (assuming a `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass different properties with the `footnoteLabelProperties` option.\n * @property {HastProperties | null | undefined} [footnoteLabelProperties={className: ['sr-only']}]\n *   Properties to use on the footnote label (default: `{className:\n *   ['sr-only']}`).\n *\n *   Change it to show the label and add other properties.\n *\n *   This label is typically hidden visually (assuming an `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass an empty string.\n *   You can also add different properties.\n *\n *   > **Note**: `id: 'footnote-label'` is always added, because footnote\n *   > calls use it with `aria-describedby` to provide an accessible label.\n * @property {string | null | undefined} [footnoteLabelTagName='h2']\n *   HTML tag name to use for the footnote label element (default: `'h2'`).\n *\n *   Change it to match your document structure.\n *\n *   This label is typically hidden visually (assuming a `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass different properties with the `footnoteLabelProperties` option.\n * @property {Handlers | null | undefined} [handlers]\n *   Extra handlers for nodes (optional).\n * @property {Array<MdastNodes['type']> | null | undefined} [passThrough]\n *   List of custom mdast node types to pass through (keep) in hast (note that\n *   the node itself is passed, but eventual children are transformed)\n *   (optional).\n * @property {Handler | null | undefined} [unknownHandler]\n *   Handler for all unknown nodes (optional).\n *\n * @typedef State\n *   Info passed around.\n * @property {(node: MdastNodes) => Array<HastElementContent>} all\n *   Transform the children of an mdast parent to hast.\n * @property {<Type extends HastNodes>(from: MdastNodes, to: Type) => HastElement | Type} applyData\n *   Honor the `data` of `from`, and generate an element instead of `node`.\n * @property {Map<string, MdastDefinition>} definitionById\n *   Definitions by their identifier.\n * @property {Map<string, MdastFootnoteDefinition>} footnoteById\n *   Footnote definitions by their identifier.\n * @property {Map<string, number>} footnoteCounts\n *   Counts for how often the same footnote was called.\n * @property {Array<string>} footnoteOrder\n *   Identifiers of order when footnote calls first appear in tree order.\n * @property {Handlers} handlers\n *   Applied handlers.\n * @property {(node: MdastNodes, parent: MdastParents | undefined) => Array<HastElementContent> | HastElementContent | undefined} one\n *   Transform an mdast node to hast.\n * @property {Options} options\n *   Configuration.\n * @property {(from: MdastNodes, node: HastNodes) => undefined} patch\n *   Copy a node’s positional info.\n * @property {<Type extends HastRootContent>(nodes: Array<Type>, loose?: boolean | undefined) => Array<HastText | Type>} wrap\n *   Wrap `nodes` with line endings between each node, adds initial/final line endings when `loose`.\n */\n\nimport structuredClone from '@ungap/structured-clone'\nimport {visit} from 'unist-util-visit'\nimport {position} from 'unist-util-position'\nimport {handlers as defaultHandlers} from './handlers/index.js'\n\nconst own = {}.hasOwnProperty\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Create `state` from an mdast tree.\n *\n * @param {MdastNodes} tree\n *   mdast node to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {State}\n *   `state` function.\n */\nexport function createState(tree, options) {\n  const settings = options || emptyOptions\n  /** @type {Map<string, MdastDefinition>} */\n  const definitionById = new Map()\n  /** @type {Map<string, MdastFootnoteDefinition>} */\n  const footnoteById = new Map()\n  /** @type {Map<string, number>} */\n  const footnoteCounts = new Map()\n  /** @type {Handlers} */\n  // @ts-expect-error: the root handler returns a root.\n  // Hard to type.\n  const handlers = {...defaultHandlers, ...settings.handlers}\n\n  /** @type {State} */\n  const state = {\n    all,\n    applyData,\n    definitionById,\n    footnoteById,\n    footnoteCounts,\n    footnoteOrder: [],\n    handlers,\n    one,\n    options: settings,\n    patch,\n    wrap\n  }\n\n  visit(tree, function (node) {\n    if (node.type === 'definition' || node.type === 'footnoteDefinition') {\n      const map = node.type === 'definition' ? definitionById : footnoteById\n      const id = String(node.identifier).toUpperCase()\n\n      // Mimick CM behavior of link definitions.\n      // See: <https://github.com/syntax-tree/mdast-util-definitions/blob/9032189/lib/index.js#L20-L21>.\n      if (!map.has(id)) {\n        // @ts-expect-error: node type matches map.\n        map.set(id, node)\n      }\n    }\n  })\n\n  return state\n\n  /**\n   * Transform an mdast node into a hast node.\n   *\n   * @param {MdastNodes} node\n   *   mdast node.\n   * @param {MdastParents | undefined} [parent]\n   *   Parent of `node`.\n   * @returns {Array<HastElementContent> | HastElementContent | undefined}\n   *   Resulting hast node.\n   */\n  function one(node, parent) {\n    const type = node.type\n    const handle = state.handlers[type]\n\n    if (own.call(state.handlers, type) && handle) {\n      return handle(state, node, parent)\n    }\n\n    if (state.options.passThrough && state.options.passThrough.includes(type)) {\n      if ('children' in node) {\n        const {children, ...shallow} = node\n        const result = structuredClone(shallow)\n        // @ts-expect-error: TS doesn’t understand…\n        result.children = state.all(node)\n        // @ts-expect-error: TS doesn’t understand…\n        return result\n      }\n\n      // @ts-expect-error: it’s custom.\n      return structuredClone(node)\n    }\n\n    const unknown = state.options.unknownHandler || defaultUnknownHandler\n\n    return unknown(state, node, parent)\n  }\n\n  /**\n   * Transform the children of an mdast node into hast nodes.\n   *\n   * @param {MdastNodes} parent\n   *   mdast node to compile\n   * @returns {Array<HastElementContent>}\n   *   Resulting hast nodes.\n   */\n  function all(parent) {\n    /** @type {Array<HastElementContent>} */\n    const values = []\n\n    if ('children' in parent) {\n      const nodes = parent.children\n      let index = -1\n      while (++index < nodes.length) {\n        const result = state.one(nodes[index], parent)\n\n        // To do: see if we van clean this? Can we merge texts?\n        if (result) {\n          if (index && nodes[index - 1].type === 'break') {\n            if (!Array.isArray(result) && result.type === 'text') {\n              result.value = trimMarkdownSpaceStart(result.value)\n            }\n\n            if (!Array.isArray(result) && result.type === 'element') {\n              const head = result.children[0]\n\n              if (head && head.type === 'text') {\n                head.value = trimMarkdownSpaceStart(head.value)\n              }\n            }\n          }\n\n          if (Array.isArray(result)) {\n            values.push(...result)\n          } else {\n            values.push(result)\n          }\n        }\n      }\n    }\n\n    return values\n  }\n}\n\n/**\n * Copy a node’s positional info.\n *\n * @param {MdastNodes} from\n *   mdast node to copy from.\n * @param {HastNodes} to\n *   hast node to copy into.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(from, to) {\n  if (from.position) to.position = position(from)\n}\n\n/**\n * Honor the `data` of `from` and maybe generate an element instead of `to`.\n *\n * @template {HastNodes} Type\n *   Node type.\n * @param {MdastNodes} from\n *   mdast node to use data from.\n * @param {Type} to\n *   hast node to change.\n * @returns {HastElement | Type}\n *   Nothing.\n */\nfunction applyData(from, to) {\n  /** @type {HastElement | Type} */\n  let result = to\n\n  // Handle `data.hName`, `data.hProperties, `data.hChildren`.\n  if (from && from.data) {\n    const hName = from.data.hName\n    const hChildren = from.data.hChildren\n    const hProperties = from.data.hProperties\n\n    if (typeof hName === 'string') {\n      // Transforming the node resulted in an element with a different name\n      // than wanted:\n      if (result.type === 'element') {\n        result.tagName = hName\n      }\n      // Transforming the node resulted in a non-element, which happens for\n      // raw, text, and root nodes (unless custom handlers are passed).\n      // The intent of `hName` is to create an element, but likely also to keep\n      // the content around (otherwise: pass `hChildren`).\n      else {\n        /** @type {Array<HastElementContent>} */\n        // @ts-expect-error: assume no doctypes in `root`.\n        const children = 'children' in result ? result.children : [result]\n        result = {type: 'element', tagName: hName, properties: {}, children}\n      }\n    }\n\n    if (result.type === 'element' && hProperties) {\n      Object.assign(result.properties, structuredClone(hProperties))\n    }\n\n    if (\n      'children' in result &&\n      result.children &&\n      hChildren !== null &&\n      hChildren !== undefined\n    ) {\n      result.children = hChildren\n    }\n  }\n\n  return result\n}\n\n/**\n * Transform an unknown node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} node\n *   Unknown mdast node.\n * @returns {HastElement | HastText}\n *   Resulting hast node.\n */\nfunction defaultUnknownHandler(state, node) {\n  const data = node.data || {}\n  /** @type {HastElement | HastText} */\n  const result =\n    'value' in node &&\n    !(own.call(data, 'hProperties') || own.call(data, 'hChildren'))\n      ? {type: 'text', value: node.value}\n      : {\n          type: 'element',\n          tagName: 'div',\n          properties: {},\n          children: state.all(node)\n        }\n\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n\n/**\n * Wrap `nodes` with line endings between each node.\n *\n * @template {HastRootContent} Type\n *   Node type.\n * @param {Array<Type>} nodes\n *   List of nodes to wrap.\n * @param {boolean | undefined} [loose=false]\n *   Whether to add line endings at start and end (default: `false`).\n * @returns {Array<HastText | Type>}\n *   Wrapped nodes.\n */\nexport function wrap(nodes, loose) {\n  /** @type {Array<HastText | Type>} */\n  const result = []\n  let index = -1\n\n  if (loose) {\n    result.push({type: 'text', value: '\\n'})\n  }\n\n  while (++index < nodes.length) {\n    if (index) result.push({type: 'text', value: '\\n'})\n    result.push(nodes[index])\n  }\n\n  if (loose && nodes.length > 0) {\n    result.push({type: 'text', value: '\\n'})\n  }\n\n  return result\n}\n\n/**\n * Trim spaces and tabs at the start of `value`.\n *\n * @param {string} value\n *   Value to trim.\n * @returns {string}\n *   Result.\n */\nfunction trimMarkdownSpaceStart(value) {\n  let index = 0\n  let code = value.charCodeAt(index)\n\n  while (code === 9 || code === 32) {\n    index++\n    code = value.charCodeAt(index)\n  }\n\n  return value.slice(index)\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;CAiBC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAyJC;;;;AAED;AACA;AACA;AACA;;;;;AAEA,MAAM,MAAM,CAAC,EAAE,cAAc;AAE7B,oBAAoB,GACpB,MAAM,eAAe,CAAC;AAYf,SAAS,YAAY,IAAI,EAAE,OAAO;IACvC,MAAM,WAAW,WAAW;IAC5B,yCAAyC,GACzC,MAAM,iBAAiB,IAAI;IAC3B,iDAAiD,GACjD,MAAM,eAAe,IAAI;IACzB,gCAAgC,GAChC,MAAM,iBAAiB,IAAI;IAC3B,qBAAqB,GACrB,qDAAqD;IACrD,gBAAgB;IAChB,MAAM,WAAW;QAAC,GAAG,0KAAA,CAAA,WAAe;QAAE,GAAG,SAAS,QAAQ;IAAA;IAE1D,kBAAkB,GAClB,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA,eAAe,EAAE;QACjB;QACA;QACA,SAAS;QACT;QACA;IACF;IAEA,CAAA,GAAA,yKAAA,CAAA,QAAK,AAAD,EAAE,MAAM,SAAU,IAAI;QACxB,IAAI,KAAK,IAAI,KAAK,gBAAgB,KAAK,IAAI,KAAK,sBAAsB;YACpE,MAAM,MAAM,KAAK,IAAI,KAAK,eAAe,iBAAiB;YAC1D,MAAM,KAAK,OAAO,KAAK,UAAU,EAAE,WAAW;YAE9C,0CAA0C;YAC1C,kGAAkG;YAClG,IAAI,CAAC,IAAI,GAAG,CAAC,KAAK;gBAChB,2CAA2C;gBAC3C,IAAI,GAAG,CAAC,IAAI;YACd;QACF;IACF;IAEA,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,IAAI,IAAI,EAAE,MAAM;QACvB,MAAM,OAAO,KAAK,IAAI;QACtB,MAAM,SAAS,MAAM,QAAQ,CAAC,KAAK;QAEnC,IAAI,IAAI,IAAI,CAAC,MAAM,QAAQ,EAAE,SAAS,QAAQ;YAC5C,OAAO,OAAO,OAAO,MAAM;QAC7B;QAEA,IAAI,MAAM,OAAO,CAAC,WAAW,IAAI,MAAM,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO;YACzE,IAAI,cAAc,MAAM;gBACtB,MAAM,EAAC,QAAQ,EAAE,GAAG,SAAQ,GAAG;gBAC/B,MAAM,SAAS,CAAA,GAAA,iLAAA,CAAA,UAAe,AAAD,EAAE;gBAC/B,2CAA2C;gBAC3C,OAAO,QAAQ,GAAG,MAAM,GAAG,CAAC;gBAC5B,2CAA2C;gBAC3C,OAAO;YACT;YAEA,iCAAiC;YACjC,OAAO,CAAA,GAAA,iLAAA,CAAA,UAAe,AAAD,EAAE;QACzB;QAEA,MAAM,UAAU,MAAM,OAAO,CAAC,cAAc,IAAI;QAEhD,OAAO,QAAQ,OAAO,MAAM;IAC9B;IAEA;;;;;;;GAOC,GACD,SAAS,IAAI,MAAM;QACjB,sCAAsC,GACtC,MAAM,SAAS,EAAE;QAEjB,IAAI,cAAc,QAAQ;YACxB,MAAM,QAAQ,OAAO,QAAQ;YAC7B,IAAI,QAAQ,CAAC;YACb,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;gBAC7B,MAAM,SAAS,MAAM,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE;gBAEvC,uDAAuD;gBACvD,IAAI,QAAQ;oBACV,IAAI,SAAS,KAAK,CAAC,QAAQ,EAAE,CAAC,IAAI,KAAK,SAAS;wBAC9C,IAAI,CAAC,MAAM,OAAO,CAAC,WAAW,OAAO,IAAI,KAAK,QAAQ;4BACpD,OAAO,KAAK,GAAG,uBAAuB,OAAO,KAAK;wBACpD;wBAEA,IAAI,CAAC,MAAM,OAAO,CAAC,WAAW,OAAO,IAAI,KAAK,WAAW;4BACvD,MAAM,OAAO,OAAO,QAAQ,CAAC,EAAE;4BAE/B,IAAI,QAAQ,KAAK,IAAI,KAAK,QAAQ;gCAChC,KAAK,KAAK,GAAG,uBAAuB,KAAK,KAAK;4BAChD;wBACF;oBACF;oBAEA,IAAI,MAAM,OAAO,CAAC,SAAS;wBACzB,OAAO,IAAI,IAAI;oBACjB,OAAO;wBACL,OAAO,IAAI,CAAC;oBACd;gBACF;YACF;QACF;QAEA,OAAO;IACT;AACF;AAEA;;;;;;;;;CASC,GACD,SAAS,MAAM,IAAI,EAAE,EAAE;IACrB,IAAI,KAAK,QAAQ,EAAE,GAAG,QAAQ,GAAG,CAAA,GAAA,4JAAA,CAAA,WAAQ,AAAD,EAAE;AAC5C;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,UAAU,IAAI,EAAE,EAAE;IACzB,+BAA+B,GAC/B,IAAI,SAAS;IAEb,4DAA4D;IAC5D,IAAI,QAAQ,KAAK,IAAI,EAAE;QACrB,MAAM,QAAQ,KAAK,IAAI,CAAC,KAAK;QAC7B,MAAM,YAAY,KAAK,IAAI,CAAC,SAAS;QACrC,MAAM,cAAc,KAAK,IAAI,CAAC,WAAW;QAEzC,IAAI,OAAO,UAAU,UAAU;YAC7B,qEAAqE;YACrE,eAAe;YACf,IAAI,OAAO,IAAI,KAAK,WAAW;gBAC7B,OAAO,OAAO,GAAG;YACnB,OAKK;gBACH,sCAAsC,GACtC,kDAAkD;gBAClD,MAAM,WAAW,cAAc,SAAS,OAAO,QAAQ,GAAG;oBAAC;iBAAO;gBAClE,SAAS;oBAAC,MAAM;oBAAW,SAAS;oBAAO,YAAY,CAAC;oBAAG;gBAAQ;YACrE;QACF;QAEA,IAAI,OAAO,IAAI,KAAK,aAAa,aAAa;YAC5C,OAAO,MAAM,CAAC,OAAO,UAAU,EAAE,CAAA,GAAA,iLAAA,CAAA,UAAe,AAAD,EAAE;QACnD;QAEA,IACE,cAAc,UACd,OAAO,QAAQ,IACf,cAAc,QACd,cAAc,WACd;YACA,OAAO,QAAQ,GAAG;QACpB;IACF;IAEA,OAAO;AACT;AAEA;;;;;;;;;CASC,GACD,SAAS,sBAAsB,KAAK,EAAE,IAAI;IACxC,MAAM,OAAO,KAAK,IAAI,IAAI,CAAC;IAC3B,mCAAmC,GACnC,MAAM,SACJ,WAAW,QACX,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,kBAAkB,IAAI,IAAI,CAAC,MAAM,YAAY,IAC1D;QAAC,MAAM;QAAQ,OAAO,KAAK,KAAK;IAAA,IAChC;QACE,MAAM;QACN,SAAS;QACT,YAAY,CAAC;QACb,UAAU,MAAM,GAAG,CAAC;IACtB;IAEN,MAAM,KAAK,CAAC,MAAM;IAClB,OAAO,MAAM,SAAS,CAAC,MAAM;AAC/B;AAcO,SAAS,KAAK,KAAK,EAAE,KAAK;IAC/B,mCAAmC,GACnC,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,CAAC;IAEb,IAAI,OAAO;QACT,OAAO,IAAI,CAAC;YAAC,MAAM;YAAQ,OAAO;QAAI;IACxC;IAEA,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,IAAI,OAAO,OAAO,IAAI,CAAC;YAAC,MAAM;YAAQ,OAAO;QAAI;QACjD,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;IAC1B;IAEA,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;QAC7B,OAAO,IAAI,CAAC;YAAC,MAAM;YAAQ,OAAO;QAAI;IACxC;IAEA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,uBAAuB,KAAK;IACnC,IAAI,QAAQ;IACZ,IAAI,OAAO,MAAM,UAAU,CAAC;IAE5B,MAAO,SAAS,KAAK,SAAS,GAAI;QAChC;QACA,OAAO,MAAM,UAAU,CAAC;IAC1B;IAEA,OAAO,MAAM,KAAK,CAAC;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3548, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-hast/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Nodes} HastNodes\n * @typedef {import('mdast').Nodes} MdastNodes\n * @typedef {import('./state.js').Options} Options\n */\n\nimport {ok as assert} from 'devlop'\nimport {footer} from './footer.js'\nimport {createState} from './state.js'\n\n/**\n * Transform mdast to hast.\n *\n * ##### Notes\n *\n * ###### HTML\n *\n * Raw HTML is available in mdast as `html` nodes and can be embedded in hast\n * as semistandard `raw` nodes.\n * Most utilities ignore `raw` nodes but two notable ones don’t:\n *\n * *   `hast-util-to-html` also has an option `allowDangerousHtml` which will\n *     output the raw HTML.\n *     This is typically discouraged as noted by the option name but is useful\n *     if you completely trust authors\n * *   `hast-util-raw` can handle the raw embedded HTML strings by parsing them\n *     into standard hast nodes (`element`, `text`, etc).\n *     This is a heavy task as it needs a full HTML parser, but it is the only\n *     way to support untrusted content\n *\n * ###### Footnotes\n *\n * Many options supported here relate to footnotes.\n * Footnotes are not specified by CommonMark, which we follow by default.\n * They are supported by GitHub, so footnotes can be enabled in markdown with\n * `mdast-util-gfm`.\n *\n * The options `footnoteBackLabel` and `footnoteLabel` define natural language\n * that explains footnotes, which is hidden for sighted users but shown to\n * assistive technology.\n * When your page is not in English, you must define translated values.\n *\n * Back references use ARIA attributes, but the section label itself uses a\n * heading that is hidden with an `sr-only` class.\n * To show it to sighted users, define different attributes in\n * `footnoteLabelProperties`.\n *\n * ###### Clobbering\n *\n * Footnotes introduces a problem, as it links footnote calls to footnote\n * definitions on the page through `id` attributes generated from user content,\n * which results in DOM clobbering.\n *\n * DOM clobbering is this:\n *\n * ```html\n * <p id=x></p>\n * <script>alert(x) // `x` now refers to the DOM `p#x` element</script>\n * ```\n *\n * Elements by their ID are made available by browsers on the `window` object,\n * which is a security risk.\n * Using a prefix solves this problem.\n *\n * More information on how to handle clobbering and the prefix is explained in\n * Example: headings (DOM clobbering) in `rehype-sanitize`.\n *\n * ###### Unknown nodes\n *\n * Unknown nodes are nodes with a type that isn’t in `handlers` or `passThrough`.\n * The default behavior for unknown nodes is:\n *\n * *   when the node has a `value` (and doesn’t have `data.hName`,\n *     `data.hProperties`, or `data.hChildren`, see later), create a hast `text`\n *     node\n * *   otherwise, create a `<div>` element (which could be changed with\n *     `data.hName`), with its children mapped from mdast to hast as well\n *\n * This behavior can be changed by passing an `unknownHandler`.\n *\n * @param {MdastNodes} tree\n *   mdast tree.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {HastNodes}\n *   hast tree.\n */\nexport function toHast(tree, options) {\n  const state = createState(tree, options)\n  const node = state.one(tree, undefined)\n  const foot = footer(state)\n  /** @type {HastNodes} */\n  const result = Array.isArray(node)\n    ? {type: 'root', children: node}\n    : node || {type: 'root', children: []}\n\n  if (foot) {\n    // If there’s a footer, there were definitions, meaning block\n    // content.\n    // So `result` is a parent node.\n    assert('children' in result)\n    result.children.push({type: 'text', value: '\\n'}, foot)\n  }\n\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;AACA;AACA;;;;AA+EO,SAAS,OAAO,IAAI,EAAE,OAAO;IAClC,MAAM,QAAQ,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,MAAM;IAChC,MAAM,OAAO,MAAM,GAAG,CAAC,MAAM;IAC7B,MAAM,OAAO,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE;IACpB,sBAAsB,GACtB,MAAM,SAAS,MAAM,OAAO,CAAC,QACzB;QAAC,MAAM;QAAQ,UAAU;IAAI,IAC7B,QAAQ;QAAC,MAAM;QAAQ,UAAU,EAAE;IAAA;IAEvC,IAAI,MAAM;QACR,6DAA6D;QAC7D,WAAW;QACX,gCAAgC;QAChC,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,cAAc;QACrB,OAAO,QAAQ,CAAC,IAAI,CAAC;YAAC,MAAM;YAAQ,OAAO;QAAI,GAAG;IACpD;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3590, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/remark-rehype/lib/index.js"], "sourcesContent": ["/**\n * @import {Root as HastRoot} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {Options as ToHastOptions} from 'mdast-util-to-hast'\n * @import {Processor} from 'unified'\n * @import {VFile} from 'vfile'\n */\n\n/**\n * @typedef {Omit<ToHastOptions, 'file'>} Options\n *\n * @callback TransformBridge\n *   Bridge-mode.\n *\n *   Runs the destination with the new hast tree.\n *   Discards result.\n * @param {MdastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {Promise<undefined>}\n *   Nothing.\n *\n * @callback TransformMutate\n *  Mutate-mode.\n *\n *  Further transformers run on the hast tree.\n * @param {MdastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {HastRoot}\n *   Tree (hast).\n */\n\nimport {toHast} from 'mdast-util-to-hast'\n\n/**\n * Turn markdown into HTML.\n *\n * ##### Notes\n *\n * ###### Signature\n *\n * * if a processor is given,\n *   runs the (rehype) plugins used on it with a hast tree,\n *   then discards the result (*bridge mode*)\n * * otherwise,\n *   returns a hast tree,\n *   the plugins used after `remarkRehype` are rehype plugins (*mutate mode*)\n *\n * > 👉 **Note**:\n * > It’s highly unlikely that you want to pass a `processor`.\n *\n * ###### HTML\n *\n * Raw HTML is available in mdast as `html` nodes and can be embedded in hast\n * as semistandard `raw` nodes.\n * Most plugins ignore `raw` nodes but two notable ones don’t:\n *\n * * `rehype-stringify` also has an option `allowDangerousHtml` which will\n *   output the raw HTML.\n *   This is typically discouraged as noted by the option name but is useful if\n *   you completely trust authors\n * * `rehype-raw` can handle the raw embedded HTML strings by parsing them\n *   into standard hast nodes (`element`, `text`, etc);\n *   this is a heavy task as it needs a full HTML parser,\n *   but it is the only way to support untrusted content\n *\n * ###### Footnotes\n *\n * Many options supported here relate to footnotes.\n * Footnotes are not specified by CommonMark,\n * which we follow by default.\n * They are supported by GitHub,\n * so footnotes can be enabled in markdown with `remark-gfm`.\n *\n * The options `footnoteBackLabel` and `footnoteLabel` define natural language\n * that explains footnotes,\n * which is hidden for sighted users but shown to assistive technology.\n * When your page is not in English,\n * you must define translated values.\n *\n * Back references use ARIA attributes,\n * but the section label itself uses a heading that is hidden with an\n * `sr-only` class.\n * To show it to sighted users,\n * define different attributes in `footnoteLabelProperties`.\n *\n * ###### Clobbering\n *\n * Footnotes introduces a problem,\n * as it links footnote calls to footnote definitions on the page through `id`\n * attributes generated from user content,\n * which results in DOM clobbering.\n *\n * DOM clobbering is this:\n *\n * ```html\n * <p id=x></p>\n * <script>alert(x) // `x` now refers to the DOM `p#x` element</script>\n * ```\n *\n * Elements by their ID are made available by browsers on the `window` object,\n * which is a security risk.\n * Using a prefix solves this problem.\n *\n * More information on how to handle clobbering and the prefix is explained in\n * *Example: headings (DOM clobbering)* in `rehype-sanitize`.\n *\n * ###### Unknown nodes\n *\n * Unknown nodes are nodes with a type that isn’t in `handlers` or `passThrough`.\n * The default behavior for unknown nodes is:\n *\n * * when the node has a `value`\n *   (and doesn’t have `data.hName`, `data.hProperties`, or `data.hChildren`,\n *   see later),\n *   create a hast `text` node\n * * otherwise,\n *   create a `<div>` element (which could be changed with `data.hName`),\n *   with its children mapped from mdast to hast as well\n *\n * This behavior can be changed by passing an `unknownHandler`.\n *\n * @overload\n * @param {Processor} processor\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformBridge}\n *\n * @overload\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformMutate}\n *\n * @overload\n * @param {Readonly<Options> | Processor | null | undefined} [destination]\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformBridge | TransformMutate}\n *\n * @param {Readonly<Options> | Processor | null | undefined} [destination]\n *   Processor or configuration (optional).\n * @param {Readonly<Options> | null | undefined} [options]\n *   When a processor was given,\n *   configuration (optional).\n * @returns {TransformBridge | TransformMutate}\n *   Transform.\n */\nexport default function remarkRehype(destination, options) {\n  if (destination && 'run' in destination) {\n    /**\n     * @type {TransformBridge}\n     */\n    return async function (tree, file) {\n      // Cast because root in -> root out.\n      const hastTree = /** @type {HastRoot} */ (\n        toHast(tree, {file, ...options})\n      )\n      await destination.run(hastTree, file)\n    }\n  }\n\n  /**\n   * @type {TransformMutate}\n   */\n  return function (tree, file) {\n    // Cast because root in -> root out.\n    // To do: in the future, disallow ` || options` fallback.\n    // With `unified-engine`, `destination` can be `undefined` but\n    // `options` will be the file set.\n    // We should not pass that as `options`.\n    return /** @type {HastRoot} */ (\n      toHast(tree, {file, ...(destination || options)})\n    )\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;CAyBC;;;AAED;;AAgHe,SAAS,aAAa,WAAW,EAAE,OAAO;IACvD,IAAI,eAAe,SAAS,aAAa;QACvC;;KAEC,GACD,OAAO,eAAgB,IAAI,EAAE,IAAI;YAC/B,oCAAoC;YACpC,MAAM,WACJ,CAAA,GAAA,8JAAA,CAAA,SAAM,AAAD,EAAE,MAAM;gBAAC;gBAAM,GAAG,OAAO;YAAA;YAEhC,MAAM,YAAY,GAAG,CAAC,UAAU;QAClC;IACF;IAEA;;GAEC,GACD,OAAO,SAAU,IAAI,EAAE,IAAI;QACzB,oCAAoC;QACpC,yDAAyD;QACzD,8DAA8D;QAC9D,kCAAkC;QAClC,wCAAwC;QACxC,OACE,CAAA,GAAA,8JAAA,CAAA,SAAM,AAAD,EAAE,MAAM;YAAC;YAAM,GAAI,eAAe,OAAO;QAAC;IAEnD;AACF", "ignoreList": [0], "debugId": null}}]}