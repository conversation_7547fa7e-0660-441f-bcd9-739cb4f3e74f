{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/providers/ConvexProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { ConvexAuthProvider } from \"@convex-dev/auth/react\";\nimport { ConvexReactClient } from \"convex/react\";\nimport { ReactNode } from \"react\";\n\nconst convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL as string);\n\ninterface ConvexClientProviderProps {\n  children: ReactNode;\n}\n\nexport function ConvexClientProvider({ children }: ConvexClientProviderProps) {\n  return (\n    <ConvexAuthProvider client={convex}>\n      {children}\n    </ConvexAuthProvider>\n  );\n}\n"], "names": [], "mappings": ";;;AAMqC;;AAJrC;AACA;AAAA;AAHA;;;;AAMA,MAAM,SAAS,IAAI,2JAAA,CAAA,oBAAiB;AAM7B,SAAS,qBAAqB,EAAE,QAAQ,EAA6B;IAC1E,qBACE,6LAAC,oKAAA,CAAA,qBAAkB;QAAC,QAAQ;kBACzB;;;;;;AAGP;KANgB", "debugId": null}}]}