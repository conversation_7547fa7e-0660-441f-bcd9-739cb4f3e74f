(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/rehype-parse/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_145bf99f._.js",
  "static/chunks/node_modules_rehype-parse_index_245f4c20.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/rehype-parse/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/rehype-stringify/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_34e75a3a._.js",
  "static/chunks/node_modules_rehype-stringify_index_245f4c20.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/rehype-stringify/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/unified/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_6f765e4c._.js",
  "static/chunks/node_modules_unified_index_245f4c20.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/unified/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/hast-util-from-dom/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_51093352._.js",
  "static/chunks/node_modules_hast-util-from-dom_index_245f4c20.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/hast-util-from-dom/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/rehype-remark/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_1ee37c44._.js",
  "static/chunks/node_modules_rehype-remark_index_245f4c20.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/rehype-remark/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/remark-gfm/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_a15edae5._.js",
  "static/chunks/node_modules_remark-gfm_index_245f4c20.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/remark-gfm/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/remark-stringify/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_f1e98985._.js",
  "static/chunks/node_modules_remark-stringify_index_245f4c20.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/remark-stringify/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/remark-parse/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_2e6e588a._.js",
  "static/chunks/node_modules_remark-parse_index_245f4c20.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/remark-parse/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/remark-rehype/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_1d9efcfd._.js",
  "static/chunks/node_modules_remark-rehype_index_245f4c20.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/remark-rehype/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/rehype-format/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_ac8d6dc0._.js",
  "static/chunks/node_modules_rehype-format_index_245f4c20.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/rehype-format/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/emoji-mart/dist/module.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_emoji-mart_dist_module_70b13588.js",
  "static/chunks/node_modules_emoji-mart_dist_module_c0fe22c5.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/emoji-mart/dist/module.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@emoji-mart/data/sets/15/native.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@emoji-mart_data_sets_15_native_json_7f68325b._.js",
  "static/chunks/node_modules_@emoji-mart_data_sets_15_native_json_20f381bb._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@emoji-mart/data/sets/15/native.json (json)");
    });
});
}}),
}]);