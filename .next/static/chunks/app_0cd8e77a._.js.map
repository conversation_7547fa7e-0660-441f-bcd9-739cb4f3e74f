{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport { \n  Home, \n  FileText, \n  Clock, \n  Users, \n  Settings, \n  ChevronLeft,\n  ChevronRight,\n  Film\n} from 'lucide-react';\nimport { Button } from '../ui/button';\nimport { cn } from '@/lib/utils';\n\ninterface SidebarProps {\n  className?: string;\n}\n\nconst navigationItems = [\n  {\n    title: 'Dashboard',\n    href: '/',\n    icon: Home,\n  },\n  {\n    title: 'Scripts',\n    href: '/scripts',\n    icon: Film,\n  },\n  {\n    title: 'Recent',\n    href: '/recent',\n    icon: Clock,\n  },\n  {\n    title: 'Shared with me',\n    href: '/shared',\n    icon: Users,\n  },\n  {\n    title: 'Settings',\n    href: '/settings',\n    icon: Settings,\n  },\n];\n\nexport function Sidebar({ className }: SidebarProps) {\n  const [isCollapsed, setIsCollapsed] = useState(false);\n  const pathname = usePathname();\n\n  return (\n    <div\n      className={cn(\n        'relative flex flex-col bg-white border-r border-gray-200 transition-all duration-300',\n        isCollapsed ? 'w-16' : 'w-64',\n        className\n      )}\n    >\n      {/* Header */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n        {!isCollapsed && (\n          <div className=\"flex items-center gap-2\">\n            <Film className=\"text-blue-600\" size={24} />\n            <h2 className=\"text-xl font-semibold text-gray-900\">Scripty</h2>\n          </div>\n        )}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => setIsCollapsed(!isCollapsed)}\n          className=\"h-8 w-8 p-0\"\n        >\n          {isCollapsed ? (\n            <ChevronRight className=\"h-4 w-4\" />\n          ) : (\n            <ChevronLeft className=\"h-4 w-4\" />\n          )}\n        </Button>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 p-4\">\n        <ul className=\"space-y-2\">\n          {navigationItems.map((item) => {\n            const isActive = pathname === item.href || \n              (item.href !== '/' && pathname.startsWith(item.href));\n            \n            return (\n              <li key={item.href}>\n                <Link\n                  href={item.href}\n                  className={cn(\n                    'flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors',\n                    isActive\n                      ? 'bg-blue-50 text-blue-700 border border-blue-200'\n                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900',\n                    isCollapsed && 'justify-center px-2'\n                  )}\n                >\n                  <item.icon className=\"h-5 w-5 flex-shrink-0\" />\n                  {!isCollapsed && <span>{item.title}</span>}\n                </Link>\n              </li>\n            );\n          })}\n        </ul>\n      </nav>\n\n      {/* User section at bottom */}\n      <div className=\"p-4 border-t border-gray-200\">\n        {!isCollapsed ? (\n          <div className=\"flex items-center gap-3 px-3 py-2\">\n            <div className=\"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center\">\n              <span className=\"text-sm font-medium text-blue-700\">U</span>\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-sm font-medium text-gray-900 truncate\">User</p>\n              <p className=\"text-xs text-gray-500 truncate\"><EMAIL></p>\n            </div>\n          </div>\n        ) : (\n          <div className=\"flex justify-center\">\n            <div className=\"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center\">\n              <span className=\"text-sm font-medium text-blue-700\">U</span>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;;;AAhBA;;;;;;;AAsBA,MAAM,kBAAkB;IACtB;QACE,OAAO;QACP,MAAM;QACN,MAAM,sMAAA,CAAA,OAAI;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,qMAAA,CAAA,OAAI;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;IACb;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;IACb;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;IAChB;CACD;AAEM,SAAS,QAAQ,EAAE,SAAS,EAAgB;;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wFACA,cAAc,SAAS,QACvB;;0BAIF,6LAAC;gBAAI,WAAU;;oBACZ,CAAC,6BACA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;gCAAgB,MAAM;;;;;;0CACtC,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAGxD,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,eAAe,CAAC;wBAC/B,WAAU;kCAET,4BACC,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;iDAExB,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAM7B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;8BACX,gBAAgB,GAAG,CAAC,CAAC;wBACpB,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;wBAErD,qBACE,6LAAC;sCACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sFACA,WACI,oDACA,uDACJ,eAAe;;kDAGjB,6LAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;oCACpB,CAAC,6BAAe,6LAAC;kDAAM,KAAK,KAAK;;;;;;;;;;;;2BAZ7B,KAAK,IAAI;;;;;oBAgBtB;;;;;;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;0BACZ,CAAC,4BACA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CAAoC;;;;;;;;;;;sCAEtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAC1D,6LAAC;oCAAE,WAAU;8CAAiC;;;;;;;;;;;;;;;;;yCAIlD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlE;GApFgB;;QAEG,qIAAA,CAAA,cAAW;;;KAFd", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/auth/SignOutButton.tsx"], "sourcesContent": ["\"use client\";\nimport { useAuthActions } from \"@convex-dev/auth/react\";\nimport { useConvexAuth } from \"convex/react\";\nimport { Button } from '../ui/button';\n\nexport function SignOutButton() {\n  const { isAuthenticated } = useConvexAuth();\n  const { signOut } = useAuthActions();\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <Button\n      variant={\"default\"}\n      size={\"sm\"}\n      onClick={() => void signOut()}\n    >\n      Sign out\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;;;AAHA;;;;AAKO,SAAS;;IACd,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,oKAAA,CAAA,gBAAa,AAAD;IACxC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,oKAAA,CAAA,iBAAc,AAAD;IAEjC,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,SAAS;QACT,MAAM;QACN,SAAS,IAAM,KAAK;kBACrB;;;;;;AAIL;GAjBgB;;QACc,oKAAA,CAAA,gBAAa;QACrB,oKAAA,CAAA,iBAAc;;;KAFpB", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { Film } from 'lucide-react';\nimport { SignOutButton } from '../auth/SignOutButton';\n\ninterface HeaderProps {\n  title?: string;\n  showSidebar?: boolean;\n}\n\nexport function Header({ title = 'Scripty', showSidebar = true }: HeaderProps) {\n  return (\n    <header className=\"sticky top-0 z-10 py-3 bg-white/80 backdrop-blur-sm h-16 flex justify-between items-center border-b shadow-sm px-4\">\n      <div className=\"flex items-center gap-2\">\n        {!showSidebar && (\n          <>\n            <Film className=\"text-blue-600\" size={24} />\n            <h2 className=\"text-xl font-semibold text-gray-900\">{title}</h2>\n          </>\n        )}\n      </div>\n      <SignOutButton />\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUO,SAAS,OAAO,EAAE,QAAQ,SAAS,EAAE,cAAc,IAAI,EAAe;IAC3E,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;0BACZ,CAAC,6BACA;;sCACE,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;4BAAgB,MAAM;;;;;;sCACtC,6LAAC;4BAAG,WAAU;sCAAuC;;;;;;;;;;;;;0BAI3D,6LAAC,8IAAA,CAAA,gBAAa;;;;;;;;;;;AAGpB;KAdgB", "debugId": null}}, {"offset": {"line": 472, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/auth/SignInForm.tsx"], "sourcesContent": ["\"use client\";\nimport { useAuthActions } from \"@convex-dev/auth/react\";\nimport { useState } from \"react\";\nimport { toast } from \"sonner\";\n\nexport function SignInForm() {\n  const { signIn } = useAuthActions();\n  const [flow, setFlow] = useState<\"signIn\" | \"signUp\">(\"signIn\");\n  const [submitting, setSubmitting] = useState(false);\n\n  return (\n    <div className=\"w-full\">\n      <form\n        className=\"flex flex-col gap-2\"\n        onSubmit={(e) => {\n          e.preventDefault();\n          setSubmitting(true);\n          const formData = new FormData(e.target as HTMLFormElement);\n          formData.set(\"flow\", flow);\n          void signIn(\"password\", formData).catch((error) => {\n            let toastTitle = \"\";\n            if (error.message.includes(\"Invalid password\")) {\n              toastTitle = \"Invalid password. Please try again.\";\n            } else {\n              toastTitle =\n                flow === \"signIn\"\n                  ? \"Could not sign in, did you mean to sign up?\"\n                  : \"Could not sign up, did you mean to sign in?\";\n            }\n            toast.error(toastTitle);\n            setSubmitting(false);\n          });\n        }}\n      >\n        <input\n          className=\"auth-input-field\"\n          type=\"email\"\n          name=\"email\"\n          placeholder=\"Email\"\n          required\n        />\n        <input\n          className=\"auth-input-field\"\n          type=\"password\"\n          name=\"password\"\n          placeholder=\"Password\"\n          required\n        />\n        <button className=\"auth-button\" type=\"submit\" disabled={submitting}>\n          {flow === \"signIn\" ? \"Sign in\" : \"Sign up\"}\n        </button>\n        <div className=\"text-center text-sm text-secondary\">\n          <span className=\"text-slate-400\">\n            {flow === \"signIn\"\n              ? \"Don't have an account? \"\n              : \"Already have an account? \"}\n          </span>\n          <button\n            type=\"button\"\n            className=\"text-primary text-right hover:text-primary-hover hover:underline font-medium cursor-pointer\"\n            onClick={() => setFlow(flow === \"signIn\" ? \"signUp\" : \"signIn\")}\n          >\n            {flow === \"signIn\" ? \"Sign up instead\" : \"Sign in instead\"}\n          </button>\n        </div>\n      </form>\n      <div className=\"flex items-center justify-center my-3\">\n        <hr className=\"my-4 grow border-gray-200\" />\n        <span className=\"mx-4 text-secondary\">or</span>\n        <hr className=\"my-4 grow border-gray-200\" />\n      </div>\n      <button className=\"auth-button\" onClick={() => void signIn(\"anonymous\")}>\n        Sign in anonymously\n      </button>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAKO,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,oKAAA,CAAA,iBAAc,AAAD;IAChC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAU;gBACV,UAAU,CAAC;oBACT,EAAE,cAAc;oBAChB,cAAc;oBACd,MAAM,WAAW,IAAI,SAAS,EAAE,MAAM;oBACtC,SAAS,GAAG,CAAC,QAAQ;oBACrB,KAAK,OAAO,YAAY,UAAU,KAAK,CAAC,CAAC;wBACvC,IAAI,aAAa;wBACjB,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,qBAAqB;4BAC9C,aAAa;wBACf,OAAO;4BACL,aACE,SAAS,WACL,gDACA;wBACR;wBACA,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACZ,cAAc;oBAChB;gBACF;;kCAEA,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,MAAK;wBACL,aAAY;wBACZ,QAAQ;;;;;;kCAEV,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,MAAK;wBACL,aAAY;wBACZ,QAAQ;;;;;;kCAEV,6LAAC;wBAAO,WAAU;wBAAc,MAAK;wBAAS,UAAU;kCACrD,SAAS,WAAW,YAAY;;;;;;kCAEnC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CACb,SAAS,WACN,4BACA;;;;;;0CAEN,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,QAAQ,SAAS,WAAW,WAAW;0CAErD,SAAS,WAAW,oBAAoB;;;;;;;;;;;;;;;;;;0BAI/C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;;;;;kCACd,6LAAC;wBAAK,WAAU;kCAAsB;;;;;;kCACtC,6LAAC;wBAAG,WAAU;;;;;;;;;;;;0BAEhB,6LAAC;gBAAO,WAAU;gBAAc,SAAS,IAAM,KAAK,OAAO;0BAAc;;;;;;;;;;;;AAK/E;GAvEgB;;QACK,oKAAA,CAAA,iBAAc;;;KADnB", "debugId": null}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/components/layout/AppLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport { Authenticated, Unauthenticated } from 'convex/react';\nimport { Sidebar } from './Sidebar';\nimport { Header } from './Header';\nimport { SignInForm } from '../auth/SignInForm';\nimport { FileText } from 'lucide-react';\n\ninterface AppLayoutProps {\n  children: ReactNode;\n  showSidebar?: boolean;\n}\n\nexport function AppLayout({ children, showSidebar = true }: AppLayoutProps) {\n  return (\n    <div className=\"h-screen flex flex-col bg-gray-50 overflow-hidden\" data-testid=\"app-loaded\">\n      <Unauthenticated>\n        <div className=\"flex-1 flex items-center justify-center p-8\">\n          <div className=\"w-full max-w-md mx-auto text-center\">\n            <div className=\"mb-8\">\n              <FileText size={64} className=\"mx-auto mb-4 text-blue-600\" />\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                Scripty\n              </h1>\n              <p className=\"text-gray-600\">\n                Create and edit scripts and screenplays together in real-time\n              </p>\n            </div>\n            <SignInForm />\n          </div>\n        </div>\n      </Unauthenticated>\n\n      <Authenticated>\n        <div className=\"flex-1 flex h-full overflow-hidden\">\n          {showSidebar && <Sidebar />}\n          <div className=\"flex-1 flex flex-col\">\n            <Header showSidebar={showSidebar} />\n            <main className=\"flex-1 overflow-hidden\">\n              {children}\n            </main>\n          </div>\n        </div>\n      </Authenticated>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AACA;AACA;AAPA;;;;;;;AAcO,SAAS,UAAU,EAAE,QAAQ,EAAE,cAAc,IAAI,EAAkB;IACxE,qBACE,6LAAC;QAAI,WAAU;QAAoD,eAAY;;0BAC7E,6LAAC,iKAAA,CAAA,kBAAe;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC9B,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAI/B,6LAAC,2IAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;0BAKjB,6LAAC,iKAAA,CAAA,gBAAa;0BACZ,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,6BAAe,6LAAC,0IAAA,CAAA,UAAO;;;;;sCACxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yIAAA,CAAA,SAAM;oCAAC,aAAa;;;;;;8CACrB,6LAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;KAjCgB", "debugId": null}}, {"offset": {"line": 786, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/app/recent/page.tsx"], "sourcesContent": ["'use client';\n\nimport { AppLayout } from \"../components/layout/AppLayout\";\nimport { Clock } from \"lucide-react\";\n\nexport default function RecentPage() {\n  return (\n    <AppLayout>\n      <div className=\"flex-1 flex items-center justify-center p-8\">\n        <div className=\"text-center text-gray-500 max-w-md\">\n          <Clock size={64} className=\"mx-auto mb-6 opacity-50\" />\n          <h2 className=\"text-2xl font-semibold mb-3 text-gray-900\">Recent Documents</h2>\n          <p className=\"mb-6 text-gray-600\">\n            Your recently accessed documents will appear here.\n          </p>\n          <p className=\"text-sm text-gray-500\">\n            This feature will be implemented in a future update.\n          </p>\n        </div>\n      </div>\n    </AppLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,6LAAC,4IAAA,CAAA,YAAS;kBACR,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uMAAA,CAAA,QAAK;wBAAC,MAAM;wBAAI,WAAU;;;;;;kCAC3B,6LAAC;wBAAG,WAAU;kCAA4C;;;;;;kCAC1D,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAO/C;KAjBwB", "debugId": null}}]}