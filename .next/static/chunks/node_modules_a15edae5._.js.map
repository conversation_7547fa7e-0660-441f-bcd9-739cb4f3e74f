{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/remark-gfm/index.js"], "sourcesContent": ["// Note: types exposed from `index.d.ts`.\nexport {default} from './lib/index.js'\n"], "names": [], "mappings": "AAAA,yCAAyC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/ccount/index.js"], "sourcesContent": ["/**\n * Count how often a character (or substring) is used in a string.\n *\n * @param {string} value\n *   Value to search in.\n * @param {string} character\n *   Character (or substring) to look for.\n * @return {number}\n *   Number of times `character` occurred in `value`.\n */\nexport function ccount(value, character) {\n  const source = String(value)\n\n  if (typeof character !== 'string') {\n    throw new TypeError('Expected character')\n  }\n\n  let count = 0\n  let index = source.indexOf(character)\n\n  while (index !== -1) {\n    count++\n    index = source.indexOf(character, index + character.length)\n  }\n\n  return count\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;AACM,SAAS,OAAO,KAAK,EAAE,SAAS;IACrC,MAAM,SAAS,OAAO;IAEtB,IAAI,OAAO,cAAc,UAAU;QACjC,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,QAAQ;IACZ,IAAI,QAAQ,OAAO,OAAO,CAAC;IAE3B,MAAO,UAAU,CAAC,EAAG;QACnB;QACA,QAAQ,OAAO,OAAO,CAAC,WAAW,QAAQ,UAAU,MAAM;IAC5D;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/dequal/dist/index.mjs"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nfunction find(iter, tar, key) {\n\tfor (key of iter.keys()) {\n\t\tif (dequal(key, tar)) return key;\n\t}\n}\n\nexport function dequal(foo, bar) {\n\tvar ctor, len, tmp;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ctor === Set) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len;\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!bar.has(tmp)) return false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === Map) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len[0];\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!dequal(len[1], bar.get(tmp))) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === ArrayBuffer) {\n\t\t\tfoo = new Uint8Array(foo);\n\t\t\tbar = new Uint8Array(bar);\n\t\t} else if (ctor === DataView) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo.getInt8(len) === bar.getInt8(len));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ArrayBuffer.isView(foo)) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo[len] === bar[len]);\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n"], "names": [], "mappings": ";;;AAAA,IAAI,MAAM,OAAO,SAAS,CAAC,cAAc;AAEzC,SAAS,KAAK,IAAI,EAAE,GAAG,EAAE,GAAG;IAC3B,KAAK,OAAO,KAAK,IAAI,GAAI;QACxB,IAAI,OAAO,KAAK,MAAM,OAAO;IAC9B;AACD;AAEO,SAAS,OAAO,GAAG,EAAE,GAAG;IAC9B,IAAI,MAAM,KAAK;IACf,IAAI,QAAQ,KAAK,OAAO;IAExB,IAAI,OAAO,OAAO,CAAC,OAAK,IAAI,WAAW,MAAM,IAAI,WAAW,EAAE;QAC7D,IAAI,SAAS,MAAM,OAAO,IAAI,OAAO,OAAO,IAAI,OAAO;QACvD,IAAI,SAAS,QAAQ,OAAO,IAAI,QAAQ,OAAO,IAAI,QAAQ;QAE3D,IAAI,SAAS,OAAO;YACnB,IAAI,CAAC,MAAI,IAAI,MAAM,MAAM,IAAI,MAAM,EAAE;gBACpC,MAAO,SAAS,OAAO,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI;YAC1C;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,SAAS,KAAK;YACjB,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;gBAC1B,OAAO;YACR;YACA,KAAK,OAAO,IAAK;gBAChB,MAAM;gBACN,IAAI,OAAO,OAAO,QAAQ,UAAU;oBACnC,MAAM,KAAK,KAAK;oBAChB,IAAI,CAAC,KAAK,OAAO;gBAClB;gBACA,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,OAAO;YAC3B;YACA,OAAO;QACR;QAEA,IAAI,SAAS,KAAK;YACjB,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;gBAC1B,OAAO;YACR;YACA,KAAK,OAAO,IAAK;gBAChB,MAAM,GAAG,CAAC,EAAE;gBACZ,IAAI,OAAO,OAAO,QAAQ,UAAU;oBACnC,MAAM,KAAK,KAAK;oBAChB,IAAI,CAAC,KAAK,OAAO;gBAClB;gBACA,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,OAAO;oBAClC,OAAO;gBACR;YACD;YACA,OAAO;QACR;QAEA,IAAI,SAAS,aAAa;YACzB,MAAM,IAAI,WAAW;YACrB,MAAM,IAAI,WAAW;QACtB,OAAO,IAAI,SAAS,UAAU;YAC7B,IAAI,CAAC,MAAI,IAAI,UAAU,MAAM,IAAI,UAAU,EAAE;gBAC5C,MAAO,SAAS,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC;YAClD;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,YAAY,MAAM,CAAC,MAAM;YAC5B,IAAI,CAAC,MAAI,IAAI,UAAU,MAAM,IAAI,UAAU,EAAE;gBAC5C,MAAO,SAAS,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI;YACtC;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,CAAC,QAAQ,OAAO,QAAQ,UAAU;YACrC,MAAM;YACN,IAAK,QAAQ,IAAK;gBACjB,IAAI,IAAI,IAAI,CAAC,KAAK,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,KAAK,OAAO,OAAO;gBACjE,IAAI,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG,OAAO;YAC7D;YACA,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK;QACpC;IACD;IAEA,OAAO,QAAQ,OAAO,QAAQ;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/devlop/lib/development.js"], "sourcesContent": ["import {dequal} from 'dequal'\n\n/**\n * @type {Set<string>}\n */\nconst codesWarned = new Set()\n\nclass AssertionError extends Error {\n  name = /** @type {const} */ ('Assertion')\n  code = /** @type {const} */ ('ERR_ASSERTION')\n\n  /**\n   * Create an assertion error.\n   *\n   * @param {string} message\n   *   Message explaining error.\n   * @param {unknown} actual\n   *   Value.\n   * @param {unknown} expected\n   *   Baseline.\n   * @param {string} operator\n   *   Name of equality operation.\n   * @param {boolean} generated\n   *   Whether `message` is a custom message or not\n   * @returns\n   *   Instance.\n   */\n  // eslint-disable-next-line max-params\n  constructor(message, actual, expected, operator, generated) {\n    super(message)\n\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor)\n    }\n\n    /**\n     * @type {unknown}\n     */\n    this.actual = actual\n\n    /**\n     * @type {unknown}\n     */\n    this.expected = expected\n\n    /**\n     * @type {boolean}\n     */\n    this.generated = generated\n\n    /**\n     * @type {string}\n     */\n    this.operator = operator\n  }\n}\n\nclass DeprecationE<PERSON>r extends Error {\n  name = /** @type {const} */ ('DeprecationWarning')\n\n  /**\n   * Create a deprecation message.\n   *\n   * @param {string} message\n   *   Message explaining deprecation.\n   * @param {string | undefined} code\n   *   Deprecation identifier; deprecation messages will be generated only once per code.\n   * @returns\n   *   Instance.\n   */\n  constructor(message, code) {\n    super(message)\n\n    /**\n     * @type {string | undefined}\n     */\n    this.code = code\n  }\n}\n\n/**\n * Wrap a function or class to show a deprecation message when first called.\n *\n * > 👉 **Important**: only shows a message when the `development` condition is\n * > used, does nothing in production.\n *\n * When the resulting wrapped `fn` is called, emits a warning once to\n * `console.error` (`stderr`).\n * If a code is given, one warning message will be emitted in total per code.\n *\n * @template {Function} T\n *   Function or class kind.\n * @param {T} fn\n *   Function or class.\n * @param {string} message\n *   Message explaining deprecation.\n * @param {string | null | undefined} [code]\n *   Deprecation identifier (optional); deprecation messages will be generated\n *   only once per code.\n * @returns {T}\n *   Wrapped `fn`.\n */\nexport function deprecate(fn, message, code) {\n  let warned = false\n\n  // The wrapper will keep the same prototype as fn to maintain prototype chain\n  Object.setPrototypeOf(deprecated, fn)\n\n  // @ts-expect-error: it’s perfect, typescript…\n  return deprecated\n\n  /**\n   * @this {unknown}\n   * @param  {...Array<unknown>} args\n   * @returns {unknown}\n   */\n  function deprecated(...args) {\n    if (!warned) {\n      warned = true\n\n      if (typeof code === 'string' && codesWarned.has(code)) {\n        // Empty.\n      } else {\n        console.error(new DeprecationError(message, code || undefined))\n\n        if (typeof code === 'string') codesWarned.add(code)\n      }\n    }\n\n    return new.target\n      ? Reflect.construct(fn, args, new.target)\n      : Reflect.apply(fn, this, args)\n  }\n}\n\n/**\n * Assert deep strict equivalence.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @template {unknown} T\n *   Expected kind.\n * @param {unknown} actual\n *   Value.\n * @param {T} expected\n *   Baseline.\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Expected values to be deeply equal'`).\n * @returns {asserts actual is T}\n *   Nothing; throws when `actual` is not deep strict equal to `expected`.\n * @throws {AssertionError}\n *   Throws when `actual` is not deep strict equal to `expected`.\n */\nexport function equal(actual, expected, message) {\n  assert(\n    dequal(actual, expected),\n    actual,\n    expected,\n    'equal',\n    'Expected values to be deeply equal',\n    message\n  )\n}\n\n/**\n * Assert if `value` is truthy.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @param {unknown} value\n *   Value to assert.\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Expected value to be truthy'`).\n * @returns {asserts value}\n *   Nothing; throws when `value` is falsey.\n * @throws {AssertionError}\n *   Throws when `value` is falsey.\n */\nexport function ok(value, message) {\n  assert(\n    Boolean(value),\n    false,\n    true,\n    'ok',\n    'Expected value to be truthy',\n    message\n  )\n}\n\n/**\n * Assert that a code path never happens.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Unreachable'`).\n * @returns {never}\n *   Nothing; always throws.\n * @throws {AssertionError}\n *   Throws when `value` is falsey.\n */\nexport function unreachable(message) {\n  assert(false, false, true, 'ok', 'Unreachable', message)\n}\n\n/**\n * @param {boolean} bool\n *   Whether to skip this operation.\n * @param {unknown} actual\n *   Actual value.\n * @param {unknown} expected\n *   Expected value.\n * @param {string} operator\n *   Operator.\n * @param {string} defaultMessage\n *   Default message for operation.\n * @param {Error | string | null | undefined} userMessage\n *   User-provided message.\n * @returns {asserts bool}\n *   Nothing; throws when falsey.\n */\n// eslint-disable-next-line max-params\nfunction assert(bool, actual, expected, operator, defaultMessage, userMessage) {\n  if (!bool) {\n    throw userMessage instanceof Error\n      ? userMessage\n      : new AssertionError(\n          userMessage || defaultMessage,\n          actual,\n          expected,\n          operator,\n          !userMessage\n        )\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA;;CAEC,GACD,MAAM,cAAc,IAAI;AAExB,MAAM,uBAAuB;IAC3B,OAA6B,YAAY;IACzC,OAA6B,gBAAgB;IAE7C;;;;;;;;;;;;;;;GAeC,GACD,sCAAsC;IACtC,YAAY,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAE;QAC1D,KAAK,CAAC;QAEN,IAAI,MAAM,iBAAiB,EAAE;YAC3B,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;QAChD;QAEA;;KAEC,GACD,IAAI,CAAC,MAAM,GAAG;QAEd;;KAEC,GACD,IAAI,CAAC,QAAQ,GAAG;QAEhB;;KAEC,GACD,IAAI,CAAC,SAAS,GAAG;QAEjB;;KAEC,GACD,IAAI,CAAC,QAAQ,GAAG;IAClB;AACF;AAEA,MAAM,yBAAyB;IAC7B,OAA6B,qBAAqB;IAElD;;;;;;;;;GASC,GACD,YAAY,OAAO,EAAE,IAAI,CAAE;QACzB,KAAK,CAAC;QAEN;;KAEC,GACD,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAwBO,SAAS,UAAU,EAAE,EAAE,OAAO,EAAE,IAAI;IACzC,IAAI,SAAS;IAEb,6EAA6E;IAC7E,OAAO,cAAc,CAAC,YAAY;IAElC,8CAA8C;IAC9C,OAAO;;IAEP;;;;GAIC,GACD,SAAS,WAAW,GAAG,IAAI;QACzB,IAAI,CAAC,QAAQ;YACX,SAAS;YAET,IAAI,OAAO,SAAS,YAAY,YAAY,GAAG,CAAC,OAAO;YACrD,SAAS;YACX,OAAO;gBACL,QAAQ,KAAK,CAAC,IAAI,iBAAiB,SAAS,QAAQ;gBAEpD,IAAI,OAAO,SAAS,UAAU,YAAY,GAAG,CAAC;YAChD;QACF;QAEA,OAAO,aACH,QAAQ,SAAS,CAAC,IAAI,MAAM,cAC5B,QAAQ,KAAK,CAAC,IAAI,IAAI,EAAE;IAC9B;AACF;AAqBO,SAAS,MAAM,MAAM,EAAE,QAAQ,EAAE,OAAO;IAC7C,OACE,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,WACf,QACA,UACA,SACA,sCACA;AAEJ;AAiBO,SAAS,GAAG,KAAK,EAAE,OAAO;IAC/B,OACE,QAAQ,QACR,OACA,MACA,MACA,+BACA;AAEJ;AAeO,SAAS,YAAY,OAAO;IACjC,OAAO,OAAO,OAAO,MAAM,MAAM,eAAe;AAClD;AAEA;;;;;;;;;;;;;;;CAeC,GACD,sCAAsC;AACtC,SAAS,OAAO,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAE,WAAW;IAC3E,IAAI,CAAC,MAAM;QACT,MAAM,uBAAuB,QACzB,cACA,IAAI,eACF,eAAe,gBACf,QACA,UACA,UACA,CAAC;IAET;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-util-symbol/lib/codes.js"], "sourcesContent": ["/**\n * Character codes.\n *\n * This module is compiled away!\n *\n * micromark works based on character codes.\n * This module contains constants for the ASCII block and the replacement\n * character.\n * A couple of them are handled in a special way, such as the line endings\n * (CR, LF, and CR+LF, commonly known as end-of-line: EOLs), the tab (horizontal\n * tab) and its expansion based on what column it’s at (virtual space),\n * and the end-of-file (eof) character.\n * As values are preprocessed before handling them, the actual characters LF,\n * CR, HT, and NUL (which is present as the replacement character), are\n * guaranteed to not exist.\n *\n * Unicode basic latin block.\n */\nexport const codes = /** @type {const} */ ({\n  carriageReturn: -5,\n  lineFeed: -4,\n  carriageReturnLineFeed: -3,\n  horizontalTab: -2,\n  virtualSpace: -1,\n  eof: null,\n  nul: 0,\n  soh: 1,\n  stx: 2,\n  etx: 3,\n  eot: 4,\n  enq: 5,\n  ack: 6,\n  bel: 7,\n  bs: 8,\n  ht: 9, // `\\t`\n  lf: 10, // `\\n`\n  vt: 11, // `\\v`\n  ff: 12, // `\\f`\n  cr: 13, // `\\r`\n  so: 14,\n  si: 15,\n  dle: 16,\n  dc1: 17,\n  dc2: 18,\n  dc3: 19,\n  dc4: 20,\n  nak: 21,\n  syn: 22,\n  etb: 23,\n  can: 24,\n  em: 25,\n  sub: 26,\n  esc: 27,\n  fs: 28,\n  gs: 29,\n  rs: 30,\n  us: 31,\n  space: 32,\n  exclamationMark: 33, // `!`\n  quotationMark: 34, // `\"`\n  numberSign: 35, // `#`\n  dollarSign: 36, // `$`\n  percentSign: 37, // `%`\n  ampersand: 38, // `&`\n  apostrophe: 39, // `'`\n  leftParenthesis: 40, // `(`\n  rightParenthesis: 41, // `)`\n  asterisk: 42, // `*`\n  plusSign: 43, // `+`\n  comma: 44, // `,`\n  dash: 45, // `-`\n  dot: 46, // `.`\n  slash: 47, // `/`\n  digit0: 48, // `0`\n  digit1: 49, // `1`\n  digit2: 50, // `2`\n  digit3: 51, // `3`\n  digit4: 52, // `4`\n  digit5: 53, // `5`\n  digit6: 54, // `6`\n  digit7: 55, // `7`\n  digit8: 56, // `8`\n  digit9: 57, // `9`\n  colon: 58, // `:`\n  semicolon: 59, // `;`\n  lessThan: 60, // `<`\n  equalsTo: 61, // `=`\n  greaterThan: 62, // `>`\n  questionMark: 63, // `?`\n  atSign: 64, // `@`\n  uppercaseA: 65, // `A`\n  uppercaseB: 66, // `B`\n  uppercaseC: 67, // `C`\n  uppercaseD: 68, // `D`\n  uppercaseE: 69, // `E`\n  uppercaseF: 70, // `F`\n  uppercaseG: 71, // `G`\n  uppercaseH: 72, // `H`\n  uppercaseI: 73, // `I`\n  uppercaseJ: 74, // `J`\n  uppercaseK: 75, // `K`\n  uppercaseL: 76, // `L`\n  uppercaseM: 77, // `M`\n  uppercaseN: 78, // `N`\n  uppercaseO: 79, // `O`\n  uppercaseP: 80, // `P`\n  uppercaseQ: 81, // `Q`\n  uppercaseR: 82, // `R`\n  uppercaseS: 83, // `S`\n  uppercaseT: 84, // `T`\n  uppercaseU: 85, // `U`\n  uppercaseV: 86, // `V`\n  uppercaseW: 87, // `W`\n  uppercaseX: 88, // `X`\n  uppercaseY: 89, // `Y`\n  uppercaseZ: 90, // `Z`\n  leftSquareBracket: 91, // `[`\n  backslash: 92, // `\\`\n  rightSquareBracket: 93, // `]`\n  caret: 94, // `^`\n  underscore: 95, // `_`\n  graveAccent: 96, // `` ` ``\n  lowercaseA: 97, // `a`\n  lowercaseB: 98, // `b`\n  lowercaseC: 99, // `c`\n  lowercaseD: 100, // `d`\n  lowercaseE: 101, // `e`\n  lowercaseF: 102, // `f`\n  lowercaseG: 103, // `g`\n  lowercaseH: 104, // `h`\n  lowercaseI: 105, // `i`\n  lowercaseJ: 106, // `j`\n  lowercaseK: 107, // `k`\n  lowercaseL: 108, // `l`\n  lowercaseM: 109, // `m`\n  lowercaseN: 110, // `n`\n  lowercaseO: 111, // `o`\n  lowercaseP: 112, // `p`\n  lowercaseQ: 113, // `q`\n  lowercaseR: 114, // `r`\n  lowercaseS: 115, // `s`\n  lowercaseT: 116, // `t`\n  lowercaseU: 117, // `u`\n  lowercaseV: 118, // `v`\n  lowercaseW: 119, // `w`\n  lowercaseX: 120, // `x`\n  lowercaseY: 121, // `y`\n  lowercaseZ: 122, // `z`\n  leftCurlyBrace: 123, // `{`\n  verticalBar: 124, // `|`\n  rightCurlyBrace: 125, // `}`\n  tilde: 126, // `~`\n  del: 127,\n  // Unicode Specials block.\n  byteOrderMarker: 65_279,\n  // Unicode Specials block.\n  replacementCharacter: 65_533 // `�`\n})\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;CAiBC;;;AACM,MAAM,QAA8B;IACzC,gBAAgB,CAAC;IACjB,UAAU,CAAC;IACX,wBAAwB,CAAC;IACzB,eAAe,CAAC;IAChB,cAAc,CAAC;IACf,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,iBAAiB;IACjB,eAAe;IACf,YAAY;IACZ,YAAY;IACZ,aAAa;IACb,WAAW;IACX,YAAY;IACZ,iBAAiB;IACjB,kBAAkB;IAClB,UAAU;IACV,UAAU;IACV,OAAO;IACP,MAAM;IACN,KAAK;IACL,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,WAAW;IACX,UAAU;IACV,UAAU;IACV,aAAa;IACb,cAAc;IACd,QAAQ;IACR,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,mBAAmB;IACnB,WAAW;IACX,oBAAoB;IACpB,OAAO;IACP,YAAY;IACZ,aAAa;IACb,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,gBAAgB;IAChB,aAAa;IACb,iBAAiB;IACjB,OAAO;IACP,KAAK;IACL,0BAA0B;IAC1B,iBAAiB;IACjB,0BAA0B;IAC1B,sBAAsB,OAAO,MAAM;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-util-character/dev/index.js"], "sourcesContent": ["/**\n * @import {Code} from 'micromark-util-types'\n */\n\nimport {codes} from 'micromark-util-symbol'\n\n/**\n * Check whether the character code represents an ASCII alpha (`a` through `z`,\n * case insensitive).\n *\n * An **ASCII alpha** is an ASCII upper alpha or ASCII lower alpha.\n *\n * An **ASCII upper alpha** is a character in the inclusive range U+0041 (`A`)\n * to U+005A (`Z`).\n *\n * An **ASCII lower alpha** is a character in the inclusive range U+0061 (`a`)\n * to U+007A (`z`).\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiAlpha = regexCheck(/[A-Za-z]/)\n\n/**\n * Check whether the character code represents an ASCII alphanumeric (`a`\n * through `z`, case insensitive, or `0` through `9`).\n *\n * An **ASCII alphanumeric** is an ASCII digit (see `asciiDigit`) or ASCII alpha\n * (see `asciiAlpha`).\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiAlphanumeric = regexCheck(/[\\dA-Za-z]/)\n\n/**\n * Check whether the character code represents an ASCII atext.\n *\n * atext is an ASCII alphanumeric (see `asciiAlphanumeric`), or a character in\n * the inclusive ranges U+0023 NUMBER SIGN (`#`) to U+0027 APOSTROPHE (`'`),\n * U+002A ASTERISK (`*`), U+002B PLUS SIGN (`+`), U+002D DASH (`-`), U+002F\n * SLASH (`/`), U+003D EQUALS TO (`=`), U+003F QUESTION MARK (`?`), U+005E\n * CARET (`^`) to U+0060 GRAVE ACCENT (`` ` ``), or U+007B LEFT CURLY BRACE\n * (`{`) to U+007E TILDE (`~`).\n *\n * See:\n * **\\[RFC5322]**:\n * [Internet Message Format](https://tools.ietf.org/html/rfc5322).\n * P. Resnick.\n * IETF.\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiAtext = regexCheck(/[#-'*+\\--9=?A-Z^-~]/)\n\n/**\n * Check whether a character code is an ASCII control character.\n *\n * An **ASCII control** is a character in the inclusive range U+0000 NULL (NUL)\n * to U+001F (US), or U+007F (DEL).\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function asciiControl(code) {\n  return (\n    // Special whitespace codes (which have negative values), C0 and Control\n    // character DEL\n    code !== null && (code < codes.space || code === codes.del)\n  )\n}\n\n/**\n * Check whether the character code represents an ASCII digit (`0` through `9`).\n *\n * An **ASCII digit** is a character in the inclusive range U+0030 (`0`) to\n * U+0039 (`9`).\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiDigit = regexCheck(/\\d/)\n\n/**\n * Check whether the character code represents an ASCII hex digit (`a` through\n * `f`, case insensitive, or `0` through `9`).\n *\n * An **ASCII hex digit** is an ASCII digit (see `asciiDigit`), ASCII upper hex\n * digit, or an ASCII lower hex digit.\n *\n * An **ASCII upper hex digit** is a character in the inclusive range U+0041\n * (`A`) to U+0046 (`F`).\n *\n * An **ASCII lower hex digit** is a character in the inclusive range U+0061\n * (`a`) to U+0066 (`f`).\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiHexDigit = regexCheck(/[\\dA-Fa-f]/)\n\n/**\n * Check whether the character code represents ASCII punctuation.\n *\n * An **ASCII punctuation** is a character in the inclusive ranges U+0021\n * EXCLAMATION MARK (`!`) to U+002F SLASH (`/`), U+003A COLON (`:`) to U+0040 AT\n * SIGN (`@`), U+005B LEFT SQUARE BRACKET (`[`) to U+0060 GRAVE ACCENT\n * (`` ` ``), or U+007B LEFT CURLY BRACE (`{`) to U+007E TILDE (`~`).\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiPunctuation = regexCheck(/[!-/:-@[-`{-~]/)\n\n/**\n * Check whether a character code is a markdown line ending.\n *\n * A **markdown line ending** is the virtual characters M-0003 CARRIAGE RETURN\n * LINE FEED (CRLF), M-0004 LINE FEED (LF) and M-0005 CARRIAGE RETURN (CR).\n *\n * In micromark, the actual character U+000A LINE FEED (LF) and U+000D CARRIAGE\n * RETURN (CR) are replaced by these virtual characters depending on whether\n * they occurred together.\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function markdownLineEnding(code) {\n  return code !== null && code < codes.horizontalTab\n}\n\n/**\n * Check whether a character code is a markdown line ending (see\n * `markdownLineEnding`) or markdown space (see `markdownSpace`).\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function markdownLineEndingOrSpace(code) {\n  return code !== null && (code < codes.nul || code === codes.space)\n}\n\n/**\n * Check whether a character code is a markdown space.\n *\n * A **markdown space** is the concrete character U+0020 SPACE (SP) and the\n * virtual characters M-0001 VIRTUAL SPACE (VS) and M-0002 HORIZONTAL TAB (HT).\n *\n * In micromark, the actual character U+0009 CHARACTER TABULATION (HT) is\n * replaced by one M-0002 HORIZONTAL TAB (HT) and between 0 and 3 M-0001 VIRTUAL\n * SPACE (VS) characters, depending on the column at which the tab occurred.\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function markdownSpace(code) {\n  return (\n    code === codes.horizontalTab ||\n    code === codes.virtualSpace ||\n    code === codes.space\n  )\n}\n\n// Size note: removing ASCII from the regex and using `asciiPunctuation` here\n// In fact adds to the bundle size.\n/**\n * Check whether the character code represents Unicode punctuation.\n *\n * A **Unicode punctuation** is a character in the Unicode `Pc` (Punctuation,\n * Connector), `Pd` (Punctuation, Dash), `Pe` (Punctuation, Close), `Pf`\n * (Punctuation, Final quote), `Pi` (Punctuation, Initial quote), `Po`\n * (Punctuation, Other), or `Ps` (Punctuation, Open) categories, or an ASCII\n * punctuation (see `asciiPunctuation`).\n *\n * See:\n * **\\[UNICODE]**:\n * [The Unicode Standard](https://www.unicode.org/versions/).\n * Unicode Consortium.\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const unicodePunctuation = regexCheck(/\\p{P}|\\p{S}/u)\n\n/**\n * Check whether the character code represents Unicode whitespace.\n *\n * Note that this does handle micromark specific markdown whitespace characters.\n * See `markdownLineEndingOrSpace` to check that.\n *\n * A **Unicode whitespace** is a character in the Unicode `Zs` (Separator,\n * Space) category, or U+0009 CHARACTER TABULATION (HT), U+000A LINE FEED (LF),\n * U+000C (FF), or U+000D CARRIAGE RETURN (CR) (**\\[UNICODE]**).\n *\n * See:\n * **\\[UNICODE]**:\n * [The Unicode Standard](https://www.unicode.org/versions/).\n * Unicode Consortium.\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const unicodeWhitespace = regexCheck(/\\s/)\n\n/**\n * Create a code check from a regex.\n *\n * @param {RegExp} regex\n *   Expression.\n * @returns {(code: Code) => boolean}\n *   Check.\n */\nfunction regexCheck(regex) {\n  return check\n\n  /**\n   * Check whether a code matches the bound regex.\n   *\n   * @param {Code} code\n   *   Character code.\n   * @returns {boolean}\n   *   Whether the character code matches the bound regex.\n   */\n  function check(code) {\n    return code !== null && code > -1 && regex.test(String.fromCharCode(code))\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;AAED;;AAmBO,MAAM,aAAa,WAAW;AAc9B,MAAM,oBAAoB,WAAW;AAuBrC,MAAM,aAAa,WAAW;AAa9B,SAAS,aAAa,IAAI;IAC/B,OACE,wEAAwE;IACxE,gBAAgB;IAChB,SAAS,QAAQ,CAAC,OAAO,8JAAA,CAAA,QAAK,CAAC,KAAK,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG;AAE9D;AAaO,MAAM,aAAa,WAAW;AAoB9B,MAAM,gBAAgB,WAAW;AAejC,MAAM,mBAAmB,WAAW;AAiBpC,SAAS,mBAAmB,IAAI;IACrC,OAAO,SAAS,QAAQ,OAAO,8JAAA,CAAA,QAAK,CAAC,aAAa;AACpD;AAWO,SAAS,0BAA0B,IAAI;IAC5C,OAAO,SAAS,QAAQ,CAAC,OAAO,8JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK;AACnE;AAiBO,SAAS,cAAc,IAAI;IAChC,OACE,SAAS,8JAAA,CAAA,QAAK,CAAC,aAAa,IAC5B,SAAS,8JAAA,CAAA,QAAK,CAAC,YAAY,IAC3B,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK;AAExB;AAuBO,MAAM,qBAAqB,WAAW;AAsBtC,MAAM,oBAAoB,WAAW;AAE5C;;;;;;;CAOC,GACD,SAAS,WAAW,KAAK;IACvB,OAAO;;IAEP;;;;;;;GAOC,GACD,SAAS,MAAM,IAAI;QACjB,OAAO,SAAS,QAAQ,OAAO,CAAC,KAAK,MAAM,IAAI,CAAC,OAAO,YAAY,CAAC;IACtE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-find-and-replace/node_modules/escape-string-regexp/index.js"], "sourcesContent": ["export default function escapeStringRegexp(string) {\n\tif (typeof string !== 'string') {\n\t\tthrow new TypeError('Expected a string');\n\t}\n\n\t// Escape characters with special meaning either inside or outside character sets.\n\t// Use a simple backslash escape when it’s always valid, and a `\\xnn` escape when the simpler form would be disallowed by Unicode patterns’ stricter grammar.\n\treturn string\n\t\t.replace(/[|\\\\{}()[\\]^$+*?.]/g, '\\\\$&')\n\t\t.replace(/-/g, '\\\\x2d');\n}\n"], "names": [], "mappings": ";;;AAAe,SAAS,mBAAmB,MAAM;IAChD,IAAI,OAAO,WAAW,UAAU;QAC/B,MAAM,IAAI,UAAU;IACrB;IAEA,kFAAkF;IAClF,6JAA6J;IAC7J,OAAO,OACL,OAAO,CAAC,uBAAuB,QAC/B,OAAO,CAAC,MAAM;AACjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 515, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/unist-util-is/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n */\n\n/**\n * @template Fn\n * @template Fallback\n * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate\n */\n\n/**\n * @callback Check\n *   Check that an arbitrary value is a node.\n * @param {unknown} this\n *   The given context.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is a node and passes a test.\n *\n * @typedef {Record<string, unknown> | Node} Props\n *   Object to check for equivalence.\n *\n *   Note: `Node` is included as it is common but is not indexable.\n *\n * @typedef {Array<Props | TestFunction | string> | Props | TestFunction | string | null | undefined} Test\n *   Check for an arbitrary node.\n *\n * @callback TestFunction\n *   Check if a node passes a test.\n * @param {unknown} this\n *   The given context.\n * @param {Node} node\n *   A node.\n * @param {number | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean | undefined | void}\n *   Whether this node passes the test.\n *\n *   Note: `void` is included until TS sees no return as `undefined`.\n */\n\n/**\n * Check if `node` is a `Node` and whether it passes the given test.\n *\n * @param {unknown} node\n *   Thing to check, typically `Node`.\n * @param {Test} test\n *   A check for a specific node.\n * @param {number | null | undefined} index\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} parent\n *   The node’s parent.\n * @param {unknown} context\n *   Context object (`this`) to pass to `test` functions.\n * @returns {boolean}\n *   Whether `node` is a node and passes a test.\n */\nexport const is =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends string>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n   *   (<Condition extends Props>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n   *   (<Condition extends TestFunction>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n   *   ((node?: null | undefined) => false) &\n   *   ((node: unknown, test?: null | undefined, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n   *   ((node: unknown, test?: Test, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function (node, test, index, parent, context) {\n      const check = convert(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!is(parent) || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      return looksLikeANode(node)\n        ? check.call(context, node, index, parent)\n        : false\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param {Test} test\n *   *   when nullish, checks if `node` is a `Node`.\n *   *   when `string`, works like passing `(node) => node.type === test`.\n *   *   when `function` checks if function passed the node is true.\n *   *   when `object`, checks that all keys in test are in node, and that they have (strictly) equal values.\n *   *   when `array`, checks if any one of the subtests pass.\n * @returns {Check}\n *   An assertion.\n */\nexport const convert =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends string>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n   *   (<Condition extends Props>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n   *   (<Condition extends TestFunction>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n   *   ((test?: null | undefined) => (node?: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n   *   ((test?: Test) => Check)\n   * )}\n   */\n  (\n    /**\n     * @param {Test} [test]\n     * @returns {Check}\n     */\n    function (test) {\n      if (test === null || test === undefined) {\n        return ok\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return Array.isArray(test) ? anyFactory(test) : propsFactory(test)\n      }\n\n      if (typeof test === 'string') {\n        return typeFactory(test)\n      }\n\n      throw new Error('Expected function, string, or object as test')\n    }\n  )\n\n/**\n * @param {Array<Props | TestFunction | string>} tests\n * @returns {Check}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<Check>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convert(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @type {TestFunction}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].apply(this, parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn an object into a test for a node with a certain fields.\n *\n * @param {Props} check\n * @returns {Check}\n */\nfunction propsFactory(check) {\n  const checkAsRecord = /** @type {Record<string, unknown>} */ (check)\n\n  return castFactory(all)\n\n  /**\n   * @param {Node} node\n   * @returns {boolean}\n   */\n  function all(node) {\n    const nodeAsRecord = /** @type {Record<string, unknown>} */ (\n      /** @type {unknown} */ (node)\n    )\n\n    /** @type {string} */\n    let key\n\n    for (key in check) {\n      if (nodeAsRecord[key] !== checkAsRecord[key]) return false\n    }\n\n    return true\n  }\n}\n\n/**\n * Turn a string into a test for a node with a certain type.\n *\n * @param {string} check\n * @returns {Check}\n */\nfunction typeFactory(check) {\n  return castFactory(type)\n\n  /**\n   * @param {Node} node\n   */\n  function type(node) {\n    return node && node.type === check\n  }\n}\n\n/**\n * Turn a custom test into a test for a node that passes that test.\n *\n * @param {TestFunction} testFunction\n * @returns {Check}\n */\nfunction castFactory(testFunction) {\n  return check\n\n  /**\n   * @this {unknown}\n   * @type {Check}\n   */\n  function check(value, index, parent) {\n    return Boolean(\n      looksLikeANode(value) &&\n        testFunction.call(\n          this,\n          value,\n          typeof index === 'number' ? index : undefined,\n          parent || undefined\n        )\n    )\n  }\n}\n\nfunction ok() {\n  return true\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Node}\n */\nfunction looksLikeANode(value) {\n  return value !== null && typeof value === 'object' && 'type' in value\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;CAIC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoCC,GAED;;;;;;;;;;;;;;;CAeC;;;;AACM,MAAM,KAaT;;;;;;;KAOC,GACD,sCAAsC;AACtC,SAAU,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO;IAC1C,MAAM,QAAQ,QAAQ;IAEtB,IACE,UAAU,aACV,UAAU,QACV,CAAC,OAAO,UAAU,YAChB,QAAQ,KACR,UAAU,OAAO,iBAAiB,GACpC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,IACE,WAAW,aACX,WAAW,QACX,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,QAAQ,GAChC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,IACE,CAAC,WAAW,aAAa,WAAW,IAAI,MACxC,CAAC,UAAU,aAAa,UAAU,IAAI,GACtC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,eAAe,QAClB,MAAM,IAAI,CAAC,SAAS,MAAM,OAAO,UACjC;AACN;AAqBG,MAAM,UAYT;;;KAGC,GACD,SAAU,IAAI;IACZ,IAAI,SAAS,QAAQ,SAAS,WAAW;QACvC,OAAO;IACT;IAEA,IAAI,OAAO,SAAS,YAAY;QAC9B,OAAO,YAAY;IACrB;IAEA,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,MAAM,OAAO,CAAC,QAAQ,WAAW,QAAQ,aAAa;IAC/D;IAEA,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,YAAY;IACrB;IAEA,MAAM,IAAI,MAAM;AAClB;AAGJ;;;CAGC,GACD,SAAS,WAAW,KAAK;IACvB,yBAAyB,GACzB,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,MAAM,CAAC,MAAM,GAAG,QAAQ,KAAK,CAAC,MAAM;IACtC;IAEA,OAAO,YAAY;;IAEnB;;;GAGC,GACD,SAAS,IAAI,GAAG,UAAU;QACxB,IAAI,QAAQ,CAAC;QAEb,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;YAC9B,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,aAAa,OAAO;QACpD;QAEA,OAAO;IACT;AACF;AAEA;;;;;CAKC,GACD,SAAS,aAAa,KAAK;IACzB,MAAM,gBAAwD;IAE9D,OAAO,YAAY;;IAEnB;;;GAGC,GACD,SAAS,IAAI,IAAI;QACf,MAAM,eACoB;QAG1B,mBAAmB,GACnB,IAAI;QAEJ,IAAK,OAAO,MAAO;YACjB,IAAI,YAAY,CAAC,IAAI,KAAK,aAAa,CAAC,IAAI,EAAE,OAAO;QACvD;QAEA,OAAO;IACT;AACF;AAEA;;;;;CAKC,GACD,SAAS,YAAY,KAAK;IACxB,OAAO,YAAY;;IAEnB;;GAEC,GACD,SAAS,KAAK,IAAI;QAChB,OAAO,QAAQ,KAAK,IAAI,KAAK;IAC/B;AACF;AAEA;;;;;CAKC,GACD,SAAS,YAAY,YAAY;IAC/B,OAAO;;IAEP;;;GAGC,GACD,SAAS,MAAM,KAAK,EAAE,KAAK,EAAE,MAAM;QACjC,OAAO,QACL,eAAe,UACb,aAAa,IAAI,CACf,IAAI,EACJ,OACA,OAAO,UAAU,WAAW,QAAQ,WACpC,UAAU;IAGlB;AACF;AAEA,SAAS;IACP,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,eAAe,KAAK;IAC3B,OAAO,UAAU,QAAQ,OAAO,UAAU,YAAY,UAAU;AAClE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 703, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/unist-util-visit-parents/lib/color.js"], "sourcesContent": ["/**\n * @param {string} d\n * @returns {string}\n */\nexport function color(d) {\n  return d\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AACM,SAAS,MAAM,CAAC;IACrB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 718, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/unist-util-visit-parents/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} UnistNode\n * @typedef {import('unist').Parent} UnistParent\n */\n\n/**\n * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test\n *   Test from `unist-util-is`.\n *\n *   Note: we have remove and add `undefined`, because otherwise when generating\n *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,\n *   which doesn’t work when publishing on npm.\n */\n\n/**\n * @typedef {(\n *   Fn extends (value: any) => value is infer Thing\n *   ? Thing\n *   : Fallback\n * )} Predicate\n *   Get the value of a type guard `Fn`.\n * @template Fn\n *   Value; typically function that is a type guard (such as `(x): x is Y`).\n * @template Fallback\n *   Value to yield if `Fn` is not a type guard.\n */\n\n/**\n * @typedef {(\n *   Check extends null | undefined // No test.\n *   ? Value\n *   : Value extends {type: Check} // String (type) test.\n *   ? Value\n *   : Value extends Check // Partial test.\n *   ? Value\n *   : Check extends Function // Function test.\n *   ? Predicate<Check, Value> extends Value\n *     ? Predicate<Check, Value>\n *     : never\n *   : never // Some other test?\n * )} MatchesOne\n *   Check whether a node matches a primitive check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test, but not arrays.\n */\n\n/**\n * @typedef {(\n *   Check extends Array<any>\n *   ? MatchesOne<Value, Check[keyof Check]>\n *   : MatchesOne<Value, Check>\n * )} Matches\n *   Check whether a node matches a check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test.\n */\n\n/**\n * @typedef {0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10} Uint\n *   Number; capped reasonably.\n */\n\n/**\n * @typedef {I extends 0 ? 1 : I extends 1 ? 2 : I extends 2 ? 3 : I extends 3 ? 4 : I extends 4 ? 5 : I extends 5 ? 6 : I extends 6 ? 7 : I extends 7 ? 8 : I extends 8 ? 9 : 10} Increment\n *   Increment a number in the type system.\n * @template {Uint} [I=0]\n *   Index.\n */\n\n/**\n * @typedef {(\n *   Node extends UnistParent\n *   ? Node extends {children: Array<infer Children>}\n *     ? Child extends Children ? Node : never\n *     : never\n *   : never\n * )} InternalParent\n *   Collect nodes that can be parents of `Child`.\n * @template {UnistNode} Node\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {InternalParent<InclusiveDescendant<Tree>, Child>} Parent\n *   Collect nodes in `Tree` that can be parents of `Child`.\n * @template {UnistNode} Tree\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {(\n *   Depth extends Max\n *   ? never\n *   :\n *     | InternalParent<Node, Child>\n *     | InternalAncestor<Node, InternalParent<Node, Child>, Max, Increment<Depth>>\n * )} InternalAncestor\n *   Collect nodes in `Tree` that can be ancestors of `Child`.\n * @template {UnistNode} Node\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n * @template {Uint} [Max=10]\n *   Max; searches up to this depth.\n * @template {Uint} [Depth=0]\n *   Current depth.\n */\n\n/**\n * @typedef {InternalAncestor<InclusiveDescendant<Tree>, Child>} Ancestor\n *   Collect nodes in `Tree` that can be ancestors of `Child`.\n * @template {UnistNode} Tree\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {(\n *   Tree extends UnistParent\n *     ? Depth extends Max\n *       ? Tree\n *       : Tree | InclusiveDescendant<Tree['children'][number], Max, Increment<Depth>>\n *     : Tree\n * )} InclusiveDescendant\n *   Collect all (inclusive) descendants of `Tree`.\n *\n *   > 👉 **Note**: for performance reasons, this seems to be the fastest way to\n *   > recurse without actually running into an infinite loop, which the\n *   > previous version did.\n *   >\n *   > Practically, a max of `2` is typically enough assuming a `Root` is\n *   > passed, but it doesn’t improve performance.\n *   > It gets higher with `List > ListItem > Table > TableRow > TableCell`.\n *   > Using up to `10` doesn’t hurt or help either.\n * @template {UnistNode} Tree\n *   Tree type.\n * @template {Uint} [Max=10]\n *   Max; searches up to this depth.\n * @template {Uint} [Depth=0]\n *   Current depth.\n */\n\n/**\n * @typedef {'skip' | boolean} Action\n *   Union of the action types.\n *\n * @typedef {number} Index\n *   Move to the sibling at `index` next (after node itself is completely\n *   traversed).\n *\n *   Useful if mutating the tree, such as removing the node the visitor is\n *   currently on, or any of its previous siblings.\n *   Results less than 0 or greater than or equal to `children.length` stop\n *   traversing the parent.\n *\n * @typedef {[(Action | null | undefined | void)?, (Index | null | undefined)?]} ActionTuple\n *   List with one or two values, the first an action, the second an index.\n *\n * @typedef {Action | ActionTuple | Index | null | undefined | void} VisitorResult\n *   Any value that can be returned from a visitor.\n */\n\n/**\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform the parent of node (the last of `ancestors`).\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of an ancestor still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Array<VisitedParents>} ancestors\n *   Ancestors of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n * @template {UnistNode} [Visited=UnistNode]\n *   Visited node type.\n * @template {UnistParent} [VisitedParents=UnistParent]\n *   Ancestor type.\n */\n\n/**\n * @typedef {Visitor<Matches<InclusiveDescendant<Tree>, Check>, Ancestor<Tree, Matches<InclusiveDescendant<Tree>, Check>>>} BuildVisitor\n *   Build a typed `Visitor` function from a tree and a test.\n *\n *   It will infer which values are passed as `node` and which as `parents`.\n * @template {UnistNode} [Tree=UnistNode]\n *   Tree type.\n * @template {Test} [Check=Test]\n *   Test type.\n */\n\nimport {convert} from 'unist-util-is'\nimport {color} from 'unist-util-visit-parents/do-not-use-color'\n\n/** @type {Readonly<ActionTuple>} */\nconst empty = []\n\n/**\n * Continue traversing as normal.\n */\nexport const CONTINUE = true\n\n/**\n * Stop traversing immediately.\n */\nexport const EXIT = false\n\n/**\n * Do not traverse this node’s children.\n */\nexport const SKIP = 'skip'\n\n/**\n * Visit nodes, with ancestral information.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @overload\n * @param {Tree} tree\n * @param {Check} check\n * @param {BuildVisitor<Tree, Check>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {undefined}\n *\n * @overload\n * @param {Tree} tree\n * @param {BuildVisitor<Tree>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {undefined}\n *\n * @param {UnistNode} tree\n *   Tree to traverse.\n * @param {Visitor | Test} test\n *   `unist-util-is`-compatible test\n * @param {Visitor | boolean | null | undefined} [visitor]\n *   Handle each node.\n * @param {boolean | null | undefined} [reverse]\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns {undefined}\n *   Nothing.\n *\n * @template {UnistNode} Tree\n *   Node type.\n * @template {Test} Check\n *   `unist-util-is`-compatible test.\n */\nexport function visitParents(tree, test, visitor, reverse) {\n  /** @type {Test} */\n  let check\n\n  if (typeof test === 'function' && typeof visitor !== 'function') {\n    reverse = visitor\n    // @ts-expect-error no visitor given, so `visitor` is test.\n    visitor = test\n  } else {\n    // @ts-expect-error visitor given, so `test` isn’t a visitor.\n    check = test\n  }\n\n  const is = convert(check)\n  const step = reverse ? -1 : 1\n\n  factory(tree, undefined, [])()\n\n  /**\n   * @param {UnistNode} node\n   * @param {number | undefined} index\n   * @param {Array<UnistParent>} parents\n   */\n  function factory(node, index, parents) {\n    const value = /** @type {Record<string, unknown>} */ (\n      node && typeof node === 'object' ? node : {}\n    )\n\n    if (typeof value.type === 'string') {\n      const name =\n        // `hast`\n        typeof value.tagName === 'string'\n          ? value.tagName\n          : // `xast`\n          typeof value.name === 'string'\n          ? value.name\n          : undefined\n\n      Object.defineProperty(visit, 'name', {\n        value:\n          'node (' + color(node.type + (name ? '<' + name + '>' : '')) + ')'\n      })\n    }\n\n    return visit\n\n    function visit() {\n      /** @type {Readonly<ActionTuple>} */\n      let result = empty\n      /** @type {Readonly<ActionTuple>} */\n      let subresult\n      /** @type {number} */\n      let offset\n      /** @type {Array<UnistParent>} */\n      let grandparents\n\n      if (!test || is(node, index, parents[parents.length - 1] || undefined)) {\n        // @ts-expect-error: `visitor` is now a visitor.\n        result = toResult(visitor(node, parents))\n\n        if (result[0] === EXIT) {\n          return result\n        }\n      }\n\n      if ('children' in node && node.children) {\n        const nodeAsParent = /** @type {UnistParent} */ (node)\n\n        if (nodeAsParent.children && result[0] !== SKIP) {\n          offset = (reverse ? nodeAsParent.children.length : -1) + step\n          grandparents = parents.concat(nodeAsParent)\n\n          while (offset > -1 && offset < nodeAsParent.children.length) {\n            const child = nodeAsParent.children[offset]\n\n            subresult = factory(child, offset, grandparents)()\n\n            if (subresult[0] === EXIT) {\n              return subresult\n            }\n\n            offset =\n              typeof subresult[1] === 'number' ? subresult[1] : offset + step\n          }\n        }\n      }\n\n      return result\n    }\n  }\n}\n\n/**\n * Turn a return value into a clean result.\n *\n * @param {VisitorResult} value\n *   Valid return values from visitors.\n * @returns {Readonly<ActionTuple>}\n *   Clean result.\n */\nfunction toResult(value) {\n  if (Array.isArray(value)) {\n    return value\n  }\n\n  if (typeof value === 'number') {\n    return [CONTINUE, value]\n  }\n\n  return value === null || value === undefined ? empty : [value]\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;CAWC,GAED;;;;;;;;;;;;;;;;;;;CAmBC,GAED;;;;;;;;;;;CAWC,GAED;;;CAGC,GAED;;;;;CAKC,GAED;;;;;;;;;;;;;CAaC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;;;;;;;CAiBC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GAED;;;;;;;;;;;;;;;;;;CAkBC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmCC,GAED;;;;;;;;;CASC;;;;;;AAED;AACA;;;AAEA,kCAAkC,GAClC,MAAM,QAAQ,EAAE;AAKT,MAAM,WAAW;AAKjB,MAAM,OAAO;AAKb,MAAM,OAAO;AAiDb,SAAS,aAAa,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO;IACvD,iBAAiB,GACjB,IAAI;IAEJ,IAAI,OAAO,SAAS,cAAc,OAAO,YAAY,YAAY;QAC/D,UAAU;QACV,2DAA2D;QAC3D,UAAU;IACZ,OAAO;QACL,6DAA6D;QAC7D,QAAQ;IACV;IAEA,MAAM,KAAK,CAAA,GAAA,sJAAA,CAAA,UAAO,AAAD,EAAE;IACnB,MAAM,OAAO,UAAU,CAAC,IAAI;IAE5B,QAAQ,MAAM,WAAW,EAAE;IAE3B;;;;GAIC,GACD,SAAS,QAAQ,IAAI,EAAE,KAAK,EAAE,OAAO;QACnC,MAAM,QACJ,QAAQ,OAAO,SAAS,WAAW,OAAO,CAAC;QAG7C,IAAI,OAAO,MAAM,IAAI,KAAK,UAAU;YAClC,MAAM,OACJ,SAAS;YACT,OAAO,MAAM,OAAO,KAAK,WACrB,MAAM,OAAO,GAEf,OAAO,MAAM,IAAI,KAAK,WACpB,MAAM,IAAI,GACV;YAEN,OAAO,cAAc,CAAC,OAAO,QAAQ;gBACnC,OACE,WAAW,CAAA,GAAA,oKAAA,CAAA,QAAK,AAAD,EAAE,KAAK,IAAI,GAAG,CAAC,OAAO,MAAM,OAAO,MAAM,EAAE,KAAK;YACnE;QACF;QAEA,OAAO;;QAEP,SAAS;YACP,kCAAkC,GAClC,IAAI,SAAS;YACb,kCAAkC,GAClC,IAAI;YACJ,mBAAmB,GACnB,IAAI;YACJ,+BAA+B,GAC/B,IAAI;YAEJ,IAAI,CAAC,QAAQ,GAAG,MAAM,OAAO,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,IAAI,YAAY;gBACtE,gDAAgD;gBAChD,SAAS,SAAS,QAAQ,MAAM;gBAEhC,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM;oBACtB,OAAO;gBACT;YACF;YAEA,IAAI,cAAc,QAAQ,KAAK,QAAQ,EAAE;gBACvC,MAAM,eAA2C;gBAEjD,IAAI,aAAa,QAAQ,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM;oBAC/C,SAAS,CAAC,UAAU,aAAa,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI;oBACzD,eAAe,QAAQ,MAAM,CAAC;oBAE9B,MAAO,SAAS,CAAC,KAAK,SAAS,aAAa,QAAQ,CAAC,MAAM,CAAE;wBAC3D,MAAM,QAAQ,aAAa,QAAQ,CAAC,OAAO;wBAE3C,YAAY,QAAQ,OAAO,QAAQ;wBAEnC,IAAI,SAAS,CAAC,EAAE,KAAK,MAAM;4BACzB,OAAO;wBACT;wBAEA,SACE,OAAO,SAAS,CAAC,EAAE,KAAK,WAAW,SAAS,CAAC,EAAE,GAAG,SAAS;oBAC/D;gBACF;YACF;YAEA,OAAO;QACT;IACF;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,SAAS,KAAK;IACrB,IAAI,MAAM,OAAO,CAAC,QAAQ;QACxB,OAAO;IACT;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;YAAC;YAAU;SAAM;IAC1B;IAEA,OAAO,UAAU,QAAQ,UAAU,YAAY,QAAQ;QAAC;KAAM;AAChE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1007, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-find-and-replace/lib/index.js"], "sourcesContent": ["/**\n * @import {<PERSON><PERSON>, <PERSON>, PhrasingContent, <PERSON>, Text} from 'mdast'\n * @import {BuildVisitor, Test, VisitorResult} from 'unist-util-visit-parents'\n */\n\n/**\n * @typedef RegExpMatchObject\n *   Info on the match.\n * @property {number} index\n *   The index of the search at which the result was found.\n * @property {string} input\n *   A copy of the search string in the text node.\n * @property {[...Array<Parents>, Text]} stack\n *   All ancestors of the text node, where the last node is the text itself.\n *\n * @typedef {RegExp | string} Find\n *   Pattern to find.\n *\n *   Strings are escaped and then turned into global expressions.\n *\n * @typedef {Array<FindAndReplaceTuple>} FindAndReplaceList\n *   Several find and replaces, in array form.\n *\n * @typedef {[Find, Replace?]} FindAndReplaceTuple\n *   Find and replace in tuple form.\n *\n * @typedef {ReplaceFunction | string | null | undefined} Replace\n *   Thing to replace with.\n *\n * @callback ReplaceFunction\n *   Callback called when a search matches.\n * @param {...any} parameters\n *   The parameters are the result of corresponding search expression:\n *\n *   * `value` (`string`) — whole match\n *   * `...capture` (`Array<string>`) — matches from regex capture groups\n *   * `match` (`RegExpMatchObject`) — info on the match\n * @returns {Array<PhrasingContent> | PhrasingContent | string | false | null | undefined}\n *   Thing to replace with.\n *\n *   * when `null`, `undefined`, `''`, remove the match\n *   * …or when `false`, do not replace at all\n *   * …or when `string`, replace with a text node of that value\n *   * …or when `Node` or `Array<Node>`, replace with those nodes\n *\n * @typedef {[RegExp, ReplaceFunction]} Pair\n *   Normalized find and replace.\n *\n * @typedef {Array<Pair>} Pairs\n *   All find and replaced.\n *\n * @typedef Options\n *   Configuration.\n * @property {Test | null | undefined} [ignore]\n *   Test for which nodes to ignore (optional).\n */\n\nimport escape from 'escape-string-regexp'\nimport {visitParents} from 'unist-util-visit-parents'\nimport {convert} from 'unist-util-is'\n\n/**\n * Find patterns in a tree and replace them.\n *\n * The algorithm searches the tree in *preorder* for complete values in `Text`\n * nodes.\n * Partial matches are not supported.\n *\n * @param {Nodes} tree\n *   Tree to change.\n * @param {FindAndReplaceList | FindAndReplaceTuple} list\n *   Patterns to find.\n * @param {Options | null | undefined} [options]\n *   Configuration (when `find` is not `Find`).\n * @returns {undefined}\n *   Nothing.\n */\nexport function findAndReplace(tree, list, options) {\n  const settings = options || {}\n  const ignored = convert(settings.ignore || [])\n  const pairs = toPairs(list)\n  let pairIndex = -1\n\n  while (++pairIndex < pairs.length) {\n    visitParents(tree, 'text', visitor)\n  }\n\n  /** @type {BuildVisitor<Root, 'text'>} */\n  function visitor(node, parents) {\n    let index = -1\n    /** @type {Parents | undefined} */\n    let grandparent\n\n    while (++index < parents.length) {\n      const parent = parents[index]\n      /** @type {Array<Nodes> | undefined} */\n      const siblings = grandparent ? grandparent.children : undefined\n\n      if (\n        ignored(\n          parent,\n          siblings ? siblings.indexOf(parent) : undefined,\n          grandparent\n        )\n      ) {\n        return\n      }\n\n      grandparent = parent\n    }\n\n    if (grandparent) {\n      return handler(node, parents)\n    }\n  }\n\n  /**\n   * Handle a text node which is not in an ignored parent.\n   *\n   * @param {Text} node\n   *   Text node.\n   * @param {Array<Parents>} parents\n   *   Parents.\n   * @returns {VisitorResult}\n   *   Result.\n   */\n  function handler(node, parents) {\n    const parent = parents[parents.length - 1]\n    const find = pairs[pairIndex][0]\n    const replace = pairs[pairIndex][1]\n    let start = 0\n    /** @type {Array<Nodes>} */\n    const siblings = parent.children\n    const index = siblings.indexOf(node)\n    let change = false\n    /** @type {Array<PhrasingContent>} */\n    let nodes = []\n\n    find.lastIndex = 0\n\n    let match = find.exec(node.value)\n\n    while (match) {\n      const position = match.index\n      /** @type {RegExpMatchObject} */\n      const matchObject = {\n        index: match.index,\n        input: match.input,\n        stack: [...parents, node]\n      }\n      let value = replace(...match, matchObject)\n\n      if (typeof value === 'string') {\n        value = value.length > 0 ? {type: 'text', value} : undefined\n      }\n\n      // It wasn’t a match after all.\n      if (value === false) {\n        // False acts as if there was no match.\n        // So we need to reset `lastIndex`, which currently being at the end of\n        // the current match, to the beginning.\n        find.lastIndex = position + 1\n      } else {\n        if (start !== position) {\n          nodes.push({\n            type: 'text',\n            value: node.value.slice(start, position)\n          })\n        }\n\n        if (Array.isArray(value)) {\n          nodes.push(...value)\n        } else if (value) {\n          nodes.push(value)\n        }\n\n        start = position + match[0].length\n        change = true\n      }\n\n      if (!find.global) {\n        break\n      }\n\n      match = find.exec(node.value)\n    }\n\n    if (change) {\n      if (start < node.value.length) {\n        nodes.push({type: 'text', value: node.value.slice(start)})\n      }\n\n      parent.children.splice(index, 1, ...nodes)\n    } else {\n      nodes = [node]\n    }\n\n    return index + nodes.length\n  }\n}\n\n/**\n * Turn a tuple or a list of tuples into pairs.\n *\n * @param {FindAndReplaceList | FindAndReplaceTuple} tupleOrList\n *   Schema.\n * @returns {Pairs}\n *   Clean pairs.\n */\nfunction toPairs(tupleOrList) {\n  /** @type {Pairs} */\n  const result = []\n\n  if (!Array.isArray(tupleOrList)) {\n    throw new TypeError('Expected find and replace tuple or list of tuples')\n  }\n\n  /** @type {FindAndReplaceList} */\n  // @ts-expect-error: correct.\n  const list =\n    !tupleOrList[0] || Array.isArray(tupleOrList[0])\n      ? tupleOrList\n      : [tupleOrList]\n\n  let index = -1\n\n  while (++index < list.length) {\n    const tuple = list[index]\n    result.push([toExpression(tuple[0]), toFunction(tuple[1])])\n  }\n\n  return result\n}\n\n/**\n * Turn a find into an expression.\n *\n * @param {Find} find\n *   Find.\n * @returns {RegExp}\n *   Expression.\n */\nfunction toExpression(find) {\n  return typeof find === 'string' ? new RegExp(escape(find), 'g') : find\n}\n\n/**\n * Turn a replace into a function.\n *\n * @param {Replace} replace\n *   Replace.\n * @returns {ReplaceFunction}\n *   Function.\n */\nfunction toFunction(replace) {\n  return typeof replace === 'function'\n    ? replace\n    : function () {\n        return replace\n      }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAkDC;;;AAED;AACA;AACA;;;;AAkBO,SAAS,eAAe,IAAI,EAAE,IAAI,EAAE,OAAO;IAChD,MAAM,WAAW,WAAW,CAAC;IAC7B,MAAM,UAAU,CAAA,GAAA,sJAAA,CAAA,UAAO,AAAD,EAAE,SAAS,MAAM,IAAI,EAAE;IAC7C,MAAM,QAAQ,QAAQ;IACtB,IAAI,YAAY,CAAC;IAEjB,MAAO,EAAE,YAAY,MAAM,MAAM,CAAE;QACjC,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,EAAE,MAAM,QAAQ;IAC7B;IAEA,uCAAuC,GACvC,SAAS,QAAQ,IAAI,EAAE,OAAO;QAC5B,IAAI,QAAQ,CAAC;QACb,gCAAgC,GAChC,IAAI;QAEJ,MAAO,EAAE,QAAQ,QAAQ,MAAM,CAAE;YAC/B,MAAM,SAAS,OAAO,CAAC,MAAM;YAC7B,qCAAqC,GACrC,MAAM,WAAW,cAAc,YAAY,QAAQ,GAAG;YAEtD,IACE,QACE,QACA,WAAW,SAAS,OAAO,CAAC,UAAU,WACtC,cAEF;gBACA;YACF;YAEA,cAAc;QAChB;QAEA,IAAI,aAAa;YACf,OAAO,QAAQ,MAAM;QACvB;IACF;IAEA;;;;;;;;;GASC,GACD,SAAS,QAAQ,IAAI,EAAE,OAAO;QAC5B,MAAM,SAAS,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;QAC1C,MAAM,OAAO,KAAK,CAAC,UAAU,CAAC,EAAE;QAChC,MAAM,UAAU,KAAK,CAAC,UAAU,CAAC,EAAE;QACnC,IAAI,QAAQ;QACZ,yBAAyB,GACzB,MAAM,WAAW,OAAO,QAAQ;QAChC,MAAM,QAAQ,SAAS,OAAO,CAAC;QAC/B,IAAI,SAAS;QACb,mCAAmC,GACnC,IAAI,QAAQ,EAAE;QAEd,KAAK,SAAS,GAAG;QAEjB,IAAI,QAAQ,KAAK,IAAI,CAAC,KAAK,KAAK;QAEhC,MAAO,MAAO;YACZ,MAAM,WAAW,MAAM,KAAK;YAC5B,8BAA8B,GAC9B,MAAM,cAAc;gBAClB,OAAO,MAAM,KAAK;gBAClB,OAAO,MAAM,KAAK;gBAClB,OAAO;uBAAI;oBAAS;iBAAK;YAC3B;YACA,IAAI,QAAQ,WAAW,OAAO;YAE9B,IAAI,OAAO,UAAU,UAAU;gBAC7B,QAAQ,MAAM,MAAM,GAAG,IAAI;oBAAC,MAAM;oBAAQ;gBAAK,IAAI;YACrD;YAEA,+BAA+B;YAC/B,IAAI,UAAU,OAAO;gBACnB,uCAAuC;gBACvC,uEAAuE;gBACvE,uCAAuC;gBACvC,KAAK,SAAS,GAAG,WAAW;YAC9B,OAAO;gBACL,IAAI,UAAU,UAAU;oBACtB,MAAM,IAAI,CAAC;wBACT,MAAM;wBACN,OAAO,KAAK,KAAK,CAAC,KAAK,CAAC,OAAO;oBACjC;gBACF;gBAEA,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,IAAI,IAAI;gBAChB,OAAO,IAAI,OAAO;oBAChB,MAAM,IAAI,CAAC;gBACb;gBAEA,QAAQ,WAAW,KAAK,CAAC,EAAE,CAAC,MAAM;gBAClC,SAAS;YACX;YAEA,IAAI,CAAC,KAAK,MAAM,EAAE;gBAChB;YACF;YAEA,QAAQ,KAAK,IAAI,CAAC,KAAK,KAAK;QAC9B;QAEA,IAAI,QAAQ;YACV,IAAI,QAAQ,KAAK,KAAK,CAAC,MAAM,EAAE;gBAC7B,MAAM,IAAI,CAAC;oBAAC,MAAM;oBAAQ,OAAO,KAAK,KAAK,CAAC,KAAK,CAAC;gBAAM;YAC1D;YAEA,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,MAAM;QACtC,OAAO;YACL,QAAQ;gBAAC;aAAK;QAChB;QAEA,OAAO,QAAQ,MAAM,MAAM;IAC7B;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,QAAQ,WAAW;IAC1B,kBAAkB,GAClB,MAAM,SAAS,EAAE;IAEjB,IAAI,CAAC,MAAM,OAAO,CAAC,cAAc;QAC/B,MAAM,IAAI,UAAU;IACtB;IAEA,+BAA+B,GAC/B,6BAA6B;IAC7B,MAAM,OACJ,CAAC,WAAW,CAAC,EAAE,IAAI,MAAM,OAAO,CAAC,WAAW,CAAC,EAAE,IAC3C,cACA;QAAC;KAAY;IAEnB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,KAAK,MAAM,CAAE;QAC5B,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,OAAO,IAAI,CAAC;YAAC,aAAa,KAAK,CAAC,EAAE;YAAG,WAAW,KAAK,CAAC,EAAE;SAAE;IAC5D;IAEA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,aAAa,IAAI;IACxB,OAAO,OAAO,SAAS,WAAW,IAAI,OAAO,CAAA,GAAA,iNAAA,CAAA,UAAM,AAAD,EAAE,OAAO,OAAO;AACpE;AAEA;;;;;;;CAOC,GACD,SAAS,WAAW,OAAO;IACzB,OAAO,OAAO,YAAY,aACtB,UACA;QACE,OAAO;IACT;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1225, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-gfm-autolink-literal/lib/index.js"], "sourcesContent": ["/**\n * @import {RegExpMatchObject, ReplaceFunction} from 'mdast-util-find-and-replace'\n * @import {CompileContext, Extension as FromMarkdownExtension, Handle as FromMarkdownHandle, Transform as FromMarkdownTransform} from 'mdast-util-from-markdown'\n * @import {ConstructName, Options as ToMarkdownExtension} from 'mdast-util-to-markdown'\n * @import {Link, PhrasingContent} from 'mdast'\n */\n\nimport {ccount} from 'ccount'\nimport {ok as assert} from 'devlop'\nimport {unicodePunctuation, unicodeWhitespace} from 'micromark-util-character'\nimport {findAndReplace} from 'mdast-util-find-and-replace'\n\n/** @type {ConstructName} */\nconst inConstruct = 'phrasing'\n/** @type {Array<ConstructName>} */\nconst notInConstruct = ['autolink', 'link', 'image', 'label']\n\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM autolink\n * literals in markdown.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable GFM autolink literals.\n */\nexport function gfmAutolinkLiteralFromMarkdown() {\n  return {\n    transforms: [transformGfmAutolinkLiterals],\n    enter: {\n      literalAutolink: enterLiteralAutolink,\n      literalAutolinkEmail: enterLiteralAutolinkValue,\n      literalAutolinkHttp: enterLiteralAutolinkValue,\n      literalAutolinkWww: enterLiteralAutolinkValue\n    },\n    exit: {\n      literalAutolink: exitLiteralAutolink,\n      literalAutolinkEmail: exitLiteralAutolinkEmail,\n      literalAutolinkHttp: exitLiteralAutolinkHttp,\n      literalAutolinkWww: exitLiteralAutolinkWww\n    }\n  }\n}\n\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM autolink\n * literals in markdown.\n *\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable GFM autolink literals.\n */\nexport function gfmAutolinkLiteralToMarkdown() {\n  return {\n    unsafe: [\n      {\n        character: '@',\n        before: '[+\\\\-.\\\\w]',\n        after: '[\\\\-.\\\\w]',\n        inConstruct,\n        notInConstruct\n      },\n      {\n        character: '.',\n        before: '[Ww]',\n        after: '[\\\\-.\\\\w]',\n        inConstruct,\n        notInConstruct\n      },\n      {\n        character: ':',\n        before: '[ps]',\n        after: '\\\\/',\n        inConstruct,\n        notInConstruct\n      }\n    ]\n  }\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterLiteralAutolink(token) {\n  this.enter({type: 'link', title: null, url: '', children: []}, token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterLiteralAutolinkValue(token) {\n  this.config.enter.autolinkProtocol.call(this, token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitLiteralAutolinkHttp(token) {\n  this.config.exit.autolinkProtocol.call(this, token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitLiteralAutolinkWww(token) {\n  this.config.exit.data.call(this, token)\n  const node = this.stack[this.stack.length - 1]\n  assert(node.type === 'link')\n  node.url = 'http://' + this.sliceSerialize(token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitLiteralAutolinkEmail(token) {\n  this.config.exit.autolinkEmail.call(this, token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitLiteralAutolink(token) {\n  this.exit(token)\n}\n\n/** @type {FromMarkdownTransform} */\nfunction transformGfmAutolinkLiterals(tree) {\n  findAndReplace(\n    tree,\n    [\n      [/(https?:\\/\\/|www(?=\\.))([-.\\w]+)([^ \\t\\r\\n]*)/gi, findUrl],\n      [/(?<=^|\\s|\\p{P}|\\p{S})([-.\\w+]+)@([-\\w]+(?:\\.[-\\w]+)+)/gu, findEmail]\n    ],\n    {ignore: ['link', 'linkReference']}\n  )\n}\n\n/**\n * @type {ReplaceFunction}\n * @param {string} _\n * @param {string} protocol\n * @param {string} domain\n * @param {string} path\n * @param {RegExpMatchObject} match\n * @returns {Array<PhrasingContent> | Link | false}\n */\n// eslint-disable-next-line max-params\nfunction findUrl(_, protocol, domain, path, match) {\n  let prefix = ''\n\n  // Not an expected previous character.\n  if (!previous(match)) {\n    return false\n  }\n\n  // Treat `www` as part of the domain.\n  if (/^w/i.test(protocol)) {\n    domain = protocol + domain\n    protocol = ''\n    prefix = 'http://'\n  }\n\n  if (!isCorrectDomain(domain)) {\n    return false\n  }\n\n  const parts = splitUrl(domain + path)\n\n  if (!parts[0]) return false\n\n  /** @type {Link} */\n  const result = {\n    type: 'link',\n    title: null,\n    url: prefix + protocol + parts[0],\n    children: [{type: 'text', value: protocol + parts[0]}]\n  }\n\n  if (parts[1]) {\n    return [result, {type: 'text', value: parts[1]}]\n  }\n\n  return result\n}\n\n/**\n * @type {ReplaceFunction}\n * @param {string} _\n * @param {string} atext\n * @param {string} label\n * @param {RegExpMatchObject} match\n * @returns {Link | false}\n */\nfunction findEmail(_, atext, label, match) {\n  if (\n    // Not an expected previous character.\n    !previous(match, true) ||\n    // Label ends in not allowed character.\n    /[-\\d_]$/.test(label)\n  ) {\n    return false\n  }\n\n  return {\n    type: 'link',\n    title: null,\n    url: 'mailto:' + atext + '@' + label,\n    children: [{type: 'text', value: atext + '@' + label}]\n  }\n}\n\n/**\n * @param {string} domain\n * @returns {boolean}\n */\nfunction isCorrectDomain(domain) {\n  const parts = domain.split('.')\n\n  if (\n    parts.length < 2 ||\n    (parts[parts.length - 1] &&\n      (/_/.test(parts[parts.length - 1]) ||\n        !/[a-zA-Z\\d]/.test(parts[parts.length - 1]))) ||\n    (parts[parts.length - 2] &&\n      (/_/.test(parts[parts.length - 2]) ||\n        !/[a-zA-Z\\d]/.test(parts[parts.length - 2])))\n  ) {\n    return false\n  }\n\n  return true\n}\n\n/**\n * @param {string} url\n * @returns {[string, string | undefined]}\n */\nfunction splitUrl(url) {\n  const trailExec = /[!\"&'),.:;<>?\\]}]+$/.exec(url)\n\n  if (!trailExec) {\n    return [url, undefined]\n  }\n\n  url = url.slice(0, trailExec.index)\n\n  let trail = trailExec[0]\n  let closingParenIndex = trail.indexOf(')')\n  const openingParens = ccount(url, '(')\n  let closingParens = ccount(url, ')')\n\n  while (closingParenIndex !== -1 && openingParens > closingParens) {\n    url += trail.slice(0, closingParenIndex + 1)\n    trail = trail.slice(closingParenIndex + 1)\n    closingParenIndex = trail.indexOf(')')\n    closingParens++\n  }\n\n  return [url, trail]\n}\n\n/**\n * @param {RegExpMatchObject} match\n * @param {boolean | null | undefined} [email=false]\n * @returns {boolean}\n */\nfunction previous(match, email) {\n  const code = match.input.charCodeAt(match.index - 1)\n\n  return (\n    (match.index === 0 ||\n      unicodeWhitespace(code) ||\n      unicodePunctuation(code)) &&\n    // If it’s an email, the previous character should not be a slash.\n    (!email || code !== 47)\n  )\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAED;AACA;AACA;AACA;;;;;AAEA,0BAA0B,GAC1B,MAAM,cAAc;AACpB,iCAAiC,GACjC,MAAM,iBAAiB;IAAC;IAAY;IAAQ;IAAS;CAAQ;AAStD,SAAS;IACd,OAAO;QACL,YAAY;YAAC;SAA6B;QAC1C,OAAO;YACL,iBAAiB;YACjB,sBAAsB;YACtB,qBAAqB;YACrB,oBAAoB;QACtB;QACA,MAAM;YACJ,iBAAiB;YACjB,sBAAsB;YACtB,qBAAqB;YACrB,oBAAoB;QACtB;IACF;AACF;AASO,SAAS;IACd,OAAO;QACL,QAAQ;YACN;gBACE,WAAW;gBACX,QAAQ;gBACR,OAAO;gBACP;gBACA;YACF;YACA;gBACE,WAAW;gBACX,QAAQ;gBACR,OAAO;gBACP;gBACA;YACF;YACA;gBACE,WAAW;gBACX,QAAQ;gBACR,OAAO;gBACP;gBACA;YACF;SACD;IACH;AACF;AAEA;;;CAGC,GACD,SAAS,qBAAqB,KAAK;IACjC,IAAI,CAAC,KAAK,CAAC;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;QAAI,UAAU,EAAE;IAAA,GAAG;AACjE;AAEA;;;CAGC,GACD,SAAS,0BAA0B,KAAK;IACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE;AAChD;AAEA;;;CAGC,GACD,SAAS,wBAAwB,KAAK;IACpC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE;AAC/C;AAEA;;;CAGC,GACD,SAAS,uBAAuB,KAAK;IACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;IACjC,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;IAC9C,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK;IACrB,KAAK,GAAG,GAAG,YAAY,IAAI,CAAC,cAAc,CAAC;AAC7C;AAEA;;;CAGC,GACD,SAAS,yBAAyB,KAAK;IACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE;AAC5C;AAEA;;;CAGC,GACD,SAAS,oBAAoB,KAAK;IAChC,IAAI,CAAC,IAAI,CAAC;AACZ;AAEA,kCAAkC,GAClC,SAAS,6BAA6B,IAAI;IACxC,CAAA,GAAA,0KAAA,CAAA,iBAAc,AAAD,EACX,MACA;QACE;YAAC;YAAmD;SAAQ;QAC5D;YAAC;YAA2D;SAAU;KACvE,EACD;QAAC,QAAQ;YAAC;YAAQ;SAAgB;IAAA;AAEtC;AAEA;;;;;;;;CAQC,GACD,sCAAsC;AACtC,SAAS,QAAQ,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK;IAC/C,IAAI,SAAS;IAEb,sCAAsC;IACtC,IAAI,CAAC,SAAS,QAAQ;QACpB,OAAO;IACT;IAEA,qCAAqC;IACrC,IAAI,MAAM,IAAI,CAAC,WAAW;QACxB,SAAS,WAAW;QACpB,WAAW;QACX,SAAS;IACX;IAEA,IAAI,CAAC,gBAAgB,SAAS;QAC5B,OAAO;IACT;IAEA,MAAM,QAAQ,SAAS,SAAS;IAEhC,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO;IAEtB,iBAAiB,GACjB,MAAM,SAAS;QACb,MAAM;QACN,OAAO;QACP,KAAK,SAAS,WAAW,KAAK,CAAC,EAAE;QACjC,UAAU;YAAC;gBAAC,MAAM;gBAAQ,OAAO,WAAW,KAAK,CAAC,EAAE;YAAA;SAAE;IACxD;IAEA,IAAI,KAAK,CAAC,EAAE,EAAE;QACZ,OAAO;YAAC;YAAQ;gBAAC,MAAM;gBAAQ,OAAO,KAAK,CAAC,EAAE;YAAA;SAAE;IAClD;IAEA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,UAAU,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;IACvC,IACE,sCAAsC;IACtC,CAAC,SAAS,OAAO,SACjB,uCAAuC;IACvC,UAAU,IAAI,CAAC,QACf;QACA,OAAO;IACT;IAEA,OAAO;QACL,MAAM;QACN,OAAO;QACP,KAAK,YAAY,QAAQ,MAAM;QAC/B,UAAU;YAAC;gBAAC,MAAM;gBAAQ,OAAO,QAAQ,MAAM;YAAK;SAAE;IACxD;AACF;AAEA;;;CAGC,GACD,SAAS,gBAAgB,MAAM;IAC7B,MAAM,QAAQ,OAAO,KAAK,CAAC;IAE3B,IACE,MAAM,MAAM,GAAG,KACd,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,IACtB,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,KAC/B,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,KAC9C,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,IACtB,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,KAC/B,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,GAC/C;QACA,OAAO;IACT;IAEA,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,SAAS,GAAG;IACnB,MAAM,YAAY,sBAAsB,IAAI,CAAC;IAE7C,IAAI,CAAC,WAAW;QACd,OAAO;YAAC;YAAK;SAAU;IACzB;IAEA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,KAAK;IAElC,IAAI,QAAQ,SAAS,CAAC,EAAE;IACxB,IAAI,oBAAoB,MAAM,OAAO,CAAC;IACtC,MAAM,gBAAgB,CAAA,GAAA,kIAAA,CAAA,SAAM,AAAD,EAAE,KAAK;IAClC,IAAI,gBAAgB,CAAA,GAAA,kIAAA,CAAA,SAAM,AAAD,EAAE,KAAK;IAEhC,MAAO,sBAAsB,CAAC,KAAK,gBAAgB,cAAe;QAChE,OAAO,MAAM,KAAK,CAAC,GAAG,oBAAoB;QAC1C,QAAQ,MAAM,KAAK,CAAC,oBAAoB;QACxC,oBAAoB,MAAM,OAAO,CAAC;QAClC;IACF;IAEA,OAAO;QAAC;QAAK;KAAM;AACrB;AAEA;;;;CAIC,GACD,SAAS,SAAS,KAAK,EAAE,KAAK;IAC5B,MAAM,OAAO,MAAM,KAAK,CAAC,UAAU,CAAC,MAAM,KAAK,GAAG;IAElD,OACE,CAAC,MAAM,KAAK,KAAK,KACf,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE,SAClB,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,KAC1B,kEAAkE;IAClE,CAAC,CAAC,SAAS,SAAS,EAAE;AAE1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1481, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-util-symbol/lib/values.js"], "sourcesContent": ["/**\n * This module is compiled away!\n *\n * While micromark works based on character codes, this module includes the\n * string versions of ’em.\n * The C0 block, except for LF, CR, HT, and w/ the replacement character added,\n * are available here.\n */\nexport const values = /** @type {const} */ ({\n  ht: '\\t',\n  lf: '\\n',\n  cr: '\\r',\n  space: ' ',\n  exclamationMark: '!',\n  quotationMark: '\"',\n  numberSign: '#',\n  dollarSign: '$',\n  percentSign: '%',\n  ampersand: '&',\n  apostrophe: \"'\",\n  leftParenthesis: '(',\n  rightParenthesis: ')',\n  asterisk: '*',\n  plusSign: '+',\n  comma: ',',\n  dash: '-',\n  dot: '.',\n  slash: '/',\n  digit0: '0',\n  digit1: '1',\n  digit2: '2',\n  digit3: '3',\n  digit4: '4',\n  digit5: '5',\n  digit6: '6',\n  digit7: '7',\n  digit8: '8',\n  digit9: '9',\n  colon: ':',\n  semicolon: ';',\n  lessThan: '<',\n  equalsTo: '=',\n  greaterThan: '>',\n  questionMark: '?',\n  atSign: '@',\n  uppercaseA: 'A',\n  uppercaseB: 'B',\n  uppercaseC: 'C',\n  uppercaseD: 'D',\n  uppercaseE: 'E',\n  uppercaseF: 'F',\n  uppercaseG: 'G',\n  uppercaseH: 'H',\n  uppercaseI: 'I',\n  uppercaseJ: 'J',\n  uppercaseK: 'K',\n  uppercaseL: 'L',\n  uppercaseM: 'M',\n  uppercaseN: 'N',\n  uppercaseO: 'O',\n  uppercaseP: 'P',\n  uppercaseQ: 'Q',\n  uppercaseR: 'R',\n  uppercaseS: 'S',\n  uppercaseT: 'T',\n  uppercaseU: 'U',\n  uppercaseV: 'V',\n  uppercaseW: 'W',\n  uppercaseX: 'X',\n  uppercaseY: 'Y',\n  uppercaseZ: 'Z',\n  leftSquareBracket: '[',\n  backslash: '\\\\',\n  rightSquareBracket: ']',\n  caret: '^',\n  underscore: '_',\n  graveAccent: '`',\n  lowercaseA: 'a',\n  lowercaseB: 'b',\n  lowercaseC: 'c',\n  lowercaseD: 'd',\n  lowercaseE: 'e',\n  lowercaseF: 'f',\n  lowercaseG: 'g',\n  lowercaseH: 'h',\n  lowercaseI: 'i',\n  lowercaseJ: 'j',\n  lowercaseK: 'k',\n  lowercaseL: 'l',\n  lowercaseM: 'm',\n  lowercaseN: 'n',\n  lowercaseO: 'o',\n  lowercaseP: 'p',\n  lowercaseQ: 'q',\n  lowercaseR: 'r',\n  lowercaseS: 's',\n  lowercaseT: 't',\n  lowercaseU: 'u',\n  lowercaseV: 'v',\n  lowercaseW: 'w',\n  lowercaseX: 'x',\n  lowercaseY: 'y',\n  lowercaseZ: 'z',\n  leftCurlyBrace: '{',\n  verticalBar: '|',\n  rightCurlyBrace: '}',\n  tilde: '~',\n  replacementCharacter: '�'\n})\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACM,MAAM,SAA+B;IAC1C,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,iBAAiB;IACjB,eAAe;IACf,YAAY;IACZ,YAAY;IACZ,aAAa;IACb,WAAW;IACX,YAAY;IACZ,iBAAiB;IACjB,kBAAkB;IAClB,UAAU;IACV,UAAU;IACV,OAAO;IACP,MAAM;IACN,KAAK;IACL,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,WAAW;IACX,UAAU;IACV,UAAU;IACV,aAAa;IACb,cAAc;IACd,QAAQ;IACR,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,mBAAmB;IACnB,WAAW;IACX,oBAAoB;IACpB,OAAO;IACP,YAAY;IACZ,aAAa;IACb,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,gBAAgB;IAChB,aAAa;IACb,iBAAiB;IACjB,OAAO;IACP,sBAAsB;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1598, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-util-normalize-identifier/dev/index.js"], "sourcesContent": ["import {values} from 'micromark-util-symbol'\n\n/**\n * Normalize an identifier (as found in references, definitions).\n *\n * Collapses markdown whitespace, trim, and then lower- and uppercase.\n *\n * Some characters are considered “uppercase”, such as U+03F4 (`ϴ`), but if their\n * lowercase counterpart (U+03B8 (`θ`)) is uppercased will result in a different\n * uppercase character (U+0398 (`Θ`)).\n * So, to get a canonical form, we perform both lower- and uppercase.\n *\n * Using uppercase last makes sure keys will never interact with default\n * prototypal values (such as `constructor`): nothing in the prototype of\n * `Object` is uppercase.\n *\n * @param {string} value\n *   Identifier to normalize.\n * @returns {string}\n *   Normalized identifier.\n */\nexport function normalizeIdentifier(value) {\n  return (\n    value\n      // Collapse markdown whitespace.\n      .replace(/[\\t\\n\\r ]+/g, values.space)\n      // Trim.\n      .replace(/^ | $/g, '')\n      // Some characters are considered “uppercase”, but if their lowercase\n      // counterpart is uppercased will result in a different uppercase\n      // character.\n      // Hence, to get that form, we perform both lower- and uppercase.\n      // Upper case makes sure keys will not interact with default prototypal\n      // methods: no method is uppercase.\n      .toLowerCase()\n      .toUpperCase()\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;;AAqBO,SAAS,oBAAoB,KAAK;IACvC,OACE,KACE,gCAAgC;KAC/B,OAAO,CAAC,eAAe,+JAAA,CAAA,SAAM,CAAC,KAAK,CACpC,QAAQ;KACP,OAAO,CAAC,UAAU,GACnB,qEAAqE;IACrE,iEAAiE;IACjE,aAAa;IACb,iEAAiE;IACjE,uEAAuE;IACvE,mCAAmC;KAClC,WAAW,GACX,WAAW;AAElB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1620, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-gfm-footnote/lib/index.js"], "sourcesContent": ["/**\n * @import {\n *   CompileContext,\n *   Extension as FromMarkdownExtension,\n *   Handle as FromMarkdownHandle\n * } from 'mdast-util-from-markdown'\n * @import {ToMarkdownOptions} from 'mdast-util-gfm-footnote'\n * @import {\n *   Handle as ToMarkdownHandle,\n *   Map,\n *   Options as ToMarkdownExtension\n * } from 'mdast-util-to-markdown'\n * @import {FootnoteDefinition, FootnoteReference} from 'mdast'\n */\n\nimport {ok as assert} from 'devlop'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\n\nfootnoteReference.peek = footnoteReferencePeek\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterFootnoteCallString() {\n  this.buffer()\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterFootnoteCall(token) {\n  this.enter({type: 'footnoteReference', identifier: '', label: ''}, token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterFootnoteDefinitionLabelString() {\n  this.buffer()\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterFootnoteDefinition(token) {\n  this.enter(\n    {type: 'footnoteDefinition', identifier: '', label: '', children: []},\n    token\n  )\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitFootnoteCallString(token) {\n  const label = this.resume()\n  const node = this.stack[this.stack.length - 1]\n  assert(node.type === 'footnoteReference')\n  node.identifier = normalizeIdentifier(\n    this.sliceSerialize(token)\n  ).toLowerCase()\n  node.label = label\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitFootnoteCall(token) {\n  this.exit(token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitFootnoteDefinitionLabelString(token) {\n  const label = this.resume()\n  const node = this.stack[this.stack.length - 1]\n  assert(node.type === 'footnoteDefinition')\n  node.identifier = normalizeIdentifier(\n    this.sliceSerialize(token)\n  ).toLowerCase()\n  node.label = label\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitFootnoteDefinition(token) {\n  this.exit(token)\n}\n\n/** @type {ToMarkdownHandle} */\nfunction footnoteReferencePeek() {\n  return '['\n}\n\n/**\n * @type {ToMarkdownHandle}\n * @param {FootnoteReference} node\n */\nfunction footnoteReference(node, _, state, info) {\n  const tracker = state.createTracker(info)\n  let value = tracker.move('[^')\n  const exit = state.enter('footnoteReference')\n  const subexit = state.enter('reference')\n  value += tracker.move(\n    state.safe(state.associationId(node), {after: ']', before: value})\n  )\n  subexit()\n  exit()\n  value += tracker.move(']')\n  return value\n}\n\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM footnotes\n * in markdown.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-from-markdown`.\n */\nexport function gfmFootnoteFromMarkdown() {\n  return {\n    enter: {\n      gfmFootnoteCallString: enterFootnoteCallString,\n      gfmFootnoteCall: enterFootnoteCall,\n      gfmFootnoteDefinitionLabelString: enterFootnoteDefinitionLabelString,\n      gfmFootnoteDefinition: enterFootnoteDefinition\n    },\n    exit: {\n      gfmFootnoteCallString: exitFootnoteCallString,\n      gfmFootnoteCall: exitFootnoteCall,\n      gfmFootnoteDefinitionLabelString: exitFootnoteDefinitionLabelString,\n      gfmFootnoteDefinition: exitFootnoteDefinition\n    }\n  }\n}\n\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM footnotes\n * in markdown.\n *\n * @param {ToMarkdownOptions | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown`.\n */\nexport function gfmFootnoteToMarkdown(options) {\n  // To do: next major: change default.\n  let firstLineBlank = false\n\n  if (options && options.firstLineBlank) {\n    firstLineBlank = true\n  }\n\n  return {\n    handlers: {footnoteDefinition, footnoteReference},\n    // This is on by default already.\n    unsafe: [{character: '[', inConstruct: ['label', 'phrasing', 'reference']}]\n  }\n\n  /**\n   * @type {ToMarkdownHandle}\n   * @param {FootnoteDefinition} node\n   */\n  function footnoteDefinition(node, _, state, info) {\n    const tracker = state.createTracker(info)\n    let value = tracker.move('[^')\n    const exit = state.enter('footnoteDefinition')\n    const subexit = state.enter('label')\n    value += tracker.move(\n      state.safe(state.associationId(node), {before: value, after: ']'})\n    )\n    subexit()\n\n    value += tracker.move(']:')\n\n    if (node.children && node.children.length > 0) {\n      tracker.shift(4)\n\n      value += tracker.move(\n        (firstLineBlank ? '\\n' : ' ') +\n          state.indentLines(\n            state.containerFlow(node, tracker.current()),\n            firstLineBlank ? mapAll : mapExceptFirst\n          )\n      )\n    }\n\n    exit()\n\n    return value\n  }\n}\n\n/** @type {Map} */\nfunction mapExceptFirst(line, index, blank) {\n  return index === 0 ? line : mapAll(line, index, blank)\n}\n\n/** @type {Map} */\nfunction mapAll(line, index, blank) {\n  return (blank ? '' : '    ') + line\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;CAaC;;;;AAED;AACA;;;AAEA,kBAAkB,IAAI,GAAG;AAEzB;;;CAGC,GACD,SAAS;IACP,IAAI,CAAC,MAAM;AACb;AAEA;;;CAGC,GACD,SAAS,kBAAkB,KAAK;IAC9B,IAAI,CAAC,KAAK,CAAC;QAAC,MAAM;QAAqB,YAAY;QAAI,OAAO;IAAE,GAAG;AACrE;AAEA;;;CAGC,GACD,SAAS;IACP,IAAI,CAAC,MAAM;AACb;AAEA;;;CAGC,GACD,SAAS,wBAAwB,KAAK;IACpC,IAAI,CAAC,KAAK,CACR;QAAC,MAAM;QAAsB,YAAY;QAAI,OAAO;QAAI,UAAU,EAAE;IAAA,GACpE;AAEJ;AAEA;;;CAGC,GACD,SAAS,uBAAuB,KAAK;IACnC,MAAM,QAAQ,IAAI,CAAC,MAAM;IACzB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;IAC9C,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK;IACrB,KAAK,UAAU,GAAG,CAAA,GAAA,+KAAA,CAAA,sBAAmB,AAAD,EAClC,IAAI,CAAC,cAAc,CAAC,QACpB,WAAW;IACb,KAAK,KAAK,GAAG;AACf;AAEA;;;CAGC,GACD,SAAS,iBAAiB,KAAK;IAC7B,IAAI,CAAC,IAAI,CAAC;AACZ;AAEA;;;CAGC,GACD,SAAS,kCAAkC,KAAK;IAC9C,MAAM,QAAQ,IAAI,CAAC,MAAM;IACzB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;IAC9C,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK;IACrB,KAAK,UAAU,GAAG,CAAA,GAAA,+KAAA,CAAA,sBAAmB,AAAD,EAClC,IAAI,CAAC,cAAc,CAAC,QACpB,WAAW;IACb,KAAK,KAAK,GAAG;AACf;AAEA;;;CAGC,GACD,SAAS,uBAAuB,KAAK;IACnC,IAAI,CAAC,IAAI,CAAC;AACZ;AAEA,6BAA6B,GAC7B,SAAS;IACP,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,kBAAkB,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI;IAC7C,MAAM,UAAU,MAAM,aAAa,CAAC;IACpC,IAAI,QAAQ,QAAQ,IAAI,CAAC;IACzB,MAAM,OAAO,MAAM,KAAK,CAAC;IACzB,MAAM,UAAU,MAAM,KAAK,CAAC;IAC5B,SAAS,QAAQ,IAAI,CACnB,MAAM,IAAI,CAAC,MAAM,aAAa,CAAC,OAAO;QAAC,OAAO;QAAK,QAAQ;IAAK;IAElE;IACA;IACA,SAAS,QAAQ,IAAI,CAAC;IACtB,OAAO;AACT;AASO,SAAS;IACd,OAAO;QACL,OAAO;YACL,uBAAuB;YACvB,iBAAiB;YACjB,kCAAkC;YAClC,uBAAuB;QACzB;QACA,MAAM;YACJ,uBAAuB;YACvB,iBAAiB;YACjB,kCAAkC;YAClC,uBAAuB;QACzB;IACF;AACF;AAWO,SAAS,sBAAsB,OAAO;IAC3C,qCAAqC;IACrC,IAAI,iBAAiB;IAErB,IAAI,WAAW,QAAQ,cAAc,EAAE;QACrC,iBAAiB;IACnB;IAEA,OAAO;QACL,UAAU;YAAC;YAAoB;QAAiB;QAChD,iCAAiC;QACjC,QAAQ;YAAC;gBAAC,WAAW;gBAAK,aAAa;oBAAC;oBAAS;oBAAY;iBAAY;YAAA;SAAE;IAC7E;;IAEA;;;GAGC,GACD,SAAS,mBAAmB,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI;QAC9C,MAAM,UAAU,MAAM,aAAa,CAAC;QACpC,IAAI,QAAQ,QAAQ,IAAI,CAAC;QACzB,MAAM,OAAO,MAAM,KAAK,CAAC;QACzB,MAAM,UAAU,MAAM,KAAK,CAAC;QAC5B,SAAS,QAAQ,IAAI,CACnB,MAAM,IAAI,CAAC,MAAM,aAAa,CAAC,OAAO;YAAC,QAAQ;YAAO,OAAO;QAAG;QAElE;QAEA,SAAS,QAAQ,IAAI,CAAC;QAEtB,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC7C,QAAQ,KAAK,CAAC;YAEd,SAAS,QAAQ,IAAI,CACnB,CAAC,iBAAiB,OAAO,GAAG,IAC1B,MAAM,WAAW,CACf,MAAM,aAAa,CAAC,MAAM,QAAQ,OAAO,KACzC,iBAAiB,SAAS;QAGlC;QAEA;QAEA,OAAO;IACT;AACF;AAEA,gBAAgB,GAChB,SAAS,eAAe,IAAI,EAAE,KAAK,EAAE,KAAK;IACxC,OAAO,UAAU,IAAI,OAAO,OAAO,MAAM,OAAO;AAClD;AAEA,gBAAgB,GAChB,SAAS,OAAO,IAAI,EAAE,KAAK,EAAE,KAAK;IAChC,OAAO,CAAC,QAAQ,KAAK,MAAM,IAAI;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1801, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-gfm-strikethrough/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('mdast').Delete} Delete\n *\n * @typedef {import('mdast-util-from-markdown').CompileContext} CompileContext\n * @typedef {import('mdast-util-from-markdown').Extension} FromMarkdownExtension\n * @typedef {import('mdast-util-from-markdown').Handle} FromMarkdownHandle\n *\n * @typedef {import('mdast-util-to-markdown').ConstructName} ConstructName\n * @typedef {import('mdast-util-to-markdown').Handle} ToMarkdownHandle\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownExtension\n */\n\n/**\n * List of constructs that occur in phrasing (paragraphs, headings), but cannot\n * contain strikethrough.\n * So they sort of cancel each other out.\n * Note: could use a better name.\n *\n * Note: keep in sync with: <https://github.com/syntax-tree/mdast-util-to-markdown/blob/8ce8dbf/lib/unsafe.js#L14>\n *\n * @type {Array<ConstructName>}\n */\nconst constructsWithoutStrikethrough = [\n  'autolink',\n  'destinationLiteral',\n  'destinationRaw',\n  'reference',\n  'titleQuote',\n  'titleApostrophe'\n]\n\nhandleDelete.peek = peekDelete\n\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM\n * strikethrough in markdown.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-from-markdown` to enable GFM strikethrough.\n */\nexport function gfmStrikethroughFromMarkdown() {\n  return {\n    canContainEols: ['delete'],\n    enter: {strikethrough: enterStrikethrough},\n    exit: {strikethrough: exitStrikethrough}\n  }\n}\n\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM\n * strikethrough in markdown.\n *\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable GFM strikethrough.\n */\nexport function gfmStrikethroughToMarkdown() {\n  return {\n    unsafe: [\n      {\n        character: '~',\n        inConstruct: 'phrasing',\n        notInConstruct: constructsWithoutStrikethrough\n      }\n    ],\n    handlers: {delete: handleDelete}\n  }\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterStrikethrough(token) {\n  this.enter({type: 'delete', children: []}, token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitStrikethrough(token) {\n  this.exit(token)\n}\n\n/**\n * @type {ToMarkdownHandle}\n * @param {Delete} node\n */\nfunction handleDelete(node, _, state, info) {\n  const tracker = state.createTracker(info)\n  const exit = state.enter('strikethrough')\n  let value = tracker.move('~~')\n  value += state.containerPhrasing(node, {\n    ...tracker.current(),\n    before: value,\n    after: '~'\n  })\n  value += tracker.move('~~')\n  exit()\n  return value\n}\n\n/** @type {ToMarkdownHandle} */\nfunction peekDelete() {\n  return '~'\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC,GAED;;;;;;;;;CASC;;;;AACD,MAAM,iCAAiC;IACrC;IACA;IACA;IACA;IACA;IACA;CACD;AAED,aAAa,IAAI,GAAG;AASb,SAAS;IACd,OAAO;QACL,gBAAgB;YAAC;SAAS;QAC1B,OAAO;YAAC,eAAe;QAAkB;QACzC,MAAM;YAAC,eAAe;QAAiB;IACzC;AACF;AASO,SAAS;IACd,OAAO;QACL,QAAQ;YACN;gBACE,WAAW;gBACX,aAAa;gBACb,gBAAgB;YAClB;SACD;QACD,UAAU;YAAC,QAAQ;QAAY;IACjC;AACF;AAEA;;;CAGC,GACD,SAAS,mBAAmB,KAAK;IAC/B,IAAI,CAAC,KAAK,CAAC;QAAC,MAAM;QAAU,UAAU,EAAE;IAAA,GAAG;AAC7C;AAEA;;;CAGC,GACD,SAAS,kBAAkB,KAAK;IAC9B,IAAI,CAAC,IAAI,CAAC;AACZ;AAEA;;;CAGC,GACD,SAAS,aAAa,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI;IACxC,MAAM,UAAU,MAAM,aAAa,CAAC;IACpC,MAAM,OAAO,MAAM,KAAK,CAAC;IACzB,IAAI,QAAQ,QAAQ,IAAI,CAAC;IACzB,SAAS,MAAM,iBAAiB,CAAC,MAAM;QACrC,GAAG,QAAQ,OAAO,EAAE;QACpB,QAAQ;QACR,OAAO;IACT;IACA,SAAS,QAAQ,IAAI,CAAC;IACtB;IACA,OAAO;AACT;AAEA,6BAA6B,GAC7B,SAAS;IACP,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1900, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/markdown-table/index.js"], "sourcesContent": ["// To do: next major: remove.\n/**\n * @typedef {Options} MarkdownTableOptions\n *   Configuration.\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [alignDelimiters=true]\n *   Whether to align the delimiters (default: `true`);\n *   they are aligned by default:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   Pass `false` to make them staggered:\n *\n *   ```markdown\n *   | Alpha | B |\n *   | - | - |\n *   | C | Delta |\n *   ```\n * @property {ReadonlyArray<string | null | undefined> | string | null | undefined} [align]\n *   How to align columns (default: `''`);\n *   one style for all columns or styles for their respective columns;\n *   each style is either `'l'` (left), `'r'` (right), or `'c'` (center);\n *   other values are treated as `''`, which doesn’t place the colon in the\n *   alignment row but does align left;\n *   *only the lowercased first character is used, so `Right` is fine.*\n * @property {boolean | null | undefined} [delimiterEnd=true]\n *   Whether to end each row with the delimiter (default: `true`).\n *\n *   > 👉 **Note**: please don’t use this: it could create fragile structures\n *   > that aren’t understandable to some markdown parsers.\n *\n *   When `true`, there are ending delimiters:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there are no ending delimiters:\n *\n *   ```markdown\n *   | Alpha | B\n *   | ----- | -----\n *   | C     | Delta\n *   ```\n * @property {boolean | null | undefined} [delimiterStart=true]\n *   Whether to begin each row with the delimiter (default: `true`).\n *\n *   > 👉 **Note**: please don’t use this: it could create fragile structures\n *   > that aren’t understandable to some markdown parsers.\n *\n *   When `true`, there are starting delimiters:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there are no starting delimiters:\n *\n *   ```markdown\n *   Alpha | B     |\n *   ----- | ----- |\n *   C     | Delta |\n *   ```\n * @property {boolean | null | undefined} [padding=true]\n *   Whether to add a space of padding between delimiters and cells\n *   (default: `true`).\n *\n *   When `true`, there is padding:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there is no padding:\n *\n *   ```markdown\n *   |Alpha|B    |\n *   |-----|-----|\n *   |C    |Delta|\n *   ```\n * @property {((value: string) => number) | null | undefined} [stringLength]\n *   Function to detect the length of table cell content (optional);\n *   this is used when aligning the delimiters (`|`) between table cells;\n *   full-width characters and emoji mess up delimiter alignment when viewing\n *   the markdown source;\n *   to fix this, you can pass this function,\n *   which receives the cell content and returns its “visible” size;\n *   note that what is and isn’t visible depends on where the text is displayed.\n *\n *   Without such a function, the following:\n *\n *   ```js\n *   markdownTable([\n *     ['Alpha', 'Bravo'],\n *     ['中文', 'Charlie'],\n *     ['👩‍❤️‍👩', 'Delta']\n *   ])\n *   ```\n *\n *   Yields:\n *\n *   ```markdown\n *   | Alpha | Bravo |\n *   | - | - |\n *   | 中文 | Charlie |\n *   | 👩‍❤️‍👩 | Delta |\n *   ```\n *\n *   With [`string-width`](https://github.com/sindresorhus/string-width):\n *\n *   ```js\n *   import stringWidth from 'string-width'\n *\n *   markdownTable(\n *     [\n *       ['Alpha', 'Bravo'],\n *       ['中文', 'Charlie'],\n *       ['👩‍❤️‍👩', 'Delta']\n *     ],\n *     {stringLength: stringWidth}\n *   )\n *   ```\n *\n *   Yields:\n *\n *   ```markdown\n *   | Alpha | Bravo   |\n *   | ----- | ------- |\n *   | 中文  | Charlie |\n *   | 👩‍❤️‍👩    | Delta   |\n *   ```\n */\n\n/**\n * @param {string} value\n *   Cell value.\n * @returns {number}\n *   Cell size.\n */\nfunction defaultStringLength(value) {\n  return value.length\n}\n\n/**\n * Generate a markdown\n * ([GFM](https://docs.github.com/en/github/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables))\n * table.\n *\n * @param {ReadonlyArray<ReadonlyArray<string | null | undefined>>} table\n *   Table data (matrix of strings).\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Result.\n */\nexport function markdownTable(table, options) {\n  const settings = options || {}\n  // To do: next major: change to spread.\n  const align = (settings.align || []).concat()\n  const stringLength = settings.stringLength || defaultStringLength\n  /** @type {Array<number>} Character codes as symbols for alignment per column. */\n  const alignments = []\n  /** @type {Array<Array<string>>} Cells per row. */\n  const cellMatrix = []\n  /** @type {Array<Array<number>>} Sizes of each cell per row. */\n  const sizeMatrix = []\n  /** @type {Array<number>} */\n  const longestCellByColumn = []\n  let mostCellsPerRow = 0\n  let rowIndex = -1\n\n  // This is a superfluous loop if we don’t align delimiters, but otherwise we’d\n  // do superfluous work when aligning, so optimize for aligning.\n  while (++rowIndex < table.length) {\n    /** @type {Array<string>} */\n    const row = []\n    /** @type {Array<number>} */\n    const sizes = []\n    let columnIndex = -1\n\n    if (table[rowIndex].length > mostCellsPerRow) {\n      mostCellsPerRow = table[rowIndex].length\n    }\n\n    while (++columnIndex < table[rowIndex].length) {\n      const cell = serialize(table[rowIndex][columnIndex])\n\n      if (settings.alignDelimiters !== false) {\n        const size = stringLength(cell)\n        sizes[columnIndex] = size\n\n        if (\n          longestCellByColumn[columnIndex] === undefined ||\n          size > longestCellByColumn[columnIndex]\n        ) {\n          longestCellByColumn[columnIndex] = size\n        }\n      }\n\n      row.push(cell)\n    }\n\n    cellMatrix[rowIndex] = row\n    sizeMatrix[rowIndex] = sizes\n  }\n\n  // Figure out which alignments to use.\n  let columnIndex = -1\n\n  if (typeof align === 'object' && 'length' in align) {\n    while (++columnIndex < mostCellsPerRow) {\n      alignments[columnIndex] = toAlignment(align[columnIndex])\n    }\n  } else {\n    const code = toAlignment(align)\n\n    while (++columnIndex < mostCellsPerRow) {\n      alignments[columnIndex] = code\n    }\n  }\n\n  // Inject the alignment row.\n  columnIndex = -1\n  /** @type {Array<string>} */\n  const row = []\n  /** @type {Array<number>} */\n  const sizes = []\n\n  while (++columnIndex < mostCellsPerRow) {\n    const code = alignments[columnIndex]\n    let before = ''\n    let after = ''\n\n    if (code === 99 /* `c` */) {\n      before = ':'\n      after = ':'\n    } else if (code === 108 /* `l` */) {\n      before = ':'\n    } else if (code === 114 /* `r` */) {\n      after = ':'\n    }\n\n    // There *must* be at least one hyphen-minus in each alignment cell.\n    let size =\n      settings.alignDelimiters === false\n        ? 1\n        : Math.max(\n            1,\n            longestCellByColumn[columnIndex] - before.length - after.length\n          )\n\n    const cell = before + '-'.repeat(size) + after\n\n    if (settings.alignDelimiters !== false) {\n      size = before.length + size + after.length\n\n      if (size > longestCellByColumn[columnIndex]) {\n        longestCellByColumn[columnIndex] = size\n      }\n\n      sizes[columnIndex] = size\n    }\n\n    row[columnIndex] = cell\n  }\n\n  // Inject the alignment row.\n  cellMatrix.splice(1, 0, row)\n  sizeMatrix.splice(1, 0, sizes)\n\n  rowIndex = -1\n  /** @type {Array<string>} */\n  const lines = []\n\n  while (++rowIndex < cellMatrix.length) {\n    const row = cellMatrix[rowIndex]\n    const sizes = sizeMatrix[rowIndex]\n    columnIndex = -1\n    /** @type {Array<string>} */\n    const line = []\n\n    while (++columnIndex < mostCellsPerRow) {\n      const cell = row[columnIndex] || ''\n      let before = ''\n      let after = ''\n\n      if (settings.alignDelimiters !== false) {\n        const size =\n          longestCellByColumn[columnIndex] - (sizes[columnIndex] || 0)\n        const code = alignments[columnIndex]\n\n        if (code === 114 /* `r` */) {\n          before = ' '.repeat(size)\n        } else if (code === 99 /* `c` */) {\n          if (size % 2) {\n            before = ' '.repeat(size / 2 + 0.5)\n            after = ' '.repeat(size / 2 - 0.5)\n          } else {\n            before = ' '.repeat(size / 2)\n            after = before\n          }\n        } else {\n          after = ' '.repeat(size)\n        }\n      }\n\n      if (settings.delimiterStart !== false && !columnIndex) {\n        line.push('|')\n      }\n\n      if (\n        settings.padding !== false &&\n        // Don’t add the opening space if we’re not aligning and the cell is\n        // empty: there will be a closing space.\n        !(settings.alignDelimiters === false && cell === '') &&\n        (settings.delimiterStart !== false || columnIndex)\n      ) {\n        line.push(' ')\n      }\n\n      if (settings.alignDelimiters !== false) {\n        line.push(before)\n      }\n\n      line.push(cell)\n\n      if (settings.alignDelimiters !== false) {\n        line.push(after)\n      }\n\n      if (settings.padding !== false) {\n        line.push(' ')\n      }\n\n      if (\n        settings.delimiterEnd !== false ||\n        columnIndex !== mostCellsPerRow - 1\n      ) {\n        line.push('|')\n      }\n    }\n\n    lines.push(\n      settings.delimiterEnd === false\n        ? line.join('').replace(/ +$/, '')\n        : line.join('')\n    )\n  }\n\n  return lines.join('\\n')\n}\n\n/**\n * @param {string | null | undefined} [value]\n *   Value to serialize.\n * @returns {string}\n *   Result.\n */\nfunction serialize(value) {\n  return value === null || value === undefined ? '' : String(value)\n}\n\n/**\n * @param {string | null | undefined} value\n *   Value.\n * @returns {number}\n *   Alignment.\n */\nfunction toAlignment(value) {\n  const code = typeof value === 'string' ? value.codePointAt(0) : 0\n\n  return code === 67 /* `C` */ || code === 99 /* `c` */\n    ? 99 /* `c` */\n    : code === 76 /* `L` */ || code === 108 /* `l` */\n      ? 108 /* `l` */\n      : code === 82 /* `R` */ || code === 114 /* `r` */\n        ? 114 /* `r` */\n        : 0\n}\n"], "names": [], "mappings": "AAAA,6BAA6B;AAC7B;;;CAGC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2IC,GAED;;;;;CAKC;;;AACD,SAAS,oBAAoB,KAAK;IAChC,OAAO,MAAM,MAAM;AACrB;AAcO,SAAS,cAAc,KAAK,EAAE,OAAO;IAC1C,MAAM,WAAW,WAAW,CAAC;IAC7B,uCAAuC;IACvC,MAAM,QAAQ,CAAC,SAAS,KAAK,IAAI,EAAE,EAAE,MAAM;IAC3C,MAAM,eAAe,SAAS,YAAY,IAAI;IAC9C,+EAA+E,GAC/E,MAAM,aAAa,EAAE;IACrB,gDAAgD,GAChD,MAAM,aAAa,EAAE;IACrB,6DAA6D,GAC7D,MAAM,aAAa,EAAE;IACrB,0BAA0B,GAC1B,MAAM,sBAAsB,EAAE;IAC9B,IAAI,kBAAkB;IACtB,IAAI,WAAW,CAAC;IAEhB,8EAA8E;IAC9E,+DAA+D;IAC/D,MAAO,EAAE,WAAW,MAAM,MAAM,CAAE;QAChC,0BAA0B,GAC1B,MAAM,MAAM,EAAE;QACd,0BAA0B,GAC1B,MAAM,QAAQ,EAAE;QAChB,IAAI,cAAc,CAAC;QAEnB,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,iBAAiB;YAC5C,kBAAkB,KAAK,CAAC,SAAS,CAAC,MAAM;QAC1C;QAEA,MAAO,EAAE,cAAc,KAAK,CAAC,SAAS,CAAC,MAAM,CAAE;YAC7C,MAAM,OAAO,UAAU,KAAK,CAAC,SAAS,CAAC,YAAY;YAEnD,IAAI,SAAS,eAAe,KAAK,OAAO;gBACtC,MAAM,OAAO,aAAa;gBAC1B,KAAK,CAAC,YAAY,GAAG;gBAErB,IACE,mBAAmB,CAAC,YAAY,KAAK,aACrC,OAAO,mBAAmB,CAAC,YAAY,EACvC;oBACA,mBAAmB,CAAC,YAAY,GAAG;gBACrC;YACF;YAEA,IAAI,IAAI,CAAC;QACX;QAEA,UAAU,CAAC,SAAS,GAAG;QACvB,UAAU,CAAC,SAAS,GAAG;IACzB;IAEA,sCAAsC;IACtC,IAAI,cAAc,CAAC;IAEnB,IAAI,OAAO,UAAU,YAAY,YAAY,OAAO;QAClD,MAAO,EAAE,cAAc,gBAAiB;YACtC,UAAU,CAAC,YAAY,GAAG,YAAY,KAAK,CAAC,YAAY;QAC1D;IACF,OAAO;QACL,MAAM,OAAO,YAAY;QAEzB,MAAO,EAAE,cAAc,gBAAiB;YACtC,UAAU,CAAC,YAAY,GAAG;QAC5B;IACF;IAEA,4BAA4B;IAC5B,cAAc,CAAC;IACf,0BAA0B,GAC1B,MAAM,MAAM,EAAE;IACd,0BAA0B,GAC1B,MAAM,QAAQ,EAAE;IAEhB,MAAO,EAAE,cAAc,gBAAiB;QACtC,MAAM,OAAO,UAAU,CAAC,YAAY;QACpC,IAAI,SAAS;QACb,IAAI,QAAQ;QAEZ,IAAI,SAAS,GAAG,OAAO,KAAI;YACzB,SAAS;YACT,QAAQ;QACV,OAAO,IAAI,SAAS,IAAI,OAAO,KAAI;YACjC,SAAS;QACX,OAAO,IAAI,SAAS,IAAI,OAAO,KAAI;YACjC,QAAQ;QACV;QAEA,oEAAoE;QACpE,IAAI,OACF,SAAS,eAAe,KAAK,QACzB,IACA,KAAK,GAAG,CACN,GACA,mBAAmB,CAAC,YAAY,GAAG,OAAO,MAAM,GAAG,MAAM,MAAM;QAGvE,MAAM,OAAO,SAAS,IAAI,MAAM,CAAC,QAAQ;QAEzC,IAAI,SAAS,eAAe,KAAK,OAAO;YACtC,OAAO,OAAO,MAAM,GAAG,OAAO,MAAM,MAAM;YAE1C,IAAI,OAAO,mBAAmB,CAAC,YAAY,EAAE;gBAC3C,mBAAmB,CAAC,YAAY,GAAG;YACrC;YAEA,KAAK,CAAC,YAAY,GAAG;QACvB;QAEA,GAAG,CAAC,YAAY,GAAG;IACrB;IAEA,4BAA4B;IAC5B,WAAW,MAAM,CAAC,GAAG,GAAG;IACxB,WAAW,MAAM,CAAC,GAAG,GAAG;IAExB,WAAW,CAAC;IACZ,0BAA0B,GAC1B,MAAM,QAAQ,EAAE;IAEhB,MAAO,EAAE,WAAW,WAAW,MAAM,CAAE;QACrC,MAAM,MAAM,UAAU,CAAC,SAAS;QAChC,MAAM,QAAQ,UAAU,CAAC,SAAS;QAClC,cAAc,CAAC;QACf,0BAA0B,GAC1B,MAAM,OAAO,EAAE;QAEf,MAAO,EAAE,cAAc,gBAAiB;YACtC,MAAM,OAAO,GAAG,CAAC,YAAY,IAAI;YACjC,IAAI,SAAS;YACb,IAAI,QAAQ;YAEZ,IAAI,SAAS,eAAe,KAAK,OAAO;gBACtC,MAAM,OACJ,mBAAmB,CAAC,YAAY,GAAG,CAAC,KAAK,CAAC,YAAY,IAAI,CAAC;gBAC7D,MAAM,OAAO,UAAU,CAAC,YAAY;gBAEpC,IAAI,SAAS,IAAI,OAAO,KAAI;oBAC1B,SAAS,IAAI,MAAM,CAAC;gBACtB,OAAO,IAAI,SAAS,GAAG,OAAO,KAAI;oBAChC,IAAI,OAAO,GAAG;wBACZ,SAAS,IAAI,MAAM,CAAC,OAAO,IAAI;wBAC/B,QAAQ,IAAI,MAAM,CAAC,OAAO,IAAI;oBAChC,OAAO;wBACL,SAAS,IAAI,MAAM,CAAC,OAAO;wBAC3B,QAAQ;oBACV;gBACF,OAAO;oBACL,QAAQ,IAAI,MAAM,CAAC;gBACrB;YACF;YAEA,IAAI,SAAS,cAAc,KAAK,SAAS,CAAC,aAAa;gBACrD,KAAK,IAAI,CAAC;YACZ;YAEA,IACE,SAAS,OAAO,KAAK,SACrB,oEAAoE;YACpE,wCAAwC;YACxC,CAAC,CAAC,SAAS,eAAe,KAAK,SAAS,SAAS,EAAE,KACnD,CAAC,SAAS,cAAc,KAAK,SAAS,WAAW,GACjD;gBACA,KAAK,IAAI,CAAC;YACZ;YAEA,IAAI,SAAS,eAAe,KAAK,OAAO;gBACtC,KAAK,IAAI,CAAC;YACZ;YAEA,KAAK,IAAI,CAAC;YAEV,IAAI,SAAS,eAAe,KAAK,OAAO;gBACtC,KAAK,IAAI,CAAC;YACZ;YAEA,IAAI,SAAS,OAAO,KAAK,OAAO;gBAC9B,KAAK,IAAI,CAAC;YACZ;YAEA,IACE,SAAS,YAAY,KAAK,SAC1B,gBAAgB,kBAAkB,GAClC;gBACA,KAAK,IAAI,CAAC;YACZ;QACF;QAEA,MAAM,IAAI,CACR,SAAS,YAAY,KAAK,QACtB,KAAK,IAAI,CAAC,IAAI,OAAO,CAAC,OAAO,MAC7B,KAAK,IAAI,CAAC;IAElB;IAEA,OAAO,MAAM,IAAI,CAAC;AACpB;AAEA;;;;;CAKC,GACD,SAAS,UAAU,KAAK;IACtB,OAAO,UAAU,QAAQ,UAAU,YAAY,KAAK,OAAO;AAC7D;AAEA;;;;;CAKC,GACD,SAAS,YAAY,KAAK;IACxB,MAAM,OAAO,OAAO,UAAU,WAAW,MAAM,WAAW,CAAC,KAAK;IAEhE,OAAO,SAAS,GAAG,OAAO,OAAM,SAAS,GAAG,OAAO,MAC/C,GAAG,OAAO,MACV,SAAS,GAAG,OAAO,OAAM,SAAS,IAAI,OAAO,MAC3C,IAAI,OAAO,MACX,SAAS,GAAG,OAAO,OAAM,SAAS,IAAI,OAAO,MAC3C,IAAI,OAAO,MACX;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2208, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/handle/blockquote.js"], "sourcesContent": ["/**\n * @import {Blockquote, Parents} from 'mdast'\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {Blockquote} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function blockquote(node, _, state, info) {\n  const exit = state.enter('blockquote')\n  const tracker = state.createTracker(info)\n  tracker.move('> ')\n  tracker.shift(2)\n  const value = state.indentLines(\n    state.containerFlow(node, tracker.current()),\n    map\n  )\n  exit()\n  return value\n}\n\n/** @type {Map} */\nfunction map(line, _, blank) {\n  return '>' + (blank ? '' : ' ') + line\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;;;CAMC;;;AACM,SAAS,WAAW,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI;IAC7C,MAAM,OAAO,MAAM,KAAK,CAAC;IACzB,MAAM,UAAU,MAAM,aAAa,CAAC;IACpC,QAAQ,IAAI,CAAC;IACb,QAAQ,KAAK,CAAC;IACd,MAAM,QAAQ,MAAM,WAAW,CAC7B,MAAM,aAAa,CAAC,MAAM,QAAQ,OAAO,KACzC;IAEF;IACA,OAAO;AACT;AAEA,gBAAgB,GAChB,SAAS,IAAI,IAAI,EAAE,CAAC,EAAE,KAAK;IACzB,OAAO,MAAM,CAAC,QAAQ,KAAK,GAAG,IAAI;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2238, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js"], "sourcesContent": ["/**\n * @import {ConstructName, Unsafe} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe} pattern\n * @returns {boolean}\n */\nexport function patternInScope(stack, pattern) {\n  return (\n    listInScope(stack, pattern.inConstruct, true) &&\n    !listInScope(stack, pattern.notInConstruct, false)\n  )\n}\n\n/**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe['inConstruct']} list\n * @param {boolean} none\n * @returns {boolean}\n */\nfunction listInScope(stack, list, none) {\n  if (typeof list === 'string') {\n    list = [list]\n  }\n\n  if (!list || list.length === 0) {\n    return none\n  }\n\n  let index = -1\n\n  while (++index < list.length) {\n    if (stack.includes(list[index])) {\n      return true\n    }\n  }\n\n  return false\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;CAIC;;;AACM,SAAS,eAAe,KAAK,EAAE,OAAO;IAC3C,OACE,YAAY,OAAO,QAAQ,WAAW,EAAE,SACxC,CAAC,YAAY,OAAO,QAAQ,cAAc,EAAE;AAEhD;AAEA;;;;;CAKC,GACD,SAAS,YAAY,KAAK,EAAE,IAAI,EAAE,IAAI;IACpC,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO;YAAC;SAAK;IACf;IAEA,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,KAAK,MAAM,CAAE;QAC5B,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG;YAC/B,OAAO;QACT;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2278, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/handle/break.js"], "sourcesContent": ["/**\n * @import {Break, Parents} from 'mdast'\n * @import {Info, State} from 'mdast-util-to-markdown'\n */\n\nimport {patternInScope} from '../util/pattern-in-scope.js'\n\n/**\n * @param {Break} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function hardBreak(_, _1, state, info) {\n  let index = -1\n\n  while (++index < state.unsafe.length) {\n    // If we can’t put eols in this construct (setext headings, tables), use a\n    // space instead.\n    if (\n      state.unsafe[index].character === '\\n' &&\n      patternInScope(state.stack, state.unsafe[index])\n    ) {\n      return /[ \\t]/.test(info.before) ? '' : ' '\n    }\n  }\n\n  return '\\\\\\n'\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AASO,SAAS,UAAU,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI;IAC1C,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAE;QACpC,0EAA0E;QAC1E,iBAAiB;QACjB,IACE,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,KAAK,QAClC,CAAA,GAAA,2LAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,KAAK,EAAE,MAAM,MAAM,CAAC,MAAM,GAC/C;YACA,OAAO,QAAQ,IAAI,CAAC,KAAK,MAAM,IAAI,KAAK;QAC1C;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2303, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/longest-streak/index.js"], "sourcesContent": ["/**\n * Get the count of the longest repeating streak of `substring` in `value`.\n *\n * @param {string} value\n *   Content to search in.\n * @param {string} substring\n *   Substring to look for, typically one character.\n * @returns {number}\n *   Count of most frequent adjacent `substring`s in `value`.\n */\nexport function longestStreak(value, substring) {\n  const source = String(value)\n  let index = source.indexOf(substring)\n  let expected = index\n  let count = 0\n  let max = 0\n\n  if (typeof substring !== 'string') {\n    throw new TypeError('Expected substring')\n  }\n\n  while (index !== -1) {\n    if (index === expected) {\n      if (++count > max) {\n        max = count\n      }\n    } else {\n      count = 1\n    }\n\n    expected = index + substring.length\n    index = source.indexOf(substring, expected)\n  }\n\n  return max\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;AACM,SAAS,cAAc,KAAK,EAAE,SAAS;IAC5C,MAAM,SAAS,OAAO;IACtB,IAAI,QAAQ,OAAO,OAAO,CAAC;IAC3B,IAAI,WAAW;IACf,IAAI,QAAQ;IACZ,IAAI,MAAM;IAEV,IAAI,OAAO,cAAc,UAAU;QACjC,MAAM,IAAI,UAAU;IACtB;IAEA,MAAO,UAAU,CAAC,EAAG;QACnB,IAAI,UAAU,UAAU;YACtB,IAAI,EAAE,QAAQ,KAAK;gBACjB,MAAM;YACR;QACF,OAAO;YACL,QAAQ;QACV;QAEA,WAAW,QAAQ,UAAU,MAAM;QACnC,QAAQ,OAAO,OAAO,CAAC,WAAW;IACpC;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2343, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js"], "sourcesContent": ["/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Code} from 'mdast'\n */\n\n/**\n * @param {Code} node\n * @param {State} state\n * @returns {boolean}\n */\nexport function formatCodeAsIndented(node, state) {\n  return Boolean(\n    state.options.fences === false &&\n      node.value &&\n      // If there’s no info…\n      !node.lang &&\n      // And there’s a non-whitespace character…\n      /[^ \\r\\n]/.test(node.value) &&\n      // And the value doesn’t start or end in a blank…\n      !/^[\\t ]*(?:[\\r\\n]|$)|(?:^|[\\r\\n])[\\t ]*$/.test(node.value)\n  )\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;CAIC;;;AACM,SAAS,qBAAqB,IAAI,EAAE,KAAK;IAC9C,OAAO,QACL,MAAM,OAAO,CAAC,MAAM,KAAK,SACvB,KAAK,KAAK,IACV,sBAAsB;IACtB,CAAC,KAAK,IAAI,IACV,0CAA0C;IAC1C,WAAW,IAAI,CAAC,KAAK,KAAK,KAC1B,iDAAiD;IACjD,CAAC,0CAA0C,IAAI,CAAC,KAAK,KAAK;AAEhE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2365, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/util/check-fence.js"], "sourcesContent": ["/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['fence'], null | undefined>}\n */\nexport function checkFence(state) {\n  const marker = state.options.fence || '`'\n\n  if (marker !== '`' && marker !== '~') {\n    throw new Error(\n      'Cannot serialize code with `' +\n        marker +\n        '` for `options.fence`, expected `` ` `` or `~`'\n    )\n  }\n\n  return marker\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;CAGC;;;AACM,SAAS,WAAW,KAAK;IAC9B,MAAM,SAAS,MAAM,OAAO,CAAC,KAAK,IAAI;IAEtC,IAAI,WAAW,OAAO,WAAW,KAAK;QACpC,MAAM,IAAI,MACR,iCACE,SACA;IAEN;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2386, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/handle/code.js"], "sourcesContent": ["/**\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n * @import {Code, Parents} from 'mdast'\n */\n\nimport {longestStreak} from 'longest-streak'\nimport {formatCodeAsIndented} from '../util/format-code-as-indented.js'\nimport {checkFence} from '../util/check-fence.js'\n\n/**\n * @param {Code} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function code(node, _, state, info) {\n  const marker = checkFence(state)\n  const raw = node.value || ''\n  const suffix = marker === '`' ? 'GraveAccent' : 'Tilde'\n\n  if (formatCodeAsIndented(node, state)) {\n    const exit = state.enter('codeIndented')\n    const value = state.indentLines(raw, map)\n    exit()\n    return value\n  }\n\n  const tracker = state.createTracker(info)\n  const sequence = marker.repeat(Math.max(longestStreak(raw, marker) + 1, 3))\n  const exit = state.enter('codeFenced')\n  let value = tracker.move(sequence)\n\n  if (node.lang) {\n    const subexit = state.enter(`codeFencedLang${suffix}`)\n    value += tracker.move(\n      state.safe(node.lang, {\n        before: value,\n        after: ' ',\n        encode: ['`'],\n        ...tracker.current()\n      })\n    )\n    subexit()\n  }\n\n  if (node.lang && node.meta) {\n    const subexit = state.enter(`codeFencedMeta${suffix}`)\n    value += tracker.move(' ')\n    value += tracker.move(\n      state.safe(node.meta, {\n        before: value,\n        after: '\\n',\n        encode: ['`'],\n        ...tracker.current()\n      })\n    )\n    subexit()\n  }\n\n  value += tracker.move('\\n')\n\n  if (raw) {\n    value += tracker.move(raw + '\\n')\n  }\n\n  value += tracker.move(sequence)\n  exit()\n  return value\n}\n\n/** @type {Map} */\nfunction map(line, _, blank) {\n  return (blank ? '' : '    ') + line\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;AACA;;;;AASO,SAAS,KAAK,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI;IACvC,MAAM,SAAS,CAAA,GAAA,mLAAA,CAAA,aAAU,AAAD,EAAE;IAC1B,MAAM,MAAM,KAAK,KAAK,IAAI;IAC1B,MAAM,SAAS,WAAW,MAAM,gBAAgB;IAEhD,IAAI,CAAA,GAAA,qMAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM,QAAQ;QACrC,MAAM,OAAO,MAAM,KAAK,CAAC;QACzB,MAAM,QAAQ,MAAM,WAAW,CAAC,KAAK;QACrC;QACA,OAAO;IACT;IAEA,MAAM,UAAU,MAAM,aAAa,CAAC;IACpC,MAAM,WAAW,OAAO,MAAM,CAAC,KAAK,GAAG,CAAC,CAAA,GAAA,6IAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,UAAU,GAAG;IACxE,MAAM,OAAO,MAAM,KAAK,CAAC;IACzB,IAAI,QAAQ,QAAQ,IAAI,CAAC;IAEzB,IAAI,KAAK,IAAI,EAAE;QACb,MAAM,UAAU,MAAM,KAAK,CAAC,CAAC,cAAc,EAAE,QAAQ;QACrD,SAAS,QAAQ,IAAI,CACnB,MAAM,IAAI,CAAC,KAAK,IAAI,EAAE;YACpB,QAAQ;YACR,OAAO;YACP,QAAQ;gBAAC;aAAI;YACb,GAAG,QAAQ,OAAO,EAAE;QACtB;QAEF;IACF;IAEA,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,EAAE;QAC1B,MAAM,UAAU,MAAM,KAAK,CAAC,CAAC,cAAc,EAAE,QAAQ;QACrD,SAAS,QAAQ,IAAI,CAAC;QACtB,SAAS,QAAQ,IAAI,CACnB,MAAM,IAAI,CAAC,KAAK,IAAI,EAAE;YACpB,QAAQ;YACR,OAAO;YACP,QAAQ;gBAAC;aAAI;YACb,GAAG,QAAQ,OAAO,EAAE;QACtB;QAEF;IACF;IAEA,SAAS,QAAQ,IAAI,CAAC;IAEtB,IAAI,KAAK;QACP,SAAS,QAAQ,IAAI,CAAC,MAAM;IAC9B;IAEA,SAAS,QAAQ,IAAI,CAAC;IACtB;IACA,OAAO;AACT;AAEA,gBAAgB,GAChB,SAAS,IAAI,IAAI,EAAE,CAAC,EAAE,KAAK;IACzB,OAAO,CAAC,QAAQ,KAAK,MAAM,IAAI;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2454, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/util/check-quote.js"], "sourcesContent": ["/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['quote'], null | undefined>}\n */\nexport function checkQuote(state) {\n  const marker = state.options.quote || '\"'\n\n  if (marker !== '\"' && marker !== \"'\") {\n    throw new Error(\n      'Cannot serialize title with `' +\n        marker +\n        '` for `options.quote`, expected `\"`, or `\\'`'\n    )\n  }\n\n  return marker\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;CAGC;;;AACM,SAAS,WAAW,KAAK;IAC9B,MAAM,SAAS,MAAM,OAAO,CAAC,KAAK,IAAI;IAEtC,IAAI,WAAW,OAAO,WAAW,KAAK;QACpC,MAAM,IAAI,MACR,kCACE,SACA;IAEN;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2475, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/handle/definition.js"], "sourcesContent": ["/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Definition, Parents} from 'mdast'\n */\n\nimport {checkQuote} from '../util/check-quote.js'\n\n/**\n * @param {Definition} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function definition(node, _, state, info) {\n  const quote = checkQuote(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const exit = state.enter('definition')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('[')\n  value += tracker.move(\n    state.safe(state.associationId(node), {\n      before: value,\n      after: ']',\n      ...tracker.current()\n    })\n  )\n  value += tracker.move(']: ')\n\n  subexit()\n\n  if (\n    // If there’s no url, or…\n    !node.url ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : '\\n',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  exit()\n\n  return value\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AASO,SAAS,WAAW,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI;IAC7C,MAAM,QAAQ,CAAA,GAAA,mLAAA,CAAA,aAAU,AAAD,EAAE;IACzB,MAAM,SAAS,UAAU,MAAM,UAAU;IACzC,MAAM,OAAO,MAAM,KAAK,CAAC;IACzB,IAAI,UAAU,MAAM,KAAK,CAAC;IAC1B,MAAM,UAAU,MAAM,aAAa,CAAC;IACpC,IAAI,QAAQ,QAAQ,IAAI,CAAC;IACzB,SAAS,QAAQ,IAAI,CACnB,MAAM,IAAI,CAAC,MAAM,aAAa,CAAC,OAAO;QACpC,QAAQ;QACR,OAAO;QACP,GAAG,QAAQ,OAAO,EAAE;IACtB;IAEF,SAAS,QAAQ,IAAI,CAAC;IAEtB;IAEA,IACE,yBAAyB;IACzB,CAAC,KAAK,GAAG,IACT,iDAAiD;IACjD,eAAe,IAAI,CAAC,KAAK,GAAG,GAC5B;QACA,UAAU,MAAM,KAAK,CAAC;QACtB,SAAS,QAAQ,IAAI,CAAC;QACtB,SAAS,QAAQ,IAAI,CACnB,MAAM,IAAI,CAAC,KAAK,GAAG,EAAE;YAAC,QAAQ;YAAO,OAAO;YAAK,GAAG,QAAQ,OAAO,EAAE;QAAA;QAEvE,SAAS,QAAQ,IAAI,CAAC;IACxB,OAAO;QACL,kCAAkC;QAClC,UAAU,MAAM,KAAK,CAAC;QACtB,SAAS,QAAQ,IAAI,CACnB,MAAM,IAAI,CAAC,KAAK,GAAG,EAAE;YACnB,QAAQ;YACR,OAAO,KAAK,KAAK,GAAG,MAAM;YAC1B,GAAG,QAAQ,OAAO,EAAE;QACtB;IAEJ;IAEA;IAEA,IAAI,KAAK,KAAK,EAAE;QACd,UAAU,MAAM,KAAK,CAAC,CAAC,KAAK,EAAE,QAAQ;QACtC,SAAS,QAAQ,IAAI,CAAC,MAAM;QAC5B,SAAS,QAAQ,IAAI,CACnB,MAAM,IAAI,CAAC,KAAK,KAAK,EAAE;YACrB,QAAQ;YACR,OAAO;YACP,GAAG,QAAQ,OAAO,EAAE;QACtB;QAEF,SAAS,QAAQ,IAAI,CAAC;QACtB;IACF;IAEA;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2538, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js"], "sourcesContent": ["/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['emphasis'], null | undefined>}\n */\nexport function checkEmphasis(state) {\n  const marker = state.options.emphasis || '*'\n\n  if (marker !== '*' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize emphasis with `' +\n        marker +\n        '` for `options.emphasis`, expected `*`, or `_`'\n    )\n  }\n\n  return marker\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;CAGC;;;AACM,SAAS,cAAc,KAAK;IACjC,MAAM,SAAS,MAAM,OAAO,CAAC,QAAQ,IAAI;IAEzC,IAAI,WAAW,OAAO,WAAW,KAAK;QACpC,MAAM,IAAI,MACR,qCACE,SACA;IAEN;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2559, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js"], "sourcesContent": ["/**\n * Encode a code point as a character reference.\n *\n * @param {number} code\n *   Code point to encode.\n * @returns {string}\n *   Encoded character reference.\n */\nexport function encodeCharacterReference(code) {\n  return '&#x' + code.toString(16).toUpperCase() + ';'\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACM,SAAS,yBAAyB,IAAI;IAC3C,OAAO,QAAQ,KAAK,QAAQ,CAAC,IAAI,WAAW,KAAK;AACnD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2578, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-util-symbol/lib/constants.js"], "sourcesContent": ["/**\n * This module is compiled away!\n *\n * Parsing markdown comes with a couple of constants, such as minimum or maximum\n * sizes of certain sequences.\n * Additionally, there are a couple symbols used inside micromark.\n * These are all defined here, but compiled away by scripts.\n */\nexport const constants = /** @type {const} */ ({\n  attentionSideAfter: 2, // Symbol to mark an attention sequence as after content: `a*`\n  attentionSideBefore: 1, // Symbol to mark an attention sequence as before content: `*a`\n  atxHeadingOpeningFenceSizeMax: 6, // 6 number signs is fine, 7 isn’t.\n  autolinkDomainSizeMax: 63, // 63 characters is fine, 64 is too many.\n  autolinkSchemeSizeMax: 32, // 32 characters is fine, 33 is too many.\n  cdataOpeningString: 'CDATA[', // And preceded by `<![`.\n  characterGroupPunctuation: 2, // Symbol used to indicate a character is punctuation\n  characterGroupWhitespace: 1, // Symbol used to indicate a character is whitespace\n  characterReferenceDecimalSizeMax: 7, // `&#9999999;`.\n  characterReferenceHexadecimalSizeMax: 6, // `&#xff9999;`.\n  characterReferenceNamedSizeMax: 31, // `&CounterClockwiseContourIntegral;`.\n  codeFencedSequenceSizeMin: 3, // At least 3 ticks or tildes are needed.\n  contentTypeContent: 'content',\n  contentTypeDocument: 'document',\n  contentTypeFlow: 'flow',\n  contentTypeString: 'string',\n  contentTypeText: 'text',\n  hardBreakPrefixSizeMin: 2, // At least 2 trailing spaces are needed.\n  htmlBasic: 6, // Symbol for `<div`\n  htmlCdata: 5, // Symbol for `<![CDATA[]]>`\n  htmlComment: 2, // Symbol for `<!---->`\n  htmlComplete: 7, // Symbol for `<x>`\n  htmlDeclaration: 4, // Symbol for `<!doctype>`\n  htmlInstruction: 3, // Symbol for `<?php?>`\n  htmlRawSizeMax: 8, // Length of `textarea`.\n  htmlRaw: 1, // Symbol for `<script>`\n  linkResourceDestinationBalanceMax: 32, // See: <https://spec.commonmark.org/0.30/#link-destination>, <https://github.com/remarkjs/react-markdown/issues/658#issuecomment-984345577>\n  linkReferenceSizeMax: 999, // See: <https://spec.commonmark.org/0.30/#link-label>\n  listItemValueSizeMax: 10, // See: <https://spec.commonmark.org/0.30/#ordered-list-marker>\n  numericBaseDecimal: 10,\n  numericBaseHexadecimal: 0x10,\n  tabSize: 4, // Tabs have a hard-coded size of 4, per CommonMark.\n  thematicBreakMarkerCountMin: 3, // At least 3 asterisks, dashes, or underscores are needed.\n  v8MaxSafeChunkSize: 10_000 // V8 (and potentially others) have problems injecting giant arrays into other arrays, hence we operate in chunks.\n})\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACM,MAAM,YAAkC;IAC7C,oBAAoB;IACpB,qBAAqB;IACrB,+BAA+B;IAC/B,uBAAuB;IACvB,uBAAuB;IACvB,oBAAoB;IACpB,2BAA2B;IAC3B,0BAA0B;IAC1B,kCAAkC;IAClC,sCAAsC;IACtC,gCAAgC;IAChC,2BAA2B;IAC3B,oBAAoB;IACpB,qBAAqB;IACrB,iBAAiB;IACjB,mBAAmB;IACnB,iBAAiB;IACjB,wBAAwB;IACxB,WAAW;IACX,WAAW;IACX,aAAa;IACb,cAAc;IACd,iBAAiB;IACjB,iBAAiB;IACjB,gBAAgB;IAChB,SAAS;IACT,mCAAmC;IACnC,sBAAsB;IACtB,sBAAsB;IACtB,oBAAoB;IACpB,wBAAwB;IACxB,SAAS;IACT,6BAA6B;IAC7B,oBAAoB,OAAO,kHAAkH;AAC/I", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2630, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-util-classify-character/dev/index.js"], "sourcesContent": ["/**\n * @import {Code} from 'micromark-util-types'\n */\n\nimport {\n  markdownLineEndingOrSpace,\n  unicodePunctuation,\n  unicodeWhitespace\n} from 'micromark-util-character'\nimport {codes, constants} from 'micromark-util-symbol'\n\n/**\n * Classify whether a code represents whitespace, punctuation, or something\n * else.\n *\n * Used for attention (emphasis, strong), whose sequences can open or close\n * based on the class of surrounding characters.\n *\n * > 👉 **Note**: eof (`null`) is seen as whitespace.\n *\n * @param {Code} code\n *   Code.\n * @returns {typeof constants.characterGroupWhitespace | typeof constants.characterGroupPunctuation | undefined}\n *   Group.\n */\nexport function classifyCharacter(code) {\n  if (\n    code === codes.eof ||\n    markdownLineEndingOrSpace(code) ||\n    unicodeWhitespace(code)\n  ) {\n    return constants.characterGroupWhitespace\n  }\n\n  if (unicodePunctuation(code)) {\n    return constants.characterGroupPunctuation\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;AAKA;AAAA;;;AAgBO,SAAS,kBAAkB,IAAI;IACpC,IACE,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,CAAA,GAAA,iKAAA,CAAA,4BAAyB,AAAD,EAAE,SAC1B,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE,OAClB;QACA,OAAO,kKAAA,CAAA,YAAS,CAAC,wBAAwB;IAC3C;IAEA,IAAI,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;QAC5B,OAAO,kKAAA,CAAA,YAAS,CAAC,yBAAyB;IAC5C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2654, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/util/encode-info.js"], "sourcesContent": ["/**\n * @import {EncodeSides} from '../types.js'\n */\n\nimport {classifyCharacter} from 'micromark-util-classify-character'\n\n/**\n * Check whether to encode (as a character reference) the characters\n * surrounding an attention run.\n *\n * Which characters are around an attention run influence whether it works or\n * not.\n *\n * See <https://github.com/orgs/syntax-tree/discussions/60> for more info.\n * See this markdown in a particular renderer to see what works:\n *\n * ```markdown\n * |                         | A (letter inside) | B (punctuation inside) | C (whitespace inside) | D (nothing inside) |\n * | ----------------------- | ----------------- | ---------------------- | --------------------- | ------------------ |\n * | 1 (letter outside)      | x*y*z             | x*.*z                  | x* *z                 | x**z               |\n * | 2 (punctuation outside) | .*y*.             | .*.*.                  | .* *.                 | .**.               |\n * | 3 (whitespace outside)  | x *y* z           | x *.* z                | x * * z               | x ** z             |\n * | 4 (nothing outside)     | *x*               | *.*                    | * *                   | **                 |\n * ```\n *\n * @param {number} outside\n *   Code point on the outer side of the run.\n * @param {number} inside\n *   Code point on the inner side of the run.\n * @param {'*' | '_'} marker\n *   Marker of the run.\n *   Underscores are handled more strictly (they form less often) than\n *   asterisks.\n * @returns {EncodeSides}\n *   Whether to encode characters.\n */\n// Important: punctuation must never be encoded.\n// Punctuation is solely used by markdown constructs.\n// And by encoding itself.\n// Encoding them will break constructs or double encode things.\nexport function encodeInfo(outside, inside, marker) {\n  const outsideKind = classifyCharacter(outside)\n  const insideKind = classifyCharacter(inside)\n\n  // Letter outside:\n  if (outsideKind === undefined) {\n    return insideKind === undefined\n      ? // Letter inside:\n        // we have to encode *both* letters for `_` as it is looser.\n        // it already forms for `*` (and GFMs `~`).\n        marker === '_'\n        ? {inside: true, outside: true}\n        : {inside: false, outside: false}\n      : insideKind === 1\n        ? // Whitespace inside: encode both (letter, whitespace).\n          {inside: true, outside: true}\n        : // Punctuation inside: encode outer (letter)\n          {inside: false, outside: true}\n  }\n\n  // Whitespace outside:\n  if (outsideKind === 1) {\n    return insideKind === undefined\n      ? // Letter inside: already forms.\n        {inside: false, outside: false}\n      : insideKind === 1\n        ? // Whitespace inside: encode both (whitespace).\n          {inside: true, outside: true}\n        : // Punctuation inside: already forms.\n          {inside: false, outside: false}\n  }\n\n  // Punctuation outside:\n  return insideKind === undefined\n    ? // Letter inside: already forms.\n      {inside: false, outside: false}\n    : insideKind === 1\n      ? // Whitespace inside: encode inner (whitespace).\n        {inside: true, outside: false}\n      : // Punctuation inside: already forms.\n        {inside: false, outside: false}\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;;AAoCO,SAAS,WAAW,OAAO,EAAE,MAAM,EAAE,MAAM;IAChD,MAAM,cAAc,CAAA,GAAA,6KAAA,CAAA,oBAAiB,AAAD,EAAE;IACtC,MAAM,aAAa,CAAA,GAAA,6KAAA,CAAA,oBAAiB,AAAD,EAAE;IAErC,kBAAkB;IAClB,IAAI,gBAAgB,WAAW;QAC7B,OAAO,eAAe,YAElB,4DAA4D;QAC5D,2CAA2C;QAC3C,WAAW,MACT;YAAC,QAAQ;YAAM,SAAS;QAAI,IAC5B;YAAC,QAAQ;YAAO,SAAS;QAAK,IAChC,eAAe,IAEb;YAAC,QAAQ;YAAM,SAAS;QAAI,IAE5B;YAAC,QAAQ;YAAO,SAAS;QAAI;IACrC;IAEA,sBAAsB;IACtB,IAAI,gBAAgB,GAAG;QACrB,OAAO,eAAe,YAElB;YAAC,QAAQ;YAAO,SAAS;QAAK,IAC9B,eAAe,IAEb;YAAC,QAAQ;YAAM,SAAS;QAAI,IAE5B;YAAC,QAAQ;YAAO,SAAS;QAAK;IACtC;IAEA,uBAAuB;IACvB,OAAO,eAAe,YAElB;QAAC,QAAQ;QAAO,SAAS;IAAK,IAC9B,eAAe,IAEb;QAAC,QAAQ;QAAM,SAAS;IAAK,IAE7B;QAAC,QAAQ;QAAO,SAAS;IAAK;AACtC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2713, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/handle/emphasis.js"], "sourcesContent": ["/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Emphasis, Parents} from 'mdast'\n */\n\nimport {checkEmphasis} from '../util/check-emphasis.js'\nimport {encodeCharacterReference} from '../util/encode-character-reference.js'\nimport {encodeInfo} from '../util/encode-info.js'\n\nemphasis.peek = emphasisPeek\n\n/**\n * @param {Emphasis} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function emphasis(node, _, state, info) {\n  const marker = checkEmphasis(state)\n  const exit = state.enter('emphasis')\n  const tracker = state.createTracker(info)\n  const before = tracker.move(marker)\n\n  let between = tracker.move(\n    state.containerPhrasing(node, {\n      after: marker,\n      before,\n      ...tracker.current()\n    })\n  )\n  const betweenHead = between.charCodeAt(0)\n  const open = encodeInfo(\n    info.before.charCodeAt(info.before.length - 1),\n    betweenHead,\n    marker\n  )\n\n  if (open.inside) {\n    between = encodeCharacterReference(betweenHead) + between.slice(1)\n  }\n\n  const betweenTail = between.charCodeAt(between.length - 1)\n  const close = encodeInfo(info.after.charCodeAt(0), betweenTail, marker)\n\n  if (close.inside) {\n    between = between.slice(0, -1) + encodeCharacterReference(betweenTail)\n  }\n\n  const after = tracker.move(marker)\n\n  exit()\n\n  state.attentionEncodeSurroundingInfo = {\n    after: close.outside,\n    before: open.outside\n  }\n  return before + between + after\n}\n\n/**\n * @param {Emphasis} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction emphasisPeek(_, _1, state) {\n  return state.options.emphasis || '*'\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;AACA;;;;AAEA,SAAS,IAAI,GAAG;AAST,SAAS,SAAS,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI;IAC3C,MAAM,SAAS,CAAA,GAAA,sLAAA,CAAA,gBAAa,AAAD,EAAE;IAC7B,MAAM,OAAO,MAAM,KAAK,CAAC;IACzB,MAAM,UAAU,MAAM,aAAa,CAAC;IACpC,MAAM,SAAS,QAAQ,IAAI,CAAC;IAE5B,IAAI,UAAU,QAAQ,IAAI,CACxB,MAAM,iBAAiB,CAAC,MAAM;QAC5B,OAAO;QACP;QACA,GAAG,QAAQ,OAAO,EAAE;IACtB;IAEF,MAAM,cAAc,QAAQ,UAAU,CAAC;IACvC,MAAM,OAAO,CAAA,GAAA,mLAAA,CAAA,aAAU,AAAD,EACpB,KAAK,MAAM,CAAC,UAAU,CAAC,KAAK,MAAM,CAAC,MAAM,GAAG,IAC5C,aACA;IAGF,IAAI,KAAK,MAAM,EAAE;QACf,UAAU,CAAA,GAAA,qMAAA,CAAA,2BAAwB,AAAD,EAAE,eAAe,QAAQ,KAAK,CAAC;IAClE;IAEA,MAAM,cAAc,QAAQ,UAAU,CAAC,QAAQ,MAAM,GAAG;IACxD,MAAM,QAAQ,CAAA,GAAA,mLAAA,CAAA,aAAU,AAAD,EAAE,KAAK,KAAK,CAAC,UAAU,CAAC,IAAI,aAAa;IAEhE,IAAI,MAAM,MAAM,EAAE;QAChB,UAAU,QAAQ,KAAK,CAAC,GAAG,CAAC,KAAK,CAAA,GAAA,qMAAA,CAAA,2BAAwB,AAAD,EAAE;IAC5D;IAEA,MAAM,QAAQ,QAAQ,IAAI,CAAC;IAE3B;IAEA,MAAM,8BAA8B,GAAG;QACrC,OAAO,MAAM,OAAO;QACpB,QAAQ,KAAK,OAAO;IACtB;IACA,OAAO,SAAS,UAAU;AAC5B;AAEA;;;;;CAKC,GACD,SAAS,aAAa,CAAC,EAAE,EAAE,EAAE,KAAK;IAChC,OAAO,MAAM,OAAO,CAAC,QAAQ,IAAI;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2768, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/unist-util-visit/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} UnistNode\n * @typedef {import('unist').Parent} UnistParent\n * @typedef {import('unist-util-visit-parents').VisitorResult} VisitorResult\n */\n\n/**\n * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test\n *   Test from `unist-util-is`.\n *\n *   Note: we have remove and add `undefined`, because otherwise when generating\n *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,\n *   which doesn’t work when publishing on npm.\n */\n\n// To do: use types from `unist-util-visit-parents` when it’s released.\n\n/**\n * @typedef {(\n *   Fn extends (value: any) => value is infer Thing\n *   ? Thing\n *   : Fallback\n * )} Predicate\n *   Get the value of a type guard `Fn`.\n * @template Fn\n *   Value; typically function that is a type guard (such as `(x): x is Y`).\n * @template Fallback\n *   Value to yield if `Fn` is not a type guard.\n */\n\n/**\n * @typedef {(\n *   Check extends null | undefined // No test.\n *   ? Value\n *   : Value extends {type: Check} // String (type) test.\n *   ? Value\n *   : Value extends Check // Partial test.\n *   ? Value\n *   : Check extends Function // Function test.\n *   ? Predicate<Check, Value> extends Value\n *     ? Predicate<Check, Value>\n *     : never\n *   : never // Some other test?\n * )} MatchesOne\n *   Check whether a node matches a primitive check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test, but not arrays.\n */\n\n/**\n * @typedef {(\n *   Check extends Array<any>\n *   ? MatchesOne<Value, Check[keyof Check]>\n *   : MatchesOne<Value, Check>\n * )} Matches\n *   Check whether a node matches a check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test.\n */\n\n/**\n * @typedef {0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10} Uint\n *   Number; capped reasonably.\n */\n\n/**\n * @typedef {I extends 0 ? 1 : I extends 1 ? 2 : I extends 2 ? 3 : I extends 3 ? 4 : I extends 4 ? 5 : I extends 5 ? 6 : I extends 6 ? 7 : I extends 7 ? 8 : I extends 8 ? 9 : 10} Increment\n *   Increment a number in the type system.\n * @template {Uint} [I=0]\n *   Index.\n */\n\n/**\n * @typedef {(\n *   Node extends UnistParent\n *   ? Node extends {children: Array<infer Children>}\n *     ? Child extends Children ? Node : never\n *     : never\n *   : never\n * )} InternalParent\n *   Collect nodes that can be parents of `Child`.\n * @template {UnistNode} Node\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {InternalParent<InclusiveDescendant<Tree>, Child>} Parent\n *   Collect nodes in `Tree` that can be parents of `Child`.\n * @template {UnistNode} Tree\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {(\n *   Depth extends Max\n *   ? never\n *   :\n *     | InternalParent<Node, Child>\n *     | InternalAncestor<Node, InternalParent<Node, Child>, Max, Increment<Depth>>\n * )} InternalAncestor\n *   Collect nodes in `Tree` that can be ancestors of `Child`.\n * @template {UnistNode} Node\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n * @template {Uint} [Max=10]\n *   Max; searches up to this depth.\n * @template {Uint} [Depth=0]\n *   Current depth.\n */\n\n/**\n * @typedef {(\n *   Tree extends UnistParent\n *     ? Depth extends Max\n *       ? Tree\n *       : Tree | InclusiveDescendant<Tree['children'][number], Max, Increment<Depth>>\n *     : Tree\n * )} InclusiveDescendant\n *   Collect all (inclusive) descendants of `Tree`.\n *\n *   > 👉 **Note**: for performance reasons, this seems to be the fastest way to\n *   > recurse without actually running into an infinite loop, which the\n *   > previous version did.\n *   >\n *   > Practically, a max of `2` is typically enough assuming a `Root` is\n *   > passed, but it doesn’t improve performance.\n *   > It gets higher with `List > ListItem > Table > TableRow > TableCell`.\n *   > Using up to `10` doesn’t hurt or help either.\n * @template {UnistNode} Tree\n *   Tree type.\n * @template {Uint} [Max=10]\n *   Max; searches up to this depth.\n * @template {Uint} [Depth=0]\n *   Current depth.\n */\n\n/**\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform `parent`.\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of `parent` still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Visited extends UnistNode ? number | undefined : never} index\n *   Index of `node` in `parent`.\n * @param {Ancestor extends UnistParent ? Ancestor | undefined : never} parent\n *   Parent of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n * @template {UnistNode} [Visited=UnistNode]\n *   Visited node type.\n * @template {UnistParent} [Ancestor=UnistParent]\n *   Ancestor type.\n */\n\n/**\n * @typedef {Visitor<Visited, Parent<Ancestor, Visited>>} BuildVisitorFromMatch\n *   Build a typed `Visitor` function from a node and all possible parents.\n *\n *   It will infer which values are passed as `node` and which as `parent`.\n * @template {UnistNode} Visited\n *   Node type.\n * @template {UnistParent} Ancestor\n *   Parent type.\n */\n\n/**\n * @typedef {(\n *   BuildVisitorFromMatch<\n *     Matches<Descendant, Check>,\n *     Extract<Descendant, UnistParent>\n *   >\n * )} BuildVisitorFromDescendants\n *   Build a typed `Visitor` function from a list of descendants and a test.\n *\n *   It will infer which values are passed as `node` and which as `parent`.\n * @template {UnistNode} Descendant\n *   Node type.\n * @template {Test} Check\n *   Test type.\n */\n\n/**\n * @typedef {(\n *   BuildVisitorFromDescendants<\n *     InclusiveDescendant<Tree>,\n *     Check\n *   >\n * )} BuildVisitor\n *   Build a typed `Visitor` function from a tree and a test.\n *\n *   It will infer which values are passed as `node` and which as `parent`.\n * @template {UnistNode} [Tree=UnistNode]\n *   Node type.\n * @template {Test} [Check=Test]\n *   Test type.\n */\n\nimport {visitParents} from 'unist-util-visit-parents'\n\nexport {CONTINUE, EXIT, SKIP} from 'unist-util-visit-parents'\n\n/**\n * Visit nodes.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @overload\n * @param {Tree} tree\n * @param {Check} check\n * @param {BuildVisitor<Tree, Check>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {undefined}\n *\n * @overload\n * @param {Tree} tree\n * @param {BuildVisitor<Tree>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {undefined}\n *\n * @param {UnistNode} tree\n *   Tree to traverse.\n * @param {Visitor | Test} testOrVisitor\n *   `unist-util-is`-compatible test (optional, omit to pass a visitor).\n * @param {Visitor | boolean | null | undefined} [visitorOrReverse]\n *   Handle each node (when test is omitted, pass `reverse`).\n * @param {boolean | null | undefined} [maybeReverse=false]\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns {undefined}\n *   Nothing.\n *\n * @template {UnistNode} Tree\n *   Node type.\n * @template {Test} Check\n *   `unist-util-is`-compatible test.\n */\nexport function visit(tree, testOrVisitor, visitorOrReverse, maybeReverse) {\n  /** @type {boolean | null | undefined} */\n  let reverse\n  /** @type {Test} */\n  let test\n  /** @type {Visitor} */\n  let visitor\n\n  if (\n    typeof testOrVisitor === 'function' &&\n    typeof visitorOrReverse !== 'function'\n  ) {\n    test = undefined\n    visitor = testOrVisitor\n    reverse = visitorOrReverse\n  } else {\n    // @ts-expect-error: assume the overload with test was given.\n    test = testOrVisitor\n    // @ts-expect-error: assume the overload with test was given.\n    visitor = visitorOrReverse\n    reverse = maybeReverse\n  }\n\n  visitParents(tree, test, overload, reverse)\n\n  /**\n   * @param {UnistNode} node\n   * @param {Array<UnistParent>} parents\n   */\n  function overload(node, parents) {\n    const parent = parents[parents.length - 1]\n    const index = parent ? parent.children.indexOf(node) : undefined\n    return visitor(node, index, parent)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC,GAED,uEAAuE;AAEvE;;;;;;;;;;;CAWC,GAED;;;;;;;;;;;;;;;;;;;CAmBC,GAED;;;;;;;;;;;CAWC,GAED;;;CAGC,GAED;;;;;CAKC,GAED;;;;;;;;;;;;;CAaC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;;;;;;;CAiBC,GAED;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqCC,GAED;;;;;;;;;CASC,GAED;;;;;;;;;;;;;;CAcC,GAED;;;;;;;;;;;;;;CAcC;;;AAED;;;AAmDO,SAAS,MAAM,IAAI,EAAE,aAAa,EAAE,gBAAgB,EAAE,YAAY;IACvE,uCAAuC,GACvC,IAAI;IACJ,iBAAiB,GACjB,IAAI;IACJ,oBAAoB,GACpB,IAAI;IAEJ,IACE,OAAO,kBAAkB,cACzB,OAAO,qBAAqB,YAC5B;QACA,OAAO;QACP,UAAU;QACV,UAAU;IACZ,OAAO;QACL,6DAA6D;QAC7D,OAAO;QACP,6DAA6D;QAC7D,UAAU;QACV,UAAU;IACZ;IAEA,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,EAAE,MAAM,MAAM,UAAU;IAEnC;;;GAGC,GACD,SAAS,SAAS,IAAI,EAAE,OAAO;QAC7B,MAAM,SAAS,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;QAC1C,MAAM,QAAQ,SAAS,OAAO,QAAQ,CAAC,OAAO,CAAC,QAAQ;QACvD,OAAO,QAAQ,MAAM,OAAO;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3001, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-string/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('mdast').Nodes} Nodes\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [includeImageAlt=true]\n *   Whether to use `alt` for `image`s (default: `true`).\n * @property {boolean | null | undefined} [includeHtml=true]\n *   Whether to use `value` of HTML (default: `true`).\n */\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Get the text content of a node or list of nodes.\n *\n * Prefers the node’s plain-text fields, otherwise serializes its children,\n * and if the given value is an array, serialize the nodes in it.\n *\n * @param {unknown} [value]\n *   Thing to serialize, typically `Node`.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `value`.\n */\nexport function toString(value, options) {\n  const settings = options || emptyOptions\n  const includeImageAlt =\n    typeof settings.includeImageAlt === 'boolean'\n      ? settings.includeImageAlt\n      : true\n  const includeHtml =\n    typeof settings.includeHtml === 'boolean' ? settings.includeHtml : true\n\n  return one(value, includeImageAlt, includeHtml)\n}\n\n/**\n * One node or several nodes.\n *\n * @param {unknown} value\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(value, includeImageAlt, includeHtml) {\n  if (node(value)) {\n    if ('value' in value) {\n      return value.type === 'html' && !includeHtml ? '' : value.value\n    }\n\n    if (includeImageAlt && 'alt' in value && value.alt) {\n      return value.alt\n    }\n\n    if ('children' in value) {\n      return all(value.children, includeImageAlt, includeHtml)\n    }\n  }\n\n  if (Array.isArray(value)) {\n    return all(value, includeImageAlt, includeHtml)\n  }\n\n  return ''\n}\n\n/**\n * Serialize a list of nodes.\n *\n * @param {Array<unknown>} values\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized nodes.\n */\nfunction all(values, includeImageAlt, includeHtml) {\n  /** @type {Array<string>} */\n  const result = []\n  let index = -1\n\n  while (++index < values.length) {\n    result[index] = one(values[index], includeImageAlt, includeHtml)\n  }\n\n  return result.join('')\n}\n\n/**\n * Check if `value` looks like a node.\n *\n * @param {unknown} value\n *   Thing.\n * @returns {value is Nodes}\n *   Whether `value` is a node.\n */\nfunction node(value) {\n  return Boolean(value && typeof value === 'object')\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC,GAED,oBAAoB;;;AACpB,MAAM,eAAe,CAAC;AAef,SAAS,SAAS,KAAK,EAAE,OAAO;IACrC,MAAM,WAAW,WAAW;IAC5B,MAAM,kBACJ,OAAO,SAAS,eAAe,KAAK,YAChC,SAAS,eAAe,GACxB;IACN,MAAM,cACJ,OAAO,SAAS,WAAW,KAAK,YAAY,SAAS,WAAW,GAAG;IAErE,OAAO,IAAI,OAAO,iBAAiB;AACrC;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,IAAI,KAAK,EAAE,eAAe,EAAE,WAAW;IAC9C,IAAI,KAAK,QAAQ;QACf,IAAI,WAAW,OAAO;YACpB,OAAO,MAAM,IAAI,KAAK,UAAU,CAAC,cAAc,KAAK,MAAM,KAAK;QACjE;QAEA,IAAI,mBAAmB,SAAS,SAAS,MAAM,GAAG,EAAE;YAClD,OAAO,MAAM,GAAG;QAClB;QAEA,IAAI,cAAc,OAAO;YACvB,OAAO,IAAI,MAAM,QAAQ,EAAE,iBAAiB;QAC9C;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,QAAQ;QACxB,OAAO,IAAI,OAAO,iBAAiB;IACrC;IAEA,OAAO;AACT;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,IAAI,MAAM,EAAE,eAAe,EAAE,WAAW;IAC/C,0BAA0B,GAC1B,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;QAC9B,MAAM,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,MAAM,EAAE,iBAAiB;IACtD;IAEA,OAAO,OAAO,IAAI,CAAC;AACrB;AAEA;;;;;;;CAOC,GACD,SAAS,KAAK,KAAK;IACjB,OAAO,QAAQ,SAAS,OAAO,UAAU;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3083, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js"], "sourcesContent": ["/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Heading} from 'mdast'\n */\n\nimport {EXIT, visit} from 'unist-util-visit'\nimport {toString} from 'mdast-util-to-string'\n\n/**\n * @param {Heading} node\n * @param {State} state\n * @returns {boolean}\n */\nexport function formatHeadingAsSetext(node, state) {\n  let literalWithBreak = false\n\n  // Look for literals with a line break.\n  // Note that this also\n  visit(node, function (node) {\n    if (\n      ('value' in node && /\\r?\\n|\\r/.test(node.value)) ||\n      node.type === 'break'\n    ) {\n      literalWithBreak = true\n      return EXIT\n    }\n  })\n\n  return Boolean(\n    (!node.depth || node.depth < 3) &&\n      toString(node) &&\n      (state.options.setext || literalWithBreak)\n  )\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AAAA;AACA;;;AAOO,SAAS,sBAAsB,IAAI,EAAE,KAAK;IAC/C,IAAI,mBAAmB;IAEvB,uCAAuC;IACvC,sBAAsB;IACtB,CAAA,GAAA,yKAAA,CAAA,QAAK,AAAD,EAAE,MAAM,SAAU,IAAI;QACxB,IACE,AAAC,WAAW,QAAQ,WAAW,IAAI,CAAC,KAAK,KAAK,KAC9C,KAAK,IAAI,KAAK,SACd;YACA,mBAAmB;YACnB,OAAO,oKAAA,CAAA,OAAI;QACb;IACF;IAEA,OAAO,QACL,CAAC,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,CAAC,KAC5B,CAAA,GAAA,gKAAA,CAAA,WAAQ,AAAD,EAAE,SACT,CAAC,MAAM,OAAO,CAAC,MAAM,IAAI,gBAAgB;AAE/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3112, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/handle/heading.js"], "sourcesContent": ["/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Heading, Parents} from 'mdast'\n */\n\nimport {encodeCharacterReference} from '../util/encode-character-reference.js'\nimport {formatHeadingAsSetext} from '../util/format-heading-as-setext.js'\n\n/**\n * @param {Heading} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function heading(node, _, state, info) {\n  const rank = Math.max(Math.min(6, node.depth || 1), 1)\n  const tracker = state.createTracker(info)\n\n  if (formatHeadingAsSetext(node, state)) {\n    const exit = state.enter('headingSetext')\n    const subexit = state.enter('phrasing')\n    const value = state.containerPhrasing(node, {\n      ...tracker.current(),\n      before: '\\n',\n      after: '\\n'\n    })\n    subexit()\n    exit()\n\n    return (\n      value +\n      '\\n' +\n      (rank === 1 ? '=' : '-').repeat(\n        // The whole size…\n        value.length -\n          // Minus the position of the character after the last EOL (or\n          // 0 if there is none)…\n          (Math.max(value.lastIndexOf('\\r'), value.lastIndexOf('\\n')) + 1)\n      )\n    )\n  }\n\n  const sequence = '#'.repeat(rank)\n  const exit = state.enter('headingAtx')\n  const subexit = state.enter('phrasing')\n\n  // Note: for proper tracking, we should reset the output positions when there\n  // is no content returned, because then the space is not output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  tracker.move(sequence + ' ')\n\n  let value = state.containerPhrasing(node, {\n    before: '# ',\n    after: '\\n',\n    ...tracker.current()\n  })\n\n  if (/^[\\t ]/.test(value)) {\n    // To do: what effect has the character reference on tracking?\n    value = encodeCharacterReference(value.charCodeAt(0)) + value.slice(1)\n  }\n\n  value = value ? sequence + ' ' + value : sequence\n\n  if (state.options.closeAtx) {\n    value += ' ' + sequence\n  }\n\n  subexit()\n  exit()\n\n  return value\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;;;AASO,SAAS,QAAQ,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI;IAC1C,MAAM,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,IAAI,IAAI;IACpD,MAAM,UAAU,MAAM,aAAa,CAAC;IAEpC,IAAI,CAAA,GAAA,sMAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM,QAAQ;QACtC,MAAM,OAAO,MAAM,KAAK,CAAC;QACzB,MAAM,UAAU,MAAM,KAAK,CAAC;QAC5B,MAAM,QAAQ,MAAM,iBAAiB,CAAC,MAAM;YAC1C,GAAG,QAAQ,OAAO,EAAE;YACpB,QAAQ;YACR,OAAO;QACT;QACA;QACA;QAEA,OACE,QACA,OACA,CAAC,SAAS,IAAI,MAAM,GAAG,EAAE,MAAM,CAC7B,kBAAkB;QAClB,MAAM,MAAM,GACV,6DAA6D;QAC7D,uBAAuB;QACvB,CAAC,KAAK,GAAG,CAAC,MAAM,WAAW,CAAC,OAAO,MAAM,WAAW,CAAC,SAAS,CAAC;IAGvE;IAEA,MAAM,WAAW,IAAI,MAAM,CAAC;IAC5B,MAAM,OAAO,MAAM,KAAK,CAAC;IACzB,MAAM,UAAU,MAAM,KAAK,CAAC;IAE5B,6EAA6E;IAC7E,gEAAgE;IAChE,4EAA4E;IAC5E,yCAAyC;IACzC,QAAQ,IAAI,CAAC,WAAW;IAExB,IAAI,QAAQ,MAAM,iBAAiB,CAAC,MAAM;QACxC,QAAQ;QACR,OAAO;QACP,GAAG,QAAQ,OAAO,EAAE;IACtB;IAEA,IAAI,SAAS,IAAI,CAAC,QAAQ;QACxB,8DAA8D;QAC9D,QAAQ,CAAA,GAAA,qMAAA,CAAA,2BAAwB,AAAD,EAAE,MAAM,UAAU,CAAC,MAAM,MAAM,KAAK,CAAC;IACtE;IAEA,QAAQ,QAAQ,WAAW,MAAM,QAAQ;IAEzC,IAAI,MAAM,OAAO,CAAC,QAAQ,EAAE;QAC1B,SAAS,MAAM;IACjB;IAEA;IACA;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3171, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/handle/html.js"], "sourcesContent": ["/**\n * @import {Html} from 'mdast'\n */\n\nhtml.peek = htmlPeek\n\n/**\n * @param {Html} node\n * @returns {string}\n */\nexport function html(node) {\n  return node.value || ''\n}\n\n/**\n * @returns {string}\n */\nfunction htmlPeek() {\n  return '<'\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED,KAAK,IAAI,GAAG;AAML,SAAS,KAAK,IAAI;IACvB,OAAO,KAAK,KAAK,IAAI;AACvB;AAEA;;CAEC,GACD,SAAS;IACP,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3191, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/handle/image.js"], "sourcesContent": ["/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Image, Parents} from 'mdast'\n */\n\nimport {checkQuote} from '../util/check-quote.js'\n\nimage.peek = imagePeek\n\n/**\n * @param {Image} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function image(node, _, state, info) {\n  const quote = checkQuote(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const exit = state.enter('image')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('![')\n  value += tracker.move(\n    state.safe(node.alt, {before: value, after: ']', ...tracker.current()})\n  )\n  value += tracker.move('](')\n\n  subexit()\n\n  if (\n    // If there’s no url but there is a title…\n    (!node.url && node.title) ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : ')',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  value += tracker.move(')')\n  exit()\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction imagePeek() {\n  return '!'\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AAEA,MAAM,IAAI,GAAG;AASN,SAAS,MAAM,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI;IACxC,MAAM,QAAQ,CAAA,GAAA,mLAAA,CAAA,aAAU,AAAD,EAAE;IACzB,MAAM,SAAS,UAAU,MAAM,UAAU;IACzC,MAAM,OAAO,MAAM,KAAK,CAAC;IACzB,IAAI,UAAU,MAAM,KAAK,CAAC;IAC1B,MAAM,UAAU,MAAM,aAAa,CAAC;IACpC,IAAI,QAAQ,QAAQ,IAAI,CAAC;IACzB,SAAS,QAAQ,IAAI,CACnB,MAAM,IAAI,CAAC,KAAK,GAAG,EAAE;QAAC,QAAQ;QAAO,OAAO;QAAK,GAAG,QAAQ,OAAO,EAAE;IAAA;IAEvE,SAAS,QAAQ,IAAI,CAAC;IAEtB;IAEA,IACE,0CAA0C;IACzC,CAAC,KAAK,GAAG,IAAI,KAAK,KAAK,IACxB,iDAAiD;IACjD,eAAe,IAAI,CAAC,KAAK,GAAG,GAC5B;QACA,UAAU,MAAM,KAAK,CAAC;QACtB,SAAS,QAAQ,IAAI,CAAC;QACtB,SAAS,QAAQ,IAAI,CACnB,MAAM,IAAI,CAAC,KAAK,GAAG,EAAE;YAAC,QAAQ;YAAO,OAAO;YAAK,GAAG,QAAQ,OAAO,EAAE;QAAA;QAEvE,SAAS,QAAQ,IAAI,CAAC;IACxB,OAAO;QACL,kCAAkC;QAClC,UAAU,MAAM,KAAK,CAAC;QACtB,SAAS,QAAQ,IAAI,CACnB,MAAM,IAAI,CAAC,KAAK,GAAG,EAAE;YACnB,QAAQ;YACR,OAAO,KAAK,KAAK,GAAG,MAAM;YAC1B,GAAG,QAAQ,OAAO,EAAE;QACtB;IAEJ;IAEA;IAEA,IAAI,KAAK,KAAK,EAAE;QACd,UAAU,MAAM,KAAK,CAAC,CAAC,KAAK,EAAE,QAAQ;QACtC,SAAS,QAAQ,IAAI,CAAC,MAAM;QAC5B,SAAS,QAAQ,IAAI,CACnB,MAAM,IAAI,CAAC,KAAK,KAAK,EAAE;YACrB,QAAQ;YACR,OAAO;YACP,GAAG,QAAQ,OAAO,EAAE;QACtB;QAEF,SAAS,QAAQ,IAAI,CAAC;QACtB;IACF;IAEA,SAAS,QAAQ,IAAI,CAAC;IACtB;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS;IACP,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3261, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/handle/image-reference.js"], "sourcesContent": ["/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {ImageReference, Parents} from 'mdast'\n */\n\nimageReference.peek = imageReferencePeek\n\n/**\n * @param {ImageReference} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function imageReference(node, _, state, info) {\n  const type = node.referenceType\n  const exit = state.enter('imageReference')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('![')\n  const alt = state.safe(node.alt, {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  value += tracker.move(alt + '][')\n\n  subexit()\n  // Hide the fact that we’re in phrasing, because escapes don’t work.\n  const stack = state.stack\n  state.stack = []\n  subexit = state.enter('reference')\n  // Note: for proper tracking, we should reset the output positions when we end\n  // up making a `shortcut` reference, because then there is no brace output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  const reference = state.safe(state.associationId(node), {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  subexit()\n  state.stack = stack\n  exit()\n\n  if (type === 'full' || !alt || alt !== reference) {\n    value += tracker.move(reference + ']')\n  } else if (type === 'shortcut') {\n    // Remove the unwanted `[`.\n    value = value.slice(0, -1)\n  } else {\n    value += tracker.move(']')\n  }\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction imageReferencePeek() {\n  return '!'\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED,eAAe,IAAI,GAAG;AASf,SAAS,eAAe,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI;IACjD,MAAM,OAAO,KAAK,aAAa;IAC/B,MAAM,OAAO,MAAM,KAAK,CAAC;IACzB,IAAI,UAAU,MAAM,KAAK,CAAC;IAC1B,MAAM,UAAU,MAAM,aAAa,CAAC;IACpC,IAAI,QAAQ,QAAQ,IAAI,CAAC;IACzB,MAAM,MAAM,MAAM,IAAI,CAAC,KAAK,GAAG,EAAE;QAC/B,QAAQ;QACR,OAAO;QACP,GAAG,QAAQ,OAAO,EAAE;IACtB;IACA,SAAS,QAAQ,IAAI,CAAC,MAAM;IAE5B;IACA,oEAAoE;IACpE,MAAM,QAAQ,MAAM,KAAK;IACzB,MAAM,KAAK,GAAG,EAAE;IAChB,UAAU,MAAM,KAAK,CAAC;IACtB,8EAA8E;IAC9E,2EAA2E;IAC3E,4EAA4E;IAC5E,yCAAyC;IACzC,MAAM,YAAY,MAAM,IAAI,CAAC,MAAM,aAAa,CAAC,OAAO;QACtD,QAAQ;QACR,OAAO;QACP,GAAG,QAAQ,OAAO,EAAE;IACtB;IACA;IACA,MAAM,KAAK,GAAG;IACd;IAEA,IAAI,SAAS,UAAU,CAAC,OAAO,QAAQ,WAAW;QAChD,SAAS,QAAQ,IAAI,CAAC,YAAY;IACpC,OAAO,IAAI,SAAS,YAAY;QAC9B,2BAA2B;QAC3B,QAAQ,MAAM,KAAK,CAAC,GAAG,CAAC;IAC1B,OAAO;QACL,SAAS,QAAQ,IAAI,CAAC;IACxB;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS;IACP,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3318, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/handle/inline-code.js"], "sourcesContent": ["/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {InlineCode, Parents} from 'mdast'\n */\n\ninlineCode.peek = inlineCodePeek\n\n/**\n * @param {InlineCode} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @returns {string}\n */\nexport function inlineCode(node, _, state) {\n  let value = node.value || ''\n  let sequence = '`'\n  let index = -1\n\n  // If there is a single grave accent on its own in the code, use a fence of\n  // two.\n  // If there are two in a row, use one.\n  while (new RegExp('(^|[^`])' + sequence + '([^`]|$)').test(value)) {\n    sequence += '`'\n  }\n\n  // If this is not just spaces or eols (tabs don’t count), and either the\n  // first or last character are a space, eol, or tick, then pad with spaces.\n  if (\n    /[^ \\r\\n]/.test(value) &&\n    ((/^[ \\r\\n]/.test(value) && /[ \\r\\n]$/.test(value)) || /^`|`$/.test(value))\n  ) {\n    value = ' ' + value + ' '\n  }\n\n  // We have a potential problem: certain characters after eols could result in\n  // blocks being seen.\n  // For example, if someone injected the string `'\\n# b'`, then that would\n  // result in an ATX heading.\n  // We can’t escape characters in `inlineCode`, but because eols are\n  // transformed to spaces when going from markdown to HTML anyway, we can swap\n  // them out.\n  while (++index < state.unsafe.length) {\n    const pattern = state.unsafe[index]\n    const expression = state.compilePattern(pattern)\n    /** @type {RegExpExecArray | null} */\n    let match\n\n    // Only look for `atBreak`s.\n    // Btw: note that `atBreak` patterns will always start the regex at LF or\n    // CR.\n    if (!pattern.atBreak) continue\n\n    while ((match = expression.exec(value))) {\n      let position = match.index\n\n      // Support CRLF (patterns only look for one of the characters).\n      if (\n        value.charCodeAt(position) === 10 /* `\\n` */ &&\n        value.charCodeAt(position - 1) === 13 /* `\\r` */\n      ) {\n        position--\n      }\n\n      value = value.slice(0, position) + ' ' + value.slice(match.index + 1)\n    }\n  }\n\n  return sequence + value + sequence\n}\n\n/**\n * @returns {string}\n */\nfunction inlineCodePeek() {\n  return '`'\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED,WAAW,IAAI,GAAG;AAQX,SAAS,WAAW,IAAI,EAAE,CAAC,EAAE,KAAK;IACvC,IAAI,QAAQ,KAAK,KAAK,IAAI;IAC1B,IAAI,WAAW;IACf,IAAI,QAAQ,CAAC;IAEb,2EAA2E;IAC3E,OAAO;IACP,sCAAsC;IACtC,MAAO,IAAI,OAAO,aAAa,WAAW,YAAY,IAAI,CAAC,OAAQ;QACjE,YAAY;IACd;IAEA,wEAAwE;IACxE,2EAA2E;IAC3E,IACE,WAAW,IAAI,CAAC,UAChB,CAAC,AAAC,WAAW,IAAI,CAAC,UAAU,WAAW,IAAI,CAAC,UAAW,QAAQ,IAAI,CAAC,MAAM,GAC1E;QACA,QAAQ,MAAM,QAAQ;IACxB;IAEA,6EAA6E;IAC7E,qBAAqB;IACrB,yEAAyE;IACzE,4BAA4B;IAC5B,mEAAmE;IACnE,6EAA6E;IAC7E,YAAY;IACZ,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAE;QACpC,MAAM,UAAU,MAAM,MAAM,CAAC,MAAM;QACnC,MAAM,aAAa,MAAM,cAAc,CAAC;QACxC,mCAAmC,GACnC,IAAI;QAEJ,4BAA4B;QAC5B,yEAAyE;QACzE,MAAM;QACN,IAAI,CAAC,QAAQ,OAAO,EAAE;QAEtB,MAAQ,QAAQ,WAAW,IAAI,CAAC,OAAS;YACvC,IAAI,WAAW,MAAM,KAAK;YAE1B,+DAA+D;YAC/D,IACE,MAAM,UAAU,CAAC,cAAc,GAAG,QAAQ,OAC1C,MAAM,UAAU,CAAC,WAAW,OAAO,GAAG,QAAQ,KAC9C;gBACA;YACF;YAEA,QAAQ,MAAM,KAAK,CAAC,GAAG,YAAY,MAAM,MAAM,KAAK,CAAC,MAAM,KAAK,GAAG;QACrE;IACF;IAEA,OAAO,WAAW,QAAQ;AAC5B;AAEA;;CAEC,GACD,SAAS;IACP,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3377, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js"], "sourcesContent": ["/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Link} from 'mdast'\n */\n\nimport {toString} from 'mdast-util-to-string'\n\n/**\n * @param {Link} node\n * @param {State} state\n * @returns {boolean}\n */\nexport function formatLinkAsAutolink(node, state) {\n  const raw = toString(node)\n\n  return Boolean(\n    !state.options.resourceLink &&\n      // If there’s a url…\n      node.url &&\n      // And there’s a no title…\n      !node.title &&\n      // And the content of `node` is a single text node…\n      node.children &&\n      node.children.length === 1 &&\n      node.children[0].type === 'text' &&\n      // And if the url is the same as the content…\n      (raw === node.url || 'mailto:' + raw === node.url) &&\n      // And that starts w/ a protocol…\n      /^[a-z][a-z+.-]+:/i.test(node.url) &&\n      // And that doesn’t contain ASCII control codes (character escapes and\n      // references don’t work), space, or angle brackets…\n      !/[\\0- <>\\u007F]/.test(node.url)\n  )\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AAOO,SAAS,qBAAqB,IAAI,EAAE,KAAK;IAC9C,MAAM,MAAM,CAAA,GAAA,gKAAA,CAAA,WAAQ,AAAD,EAAE;IAErB,OAAO,QACL,CAAC,MAAM,OAAO,CAAC,YAAY,IACzB,oBAAoB;IACpB,KAAK,GAAG,IACR,0BAA0B;IAC1B,CAAC,KAAK,KAAK,IACX,mDAAmD;IACnD,KAAK,QAAQ,IACb,KAAK,QAAQ,CAAC,MAAM,KAAK,KACzB,KAAK,QAAQ,CAAC,EAAE,CAAC,IAAI,KAAK,UAC1B,6CAA6C;IAC7C,CAAC,QAAQ,KAAK,GAAG,IAAI,YAAY,QAAQ,KAAK,GAAG,KACjD,iCAAiC;IACjC,oBAAoB,IAAI,CAAC,KAAK,GAAG,KACjC,sEAAsE;IACtE,oDAAoD;IACpD,CAAC,iBAAiB,IAAI,CAAC,KAAK,GAAG;AAErC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3402, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/handle/link.js"], "sourcesContent": ["/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Link, Parents} from 'mdast'\n * @import {Exit} from '../types.js'\n */\n\nimport {checkQuote} from '../util/check-quote.js'\nimport {formatLinkAsAutolink} from '../util/format-link-as-autolink.js'\n\nlink.peek = linkPeek\n\n/**\n * @param {Link} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function link(node, _, state, info) {\n  const quote = checkQuote(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const tracker = state.createTracker(info)\n  /** @type {Exit} */\n  let exit\n  /** @type {Exit} */\n  let subexit\n\n  if (formatLinkAsAutolink(node, state)) {\n    // Hide the fact that we’re in phrasing, because escapes don’t work.\n    const stack = state.stack\n    state.stack = []\n    exit = state.enter('autolink')\n    let value = tracker.move('<')\n    value += tracker.move(\n      state.containerPhrasing(node, {\n        before: value,\n        after: '>',\n        ...tracker.current()\n      })\n    )\n    value += tracker.move('>')\n    exit()\n    state.stack = stack\n    return value\n  }\n\n  exit = state.enter('link')\n  subexit = state.enter('label')\n  let value = tracker.move('[')\n  value += tracker.move(\n    state.containerPhrasing(node, {\n      before: value,\n      after: '](',\n      ...tracker.current()\n    })\n  )\n  value += tracker.move('](')\n  subexit()\n\n  if (\n    // If there’s no url but there is a title…\n    (!node.url && node.title) ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : ')',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  value += tracker.move(')')\n\n  exit()\n  return value\n}\n\n/**\n * @param {Link} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @returns {string}\n */\nfunction linkPeek(node, _, state) {\n  return formatLinkAsAutolink(node, state) ? '<' : '['\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;AACA;;;AAEA,KAAK,IAAI,GAAG;AASL,SAAS,KAAK,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI;IACvC,MAAM,QAAQ,CAAA,GAAA,mLAAA,CAAA,aAAU,AAAD,EAAE;IACzB,MAAM,SAAS,UAAU,MAAM,UAAU;IACzC,MAAM,UAAU,MAAM,aAAa,CAAC;IACpC,iBAAiB,GACjB,IAAI;IACJ,iBAAiB,GACjB,IAAI;IAEJ,IAAI,CAAA,GAAA,qMAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM,QAAQ;QACrC,oEAAoE;QACpE,MAAM,QAAQ,MAAM,KAAK;QACzB,MAAM,KAAK,GAAG,EAAE;QAChB,OAAO,MAAM,KAAK,CAAC;QACnB,IAAI,QAAQ,QAAQ,IAAI,CAAC;QACzB,SAAS,QAAQ,IAAI,CACnB,MAAM,iBAAiB,CAAC,MAAM;YAC5B,QAAQ;YACR,OAAO;YACP,GAAG,QAAQ,OAAO,EAAE;QACtB;QAEF,SAAS,QAAQ,IAAI,CAAC;QACtB;QACA,MAAM,KAAK,GAAG;QACd,OAAO;IACT;IAEA,OAAO,MAAM,KAAK,CAAC;IACnB,UAAU,MAAM,KAAK,CAAC;IACtB,IAAI,QAAQ,QAAQ,IAAI,CAAC;IACzB,SAAS,QAAQ,IAAI,CACnB,MAAM,iBAAiB,CAAC,MAAM;QAC5B,QAAQ;QACR,OAAO;QACP,GAAG,QAAQ,OAAO,EAAE;IACtB;IAEF,SAAS,QAAQ,IAAI,CAAC;IACtB;IAEA,IACE,0CAA0C;IACzC,CAAC,KAAK,GAAG,IAAI,KAAK,KAAK,IACxB,iDAAiD;IACjD,eAAe,IAAI,CAAC,KAAK,GAAG,GAC5B;QACA,UAAU,MAAM,KAAK,CAAC;QACtB,SAAS,QAAQ,IAAI,CAAC;QACtB,SAAS,QAAQ,IAAI,CACnB,MAAM,IAAI,CAAC,KAAK,GAAG,EAAE;YAAC,QAAQ;YAAO,OAAO;YAAK,GAAG,QAAQ,OAAO,EAAE;QAAA;QAEvE,SAAS,QAAQ,IAAI,CAAC;IACxB,OAAO;QACL,kCAAkC;QAClC,UAAU,MAAM,KAAK,CAAC;QACtB,SAAS,QAAQ,IAAI,CACnB,MAAM,IAAI,CAAC,KAAK,GAAG,EAAE;YACnB,QAAQ;YACR,OAAO,KAAK,KAAK,GAAG,MAAM;YAC1B,GAAG,QAAQ,OAAO,EAAE;QACtB;IAEJ;IAEA;IAEA,IAAI,KAAK,KAAK,EAAE;QACd,UAAU,MAAM,KAAK,CAAC,CAAC,KAAK,EAAE,QAAQ;QACtC,SAAS,QAAQ,IAAI,CAAC,MAAM;QAC5B,SAAS,QAAQ,IAAI,CACnB,MAAM,IAAI,CAAC,KAAK,KAAK,EAAE;YACrB,QAAQ;YACR,OAAO;YACP,GAAG,QAAQ,OAAO,EAAE;QACtB;QAEF,SAAS,QAAQ,IAAI,CAAC;QACtB;IACF;IAEA,SAAS,QAAQ,IAAI,CAAC;IAEtB;IACA,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,SAAS,IAAI,EAAE,CAAC,EAAE,KAAK;IAC9B,OAAO,CAAA,GAAA,qMAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM,SAAS,MAAM;AACnD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3496, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/handle/link-reference.js"], "sourcesContent": ["/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {LinkReference, Parents} from 'mdast'\n */\n\nlinkReference.peek = linkReferencePeek\n\n/**\n * @param {LinkReference} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function linkReference(node, _, state, info) {\n  const type = node.referenceType\n  const exit = state.enter('linkReference')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('[')\n  const text = state.containerPhrasing(node, {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  value += tracker.move(text + '][')\n\n  subexit()\n  // Hide the fact that we’re in phrasing, because escapes don’t work.\n  const stack = state.stack\n  state.stack = []\n  subexit = state.enter('reference')\n  // Note: for proper tracking, we should reset the output positions when we end\n  // up making a `shortcut` reference, because then there is no brace output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  const reference = state.safe(state.associationId(node), {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  subexit()\n  state.stack = stack\n  exit()\n\n  if (type === 'full' || !text || text !== reference) {\n    value += tracker.move(reference + ']')\n  } else if (type === 'shortcut') {\n    // Remove the unwanted `[`.\n    value = value.slice(0, -1)\n  } else {\n    value += tracker.move(']')\n  }\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction linkReferencePeek() {\n  return '['\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED,cAAc,IAAI,GAAG;AASd,SAAS,cAAc,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI;IAChD,MAAM,OAAO,KAAK,aAAa;IAC/B,MAAM,OAAO,MAAM,KAAK,CAAC;IACzB,IAAI,UAAU,MAAM,KAAK,CAAC;IAC1B,MAAM,UAAU,MAAM,aAAa,CAAC;IACpC,IAAI,QAAQ,QAAQ,IAAI,CAAC;IACzB,MAAM,OAAO,MAAM,iBAAiB,CAAC,MAAM;QACzC,QAAQ;QACR,OAAO;QACP,GAAG,QAAQ,OAAO,EAAE;IACtB;IACA,SAAS,QAAQ,IAAI,CAAC,OAAO;IAE7B;IACA,oEAAoE;IACpE,MAAM,QAAQ,MAAM,KAAK;IACzB,MAAM,KAAK,GAAG,EAAE;IAChB,UAAU,MAAM,KAAK,CAAC;IACtB,8EAA8E;IAC9E,2EAA2E;IAC3E,4EAA4E;IAC5E,yCAAyC;IACzC,MAAM,YAAY,MAAM,IAAI,CAAC,MAAM,aAAa,CAAC,OAAO;QACtD,QAAQ;QACR,OAAO;QACP,GAAG,QAAQ,OAAO,EAAE;IACtB;IACA;IACA,MAAM,KAAK,GAAG;IACd;IAEA,IAAI,SAAS,UAAU,CAAC,QAAQ,SAAS,WAAW;QAClD,SAAS,QAAQ,IAAI,CAAC,YAAY;IACpC,OAAO,IAAI,SAAS,YAAY;QAC9B,2BAA2B;QAC3B,QAAQ,MAAM,KAAK,CAAC,GAAG,CAAC;IAC1B,OAAO;QACL,SAAS,QAAQ,IAAI,CAAC;IACxB;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS;IACP,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3553, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/util/check-bullet.js"], "sourcesContent": ["/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */\nexport function checkBullet(state) {\n  const marker = state.options.bullet || '*'\n\n  if (marker !== '*' && marker !== '+' && marker !== '-') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        marker +\n        '` for `options.bullet`, expected `*`, `+`, or `-`'\n    )\n  }\n\n  return marker\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;CAGC;;;AACM,SAAS,YAAY,KAAK;IAC/B,MAAM,SAAS,MAAM,OAAO,CAAC,MAAM,IAAI;IAEvC,IAAI,WAAW,OAAO,WAAW,OAAO,WAAW,KAAK;QACtD,MAAM,IAAI,MACR,kCACE,SACA;IAEN;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3574, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js"], "sourcesContent": ["/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\nimport {checkBullet} from './check-bullet.js'\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */\nexport function checkBulletOther(state) {\n  const bullet = checkBullet(state)\n  const bulletOther = state.options.bulletOther\n\n  if (!bulletOther) {\n    return bullet === '*' ? '-' : '*'\n  }\n\n  if (bulletOther !== '*' && bulletOther !== '+' && bulletOther !== '-') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        bulletOther +\n        '` for `options.bulletOther`, expected `*`, `+`, or `-`'\n    )\n  }\n\n  if (bulletOther === bullet) {\n    throw new Error(\n      'Expected `bullet` (`' +\n        bullet +\n        '`) and `bulletOther` (`' +\n        bulletOther +\n        '`) to be different'\n    )\n  }\n\n  return bulletOther\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;;AAMO,SAAS,iBAAiB,KAAK;IACpC,MAAM,SAAS,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD,EAAE;IAC3B,MAAM,cAAc,MAAM,OAAO,CAAC,WAAW;IAE7C,IAAI,CAAC,aAAa;QAChB,OAAO,WAAW,MAAM,MAAM;IAChC;IAEA,IAAI,gBAAgB,OAAO,gBAAgB,OAAO,gBAAgB,KAAK;QACrE,MAAM,IAAI,MACR,kCACE,cACA;IAEN;IAEA,IAAI,gBAAgB,QAAQ;QAC1B,MAAM,IAAI,MACR,yBACE,SACA,4BACA,cACA;IAEN;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3601, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js"], "sourcesContent": ["/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bulletOrdered'], null | undefined>}\n */\nexport function checkBulletOrdered(state) {\n  const marker = state.options.bulletOrdered || '.'\n\n  if (marker !== '.' && marker !== ')') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        marker +\n        '` for `options.bulletOrdered`, expected `.` or `)`'\n    )\n  }\n\n  return marker\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;CAGC;;;AACM,SAAS,mBAAmB,KAAK;IACtC,MAAM,SAAS,MAAM,OAAO,CAAC,aAAa,IAAI;IAE9C,IAAI,WAAW,OAAO,WAAW,KAAK;QACpC,MAAM,IAAI,MACR,kCACE,SACA;IAEN;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3622, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/util/check-rule.js"], "sourcesContent": ["/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['rule'], null | undefined>}\n */\nexport function checkRule(state) {\n  const marker = state.options.rule || '*'\n\n  if (marker !== '*' && marker !== '-' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize rules with `' +\n        marker +\n        '` for `options.rule`, expected `*`, `-`, or `_`'\n    )\n  }\n\n  return marker\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;CAGC;;;AACM,SAAS,UAAU,KAAK;IAC7B,MAAM,SAAS,MAAM,OAAO,CAAC,IAAI,IAAI;IAErC,IAAI,WAAW,OAAO,WAAW,OAAO,WAAW,KAAK;QACtD,MAAM,IAAI,MACR,kCACE,SACA;IAEN;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3643, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/handle/list.js"], "sourcesContent": ["/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {List, Parents} from 'mdast'\n */\n\nimport {checkBullet} from '../util/check-bullet.js'\nimport {checkBulletOther} from '../util/check-bullet-other.js'\nimport {checkBulletOrdered} from '../util/check-bullet-ordered.js'\nimport {checkRule} from '../util/check-rule.js'\n\n/**\n * @param {List} node\n * @param {Parents | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function list(node, parent, state, info) {\n  const exit = state.enter('list')\n  const bulletCurrent = state.bulletCurrent\n  /** @type {string} */\n  let bullet = node.ordered ? checkBulletOrdered(state) : checkBullet(state)\n  /** @type {string} */\n  const bulletOther = node.ordered\n    ? bullet === '.'\n      ? ')'\n      : '.'\n    : checkBulletOther(state)\n  let useDifferentMarker =\n    parent && state.bulletLastUsed ? bullet === state.bulletLastUsed : false\n\n  if (!node.ordered) {\n    const firstListItem = node.children ? node.children[0] : undefined\n\n    // If there’s an empty first list item directly in two list items,\n    // we have to use a different bullet:\n    //\n    // ```markdown\n    // * - *\n    // ```\n    //\n    // …because otherwise it would become one big thematic break.\n    if (\n      // Bullet could be used as a thematic break marker:\n      (bullet === '*' || bullet === '-') &&\n      // Empty first list item:\n      firstListItem &&\n      (!firstListItem.children || !firstListItem.children[0]) &&\n      // Directly in two other list items:\n      state.stack[state.stack.length - 1] === 'list' &&\n      state.stack[state.stack.length - 2] === 'listItem' &&\n      state.stack[state.stack.length - 3] === 'list' &&\n      state.stack[state.stack.length - 4] === 'listItem' &&\n      // That are each the first child.\n      state.indexStack[state.indexStack.length - 1] === 0 &&\n      state.indexStack[state.indexStack.length - 2] === 0 &&\n      state.indexStack[state.indexStack.length - 3] === 0\n    ) {\n      useDifferentMarker = true\n    }\n\n    // If there’s a thematic break at the start of the first list item,\n    // we have to use a different bullet:\n    //\n    // ```markdown\n    // * ---\n    // ```\n    //\n    // …because otherwise it would become one big thematic break.\n    if (checkRule(state) === bullet && firstListItem) {\n      let index = -1\n\n      while (++index < node.children.length) {\n        const item = node.children[index]\n\n        if (\n          item &&\n          item.type === 'listItem' &&\n          item.children &&\n          item.children[0] &&\n          item.children[0].type === 'thematicBreak'\n        ) {\n          useDifferentMarker = true\n          break\n        }\n      }\n    }\n  }\n\n  if (useDifferentMarker) {\n    bullet = bulletOther\n  }\n\n  state.bulletCurrent = bullet\n  const value = state.containerFlow(node, info)\n  state.bulletLastUsed = bullet\n  state.bulletCurrent = bulletCurrent\n  exit()\n  return value\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;AACA;AACA;;;;;AASO,SAAS,KAAK,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI;IAC5C,MAAM,OAAO,MAAM,KAAK,CAAC;IACzB,MAAM,gBAAgB,MAAM,aAAa;IACzC,mBAAmB,GACnB,IAAI,SAAS,KAAK,OAAO,GAAG,CAAA,GAAA,+LAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD,EAAE;IACpE,mBAAmB,GACnB,MAAM,cAAc,KAAK,OAAO,GAC5B,WAAW,MACT,MACA,MACF,CAAA,GAAA,6LAAA,CAAA,mBAAgB,AAAD,EAAE;IACrB,IAAI,qBACF,UAAU,MAAM,cAAc,GAAG,WAAW,MAAM,cAAc,GAAG;IAErE,IAAI,CAAC,KAAK,OAAO,EAAE;QACjB,MAAM,gBAAgB,KAAK,QAAQ,GAAG,KAAK,QAAQ,CAAC,EAAE,GAAG;QAEzD,kEAAkE;QAClE,qCAAqC;QACrC,EAAE;QACF,cAAc;QACd,QAAQ;QACR,MAAM;QACN,EAAE;QACF,6DAA6D;QAC7D,IACE,mDAAmD;QACnD,CAAC,WAAW,OAAO,WAAW,GAAG,KACjC,yBAAyB;QACzB,iBACA,CAAC,CAAC,cAAc,QAAQ,IAAI,CAAC,cAAc,QAAQ,CAAC,EAAE,KACtD,oCAAoC;QACpC,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,GAAG,EAAE,KAAK,UACxC,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,GAAG,EAAE,KAAK,cACxC,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,GAAG,EAAE,KAAK,UACxC,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,GAAG,EAAE,KAAK,cACxC,iCAAiC;QACjC,MAAM,UAAU,CAAC,MAAM,UAAU,CAAC,MAAM,GAAG,EAAE,KAAK,KAClD,MAAM,UAAU,CAAC,MAAM,UAAU,CAAC,MAAM,GAAG,EAAE,KAAK,KAClD,MAAM,UAAU,CAAC,MAAM,UAAU,CAAC,MAAM,GAAG,EAAE,KAAK,GAClD;YACA,qBAAqB;QACvB;QAEA,mEAAmE;QACnE,qCAAqC;QACrC,EAAE;QACF,cAAc;QACd,QAAQ;QACR,MAAM;QACN,EAAE;QACF,6DAA6D;QAC7D,IAAI,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,EAAE,WAAW,UAAU,eAAe;YAChD,IAAI,QAAQ,CAAC;YAEb,MAAO,EAAE,QAAQ,KAAK,QAAQ,CAAC,MAAM,CAAE;gBACrC,MAAM,OAAO,KAAK,QAAQ,CAAC,MAAM;gBAEjC,IACE,QACA,KAAK,IAAI,KAAK,cACd,KAAK,QAAQ,IACb,KAAK,QAAQ,CAAC,EAAE,IAChB,KAAK,QAAQ,CAAC,EAAE,CAAC,IAAI,KAAK,iBAC1B;oBACA,qBAAqB;oBACrB;gBACF;YACF;QACF;IACF;IAEA,IAAI,oBAAoB;QACtB,SAAS;IACX;IAEA,MAAM,aAAa,GAAG;IACtB,MAAM,QAAQ,MAAM,aAAa,CAAC,MAAM;IACxC,MAAM,cAAc,GAAG;IACvB,MAAM,aAAa,GAAG;IACtB;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3715, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js"], "sourcesContent": ["/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['listItemIndent'], null | undefined>}\n */\nexport function checkListItemIndent(state) {\n  const style = state.options.listItemIndent || 'one'\n\n  if (style !== 'tab' && style !== 'one' && style !== 'mixed') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        style +\n        '` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`'\n    )\n  }\n\n  return style\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;CAGC;;;AACM,SAAS,oBAAoB,KAAK;IACvC,MAAM,QAAQ,MAAM,OAAO,CAAC,cAAc,IAAI;IAE9C,IAAI,UAAU,SAAS,UAAU,SAAS,UAAU,SAAS;QAC3D,MAAM,IAAI,MACR,kCACE,QACA;IAEN;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3736, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/handle/list-item.js"], "sourcesContent": ["/**\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n * @import {ListItem, Parents} from 'mdast'\n */\n\nimport {checkBullet} from '../util/check-bullet.js'\nimport {checkListItemIndent} from '../util/check-list-item-indent.js'\n\n/**\n * @param {ListItem} node\n * @param {Parents | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function listItem(node, parent, state, info) {\n  const listItemIndent = checkListItemIndent(state)\n  let bullet = state.bulletCurrent || checkBullet(state)\n\n  // Add the marker value for ordered lists.\n  if (parent && parent.type === 'list' && parent.ordered) {\n    bullet =\n      (typeof parent.start === 'number' && parent.start > -1\n        ? parent.start\n        : 1) +\n      (state.options.incrementListMarker === false\n        ? 0\n        : parent.children.indexOf(node)) +\n      bullet\n  }\n\n  let size = bullet.length + 1\n\n  if (\n    listItemIndent === 'tab' ||\n    (listItemIndent === 'mixed' &&\n      ((parent && parent.type === 'list' && parent.spread) || node.spread))\n  ) {\n    size = Math.ceil(size / 4) * 4\n  }\n\n  const tracker = state.createTracker(info)\n  tracker.move(bullet + ' '.repeat(size - bullet.length))\n  tracker.shift(size)\n  const exit = state.enter('listItem')\n  const value = state.indentLines(\n    state.containerFlow(node, tracker.current()),\n    map\n  )\n  exit()\n\n  return value\n\n  /** @type {Map} */\n  function map(line, index, blank) {\n    if (index) {\n      return (blank ? '' : ' '.repeat(size)) + line\n    }\n\n    return (blank ? bullet : bullet + ' '.repeat(size - bullet.length)) + line\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;;;AASO,SAAS,SAAS,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI;IAChD,MAAM,iBAAiB,CAAA,GAAA,oMAAA,CAAA,sBAAmB,AAAD,EAAE;IAC3C,IAAI,SAAS,MAAM,aAAa,IAAI,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD,EAAE;IAEhD,0CAA0C;IAC1C,IAAI,UAAU,OAAO,IAAI,KAAK,UAAU,OAAO,OAAO,EAAE;QACtD,SACE,CAAC,OAAO,OAAO,KAAK,KAAK,YAAY,OAAO,KAAK,GAAG,CAAC,IACjD,OAAO,KAAK,GACZ,CAAC,IACL,CAAC,MAAM,OAAO,CAAC,mBAAmB,KAAK,QACnC,IACA,OAAO,QAAQ,CAAC,OAAO,CAAC,KAAK,IACjC;IACJ;IAEA,IAAI,OAAO,OAAO,MAAM,GAAG;IAE3B,IACE,mBAAmB,SAClB,mBAAmB,WAClB,CAAC,AAAC,UAAU,OAAO,IAAI,KAAK,UAAU,OAAO,MAAM,IAAK,KAAK,MAAM,GACrE;QACA,OAAO,KAAK,IAAI,CAAC,OAAO,KAAK;IAC/B;IAEA,MAAM,UAAU,MAAM,aAAa,CAAC;IACpC,QAAQ,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,OAAO,OAAO,MAAM;IACrD,QAAQ,KAAK,CAAC;IACd,MAAM,OAAO,MAAM,KAAK,CAAC;IACzB,MAAM,QAAQ,MAAM,WAAW,CAC7B,MAAM,aAAa,CAAC,MAAM,QAAQ,OAAO,KACzC;IAEF;IAEA,OAAO;;IAEP,gBAAgB,GAChB,SAAS,IAAI,IAAI,EAAE,KAAK,EAAE,KAAK;QAC7B,IAAI,OAAO;YACT,OAAO,CAAC,QAAQ,KAAK,IAAI,MAAM,CAAC,KAAK,IAAI;QAC3C;QAEA,OAAO,CAAC,QAAQ,SAAS,SAAS,IAAI,MAAM,CAAC,OAAO,OAAO,MAAM,CAAC,IAAI;IACxE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3778, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/handle/paragraph.js"], "sourcesContent": ["/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Paragraph, Parents} from 'mdast'\n */\n\n/**\n * @param {Paragraph} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function paragraph(node, _, state, info) {\n  const exit = state.enter('paragraph')\n  const subexit = state.enter('phrasing')\n  const value = state.containerPhrasing(node, info)\n  subexit()\n  exit()\n  return value\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;;;CAMC;;;AACM,SAAS,UAAU,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI;IAC5C,MAAM,OAAO,MAAM,KAAK,CAAC;IACzB,MAAM,UAAU,MAAM,KAAK,CAAC;IAC5B,MAAM,QAAQ,MAAM,iBAAiB,CAAC,MAAM;IAC5C;IACA;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3804, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-phrasing/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('mdast').Html} Html\n * @typedef {import('mdast').PhrasingContent} PhrasingContent\n */\n\nimport {convert} from 'unist-util-is'\n\n/**\n * Check if the given value is *phrasing content*.\n *\n * > 👉 **Note**: Excludes `html`, which can be both phrasing or flow.\n *\n * @param node\n *   Thing to check, typically `Node`.\n * @returns\n *   Whether `value` is phrasing content.\n */\n\nexport const phrasing =\n  /** @type {(node?: unknown) => node is Exclude<PhrasingContent, Html>} */\n  (\n    convert([\n      'break',\n      'delete',\n      'emphasis',\n      // To do: next major: removed since footnotes were added to GFM.\n      'footnote',\n      'footnoteReference',\n      'image',\n      'imageReference',\n      'inlineCode',\n      // Enabled by `mdast-util-math`:\n      'inlineMath',\n      'link',\n      'linkReference',\n      // Enabled by `mdast-util-mdx`:\n      'mdxJsxTextElement',\n      // Enabled by `mdast-util-mdx`:\n      'mdxTextExpression',\n      'strong',\n      'text',\n      // Enabled by `mdast-util-directive`:\n      'textDirective'\n    ])\n  )\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AAaO,MAAM,WAGT,CAAA,GAAA,sJAAA,CAAA,UAAO,AAAD,EAAE;IACN;IACA;IACA;IACA,gEAAgE;IAChE;IACA;IACA;IACA;IACA;IACA,gCAAgC;IAChC;IACA;IACA;IACA,+BAA+B;IAC/B;IACA,+BAA+B;IAC/B;IACA;IACA;IACA,qCAAqC;IACrC;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3841, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/handle/root.js"], "sourcesContent": ["/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Root} from 'mdast'\n */\n\nimport {phrasing} from 'mdast-util-phrasing'\n\n/**\n * @param {Root} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function root(node, _, state, info) {\n  // Note: `html` nodes are ambiguous.\n  const hasPhrasing = node.children.some(function (d) {\n    return phrasing(d)\n  })\n\n  const container = hasPhrasing ? state.containerPhrasing : state.containerFlow\n  return container.call(state, node, info)\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AASO,SAAS,KAAK,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI;IACvC,oCAAoC;IACpC,MAAM,cAAc,KAAK,QAAQ,CAAC,IAAI,CAAC,SAAU,CAAC;QAChD,OAAO,CAAA,GAAA,4JAAA,CAAA,WAAQ,AAAD,EAAE;IAClB;IAEA,MAAM,YAAY,cAAc,MAAM,iBAAiB,GAAG,MAAM,aAAa;IAC7E,OAAO,UAAU,IAAI,CAAC,OAAO,MAAM;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3863, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/util/check-strong.js"], "sourcesContent": ["/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['strong'], null | undefined>}\n */\nexport function checkStrong(state) {\n  const marker = state.options.strong || '*'\n\n  if (marker !== '*' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize strong with `' +\n        marker +\n        '` for `options.strong`, expected `*`, or `_`'\n    )\n  }\n\n  return marker\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;CAGC;;;AACM,SAAS,YAAY,KAAK;IAC/B,MAAM,SAAS,MAAM,OAAO,CAAC,MAAM,IAAI;IAEvC,IAAI,WAAW,OAAO,WAAW,KAAK;QACpC,MAAM,IAAI,MACR,mCACE,SACA;IAEN;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3884, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/handle/strong.js"], "sourcesContent": ["/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Strong} from 'mdast'\n */\n\nimport {checkStrong} from '../util/check-strong.js'\nimport {encodeCharacterReference} from '../util/encode-character-reference.js'\nimport {encodeInfo} from '../util/encode-info.js'\n\nstrong.peek = strongPeek\n\n/**\n * @param {Strong} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function strong(node, _, state, info) {\n  const marker = checkStrong(state)\n  const exit = state.enter('strong')\n  const tracker = state.createTracker(info)\n  const before = tracker.move(marker + marker)\n\n  let between = tracker.move(\n    state.containerPhrasing(node, {\n      after: marker,\n      before,\n      ...tracker.current()\n    })\n  )\n  const betweenHead = between.charCodeAt(0)\n  const open = encodeInfo(\n    info.before.charCodeAt(info.before.length - 1),\n    betweenHead,\n    marker\n  )\n\n  if (open.inside) {\n    between = encodeCharacterReference(betweenHead) + between.slice(1)\n  }\n\n  const betweenTail = between.charCodeAt(between.length - 1)\n  const close = encodeInfo(info.after.charCodeAt(0), betweenTail, marker)\n\n  if (close.inside) {\n    between = between.slice(0, -1) + encodeCharacterReference(betweenTail)\n  }\n\n  const after = tracker.move(marker + marker)\n\n  exit()\n\n  state.attentionEncodeSurroundingInfo = {\n    after: close.outside,\n    before: open.outside\n  }\n  return before + between + after\n}\n\n/**\n * @param {Strong} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction strongPeek(_, _1, state) {\n  return state.options.strong || '*'\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;AACA;;;;AAEA,OAAO,IAAI,GAAG;AASP,SAAS,OAAO,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI;IACzC,MAAM,SAAS,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD,EAAE;IAC3B,MAAM,OAAO,MAAM,KAAK,CAAC;IACzB,MAAM,UAAU,MAAM,aAAa,CAAC;IACpC,MAAM,SAAS,QAAQ,IAAI,CAAC,SAAS;IAErC,IAAI,UAAU,QAAQ,IAAI,CACxB,MAAM,iBAAiB,CAAC,MAAM;QAC5B,OAAO;QACP;QACA,GAAG,QAAQ,OAAO,EAAE;IACtB;IAEF,MAAM,cAAc,QAAQ,UAAU,CAAC;IACvC,MAAM,OAAO,CAAA,GAAA,mLAAA,CAAA,aAAU,AAAD,EACpB,KAAK,MAAM,CAAC,UAAU,CAAC,KAAK,MAAM,CAAC,MAAM,GAAG,IAC5C,aACA;IAGF,IAAI,KAAK,MAAM,EAAE;QACf,UAAU,CAAA,GAAA,qMAAA,CAAA,2BAAwB,AAAD,EAAE,eAAe,QAAQ,KAAK,CAAC;IAClE;IAEA,MAAM,cAAc,QAAQ,UAAU,CAAC,QAAQ,MAAM,GAAG;IACxD,MAAM,QAAQ,CAAA,GAAA,mLAAA,CAAA,aAAU,AAAD,EAAE,KAAK,KAAK,CAAC,UAAU,CAAC,IAAI,aAAa;IAEhE,IAAI,MAAM,MAAM,EAAE;QAChB,UAAU,QAAQ,KAAK,CAAC,GAAG,CAAC,KAAK,CAAA,GAAA,qMAAA,CAAA,2BAAwB,AAAD,EAAE;IAC5D;IAEA,MAAM,QAAQ,QAAQ,IAAI,CAAC,SAAS;IAEpC;IAEA,MAAM,8BAA8B,GAAG;QACrC,OAAO,MAAM,OAAO;QACpB,QAAQ,KAAK,OAAO;IACtB;IACA,OAAO,SAAS,UAAU;AAC5B;AAEA;;;;;CAKC,GACD,SAAS,WAAW,CAAC,EAAE,EAAE,EAAE,KAAK;IAC9B,OAAO,MAAM,OAAO,CAAC,MAAM,IAAI;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3939, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/handle/text.js"], "sourcesContent": ["/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Text} from 'mdast'\n */\n\n/**\n * @param {Text} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function text(node, _, state, info) {\n  return state.safe(node.value, info)\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;;;CAMC;;;AACM,SAAS,KAAK,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI;IACvC,OAAO,MAAM,IAAI,CAAC,KAAK,KAAK,EAAE;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3960, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js"], "sourcesContent": ["/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['ruleRepetition'], null | undefined>}\n */\nexport function checkRuleRepetition(state) {\n  const repetition = state.options.ruleRepetition || 3\n\n  if (repetition < 3) {\n    throw new Error(\n      'Cannot serialize rules with repetition `' +\n        repetition +\n        '` for `options.ruleRepetition`, expected `3` or more'\n    )\n  }\n\n  return repetition\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;CAGC;;;AACM,SAAS,oBAAoB,KAAK;IACvC,MAAM,aAAa,MAAM,OAAO,CAAC,cAAc,IAAI;IAEnD,IAAI,aAAa,GAAG;QAClB,MAAM,IAAI,MACR,6CACE,aACA;IAEN;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3981, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js"], "sourcesContent": ["/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Parents, ThematicBreak} from 'mdast'\n */\n\nimport {checkRuleRepetition} from '../util/check-rule-repetition.js'\nimport {checkRule} from '../util/check-rule.js'\n\n/**\n * @param {ThematicBreak} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nexport function thematicBreak(_, _1, state) {\n  const value = (\n    checkRule(state) + (state.options.ruleSpaces ? ' ' : '')\n  ).repeat(checkRuleRepetition(state))\n\n  return state.options.ruleSpaces ? value.slice(0, -1) : value\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;;;AAQO,SAAS,cAAc,CAAC,EAAE,EAAE,EAAE,KAAK;IACxC,MAAM,QAAQ,CACZ,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,EAAE,SAAS,CAAC,MAAM,OAAO,CAAC,UAAU,GAAG,MAAM,EAAE,CACzD,EAAE,MAAM,CAAC,CAAA,GAAA,gMAAA,CAAA,sBAAmB,AAAD,EAAE;IAE7B,OAAO,MAAM,OAAO,CAAC,UAAU,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,KAAK;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4001, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-to-markdown/lib/handle/index.js"], "sourcesContent": ["import {blockquote} from './blockquote.js'\nimport {hardBreak} from './break.js'\nimport {code} from './code.js'\nimport {definition} from './definition.js'\nimport {emphasis} from './emphasis.js'\nimport {heading} from './heading.js'\nimport {html} from './html.js'\nimport {image} from './image.js'\nimport {imageReference} from './image-reference.js'\nimport {inlineCode} from './inline-code.js'\nimport {link} from './link.js'\nimport {linkReference} from './link-reference.js'\nimport {list} from './list.js'\nimport {listItem} from './list-item.js'\nimport {paragraph} from './paragraph.js'\nimport {root} from './root.js'\nimport {strong} from './strong.js'\nimport {text} from './text.js'\nimport {thematicBreak} from './thematic-break.js'\n\n/**\n * Default (CommonMark) handlers.\n */\nexport const handle = {\n  blockquote,\n  break: hardBreak,\n  code,\n  definition,\n  emphasis,\n  hardBreak,\n  heading,\n  html,\n  image,\n  imageReference,\n  inlineCode,\n  link,\n  linkReference,\n  list,\n  listItem,\n  paragraph,\n  root,\n  strong,\n  text,\n  thematicBreak\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;AAKO,MAAM,SAAS;IACpB,YAAA,iLAAA,CAAA,aAAU;IACV,OAAO,4KAAA,CAAA,YAAS;IAChB,MAAA,2KAAA,CAAA,OAAI;IACJ,YAAA,iLAAA,CAAA,aAAU;IACV,UAAA,+KAAA,CAAA,WAAQ;IACR,WAAA,4KAAA,CAAA,YAAS;IACT,SAAA,8KAAA,CAAA,UAAO;IACP,MAAA,2KAAA,CAAA,OAAI;IACJ,OAAA,4KAAA,CAAA,QAAK;IACL,gBAAA,yLAAA,CAAA,iBAAc;IACd,YAAA,qLAAA,CAAA,aAAU;IACV,MAAA,2KAAA,CAAA,OAAI;IACJ,eAAA,wLAAA,CAAA,gBAAa;IACb,MAAA,2KAAA,CAAA,OAAI;IACJ,UAAA,mLAAA,CAAA,WAAQ;IACR,WAAA,gLAAA,CAAA,YAAS;IACT,MAAA,2KAAA,CAAA,OAAI;IACJ,QAAA,6KAAA,CAAA,SAAM;IACN,MAAA,2KAAA,CAAA,OAAI;IACJ,eAAA,wLAAA,CAAA,gBAAa;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4080, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-gfm-table/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('mdast').Table} Table\n * @typedef {import('mdast').TableCell} TableCell\n * @typedef {import('mdast').TableRow} TableRow\n *\n * @typedef {import('markdown-table').Options} MarkdownTableOptions\n *\n * @typedef {import('mdast-util-from-markdown').CompileContext} CompileContext\n * @typedef {import('mdast-util-from-markdown').Extension} FromMarkdownExtension\n * @typedef {import('mdast-util-from-markdown').Handle} FromMarkdownHandle\n *\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownExtension\n * @typedef {import('mdast-util-to-markdown').Handle} ToMarkdownHandle\n * @typedef {import('mdast-util-to-markdown').State} State\n * @typedef {import('mdast-util-to-markdown').Info} Info\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [tableCellPadding=true]\n *   Whether to add a space of padding between delimiters and cells (default:\n *   `true`).\n * @property {boolean | null | undefined} [tablePipeAlign=true]\n *   Whether to align the delimiters (default: `true`).\n * @property {MarkdownTableOptions['stringLength'] | null | undefined} [stringLength]\n *   Function to detect the length of table cell content, used when aligning\n *   the delimiters between cells (optional).\n */\n\nimport {ok as assert} from 'devlop'\nimport {markdownTable} from 'markdown-table'\nimport {defaultHandlers} from 'mdast-util-to-markdown'\n\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM tables in\n * markdown.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-from-markdown` to enable GFM tables.\n */\nexport function gfmTableFromMarkdown() {\n  return {\n    enter: {\n      table: enterTable,\n      tableData: enterCell,\n      tableHeader: enterCell,\n      tableRow: enterRow\n    },\n    exit: {\n      codeText: exitCodeText,\n      table: exitTable,\n      tableData: exit,\n      tableHeader: exit,\n      tableRow: exit\n    }\n  }\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterTable(token) {\n  const align = token._align\n  assert(align, 'expected `_align` on table')\n  this.enter(\n    {\n      type: 'table',\n      align: align.map(function (d) {\n        return d === 'none' ? null : d\n      }),\n      children: []\n    },\n    token\n  )\n  this.data.inTable = true\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitTable(token) {\n  this.exit(token)\n  this.data.inTable = undefined\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterRow(token) {\n  this.enter({type: 'tableRow', children: []}, token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exit(token) {\n  this.exit(token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterCell(token) {\n  this.enter({type: 'tableCell', children: []}, token)\n}\n\n// Overwrite the default code text data handler to unescape escaped pipes when\n// they are in tables.\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitCodeText(token) {\n  let value = this.resume()\n\n  if (this.data.inTable) {\n    value = value.replace(/\\\\([\\\\|])/g, replace)\n  }\n\n  const node = this.stack[this.stack.length - 1]\n  assert(node.type === 'inlineCode')\n  node.value = value\n  this.exit(token)\n}\n\n/**\n * @param {string} $0\n * @param {string} $1\n * @returns {string}\n */\nfunction replace($0, $1) {\n  // Pipes work, backslashes don’t (but can’t escape pipes).\n  return $1 === '|' ? $1 : $0\n}\n\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM tables in\n * markdown.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable GFM tables.\n */\nexport function gfmTableToMarkdown(options) {\n  const settings = options || {}\n  const padding = settings.tableCellPadding\n  const alignDelimiters = settings.tablePipeAlign\n  const stringLength = settings.stringLength\n  const around = padding ? ' ' : '|'\n\n  return {\n    unsafe: [\n      {character: '\\r', inConstruct: 'tableCell'},\n      {character: '\\n', inConstruct: 'tableCell'},\n      // A pipe, when followed by a tab or space (padding), or a dash or colon\n      // (unpadded delimiter row), could result in a table.\n      {atBreak: true, character: '|', after: '[\\t :-]'},\n      // A pipe in a cell must be encoded.\n      {character: '|', inConstruct: 'tableCell'},\n      // A colon must be followed by a dash, in which case it could start a\n      // delimiter row.\n      {atBreak: true, character: ':', after: '-'},\n      // A delimiter row can also start with a dash, when followed by more\n      // dashes, a colon, or a pipe.\n      // This is a stricter version than the built in check for lists, thematic\n      // breaks, and setex heading underlines though:\n      // <https://github.com/syntax-tree/mdast-util-to-markdown/blob/51a2038/lib/unsafe.js#L57>\n      {atBreak: true, character: '-', after: '[:|-]'}\n    ],\n    handlers: {\n      inlineCode: inlineCodeWithTable,\n      table: handleTable,\n      tableCell: handleTableCell,\n      tableRow: handleTableRow\n    }\n  }\n\n  /**\n   * @type {ToMarkdownHandle}\n   * @param {Table} node\n   */\n  function handleTable(node, _, state, info) {\n    return serializeData(handleTableAsData(node, state, info), node.align)\n  }\n\n  /**\n   * This function isn’t really used normally, because we handle rows at the\n   * table level.\n   * But, if someone passes in a table row, this ensures we make somewhat sense.\n   *\n   * @type {ToMarkdownHandle}\n   * @param {TableRow} node\n   */\n  function handleTableRow(node, _, state, info) {\n    const row = handleTableRowAsData(node, state, info)\n    const value = serializeData([row])\n    // `markdown-table` will always add an align row\n    return value.slice(0, value.indexOf('\\n'))\n  }\n\n  /**\n   * @type {ToMarkdownHandle}\n   * @param {TableCell} node\n   */\n  function handleTableCell(node, _, state, info) {\n    const exit = state.enter('tableCell')\n    const subexit = state.enter('phrasing')\n    const value = state.containerPhrasing(node, {\n      ...info,\n      before: around,\n      after: around\n    })\n    subexit()\n    exit()\n    return value\n  }\n\n  /**\n   * @param {Array<Array<string>>} matrix\n   * @param {Array<string | null | undefined> | null | undefined} [align]\n   */\n  function serializeData(matrix, align) {\n    return markdownTable(matrix, {\n      align,\n      // @ts-expect-error: `markdown-table` types should support `null`.\n      alignDelimiters,\n      // @ts-expect-error: `markdown-table` types should support `null`.\n      padding,\n      // @ts-expect-error: `markdown-table` types should support `null`.\n      stringLength\n    })\n  }\n\n  /**\n   * @param {Table} node\n   * @param {State} state\n   * @param {Info} info\n   */\n  function handleTableAsData(node, state, info) {\n    const children = node.children\n    let index = -1\n    /** @type {Array<Array<string>>} */\n    const result = []\n    const subexit = state.enter('table')\n\n    while (++index < children.length) {\n      result[index] = handleTableRowAsData(children[index], state, info)\n    }\n\n    subexit()\n\n    return result\n  }\n\n  /**\n   * @param {TableRow} node\n   * @param {State} state\n   * @param {Info} info\n   */\n  function handleTableRowAsData(node, state, info) {\n    const children = node.children\n    let index = -1\n    /** @type {Array<string>} */\n    const result = []\n    const subexit = state.enter('tableRow')\n\n    while (++index < children.length) {\n      // Note: the positional info as used here is incorrect.\n      // Making it correct would be impossible due to aligning cells?\n      // And it would need copy/pasting `markdown-table` into this project.\n      result[index] = handleTableCell(children[index], node, state, info)\n    }\n\n    subexit()\n\n    return result\n  }\n\n  /**\n   * @type {ToMarkdownHandle}\n   * @param {InlineCode} node\n   */\n  function inlineCodeWithTable(node, parent, state) {\n    let value = defaultHandlers.inlineCode(node, parent, state)\n\n    if (state.stack.includes('tableCell')) {\n      value = value.replace(/\\|/g, '\\\\$&')\n    }\n\n    return value\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;CAgBC,GAED;;;;;;;;;;;CAWC;;;;AAED;AACA;AACA;;;;AASO,SAAS;IACd,OAAO;QACL,OAAO;YACL,OAAO;YACP,WAAW;YACX,aAAa;YACb,UAAU;QACZ;QACA,MAAM;YACJ,UAAU;YACV,OAAO;YACP,WAAW;YACX,aAAa;YACb,UAAU;QACZ;IACF;AACF;AAEA;;;CAGC,GACD,SAAS,WAAW,KAAK;IACvB,MAAM,QAAQ,MAAM,MAAM;IAC1B,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,OAAO;IACd,IAAI,CAAC,KAAK,CACR;QACE,MAAM;QACN,OAAO,MAAM,GAAG,CAAC,SAAU,CAAC;YAC1B,OAAO,MAAM,SAAS,OAAO;QAC/B;QACA,UAAU,EAAE;IACd,GACA;IAEF,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG;AACtB;AAEA;;;CAGC,GACD,SAAS,UAAU,KAAK;IACtB,IAAI,CAAC,IAAI,CAAC;IACV,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG;AACtB;AAEA;;;CAGC,GACD,SAAS,SAAS,KAAK;IACrB,IAAI,CAAC,KAAK,CAAC;QAAC,MAAM;QAAY,UAAU,EAAE;IAAA,GAAG;AAC/C;AAEA;;;CAGC,GACD,SAAS,KAAK,KAAK;IACjB,IAAI,CAAC,IAAI,CAAC;AACZ;AAEA;;;CAGC,GACD,SAAS,UAAU,KAAK;IACtB,IAAI,CAAC,KAAK,CAAC;QAAC,MAAM;QAAa,UAAU,EAAE;IAAA,GAAG;AAChD;AAEA,8EAA8E;AAC9E,sBAAsB;AACtB;;;CAGC,GACD,SAAS,aAAa,KAAK;IACzB,IAAI,QAAQ,IAAI,CAAC,MAAM;IAEvB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;QACrB,QAAQ,MAAM,OAAO,CAAC,cAAc;IACtC;IAEA,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;IAC9C,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK;IACrB,KAAK,KAAK,GAAG;IACb,IAAI,CAAC,IAAI,CAAC;AACZ;AAEA;;;;CAIC,GACD,SAAS,QAAQ,EAAE,EAAE,EAAE;IACrB,0DAA0D;IAC1D,OAAO,OAAO,MAAM,KAAK;AAC3B;AAWO,SAAS,mBAAmB,OAAO;IACxC,MAAM,WAAW,WAAW,CAAC;IAC7B,MAAM,UAAU,SAAS,gBAAgB;IACzC,MAAM,kBAAkB,SAAS,cAAc;IAC/C,MAAM,eAAe,SAAS,YAAY;IAC1C,MAAM,SAAS,UAAU,MAAM;IAE/B,OAAO;QACL,QAAQ;YACN;gBAAC,WAAW;gBAAM,aAAa;YAAW;YAC1C;gBAAC,WAAW;gBAAM,aAAa;YAAW;YAC1C,wEAAwE;YACxE,qDAAqD;YACrD;gBAAC,SAAS;gBAAM,WAAW;gBAAK,OAAO;YAAS;YAChD,oCAAoC;YACpC;gBAAC,WAAW;gBAAK,aAAa;YAAW;YACzC,qEAAqE;YACrE,iBAAiB;YACjB;gBAAC,SAAS;gBAAM,WAAW;gBAAK,OAAO;YAAG;YAC1C,oEAAoE;YACpE,8BAA8B;YAC9B,yEAAyE;YACzE,+CAA+C;YAC/C,yFAAyF;YACzF;gBAAC,SAAS;gBAAM,WAAW;gBAAK,OAAO;YAAO;SAC/C;QACD,UAAU;YACR,YAAY;YACZ,OAAO;YACP,WAAW;YACX,UAAU;QACZ;IACF;;IAEA;;;GAGC,GACD,SAAS,YAAY,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI;QACvC,OAAO,cAAc,kBAAkB,MAAM,OAAO,OAAO,KAAK,KAAK;IACvE;IAEA;;;;;;;GAOC,GACD,SAAS,eAAe,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI;QAC1C,MAAM,MAAM,qBAAqB,MAAM,OAAO;QAC9C,MAAM,QAAQ,cAAc;YAAC;SAAI;QACjC,gDAAgD;QAChD,OAAO,MAAM,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC;IACtC;IAEA;;;GAGC,GACD,SAAS,gBAAgB,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI;QAC3C,MAAM,OAAO,MAAM,KAAK,CAAC;QACzB,MAAM,UAAU,MAAM,KAAK,CAAC;QAC5B,MAAM,QAAQ,MAAM,iBAAiB,CAAC,MAAM;YAC1C,GAAG,IAAI;YACP,QAAQ;YACR,OAAO;QACT;QACA;QACA;QACA,OAAO;IACT;IAEA;;;GAGC,GACD,SAAS,cAAc,MAAM,EAAE,KAAK;QAClC,OAAO,CAAA,GAAA,6IAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAC3B;YACA,kEAAkE;YAClE;YACA,kEAAkE;YAClE;YACA,kEAAkE;YAClE;QACF;IACF;IAEA;;;;GAIC,GACD,SAAS,kBAAkB,IAAI,EAAE,KAAK,EAAE,IAAI;QAC1C,MAAM,WAAW,KAAK,QAAQ;QAC9B,IAAI,QAAQ,CAAC;QACb,iCAAiC,GACjC,MAAM,SAAS,EAAE;QACjB,MAAM,UAAU,MAAM,KAAK,CAAC;QAE5B,MAAO,EAAE,QAAQ,SAAS,MAAM,CAAE;YAChC,MAAM,CAAC,MAAM,GAAG,qBAAqB,QAAQ,CAAC,MAAM,EAAE,OAAO;QAC/D;QAEA;QAEA,OAAO;IACT;IAEA;;;;GAIC,GACD,SAAS,qBAAqB,IAAI,EAAE,KAAK,EAAE,IAAI;QAC7C,MAAM,WAAW,KAAK,QAAQ;QAC9B,IAAI,QAAQ,CAAC;QACb,0BAA0B,GAC1B,MAAM,SAAS,EAAE;QACjB,MAAM,UAAU,MAAM,KAAK,CAAC;QAE5B,MAAO,EAAE,QAAQ,SAAS,MAAM,CAAE;YAChC,uDAAuD;YACvD,+DAA+D;YAC/D,qEAAqE;YACrE,MAAM,CAAC,MAAM,GAAG,gBAAgB,QAAQ,CAAC,MAAM,EAAE,MAAM,OAAO;QAChE;QAEA;QAEA,OAAO;IACT;IAEA;;;GAGC,GACD,SAAS,oBAAoB,IAAI,EAAE,MAAM,EAAE,KAAK;QAC9C,IAAI,QAAQ,yNAAA,CAAA,kBAAe,CAAC,UAAU,CAAC,MAAM,QAAQ;QAErD,IAAI,MAAM,KAAK,CAAC,QAAQ,CAAC,cAAc;YACrC,QAAQ,MAAM,OAAO,CAAC,OAAO;QAC/B;QAEA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4357, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-gfm-task-list-item/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('mdast-util-from-markdown').CompileContext} CompileContext\n * @typedef {import('mdast-util-from-markdown').Extension} FromMarkdownExtension\n * @typedef {import('mdast-util-from-markdown').Handle} FromMarkdownHandle\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownExtension\n * @typedef {import('mdast-util-to-markdown').Handle} ToMarkdownHandle\n */\n\nimport {ok as assert} from 'devlop'\nimport {defaultHandlers} from 'mdast-util-to-markdown'\n\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM task\n * list items in markdown.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-from-markdown` to enable GFM task list items.\n */\nexport function gfmTaskListItemFromMarkdown() {\n  return {\n    exit: {\n      taskListCheckValueChecked: exitCheck,\n      taskListCheckValueUnchecked: exitCheck,\n      paragraph: exitParagraphWithTaskListItem\n    }\n  }\n}\n\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM task list\n * items in markdown.\n *\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable GFM task list items.\n */\nexport function gfmTaskListItemToMarkdown() {\n  return {\n    unsafe: [{atBreak: true, character: '-', after: '[:|-]'}],\n    handlers: {listItem: listItemWithTaskListItem}\n  }\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitCheck(token) {\n  // We’re always in a paragraph, in a list item.\n  const node = this.stack[this.stack.length - 2]\n  assert(node.type === 'listItem')\n  node.checked = token.type === 'taskListCheckValueChecked'\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitParagraphWithTaskListItem(token) {\n  const parent = this.stack[this.stack.length - 2]\n\n  if (\n    parent &&\n    parent.type === 'listItem' &&\n    typeof parent.checked === 'boolean'\n  ) {\n    const node = this.stack[this.stack.length - 1]\n    assert(node.type === 'paragraph')\n    const head = node.children[0]\n\n    if (head && head.type === 'text') {\n      const siblings = parent.children\n      let index = -1\n      /** @type {Paragraph | undefined} */\n      let firstParaghraph\n\n      while (++index < siblings.length) {\n        const sibling = siblings[index]\n        if (sibling.type === 'paragraph') {\n          firstParaghraph = sibling\n          break\n        }\n      }\n\n      if (firstParaghraph === node) {\n        // Must start with a space or a tab.\n        head.value = head.value.slice(1)\n\n        if (head.value.length === 0) {\n          node.children.shift()\n        } else if (\n          node.position &&\n          head.position &&\n          typeof head.position.start.offset === 'number'\n        ) {\n          head.position.start.column++\n          head.position.start.offset++\n          node.position.start = Object.assign({}, head.position.start)\n        }\n      }\n    }\n  }\n\n  this.exit(token)\n}\n\n/**\n * @type {ToMarkdownHandle}\n * @param {ListItem} node\n */\nfunction listItemWithTaskListItem(node, parent, state, info) {\n  const head = node.children[0]\n  const checkable =\n    typeof node.checked === 'boolean' && head && head.type === 'paragraph'\n  const checkbox = '[' + (node.checked ? 'x' : ' ') + '] '\n  const tracker = state.createTracker(info)\n\n  if (checkable) {\n    tracker.move(checkbox)\n  }\n\n  let value = defaultHandlers.listItem(node, parent, state, {\n    ...info,\n    ...tracker.current()\n  })\n\n  if (checkable) {\n    value = value.replace(/^(?:[*+-]|\\d+\\.)([\\r\\n]| {1,3})/, check)\n  }\n\n  return value\n\n  /**\n   * @param {string} $0\n   * @returns {string}\n   */\n  function check($0) {\n    return $0 + checkbox\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;AAED;AACA;;;AASO,SAAS;IACd,OAAO;QACL,MAAM;YACJ,2BAA2B;YAC3B,6BAA6B;YAC7B,WAAW;QACb;IACF;AACF;AASO,SAAS;IACd,OAAO;QACL,QAAQ;YAAC;gBAAC,SAAS;gBAAM,WAAW;gBAAK,OAAO;YAAO;SAAE;QACzD,UAAU;YAAC,UAAU;QAAwB;IAC/C;AACF;AAEA;;;CAGC,GACD,SAAS,UAAU,KAAK;IACtB,+CAA+C;IAC/C,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;IAC9C,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK;IACrB,KAAK,OAAO,GAAG,MAAM,IAAI,KAAK;AAChC;AAEA;;;CAGC,GACD,SAAS,8BAA8B,KAAK;IAC1C,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;IAEhD,IACE,UACA,OAAO,IAAI,KAAK,cAChB,OAAO,OAAO,OAAO,KAAK,WAC1B;QACA,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK;QACrB,MAAM,OAAO,KAAK,QAAQ,CAAC,EAAE;QAE7B,IAAI,QAAQ,KAAK,IAAI,KAAK,QAAQ;YAChC,MAAM,WAAW,OAAO,QAAQ;YAChC,IAAI,QAAQ,CAAC;YACb,kCAAkC,GAClC,IAAI;YAEJ,MAAO,EAAE,QAAQ,SAAS,MAAM,CAAE;gBAChC,MAAM,UAAU,QAAQ,CAAC,MAAM;gBAC/B,IAAI,QAAQ,IAAI,KAAK,aAAa;oBAChC,kBAAkB;oBAClB;gBACF;YACF;YAEA,IAAI,oBAAoB,MAAM;gBAC5B,oCAAoC;gBACpC,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,KAAK,CAAC;gBAE9B,IAAI,KAAK,KAAK,CAAC,MAAM,KAAK,GAAG;oBAC3B,KAAK,QAAQ,CAAC,KAAK;gBACrB,OAAO,IACL,KAAK,QAAQ,IACb,KAAK,QAAQ,IACb,OAAO,KAAK,QAAQ,CAAC,KAAK,CAAC,MAAM,KAAK,UACtC;oBACA,KAAK,QAAQ,CAAC,KAAK,CAAC,MAAM;oBAC1B,KAAK,QAAQ,CAAC,KAAK,CAAC,MAAM;oBAC1B,KAAK,QAAQ,CAAC,KAAK,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK,QAAQ,CAAC,KAAK;gBAC7D;YACF;QACF;IACF;IAEA,IAAI,CAAC,IAAI,CAAC;AACZ;AAEA;;;CAGC,GACD,SAAS,yBAAyB,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI;IACzD,MAAM,OAAO,KAAK,QAAQ,CAAC,EAAE;IAC7B,MAAM,YACJ,OAAO,KAAK,OAAO,KAAK,aAAa,QAAQ,KAAK,IAAI,KAAK;IAC7D,MAAM,WAAW,MAAM,CAAC,KAAK,OAAO,GAAG,MAAM,GAAG,IAAI;IACpD,MAAM,UAAU,MAAM,aAAa,CAAC;IAEpC,IAAI,WAAW;QACb,QAAQ,IAAI,CAAC;IACf;IAEA,IAAI,QAAQ,yNAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,MAAM,QAAQ,OAAO;QACxD,GAAG,IAAI;QACP,GAAG,QAAQ,OAAO,EAAE;IACtB;IAEA,IAAI,WAAW;QACb,QAAQ,MAAM,OAAO,CAAC,mCAAmC;IAC3D;IAEA,OAAO;;IAEP;;;GAGC,GACD,SAAS,MAAM,EAAE;QACf,OAAO,KAAK;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4473, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/mdast-util-gfm/lib/index.js"], "sourcesContent": ["/**\n * @import {Extension as FromMarkdownExtension} from 'mdast-util-from-markdown'\n * @import {Options} from 'mdast-util-gfm'\n * @import {Options as ToMarkdownExtension} from 'mdast-util-to-markdown'\n */\n\nimport {\n  gfmAutolinkLiteralFromMarkdown,\n  gfmAutolinkLiteralToMarkdown\n} from 'mdast-util-gfm-autolink-literal'\nimport {\n  gfmFootnoteFromMarkdown,\n  gfmFootnoteToMarkdown\n} from 'mdast-util-gfm-footnote'\nimport {\n  gfmStrikethroughFromMarkdown,\n  gfmStrikethroughToMarkdown\n} from 'mdast-util-gfm-strikethrough'\nimport {gfmTableFromMarkdown, gfmTableToMarkdown} from 'mdast-util-gfm-table'\nimport {\n  gfmTaskListItemFromMarkdown,\n  gfmTaskListItemToMarkdown\n} from 'mdast-util-gfm-task-list-item'\n\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM (autolink\n * literals, footnotes, strikethrough, tables, tasklists).\n *\n * @returns {Array<FromMarkdownExtension>}\n *   Extension for `mdast-util-from-markdown` to enable GFM (autolink literals,\n *   footnotes, strikethrough, tables, tasklists).\n */\nexport function gfmFromMarkdown() {\n  return [\n    gfmAutolinkLiteralFromMarkdown(),\n    gfmFootnoteFromMarkdown(),\n    gfmStrikethroughFromMarkdown(),\n    gfmTableFromMarkdown(),\n    gfmTaskListItemFromMarkdown()\n  ]\n}\n\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM (autolink\n * literals, footnotes, strikethrough, tables, tasklists).\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable GFM (autolink literals,\n *   footnotes, strikethrough, tables, tasklists).\n */\nexport function gfmToMarkdown(options) {\n  return {\n    extensions: [\n      gfmAutolinkLiteralToMarkdown(),\n      gfmFootnoteToMarkdown(options),\n      gfmStrikethroughToMarkdown(),\n      gfmTableToMarkdown(options),\n      gfmTaskListItemToMarkdown()\n    ]\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAED;AAIA;AAIA;AAIA;AACA;;;;;;AAaO,SAAS;IACd,OAAO;QACL,CAAA,GAAA,8KAAA,CAAA,iCAA8B,AAAD;QAC7B,CAAA,GAAA,mKAAA,CAAA,0BAAuB,AAAD;QACtB,CAAA,GAAA,wKAAA,CAAA,+BAA4B,AAAD;QAC3B,CAAA,GAAA,gKAAA,CAAA,uBAAoB,AAAD;QACnB,CAAA,GAAA,+KAAA,CAAA,8BAA2B,AAAD;KAC3B;AACH;AAYO,SAAS,cAAc,OAAO;IACnC,OAAO;QACL,YAAY;YACV,CAAA,GAAA,8KAAA,CAAA,+BAA4B,AAAD;YAC3B,CAAA,GAAA,mKAAA,CAAA,wBAAqB,AAAD,EAAE;YACtB,CAAA,GAAA,wKAAA,CAAA,6BAA0B,AAAD;YACzB,CAAA,GAAA,gKAAA,CAAA,qBAAkB,AAAD,EAAE;YACnB,CAAA,GAAA,+KAAA,CAAA,4BAAyB,AAAD;SACzB;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4517, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-util-chunked/dev/index.js"], "sourcesContent": ["import {constants} from 'micromark-util-symbol'\n\n/**\n * Like `Array#splice`, but smarter for giant arrays.\n *\n * `Array#splice` takes all items to be inserted as individual argument which\n * causes a stack overflow in V8 when trying to insert 100k items for instance.\n *\n * Otherwise, this does not return the removed items, and takes `items` as an\n * array instead of rest parameters.\n *\n * @template {unknown} T\n *   Item type.\n * @param {Array<T>} list\n *   List to operate on.\n * @param {number} start\n *   Index to remove/insert at (can be negative).\n * @param {number} remove\n *   Number of items to remove.\n * @param {Array<T>} items\n *   Items to inject into `list`.\n * @returns {undefined}\n *   Nothing.\n */\nexport function splice(list, start, remove, items) {\n  const end = list.length\n  let chunkStart = 0\n  /** @type {Array<unknown>} */\n  let parameters\n\n  // Make start between zero and `end` (included).\n  if (start < 0) {\n    start = -start > end ? 0 : end + start\n  } else {\n    start = start > end ? end : start\n  }\n\n  remove = remove > 0 ? remove : 0\n\n  // No need to chunk the items if there’s only a couple (10k) items.\n  if (items.length < constants.v8MaxSafeChunkSize) {\n    parameters = Array.from(items)\n    parameters.unshift(start, remove)\n    // @ts-expect-error Hush, it’s fine.\n    list.splice(...parameters)\n  } else {\n    // Delete `remove` items starting from `start`\n    if (remove) list.splice(start, remove)\n\n    // Insert the items in chunks to not cause stack overflows.\n    while (chunkStart < items.length) {\n      parameters = items.slice(\n        chunkStart,\n        chunkStart + constants.v8MaxSafeChunkSize\n      )\n      parameters.unshift(start, 0)\n      // @ts-expect-error Hush, it’s fine.\n      list.splice(...parameters)\n\n      chunkStart += constants.v8MaxSafeChunkSize\n      start += constants.v8MaxSafeChunkSize\n    }\n  }\n}\n\n/**\n * Append `items` (an array) at the end of `list` (another array).\n * When `list` was empty, returns `items` instead.\n *\n * This prevents a potentially expensive operation when `list` is empty,\n * and adds items in batches to prevent V8 from hanging.\n *\n * @template {unknown} T\n *   Item type.\n * @param {Array<T>} list\n *   List to operate on.\n * @param {Array<T>} items\n *   Items to add to `list`.\n * @returns {Array<T>}\n *   Either `list` or `items`.\n */\nexport function push(list, items) {\n  if (list.length > 0) {\n    splice(list, list.length, 0, items)\n    return list\n  }\n\n  return items\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAwBO,SAAS,OAAO,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK;IAC/C,MAAM,MAAM,KAAK,MAAM;IACvB,IAAI,aAAa;IACjB,2BAA2B,GAC3B,IAAI;IAEJ,gDAAgD;IAChD,IAAI,QAAQ,GAAG;QACb,QAAQ,CAAC,QAAQ,MAAM,IAAI,MAAM;IACnC,OAAO;QACL,QAAQ,QAAQ,MAAM,MAAM;IAC9B;IAEA,SAAS,SAAS,IAAI,SAAS;IAE/B,mEAAmE;IACnE,IAAI,MAAM,MAAM,GAAG,kKAAA,CAAA,YAAS,CAAC,kBAAkB,EAAE;QAC/C,aAAa,MAAM,IAAI,CAAC;QACxB,WAAW,OAAO,CAAC,OAAO;QAC1B,oCAAoC;QACpC,KAAK,MAAM,IAAI;IACjB,OAAO;QACL,8CAA8C;QAC9C,IAAI,QAAQ,KAAK,MAAM,CAAC,OAAO;QAE/B,2DAA2D;QAC3D,MAAO,aAAa,MAAM,MAAM,CAAE;YAChC,aAAa,MAAM,KAAK,CACtB,YACA,aAAa,kKAAA,CAAA,YAAS,CAAC,kBAAkB;YAE3C,WAAW,OAAO,CAAC,OAAO;YAC1B,oCAAoC;YACpC,KAAK,MAAM,IAAI;YAEf,cAAc,kKAAA,CAAA,YAAS,CAAC,kBAAkB;YAC1C,SAAS,kKAAA,CAAA,YAAS,CAAC,kBAAkB;QACvC;IACF;AACF;AAkBO,SAAS,KAAK,IAAI,EAAE,KAAK;IAC9B,IAAI,KAAK,MAAM,GAAG,GAAG;QACnB,OAAO,MAAM,KAAK,MAAM,EAAE,GAAG;QAC7B,OAAO;IACT;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4567, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-util-combine-extensions/index.js"], "sourcesContent": ["/**\n * @import {\n *   Extension,\n *   Handles,\n *   HtmlExtension,\n *   NormalizedExtension\n * } from 'micromark-util-types'\n */\n\nimport {splice} from 'micromark-util-chunked'\n\nconst hasOwnProperty = {}.hasOwnProperty\n\n/**\n * Combine multiple syntax extensions into one.\n *\n * @param {ReadonlyArray<Extension>} extensions\n *   List of syntax extensions.\n * @returns {NormalizedExtension}\n *   A single combined extension.\n */\nexport function combineExtensions(extensions) {\n  /** @type {NormalizedExtension} */\n  const all = {}\n  let index = -1\n\n  while (++index < extensions.length) {\n    syntaxExtension(all, extensions[index])\n  }\n\n  return all\n}\n\n/**\n * Merge `extension` into `all`.\n *\n * @param {NormalizedExtension} all\n *   Extension to merge into.\n * @param {Extension} extension\n *   Extension to merge.\n * @returns {undefined}\n *   Nothing.\n */\nfunction syntaxExtension(all, extension) {\n  /** @type {keyof Extension} */\n  let hook\n\n  for (hook in extension) {\n    const maybe = hasOwnProperty.call(all, hook) ? all[hook] : undefined\n    /** @type {Record<string, unknown>} */\n    const left = maybe || (all[hook] = {})\n    /** @type {Record<string, unknown> | undefined} */\n    const right = extension[hook]\n    /** @type {string} */\n    let code\n\n    if (right) {\n      for (code in right) {\n        if (!hasOwnProperty.call(left, code)) left[code] = []\n        const value = right[code]\n        constructs(\n          // @ts-expect-error Looks like a list.\n          left[code],\n          Array.isArray(value) ? value : value ? [value] : []\n        )\n      }\n    }\n  }\n}\n\n/**\n * Merge `list` into `existing` (both lists of constructs).\n * Mutates `existing`.\n *\n * @param {Array<unknown>} existing\n *   List of constructs to merge into.\n * @param {Array<unknown>} list\n *   List of constructs to merge.\n * @returns {undefined}\n *   Nothing.\n */\nfunction constructs(existing, list) {\n  let index = -1\n  /** @type {Array<unknown>} */\n  const before = []\n\n  while (++index < list.length) {\n    // @ts-expect-error Looks like an object.\n    ;(list[index].add === 'after' ? existing : before).push(list[index])\n  }\n\n  splice(existing, 0, 0, before)\n}\n\n/**\n * Combine multiple HTML extensions into one.\n *\n * @param {ReadonlyArray<HtmlExtension>} htmlExtensions\n *   List of HTML extensions.\n * @returns {HtmlExtension}\n *   Single combined HTML extension.\n */\nexport function combineHtmlExtensions(htmlExtensions) {\n  /** @type {HtmlExtension} */\n  const handlers = {}\n  let index = -1\n\n  while (++index < htmlExtensions.length) {\n    htmlExtension(handlers, htmlExtensions[index])\n  }\n\n  return handlers\n}\n\n/**\n * Merge `extension` into `all`.\n *\n * @param {HtmlExtension} all\n *   Extension to merge into.\n * @param {HtmlExtension} extension\n *   Extension to merge.\n * @returns {undefined}\n *   Nothing.\n */\nfunction htmlExtension(all, extension) {\n  /** @type {keyof HtmlExtension} */\n  let hook\n\n  for (hook in extension) {\n    const maybe = hasOwnProperty.call(all, hook) ? all[hook] : undefined\n    const left = maybe || (all[hook] = {})\n    const right = extension[hook]\n    /** @type {keyof Handles} */\n    let type\n\n    if (right) {\n      for (type in right) {\n        // @ts-expect-error assume document vs regular handler are managed correctly.\n        left[type] = right[type]\n      }\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AAED;;AAEA,MAAM,iBAAiB,CAAC,EAAE,cAAc;AAUjC,SAAS,kBAAkB,UAAU;IAC1C,gCAAgC,GAChC,MAAM,MAAM,CAAC;IACb,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,WAAW,MAAM,CAAE;QAClC,gBAAgB,KAAK,UAAU,CAAC,MAAM;IACxC;IAEA,OAAO;AACT;AAEA;;;;;;;;;CASC,GACD,SAAS,gBAAgB,GAAG,EAAE,SAAS;IACrC,4BAA4B,GAC5B,IAAI;IAEJ,IAAK,QAAQ,UAAW;QACtB,MAAM,QAAQ,eAAe,IAAI,CAAC,KAAK,QAAQ,GAAG,CAAC,KAAK,GAAG;QAC3D,oCAAoC,GACpC,MAAM,OAAO,SAAS,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;QACrC,gDAAgD,GAChD,MAAM,QAAQ,SAAS,CAAC,KAAK;QAC7B,mBAAmB,GACnB,IAAI;QAEJ,IAAI,OAAO;YACT,IAAK,QAAQ,MAAO;gBAClB,IAAI,CAAC,eAAe,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,KAAK,GAAG,EAAE;gBACrD,MAAM,QAAQ,KAAK,CAAC,KAAK;gBACzB,WACE,sCAAsC;gBACtC,IAAI,CAAC,KAAK,EACV,MAAM,OAAO,CAAC,SAAS,QAAQ,QAAQ;oBAAC;iBAAM,GAAG,EAAE;YAEvD;QACF;IACF;AACF;AAEA;;;;;;;;;;CAUC,GACD,SAAS,WAAW,QAAQ,EAAE,IAAI;IAChC,IAAI,QAAQ,CAAC;IACb,2BAA2B,GAC3B,MAAM,SAAS,EAAE;IAEjB,MAAO,EAAE,QAAQ,KAAK,MAAM,CAAE;QAC5B,yCAAyC;;QACxC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,UAAU,WAAW,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM;IACrE;IAEA,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,UAAU,GAAG,GAAG;AACzB;AAUO,SAAS,sBAAsB,cAAc;IAClD,0BAA0B,GAC1B,MAAM,WAAW,CAAC;IAClB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,eAAe,MAAM,CAAE;QACtC,cAAc,UAAU,cAAc,CAAC,MAAM;IAC/C;IAEA,OAAO;AACT;AAEA;;;;;;;;;CASC,GACD,SAAS,cAAc,GAAG,EAAE,SAAS;IACnC,gCAAgC,GAChC,IAAI;IAEJ,IAAK,QAAQ,UAAW;QACtB,MAAM,QAAQ,eAAe,IAAI,CAAC,KAAK,QAAQ,GAAG,CAAC,KAAK,GAAG;QAC3D,MAAM,OAAO,SAAS,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;QACrC,MAAM,QAAQ,SAAS,CAAC,KAAK;QAC7B,0BAA0B,GAC1B,IAAI;QAEJ,IAAI,OAAO;YACT,IAAK,QAAQ,MAAO;gBAClB,6EAA6E;gBAC7E,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;YAC1B;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4675, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js"], "sourcesContent": ["/**\n * @import {Code, ConstructRecord, Event, Extension, Previous, State, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\nimport {\n  asciiAlpha,\n  asciiAlphanumeric,\n  asciiControl,\n  markdownLineEndingOrSpace,\n  unicodePunctuation,\n  unicodeWhitespace\n} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol'\n\nconst wwwPrefix = {tokenize: tokenizeWwwPrefix, partial: true}\nconst domain = {tokenize: tokenizeDomain, partial: true}\nconst path = {tokenize: tokenizePath, partial: true}\nconst trail = {tokenize: tokenizeTrail, partial: true}\nconst emailDomainDotTrail = {\n  tokenize: tokenizeEmailDomainDotTrail,\n  partial: true\n}\n\nconst wwwAutolink = {\n  name: 'wwwAutolink',\n  tokenize: tokenizeWwwAutolink,\n  previous: previousWww\n}\n\nconst protocolAutolink = {\n  name: 'protocolAutolink',\n  tokenize: tokenizeProtocolAutolink,\n  previous: previousProtocol\n}\n\nconst emailAutolink = {\n  name: 'emailAutolink',\n  tokenize: tokenizeEmailAutolink,\n  previous: previousEmail\n}\n\n/** @type {ConstructRecord} */\nconst text = {}\n\n/**\n * Create an extension for `micromark` to support GitHub autolink literal\n * syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to enable GFM\n *   autolink literal syntax.\n */\nexport function gfmAutolinkLiteral() {\n  return {text}\n}\n\n/** @type {Code} */\nlet code = codes.digit0\n\n// Add alphanumerics.\nwhile (code < codes.leftCurlyBrace) {\n  text[code] = emailAutolink\n  code++\n  if (code === codes.colon) code = codes.uppercaseA\n  else if (code === codes.leftSquareBracket) code = codes.lowercaseA\n}\n\ntext[codes.plusSign] = emailAutolink\ntext[codes.dash] = emailAutolink\ntext[codes.dot] = emailAutolink\ntext[codes.underscore] = emailAutolink\ntext[codes.uppercaseH] = [emailAutolink, protocolAutolink]\ntext[codes.lowercaseH] = [emailAutolink, protocolAutolink]\ntext[codes.uppercaseW] = [emailAutolink, wwwAutolink]\ntext[codes.lowercaseW] = [emailAutolink, wwwAutolink]\n\n// To do: perform email autolink literals on events, afterwards.\n// That’s where `markdown-rs` and `cmark-gfm` perform it.\n// It should look for `@`, then for atext backwards, and then for a label\n// forwards.\n// To do: `mailto:`, `xmpp:` protocol as prefix.\n\n/**\n * Email autolink literal.\n *\n * ```markdown\n * > | a <EMAIL> b\n *       ^^^^^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeEmailAutolink(effects, ok, nok) {\n  const self = this\n  /** @type {boolean | undefined} */\n  let dot\n  /** @type {boolean} */\n  let data\n\n  return start\n\n  /**\n   * Start of email autolink literal.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (\n      !gfmAtext(code) ||\n      !previousEmail.call(self, self.previous) ||\n      previousUnbalanced(self.events)\n    ) {\n      return nok(code)\n    }\n\n    effects.enter('literalAutolink')\n    effects.enter('literalAutolinkEmail')\n    return atext(code)\n  }\n\n  /**\n   * In email atext.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atext(code) {\n    if (gfmAtext(code)) {\n      effects.consume(code)\n      return atext\n    }\n\n    if (code === codes.atSign) {\n      effects.consume(code)\n      return emailDomain\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In email domain.\n   *\n   * The reference code is a bit overly complex as it handles the `@`, of which\n   * there may be just one.\n   * Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L318>\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailDomain(code) {\n    // Dot followed by alphanumerical (not `-` or `_`).\n    if (code === codes.dot) {\n      return effects.check(\n        emailDomainDotTrail,\n        emailDomainAfter,\n        emailDomainDot\n      )(code)\n    }\n\n    // Alphanumerical, `-`, and `_`.\n    if (\n      code === codes.dash ||\n      code === codes.underscore ||\n      asciiAlphanumeric(code)\n    ) {\n      data = true\n      effects.consume(code)\n      return emailDomain\n    }\n\n    // To do: `/` if xmpp.\n\n    // Note: normally we’d truncate trailing punctuation from the link.\n    // However, email autolink literals cannot contain any of those markers,\n    // except for `.`, but that can only occur if it isn’t trailing.\n    // So we can ignore truncating!\n    return emailDomainAfter(code)\n  }\n\n  /**\n   * In email domain, on dot that is not a trail.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *                      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailDomainDot(code) {\n    effects.consume(code)\n    dot = true\n    return emailDomain\n  }\n\n  /**\n   * After email domain.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailDomainAfter(code) {\n    // Domain must not be empty, must include a dot, and must end in alphabetical.\n    // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L332>.\n    if (data && dot && asciiAlpha(self.previous)) {\n      effects.exit('literalAutolinkEmail')\n      effects.exit('literalAutolink')\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n\n/**\n * `www` autolink literal.\n *\n * ```markdown\n * > | a www.example.org b\n *       ^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeWwwAutolink(effects, ok, nok) {\n  const self = this\n\n  return wwwStart\n\n  /**\n   * Start of www autolink literal.\n   *\n   * ```markdown\n   * > | www.example.com/a?b#c\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function wwwStart(code) {\n    if (\n      (code !== codes.uppercaseW && code !== codes.lowercaseW) ||\n      !previousWww.call(self, self.previous) ||\n      previousUnbalanced(self.events)\n    ) {\n      return nok(code)\n    }\n\n    effects.enter('literalAutolink')\n    effects.enter('literalAutolinkWww')\n    // Note: we *check*, so we can discard the `www.` we parsed.\n    // If it worked, we consider it as a part of the domain.\n    return effects.check(\n      wwwPrefix,\n      effects.attempt(domain, effects.attempt(path, wwwAfter), nok),\n      nok\n    )(code)\n  }\n\n  /**\n   * After a www autolink literal.\n   *\n   * ```markdown\n   * > | www.example.com/a?b#c\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function wwwAfter(code) {\n    effects.exit('literalAutolinkWww')\n    effects.exit('literalAutolink')\n    return ok(code)\n  }\n}\n\n/**\n * Protocol autolink literal.\n *\n * ```markdown\n * > | a https://example.org b\n *       ^^^^^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeProtocolAutolink(effects, ok, nok) {\n  const self = this\n  let buffer = ''\n  let seen = false\n\n  return protocolStart\n\n  /**\n   * Start of protocol autolink literal.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function protocolStart(code) {\n    if (\n      (code === codes.uppercaseH || code === codes.lowercaseH) &&\n      previousProtocol.call(self, self.previous) &&\n      !previousUnbalanced(self.events)\n    ) {\n      effects.enter('literalAutolink')\n      effects.enter('literalAutolinkHttp')\n      buffer += String.fromCodePoint(code)\n      effects.consume(code)\n      return protocolPrefixInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In protocol.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *     ^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function protocolPrefixInside(code) {\n    // `5` is size of `https`\n    if (asciiAlpha(code) && buffer.length < 5) {\n      // @ts-expect-error: definitely number.\n      buffer += String.fromCodePoint(code)\n      effects.consume(code)\n      return protocolPrefixInside\n    }\n\n    if (code === codes.colon) {\n      const protocol = buffer.toLowerCase()\n\n      if (protocol === 'http' || protocol === 'https') {\n        effects.consume(code)\n        return protocolSlashesInside\n      }\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In slashes.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *           ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function protocolSlashesInside(code) {\n    if (code === codes.slash) {\n      effects.consume(code)\n\n      if (seen) {\n        return afterProtocol\n      }\n\n      seen = true\n      return protocolSlashesInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After protocol, before domain.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterProtocol(code) {\n    // To do: this is different from `markdown-rs`:\n    // https://github.com/wooorm/markdown-rs/blob/b3a921c761309ae00a51fe348d8a43adbc54b518/src/construct/gfm_autolink_literal.rs#L172-L182\n    return code === codes.eof ||\n      asciiControl(code) ||\n      markdownLineEndingOrSpace(code) ||\n      unicodeWhitespace(code) ||\n      unicodePunctuation(code)\n      ? nok(code)\n      : effects.attempt(domain, effects.attempt(path, protocolAfter), nok)(code)\n  }\n\n  /**\n   * After a protocol autolink literal.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *                              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function protocolAfter(code) {\n    effects.exit('literalAutolinkHttp')\n    effects.exit('literalAutolink')\n    return ok(code)\n  }\n}\n\n/**\n * `www` prefix.\n *\n * ```markdown\n * > | a www.example.org b\n *       ^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeWwwPrefix(effects, ok, nok) {\n  let size = 0\n\n  return wwwPrefixInside\n\n  /**\n   * In www prefix.\n   *\n   * ```markdown\n   * > | www.example.com\n   *     ^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function wwwPrefixInside(code) {\n    if ((code === codes.uppercaseW || code === codes.lowercaseW) && size < 3) {\n      size++\n      effects.consume(code)\n      return wwwPrefixInside\n    }\n\n    if (code === codes.dot && size === 3) {\n      effects.consume(code)\n      return wwwPrefixAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After www prefix.\n   *\n   * ```markdown\n   * > | www.example.com\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function wwwPrefixAfter(code) {\n    // If there is *anything*, we can link.\n    return code === codes.eof ? nok(code) : ok(code)\n  }\n}\n\n/**\n * Domain.\n *\n * ```markdown\n * > | a https://example.org b\n *               ^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeDomain(effects, ok, nok) {\n  /** @type {boolean | undefined} */\n  let underscoreInLastSegment\n  /** @type {boolean | undefined} */\n  let underscoreInLastLastSegment\n  /** @type {boolean | undefined} */\n  let seen\n\n  return domainInside\n\n  /**\n   * In domain.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *             ^^^^^^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function domainInside(code) {\n    // Check whether this marker, which is a trailing punctuation\n    // marker, optionally followed by more trailing markers, and then\n    // followed by an end.\n    if (code === codes.dot || code === codes.underscore) {\n      return effects.check(trail, domainAfter, domainAtPunctuation)(code)\n    }\n\n    // GH documents that only alphanumerics (other than `-`, `.`, and `_`) can\n    // occur, which sounds like ASCII only, but they also support `www.點看.com`,\n    // so that’s Unicode.\n    // Instead of some new production for Unicode alphanumerics, markdown\n    // already has that for Unicode punctuation and whitespace, so use those.\n    // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L12>.\n    if (\n      code === codes.eof ||\n      markdownLineEndingOrSpace(code) ||\n      unicodeWhitespace(code) ||\n      (code !== codes.dash && unicodePunctuation(code))\n    ) {\n      return domainAfter(code)\n    }\n\n    seen = true\n    effects.consume(code)\n    return domainInside\n  }\n\n  /**\n   * In domain, at potential trailing punctuation, that was not trailing.\n   *\n   * ```markdown\n   * > | https://example.com\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function domainAtPunctuation(code) {\n    // There is an underscore in the last segment of the domain\n    if (code === codes.underscore) {\n      underscoreInLastSegment = true\n    }\n    // Otherwise, it’s a `.`: save the last segment underscore in the\n    // penultimate segment slot.\n    else {\n      underscoreInLastLastSegment = underscoreInLastSegment\n      underscoreInLastSegment = undefined\n    }\n\n    effects.consume(code)\n    return domainInside\n  }\n\n  /**\n   * After domain.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *                        ^\n   * ```\n   *\n   * @type {State} */\n  function domainAfter(code) {\n    // Note: that’s GH says a dot is needed, but it’s not true:\n    // <https://github.com/github/cmark-gfm/issues/279>\n    if (underscoreInLastLastSegment || underscoreInLastSegment || !seen) {\n      return nok(code)\n    }\n\n    return ok(code)\n  }\n}\n\n/**\n * Path.\n *\n * ```markdown\n * > | a https://example.org/stuff b\n *                          ^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizePath(effects, ok) {\n  let sizeOpen = 0\n  let sizeClose = 0\n\n  return pathInside\n\n  /**\n   * In path.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *                        ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function pathInside(code) {\n    if (code === codes.leftParenthesis) {\n      sizeOpen++\n      effects.consume(code)\n      return pathInside\n    }\n\n    // To do: `markdown-rs` also needs this.\n    // If this is a paren, and there are less closings than openings,\n    // we don’t check for a trail.\n    if (code === codes.rightParenthesis && sizeClose < sizeOpen) {\n      return pathAtPunctuation(code)\n    }\n\n    // Check whether this trailing punctuation marker is optionally\n    // followed by more trailing markers, and then followed\n    // by an end.\n    if (\n      code === codes.exclamationMark ||\n      code === codes.quotationMark ||\n      code === codes.ampersand ||\n      code === codes.apostrophe ||\n      code === codes.rightParenthesis ||\n      code === codes.asterisk ||\n      code === codes.comma ||\n      code === codes.dot ||\n      code === codes.colon ||\n      code === codes.semicolon ||\n      code === codes.lessThan ||\n      code === codes.questionMark ||\n      code === codes.rightSquareBracket ||\n      code === codes.underscore ||\n      code === codes.tilde\n    ) {\n      return effects.check(trail, ok, pathAtPunctuation)(code)\n    }\n\n    if (\n      code === codes.eof ||\n      markdownLineEndingOrSpace(code) ||\n      unicodeWhitespace(code)\n    ) {\n      return ok(code)\n    }\n\n    effects.consume(code)\n    return pathInside\n  }\n\n  /**\n   * In path, at potential trailing punctuation, that was not trailing.\n   *\n   * ```markdown\n   * > | https://example.com/a\"b\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function pathAtPunctuation(code) {\n    // Count closing parens.\n    if (code === codes.rightParenthesis) {\n      sizeClose++\n    }\n\n    effects.consume(code)\n    return pathInside\n  }\n}\n\n/**\n * Trail.\n *\n * This calls `ok` if this *is* the trail, followed by an end, which means\n * the entire trail is not part of the link.\n * It calls `nok` if this *is* part of the link.\n *\n * ```markdown\n * > | https://example.com\").\n *                        ^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeTrail(effects, ok, nok) {\n  return trail\n\n  /**\n   * In trail of domain or path.\n   *\n   * ```markdown\n   * > | https://example.com\").\n   *                        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function trail(code) {\n    // Regular trailing punctuation.\n    if (\n      code === codes.exclamationMark ||\n      code === codes.quotationMark ||\n      code === codes.apostrophe ||\n      code === codes.rightParenthesis ||\n      code === codes.asterisk ||\n      code === codes.comma ||\n      code === codes.dot ||\n      code === codes.colon ||\n      code === codes.semicolon ||\n      code === codes.questionMark ||\n      code === codes.underscore ||\n      code === codes.tilde\n    ) {\n      effects.consume(code)\n      return trail\n    }\n\n    // `&` followed by one or more alphabeticals and then a `;`, is\n    // as a whole considered as trailing punctuation.\n    // In all other cases, it is considered as continuation of the URL.\n    if (code === codes.ampersand) {\n      effects.consume(code)\n      return trailCharacterReferenceStart\n    }\n\n    // Needed because we allow literals after `[`, as we fix:\n    // <https://github.com/github/cmark-gfm/issues/278>.\n    // Check that it is not followed by `(` or `[`.\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code)\n      return trailBracketAfter\n    }\n\n    if (\n      // `<` is an end.\n      code === codes.lessThan ||\n      // So is whitespace.\n      code === codes.eof ||\n      markdownLineEndingOrSpace(code) ||\n      unicodeWhitespace(code)\n    ) {\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In trail, after `]`.\n   *\n   * > 👉 **Note**: this deviates from `cmark-gfm` to fix a bug.\n   * > See end of <https://github.com/github/cmark-gfm/issues/278> for more.\n   *\n   * ```markdown\n   * > | https://example.com](\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function trailBracketAfter(code) {\n    // Whitespace or something that could start a resource or reference is the end.\n    // Switch back to trail otherwise.\n    if (\n      code === codes.eof ||\n      code === codes.leftParenthesis ||\n      code === codes.leftSquareBracket ||\n      markdownLineEndingOrSpace(code) ||\n      unicodeWhitespace(code)\n    ) {\n      return ok(code)\n    }\n\n    return trail(code)\n  }\n\n  /**\n   * In character-reference like trail, after `&`.\n   *\n   * ```markdown\n   * > | https://example.com&amp;).\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function trailCharacterReferenceStart(code) {\n    // When non-alpha, it’s not a trail.\n    return asciiAlpha(code) ? trailCharacterReferenceInside(code) : nok(code)\n  }\n\n  /**\n   * In character-reference like trail.\n   *\n   * ```markdown\n   * > | https://example.com&amp;).\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function trailCharacterReferenceInside(code) {\n    // Switch back to trail if this is well-formed.\n    if (code === codes.semicolon) {\n      effects.consume(code)\n      return trail\n    }\n\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return trailCharacterReferenceInside\n    }\n\n    // It’s not a trail.\n    return nok(code)\n  }\n}\n\n/**\n * Dot in email domain trail.\n *\n * This calls `ok` if this *is* the trail, followed by an end, which means\n * the trail is not part of the link.\n * It calls `nok` if this *is* part of the link.\n *\n * ```markdown\n * > | <EMAIL>.\n *                        ^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeEmailDomainDotTrail(effects, ok, nok) {\n  return start\n\n  /**\n   * Dot.\n   *\n   * ```markdown\n   * > | <EMAIL>.\n   *                    ^   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // Must be dot.\n    effects.consume(code)\n    return after\n  }\n\n  /**\n   * After dot.\n   *\n   * ```markdown\n   * > | <EMAIL>.\n   *                     ^   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // Not a trail if alphanumeric.\n    return asciiAlphanumeric(code) ? nok(code) : ok(code)\n  }\n}\n\n/**\n * See:\n * <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L156>.\n *\n * @type {Previous}\n */\nfunction previousWww(code) {\n  return (\n    code === codes.eof ||\n    code === codes.leftParenthesis ||\n    code === codes.asterisk ||\n    code === codes.underscore ||\n    code === codes.leftSquareBracket ||\n    code === codes.rightSquareBracket ||\n    code === codes.tilde ||\n    markdownLineEndingOrSpace(code)\n  )\n}\n\n/**\n * See:\n * <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L214>.\n *\n * @type {Previous}\n */\nfunction previousProtocol(code) {\n  return !asciiAlpha(code)\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Previous}\n */\nfunction previousEmail(code) {\n  // Do not allow a slash “inside” atext.\n  // The reference code is a bit weird, but that’s what it results in.\n  // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L307>.\n  // Other than slash, every preceding character is allowed.\n  return !(code === codes.slash || gfmAtext(code))\n}\n\n/**\n * @param {Code} code\n * @returns {boolean}\n */\nfunction gfmAtext(code) {\n  return (\n    code === codes.plusSign ||\n    code === codes.dash ||\n    code === codes.dot ||\n    code === codes.underscore ||\n    asciiAlphanumeric(code)\n  )\n}\n\n/**\n * @param {Array<Event>} events\n * @returns {boolean}\n */\nfunction previousUnbalanced(events) {\n  let index = events.length\n  let result = false\n\n  while (index--) {\n    const token = events[index][1]\n\n    if (\n      (token.type === 'labelLink' || token.type === 'labelImage') &&\n      !token._balanced\n    ) {\n      result = true\n      break\n    }\n\n    // If we’ve seen this token, and it was marked as not having any unbalanced\n    // bracket before it, we can exit.\n    if (token._gfmAutolinkLiteralWalkedInto) {\n      result = false\n      break\n    }\n  }\n\n  if (events.length > 0 && !result) {\n    // Mark the last token as “walked into” w/o finding\n    // anything.\n    events[events.length - 1][1]._gfmAutolinkLiteralWalkedInto = true\n  }\n\n  return result\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;AAQA;;;AAEA,MAAM,YAAY;IAAC,UAAU;IAAmB,SAAS;AAAI;AAC7D,MAAM,SAAS;IAAC,UAAU;IAAgB,SAAS;AAAI;AACvD,MAAM,OAAO;IAAC,UAAU;IAAc,SAAS;AAAI;AACnD,MAAM,QAAQ;IAAC,UAAU;IAAe,SAAS;AAAI;AACrD,MAAM,sBAAsB;IAC1B,UAAU;IACV,SAAS;AACX;AAEA,MAAM,cAAc;IAClB,MAAM;IACN,UAAU;IACV,UAAU;AACZ;AAEA,MAAM,mBAAmB;IACvB,MAAM;IACN,UAAU;IACV,UAAU;AACZ;AAEA,MAAM,gBAAgB;IACpB,MAAM;IACN,UAAU;IACV,UAAU;AACZ;AAEA,4BAA4B,GAC5B,MAAM,OAAO,CAAC;AAUP,SAAS;IACd,OAAO;QAAC;IAAI;AACd;AAEA,iBAAiB,GACjB,IAAI,OAAO,8JAAA,CAAA,QAAK,CAAC,MAAM;AAEvB,qBAAqB;AACrB,MAAO,OAAO,8JAAA,CAAA,QAAK,CAAC,cAAc,CAAE;IAClC,IAAI,CAAC,KAAK,GAAG;IACb;IACA,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK,EAAE,OAAO,8JAAA,CAAA,QAAK,CAAC,UAAU;SAC5C,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,iBAAiB,EAAE,OAAO,8JAAA,CAAA,QAAK,CAAC,UAAU;AACpE;AAEA,IAAI,CAAC,8JAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,GAAG;AACvB,IAAI,CAAC,8JAAA,CAAA,QAAK,CAAC,IAAI,CAAC,GAAG;AACnB,IAAI,CAAC,8JAAA,CAAA,QAAK,CAAC,GAAG,CAAC,GAAG;AAClB,IAAI,CAAC,8JAAA,CAAA,QAAK,CAAC,UAAU,CAAC,GAAG;AACzB,IAAI,CAAC,8JAAA,CAAA,QAAK,CAAC,UAAU,CAAC,GAAG;IAAC;IAAe;CAAiB;AAC1D,IAAI,CAAC,8JAAA,CAAA,QAAK,CAAC,UAAU,CAAC,GAAG;IAAC;IAAe;CAAiB;AAC1D,IAAI,CAAC,8JAAA,CAAA,QAAK,CAAC,UAAU,CAAC,GAAG;IAAC;IAAe;CAAY;AACrD,IAAI,CAAC,8JAAA,CAAA,QAAK,CAAC,UAAU,CAAC,GAAG;IAAC;IAAe;CAAY;AAErD,gEAAgE;AAChE,yDAAyD;AACzD,yEAAyE;AACzE,YAAY;AACZ,gDAAgD;AAEhD;;;;;;;;;;CAUC,GACD,SAAS,sBAAsB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC7C,MAAM,OAAO,IAAI;IACjB,gCAAgC,GAChC,IAAI;IACJ,oBAAoB,GACpB,IAAI;IAEJ,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,IACE,CAAC,SAAS,SACV,CAAC,cAAc,IAAI,CAAC,MAAM,KAAK,QAAQ,KACvC,mBAAmB,KAAK,MAAM,GAC9B;YACA,OAAO,IAAI;QACb;QAEA,QAAQ,KAAK,CAAC;QACd,QAAQ,KAAK,CAAC;QACd,OAAO,MAAM;IACf;IAEA;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,IAAI,SAAS,OAAO;YAClB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,MAAM,EAAE;YACzB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;;;;;GAaC,GACD,SAAS,YAAY,IAAI;QACvB,mDAAmD;QACnD,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,OAAO,QAAQ,KAAK,CAClB,qBACA,kBACA,gBACA;QACJ;QAEA,gCAAgC;QAChC,IACE,SAAS,8JAAA,CAAA,QAAK,CAAC,IAAI,IACnB,SAAS,8JAAA,CAAA,QAAK,CAAC,UAAU,IACzB,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE,OAClB;YACA,OAAO;YACP,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,sBAAsB;QAEtB,mEAAmE;QACnE,wEAAwE;QACxE,gEAAgE;QAChE,+BAA+B;QAC/B,OAAO,iBAAiB;IAC1B;IAEA;;;;;;;;;GASC,GACD,SAAS,eAAe,IAAI;QAC1B,QAAQ,OAAO,CAAC;QAChB,MAAM;QACN,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,iBAAiB,IAAI;QAC5B,8EAA8E;QAC9E,yFAAyF;QACzF,IAAI,QAAQ,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,KAAK,QAAQ,GAAG;YAC5C,QAAQ,IAAI,CAAC;YACb,QAAQ,IAAI,CAAC;YACb,OAAO,GAAG;QACZ;QAEA,OAAO,IAAI;IACb;AACF;AAEA;;;;;;;;;;CAUC,GACD,SAAS,oBAAoB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC3C,MAAM,OAAO,IAAI;IAEjB,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,SAAS,IAAI;QACpB,IACE,AAAC,SAAS,8JAAA,CAAA,QAAK,CAAC,UAAU,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,UAAU,IACvD,CAAC,YAAY,IAAI,CAAC,MAAM,KAAK,QAAQ,KACrC,mBAAmB,KAAK,MAAM,GAC9B;YACA,OAAO,IAAI;QACb;QAEA,QAAQ,KAAK,CAAC;QACd,QAAQ,KAAK,CAAC;QACd,4DAA4D;QAC5D,wDAAwD;QACxD,OAAO,QAAQ,KAAK,CAClB,WACA,QAAQ,OAAO,CAAC,QAAQ,QAAQ,OAAO,CAAC,MAAM,WAAW,MACzD,KACA;IACJ;IAEA;;;;;;;;;GASC,GACD,SAAS,SAAS,IAAI;QACpB,QAAQ,IAAI,CAAC;QACb,QAAQ,IAAI,CAAC;QACb,OAAO,GAAG;IACZ;AACF;AAEA;;;;;;;;;;CAUC,GACD,SAAS,yBAAyB,OAAO,EAAE,EAAE,EAAE,GAAG;IAChD,MAAM,OAAO,IAAI;IACjB,IAAI,SAAS;IACb,IAAI,OAAO;IAEX,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,cAAc,IAAI;QACzB,IACE,CAAC,SAAS,8JAAA,CAAA,QAAK,CAAC,UAAU,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,UAAU,KACvD,iBAAiB,IAAI,CAAC,MAAM,KAAK,QAAQ,KACzC,CAAC,mBAAmB,KAAK,MAAM,GAC/B;YACA,QAAQ,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC;YACd,UAAU,OAAO,aAAa,CAAC;YAC/B,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,qBAAqB,IAAI;QAChC,yBAAyB;QACzB,IAAI,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,SAAS,OAAO,MAAM,GAAG,GAAG;YACzC,uCAAuC;YACvC,UAAU,OAAO,aAAa,CAAC;YAC/B,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK,EAAE;YACxB,MAAM,WAAW,OAAO,WAAW;YAEnC,IAAI,aAAa,UAAU,aAAa,SAAS;gBAC/C,QAAQ,OAAO,CAAC;gBAChB,OAAO;YACT;QACF;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,sBAAsB,IAAI;QACjC,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK,EAAE;YACxB,QAAQ,OAAO,CAAC;YAEhB,IAAI,MAAM;gBACR,OAAO;YACT;YAEA,OAAO;YACP,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,cAAc,IAAI;QACzB,+CAA+C;QAC/C,sIAAsI;QACtI,OAAO,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,IACvB,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE,SACb,CAAA,GAAA,iKAAA,CAAA,4BAAyB,AAAD,EAAE,SAC1B,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE,SAClB,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,QACjB,IAAI,QACJ,QAAQ,OAAO,CAAC,QAAQ,QAAQ,OAAO,CAAC,MAAM,gBAAgB,KAAK;IACzE;IAEA;;;;;;;;;GASC,GACD,SAAS,cAAc,IAAI;QACzB,QAAQ,IAAI,CAAC;QACb,QAAQ,IAAI,CAAC;QACb,OAAO,GAAG;IACZ;AACF;AAEA;;;;;;;;;;CAUC,GACD,SAAS,kBAAkB,OAAO,EAAE,EAAE,EAAE,GAAG;IACzC,IAAI,OAAO;IAEX,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,gBAAgB,IAAI;QAC3B,IAAI,CAAC,SAAS,8JAAA,CAAA,QAAK,CAAC,UAAU,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,UAAU,KAAK,OAAO,GAAG;YACxE;YACA,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,SAAS,GAAG;YACpC,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,eAAe,IAAI;QAC1B,uCAAuC;QACvC,OAAO,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,GAAG,IAAI,QAAQ,GAAG;IAC7C;AACF;AAEA;;;;;;;;;;CAUC,GACD,SAAS,eAAe,OAAO,EAAE,EAAE,EAAE,GAAG;IACtC,gCAAgC,GAChC,IAAI;IACJ,gCAAgC,GAChC,IAAI;IACJ,gCAAgC,GAChC,IAAI;IAEJ,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,aAAa,IAAI;QACxB,6DAA6D;QAC7D,iEAAiE;QACjE,sBAAsB;QACtB,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,UAAU,EAAE;YACnD,OAAO,QAAQ,KAAK,CAAC,OAAO,aAAa,qBAAqB;QAChE;QAEA,0EAA0E;QAC1E,2EAA2E;QAC3E,qBAAqB;QACrB,qEAAqE;QACrE,yEAAyE;QACzE,wFAAwF;QACxF,IACE,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,CAAA,GAAA,iKAAA,CAAA,4BAAyB,AAAD,EAAE,SAC1B,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE,SACjB,SAAS,8JAAA,CAAA,QAAK,CAAC,IAAI,IAAI,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,OAC3C;YACA,OAAO,YAAY;QACrB;QAEA,OAAO;QACP,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,oBAAoB,IAAI;QAC/B,2DAA2D;QAC3D,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,UAAU,EAAE;YAC7B,0BAA0B;QAC5B,OAGK;YACH,8BAA8B;YAC9B,0BAA0B;QAC5B;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;mBAQiB,GACjB,SAAS,YAAY,IAAI;QACvB,2DAA2D;QAC3D,mDAAmD;QACnD,IAAI,+BAA+B,2BAA2B,CAAC,MAAM;YACnE,OAAO,IAAI;QACb;QAEA,OAAO,GAAG;IACZ;AACF;AAEA;;;;;;;;;;CAUC,GACD,SAAS,aAAa,OAAO,EAAE,EAAE;IAC/B,IAAI,WAAW;IACf,IAAI,YAAY;IAEhB,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,WAAW,IAAI;QACtB,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,eAAe,EAAE;YAClC;YACA,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,wCAAwC;QACxC,iEAAiE;QACjE,8BAA8B;QAC9B,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,gBAAgB,IAAI,YAAY,UAAU;YAC3D,OAAO,kBAAkB;QAC3B;QAEA,+DAA+D;QAC/D,uDAAuD;QACvD,aAAa;QACb,IACE,SAAS,8JAAA,CAAA,QAAK,CAAC,eAAe,IAC9B,SAAS,8JAAA,CAAA,QAAK,CAAC,aAAa,IAC5B,SAAS,8JAAA,CAAA,QAAK,CAAC,SAAS,IACxB,SAAS,8JAAA,CAAA,QAAK,CAAC,UAAU,IACzB,SAAS,8JAAA,CAAA,QAAK,CAAC,gBAAgB,IAC/B,SAAS,8JAAA,CAAA,QAAK,CAAC,QAAQ,IACvB,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK,IACpB,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK,IACpB,SAAS,8JAAA,CAAA,QAAK,CAAC,SAAS,IACxB,SAAS,8JAAA,CAAA,QAAK,CAAC,QAAQ,IACvB,SAAS,8JAAA,CAAA,QAAK,CAAC,YAAY,IAC3B,SAAS,8JAAA,CAAA,QAAK,CAAC,kBAAkB,IACjC,SAAS,8JAAA,CAAA,QAAK,CAAC,UAAU,IACzB,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK,EACpB;YACA,OAAO,QAAQ,KAAK,CAAC,OAAO,IAAI,mBAAmB;QACrD;QAEA,IACE,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,CAAA,GAAA,iKAAA,CAAA,4BAAyB,AAAD,EAAE,SAC1B,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE,OAClB;YACA,OAAO,GAAG;QACZ;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,kBAAkB,IAAI;QAC7B,wBAAwB;QACxB,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,gBAAgB,EAAE;YACnC;QACF;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;AACF;AAEA;;;;;;;;;;;;;;CAcC,GACD,SAAS,cAAc,OAAO,EAAE,EAAE,EAAE,GAAG;IACrC,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,gCAAgC;QAChC,IACE,SAAS,8JAAA,CAAA,QAAK,CAAC,eAAe,IAC9B,SAAS,8JAAA,CAAA,QAAK,CAAC,aAAa,IAC5B,SAAS,8JAAA,CAAA,QAAK,CAAC,UAAU,IACzB,SAAS,8JAAA,CAAA,QAAK,CAAC,gBAAgB,IAC/B,SAAS,8JAAA,CAAA,QAAK,CAAC,QAAQ,IACvB,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK,IACpB,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK,IACpB,SAAS,8JAAA,CAAA,QAAK,CAAC,SAAS,IACxB,SAAS,8JAAA,CAAA,QAAK,CAAC,YAAY,IAC3B,SAAS,8JAAA,CAAA,QAAK,CAAC,UAAU,IACzB,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK,EACpB;YACA,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,+DAA+D;QAC/D,iDAAiD;QACjD,mEAAmE;QACnE,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,SAAS,EAAE;YAC5B,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,yDAAyD;QACzD,oDAAoD;QACpD,+CAA+C;QAC/C,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,kBAAkB,EAAE;YACrC,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IACE,iBAAiB;QACjB,SAAS,8JAAA,CAAA,QAAK,CAAC,QAAQ,IACvB,oBAAoB;QACpB,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,CAAA,GAAA,iKAAA,CAAA,4BAAyB,AAAD,EAAE,SAC1B,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE,OAClB;YACA,OAAO,GAAG;QACZ;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;;;;GAYC,GACD,SAAS,kBAAkB,IAAI;QAC7B,+EAA+E;QAC/E,kCAAkC;QAClC,IACE,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,8JAAA,CAAA,QAAK,CAAC,eAAe,IAC9B,SAAS,8JAAA,CAAA,QAAK,CAAC,iBAAiB,IAChC,CAAA,GAAA,iKAAA,CAAA,4BAAyB,AAAD,EAAE,SAC1B,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE,OAClB;YACA,OAAO,GAAG;QACZ;QAEA,OAAO,MAAM;IACf;IAEA;;;;;;;;;GASC,GACD,SAAS,6BAA6B,IAAI;QACxC,oCAAoC;QACpC,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,8BAA8B,QAAQ,IAAI;IACtE;IAEA;;;;;;;;;GASC,GACD,SAAS,8BAA8B,IAAI;QACzC,+CAA+C;QAC/C,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,SAAS,EAAE;YAC5B,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,OAAO;YACpB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,oBAAoB;QACpB,OAAO,IAAI;IACb;AACF;AAEA;;;;;;;;;;;;;;CAcC,GACD,SAAS,4BAA4B,OAAO,EAAE,EAAE,EAAE,GAAG;IACnD,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,eAAe;QACf,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,+BAA+B;QAC/B,OAAO,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,IAAI,QAAQ,GAAG;IAClD;AACF;AAEA;;;;;CAKC,GACD,SAAS,YAAY,IAAI;IACvB,OACE,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,8JAAA,CAAA,QAAK,CAAC,eAAe,IAC9B,SAAS,8JAAA,CAAA,QAAK,CAAC,QAAQ,IACvB,SAAS,8JAAA,CAAA,QAAK,CAAC,UAAU,IACzB,SAAS,8JAAA,CAAA,QAAK,CAAC,iBAAiB,IAChC,SAAS,8JAAA,CAAA,QAAK,CAAC,kBAAkB,IACjC,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK,IACpB,CAAA,GAAA,iKAAA,CAAA,4BAAyB,AAAD,EAAE;AAE9B;AAEA;;;;;CAKC,GACD,SAAS,iBAAiB,IAAI;IAC5B,OAAO,CAAC,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE;AACrB;AAEA;;;CAGC,GACD,SAAS,cAAc,IAAI;IACzB,uCAAuC;IACvC,oEAAoE;IACpE,yFAAyF;IACzF,0DAA0D;IAC1D,OAAO,CAAC,CAAC,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK,IAAI,SAAS,KAAK;AACjD;AAEA;;;CAGC,GACD,SAAS,SAAS,IAAI;IACpB,OACE,SAAS,8JAAA,CAAA,QAAK,CAAC,QAAQ,IACvB,SAAS,8JAAA,CAAA,QAAK,CAAC,IAAI,IACnB,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,8JAAA,CAAA,QAAK,CAAC,UAAU,IACzB,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE;AAEtB;AAEA;;;CAGC,GACD,SAAS,mBAAmB,MAAM;IAChC,IAAI,QAAQ,OAAO,MAAM;IACzB,IAAI,SAAS;IAEb,MAAO,QAAS;QACd,MAAM,QAAQ,MAAM,CAAC,MAAM,CAAC,EAAE;QAE9B,IACE,CAAC,MAAM,IAAI,KAAK,eAAe,MAAM,IAAI,KAAK,YAAY,KAC1D,CAAC,MAAM,SAAS,EAChB;YACA,SAAS;YACT;QACF;QAEA,2EAA2E;QAC3E,kCAAkC;QAClC,IAAI,MAAM,6BAA6B,EAAE;YACvC,SAAS;YACT;QACF;IACF;IAEA,IAAI,OAAO,MAAM,GAAG,KAAK,CAAC,QAAQ;QAChC,mDAAmD;QACnD,YAAY;QACZ,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,6BAA6B,GAAG;IAC/D;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5449, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-util-encode/index.js"], "sourcesContent": ["const characterReferences = {'\"': 'quot', '&': 'amp', '<': 'lt', '>': 'gt'}\n\n/**\n * Encode only the dangerous HTML characters.\n *\n * This ensures that certain characters which have special meaning in HTML are\n * dealt with.\n * Technically, we can skip `>` and `\"` in many cases, but CM includes them.\n *\n * @param {string} value\n *   Value to encode.\n * @returns {string}\n *   Encoded value.\n */\nexport function encode(value) {\n  return value.replace(/[\"&<>]/g, replace)\n\n  /**\n   * @param {string} value\n   *   Value to replace.\n   * @returns {string}\n   *   Encoded value.\n   */\n  function replace(value) {\n    return (\n      '&' +\n      characterReferences[\n        /** @type {keyof typeof characterReferences} */ (value)\n      ] +\n      ';'\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,MAAM,sBAAsB;IAAC,KAAK;IAAQ,KAAK;IAAO,KAAK;IAAM,KAAK;AAAI;AAcnE,SAAS,OAAO,KAAK;IAC1B,OAAO,MAAM,OAAO,CAAC,WAAW;;IAEhC;;;;;GAKC,GACD,SAAS,QAAQ,KAAK;QACpB,OACE,MACA,mBAAmB,CACgC,MAClD,GACD;IAEJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5476, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-util-sanitize-uri/dev/index.js"], "sourcesContent": ["import {asciiAlphanumeric} from 'micromark-util-character'\nimport {encode} from 'micromark-util-encode'\nimport {codes, values} from 'micromark-util-symbol'\n\n/**\n * Make a value safe for injection as a URL.\n *\n * This encodes unsafe characters with percent-encoding and skips already\n * encoded sequences (see `normalizeUri`).\n * Further unsafe characters are encoded as character references (see\n * `micromark-util-encode`).\n *\n * A regex of allowed protocols can be given, in which case the URL is\n * sanitized.\n * For example, `/^(https?|ircs?|mailto|xmpp)$/i` can be used for `a[href]`, or\n * `/^https?$/i` for `img[src]` (this is what `github.com` allows).\n * If the URL includes an unknown protocol (one not matched by `protocol`, such\n * as a dangerous example, `javascript:`), the value is ignored.\n *\n * @param {string | null | undefined} url\n *   URI to sanitize.\n * @param {RegExp | null | undefined} [protocol]\n *   Allowed protocols.\n * @returns {string}\n *   Sanitized URI.\n */\nexport function sanitizeUri(url, protocol) {\n  const value = encode(normalizeUri(url || ''))\n\n  if (!protocol) {\n    return value\n  }\n\n  const colon = value.indexOf(':')\n  const questionMark = value.indexOf('?')\n  const numberSign = value.indexOf('#')\n  const slash = value.indexOf('/')\n\n  if (\n    // If there is no protocol, it’s relative.\n    colon < 0 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash > -1 && colon > slash) ||\n    (questionMark > -1 && colon > questionMark) ||\n    (numberSign > -1 && colon > numberSign) ||\n    // It is a protocol, it should be allowed.\n    protocol.test(value.slice(0, colon))\n  ) {\n    return value\n  }\n\n  return ''\n}\n\n/**\n * Normalize a URL.\n *\n * Encode unsafe characters with percent-encoding, skipping already encoded\n * sequences.\n *\n * @param {string} value\n *   URI to normalize.\n * @returns {string}\n *   Normalized URI.\n */\nexport function normalizeUri(value) {\n  /** @type {Array<string>} */\n  const result = []\n  let index = -1\n  let start = 0\n  let skip = 0\n\n  while (++index < value.length) {\n    const code = value.charCodeAt(index)\n    /** @type {string} */\n    let replace = ''\n\n    // A correct percent encoded value.\n    if (\n      code === codes.percentSign &&\n      asciiAlphanumeric(value.charCodeAt(index + 1)) &&\n      asciiAlphanumeric(value.charCodeAt(index + 2))\n    ) {\n      skip = 2\n    }\n    // ASCII.\n    else if (code < 128) {\n      if (!/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(code))) {\n        replace = String.fromCharCode(code)\n      }\n    }\n    // Astral.\n    else if (code > 55_295 && code < 57_344) {\n      const next = value.charCodeAt(index + 1)\n\n      // A correct surrogate pair.\n      if (code < 56_320 && next > 56_319 && next < 57_344) {\n        replace = String.fromCharCode(code, next)\n        skip = 1\n      }\n      // Lone surrogate.\n      else {\n        replace = values.replacementCharacter\n      }\n    }\n    // Unicode.\n    else {\n      replace = String.fromCharCode(code)\n    }\n\n    if (replace) {\n      result.push(value.slice(start, index), encodeURIComponent(replace))\n      start = index + skip + 1\n      replace = ''\n    }\n\n    if (skip) {\n      index += skip\n      skip = 0\n    }\n  }\n\n  return result.join('') + value.slice(start)\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;;;;AAwBO,SAAS,YAAY,GAAG,EAAE,QAAQ;IACvC,MAAM,QAAQ,CAAA,GAAA,uJAAA,CAAA,SAAM,AAAD,EAAE,aAAa,OAAO;IAEzC,IAAI,CAAC,UAAU;QACb,OAAO;IACT;IAEA,MAAM,QAAQ,MAAM,OAAO,CAAC;IAC5B,MAAM,eAAe,MAAM,OAAO,CAAC;IACnC,MAAM,aAAa,MAAM,OAAO,CAAC;IACjC,MAAM,QAAQ,MAAM,OAAO,CAAC;IAE5B,IACE,0CAA0C;IAC1C,QAAQ,KAEP,QAAQ,CAAC,KAAK,QAAQ,SACtB,eAAe,CAAC,KAAK,QAAQ,gBAC7B,aAAa,CAAC,KAAK,QAAQ,cAC5B,0CAA0C;IAC1C,SAAS,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,SAC7B;QACA,OAAO;IACT;IAEA,OAAO;AACT;AAaO,SAAS,aAAa,KAAK;IAChC,0BAA0B,GAC1B,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,CAAC;IACb,IAAI,QAAQ;IACZ,IAAI,OAAO;IAEX,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,MAAM,OAAO,MAAM,UAAU,CAAC;QAC9B,mBAAmB,GACnB,IAAI,UAAU;QAEd,mCAAmC;QACnC,IACE,SAAS,8JAAA,CAAA,QAAK,CAAC,WAAW,IAC1B,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,UAAU,CAAC,QAAQ,OAC3C,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,UAAU,CAAC,QAAQ,KAC3C;YACA,OAAO;QACT,OAEK,IAAI,OAAO,KAAK;YACnB,IAAI,CAAC,oBAAoB,IAAI,CAAC,OAAO,YAAY,CAAC,QAAQ;gBACxD,UAAU,OAAO,YAAY,CAAC;YAChC;QACF,OAEK,IAAI,OAAO,UAAU,OAAO,QAAQ;YACvC,MAAM,OAAO,MAAM,UAAU,CAAC,QAAQ;YAEtC,4BAA4B;YAC5B,IAAI,OAAO,UAAU,OAAO,UAAU,OAAO,QAAQ;gBACnD,UAAU,OAAO,YAAY,CAAC,MAAM;gBACpC,OAAO;YACT,OAEK;gBACH,UAAU,+JAAA,CAAA,SAAM,CAAC,oBAAoB;YACvC;QACF,OAEK;YACH,UAAU,OAAO,YAAY,CAAC;QAChC;QAEA,IAAI,SAAS;YACX,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,OAAO,QAAQ,mBAAmB;YAC1D,QAAQ,QAAQ,OAAO;YACvB,UAAU;QACZ;QAEA,IAAI,MAAM;YACR,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO,OAAO,IAAI,CAAC,MAAM,MAAM,KAAK,CAAC;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5548, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-extension-gfm-autolink-literal/dev/lib/html.js"], "sourcesContent": ["/**\n * @import {CompileContext, Handle, HtmlExtension, Token} from 'micromark-util-types'\n */\n\nimport {sanitizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Create an HTML extension for `micromark` to support GitHub autolink literal\n * when serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GitHub autolink literal when serializing to HTML.\n */\nexport function gfmAutolinkLiteralHtml() {\n  return {\n    exit: {literalAutolinkEmail, literalAutolinkHttp, literalAutolinkWww}\n  }\n}\n\n/**\n * @this {CompileContext}\n * @type {Handle}\n */\nfunction literalAutolinkWww(token) {\n  anchorFromToken.call(this, token, 'http://')\n}\n\n/**\n * @this {CompileContext}\n * @type {Handle}\n */\nfunction literalAutolinkEmail(token) {\n  anchorFromToken.call(this, token, 'mailto:')\n}\n\n/**\n * @this {CompileContext}\n * @type {Handle}\n */\nfunction literalAutolinkHttp(token) {\n  anchorFromToken.call(this, token)\n}\n\n/**\n * @this CompileContext\n * @param {Token} token\n * @param {string | null | undefined} [protocol]\n * @returns {undefined}\n */\nfunction anchorFromToken(token, protocol) {\n  const url = this.sliceSerialize(token)\n  this.tag('<a href=\"' + sanitizeUri((protocol || '') + url) + '\">')\n  this.raw(this.encode(url))\n  this.tag('</a>')\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;;AAUO,SAAS;IACd,OAAO;QACL,MAAM;YAAC;YAAsB;YAAqB;QAAkB;IACtE;AACF;AAEA;;;CAGC,GACD,SAAS,mBAAmB,KAAK;IAC/B,gBAAgB,IAAI,CAAC,IAAI,EAAE,OAAO;AACpC;AAEA;;;CAGC,GACD,SAAS,qBAAqB,KAAK;IACjC,gBAAgB,IAAI,CAAC,IAAI,EAAE,OAAO;AACpC;AAEA;;;CAGC,GACD,SAAS,oBAAoB,KAAK;IAChC,gBAAgB,IAAI,CAAC,IAAI,EAAE;AAC7B;AAEA;;;;;CAKC,GACD,SAAS,gBAAgB,KAAK,EAAE,QAAQ;IACtC,MAAM,MAAM,IAAI,CAAC,cAAc,CAAC;IAChC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAA,GAAA,uKAAA,CAAA,cAAW,AAAD,EAAE,CAAC,YAAY,EAAE,IAAI,OAAO;IAC7D,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;IACrB,IAAI,CAAC,GAAG,CAAC;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5599, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-factory-space/dev/index.js"], "sourcesContent": ["/**\n * @import {Effects, State, TokenType} from 'micromark-util-types'\n */\n\nimport {markdownSpace} from 'micromark-util-character'\n\n// To do: implement `spaceOrTab`, `spaceOrTabMinMax`, `spaceOrTabWithOptions`.\n\n/**\n * Parse spaces and tabs.\n *\n * There is no `nok` parameter:\n *\n * *   spaces in markdown are often optional, in which case this factory can be\n *     used and `ok` will be switched to whether spaces were found or not\n * *   one line ending or space can be detected with `markdownSpace(code)` right\n *     before using `factorySpace`\n *\n * ###### Examples\n *\n * Where `␉` represents a tab (plus how much it expands) and `␠` represents a\n * single space.\n *\n * ```markdown\n * ␉\n * ␠␠␠␠\n * ␉␠\n * ```\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {TokenType} type\n *   Type (`' \\t'`).\n * @param {number | undefined} [max=Infinity]\n *   Max (exclusive).\n * @returns {State}\n *   Start state.\n */\nexport function factorySpace(effects, ok, type, max) {\n  const limit = max ? max - 1 : Number.POSITIVE_INFINITY\n  let size = 0\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    if (markdownSpace(code)) {\n      effects.enter(type)\n      return prefix(code)\n    }\n\n    return ok(code)\n  }\n\n  /** @type {State} */\n  function prefix(code) {\n    if (markdownSpace(code) && size++ < limit) {\n      effects.consume(code)\n      return prefix\n    }\n\n    effects.exit(type)\n    return ok(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;;AAoCO,SAAS,aAAa,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG;IACjD,MAAM,QAAQ,MAAM,MAAM,IAAI,OAAO,iBAAiB;IACtD,IAAI,OAAO;IAEX,OAAO;;IAEP,kBAAkB,GAClB,SAAS,MAAM,IAAI;QACjB,IAAI,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,QAAQ,KAAK,CAAC;YACd,OAAO,OAAO;QAChB;QAEA,OAAO,GAAG;IACZ;IAEA,kBAAkB,GAClB,SAAS,OAAO,IAAI;QAClB,IAAI,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,SAAS,OAAO;YACzC,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,QAAQ,IAAI,CAAC;QACb,OAAO,GAAG;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5633, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-util-symbol/lib/types.js"], "sourcesContent": ["/**\n * This module is compiled away!\n *\n * Here is the list of all types of tokens exposed by micromark, with a short\n * explanation of what they include and where they are found.\n * In picking names, generally, the rule is to be as explicit as possible\n * instead of reusing names.\n * For example, there is a `definitionDestination` and a `resourceDestination`,\n * instead of one shared name.\n */\n\n// Note: when changing the next record, you must also change `TokenTypeMap`\n// in `micromark-util-types/index.d.ts`.\nexport const types = /** @type {const} */ ({\n  // Generic type for data, such as in a title, a destination, etc.\n  data: 'data',\n\n  // Generic type for syntactic whitespace (tabs, virtual spaces, spaces).\n  // Such as, between a fenced code fence and an info string.\n  whitespace: 'whitespace',\n\n  // Generic type for line endings (line feed, carriage return, carriage return +\n  // line feed).\n  lineEnding: 'lineEnding',\n\n  // A line ending, but ending a blank line.\n  lineEndingBlank: 'lineEndingBlank',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the start of a\n  // line.\n  linePrefix: 'linePrefix',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the end of a\n  // line.\n  lineSuffix: 'lineSuffix',\n\n  // Whole ATX heading:\n  //\n  // ```markdown\n  // #\n  // ## Alpha\n  // ### Bravo ###\n  // ```\n  //\n  // Includes `atxHeadingSequence`, `whitespace`, `atxHeadingText`.\n  atxHeading: 'atxHeading',\n\n  // Sequence of number signs in an ATX heading (`###`).\n  atxHeadingSequence: 'atxHeadingSequence',\n\n  // Content in an ATX heading (`alpha`).\n  // Includes text.\n  atxHeadingText: 'atxHeadingText',\n\n  // Whole autolink (`<https://example.com>` or `<<EMAIL>>`)\n  // Includes `autolinkMarker` and `autolinkProtocol` or `autolinkEmail`.\n  autolink: 'autolink',\n\n  // Email autolink w/o markers (`<EMAIL>`)\n  autolinkEmail: 'autolinkEmail',\n\n  // Marker around an `autolinkProtocol` or `autolinkEmail` (`<` or `>`).\n  autolinkMarker: 'autolinkMarker',\n\n  // Protocol autolink w/o markers (`https://example.com`)\n  autolinkProtocol: 'autolinkProtocol',\n\n  // A whole character escape (`\\-`).\n  // Includes `escapeMarker` and `characterEscapeValue`.\n  characterEscape: 'characterEscape',\n\n  // The escaped character (`-`).\n  characterEscapeValue: 'characterEscapeValue',\n\n  // A whole character reference (`&amp;`, `&#8800;`, or `&#x1D306;`).\n  // Includes `characterReferenceMarker`, an optional\n  // `characterReferenceMarkerNumeric`, in which case an optional\n  // `characterReferenceMarkerHexadecimal`, and a `characterReferenceValue`.\n  characterReference: 'characterReference',\n\n  // The start or end marker (`&` or `;`).\n  characterReferenceMarker: 'characterReferenceMarker',\n\n  // Mark reference as numeric (`#`).\n  characterReferenceMarkerNumeric: 'characterReferenceMarkerNumeric',\n\n  // Mark reference as numeric (`x` or `X`).\n  characterReferenceMarkerHexadecimal: 'characterReferenceMarkerHexadecimal',\n\n  // Value of character reference w/o markers (`amp`, `8800`, or `1D306`).\n  characterReferenceValue: 'characterReferenceValue',\n\n  // Whole fenced code:\n  //\n  // ````markdown\n  // ```js\n  // alert(1)\n  // ```\n  // ````\n  codeFenced: 'codeFenced',\n\n  // A fenced code fence, including whitespace, sequence, info, and meta\n  // (` ```js `).\n  codeFencedFence: 'codeFencedFence',\n\n  // Sequence of grave accent or tilde characters (` ``` `) in a fence.\n  codeFencedFenceSequence: 'codeFencedFenceSequence',\n\n  // Info word (`js`) in a fence.\n  // Includes string.\n  codeFencedFenceInfo: 'codeFencedFenceInfo',\n\n  // Meta words (`highlight=\"1\"`) in a fence.\n  // Includes string.\n  codeFencedFenceMeta: 'codeFencedFenceMeta',\n\n  // A line of code.\n  codeFlowValue: 'codeFlowValue',\n\n  // Whole indented code:\n  //\n  // ```markdown\n  //     alert(1)\n  // ```\n  //\n  // Includes `lineEnding`, `linePrefix`, and `codeFlowValue`.\n  codeIndented: 'codeIndented',\n\n  // A text code (``` `alpha` ```).\n  // Includes `codeTextSequence`, `codeTextData`, `lineEnding`, and can include\n  // `codeTextPadding`.\n  codeText: 'codeText',\n\n  codeTextData: 'codeTextData',\n\n  // A space or line ending right after or before a tick.\n  codeTextPadding: 'codeTextPadding',\n\n  // A text code fence (` `` `).\n  codeTextSequence: 'codeTextSequence',\n\n  // Whole content:\n  //\n  // ```markdown\n  // [a]: b\n  // c\n  // =\n  // d\n  // ```\n  //\n  // Includes `paragraph` and `definition`.\n  content: 'content',\n  // Whole definition:\n  //\n  // ```markdown\n  // [micromark]: https://github.com/micromark/micromark\n  // ```\n  //\n  // Includes `definitionLabel`, `definitionMarker`, `whitespace`,\n  // `definitionDestination`, and optionally `lineEnding` and `definitionTitle`.\n  definition: 'definition',\n\n  // Destination of a definition (`https://github.com/micromark/micromark` or\n  // `<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteral` or `definitionDestinationRaw`.\n  definitionDestination: 'definitionDestination',\n\n  // Enclosed destination of a definition\n  // (`<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteralMarker` and optionally\n  // `definitionDestinationString`.\n  definitionDestinationLiteral: 'definitionDestinationLiteral',\n\n  // Markers of an enclosed definition destination (`<` or `>`).\n  definitionDestinationLiteralMarker: 'definitionDestinationLiteralMarker',\n\n  // Unenclosed destination of a definition\n  // (`https://github.com/micromark/micromark`).\n  // Includes `definitionDestinationString`.\n  definitionDestinationRaw: 'definitionDestinationRaw',\n\n  // Text in an destination (`https://github.com/micromark/micromark`).\n  // Includes string.\n  definitionDestinationString: 'definitionDestinationString',\n\n  // Label of a definition (`[micromark]`).\n  // Includes `definitionLabelMarker` and `definitionLabelString`.\n  definitionLabel: 'definitionLabel',\n\n  // Markers of a definition label (`[` or `]`).\n  definitionLabelMarker: 'definitionLabelMarker',\n\n  // Value of a definition label (`micromark`).\n  // Includes string.\n  definitionLabelString: 'definitionLabelString',\n\n  // Marker between a label and a destination (`:`).\n  definitionMarker: 'definitionMarker',\n\n  // Title of a definition (`\"x\"`, `'y'`, or `(z)`).\n  // Includes `definitionTitleMarker` and optionally `definitionTitleString`.\n  definitionTitle: 'definitionTitle',\n\n  // Marker around a title of a definition (`\"`, `'`, `(`, or `)`).\n  definitionTitleMarker: 'definitionTitleMarker',\n\n  // Data without markers in a title (`z`).\n  // Includes string.\n  definitionTitleString: 'definitionTitleString',\n\n  // Emphasis (`*alpha*`).\n  // Includes `emphasisSequence` and `emphasisText`.\n  emphasis: 'emphasis',\n\n  // Sequence of emphasis markers (`*` or `_`).\n  emphasisSequence: 'emphasisSequence',\n\n  // Emphasis text (`alpha`).\n  // Includes text.\n  emphasisText: 'emphasisText',\n\n  // The character escape marker (`\\`).\n  escapeMarker: 'escapeMarker',\n\n  // A hard break created with a backslash (`\\\\n`).\n  // Note: does not include the line ending.\n  hardBreakEscape: 'hardBreakEscape',\n\n  // A hard break created with trailing spaces (`  \\n`).\n  // Does not include the line ending.\n  hardBreakTrailing: 'hardBreakTrailing',\n\n  // Flow HTML:\n  //\n  // ```markdown\n  // <div\n  // ```\n  //\n  // Inlcudes `lineEnding`, `htmlFlowData`.\n  htmlFlow: 'htmlFlow',\n\n  htmlFlowData: 'htmlFlowData',\n\n  // HTML in text (the tag in `a <i> b`).\n  // Includes `lineEnding`, `htmlTextData`.\n  htmlText: 'htmlText',\n\n  htmlTextData: 'htmlTextData',\n\n  // Whole image (`![alpha](bravo)`, `![alpha][bravo]`, `![alpha][]`, or\n  // `![alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  image: 'image',\n\n  // Whole link label (`[*alpha*]`).\n  // Includes `labelLink` or `labelImage`, `labelText`, and `labelEnd`.\n  label: 'label',\n\n  // Text in an label (`*alpha*`).\n  // Includes text.\n  labelText: 'labelText',\n\n  // Start a link label (`[`).\n  // Includes a `labelMarker`.\n  labelLink: 'labelLink',\n\n  // Start an image label (`![`).\n  // Includes `labelImageMarker` and `labelMarker`.\n  labelImage: 'labelImage',\n\n  // Marker of a label (`[` or `]`).\n  labelMarker: 'labelMarker',\n\n  // Marker to start an image (`!`).\n  labelImageMarker: 'labelImageMarker',\n\n  // End a label (`]`).\n  // Includes `labelMarker`.\n  labelEnd: 'labelEnd',\n\n  // Whole link (`[alpha](bravo)`, `[alpha][bravo]`, `[alpha][]`, or `[alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  link: 'link',\n\n  // Whole paragraph:\n  //\n  // ```markdown\n  // alpha\n  // bravo.\n  // ```\n  //\n  // Includes text.\n  paragraph: 'paragraph',\n\n  // A reference (`[alpha]` or `[]`).\n  // Includes `referenceMarker` and an optional `referenceString`.\n  reference: 'reference',\n\n  // A reference marker (`[` or `]`).\n  referenceMarker: 'referenceMarker',\n\n  // Reference text (`alpha`).\n  // Includes string.\n  referenceString: 'referenceString',\n\n  // A resource (`(https://example.com \"alpha\")`).\n  // Includes `resourceMarker`, an optional `resourceDestination` with an optional\n  // `whitespace` and `resourceTitle`.\n  resource: 'resource',\n\n  // A resource destination (`https://example.com`).\n  // Includes `resourceDestinationLiteral` or `resourceDestinationRaw`.\n  resourceDestination: 'resourceDestination',\n\n  // A literal resource destination (`<https://example.com>`).\n  // Includes `resourceDestinationLiteralMarker` and optionally\n  // `resourceDestinationString`.\n  resourceDestinationLiteral: 'resourceDestinationLiteral',\n\n  // A resource destination marker (`<` or `>`).\n  resourceDestinationLiteralMarker: 'resourceDestinationLiteralMarker',\n\n  // A raw resource destination (`https://example.com`).\n  // Includes `resourceDestinationString`.\n  resourceDestinationRaw: 'resourceDestinationRaw',\n\n  // Resource destination text (`https://example.com`).\n  // Includes string.\n  resourceDestinationString: 'resourceDestinationString',\n\n  // A resource marker (`(` or `)`).\n  resourceMarker: 'resourceMarker',\n\n  // A resource title (`\"alpha\"`, `'alpha'`, or `(alpha)`).\n  // Includes `resourceTitleMarker` and optionally `resourceTitleString`.\n  resourceTitle: 'resourceTitle',\n\n  // A resource title marker (`\"`, `'`, `(`, or `)`).\n  resourceTitleMarker: 'resourceTitleMarker',\n\n  // Resource destination title (`alpha`).\n  // Includes string.\n  resourceTitleString: 'resourceTitleString',\n\n  // Whole setext heading:\n  //\n  // ```markdown\n  // alpha\n  // bravo\n  // =====\n  // ```\n  //\n  // Includes `setextHeadingText`, `lineEnding`, `linePrefix`, and\n  // `setextHeadingLine`.\n  setextHeading: 'setextHeading',\n\n  // Content in a setext heading (`alpha\\nbravo`).\n  // Includes text.\n  setextHeadingText: 'setextHeadingText',\n\n  // Underline in a setext heading, including whitespace suffix (`==`).\n  // Includes `setextHeadingLineSequence`.\n  setextHeadingLine: 'setextHeadingLine',\n\n  // Sequence of equals or dash characters in underline in a setext heading (`-`).\n  setextHeadingLineSequence: 'setextHeadingLineSequence',\n\n  // Strong (`**alpha**`).\n  // Includes `strongSequence` and `strongText`.\n  strong: 'strong',\n\n  // Sequence of strong markers (`**` or `__`).\n  strongSequence: 'strongSequence',\n\n  // Strong text (`alpha`).\n  // Includes text.\n  strongText: 'strongText',\n\n  // Whole thematic break:\n  //\n  // ```markdown\n  // * * *\n  // ```\n  //\n  // Includes `thematicBreakSequence` and `whitespace`.\n  thematicBreak: 'thematicBreak',\n\n  // A sequence of one or more thematic break markers (`***`).\n  thematicBreakSequence: 'thematicBreakSequence',\n\n  // Whole block quote:\n  //\n  // ```markdown\n  // > a\n  // >\n  // > b\n  // ```\n  //\n  // Includes `blockQuotePrefix` and flow.\n  blockQuote: 'blockQuote',\n  // The `>` or `> ` of a block quote.\n  blockQuotePrefix: 'blockQuotePrefix',\n  // The `>` of a block quote prefix.\n  blockQuoteMarker: 'blockQuoteMarker',\n  // The optional ` ` of a block quote prefix.\n  blockQuotePrefixWhitespace: 'blockQuotePrefixWhitespace',\n\n  // Whole ordered list:\n  //\n  // ```markdown\n  // 1. a\n  //    b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listOrdered: 'listOrdered',\n\n  // Whole unordered list:\n  //\n  // ```markdown\n  // - a\n  //   b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listUnordered: 'listUnordered',\n\n  // The indent of further list item lines.\n  listItemIndent: 'listItemIndent',\n\n  // A marker, as in, `*`, `+`, `-`, `.`, or `)`.\n  listItemMarker: 'listItemMarker',\n\n  // The thing that starts a list item, such as `1. `.\n  // Includes `listItemValue` if ordered, `listItemMarker`, and\n  // `listItemPrefixWhitespace` (unless followed by a line ending).\n  listItemPrefix: 'listItemPrefix',\n\n  // The whitespace after a marker.\n  listItemPrefixWhitespace: 'listItemPrefixWhitespace',\n\n  // The numerical value of an ordered item.\n  listItemValue: 'listItemValue',\n\n  // Internal types used for subtokenizers, compiled away\n  chunkDocument: 'chunkDocument',\n  chunkContent: 'chunkContent',\n  chunkFlow: 'chunkFlow',\n  chunkText: 'chunkText',\n  chunkString: 'chunkString'\n})\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC,GAED,2EAA2E;AAC3E,wCAAwC;;;;AACjC,MAAM,QAA8B;IACzC,iEAAiE;IACjE,MAAM;IAEN,wEAAwE;IACxE,2DAA2D;IAC3D,YAAY;IAEZ,+EAA+E;IAC/E,cAAc;IACd,YAAY;IAEZ,0CAA0C;IAC1C,iBAAiB;IAEjB,+EAA+E;IAC/E,QAAQ;IACR,YAAY;IAEZ,6EAA6E;IAC7E,QAAQ;IACR,YAAY;IAEZ,qBAAqB;IACrB,EAAE;IACF,cAAc;IACd,IAAI;IACJ,WAAW;IACX,gBAAgB;IAChB,MAAM;IACN,EAAE;IACF,iEAAiE;IACjE,YAAY;IAEZ,sDAAsD;IACtD,oBAAoB;IAEpB,uCAAuC;IACvC,iBAAiB;IACjB,gBAAgB;IAEhB,oEAAoE;IACpE,uEAAuE;IACvE,UAAU;IAEV,mDAAmD;IACnD,eAAe;IAEf,uEAAuE;IACvE,gBAAgB;IAEhB,wDAAwD;IACxD,kBAAkB;IAElB,mCAAmC;IACnC,sDAAsD;IACtD,iBAAiB;IAEjB,+BAA+B;IAC/B,sBAAsB;IAEtB,oEAAoE;IACpE,mDAAmD;IACnD,+DAA+D;IAC/D,0EAA0E;IAC1E,oBAAoB;IAEpB,wCAAwC;IACxC,0BAA0B;IAE1B,mCAAmC;IACnC,iCAAiC;IAEjC,0CAA0C;IAC1C,qCAAqC;IAErC,wEAAwE;IACxE,yBAAyB;IAEzB,qBAAqB;IACrB,EAAE;IACF,eAAe;IACf,QAAQ;IACR,WAAW;IACX,MAAM;IACN,OAAO;IACP,YAAY;IAEZ,sEAAsE;IACtE,eAAe;IACf,iBAAiB;IAEjB,qEAAqE;IACrE,yBAAyB;IAEzB,+BAA+B;IAC/B,mBAAmB;IACnB,qBAAqB;IAErB,2CAA2C;IAC3C,mBAAmB;IACnB,qBAAqB;IAErB,kBAAkB;IAClB,eAAe;IAEf,uBAAuB;IACvB,EAAE;IACF,cAAc;IACd,eAAe;IACf,MAAM;IACN,EAAE;IACF,4DAA4D;IAC5D,cAAc;IAEd,iCAAiC;IACjC,6EAA6E;IAC7E,qBAAqB;IACrB,UAAU;IAEV,cAAc;IAEd,uDAAuD;IACvD,iBAAiB;IAEjB,8BAA8B;IAC9B,kBAAkB;IAElB,iBAAiB;IACjB,EAAE;IACF,cAAc;IACd,SAAS;IACT,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,MAAM;IACN,EAAE;IACF,yCAAyC;IACzC,SAAS;IACT,oBAAoB;IACpB,EAAE;IACF,cAAc;IACd,sDAAsD;IACtD,MAAM;IACN,EAAE;IACF,gEAAgE;IAChE,8EAA8E;IAC9E,YAAY;IAEZ,2EAA2E;IAC3E,+CAA+C;IAC/C,yEAAyE;IACzE,uBAAuB;IAEvB,uCAAuC;IACvC,gDAAgD;IAChD,+DAA+D;IAC/D,iCAAiC;IACjC,8BAA8B;IAE9B,8DAA8D;IAC9D,oCAAoC;IAEpC,yCAAyC;IACzC,8CAA8C;IAC9C,0CAA0C;IAC1C,0BAA0B;IAE1B,qEAAqE;IACrE,mBAAmB;IACnB,6BAA6B;IAE7B,yCAAyC;IACzC,gEAAgE;IAChE,iBAAiB;IAEjB,8CAA8C;IAC9C,uBAAuB;IAEvB,6CAA6C;IAC7C,mBAAmB;IACnB,uBAAuB;IAEvB,kDAAkD;IAClD,kBAAkB;IAElB,kDAAkD;IAClD,2EAA2E;IAC3E,iBAAiB;IAEjB,iEAAiE;IACjE,uBAAuB;IAEvB,yCAAyC;IACzC,mBAAmB;IACnB,uBAAuB;IAEvB,wBAAwB;IACxB,kDAAkD;IAClD,UAAU;IAEV,6CAA6C;IAC7C,kBAAkB;IAElB,2BAA2B;IAC3B,iBAAiB;IACjB,cAAc;IAEd,qCAAqC;IACrC,cAAc;IAEd,iDAAiD;IACjD,0CAA0C;IAC1C,iBAAiB;IAEjB,sDAAsD;IACtD,oCAAoC;IACpC,mBAAmB;IAEnB,aAAa;IACb,EAAE;IACF,cAAc;IACd,OAAO;IACP,MAAM;IACN,EAAE;IACF,yCAAyC;IACzC,UAAU;IAEV,cAAc;IAEd,uCAAuC;IACvC,yCAAyC;IACzC,UAAU;IAEV,cAAc;IAEd,sEAAsE;IACtE,eAAe;IACf,8DAA8D;IAC9D,OAAO;IAEP,kCAAkC;IAClC,qEAAqE;IACrE,OAAO;IAEP,gCAAgC;IAChC,iBAAiB;IACjB,WAAW;IAEX,4BAA4B;IAC5B,4BAA4B;IAC5B,WAAW;IAEX,+BAA+B;IAC/B,iDAAiD;IACjD,YAAY;IAEZ,kCAAkC;IAClC,aAAa;IAEb,kCAAkC;IAClC,kBAAkB;IAElB,qBAAqB;IACrB,0BAA0B;IAC1B,UAAU;IAEV,8EAA8E;IAC9E,8DAA8D;IAC9D,MAAM;IAEN,mBAAmB;IACnB,EAAE;IACF,cAAc;IACd,QAAQ;IACR,SAAS;IACT,MAAM;IACN,EAAE;IACF,iBAAiB;IACjB,WAAW;IAEX,mCAAmC;IACnC,gEAAgE;IAChE,WAAW;IAEX,mCAAmC;IACnC,iBAAiB;IAEjB,4BAA4B;IAC5B,mBAAmB;IACnB,iBAAiB;IAEjB,gDAAgD;IAChD,gFAAgF;IAChF,oCAAoC;IACpC,UAAU;IAEV,kDAAkD;IAClD,qEAAqE;IACrE,qBAAqB;IAErB,4DAA4D;IAC5D,6DAA6D;IAC7D,+BAA+B;IAC/B,4BAA4B;IAE5B,8CAA8C;IAC9C,kCAAkC;IAElC,sDAAsD;IACtD,wCAAwC;IACxC,wBAAwB;IAExB,qDAAqD;IACrD,mBAAmB;IACnB,2BAA2B;IAE3B,kCAAkC;IAClC,gBAAgB;IAEhB,yDAAyD;IACzD,uEAAuE;IACvE,eAAe;IAEf,mDAAmD;IACnD,qBAAqB;IAErB,wCAAwC;IACxC,mBAAmB;IACnB,qBAAqB;IAErB,wBAAwB;IACxB,EAAE;IACF,cAAc;IACd,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,EAAE;IACF,gEAAgE;IAChE,uBAAuB;IACvB,eAAe;IAEf,gDAAgD;IAChD,iBAAiB;IACjB,mBAAmB;IAEnB,qEAAqE;IACrE,wCAAwC;IACxC,mBAAmB;IAEnB,gFAAgF;IAChF,2BAA2B;IAE3B,wBAAwB;IACxB,8CAA8C;IAC9C,QAAQ;IAER,6CAA6C;IAC7C,gBAAgB;IAEhB,yBAAyB;IACzB,iBAAiB;IACjB,YAAY;IAEZ,wBAAwB;IACxB,EAAE;IACF,cAAc;IACd,QAAQ;IACR,MAAM;IACN,EAAE;IACF,qDAAqD;IACrD,eAAe;IAEf,4DAA4D;IAC5D,uBAAuB;IAEvB,qBAAqB;IACrB,EAAE;IACF,cAAc;IACd,MAAM;IACN,IAAI;IACJ,MAAM;IACN,MAAM;IACN,EAAE;IACF,wCAAwC;IACxC,YAAY;IACZ,oCAAoC;IACpC,kBAAkB;IAClB,mCAAmC;IACnC,kBAAkB;IAClB,4CAA4C;IAC5C,4BAA4B;IAE5B,sBAAsB;IACtB,EAAE;IACF,cAAc;IACd,OAAO;IACP,OAAO;IACP,MAAM;IACN,EAAE;IACF,+EAA+E;IAC/E,SAAS;IACT,aAAa;IAEb,wBAAwB;IACxB,EAAE;IACF,cAAc;IACd,MAAM;IACN,MAAM;IACN,MAAM;IACN,EAAE;IACF,+EAA+E;IAC/E,SAAS;IACT,eAAe;IAEf,yCAAyC;IACzC,gBAAgB;IAEhB,+CAA+C;IAC/C,gBAAgB;IAEhB,oDAAoD;IACpD,6DAA6D;IAC7D,iEAAiE;IACjE,gBAAgB;IAEhB,iCAAiC;IACjC,0BAA0B;IAE1B,0CAA0C;IAC1C,eAAe;IAEf,uDAAuD;IACvD,eAAe;IACf,cAAc;IACd,WAAW;IACX,WAAW;IACX,aAAa;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5999, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-core-commonmark/dev/lib/blank-line.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const blankLine = {partial: true, tokenize: tokenizeBlankLine}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlankLine(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of blank line.\n   *\n   * > 👉 **Note**: `␠` represents a space character.\n   *\n   * ```markdown\n   * > | ␠␠␊\n   *     ^\n   * > | ␊\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    return markdownSpace(code)\n      ? factorySpace(effects, after, types.linePrefix)(code)\n      : after(code)\n  }\n\n  /**\n   * At eof/eol, after optional whitespace.\n   *\n   * > 👉 **Note**: `␠` represents a space character.\n   *\n   * ```markdown\n   * > | ␠␠␊\n   *       ^\n   * > | ␊\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return code === codes.eof || markdownLineEnding(code) ? ok(code) : nok(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AAED;AACA;AACA;AAAA;;;;AAGO,MAAM,YAAY;IAAC,SAAS;IAAM,UAAU;AAAiB;AAEpE;;;;CAIC,GACD,SAAS,kBAAkB,OAAO,EAAE,EAAE,EAAE,GAAG;IACzC,OAAO;;IAEP;;;;;;;;;;;;;GAaC,GACD,SAAS,MAAM,IAAI;QACjB,OAAO,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE,QACjB,CAAA,GAAA,gKAAA,CAAA,eAAY,AAAD,EAAE,SAAS,OAAO,8JAAA,CAAA,QAAK,CAAC,UAAU,EAAE,QAC/C,MAAM;IACZ;IAEA;;;;;;;;;;;;;GAaC,GACD,SAAS,MAAM,IAAI;QACjB,OAAO,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ,GAAG,QAAQ,IAAI;IACzE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6066, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-extension-gfm-footnote/dev/lib/syntax.js"], "sourcesContent": ["/**\n * @import {Event, Exiter, Extension, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {blankLine} from 'micromark-core-commonmark'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEndingOrSpace} from 'micromark-util-character'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\nconst indent = {tokenize: tokenizeIndent, partial: true}\n\n// To do: micromark should support a `_hiddenGfmFootnoteSupport`, which only\n// affects label start (image).\n// That will let us drop `tokenizePotentialGfmFootnote*`.\n// It currently has a `_hiddenFootnoteSupport`, which affects that and more.\n// That can be removed when `micromark-extension-footnote` is archived.\n\n/**\n * Create an extension for `micromark` to enable GFM footnote syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to\n *   enable GFM footnote syntax.\n */\nexport function gfmFootnote() {\n  /** @type {Extension} */\n  return {\n    document: {\n      [codes.leftSquareBracket]: {\n        name: 'gfmFootnoteDefinition',\n        tokenize: tokenizeDefinitionStart,\n        continuation: {tokenize: tokenizeDefinitionContinuation},\n        exit: gfmFootnoteDefinitionEnd\n      }\n    },\n    text: {\n      [codes.leftSquareBracket]: {\n        name: 'gfmFootnoteCall',\n        tokenize: tokenizeGfmFootnoteCall\n      },\n      [codes.rightSquareBracket]: {\n        name: 'gfmPotentialFootnoteCall',\n        add: 'after',\n        tokenize: tokenizePotentialGfmFootnoteCall,\n        resolveTo: resolveToPotentialGfmFootnoteCall\n      }\n    }\n  }\n}\n\n// To do: remove after micromark update.\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizePotentialGfmFootnoteCall(effects, ok, nok) {\n  const self = this\n  let index = self.events.length\n  const defined = self.parser.gfmFootnotes || (self.parser.gfmFootnotes = [])\n  /** @type {Token} */\n  let labelStart\n\n  // Find an opening.\n  while (index--) {\n    const token = self.events[index][1]\n\n    if (token.type === types.labelImage) {\n      labelStart = token\n      break\n    }\n\n    // Exit if we’ve walked far enough.\n    if (\n      token.type === 'gfmFootnoteCall' ||\n      token.type === types.labelLink ||\n      token.type === types.label ||\n      token.type === types.image ||\n      token.type === types.link\n    ) {\n      break\n    }\n  }\n\n  return start\n\n  /**\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.rightSquareBracket, 'expected `]`')\n\n    if (!labelStart || !labelStart._balanced) {\n      return nok(code)\n    }\n\n    const id = normalizeIdentifier(\n      self.sliceSerialize({start: labelStart.end, end: self.now()})\n    )\n\n    if (id.codePointAt(0) !== codes.caret || !defined.includes(id.slice(1))) {\n      return nok(code)\n    }\n\n    effects.enter('gfmFootnoteCallLabelMarker')\n    effects.consume(code)\n    effects.exit('gfmFootnoteCallLabelMarker')\n    return ok(code)\n  }\n}\n\n// To do: remove after micromark update.\n/** @type {Resolver} */\nfunction resolveToPotentialGfmFootnoteCall(events, context) {\n  let index = events.length\n  /** @type {Token | undefined} */\n  let labelStart\n\n  // Find an opening.\n  while (index--) {\n    if (\n      events[index][1].type === types.labelImage &&\n      events[index][0] === 'enter'\n    ) {\n      labelStart = events[index][1]\n      break\n    }\n  }\n\n  assert(labelStart, 'expected `labelStart` to resolve')\n\n  // Change the `labelImageMarker` to a `data`.\n  events[index + 1][1].type = types.data\n  events[index + 3][1].type = 'gfmFootnoteCallLabelMarker'\n\n  // The whole (without `!`):\n  /** @type {Token} */\n  const call = {\n    type: 'gfmFootnoteCall',\n    start: Object.assign({}, events[index + 3][1].start),\n    end: Object.assign({}, events[events.length - 1][1].end)\n  }\n  // The `^` marker\n  /** @type {Token} */\n  const marker = {\n    type: 'gfmFootnoteCallMarker',\n    start: Object.assign({}, events[index + 3][1].end),\n    end: Object.assign({}, events[index + 3][1].end)\n  }\n  // Increment the end 1 character.\n  marker.end.column++\n  marker.end.offset++\n  marker.end._bufferIndex++\n  /** @type {Token} */\n  const string = {\n    type: 'gfmFootnoteCallString',\n    start: Object.assign({}, marker.end),\n    end: Object.assign({}, events[events.length - 1][1].start)\n  }\n  /** @type {Token} */\n  const chunk = {\n    type: types.chunkString,\n    contentType: 'string',\n    start: Object.assign({}, string.start),\n    end: Object.assign({}, string.end)\n  }\n\n  /** @type {Array<Event>} */\n  const replacement = [\n    // Take the `labelImageMarker` (now `data`, the `!`)\n    events[index + 1],\n    events[index + 2],\n    ['enter', call, context],\n    // The `[`\n    events[index + 3],\n    events[index + 4],\n    // The `^`.\n    ['enter', marker, context],\n    ['exit', marker, context],\n    // Everything in between.\n    ['enter', string, context],\n    ['enter', chunk, context],\n    ['exit', chunk, context],\n    ['exit', string, context],\n    // The ending (`]`, properly parsed and labelled).\n    events[events.length - 2],\n    events[events.length - 1],\n    ['exit', call, context]\n  ]\n\n  events.splice(index, events.length - index + 1, ...replacement)\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeGfmFootnoteCall(effects, ok, nok) {\n  const self = this\n  const defined = self.parser.gfmFootnotes || (self.parser.gfmFootnotes = [])\n  let size = 0\n  /** @type {boolean} */\n  let data\n\n  // Note: the implementation of `markdown-rs` is different, because it houses\n  // core *and* extensions in one project.\n  // Therefore, it can include footnote logic inside `label-end`.\n  // We can’t do that, but luckily, we can parse footnotes in a simpler way than\n  // needed for labels.\n  return start\n\n  /**\n   * Start of footnote label.\n   *\n   * ```markdown\n   * > | a [^b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.leftSquareBracket, 'expected `[`')\n    effects.enter('gfmFootnoteCall')\n    effects.enter('gfmFootnoteCallLabelMarker')\n    effects.consume(code)\n    effects.exit('gfmFootnoteCallLabelMarker')\n    return callStart\n  }\n\n  /**\n   * After `[`, at `^`.\n   *\n   * ```markdown\n   * > | a [^b] c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function callStart(code) {\n    if (code !== codes.caret) return nok(code)\n\n    effects.enter('gfmFootnoteCallMarker')\n    effects.consume(code)\n    effects.exit('gfmFootnoteCallMarker')\n    effects.enter('gfmFootnoteCallString')\n    effects.enter('chunkString').contentType = 'string'\n    return callData\n  }\n\n  /**\n   * In label.\n   *\n   * ```markdown\n   * > | a [^b] c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function callData(code) {\n    if (\n      // Too long.\n      size > constants.linkReferenceSizeMax ||\n      // Closing brace with nothing.\n      (code === codes.rightSquareBracket && !data) ||\n      // Space or tab is not supported by GFM for some reason.\n      // `\\n` and `[` not being supported makes sense.\n      code === codes.eof ||\n      code === codes.leftSquareBracket ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return nok(code)\n    }\n\n    if (code === codes.rightSquareBracket) {\n      effects.exit('chunkString')\n      const token = effects.exit('gfmFootnoteCallString')\n\n      if (!defined.includes(normalizeIdentifier(self.sliceSerialize(token)))) {\n        return nok(code)\n      }\n\n      effects.enter('gfmFootnoteCallLabelMarker')\n      effects.consume(code)\n      effects.exit('gfmFootnoteCallLabelMarker')\n      effects.exit('gfmFootnoteCall')\n      return ok\n    }\n\n    if (!markdownLineEndingOrSpace(code)) {\n      data = true\n    }\n\n    size++\n    effects.consume(code)\n    return code === codes.backslash ? callEscape : callData\n  }\n\n  /**\n   * On character after escape.\n   *\n   * ```markdown\n   * > | a [^b\\c] d\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function callEscape(code) {\n    if (\n      code === codes.leftSquareBracket ||\n      code === codes.backslash ||\n      code === codes.rightSquareBracket\n    ) {\n      effects.consume(code)\n      size++\n      return callData\n    }\n\n    return callData(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeDefinitionStart(effects, ok, nok) {\n  const self = this\n  const defined = self.parser.gfmFootnotes || (self.parser.gfmFootnotes = [])\n  /** @type {string} */\n  let identifier\n  let size = 0\n  /** @type {boolean | undefined} */\n  let data\n\n  return start\n\n  /**\n   * Start of GFM footnote definition.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.leftSquareBracket, 'expected `[`')\n    effects.enter('gfmFootnoteDefinition')._container = true\n    effects.enter('gfmFootnoteDefinitionLabel')\n    effects.enter('gfmFootnoteDefinitionLabelMarker')\n    effects.consume(code)\n    effects.exit('gfmFootnoteDefinitionLabelMarker')\n    return labelAtMarker\n  }\n\n  /**\n   * In label, at caret.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelAtMarker(code) {\n    if (code === codes.caret) {\n      effects.enter('gfmFootnoteDefinitionMarker')\n      effects.consume(code)\n      effects.exit('gfmFootnoteDefinitionMarker')\n      effects.enter('gfmFootnoteDefinitionLabelString')\n      effects.enter('chunkString').contentType = 'string'\n      return labelInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In label.\n   *\n   * > 👉 **Note**: `cmark-gfm` prevents whitespace from occurring in footnote\n   * > definition labels.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelInside(code) {\n    if (\n      // Too long.\n      size > constants.linkReferenceSizeMax ||\n      // Closing brace with nothing.\n      (code === codes.rightSquareBracket && !data) ||\n      // Space or tab is not supported by GFM for some reason.\n      // `\\n` and `[` not being supported makes sense.\n      code === codes.eof ||\n      code === codes.leftSquareBracket ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return nok(code)\n    }\n\n    if (code === codes.rightSquareBracket) {\n      effects.exit('chunkString')\n      const token = effects.exit('gfmFootnoteDefinitionLabelString')\n      identifier = normalizeIdentifier(self.sliceSerialize(token))\n      effects.enter('gfmFootnoteDefinitionLabelMarker')\n      effects.consume(code)\n      effects.exit('gfmFootnoteDefinitionLabelMarker')\n      effects.exit('gfmFootnoteDefinitionLabel')\n      return labelAfter\n    }\n\n    if (!markdownLineEndingOrSpace(code)) {\n      data = true\n    }\n\n    size++\n    effects.consume(code)\n    return code === codes.backslash ? labelEscape : labelInside\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * > 👉 **Note**: `cmark-gfm` currently does not support escaped brackets:\n   * > <https://github.com/github/cmark-gfm/issues/240>\n   *\n   * ```markdown\n   * > | [^a\\*b]: c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEscape(code) {\n    if (\n      code === codes.leftSquareBracket ||\n      code === codes.backslash ||\n      code === codes.rightSquareBracket\n    ) {\n      effects.consume(code)\n      size++\n      return labelInside\n    }\n\n    return labelInside(code)\n  }\n\n  /**\n   * After definition label.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelAfter(code) {\n    if (code === codes.colon) {\n      effects.enter('definitionMarker')\n      effects.consume(code)\n      effects.exit('definitionMarker')\n\n      if (!defined.includes(identifier)) {\n        defined.push(identifier)\n      }\n\n      // Any whitespace after the marker is eaten, forming indented code\n      // is not possible.\n      // No space is also fine, just like a block quote marker.\n      return factorySpace(\n        effects,\n        whitespaceAfter,\n        'gfmFootnoteDefinitionWhitespace'\n      )\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After definition prefix.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function whitespaceAfter(code) {\n    // `markdown-rs` has a wrapping token for the prefix that is closed here.\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeDefinitionContinuation(effects, ok, nok) {\n  /// Start of footnote definition continuation.\n  ///\n  /// ```markdown\n  ///   | [^a]: b\n  /// > |     c\n  ///     ^\n  /// ```\n  //\n  // Either a blank line, which is okay, or an indented thing.\n  return effects.check(blankLine, ok, effects.attempt(indent, ok, nok))\n}\n\n/** @type {Exiter} */\nfunction gfmFootnoteDefinitionEnd(effects) {\n  effects.exit('gfmFootnoteDefinition')\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeIndent(effects, ok, nok) {\n  const self = this\n\n  return factorySpace(\n    effects,\n    afterPrefix,\n    'gfmFootnoteDefinitionIndent',\n    constants.tabSize + 1\n  )\n\n  /**\n   * @type {State}\n   */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === 'gfmFootnoteDefinitionIndent' &&\n      tail[2].sliceSerialize(tail[1], true).length === constants.tabSize\n      ? ok(code)\n      : nok(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;;;;;AAEA,MAAM,SAAS;IAAC,UAAU;IAAgB,SAAS;AAAI;AAehD,SAAS;IACd,sBAAsB,GACtB,OAAO;QACL,UAAU;YACR,CAAC,8JAAA,CAAA,QAAK,CAAC,iBAAiB,CAAC,EAAE;gBACzB,MAAM;gBACN,UAAU;gBACV,cAAc;oBAAC,UAAU;gBAA8B;gBACvD,MAAM;YACR;QACF;QACA,MAAM;YACJ,CAAC,8JAAA,CAAA,QAAK,CAAC,iBAAiB,CAAC,EAAE;gBACzB,MAAM;gBACN,UAAU;YACZ;YACA,CAAC,8JAAA,CAAA,QAAK,CAAC,kBAAkB,CAAC,EAAE;gBAC1B,MAAM;gBACN,KAAK;gBACL,UAAU;gBACV,WAAW;YACb;QACF;IACF;AACF;AAEA,wCAAwC;AACxC;;;CAGC,GACD,SAAS,iCAAiC,OAAO,EAAE,EAAE,EAAE,GAAG;IACxD,MAAM,OAAO,IAAI;IACjB,IAAI,QAAQ,KAAK,MAAM,CAAC,MAAM;IAC9B,MAAM,UAAU,KAAK,MAAM,CAAC,YAAY,IAAI,CAAC,KAAK,MAAM,CAAC,YAAY,GAAG,EAAE;IAC1E,kBAAkB,GAClB,IAAI;IAEJ,mBAAmB;IACnB,MAAO,QAAS;QACd,MAAM,QAAQ,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE;QAEnC,IAAI,MAAM,IAAI,KAAK,8JAAA,CAAA,QAAK,CAAC,UAAU,EAAE;YACnC,aAAa;YACb;QACF;QAEA,mCAAmC;QACnC,IACE,MAAM,IAAI,KAAK,qBACf,MAAM,IAAI,KAAK,8JAAA,CAAA,QAAK,CAAC,SAAS,IAC9B,MAAM,IAAI,KAAK,8JAAA,CAAA,QAAK,CAAC,KAAK,IAC1B,MAAM,IAAI,KAAK,8JAAA,CAAA,QAAK,CAAC,KAAK,IAC1B,MAAM,IAAI,KAAK,8JAAA,CAAA,QAAK,CAAC,IAAI,EACzB;YACA;QACF;IACF;IAEA,OAAO;;IAEP;;GAEC,GACD,SAAS,MAAM,IAAI;QACjB,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,8JAAA,CAAA,QAAK,CAAC,kBAAkB,EAAE;QAE1C,IAAI,CAAC,cAAc,CAAC,WAAW,SAAS,EAAE;YACxC,OAAO,IAAI;QACb;QAEA,MAAM,KAAK,CAAA,GAAA,+KAAA,CAAA,sBAAmB,AAAD,EAC3B,KAAK,cAAc,CAAC;YAAC,OAAO,WAAW,GAAG;YAAE,KAAK,KAAK,GAAG;QAAE;QAG7D,IAAI,GAAG,WAAW,CAAC,OAAO,8JAAA,CAAA,QAAK,CAAC,KAAK,IAAI,CAAC,QAAQ,QAAQ,CAAC,GAAG,KAAK,CAAC,KAAK;YACvE,OAAO,IAAI;QACb;QAEA,QAAQ,KAAK,CAAC;QACd,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC;QACb,OAAO,GAAG;IACZ;AACF;AAEA,wCAAwC;AACxC,qBAAqB,GACrB,SAAS,kCAAkC,MAAM,EAAE,OAAO;IACxD,IAAI,QAAQ,OAAO,MAAM;IACzB,8BAA8B,GAC9B,IAAI;IAEJ,mBAAmB;IACnB,MAAO,QAAS;QACd,IACE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,8JAAA,CAAA,QAAK,CAAC,UAAU,IAC1C,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,SACrB;YACA,aAAa,MAAM,CAAC,MAAM,CAAC,EAAE;YAC7B;QACF;IACF;IAEA,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,YAAY;IAEnB,6CAA6C;IAC7C,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,IAAI,GAAG,8JAAA,CAAA,QAAK,CAAC,IAAI;IACtC,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,IAAI,GAAG;IAE5B,2BAA2B;IAC3B,kBAAkB,GAClB,MAAM,OAAO;QACX,MAAM;QACN,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,KAAK;QACnD,KAAK,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG;IACzD;IACA,iBAAiB;IACjB,kBAAkB,GAClB,MAAM,SAAS;QACb,MAAM;QACN,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,GAAG;QACjD,KAAK,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,GAAG;IACjD;IACA,iCAAiC;IACjC,OAAO,GAAG,CAAC,MAAM;IACjB,OAAO,GAAG,CAAC,MAAM;IACjB,OAAO,GAAG,CAAC,YAAY;IACvB,kBAAkB,GAClB,MAAM,SAAS;QACb,MAAM;QACN,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO,GAAG;QACnC,KAAK,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,KAAK;IAC3D;IACA,kBAAkB,GAClB,MAAM,QAAQ;QACZ,MAAM,8JAAA,CAAA,QAAK,CAAC,WAAW;QACvB,aAAa;QACb,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO,KAAK;QACrC,KAAK,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO,GAAG;IACnC;IAEA,yBAAyB,GACzB,MAAM,cAAc;QAClB,oDAAoD;QACpD,MAAM,CAAC,QAAQ,EAAE;QACjB,MAAM,CAAC,QAAQ,EAAE;QACjB;YAAC;YAAS;YAAM;SAAQ;QACxB,UAAU;QACV,MAAM,CAAC,QAAQ,EAAE;QACjB,MAAM,CAAC,QAAQ,EAAE;QACjB,WAAW;QACX;YAAC;YAAS;YAAQ;SAAQ;QAC1B;YAAC;YAAQ;YAAQ;SAAQ;QACzB,yBAAyB;QACzB;YAAC;YAAS;YAAQ;SAAQ;QAC1B;YAAC;YAAS;YAAO;SAAQ;QACzB;YAAC;YAAQ;YAAO;SAAQ;QACxB;YAAC;YAAQ;YAAQ;SAAQ;QACzB,kDAAkD;QAClD,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;QACzB,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;QACzB;YAAC;YAAQ;YAAM;SAAQ;KACxB;IAED,OAAO,MAAM,CAAC,OAAO,OAAO,MAAM,GAAG,QAAQ,MAAM;IAEnD,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,wBAAwB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC/C,MAAM,OAAO,IAAI;IACjB,MAAM,UAAU,KAAK,MAAM,CAAC,YAAY,IAAI,CAAC,KAAK,MAAM,CAAC,YAAY,GAAG,EAAE;IAC1E,IAAI,OAAO;IACX,oBAAoB,GACpB,IAAI;IAEJ,4EAA4E;IAC5E,wCAAwC;IACxC,+DAA+D;IAC/D,8EAA8E;IAC9E,qBAAqB;IACrB,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,8JAAA,CAAA,QAAK,CAAC,iBAAiB,EAAE;QACzC,QAAQ,KAAK,CAAC;QACd,QAAQ,KAAK,CAAC;QACd,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,UAAU,IAAI;QACrB,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK,EAAE,OAAO,IAAI;QAErC,QAAQ,KAAK,CAAC;QACd,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC;QACb,QAAQ,KAAK,CAAC;QACd,QAAQ,KAAK,CAAC,eAAe,WAAW,GAAG;QAC3C,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,SAAS,IAAI;QACpB,IACE,YAAY;QACZ,OAAO,kKAAA,CAAA,YAAS,CAAC,oBAAoB,IAEpC,SAAS,8JAAA,CAAA,QAAK,CAAC,kBAAkB,IAAI,CAAC,QACvC,wDAAwD;QACxD,gDAAgD;QAChD,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,8JAAA,CAAA,QAAK,CAAC,iBAAiB,IAChC,CAAA,GAAA,iKAAA,CAAA,4BAAyB,AAAD,EAAE,OAC1B;YACA,OAAO,IAAI;QACb;QAEA,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,kBAAkB,EAAE;YACrC,QAAQ,IAAI,CAAC;YACb,MAAM,QAAQ,QAAQ,IAAI,CAAC;YAE3B,IAAI,CAAC,QAAQ,QAAQ,CAAC,CAAA,GAAA,+KAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,cAAc,CAAC,UAAU;gBACtE,OAAO,IAAI;YACb;YAEA,QAAQ,KAAK,CAAC;YACd,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC;YACb,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,IAAI,CAAC,CAAA,GAAA,iKAAA,CAAA,4BAAyB,AAAD,EAAE,OAAO;YACpC,OAAO;QACT;QAEA;QACA,QAAQ,OAAO,CAAC;QAChB,OAAO,SAAS,8JAAA,CAAA,QAAK,CAAC,SAAS,GAAG,aAAa;IACjD;IAEA;;;;;;;;;GASC,GACD,SAAS,WAAW,IAAI;QACtB,IACE,SAAS,8JAAA,CAAA,QAAK,CAAC,iBAAiB,IAChC,SAAS,8JAAA,CAAA,QAAK,CAAC,SAAS,IACxB,SAAS,8JAAA,CAAA,QAAK,CAAC,kBAAkB,EACjC;YACA,QAAQ,OAAO,CAAC;YAChB;YACA,OAAO;QACT;QAEA,OAAO,SAAS;IAClB;AACF;AAEA;;;CAGC,GACD,SAAS,wBAAwB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC/C,MAAM,OAAO,IAAI;IACjB,MAAM,UAAU,KAAK,MAAM,CAAC,YAAY,IAAI,CAAC,KAAK,MAAM,CAAC,YAAY,GAAG,EAAE;IAC1E,mBAAmB,GACnB,IAAI;IACJ,IAAI,OAAO;IACX,gCAAgC,GAChC,IAAI;IAEJ,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,8JAAA,CAAA,QAAK,CAAC,iBAAiB,EAAE;QACzC,QAAQ,KAAK,CAAC,yBAAyB,UAAU,GAAG;QACpD,QAAQ,KAAK,CAAC;QACd,QAAQ,KAAK,CAAC;QACd,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,cAAc,IAAI;QACzB,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK,EAAE;YACxB,QAAQ,KAAK,CAAC;YACd,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC;YACb,QAAQ,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC,eAAe,WAAW,GAAG;YAC3C,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;;;;GAYC,GACD,SAAS,YAAY,IAAI;QACvB,IACE,YAAY;QACZ,OAAO,kKAAA,CAAA,YAAS,CAAC,oBAAoB,IAEpC,SAAS,8JAAA,CAAA,QAAK,CAAC,kBAAkB,IAAI,CAAC,QACvC,wDAAwD;QACxD,gDAAgD;QAChD,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,8JAAA,CAAA,QAAK,CAAC,iBAAiB,IAChC,CAAA,GAAA,iKAAA,CAAA,4BAAyB,AAAD,EAAE,OAC1B;YACA,OAAO,IAAI;QACb;QAEA,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,kBAAkB,EAAE;YACrC,QAAQ,IAAI,CAAC;YACb,MAAM,QAAQ,QAAQ,IAAI,CAAC;YAC3B,aAAa,CAAA,GAAA,+KAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,cAAc,CAAC;YACrD,QAAQ,KAAK,CAAC;YACd,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC;YACb,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,IAAI,CAAC,CAAA,GAAA,iKAAA,CAAA,4BAAyB,AAAD,EAAE,OAAO;YACpC,OAAO;QACT;QAEA;QACA,QAAQ,OAAO,CAAC;QAChB,OAAO,SAAS,8JAAA,CAAA,QAAK,CAAC,SAAS,GAAG,cAAc;IAClD;IAEA;;;;;;;;;;;;GAYC,GACD,SAAS,YAAY,IAAI;QACvB,IACE,SAAS,8JAAA,CAAA,QAAK,CAAC,iBAAiB,IAChC,SAAS,8JAAA,CAAA,QAAK,CAAC,SAAS,IACxB,SAAS,8JAAA,CAAA,QAAK,CAAC,kBAAkB,EACjC;YACA,QAAQ,OAAO,CAAC;YAChB;YACA,OAAO;QACT;QAEA,OAAO,YAAY;IACrB;IAEA;;;;;;;;;GASC,GACD,SAAS,WAAW,IAAI;QACtB,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK,EAAE;YACxB,QAAQ,KAAK,CAAC;YACd,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC;YAEb,IAAI,CAAC,QAAQ,QAAQ,CAAC,aAAa;gBACjC,QAAQ,IAAI,CAAC;YACf;YAEA,kEAAkE;YAClE,mBAAmB;YACnB,yDAAyD;YACzD,OAAO,CAAA,GAAA,gKAAA,CAAA,eAAY,AAAD,EAChB,SACA,iBACA;QAEJ;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,gBAAgB,IAAI;QAC3B,yEAAyE;QACzE,OAAO,GAAG;IACZ;AACF;AAEA;;;CAGC,GACD,SAAS,+BAA+B,OAAO,EAAE,EAAE,EAAE,GAAG;IACtD,8CAA8C;IAC9C,GAAG;IACH,eAAe;IACf,eAAe;IACf,aAAa;IACb,SAAS;IACT,OAAO;IACP,EAAE;IACF,4DAA4D;IAC5D,OAAO,QAAQ,KAAK,CAAC,iLAAA,CAAA,YAAS,EAAE,IAAI,QAAQ,OAAO,CAAC,QAAQ,IAAI;AAClE;AAEA,mBAAmB,GACnB,SAAS,yBAAyB,OAAO;IACvC,QAAQ,IAAI,CAAC;AACf;AAEA;;;CAGC,GACD,SAAS,eAAe,OAAO,EAAE,EAAE,EAAE,GAAG;IACtC,MAAM,OAAO,IAAI;IAEjB,OAAO,CAAA,GAAA,gKAAA,CAAA,eAAY,AAAD,EAChB,SACA,aACA,+BACA,kKAAA,CAAA,YAAS,CAAC,OAAO,GAAG;;IAGtB;;GAEC,GACD,SAAS,YAAY,IAAI;QACvB,MAAM,OAAO,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,GAAG,EAAE;QAChD,OAAO,QACL,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,iCACjB,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,MAAM,KAAK,kKAAA,CAAA,YAAS,CAAC,OAAO,GAChE,GAAG,QACH,IAAI;IACV;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6540, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-extension-gfm-footnote/dev/lib/html.js"], "sourcesContent": ["/**\n * @import {HtmlOptions as Options} from 'micromark-extension-gfm-footnote'\n * @import {HtmlExtension} from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {sanitizeUri} from 'micromark-util-sanitize-uri'\n\nconst own = {}.hasOwnProperty\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Generate the default label that GitHub uses on backreferences.\n *\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Default label.\n */\nexport function defaultBackLabel(referenceIndex, rereferenceIndex) {\n  return (\n    'Back to reference ' +\n    (referenceIndex + 1) +\n    (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n  )\n}\n\n/**\n * Create an extension for `micromark` to support GFM footnotes when\n * serializing to HTML.\n *\n * @param {Options | null | undefined} [options={}]\n *   Configuration (optional).\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GFM footnotes when serializing to HTML.\n */\nexport function gfmFootnoteHtml(options) {\n  const config = options || emptyOptions\n  const label = config.label || 'Footnotes'\n  const labelTagName = config.labelTagName || 'h2'\n  const labelAttributes =\n    config.labelAttributes === null || config.labelAttributes === undefined\n      ? 'class=\"sr-only\"'\n      : config.labelAttributes\n  const backLabel = config.backLabel || defaultBackLabel\n  const clobberPrefix =\n    config.clobberPrefix === null || config.clobberPrefix === undefined\n      ? 'user-content-'\n      : config.clobberPrefix\n  return {\n    enter: {\n      gfmFootnoteDefinition() {\n        const stack = this.getData('tightStack')\n        stack.push(false)\n      },\n      gfmFootnoteDefinitionLabelString() {\n        this.buffer()\n      },\n      gfmFootnoteCallString() {\n        this.buffer()\n      }\n    },\n    exit: {\n      gfmFootnoteDefinition() {\n        let definitions = this.getData('gfmFootnoteDefinitions')\n        const footnoteStack = this.getData('gfmFootnoteDefinitionStack')\n        assert(footnoteStack, 'expected `footnoteStack`')\n        const tightStack = this.getData('tightStack')\n        const current = footnoteStack.pop()\n        const value = this.resume()\n\n        assert(current, 'expected to be in a footnote')\n\n        if (!definitions) {\n          this.setData('gfmFootnoteDefinitions', (definitions = {}))\n        }\n\n        if (!own.call(definitions, current)) definitions[current] = value\n\n        tightStack.pop()\n        this.setData('slurpOneLineEnding', true)\n        // “Hack” to prevent a line ending from showing up if we’re in a definition in\n        // an empty list item.\n        this.setData('lastWasTag')\n      },\n      gfmFootnoteDefinitionLabelString(token) {\n        let footnoteStack = this.getData('gfmFootnoteDefinitionStack')\n\n        if (!footnoteStack) {\n          this.setData('gfmFootnoteDefinitionStack', (footnoteStack = []))\n        }\n\n        footnoteStack.push(normalizeIdentifier(this.sliceSerialize(token)))\n        this.resume() // Drop the label.\n        this.buffer() // Get ready for a value.\n      },\n      gfmFootnoteCallString(token) {\n        let calls = this.getData('gfmFootnoteCallOrder')\n        let counts = this.getData('gfmFootnoteCallCounts')\n        const id = normalizeIdentifier(this.sliceSerialize(token))\n        /** @type {number} */\n        let counter\n\n        this.resume()\n\n        if (!calls) this.setData('gfmFootnoteCallOrder', (calls = []))\n        if (!counts) this.setData('gfmFootnoteCallCounts', (counts = {}))\n\n        const index = calls.indexOf(id)\n        const safeId = sanitizeUri(id.toLowerCase())\n\n        if (index === -1) {\n          calls.push(id)\n          counts[id] = 1\n          counter = calls.length\n        } else {\n          counts[id]++\n          counter = index + 1\n        }\n\n        const reuseCounter = counts[id]\n\n        this.tag(\n          '<sup><a href=\"#' +\n            clobberPrefix +\n            'fn-' +\n            safeId +\n            '\" id=\"' +\n            clobberPrefix +\n            'fnref-' +\n            safeId +\n            (reuseCounter > 1 ? '-' + reuseCounter : '') +\n            '\" data-footnote-ref=\"\" aria-describedby=\"footnote-label\">' +\n            String(counter) +\n            '</a></sup>'\n        )\n      },\n      null() {\n        const calls = this.getData('gfmFootnoteCallOrder') || []\n        const counts = this.getData('gfmFootnoteCallCounts') || {}\n        const definitions = this.getData('gfmFootnoteDefinitions') || {}\n        let index = -1\n\n        if (calls.length > 0) {\n          this.lineEndingIfNeeded()\n          this.tag(\n            '<section data-footnotes=\"\" class=\"footnotes\"><' +\n              labelTagName +\n              ' id=\"footnote-label\"' +\n              (labelAttributes ? ' ' + labelAttributes : '') +\n              '>'\n          )\n          this.raw(this.encode(label))\n          this.tag('</' + labelTagName + '>')\n          this.lineEndingIfNeeded()\n          this.tag('<ol>')\n        }\n\n        while (++index < calls.length) {\n          // Called definitions are always defined.\n          const id = calls[index]\n          const safeId = sanitizeUri(id.toLowerCase())\n          let referenceIndex = 0\n          /** @type {Array<string>} */\n          const references = []\n\n          while (++referenceIndex <= counts[id]) {\n            references.push(\n              '<a href=\"#' +\n                clobberPrefix +\n                'fnref-' +\n                safeId +\n                (referenceIndex > 1 ? '-' + referenceIndex : '') +\n                '\" data-footnote-backref=\"\" aria-label=\"' +\n                this.encode(\n                  typeof backLabel === 'string'\n                    ? backLabel\n                    : backLabel(index, referenceIndex)\n                ) +\n                '\" class=\"data-footnote-backref\">↩' +\n                (referenceIndex > 1\n                  ? '<sup>' + referenceIndex + '</sup>'\n                  : '') +\n                '</a>'\n            )\n          }\n\n          const reference = references.join(' ')\n          let injected = false\n\n          this.lineEndingIfNeeded()\n          this.tag('<li id=\"' + clobberPrefix + 'fn-' + safeId + '\">')\n          this.lineEndingIfNeeded()\n          this.tag(\n            definitions[id].replace(/<\\/p>(?:\\r?\\n|\\r)?$/, function ($0) {\n              injected = true\n              return ' ' + reference + $0\n            })\n          )\n\n          if (!injected) {\n            this.lineEndingIfNeeded()\n            this.tag(reference)\n          }\n\n          this.lineEndingIfNeeded()\n          this.tag('</li>')\n        }\n\n        if (calls.length > 0) {\n          this.lineEndingIfNeeded()\n          this.tag('</ol>')\n          this.lineEndingIfNeeded()\n          this.tag('</section>')\n        }\n      }\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;AACA;;;;AAEA,MAAM,MAAM,CAAC,EAAE,cAAc;AAE7B,oBAAoB,GACpB,MAAM,eAAe,CAAC;AAaf,SAAS,iBAAiB,cAAc,EAAE,gBAAgB;IAC/D,OACE,uBACA,CAAC,iBAAiB,CAAC,IACnB,CAAC,mBAAmB,IAAI,MAAM,mBAAmB,EAAE;AAEvD;AAYO,SAAS,gBAAgB,OAAO;IACrC,MAAM,SAAS,WAAW;IAC1B,MAAM,QAAQ,OAAO,KAAK,IAAI;IAC9B,MAAM,eAAe,OAAO,YAAY,IAAI;IAC5C,MAAM,kBACJ,OAAO,eAAe,KAAK,QAAQ,OAAO,eAAe,KAAK,YAC1D,oBACA,OAAO,eAAe;IAC5B,MAAM,YAAY,OAAO,SAAS,IAAI;IACtC,MAAM,gBACJ,OAAO,aAAa,KAAK,QAAQ,OAAO,aAAa,KAAK,YACtD,kBACA,OAAO,aAAa;IAC1B,OAAO;QACL,OAAO;YACL;gBACE,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC;gBAC3B,MAAM,IAAI,CAAC;YACb;YACA;gBACE,IAAI,CAAC,MAAM;YACb;YACA;gBACE,IAAI,CAAC,MAAM;YACb;QACF;QACA,MAAM;YACJ;gBACE,IAAI,cAAc,IAAI,CAAC,OAAO,CAAC;gBAC/B,MAAM,gBAAgB,IAAI,CAAC,OAAO,CAAC;gBACnC,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,eAAe;gBACtB,MAAM,aAAa,IAAI,CAAC,OAAO,CAAC;gBAChC,MAAM,UAAU,cAAc,GAAG;gBACjC,MAAM,QAAQ,IAAI,CAAC,MAAM;gBAEzB,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,SAAS;gBAEhB,IAAI,CAAC,aAAa;oBAChB,IAAI,CAAC,OAAO,CAAC,0BAA2B,cAAc,CAAC;gBACzD;gBAEA,IAAI,CAAC,IAAI,IAAI,CAAC,aAAa,UAAU,WAAW,CAAC,QAAQ,GAAG;gBAE5D,WAAW,GAAG;gBACd,IAAI,CAAC,OAAO,CAAC,sBAAsB;gBACnC,8EAA8E;gBAC9E,sBAAsB;gBACtB,IAAI,CAAC,OAAO,CAAC;YACf;YACA,kCAAiC,KAAK;gBACpC,IAAI,gBAAgB,IAAI,CAAC,OAAO,CAAC;gBAEjC,IAAI,CAAC,eAAe;oBAClB,IAAI,CAAC,OAAO,CAAC,8BAA+B,gBAAgB,EAAE;gBAChE;gBAEA,cAAc,IAAI,CAAC,CAAA,GAAA,+KAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,CAAC,cAAc,CAAC;gBAC3D,IAAI,CAAC,MAAM,GAAG,kBAAkB;;gBAChC,IAAI,CAAC,MAAM,GAAG,yBAAyB;;YACzC;YACA,uBAAsB,KAAK;gBACzB,IAAI,QAAQ,IAAI,CAAC,OAAO,CAAC;gBACzB,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC;gBAC1B,MAAM,KAAK,CAAA,GAAA,+KAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,CAAC,cAAc,CAAC;gBACnD,mBAAmB,GACnB,IAAI;gBAEJ,IAAI,CAAC,MAAM;gBAEX,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,wBAAyB,QAAQ,EAAE;gBAC5D,IAAI,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,yBAA0B,SAAS,CAAC;gBAE9D,MAAM,QAAQ,MAAM,OAAO,CAAC;gBAC5B,MAAM,SAAS,CAAA,GAAA,uKAAA,CAAA,cAAW,AAAD,EAAE,GAAG,WAAW;gBAEzC,IAAI,UAAU,CAAC,GAAG;oBAChB,MAAM,IAAI,CAAC;oBACX,MAAM,CAAC,GAAG,GAAG;oBACb,UAAU,MAAM,MAAM;gBACxB,OAAO;oBACL,MAAM,CAAC,GAAG;oBACV,UAAU,QAAQ;gBACpB;gBAEA,MAAM,eAAe,MAAM,CAAC,GAAG;gBAE/B,IAAI,CAAC,GAAG,CACN,oBACE,gBACA,QACA,SACA,WACA,gBACA,WACA,SACA,CAAC,eAAe,IAAI,MAAM,eAAe,EAAE,IAC3C,8DACA,OAAO,WACP;YAEN;YACA;gBACE,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,2BAA2B,EAAE;gBACxD,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,4BAA4B,CAAC;gBACzD,MAAM,cAAc,IAAI,CAAC,OAAO,CAAC,6BAA6B,CAAC;gBAC/D,IAAI,QAAQ,CAAC;gBAEb,IAAI,MAAM,MAAM,GAAG,GAAG;oBACpB,IAAI,CAAC,kBAAkB;oBACvB,IAAI,CAAC,GAAG,CACN,mDACE,eACA,yBACA,CAAC,kBAAkB,MAAM,kBAAkB,EAAE,IAC7C;oBAEJ,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;oBACrB,IAAI,CAAC,GAAG,CAAC,OAAO,eAAe;oBAC/B,IAAI,CAAC,kBAAkB;oBACvB,IAAI,CAAC,GAAG,CAAC;gBACX;gBAEA,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;oBAC7B,yCAAyC;oBACzC,MAAM,KAAK,KAAK,CAAC,MAAM;oBACvB,MAAM,SAAS,CAAA,GAAA,uKAAA,CAAA,cAAW,AAAD,EAAE,GAAG,WAAW;oBACzC,IAAI,iBAAiB;oBACrB,0BAA0B,GAC1B,MAAM,aAAa,EAAE;oBAErB,MAAO,EAAE,kBAAkB,MAAM,CAAC,GAAG,CAAE;wBACrC,WAAW,IAAI,CACb,eACE,gBACA,WACA,SACA,CAAC,iBAAiB,IAAI,MAAM,iBAAiB,EAAE,IAC/C,4CACA,IAAI,CAAC,MAAM,CACT,OAAO,cAAc,WACjB,YACA,UAAU,OAAO,mBAEvB,sCACA,CAAC,iBAAiB,IACd,UAAU,iBAAiB,WAC3B,EAAE,IACN;oBAEN;oBAEA,MAAM,YAAY,WAAW,IAAI,CAAC;oBAClC,IAAI,WAAW;oBAEf,IAAI,CAAC,kBAAkB;oBACvB,IAAI,CAAC,GAAG,CAAC,aAAa,gBAAgB,QAAQ,SAAS;oBACvD,IAAI,CAAC,kBAAkB;oBACvB,IAAI,CAAC,GAAG,CACN,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,uBAAuB,SAAU,EAAE;wBACzD,WAAW;wBACX,OAAO,MAAM,YAAY;oBAC3B;oBAGF,IAAI,CAAC,UAAU;wBACb,IAAI,CAAC,kBAAkB;wBACvB,IAAI,CAAC,GAAG,CAAC;oBACX;oBAEA,IAAI,CAAC,kBAAkB;oBACvB,IAAI,CAAC,GAAG,CAAC;gBACX;gBAEA,IAAI,MAAM,MAAM,GAAG,GAAG;oBACpB,IAAI,CAAC,kBAAkB;oBACvB,IAAI,CAAC,GAAG,CAAC;oBACT,IAAI,CAAC,kBAAkB;oBACvB,IAAI,CAAC,GAAG,CAAC;gBACX;YACF;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6683, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-util-resolve-all/index.js"], "sourcesContent": ["/**\n * @import {Event, Resolver, TokenizeContext} from 'micromark-util-types'\n */\n\n/**\n * Call all `resolveAll`s.\n *\n * @param {ReadonlyArray<{resolveAll?: Resolver | undefined}>} constructs\n *   List of constructs, optionally with `resolveAll`s.\n * @param {Array<Event>} events\n *   List of events.\n * @param {TokenizeContext} context\n *   Context used by `tokenize`.\n * @returns {Array<Event>}\n *   Changed events.\n */\nexport function resolveAll(constructs, events, context) {\n  /** @type {Array<Resolver>} */\n  const called = []\n  let index = -1\n\n  while (++index < constructs.length) {\n    const resolve = constructs[index].resolveAll\n\n    if (resolve && !called.includes(resolve)) {\n      events = resolve(events, context)\n      called.push(resolve)\n    }\n  }\n\n  return events\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;;;;;;;;CAWC;;;AACM,SAAS,WAAW,UAAU,EAAE,MAAM,EAAE,OAAO;IACpD,4BAA4B,GAC5B,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,WAAW,MAAM,CAAE;QAClC,MAAM,UAAU,UAAU,CAAC,MAAM,CAAC,UAAU;QAE5C,IAAI,WAAW,CAAC,OAAO,QAAQ,CAAC,UAAU;YACxC,SAAS,QAAQ,QAAQ;YACzB,OAAO,IAAI,CAAC;QACd;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6717, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-extension-gfm-strikethrough/dev/lib/syntax.js"], "sourcesContent": ["/**\n * @import {Options} from 'micromark-extension-gfm-strikethrough'\n * @import {Event, Extension, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {splice} from 'micromark-util-chunked'\nimport {classify<PERSON><PERSON><PERSON>} from 'micromark-util-classify-character'\nimport {resolveAll} from 'micromark-util-resolve-all'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/**\n * Create an extension for `micromark` to enable GFM strikethrough syntax.\n *\n * @param {Options | null | undefined} [options={}]\n *   Configuration.\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions`, to\n *   enable GFM strikethrough syntax.\n */\nexport function gfmStrikethrough(options) {\n  const options_ = options || {}\n  let single = options_.singleTilde\n  const tokenizer = {\n    name: 'strikethrough',\n    tokenize: tokenizeStrikethrough,\n    resolveAll: resolveAllStrikethrough\n  }\n\n  if (single === null || single === undefined) {\n    single = true\n  }\n\n  return {\n    text: {[codes.tilde]: tokenizer},\n    insideSpan: {null: [tokenizer]},\n    attentionMarkers: {null: [codes.tilde]}\n  }\n\n  /**\n   * Take events and resolve strikethrough.\n   *\n   * @type {Resolver}\n   */\n  function resolveAllStrikethrough(events, context) {\n    let index = -1\n\n    // Walk through all events.\n    while (++index < events.length) {\n      // Find a token that can close.\n      if (\n        events[index][0] === 'enter' &&\n        events[index][1].type === 'strikethroughSequenceTemporary' &&\n        events[index][1]._close\n      ) {\n        let open = index\n\n        // Now walk back to find an opener.\n        while (open--) {\n          // Find a token that can open the closer.\n          if (\n            events[open][0] === 'exit' &&\n            events[open][1].type === 'strikethroughSequenceTemporary' &&\n            events[open][1]._open &&\n            // If the sizes are the same:\n            events[index][1].end.offset - events[index][1].start.offset ===\n              events[open][1].end.offset - events[open][1].start.offset\n          ) {\n            events[index][1].type = 'strikethroughSequence'\n            events[open][1].type = 'strikethroughSequence'\n\n            /** @type {Token} */\n            const strikethrough = {\n              type: 'strikethrough',\n              start: Object.assign({}, events[open][1].start),\n              end: Object.assign({}, events[index][1].end)\n            }\n\n            /** @type {Token} */\n            const text = {\n              type: 'strikethroughText',\n              start: Object.assign({}, events[open][1].end),\n              end: Object.assign({}, events[index][1].start)\n            }\n\n            // Opening.\n            /** @type {Array<Event>} */\n            const nextEvents = [\n              ['enter', strikethrough, context],\n              ['enter', events[open][1], context],\n              ['exit', events[open][1], context],\n              ['enter', text, context]\n            ]\n\n            const insideSpan = context.parser.constructs.insideSpan.null\n\n            if (insideSpan) {\n              // Between.\n              splice(\n                nextEvents,\n                nextEvents.length,\n                0,\n                resolveAll(insideSpan, events.slice(open + 1, index), context)\n              )\n            }\n\n            // Closing.\n            splice(nextEvents, nextEvents.length, 0, [\n              ['exit', text, context],\n              ['enter', events[index][1], context],\n              ['exit', events[index][1], context],\n              ['exit', strikethrough, context]\n            ])\n\n            splice(events, open - 1, index - open + 3, nextEvents)\n\n            index = open + nextEvents.length - 2\n            break\n          }\n        }\n      }\n    }\n\n    index = -1\n\n    while (++index < events.length) {\n      if (events[index][1].type === 'strikethroughSequenceTemporary') {\n        events[index][1].type = types.data\n      }\n    }\n\n    return events\n  }\n\n  /**\n   * @this {TokenizeContext}\n   * @type {Tokenizer}\n   */\n  function tokenizeStrikethrough(effects, ok, nok) {\n    const previous = this.previous\n    const events = this.events\n    let size = 0\n\n    return start\n\n    /** @type {State} */\n    function start(code) {\n      assert(code === codes.tilde, 'expected `~`')\n\n      if (\n        previous === codes.tilde &&\n        events[events.length - 1][1].type !== types.characterEscape\n      ) {\n        return nok(code)\n      }\n\n      effects.enter('strikethroughSequenceTemporary')\n      return more(code)\n    }\n\n    /** @type {State} */\n    function more(code) {\n      const before = classifyCharacter(previous)\n\n      if (code === codes.tilde) {\n        // If this is the third marker, exit.\n        if (size > 1) return nok(code)\n        effects.consume(code)\n        size++\n        return more\n      }\n\n      if (size < 2 && !single) return nok(code)\n      const token = effects.exit('strikethroughSequenceTemporary')\n      const after = classifyCharacter(code)\n      token._open =\n        !after || (after === constants.attentionSideAfter && Boolean(before))\n      token._close =\n        !before || (before === constants.attentionSideAfter && Boolean(after))\n      return ok(code)\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;AACA;AACA;AACA;AAAA;AAAA;;;;;;AAWO,SAAS,iBAAiB,OAAO;IACtC,MAAM,WAAW,WAAW,CAAC;IAC7B,IAAI,SAAS,SAAS,WAAW;IACjC,MAAM,YAAY;QAChB,MAAM;QACN,UAAU;QACV,YAAY;IACd;IAEA,IAAI,WAAW,QAAQ,WAAW,WAAW;QAC3C,SAAS;IACX;IAEA,OAAO;QACL,MAAM;YAAC,CAAC,8JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,EAAE;QAAS;QAC/B,YAAY;YAAC,MAAM;gBAAC;aAAU;QAAA;QAC9B,kBAAkB;YAAC,MAAM;gBAAC,8JAAA,CAAA,QAAK,CAAC,KAAK;aAAC;QAAA;IACxC;;IAEA;;;;GAIC,GACD,SAAS,wBAAwB,MAAM,EAAE,OAAO;QAC9C,IAAI,QAAQ,CAAC;QAEb,2BAA2B;QAC3B,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;YAC9B,+BAA+B;YAC/B,IACE,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,WACrB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,oCAC1B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EACvB;gBACA,IAAI,OAAO;gBAEX,mCAAmC;gBACnC,MAAO,OAAQ;oBACb,yCAAyC;oBACzC,IACE,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,UACpB,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,oCACzB,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,IACrB,6BAA6B;oBAC7B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KACzD,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAC3D;wBACA,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG;wBACxB,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,GAAG;wBAEvB,kBAAkB,GAClB,MAAM,gBAAgB;4BACpB,MAAM;4BACN,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK;4BAC9C,KAAK,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG;wBAC7C;wBAEA,kBAAkB,GAClB,MAAM,OAAO;4BACX,MAAM;4BACN,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;4BAC5C,KAAK,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK;wBAC/C;wBAEA,WAAW;wBACX,yBAAyB,GACzB,MAAM,aAAa;4BACjB;gCAAC;gCAAS;gCAAe;6BAAQ;4BACjC;gCAAC;gCAAS,MAAM,CAAC,KAAK,CAAC,EAAE;gCAAE;6BAAQ;4BACnC;gCAAC;gCAAQ,MAAM,CAAC,KAAK,CAAC,EAAE;gCAAE;6BAAQ;4BAClC;gCAAC;gCAAS;gCAAM;6BAAQ;yBACzB;wBAED,MAAM,aAAa,QAAQ,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI;wBAE5D,IAAI,YAAY;4BACd,WAAW;4BACX,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EACH,YACA,WAAW,MAAM,EACjB,GACA,CAAA,GAAA,+JAAA,CAAA,aAAU,AAAD,EAAE,YAAY,OAAO,KAAK,CAAC,OAAO,GAAG,QAAQ;wBAE1D;wBAEA,WAAW;wBACX,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,YAAY,WAAW,MAAM,EAAE,GAAG;4BACvC;gCAAC;gCAAQ;gCAAM;6BAAQ;4BACvB;gCAAC;gCAAS,MAAM,CAAC,MAAM,CAAC,EAAE;gCAAE;6BAAQ;4BACpC;gCAAC;gCAAQ,MAAM,CAAC,MAAM,CAAC,EAAE;gCAAE;6BAAQ;4BACnC;gCAAC;gCAAQ;gCAAe;6BAAQ;yBACjC;wBAED,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,OAAO,GAAG,QAAQ,OAAO,GAAG;wBAE3C,QAAQ,OAAO,WAAW,MAAM,GAAG;wBACnC;oBACF;gBACF;YACF;QACF;QAEA,QAAQ,CAAC;QAET,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;YAC9B,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,kCAAkC;gBAC9D,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG,8JAAA,CAAA,QAAK,CAAC,IAAI;YACpC;QACF;QAEA,OAAO;IACT;IAEA;;;GAGC,GACD,SAAS,sBAAsB,OAAO,EAAE,EAAE,EAAE,GAAG;QAC7C,MAAM,WAAW,IAAI,CAAC,QAAQ;QAC9B,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,IAAI,OAAO;QAEX,OAAO;;QAEP,kBAAkB,GAClB,SAAS,MAAM,IAAI;YACjB,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK,EAAE;YAE7B,IACE,aAAa,8JAAA,CAAA,QAAK,CAAC,KAAK,IACxB,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,8JAAA,CAAA,QAAK,CAAC,eAAe,EAC3D;gBACA,OAAO,IAAI;YACb;YAEA,QAAQ,KAAK,CAAC;YACd,OAAO,KAAK;QACd;QAEA,kBAAkB,GAClB,SAAS,KAAK,IAAI;YAChB,MAAM,SAAS,CAAA,GAAA,6KAAA,CAAA,oBAAiB,AAAD,EAAE;YAEjC,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK,EAAE;gBACxB,qCAAqC;gBACrC,IAAI,OAAO,GAAG,OAAO,IAAI;gBACzB,QAAQ,OAAO,CAAC;gBAChB;gBACA,OAAO;YACT;YAEA,IAAI,OAAO,KAAK,CAAC,QAAQ,OAAO,IAAI;YACpC,MAAM,QAAQ,QAAQ,IAAI,CAAC;YAC3B,MAAM,QAAQ,CAAA,GAAA,6KAAA,CAAA,oBAAiB,AAAD,EAAE;YAChC,MAAM,KAAK,GACT,CAAC,SAAU,UAAU,kKAAA,CAAA,YAAS,CAAC,kBAAkB,IAAI,QAAQ;YAC/D,MAAM,MAAM,GACV,CAAC,UAAW,WAAW,kKAAA,CAAA,YAAS,CAAC,kBAAkB,IAAI,QAAQ;YACjE,OAAO,GAAG;QACZ;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6897, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-extension-gfm-strikethrough/dev/lib/html.js"], "sourcesContent": ["/**\n * @import {HtmlExtension} from 'micromark-util-types'\n */\n\n/**\n * Create an HTML extension for `micromark` to support GFM strikethrough when\n * serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions`, to\n *   support GFM strikethrough when serializing to HTML.\n */\nexport function gfmStrikethroughHtml() {\n  return {\n    enter: {\n      strikethrough() {\n        this.tag('<del>')\n      }\n    },\n    exit: {\n      strikethrough() {\n        this.tag('</del>')\n      }\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;;;;CAOC;;;AACM,SAAS;IACd,OAAO;QACL,OAAO;YACL;gBACE,IAAI,CAAC,GAAG,CAAC;YACX;QACF;QACA,MAAM;YACJ;gBACE,IAAI,CAAC,GAAG,CAAC;YACX;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6929, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js"], "sourcesContent": ["/**\n * @import {Event} from 'micromark-util-types'\n */\n\n// Port of `edit_map.rs` from `markdown-rs`.\n// This should move to `markdown-js` later.\n\n// Deal with several changes in events, batching them together.\n//\n// Preferably, changes should be kept to a minimum.\n// Sometimes, it’s needed to change the list of events, because parsing can be\n// messy, and it helps to expose a cleaner interface of events to the compiler\n// and other users.\n// It can also help to merge many adjacent similar events.\n// And, in other cases, it’s needed to parse subcontent: pass some events\n// through another tokenizer and inject the result.\n\n/**\n * @typedef {[number, number, Array<Event>]} Change\n * @typedef {[number, number, number]} Jump\n */\n\n/**\n * Tracks a bunch of edits.\n */\nexport class EditMap {\n  /**\n   * Create a new edit map.\n   */\n  constructor() {\n    /**\n     * Record of changes.\n     *\n     * @type {Array<Change>}\n     */\n    this.map = []\n  }\n\n  /**\n   * Create an edit: a remove and/or add at a certain place.\n   *\n   * @param {number} index\n   * @param {number} remove\n   * @param {Array<Event>} add\n   * @returns {undefined}\n   */\n  add(index, remove, add) {\n    addImplementation(this, index, remove, add)\n  }\n\n  // To do: add this when moving to `micromark`.\n  // /**\n  //  * Create an edit: but insert `add` before existing additions.\n  //  *\n  //  * @param {number} index\n  //  * @param {number} remove\n  //  * @param {Array<Event>} add\n  //  * @returns {undefined}\n  //  */\n  // addBefore(index, remove, add) {\n  //   addImplementation(this, index, remove, add, true)\n  // }\n\n  /**\n   * Done, change the events.\n   *\n   * @param {Array<Event>} events\n   * @returns {undefined}\n   */\n  consume(events) {\n    this.map.sort(function (a, b) {\n      return a[0] - b[0]\n    })\n\n    /* c8 ignore next 3 -- `resolve` is never called without tables, so without edits. */\n    if (this.map.length === 0) {\n      return\n    }\n\n    // To do: if links are added in events, like they are in `markdown-rs`,\n    // this is needed.\n    // // Calculate jumps: where items in the current list move to.\n    // /** @type {Array<Jump>} */\n    // const jumps = []\n    // let index = 0\n    // let addAcc = 0\n    // let removeAcc = 0\n    // while (index < this.map.length) {\n    //   const [at, remove, add] = this.map[index]\n    //   removeAcc += remove\n    //   addAcc += add.length\n    //   jumps.push([at, removeAcc, addAcc])\n    //   index += 1\n    // }\n    //\n    // . shiftLinks(events, jumps)\n\n    let index = this.map.length\n    /** @type {Array<Array<Event>>} */\n    const vecs = []\n    while (index > 0) {\n      index -= 1\n      vecs.push(\n        events.slice(this.map[index][0] + this.map[index][1]),\n        this.map[index][2]\n      )\n\n      // Truncate rest.\n      events.length = this.map[index][0]\n    }\n\n    vecs.push(events.slice())\n    events.length = 0\n\n    let slice = vecs.pop()\n\n    while (slice) {\n      for (const element of slice) {\n        events.push(element)\n      }\n\n      slice = vecs.pop()\n    }\n\n    // Truncate everything.\n    this.map.length = 0\n  }\n}\n\n/**\n * Create an edit.\n *\n * @param {EditMap} editMap\n * @param {number} at\n * @param {number} remove\n * @param {Array<Event>} add\n * @returns {undefined}\n */\nfunction addImplementation(editMap, at, remove, add) {\n  let index = 0\n\n  /* c8 ignore next 3 -- `resolve` is never called without tables, so without edits. */\n  if (remove === 0 && add.length === 0) {\n    return\n  }\n\n  while (index < editMap.map.length) {\n    if (editMap.map[index][0] === at) {\n      editMap.map[index][1] += remove\n\n      // To do: before not used by tables, use when moving to micromark.\n      // if (before) {\n      //   add.push(...editMap.map[index][2])\n      //   editMap.map[index][2] = add\n      // } else {\n      editMap.map[index][2].push(...add)\n      // }\n\n      return\n    }\n\n    index += 1\n  }\n\n  editMap.map.push([at, remove, add])\n}\n\n// /**\n//  * Shift `previous` and `next` links according to `jumps`.\n//  *\n//  * This fixes links in case there are events removed or added between them.\n//  *\n//  * @param {Array<Event>} events\n//  * @param {Array<Jump>} jumps\n//  */\n// function shiftLinks(events, jumps) {\n//   let jumpIndex = 0\n//   let index = 0\n//   let add = 0\n//   let rm = 0\n\n//   while (index < events.length) {\n//     const rmCurr = rm\n\n//     while (jumpIndex < jumps.length && jumps[jumpIndex][0] <= index) {\n//       add = jumps[jumpIndex][2]\n//       rm = jumps[jumpIndex][1]\n//       jumpIndex += 1\n//     }\n\n//     // Ignore items that will be removed.\n//     if (rm > rmCurr) {\n//       index += rm - rmCurr\n//     } else {\n//       // ?\n//       // if let Some(link) = &events[index].link {\n//       //     if let Some(next) = link.next {\n//       //         events[next].link.as_mut().unwrap().previous = Some(index + add - rm);\n//       //         while jumpIndex < jumps.len() && jumps[jumpIndex].0 <= next {\n//       //             add = jumps[jumpIndex].2;\n//       //             rm = jumps[jumpIndex].1;\n//       //             jumpIndex += 1;\n//       //         }\n//       //         events[index].link.as_mut().unwrap().next = Some(next + add - rm);\n//       //         index = next;\n//       //         continue;\n//       //     }\n//       // }\n//       index += 1\n//     }\n//   }\n// }\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,4CAA4C;AAC5C,2CAA2C;AAE3C,+DAA+D;AAC/D,EAAE;AACF,mDAAmD;AACnD,8EAA8E;AAC9E,8EAA8E;AAC9E,mBAAmB;AACnB,0DAA0D;AAC1D,yEAAyE;AACzE,mDAAmD;AAEnD;;;CAGC,GAED;;CAEC;;;AACM,MAAM;IACX;;GAEC,GACD,aAAc;QACZ;;;;KAIC,GACD,IAAI,CAAC,GAAG,GAAG,EAAE;IACf;IAEA;;;;;;;GAOC,GACD,IAAI,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE;QACtB,kBAAkB,IAAI,EAAE,OAAO,QAAQ;IACzC;IAEA,8CAA8C;IAC9C,MAAM;IACN,iEAAiE;IACjE,KAAK;IACL,2BAA2B;IAC3B,4BAA4B;IAC5B,+BAA+B;IAC/B,0BAA0B;IAC1B,MAAM;IACN,kCAAkC;IAClC,sDAAsD;IACtD,IAAI;IAEJ;;;;;GAKC,GACD,QAAQ,MAAM,EAAE;QACd,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;YAC1B,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACpB;QAEA,mFAAmF,GACnF,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,KAAK,GAAG;YACzB;QACF;QAEA,uEAAuE;QACvE,kBAAkB;QAClB,+DAA+D;QAC/D,6BAA6B;QAC7B,mBAAmB;QACnB,gBAAgB;QAChB,iBAAiB;QACjB,oBAAoB;QACpB,oCAAoC;QACpC,8CAA8C;QAC9C,wBAAwB;QACxB,yBAAyB;QACzB,wCAAwC;QACxC,eAAe;QACf,IAAI;QACJ,EAAE;QACF,8BAA8B;QAE9B,IAAI,QAAQ,IAAI,CAAC,GAAG,CAAC,MAAM;QAC3B,gCAAgC,GAChC,MAAM,OAAO,EAAE;QACf,MAAO,QAAQ,EAAG;YAChB,SAAS;YACT,KAAK,IAAI,CACP,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,GACpD,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YAGpB,iBAAiB;YACjB,OAAO,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;QACpC;QAEA,KAAK,IAAI,CAAC,OAAO,KAAK;QACtB,OAAO,MAAM,GAAG;QAEhB,IAAI,QAAQ,KAAK,GAAG;QAEpB,MAAO,MAAO;YACZ,KAAK,MAAM,WAAW,MAAO;gBAC3B,OAAO,IAAI,CAAC;YACd;YAEA,QAAQ,KAAK,GAAG;QAClB;QAEA,uBAAuB;QACvB,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG;IACpB;AACF;AAEA;;;;;;;;CAQC,GACD,SAAS,kBAAkB,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG;IACjD,IAAI,QAAQ;IAEZ,mFAAmF,GACnF,IAAI,WAAW,KAAK,IAAI,MAAM,KAAK,GAAG;QACpC;IACF;IAEA,MAAO,QAAQ,QAAQ,GAAG,CAAC,MAAM,CAAE;QACjC,IAAI,QAAQ,GAAG,CAAC,MAAM,CAAC,EAAE,KAAK,IAAI;YAChC,QAAQ,GAAG,CAAC,MAAM,CAAC,EAAE,IAAI;YAEzB,kEAAkE;YAClE,gBAAgB;YAChB,uCAAuC;YACvC,gCAAgC;YAChC,WAAW;YACX,QAAQ,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,IAAI;YAC9B,IAAI;YAEJ;QACF;QAEA,SAAS;IACX;IAEA,QAAQ,GAAG,CAAC,IAAI,CAAC;QAAC;QAAI;QAAQ;KAAI;AACpC,EAEA,MAAM;CACN,6DAA6D;CAC7D,KAAK;CACL,8EAA8E;CAC9E,KAAK;CACL,kCAAkC;CAClC,gCAAgC;CAChC,MAAM;CACN,uCAAuC;CACvC,sBAAsB;CACtB,kBAAkB;CAClB,gBAAgB;CAChB,eAAe;CAEf,oCAAoC;CACpC,wBAAwB;CAExB,yEAAyE;CACzE,kCAAkC;CAClC,iCAAiC;CACjC,uBAAuB;CACvB,QAAQ;CAER,4CAA4C;CAC5C,yBAAyB;CACzB,6BAA6B;CAC7B,eAAe;CACf,aAAa;CACb,qDAAqD;CACrD,+CAA+C;CAC/C,0FAA0F;CAC1F,iFAAiF;CACjF,iDAAiD;CACjD,gDAAgD;CAChD,uCAAuC;CACvC,qBAAqB;CACrB,sFAAsF;CACtF,iCAAiC;CACjC,6BAA6B;CAC7B,iBAAiB;CACjB,aAAa;CACb,mBAAmB;CACnB,QAAQ;CACR,MAAM;CACN,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7112, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-extension-gfm-table/dev/lib/infer.js"], "sourcesContent": ["/**\n * @import {Event} from 'micromark-util-types'\n */\n\n/**\n * @typedef {'center' | 'left' | 'none' | 'right'} Align\n */\n\nimport {ok as assert} from 'devlop'\n\n/**\n * Figure out the alignment of a GFM table.\n *\n * @param {Readonly<Array<Event>>} events\n *   List of events.\n * @param {number} index\n *   Table enter event.\n * @returns {Array<Align>}\n *   List of aligns.\n */\nexport function gfmTableAlign(events, index) {\n  assert(events[index][1].type === 'table', 'expected table')\n  let inDelimiterRow = false\n  /** @type {Array<Align>} */\n  const align = []\n\n  while (index < events.length) {\n    const event = events[index]\n\n    if (inDelimiterRow) {\n      if (event[0] === 'enter') {\n        // Start of alignment value: set a new column.\n        // To do: `markdown-rs` uses `tableDelimiterCellValue`.\n        if (event[1].type === 'tableContent') {\n          align.push(\n            events[index + 1][1].type === 'tableDelimiterMarker'\n              ? 'left'\n              : 'none'\n          )\n        }\n      }\n      // Exits:\n      // End of alignment value: change the column.\n      // To do: `markdown-rs` uses `tableDelimiterCellValue`.\n      else if (event[1].type === 'tableContent') {\n        if (events[index - 1][1].type === 'tableDelimiterMarker') {\n          const alignIndex = align.length - 1\n\n          align[alignIndex] = align[alignIndex] === 'left' ? 'center' : 'right'\n        }\n      }\n      // Done!\n      else if (event[1].type === 'tableDelimiterRow') {\n        break\n      }\n    } else if (event[0] === 'enter' && event[1].type === 'tableDelimiterRow') {\n      inDelimiterRow = true\n    }\n\n    index += 1\n  }\n\n  return align\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;CAEC;;;AAED;;AAYO,SAAS,cAAc,MAAM,EAAE,KAAK;IACzC,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,SAAS;IAC1C,IAAI,iBAAiB;IACrB,yBAAyB,GACzB,MAAM,QAAQ,EAAE;IAEhB,MAAO,QAAQ,OAAO,MAAM,CAAE;QAC5B,MAAM,QAAQ,MAAM,CAAC,MAAM;QAE3B,IAAI,gBAAgB;YAClB,IAAI,KAAK,CAAC,EAAE,KAAK,SAAS;gBACxB,8CAA8C;gBAC9C,uDAAuD;gBACvD,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,gBAAgB;oBACpC,MAAM,IAAI,CACR,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,yBAC1B,SACA;gBAER;YACF,OAIK,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,gBAAgB;gBACzC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,wBAAwB;oBACxD,MAAM,aAAa,MAAM,MAAM,GAAG;oBAElC,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,KAAK,SAAS,WAAW;gBAChE;YACF,OAEK,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,qBAAqB;gBAC9C;YACF;QACF,OAAO,IAAI,KAAK,CAAC,EAAE,KAAK,WAAW,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,qBAAqB;YACxE,iBAAiB;QACnB;QAEA,SAAS;IACX;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7155, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-extension-gfm-table/dev/lib/syntax.js"], "sourcesContent": ["/**\n * @import {Event, Extension, Point, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n/**\n * @typedef {[number, number, number, number]} Range\n *   Cell info.\n *\n * @typedef {0 | 1 | 2 | 3} RowKind\n *   Where we are: `1` for head row, `2` for delimiter row, `3` for body row.\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {\n  markdownLineEnding,\n  markdownLineEndingOrSpace,\n  markdownSpace\n} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\nimport {EditMap} from './edit-map.js'\nimport {gfmTableAlign} from './infer.js'\n\n/**\n * Create an HTML extension for `micromark` to support GitHub tables syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to enable GFM\n *   table syntax.\n */\nexport function gfmTable() {\n  return {\n    flow: {\n      null: {name: 'table', tokenize: tokenizeTable, resolveAll: resolveTable}\n    }\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeTable(effects, ok, nok) {\n  const self = this\n  let size = 0\n  let sizeB = 0\n  /** @type {boolean | undefined} */\n  let seen\n\n  return start\n\n  /**\n   * Start of a GFM table.\n   *\n   * If there is a valid table row or table head before, then we try to parse\n   * another row.\n   * Otherwise, we try to parse a head.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   * > | | b |\n   *     ^\n   * ```\n   * @type {State}\n   */\n  function start(code) {\n    let index = self.events.length - 1\n\n    while (index > -1) {\n      const type = self.events[index][1].type\n      if (\n        type === types.lineEnding ||\n        // Note: markdown-rs uses `whitespace` instead of `linePrefix`\n        type === types.linePrefix\n      )\n        index--\n      else break\n    }\n\n    const tail = index > -1 ? self.events[index][1].type : null\n\n    const next =\n      tail === 'tableHead' || tail === 'tableRow' ? bodyRowStart : headRowBefore\n\n    // Don’t allow lazy body rows.\n    if (next === bodyRowStart && self.parser.lazy[self.now().line]) {\n      return nok(code)\n    }\n\n    return next(code)\n  }\n\n  /**\n   * Before table head row.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowBefore(code) {\n    effects.enter('tableHead')\n    effects.enter('tableRow')\n    return headRowStart(code)\n  }\n\n  /**\n   * Before table head row, after whitespace.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowStart(code) {\n    if (code === codes.verticalBar) {\n      return headRowBreak(code)\n    }\n\n    // To do: micromark-js should let us parse our own whitespace in extensions,\n    // like `markdown-rs`:\n    //\n    // ```js\n    // // 4+ spaces.\n    // if (markdownSpace(code)) {\n    //   return nok(code)\n    // }\n    // ```\n\n    seen = true\n    // Count the first character, that isn’t a pipe, double.\n    sizeB += 1\n    return headRowBreak(code)\n  }\n\n  /**\n   * At break in table head row.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *       ^\n   *         ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowBreak(code) {\n    if (code === codes.eof) {\n      // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n      return nok(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      // If anything other than one pipe (ignoring whitespace) was used, it’s fine.\n      if (sizeB > 1) {\n        sizeB = 0\n        // To do: check if this works.\n        // Feel free to interrupt:\n        self.interrupt = true\n        effects.exit('tableRow')\n        effects.enter(types.lineEnding)\n        effects.consume(code)\n        effects.exit(types.lineEnding)\n        return headDelimiterStart\n      }\n\n      // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n      return nok(code)\n    }\n\n    if (markdownSpace(code)) {\n      // To do: check if this is fine.\n      // effects.attempt(State::Next(StateName::GfmTableHeadRowBreak), State::Nok)\n      // State::Retry(space_or_tab(tokenizer))\n      return factorySpace(effects, headRowBreak, types.whitespace)(code)\n    }\n\n    sizeB += 1\n\n    if (seen) {\n      seen = false\n      // Header cell count.\n      size += 1\n    }\n\n    if (code === codes.verticalBar) {\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      // Whether a delimiter was seen.\n      seen = true\n      return headRowBreak\n    }\n\n    // Anything else is cell data.\n    effects.enter(types.data)\n    return headRowData(code)\n  }\n\n  /**\n   * In table head row data.\n   *\n   * ```markdown\n   * > | | a |\n   *       ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowData(code) {\n    if (\n      code === codes.eof ||\n      code === codes.verticalBar ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      effects.exit(types.data)\n      return headRowBreak(code)\n    }\n\n    effects.consume(code)\n    return code === codes.backslash ? headRowEscape : headRowData\n  }\n\n  /**\n   * In table head row escape.\n   *\n   * ```markdown\n   * > | | a\\-b |\n   *         ^\n   *   | | ---- |\n   *   | | c    |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowEscape(code) {\n    if (code === codes.backslash || code === codes.verticalBar) {\n      effects.consume(code)\n      return headRowData\n    }\n\n    return headRowData(code)\n  }\n\n  /**\n   * Before delimiter row.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *     ^\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterStart(code) {\n    // Reset `interrupt`.\n    self.interrupt = false\n\n    // Note: in `markdown-rs`, we need to handle piercing here too.\n    if (self.parser.lazy[self.now().line]) {\n      return nok(code)\n    }\n\n    effects.enter('tableDelimiterRow')\n    // Track if we’ve seen a `:` or `|`.\n    seen = false\n\n    if (markdownSpace(code)) {\n      assert(self.parser.constructs.disable.null, 'expected `disabled.null`')\n      return factorySpace(\n        effects,\n        headDelimiterBefore,\n        types.linePrefix,\n        self.parser.constructs.disable.null.includes('codeIndented')\n          ? undefined\n          : constants.tabSize\n      )(code)\n    }\n\n    return headDelimiterBefore(code)\n  }\n\n  /**\n   * Before delimiter row, after optional whitespace.\n   *\n   * Reused when a `|` is found later, to parse another cell.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *     ^\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterBefore(code) {\n    if (code === codes.dash || code === codes.colon) {\n      return headDelimiterValueBefore(code)\n    }\n\n    if (code === codes.verticalBar) {\n      seen = true\n      // If we start with a pipe, we open a cell marker.\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      return headDelimiterCellBefore\n    }\n\n    // More whitespace / empty row not allowed at start.\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * After `|`, before delimiter cell.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterCellBefore(code) {\n    if (markdownSpace(code)) {\n      return factorySpace(\n        effects,\n        headDelimiterValueBefore,\n        types.whitespace\n      )(code)\n    }\n\n    return headDelimiterValueBefore(code)\n  }\n\n  /**\n   * Before delimiter cell value.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterValueBefore(code) {\n    // Align: left.\n    if (code === codes.colon) {\n      sizeB += 1\n      seen = true\n\n      effects.enter('tableDelimiterMarker')\n      effects.consume(code)\n      effects.exit('tableDelimiterMarker')\n      return headDelimiterLeftAlignmentAfter\n    }\n\n    // Align: none.\n    if (code === codes.dash) {\n      sizeB += 1\n      // To do: seems weird that this *isn’t* left aligned, but that state is used?\n      return headDelimiterLeftAlignmentAfter(code)\n    }\n\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return headDelimiterCellAfter(code)\n    }\n\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * After delimiter cell left alignment marker.\n   *\n   * ```markdown\n   *   | | a  |\n   * > | | :- |\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterLeftAlignmentAfter(code) {\n    if (code === codes.dash) {\n      effects.enter('tableDelimiterFiller')\n      return headDelimiterFiller(code)\n    }\n\n    // Anything else is not ok after the left-align colon.\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * In delimiter cell filler.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterFiller(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      return headDelimiterFiller\n    }\n\n    // Align is `center` if it was `left`, `right` otherwise.\n    if (code === codes.colon) {\n      seen = true\n      effects.exit('tableDelimiterFiller')\n      effects.enter('tableDelimiterMarker')\n      effects.consume(code)\n      effects.exit('tableDelimiterMarker')\n      return headDelimiterRightAlignmentAfter\n    }\n\n    effects.exit('tableDelimiterFiller')\n    return headDelimiterRightAlignmentAfter(code)\n  }\n\n  /**\n   * After delimiter cell right alignment marker.\n   *\n   * ```markdown\n   *   | |  a |\n   * > | | -: |\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterRightAlignmentAfter(code) {\n    if (markdownSpace(code)) {\n      return factorySpace(\n        effects,\n        headDelimiterCellAfter,\n        types.whitespace\n      )(code)\n    }\n\n    return headDelimiterCellAfter(code)\n  }\n\n  /**\n   * After delimiter cell.\n   *\n   * ```markdown\n   *   | |  a |\n   * > | | -: |\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterCellAfter(code) {\n    if (code === codes.verticalBar) {\n      return headDelimiterBefore(code)\n    }\n\n    if (code === codes.eof || markdownLineEnding(code)) {\n      // Exit when:\n      // * there was no `:` or `|` at all (it’s a thematic break or setext\n      //   underline instead)\n      // * the header cell count is not the delimiter cell count\n      if (!seen || size !== sizeB) {\n        return headDelimiterNok(code)\n      }\n\n      // Note: in markdown-rs`, a reset is needed here.\n      effects.exit('tableDelimiterRow')\n      effects.exit('tableHead')\n      // To do: in `markdown-rs`, resolvers need to be registered manually.\n      // effects.register_resolver(ResolveName::GfmTable)\n      return ok(code)\n    }\n\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * In delimiter row, at a disallowed byte.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | x |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterNok(code) {\n    // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n    return nok(code)\n  }\n\n  /**\n   * Before table body row.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowStart(code) {\n    // Note: in `markdown-rs` we need to manually take care of a prefix,\n    // but in `micromark-js` that is done for us, so if we’re here, we’re\n    // never at whitespace.\n    effects.enter('tableRow')\n    return bodyRowBreak(code)\n  }\n\n  /**\n   * At break in table body row.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *     ^\n   *       ^\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowBreak(code) {\n    if (code === codes.verticalBar) {\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      return bodyRowBreak\n    }\n\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit('tableRow')\n      return ok(code)\n    }\n\n    if (markdownSpace(code)) {\n      return factorySpace(effects, bodyRowBreak, types.whitespace)(code)\n    }\n\n    // Anything else is cell content.\n    effects.enter(types.data)\n    return bodyRowData(code)\n  }\n\n  /**\n   * In table body row data.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowData(code) {\n    if (\n      code === codes.eof ||\n      code === codes.verticalBar ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      effects.exit(types.data)\n      return bodyRowBreak(code)\n    }\n\n    effects.consume(code)\n    return code === codes.backslash ? bodyRowEscape : bodyRowData\n  }\n\n  /**\n   * In table body row escape.\n   *\n   * ```markdown\n   *   | | a    |\n   *   | | ---- |\n   * > | | b\\-c |\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowEscape(code) {\n    if (code === codes.backslash || code === codes.verticalBar) {\n      effects.consume(code)\n      return bodyRowData\n    }\n\n    return bodyRowData(code)\n  }\n}\n\n/** @type {Resolver} */\n\nfunction resolveTable(events, context) {\n  let index = -1\n  let inFirstCellAwaitingPipe = true\n  /** @type {RowKind} */\n  let rowKind = 0\n  /** @type {Range} */\n  let lastCell = [0, 0, 0, 0]\n  /** @type {Range} */\n  let cell = [0, 0, 0, 0]\n  let afterHeadAwaitingFirstBodyRow = false\n  let lastTableEnd = 0\n  /** @type {Token | undefined} */\n  let currentTable\n  /** @type {Token | undefined} */\n  let currentBody\n  /** @type {Token | undefined} */\n  let currentCell\n\n  const map = new EditMap()\n\n  while (++index < events.length) {\n    const event = events[index]\n    const token = event[1]\n\n    if (event[0] === 'enter') {\n      // Start of head.\n      if (token.type === 'tableHead') {\n        afterHeadAwaitingFirstBodyRow = false\n\n        // Inject previous (body end and) table end.\n        if (lastTableEnd !== 0) {\n          assert(currentTable, 'there should be a table opening')\n          flushTableEnd(map, context, lastTableEnd, currentTable, currentBody)\n          currentBody = undefined\n          lastTableEnd = 0\n        }\n\n        // Inject table start.\n        currentTable = {\n          type: 'table',\n          start: Object.assign({}, token.start),\n          // Note: correct end is set later.\n          end: Object.assign({}, token.end)\n        }\n        map.add(index, 0, [['enter', currentTable, context]])\n      } else if (\n        token.type === 'tableRow' ||\n        token.type === 'tableDelimiterRow'\n      ) {\n        inFirstCellAwaitingPipe = true\n        currentCell = undefined\n        lastCell = [0, 0, 0, 0]\n        cell = [0, index + 1, 0, 0]\n\n        // Inject table body start.\n        if (afterHeadAwaitingFirstBodyRow) {\n          afterHeadAwaitingFirstBodyRow = false\n          currentBody = {\n            type: 'tableBody',\n            start: Object.assign({}, token.start),\n            // Note: correct end is set later.\n            end: Object.assign({}, token.end)\n          }\n          map.add(index, 0, [['enter', currentBody, context]])\n        }\n\n        rowKind = token.type === 'tableDelimiterRow' ? 2 : currentBody ? 3 : 1\n      }\n      // Cell data.\n      else if (\n        rowKind &&\n        (token.type === types.data ||\n          token.type === 'tableDelimiterMarker' ||\n          token.type === 'tableDelimiterFiller')\n      ) {\n        inFirstCellAwaitingPipe = false\n\n        // First value in cell.\n        if (cell[2] === 0) {\n          if (lastCell[1] !== 0) {\n            cell[0] = cell[1]\n            currentCell = flushCell(\n              map,\n              context,\n              lastCell,\n              rowKind,\n              undefined,\n              currentCell\n            )\n            lastCell = [0, 0, 0, 0]\n          }\n\n          cell[2] = index\n        }\n      } else if (token.type === 'tableCellDivider') {\n        if (inFirstCellAwaitingPipe) {\n          inFirstCellAwaitingPipe = false\n        } else {\n          if (lastCell[1] !== 0) {\n            cell[0] = cell[1]\n            currentCell = flushCell(\n              map,\n              context,\n              lastCell,\n              rowKind,\n              undefined,\n              currentCell\n            )\n          }\n\n          lastCell = cell\n          cell = [lastCell[1], index, 0, 0]\n        }\n      }\n    }\n    // Exit events.\n    else if (token.type === 'tableHead') {\n      afterHeadAwaitingFirstBodyRow = true\n      lastTableEnd = index\n    } else if (\n      token.type === 'tableRow' ||\n      token.type === 'tableDelimiterRow'\n    ) {\n      lastTableEnd = index\n\n      if (lastCell[1] !== 0) {\n        cell[0] = cell[1]\n        currentCell = flushCell(\n          map,\n          context,\n          lastCell,\n          rowKind,\n          index,\n          currentCell\n        )\n      } else if (cell[1] !== 0) {\n        currentCell = flushCell(map, context, cell, rowKind, index, currentCell)\n      }\n\n      rowKind = 0\n    } else if (\n      rowKind &&\n      (token.type === types.data ||\n        token.type === 'tableDelimiterMarker' ||\n        token.type === 'tableDelimiterFiller')\n    ) {\n      cell[3] = index\n    }\n  }\n\n  if (lastTableEnd !== 0) {\n    assert(currentTable, 'expected table opening')\n    flushTableEnd(map, context, lastTableEnd, currentTable, currentBody)\n  }\n\n  map.consume(context.events)\n\n  // To do: move this into `html`, when events are exposed there.\n  // That’s what `markdown-rs` does.\n  // That needs updates to `mdast-util-gfm-table`.\n  index = -1\n  while (++index < context.events.length) {\n    const event = context.events[index]\n    if (event[0] === 'enter' && event[1].type === 'table') {\n      event[1]._align = gfmTableAlign(context.events, index)\n    }\n  }\n\n  return events\n}\n\n/**\n * Generate a cell.\n *\n * @param {EditMap} map\n * @param {Readonly<TokenizeContext>} context\n * @param {Readonly<Range>} range\n * @param {RowKind} rowKind\n * @param {number | undefined} rowEnd\n * @param {Token | undefined} previousCell\n * @returns {Token | undefined}\n */\n// eslint-disable-next-line max-params\nfunction flushCell(map, context, range, rowKind, rowEnd, previousCell) {\n  // `markdown-rs` uses:\n  // rowKind === 2 ? 'tableDelimiterCell' : 'tableCell'\n  const groupName =\n    rowKind === 1\n      ? 'tableHeader'\n      : rowKind === 2\n        ? 'tableDelimiter'\n        : 'tableData'\n  // `markdown-rs` uses:\n  // rowKind === 2 ? 'tableDelimiterCellValue' : 'tableCellText'\n  const valueName = 'tableContent'\n\n  // Insert an exit for the previous cell, if there is one.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //          ^-- exit\n  //           ^^^^-- this cell\n  // ```\n  if (range[0] !== 0) {\n    assert(previousCell, 'expected previous cell enter')\n    previousCell.end = Object.assign({}, getPoint(context.events, range[0]))\n    map.add(range[0], 0, [['exit', previousCell, context]])\n  }\n\n  // Insert enter of this cell.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //           ^-- enter\n  //           ^^^^-- this cell\n  // ```\n  const now = getPoint(context.events, range[1])\n  previousCell = {\n    type: groupName,\n    start: Object.assign({}, now),\n    // Note: correct end is set later.\n    end: Object.assign({}, now)\n  }\n  map.add(range[1], 0, [['enter', previousCell, context]])\n\n  // Insert text start at first data start and end at last data end, and\n  // remove events between.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //            ^-- enter\n  //             ^-- exit\n  //           ^^^^-- this cell\n  // ```\n  if (range[2] !== 0) {\n    const relatedStart = getPoint(context.events, range[2])\n    const relatedEnd = getPoint(context.events, range[3])\n    /** @type {Token} */\n    const valueToken = {\n      type: valueName,\n      start: Object.assign({}, relatedStart),\n      end: Object.assign({}, relatedEnd)\n    }\n    map.add(range[2], 0, [['enter', valueToken, context]])\n    assert(range[3] !== 0)\n\n    if (rowKind !== 2) {\n      // Fix positional info on remaining events\n      const start = context.events[range[2]]\n      const end = context.events[range[3]]\n      start[1].end = Object.assign({}, end[1].end)\n      start[1].type = types.chunkText\n      start[1].contentType = constants.contentTypeText\n\n      // Remove if needed.\n      if (range[3] > range[2] + 1) {\n        const a = range[2] + 1\n        const b = range[3] - range[2] - 1\n        map.add(a, b, [])\n      }\n    }\n\n    map.add(range[3] + 1, 0, [['exit', valueToken, context]])\n  }\n\n  // Insert an exit for the last cell, if at the row end.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //                    ^-- exit\n  //               ^^^^^^-- this cell (the last one contains two “between” parts)\n  // ```\n  if (rowEnd !== undefined) {\n    previousCell.end = Object.assign({}, getPoint(context.events, rowEnd))\n    map.add(rowEnd, 0, [['exit', previousCell, context]])\n    previousCell = undefined\n  }\n\n  return previousCell\n}\n\n/**\n * Generate table end (and table body end).\n *\n * @param {Readonly<EditMap>} map\n * @param {Readonly<TokenizeContext>} context\n * @param {number} index\n * @param {Token} table\n * @param {Token | undefined} tableBody\n */\n// eslint-disable-next-line max-params\nfunction flushTableEnd(map, context, index, table, tableBody) {\n  /** @type {Array<Event>} */\n  const exits = []\n  const related = getPoint(context.events, index)\n\n  if (tableBody) {\n    tableBody.end = Object.assign({}, related)\n    exits.push(['exit', tableBody, context])\n  }\n\n  table.end = Object.assign({}, related)\n  exits.push(['exit', table, context])\n\n  map.add(index + 1, 0, exits)\n}\n\n/**\n * @param {Readonly<Array<Event>>} events\n * @param {number} index\n * @returns {Readonly<Point>}\n */\nfunction getPoint(events, index) {\n  const event = events[index]\n  const side = event[0] === 'enter' ? 'start' : 'end'\n  return event[1][side]\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;;;CAMC;;;AAED;AACA;AACA;AAKA;AAAA;AAAA;AACA;AACA;;;;;;;AASO,SAAS;IACd,OAAO;QACL,MAAM;YACJ,MAAM;gBAAC,MAAM;gBAAS,UAAU;gBAAe,YAAY;YAAY;QACzE;IACF;AACF;AAEA;;;CAGC,GACD,SAAS,cAAc,OAAO,EAAE,EAAE,EAAE,GAAG;IACrC,MAAM,OAAO,IAAI;IACjB,IAAI,OAAO;IACX,IAAI,QAAQ;IACZ,gCAAgC,GAChC,IAAI;IAEJ,OAAO;;IAEP;;;;;;;;;;;;;;;GAeC,GACD,SAAS,MAAM,IAAI;QACjB,IAAI,QAAQ,KAAK,MAAM,CAAC,MAAM,GAAG;QAEjC,MAAO,QAAQ,CAAC,EAAG;YACjB,MAAM,OAAO,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI;YACvC,IACE,SAAS,8JAAA,CAAA,QAAK,CAAC,UAAU,IACzB,8DAA8D;YAC9D,SAAS,8JAAA,CAAA,QAAK,CAAC,UAAU,EAEzB;iBACG;QACP;QAEA,MAAM,OAAO,QAAQ,CAAC,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG;QAEvD,MAAM,OACJ,SAAS,eAAe,SAAS,aAAa,eAAe;QAE/D,8BAA8B;QAC9B,IAAI,SAAS,gBAAgB,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,EAAE;YAC9D,OAAO,IAAI;QACb;QAEA,OAAO,KAAK;IACd;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,cAAc,IAAI;QACzB,QAAQ,KAAK,CAAC;QACd,QAAQ,KAAK,CAAC;QACd,OAAO,aAAa;IACtB;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,aAAa,IAAI;QACxB,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC9B,OAAO,aAAa;QACtB;QAEA,4EAA4E;QAC5E,sBAAsB;QACtB,EAAE;QACF,QAAQ;QACR,gBAAgB;QAChB,6BAA6B;QAC7B,qBAAqB;QACrB,IAAI;QACJ,MAAM;QAEN,OAAO;QACP,wDAAwD;QACxD,SAAS;QACT,OAAO,aAAa;IACtB;IAEA;;;;;;;;;;;;;GAaC,GACD,SAAS,aAAa,IAAI;QACxB,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,wEAAwE;YACxE,OAAO,IAAI;QACb;QAEA,IAAI,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,6EAA6E;YAC7E,IAAI,QAAQ,GAAG;gBACb,QAAQ;gBACR,8BAA8B;gBAC9B,0BAA0B;gBAC1B,KAAK,SAAS,GAAG;gBACjB,QAAQ,IAAI,CAAC;gBACb,QAAQ,KAAK,CAAC,8JAAA,CAAA,QAAK,CAAC,UAAU;gBAC9B,QAAQ,OAAO,CAAC;gBAChB,QAAQ,IAAI,CAAC,8JAAA,CAAA,QAAK,CAAC,UAAU;gBAC7B,OAAO;YACT;YAEA,wEAAwE;YACxE,OAAO,IAAI;QACb;QAEA,IAAI,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,gCAAgC;YAChC,4EAA4E;YAC5E,wCAAwC;YACxC,OAAO,CAAA,GAAA,gKAAA,CAAA,eAAY,AAAD,EAAE,SAAS,cAAc,8JAAA,CAAA,QAAK,CAAC,UAAU,EAAE;QAC/D;QAEA,SAAS;QAET,IAAI,MAAM;YACR,OAAO;YACP,qBAAqB;YACrB,QAAQ;QACV;QAEA,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC9B,QAAQ,KAAK,CAAC;YACd,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC;YACb,gCAAgC;YAChC,OAAO;YACP,OAAO;QACT;QAEA,8BAA8B;QAC9B,QAAQ,KAAK,CAAC,8JAAA,CAAA,QAAK,CAAC,IAAI;QACxB,OAAO,YAAY;IACrB;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,YAAY,IAAI;QACvB,IACE,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,8JAAA,CAAA,QAAK,CAAC,WAAW,IAC1B,CAAA,GAAA,iKAAA,CAAA,4BAAyB,AAAD,EAAE,OAC1B;YACA,QAAQ,IAAI,CAAC,8JAAA,CAAA,QAAK,CAAC,IAAI;YACvB,OAAO,aAAa;QACtB;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO,SAAS,8JAAA,CAAA,QAAK,CAAC,SAAS,GAAG,gBAAgB;IACpD;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,cAAc,IAAI;QACzB,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,SAAS,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC1D,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,YAAY;IACrB;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,mBAAmB,IAAI;QAC9B,qBAAqB;QACrB,KAAK,SAAS,GAAG;QAEjB,+DAA+D;QAC/D,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,EAAE;YACrC,OAAO,IAAI;QACb;QAEA,QAAQ,KAAK,CAAC;QACd,oCAAoC;QACpC,OAAO;QAEP,IAAI,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE;YAC5C,OAAO,CAAA,GAAA,gKAAA,CAAA,eAAY,AAAD,EAChB,SACA,qBACA,8JAAA,CAAA,QAAK,CAAC,UAAU,EAChB,KAAK,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,kBACzC,YACA,kKAAA,CAAA,YAAS,CAAC,OAAO,EACrB;QACJ;QAEA,OAAO,oBAAoB;IAC7B;IAEA;;;;;;;;;;;;;GAaC,GACD,SAAS,oBAAoB,IAAI;QAC/B,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,IAAI,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK,EAAE;YAC/C,OAAO,yBAAyB;QAClC;QAEA,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC9B,OAAO;YACP,kDAAkD;YAClD,QAAQ,KAAK,CAAC;YACd,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,oDAAoD;QACpD,OAAO,iBAAiB;IAC1B;IAEA;;;;;;;;;;GAUC,GACD,SAAS,wBAAwB,IAAI;QACnC,IAAI,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,OAAO,CAAA,GAAA,gKAAA,CAAA,eAAY,AAAD,EAChB,SACA,0BACA,8JAAA,CAAA,QAAK,CAAC,UAAU,EAChB;QACJ;QAEA,OAAO,yBAAyB;IAClC;IAEA;;;;;;;;;;GAUC,GACD,SAAS,yBAAyB,IAAI;QACpC,eAAe;QACf,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK,EAAE;YACxB,SAAS;YACT,OAAO;YAEP,QAAQ,KAAK,CAAC;YACd,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,eAAe;QACf,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,IAAI,EAAE;YACvB,SAAS;YACT,6EAA6E;YAC7E,OAAO,gCAAgC;QACzC;QAEA,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAClD,OAAO,uBAAuB;QAChC;QAEA,OAAO,iBAAiB;IAC1B;IAEA;;;;;;;;;;GAUC,GACD,SAAS,gCAAgC,IAAI;QAC3C,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,IAAI,EAAE;YACvB,QAAQ,KAAK,CAAC;YACd,OAAO,oBAAoB;QAC7B;QAEA,sDAAsD;QACtD,OAAO,iBAAiB;IAC1B;IAEA;;;;;;;;;;GAUC,GACD,SAAS,oBAAoB,IAAI;QAC/B,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,IAAI,EAAE;YACvB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,yDAAyD;QACzD,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,KAAK,EAAE;YACxB,OAAO;YACP,QAAQ,IAAI,CAAC;YACb,QAAQ,KAAK,CAAC;YACd,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,QAAQ,IAAI,CAAC;QACb,OAAO,iCAAiC;IAC1C;IAEA;;;;;;;;;;GAUC,GACD,SAAS,iCAAiC,IAAI;QAC5C,IAAI,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,OAAO,CAAA,GAAA,gKAAA,CAAA,eAAY,AAAD,EAChB,SACA,wBACA,8JAAA,CAAA,QAAK,CAAC,UAAU,EAChB;QACJ;QAEA,OAAO,uBAAuB;IAChC;IAEA;;;;;;;;;;GAUC,GACD,SAAS,uBAAuB,IAAI;QAClC,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC9B,OAAO,oBAAoB;QAC7B;QAEA,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAClD,aAAa;YACb,oEAAoE;YACpE,uBAAuB;YACvB,0DAA0D;YAC1D,IAAI,CAAC,QAAQ,SAAS,OAAO;gBAC3B,OAAO,iBAAiB;YAC1B;YAEA,iDAAiD;YACjD,QAAQ,IAAI,CAAC;YACb,QAAQ,IAAI,CAAC;YACb,qEAAqE;YACrE,mDAAmD;YACnD,OAAO,GAAG;QACZ;QAEA,OAAO,iBAAiB;IAC1B;IAEA;;;;;;;;;;GAUC,GACD,SAAS,iBAAiB,IAAI;QAC5B,wEAAwE;QACxE,OAAO,IAAI;IACb;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,aAAa,IAAI;QACxB,oEAAoE;QACpE,qEAAqE;QACrE,uBAAuB;QACvB,QAAQ,KAAK,CAAC;QACd,OAAO,aAAa;IACtB;IAEA;;;;;;;;;;;;;GAaC,GACD,SAAS,aAAa,IAAI;QACxB,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC9B,QAAQ,KAAK,CAAC;YACd,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAClD,QAAQ,IAAI,CAAC;YACb,OAAO,GAAG;QACZ;QAEA,IAAI,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,OAAO,CAAA,GAAA,gKAAA,CAAA,eAAY,AAAD,EAAE,SAAS,cAAc,8JAAA,CAAA,QAAK,CAAC,UAAU,EAAE;QAC/D;QAEA,iCAAiC;QACjC,QAAQ,KAAK,CAAC,8JAAA,CAAA,QAAK,CAAC,IAAI;QACxB,OAAO,YAAY;IACrB;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,YAAY,IAAI;QACvB,IACE,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,8JAAA,CAAA,QAAK,CAAC,WAAW,IAC1B,CAAA,GAAA,iKAAA,CAAA,4BAAyB,AAAD,EAAE,OAC1B;YACA,QAAQ,IAAI,CAAC,8JAAA,CAAA,QAAK,CAAC,IAAI;YACvB,OAAO,aAAa;QACtB;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO,SAAS,8JAAA,CAAA,QAAK,CAAC,SAAS,GAAG,gBAAgB;IACpD;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,cAAc,IAAI;QACzB,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,SAAS,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC1D,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,YAAY;IACrB;AACF;AAEA,qBAAqB,GAErB,SAAS,aAAa,MAAM,EAAE,OAAO;IACnC,IAAI,QAAQ,CAAC;IACb,IAAI,0BAA0B;IAC9B,oBAAoB,GACpB,IAAI,UAAU;IACd,kBAAkB,GAClB,IAAI,WAAW;QAAC;QAAG;QAAG;QAAG;KAAE;IAC3B,kBAAkB,GAClB,IAAI,OAAO;QAAC;QAAG;QAAG;QAAG;KAAE;IACvB,IAAI,gCAAgC;IACpC,IAAI,eAAe;IACnB,8BAA8B,GAC9B,IAAI;IACJ,8BAA8B,GAC9B,IAAI;IACJ,8BAA8B,GAC9B,IAAI;IAEJ,MAAM,MAAM,IAAI,sLAAA,CAAA,UAAO;IAEvB,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;QAC9B,MAAM,QAAQ,MAAM,CAAC,MAAM;QAC3B,MAAM,QAAQ,KAAK,CAAC,EAAE;QAEtB,IAAI,KAAK,CAAC,EAAE,KAAK,SAAS;YACxB,iBAAiB;YACjB,IAAI,MAAM,IAAI,KAAK,aAAa;gBAC9B,gCAAgC;gBAEhC,4CAA4C;gBAC5C,IAAI,iBAAiB,GAAG;oBACtB,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,cAAc;oBACrB,cAAc,KAAK,SAAS,cAAc,cAAc;oBACxD,cAAc;oBACd,eAAe;gBACjB;gBAEA,sBAAsB;gBACtB,eAAe;oBACb,MAAM;oBACN,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,KAAK;oBACpC,kCAAkC;oBAClC,KAAK,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,GAAG;gBAClC;gBACA,IAAI,GAAG,CAAC,OAAO,GAAG;oBAAC;wBAAC;wBAAS;wBAAc;qBAAQ;iBAAC;YACtD,OAAO,IACL,MAAM,IAAI,KAAK,cACf,MAAM,IAAI,KAAK,qBACf;gBACA,0BAA0B;gBAC1B,cAAc;gBACd,WAAW;oBAAC;oBAAG;oBAAG;oBAAG;iBAAE;gBACvB,OAAO;oBAAC;oBAAG,QAAQ;oBAAG;oBAAG;iBAAE;gBAE3B,2BAA2B;gBAC3B,IAAI,+BAA+B;oBACjC,gCAAgC;oBAChC,cAAc;wBACZ,MAAM;wBACN,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,KAAK;wBACpC,kCAAkC;wBAClC,KAAK,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,GAAG;oBAClC;oBACA,IAAI,GAAG,CAAC,OAAO,GAAG;wBAAC;4BAAC;4BAAS;4BAAa;yBAAQ;qBAAC;gBACrD;gBAEA,UAAU,MAAM,IAAI,KAAK,sBAAsB,IAAI,cAAc,IAAI;YACvE,OAEK,IACH,WACA,CAAC,MAAM,IAAI,KAAK,8JAAA,CAAA,QAAK,CAAC,IAAI,IACxB,MAAM,IAAI,KAAK,0BACf,MAAM,IAAI,KAAK,sBAAsB,GACvC;gBACA,0BAA0B;gBAE1B,uBAAuB;gBACvB,IAAI,IAAI,CAAC,EAAE,KAAK,GAAG;oBACjB,IAAI,QAAQ,CAAC,EAAE,KAAK,GAAG;wBACrB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;wBACjB,cAAc,UACZ,KACA,SACA,UACA,SACA,WACA;wBAEF,WAAW;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE;oBACzB;oBAEA,IAAI,CAAC,EAAE,GAAG;gBACZ;YACF,OAAO,IAAI,MAAM,IAAI,KAAK,oBAAoB;gBAC5C,IAAI,yBAAyB;oBAC3B,0BAA0B;gBAC5B,OAAO;oBACL,IAAI,QAAQ,CAAC,EAAE,KAAK,GAAG;wBACrB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;wBACjB,cAAc,UACZ,KACA,SACA,UACA,SACA,WACA;oBAEJ;oBAEA,WAAW;oBACX,OAAO;wBAAC,QAAQ,CAAC,EAAE;wBAAE;wBAAO;wBAAG;qBAAE;gBACnC;YACF;QACF,OAEK,IAAI,MAAM,IAAI,KAAK,aAAa;YACnC,gCAAgC;YAChC,eAAe;QACjB,OAAO,IACL,MAAM,IAAI,KAAK,cACf,MAAM,IAAI,KAAK,qBACf;YACA,eAAe;YAEf,IAAI,QAAQ,CAAC,EAAE,KAAK,GAAG;gBACrB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;gBACjB,cAAc,UACZ,KACA,SACA,UACA,SACA,OACA;YAEJ,OAAO,IAAI,IAAI,CAAC,EAAE,KAAK,GAAG;gBACxB,cAAc,UAAU,KAAK,SAAS,MAAM,SAAS,OAAO;YAC9D;YAEA,UAAU;QACZ,OAAO,IACL,WACA,CAAC,MAAM,IAAI,KAAK,8JAAA,CAAA,QAAK,CAAC,IAAI,IACxB,MAAM,IAAI,KAAK,0BACf,MAAM,IAAI,KAAK,sBAAsB,GACvC;YACA,IAAI,CAAC,EAAE,GAAG;QACZ;IACF;IAEA,IAAI,iBAAiB,GAAG;QACtB,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,cAAc;QACrB,cAAc,KAAK,SAAS,cAAc,cAAc;IAC1D;IAEA,IAAI,OAAO,CAAC,QAAQ,MAAM;IAE1B,+DAA+D;IAC/D,kCAAkC;IAClC,gDAAgD;IAChD,QAAQ,CAAC;IACT,MAAO,EAAE,QAAQ,QAAQ,MAAM,CAAC,MAAM,CAAE;QACtC,MAAM,QAAQ,QAAQ,MAAM,CAAC,MAAM;QACnC,IAAI,KAAK,CAAC,EAAE,KAAK,WAAW,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,SAAS;YACrD,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,MAAM,EAAE;QAClD;IACF;IAEA,OAAO;AACT;AAEA;;;;;;;;;;CAUC,GACD,sCAAsC;AACtC,SAAS,UAAU,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY;IACnE,sBAAsB;IACtB,qDAAqD;IACrD,MAAM,YACJ,YAAY,IACR,gBACA,YAAY,IACV,mBACA;IACR,sBAAsB;IACtB,8DAA8D;IAC9D,MAAM,YAAY;IAElB,yDAAyD;IACzD,EAAE;IACF,cAAc;IACd,uBAAuB;IACvB,oBAAoB;IACpB,6BAA6B;IAC7B,MAAM;IACN,IAAI,KAAK,CAAC,EAAE,KAAK,GAAG;QAClB,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,cAAc;QACrB,aAAa,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS,QAAQ,MAAM,EAAE,KAAK,CAAC,EAAE;QACtE,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG;YAAC;gBAAC;gBAAQ;gBAAc;aAAQ;SAAC;IACxD;IAEA,6BAA6B;IAC7B,EAAE;IACF,cAAc;IACd,uBAAuB;IACvB,sBAAsB;IACtB,6BAA6B;IAC7B,MAAM;IACN,MAAM,MAAM,SAAS,QAAQ,MAAM,EAAE,KAAK,CAAC,EAAE;IAC7C,eAAe;QACb,MAAM;QACN,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG;QACzB,kCAAkC;QAClC,KAAK,OAAO,MAAM,CAAC,CAAC,GAAG;IACzB;IACA,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG;QAAC;YAAC;YAAS;YAAc;SAAQ;KAAC;IAEvD,sEAAsE;IACtE,yBAAyB;IACzB,EAAE;IACF,cAAc;IACd,uBAAuB;IACvB,uBAAuB;IACvB,uBAAuB;IACvB,6BAA6B;IAC7B,MAAM;IACN,IAAI,KAAK,CAAC,EAAE,KAAK,GAAG;QAClB,MAAM,eAAe,SAAS,QAAQ,MAAM,EAAE,KAAK,CAAC,EAAE;QACtD,MAAM,aAAa,SAAS,QAAQ,MAAM,EAAE,KAAK,CAAC,EAAE;QACpD,kBAAkB,GAClB,MAAM,aAAa;YACjB,MAAM;YACN,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG;YACzB,KAAK,OAAO,MAAM,CAAC,CAAC,GAAG;QACzB;QACA,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG;YAAC;gBAAC;gBAAS;gBAAY;aAAQ;SAAC;QACrD,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,CAAC,EAAE,KAAK;QAEpB,IAAI,YAAY,GAAG;YACjB,0CAA0C;YAC1C,MAAM,QAAQ,QAAQ,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YACtC,MAAM,MAAM,QAAQ,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YACpC,KAAK,CAAC,EAAE,CAAC,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG;YAC3C,KAAK,CAAC,EAAE,CAAC,IAAI,GAAG,8JAAA,CAAA,QAAK,CAAC,SAAS;YAC/B,KAAK,CAAC,EAAE,CAAC,WAAW,GAAG,kKAAA,CAAA,YAAS,CAAC,eAAe;YAEhD,oBAAoB;YACpB,IAAI,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG;gBAC3B,MAAM,IAAI,KAAK,CAAC,EAAE,GAAG;gBACrB,MAAM,IAAI,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG;gBAChC,IAAI,GAAG,CAAC,GAAG,GAAG,EAAE;YAClB;QACF;QAEA,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,GAAG;YAAC;gBAAC;gBAAQ;gBAAY;aAAQ;SAAC;IAC1D;IAEA,uDAAuD;IACvD,EAAE;IACF,cAAc;IACd,uBAAuB;IACvB,8BAA8B;IAC9B,+EAA+E;IAC/E,MAAM;IACN,IAAI,WAAW,WAAW;QACxB,aAAa,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS,QAAQ,MAAM,EAAE;QAC9D,IAAI,GAAG,CAAC,QAAQ,GAAG;YAAC;gBAAC;gBAAQ;gBAAc;aAAQ;SAAC;QACpD,eAAe;IACjB;IAEA,OAAO;AACT;AAEA;;;;;;;;CAQC,GACD,sCAAsC;AACtC,SAAS,cAAc,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS;IAC1D,yBAAyB,GACzB,MAAM,QAAQ,EAAE;IAChB,MAAM,UAAU,SAAS,QAAQ,MAAM,EAAE;IAEzC,IAAI,WAAW;QACb,UAAU,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG;QAClC,MAAM,IAAI,CAAC;YAAC;YAAQ;YAAW;SAAQ;IACzC;IAEA,MAAM,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG;IAC9B,MAAM,IAAI,CAAC;QAAC;QAAQ;QAAO;KAAQ;IAEnC,IAAI,GAAG,CAAC,QAAQ,GAAG,GAAG;AACxB;AAEA;;;;CAIC,GACD,SAAS,SAAS,MAAM,EAAE,KAAK;IAC7B,MAAM,QAAQ,MAAM,CAAC,MAAM;IAC3B,MAAM,OAAO,KAAK,CAAC,EAAE,KAAK,UAAU,UAAU;IAC9C,OAAO,KAAK,CAAC,EAAE,CAAC,KAAK;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7983, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-extension-gfm-table/dev/lib/html.js"], "sourcesContent": ["/**\n * @import {HtmlExtension} from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\n\nconst alignment = {\n  none: '',\n  left: ' align=\"left\"',\n  right: ' align=\"right\"',\n  center: ' align=\"center\"'\n}\n\n// To do: micromark@5: use `infer` here, when all events are exposed.\n\n/**\n * Create an HTML extension for `micromark` to support GitHub tables when\n * serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GitHub tables when serializing to HTML.\n */\nexport function gfmTableHtml() {\n  return {\n    enter: {\n      table(token) {\n        const tableAlign = token._align\n        assert(tableAlign, 'expected `_align`')\n        this.lineEndingIfNeeded()\n        this.tag('<table>')\n        this.setData('tableAlign', tableAlign)\n      },\n      tableBody() {\n        this.tag('<tbody>')\n      },\n      tableData() {\n        const tableAlign = this.getData('tableAlign')\n        const tableColumn = this.getData('tableColumn')\n        assert(tableAlign, 'expected `tableAlign`')\n        assert(typeof tableColumn === 'number', 'expected `tableColumn`')\n        const align = alignment[tableAlign[tableColumn]]\n\n        if (align === undefined) {\n          // Capture results to ignore them.\n          this.buffer()\n        } else {\n          this.lineEndingIfNeeded()\n          this.tag('<td' + align + '>')\n        }\n      },\n      tableHead() {\n        this.lineEndingIfNeeded()\n        this.tag('<thead>')\n      },\n      tableHeader() {\n        const tableAlign = this.getData('tableAlign')\n        const tableColumn = this.getData('tableColumn')\n        assert(tableAlign, 'expected `tableAlign`')\n        assert(typeof tableColumn === 'number', 'expected `tableColumn`')\n        const align = alignment[tableAlign[tableColumn]]\n        this.lineEndingIfNeeded()\n        this.tag('<th' + align + '>')\n      },\n      tableRow() {\n        this.setData('tableColumn', 0)\n        this.lineEndingIfNeeded()\n        this.tag('<tr>')\n      }\n    },\n    exit: {\n      // Overwrite the default code text data handler to unescape escaped pipes when\n      // they are in tables.\n      codeTextData(token) {\n        let value = this.sliceSerialize(token)\n\n        if (this.getData('tableAlign')) {\n          value = value.replace(/\\\\([\\\\|])/g, replace)\n        }\n\n        this.raw(this.encode(value))\n      },\n      table() {\n        this.setData('tableAlign')\n        // Note: we don’t set `slurpAllLineEndings` anymore, in delimiter rows,\n        // but we do need to reset it to match a funky newline GH generates for\n        // list items combined with tables.\n        this.setData('slurpAllLineEndings')\n        this.lineEndingIfNeeded()\n        this.tag('</table>')\n      },\n      tableBody() {\n        this.lineEndingIfNeeded()\n        this.tag('</tbody>')\n      },\n      tableData() {\n        const tableAlign = this.getData('tableAlign')\n        const tableColumn = this.getData('tableColumn')\n        assert(tableAlign, 'expected `tableAlign`')\n        assert(typeof tableColumn === 'number', 'expected `tableColumn`')\n\n        if (tableColumn in tableAlign) {\n          this.tag('</td>')\n          this.setData('tableColumn', tableColumn + 1)\n        } else {\n          // Stop capturing.\n          this.resume()\n        }\n      },\n      tableHead() {\n        this.lineEndingIfNeeded()\n        this.tag('</thead>')\n      },\n      tableHeader() {\n        const tableColumn = this.getData('tableColumn')\n        assert(typeof tableColumn === 'number', 'expected `tableColumn`')\n        this.tag('</th>')\n        this.setData('tableColumn', tableColumn + 1)\n      },\n      tableRow() {\n        const tableAlign = this.getData('tableAlign')\n        let tableColumn = this.getData('tableColumn')\n        assert(tableAlign, 'expected `tableAlign`')\n        assert(typeof tableColumn === 'number', 'expected `tableColumn`')\n\n        while (tableColumn < tableAlign.length) {\n          this.lineEndingIfNeeded()\n          this.tag('<td' + alignment[tableAlign[tableColumn]] + '></td>')\n          tableColumn++\n        }\n\n        this.setData('tableColumn', tableColumn)\n        this.lineEndingIfNeeded()\n        this.tag('</tr>')\n      }\n    }\n  }\n}\n\n/**\n * @param {string} $0\n * @param {string} $1\n * @returns {string}\n */\nfunction replace($0, $1) {\n  // Pipes work, backslashes don’t (but can’t escape pipes).\n  return $1 === '|' ? $1 : $0\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;;AAEA,MAAM,YAAY;IAChB,MAAM;IACN,MAAM;IACN,OAAO;IACP,QAAQ;AACV;AAYO,SAAS;IACd,OAAO;QACL,OAAO;YACL,OAAM,KAAK;gBACT,MAAM,aAAa,MAAM,MAAM;gBAC/B,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,YAAY;gBACnB,IAAI,CAAC,kBAAkB;gBACvB,IAAI,CAAC,GAAG,CAAC;gBACT,IAAI,CAAC,OAAO,CAAC,cAAc;YAC7B;YACA;gBACE,IAAI,CAAC,GAAG,CAAC;YACX;YACA;gBACE,MAAM,aAAa,IAAI,CAAC,OAAO,CAAC;gBAChC,MAAM,cAAc,IAAI,CAAC,OAAO,CAAC;gBACjC,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,YAAY;gBACnB,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,OAAO,gBAAgB,UAAU;gBACxC,MAAM,QAAQ,SAAS,CAAC,UAAU,CAAC,YAAY,CAAC;gBAEhD,IAAI,UAAU,WAAW;oBACvB,kCAAkC;oBAClC,IAAI,CAAC,MAAM;gBACb,OAAO;oBACL,IAAI,CAAC,kBAAkB;oBACvB,IAAI,CAAC,GAAG,CAAC,QAAQ,QAAQ;gBAC3B;YACF;YACA;gBACE,IAAI,CAAC,kBAAkB;gBACvB,IAAI,CAAC,GAAG,CAAC;YACX;YACA;gBACE,MAAM,aAAa,IAAI,CAAC,OAAO,CAAC;gBAChC,MAAM,cAAc,IAAI,CAAC,OAAO,CAAC;gBACjC,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,YAAY;gBACnB,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,OAAO,gBAAgB,UAAU;gBACxC,MAAM,QAAQ,SAAS,CAAC,UAAU,CAAC,YAAY,CAAC;gBAChD,IAAI,CAAC,kBAAkB;gBACvB,IAAI,CAAC,GAAG,CAAC,QAAQ,QAAQ;YAC3B;YACA;gBACE,IAAI,CAAC,OAAO,CAAC,eAAe;gBAC5B,IAAI,CAAC,kBAAkB;gBACvB,IAAI,CAAC,GAAG,CAAC;YACX;QACF;QACA,MAAM;YACJ,8EAA8E;YAC9E,sBAAsB;YACtB,cAAa,KAAK;gBAChB,IAAI,QAAQ,IAAI,CAAC,cAAc,CAAC;gBAEhC,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe;oBAC9B,QAAQ,MAAM,OAAO,CAAC,cAAc;gBACtC;gBAEA,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;YACvB;YACA;gBACE,IAAI,CAAC,OAAO,CAAC;gBACb,uEAAuE;gBACvE,uEAAuE;gBACvE,mCAAmC;gBACnC,IAAI,CAAC,OAAO,CAAC;gBACb,IAAI,CAAC,kBAAkB;gBACvB,IAAI,CAAC,GAAG,CAAC;YACX;YACA;gBACE,IAAI,CAAC,kBAAkB;gBACvB,IAAI,CAAC,GAAG,CAAC;YACX;YACA;gBACE,MAAM,aAAa,IAAI,CAAC,OAAO,CAAC;gBAChC,MAAM,cAAc,IAAI,CAAC,OAAO,CAAC;gBACjC,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,YAAY;gBACnB,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,OAAO,gBAAgB,UAAU;gBAExC,IAAI,eAAe,YAAY;oBAC7B,IAAI,CAAC,GAAG,CAAC;oBACT,IAAI,CAAC,OAAO,CAAC,eAAe,cAAc;gBAC5C,OAAO;oBACL,kBAAkB;oBAClB,IAAI,CAAC,MAAM;gBACb;YACF;YACA;gBACE,IAAI,CAAC,kBAAkB;gBACvB,IAAI,CAAC,GAAG,CAAC;YACX;YACA;gBACE,MAAM,cAAc,IAAI,CAAC,OAAO,CAAC;gBACjC,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,OAAO,gBAAgB,UAAU;gBACxC,IAAI,CAAC,GAAG,CAAC;gBACT,IAAI,CAAC,OAAO,CAAC,eAAe,cAAc;YAC5C;YACA;gBACE,MAAM,aAAa,IAAI,CAAC,OAAO,CAAC;gBAChC,IAAI,cAAc,IAAI,CAAC,OAAO,CAAC;gBAC/B,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,YAAY;gBACnB,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,OAAO,gBAAgB,UAAU;gBAExC,MAAO,cAAc,WAAW,MAAM,CAAE;oBACtC,IAAI,CAAC,kBAAkB;oBACvB,IAAI,CAAC,GAAG,CAAC,QAAQ,SAAS,CAAC,UAAU,CAAC,YAAY,CAAC,GAAG;oBACtD;gBACF;gBAEA,IAAI,CAAC,OAAO,CAAC,eAAe;gBAC5B,IAAI,CAAC,kBAAkB;gBACvB,IAAI,CAAC,GAAG,CAAC;YACX;QACF;IACF;AACF;AAEA;;;;CAIC,GACD,SAAS,QAAQ,EAAE,EAAE,EAAE;IACrB,0DAA0D;IAC1D,OAAO,OAAO,MAAM,KAAK;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8119, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-extension-gfm-tagfilter/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').CompileContext} CompileContext\n * @typedef {import('micromark-util-types').HtmlExtension} HtmlExtension\n * @typedef {import('micromark-util-types').Token} Token\n */\n\n// An opening or closing tag start, followed by a case-insensitive specific tag name,\n// followed by HTML whitespace, a greater than, or a slash.\nconst reFlow =\n  /<(\\/?)(iframe|noembed|noframes|plaintext|script|style|title|textarea|xmp)(?=[\\t\\n\\f\\r />])/gi\n\n// As HTML (text) parses tags separately (and very strictly), we don’t need to be\n// global.\nconst reText = new RegExp('^' + reFlow.source, 'i')\n\n/**\n * Create an HTML extension for `micromark` to support GitHubs weird and\n * useless tagfilter when serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to support\n *   GitHubs weird and useless tagfilter when serializing to HTML.\n */\nexport function gfmTagfilterHtml() {\n  return {\n    exit: {\n      htmlFlowData(token) {\n        exitHtmlData.call(this, token, reFlow)\n      },\n      htmlTextData(token) {\n        exitHtmlData.call(this, token, reText)\n      }\n    }\n  }\n}\n\n/**\n * @this {CompileContext}\n * @param {Token} token\n * @param {RegExp} filter\n * @returns {undefined}\n */\nfunction exitHtmlData(token, filter) {\n  let value = this.sliceSerialize(token)\n\n  if (this.options.allowDangerousHtml) {\n    value = value.replace(filter, '&lt;$1$2')\n  }\n\n  this.raw(this.encode(value))\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED,qFAAqF;AACrF,2DAA2D;;;;AAC3D,MAAM,SACJ;AAEF,iFAAiF;AACjF,UAAU;AACV,MAAM,SAAS,IAAI,OAAO,MAAM,OAAO,MAAM,EAAE;AAUxC,SAAS;IACd,OAAO;QACL,MAAM;YACJ,cAAa,KAAK;gBAChB,aAAa,IAAI,CAAC,IAAI,EAAE,OAAO;YACjC;YACA,cAAa,KAAK;gBAChB,aAAa,IAAI,CAAC,IAAI,EAAE,OAAO;YACjC;QACF;IACF;AACF;AAEA;;;;;CAKC,GACD,SAAS,aAAa,KAAK,EAAE,MAAM;IACjC,IAAI,QAAQ,IAAI,CAAC,cAAc,CAAC;IAEhC,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;QACnC,QAAQ,MAAM,OAAO,CAAC,QAAQ;IAChC;IAEA,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8162, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-extension-gfm-task-list-item/dev/lib/syntax.js"], "sourcesContent": ["/**\n * @import {Extension, State, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {\n  markdownLineEnding,\n  markdownLineEndingOrSpace,\n  markdownSpace\n} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\nconst tasklistCheck = {name: 'tasklist<PERSON>heck', tokenize: tokenizeTasklistCheck}\n\n/**\n * Create an HTML extension for `micromark` to support GFM task list items\n * syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GFM task list items when serializing to HTML.\n */\nexport function gfmTaskListItem() {\n  return {\n    text: {[codes.leftSquareBracket]: tasklistCheck}\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeTasklistCheck(effects, ok, nok) {\n  const self = this\n\n  return open\n\n  /**\n   * At start of task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    assert(code === codes.leftSquareBracket, 'expected `[`')\n\n    if (\n      // Exit if there’s stuff before.\n      self.previous !== codes.eof ||\n      // Exit if not in the first content that is the first child of a list\n      // item.\n      !self._gfmTasklistFirstContentOfListItem\n    ) {\n      return nok(code)\n    }\n\n    effects.enter('taskListCheck')\n    effects.enter('taskListCheckMarker')\n    effects.consume(code)\n    effects.exit('taskListCheckMarker')\n    return inside\n  }\n\n  /**\n   * In task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    // Currently we match how GH works in files.\n    // To match how GH works in comments, use `markdownSpace` (`[\\t ]`) instead\n    // of `markdownLineEndingOrSpace` (`[\\t\\n\\r ]`).\n    if (markdownLineEndingOrSpace(code)) {\n      effects.enter('taskListCheckValueUnchecked')\n      effects.consume(code)\n      effects.exit('taskListCheckValueUnchecked')\n      return close\n    }\n\n    if (code === codes.uppercaseX || code === codes.lowercaseX) {\n      effects.enter('taskListCheckValueChecked')\n      effects.consume(code)\n      effects.exit('taskListCheckValueChecked')\n      return close\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * At close of task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function close(code) {\n    if (code === codes.rightSquareBracket) {\n      effects.enter('taskListCheckMarker')\n      effects.consume(code)\n      effects.exit('taskListCheckMarker')\n      effects.exit('taskListCheck')\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * @type {State}\n   */\n  function after(code) {\n    // EOL in paragraph means there must be something else after it.\n    if (markdownLineEnding(code)) {\n      return ok(code)\n    }\n\n    // Space or tab?\n    // Check what comes after.\n    if (markdownSpace(code)) {\n      return effects.check({tokenize: spaceThenNonSpace}, ok, nok)(code)\n    }\n\n    // EOF, or non-whitespace, both wrong.\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction spaceThenNonSpace(effects, ok, nok) {\n  return factorySpace(effects, after, types.whitespace)\n\n  /**\n   * After whitespace, after task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // EOF means there was nothing, so bad.\n    // EOL means there’s content after it, so good.\n    // Impossible to have more spaces.\n    // Anything else is good.\n    return code === codes.eof ? nok(code) : ok(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;AACA;AACA;AAKA;AAAA;;;;;AAEA,MAAM,gBAAgB;IAAC,MAAM;IAAiB,UAAU;AAAqB;AAUtE,SAAS;IACd,OAAO;QACL,MAAM;YAAC,CAAC,8JAAA,CAAA,QAAK,CAAC,iBAAiB,CAAC,EAAE;QAAa;IACjD;AACF;AAEA;;;CAGC,GACD,SAAS,sBAAsB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC7C,MAAM,OAAO,IAAI;IAEjB,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,KAAK,IAAI;QAChB,CAAA,GAAA,+IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,8JAAA,CAAA,QAAK,CAAC,iBAAiB,EAAE;QAEzC,IACE,gCAAgC;QAChC,KAAK,QAAQ,KAAK,8JAAA,CAAA,QAAK,CAAC,GAAG,IAC3B,qEAAqE;QACrE,QAAQ;QACR,CAAC,KAAK,kCAAkC,EACxC;YACA,OAAO,IAAI;QACb;QAEA,QAAQ,KAAK,CAAC;QACd,QAAQ,KAAK,CAAC;QACd,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,OAAO,IAAI;QAClB,4CAA4C;QAC5C,2EAA2E;QAC3E,gDAAgD;QAChD,IAAI,CAAA,GAAA,iKAAA,CAAA,4BAAyB,AAAD,EAAE,OAAO;YACnC,QAAQ,KAAK,CAAC;YACd,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,UAAU,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,UAAU,EAAE;YAC1D,QAAQ,KAAK,CAAC;YACd,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,IAAI,SAAS,8JAAA,CAAA,QAAK,CAAC,kBAAkB,EAAE;YACrC,QAAQ,KAAK,CAAC;YACd,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC;YACb,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;GAEC,GACD,SAAS,MAAM,IAAI;QACjB,gEAAgE;QAChE,IAAI,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,OAAO,GAAG;QACZ;QAEA,gBAAgB;QAChB,0BAA0B;QAC1B,IAAI,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,OAAO,QAAQ,KAAK,CAAC;gBAAC,UAAU;YAAiB,GAAG,IAAI,KAAK;QAC/D;QAEA,sCAAsC;QACtC,OAAO,IAAI;IACb;AACF;AAEA;;;CAGC,GACD,SAAS,kBAAkB,OAAO,EAAE,EAAE,EAAE,GAAG;IACzC,OAAO,CAAA,GAAA,gKAAA,CAAA,eAAY,AAAD,EAAE,SAAS,OAAO,8JAAA,CAAA,QAAK,CAAC,UAAU;;IAEpD;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,uCAAuC;QACvC,+CAA+C;QAC/C,kCAAkC;QAClC,yBAAyB;QACzB,OAAO,SAAS,8JAAA,CAAA,QAAK,CAAC,GAAG,GAAG,IAAI,QAAQ,GAAG;IAC7C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8310, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-extension-gfm-task-list-item/dev/lib/html.js"], "sourcesContent": ["/**\n * @import {HtmlExtension} from 'micromark-util-types'\n */\n\n/**\n * Create an HTML extension for `micromark` to support GFM task list items when\n * serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GFM task list items when serializing to HTML.\n */\nexport function gfmTaskListItemHtml() {\n  return {\n    enter: {\n      taskListCheck() {\n        this.tag('<input type=\"checkbox\" disabled=\"\" ')\n      }\n    },\n    exit: {\n      taskListCheck() {\n        this.tag('/>')\n      },\n      taskListCheckValueChecked() {\n        this.tag('checked=\"\" ')\n      }\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;;;;CAOC;;;AACM,SAAS;IACd,OAAO;QACL,OAAO;YACL;gBACE,IAAI,CAAC,GAAG,CAAC;YACX;QACF;QACA,MAAM;YACJ;gBACE,IAAI,CAAC,GAAG,CAAC;YACX;YACA;gBACE,IAAI,CAAC,GAAG,CAAC;YACX;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8345, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/micromark-extension-gfm/index.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-extension-gfm-footnote').HtmlOptions} HtmlOptions\n * @typedef {import('micromark-extension-gfm-strikethrough').Options} Options\n * @typedef {import('micromark-util-types').Extension} Extension\n * @typedef {import('micromark-util-types').HtmlExtension} HtmlExtension\n */\n\nimport {\n  combineExtensions,\n  combineHtmlExtensions\n} from 'micromark-util-combine-extensions'\nimport {\n  gfmAutolinkLiteral,\n  gfmAutolinkLiteralHtml\n} from 'micromark-extension-gfm-autolink-literal'\nimport {gfmFootnote, gfmFootnoteHtml} from 'micromark-extension-gfm-footnote'\nimport {\n  gfmStrikethrough,\n  gfmStrikethroughHtml\n} from 'micromark-extension-gfm-strikethrough'\nimport {gfmTable, gfmTableHtml} from 'micromark-extension-gfm-table'\nimport {gfmTagfilterHtml} from 'micromark-extension-gfm-tagfilter'\nimport {\n  gfmTaskListItem,\n  gfmTaskListItemHtml\n} from 'micromark-extension-gfm-task-list-item'\n\n/**\n * Create an extension for `micromark` to enable GFM syntax.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n *\n *   Passed to `micromark-extens-gfm-strikethrough`.\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to enable GFM\n *   syntax.\n */\nexport function gfm(options) {\n  return combineExtensions([\n    gfmAutolinkLiteral(),\n    gfmFootnote(),\n    gfmStrikethrough(options),\n    gfmTable(),\n    gfmTaskListItem()\n  ])\n}\n\n/**\n * Create an extension for `micromark` to support GFM when serializing to HTML.\n *\n * @param {HtmlOptions | null | undefined} [options]\n *   Configuration (optional).\n *\n *   Passed to `micromark-extens-gfm-footnote`.\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GFM when serializing to HTML.\n */\nexport function gfmHtml(options) {\n  return combineHtmlExtensions([\n    gfmAutolinkLiteralHtml(),\n    gfmFootnoteHtml(options),\n    gfmStrikethroughHtml(),\n    gfmTableHtml(),\n    gfmTagfilterHtml(),\n    gfmTaskListItemHtml()\n  ])\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAED;AAIA;AAAA;AAIA;AAAA;AACA;AAAA;AAIA;AAAA;AACA;AACA;AAAA;;;;;;;;AAgBO,SAAS,IAAI,OAAO;IACzB,OAAO,CAAA,GAAA,sKAAA,CAAA,oBAAiB,AAAD,EAAE;QACvB,CAAA,GAAA,+LAAA,CAAA,qBAAkB,AAAD;QACjB,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD;QACV,CAAA,GAAA,yLAAA,CAAA,mBAAgB,AAAD,EAAE;QACjB,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD;QACP,CAAA,GAAA,gMAAA,CAAA,kBAAe,AAAD;KACf;AACH;AAaO,SAAS,QAAQ,OAAO;IAC7B,OAAO,CAAA,GAAA,sKAAA,CAAA,wBAAqB,AAAD,EAAE;QAC3B,CAAA,GAAA,6LAAA,CAAA,yBAAsB,AAAD;QACrB,CAAA,GAAA,kLAAA,CAAA,kBAAe,AAAD,EAAE;QAChB,CAAA,GAAA,uLAAA,CAAA,uBAAoB,AAAD;QACnB,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD;QACX,CAAA,GAAA,6KAAA,CAAA,mBAAgB,AAAD;QACf,CAAA,GAAA,8LAAA,CAAA,sBAAmB,AAAD;KACnB;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8398, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/collaborative_content_editor_ko8hwk/node_modules/remark-gfm/lib/index.js"], "sourcesContent": ["/**\n * @import {Root} from 'mdast'\n * @import {Options} from 'remark-gfm'\n * @import {} from 'remark-parse'\n * @import {} from 'remark-stringify'\n * @import {Processor} from 'unified'\n */\n\nimport {gfmFromMarkdown, gfmToMarkdown} from 'mdast-util-gfm'\nimport {gfm} from 'micromark-extension-gfm'\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Add support GFM (autolink literals, footnotes, strikethrough, tables,\n * tasklists).\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nexport default function remarkGfm(options) {\n  // @ts-expect-error: TS is wrong about `this`.\n  // eslint-disable-next-line unicorn/no-this-assignment\n  const self = /** @type {Processor<Root>} */ (this)\n  const settings = options || emptyOptions\n  const data = self.data()\n\n  const micromarkExtensions =\n    data.micromarkExtensions || (data.micromarkExtensions = [])\n  const fromMarkdownExtensions =\n    data.fromMarkdownExtensions || (data.fromMarkdownExtensions = [])\n  const toMarkdownExtensions =\n    data.toMarkdownExtensions || (data.toMarkdownExtensions = [])\n\n  micromarkExtensions.push(gfm(settings))\n  fromMarkdownExtensions.push(gfmFromMarkdown())\n  toMarkdownExtensions.push(gfmToMarkdown(settings))\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AAED;AACA;;;AAEA,oBAAoB,GACpB,MAAM,eAAe,CAAC;AAWP,SAAS,UAAU,OAAO;IACvC,8CAA8C;IAC9C,sDAAsD;IACtD,MAAM,OAAuC,IAAI;IACjD,MAAM,WAAW,WAAW;IAC5B,MAAM,OAAO,KAAK,IAAI;IAEtB,MAAM,sBACJ,KAAK,mBAAmB,IAAI,CAAC,KAAK,mBAAmB,GAAG,EAAE;IAC5D,MAAM,yBACJ,KAAK,sBAAsB,IAAI,CAAC,KAAK,sBAAsB,GAAG,EAAE;IAClE,MAAM,uBACJ,KAAK,oBAAoB,IAAI,CAAC,KAAK,oBAAoB,GAAG,EAAE;IAE9D,oBAAoB,IAAI,CAAC,CAAA,GAAA,yJAAA,CAAA,MAAG,AAAD,EAAE;IAC7B,uBAAuB,IAAI,CAAC,CAAA,GAAA,uJAAA,CAAA,kBAAe,AAAD;IAC1C,qBAAqB,IAAI,CAAC,CAAA,GAAA,uJAAA,CAAA,gBAAa,AAAD,EAAE;AAC1C", "ignoreList": [0], "debugId": null}}]}