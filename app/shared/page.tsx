'use client';

import { AppLayout } from "../components/layout/AppLayout";
import { Users } from "lucide-react";

export default function SharedPage() {
  return (
    <AppLayout>
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center text-gray-500 max-w-md">
          <Users size={64} className="mx-auto mb-6 opacity-50" />
          <h2 className="text-2xl font-semibold mb-3 text-gray-900">Shared with Me</h2>
          <p className="mb-6 text-gray-600">
            Documents and scripts shared with you by other users will appear here.
          </p>
          <p className="text-sm text-gray-500">
            This feature will be implemented in a future update.
          </p>
        </div>
      </div>
    </AppLayout>
  );
}
