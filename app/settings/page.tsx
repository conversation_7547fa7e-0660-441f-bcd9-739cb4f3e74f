'use client';

import { AppLayout } from "../components/layout/AppLayout";
import { Settings } from "lucide-react";

export default function SettingsPage() {
  return (
    <AppLayout>
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center text-gray-500 max-w-md">
          <Settings size={64} className="mx-auto mb-6 opacity-50" />
          <h2 className="text-2xl font-semibold mb-3 text-gray-900">Settings</h2>
          <p className="mb-6 text-gray-600">
            Application settings and preferences will be available here.
          </p>
          <p className="text-sm text-gray-500">
            This feature will be implemented in a future update.
          </p>
        </div>
      </div>
    </AppLayout>
  );
}
