'use client';

import { useState } from "react";
import * as React from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { But<PERSON> } from "./ui/button";
import { Card, CardContent } from "./ui/card";
import { Badge } from "./ui/badge";
import { Input } from "./ui/input";
import { Textarea } from "./ui/textarea";
import {
  Plus,
  Trash2,
  Crown,
  Eye,
  Edit,
  Lock,
  AlertCircle,
  FolderOpen
} from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "./ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { useScriptUrls } from "@/hooks/useScriptRouting";
import { Id } from "../../convex/_generated/dataModel";

interface ScriptListProps {
  onSelectScript: (scriptId: Id<"scripts">) => void;
  selectedScriptId: Id<"scripts"> | null;
  onScriptCountChange?: (count: number) => void;
}

export function ScriptList({ onSelectScript, selectedScriptId, onScriptCountChange }: ScriptListProps) {
  const scripts = useQuery(api.scripts.getUserScripts);
  const canCreateResult = useQuery(api.scripts.canCreateScripts);
  const createScript = useMutation(api.scripts.createScript);
  const deleteScript = useMutation(api.scripts.deleteScript);
  const { getScriptUrl } = useScriptUrls();

  // Extract canCreate boolean for backward compatibility
  const canCreate = canCreateResult?.canCreate ?? false;

  // State for create dialog
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newScriptTitle, setNewScriptTitle] = useState("");
  const [newScriptDescription, setNewScriptDescription] = useState("");
  const [isCreating, setIsCreating] = useState(false);

  // State for delete dialog
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [scriptToDelete, setScriptToDelete] = useState<Id<"scripts"> | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const { toast } = useToast();

  // Notify parent component of script count changes
  React.useEffect(() => {
    if (scripts && onScriptCountChange) {
      onScriptCountChange(scripts.length);
    }
  }, [scripts, onScriptCountChange]);

  const handleShowCreateDialog = () => {
    setNewScriptTitle("");
    setNewScriptDescription("");
    setShowCreateDialog(true);
  };

  const handleCreateScript = async () => {
    if (!newScriptTitle.trim()) {
      toast({
        title: "Error",
        description: "Please enter a script title",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);
    try {
      const scriptId = await createScript({
        title: newScriptTitle.trim(),
        description: newScriptDescription.trim() || undefined,
      });

      toast({
        title: "Success",
        description: "Script created successfully",
      });

      setShowCreateDialog(false);
      setNewScriptTitle("");
      setNewScriptDescription("");

      // Navigate to the new script
      onSelectScript(scriptId);
    } catch (error) {
      console.error("Failed to create script:", error);
      toast({
        title: "Error",
        description: "Failed to create script. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteScript = async () => {
    if (!scriptToDelete) return;

    setIsDeleting(true);
    try {
      await deleteScript({ id: scriptToDelete });
      
      toast({
        title: "Success",
        description: "Script deleted successfully",
      });

      setShowDeleteDialog(false);
      setScriptToDelete(null);

      // If the deleted script was selected, clear selection
      if (selectedScriptId === scriptToDelete) {
        // Navigate to home or first available script
        if (scripts && scripts.length > 1) {
          const remainingScripts = scripts.filter(s => s._id !== scriptToDelete);
          if (remainingScripts.length > 0) {
            onSelectScript(remainingScripts[0]._id);
          }
        }
      }
    } catch (error) {
      console.error("Failed to delete script:", error);
      toast({
        title: "Error",
        description: "Failed to delete script. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case "owner":
        return <Crown className="h-3 w-3" />;
      case "write":
        return <Edit className="h-3 w-3" />;
      case "read":
        return <Eye className="h-3 w-3" />;
      default:
        return <Lock className="h-3 w-3" />;
    }
  };

  const getPermissionColor = (permission: string) => {
    switch (permission) {
      case "owner":
        return "default";
      case "write":
        return "secondary";
      case "read":
        return "outline";
      default:
        return "destructive";
    }
  };

  if (!scripts) {
    return (
      <div className="w-64 h-screen bg-white border-r border-gray-200 hidden md:flex md:flex-col">
        <div className="p-6 border-b border-gray-200 bg-white">
          <h2 className="text-lg font-semibold text-gray-900">Scripts</h2>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-64 h-screen bg-white border-r border-gray-200 hidden md:flex md:flex-col">
      {/* Header */}
      <div className="p-6 border-b border-gray-200 bg-white">
        {/* <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">Scripts</h2>
          {canCreate && (
            <Button
              onClick={handleShowCreateDialog}
              disabled={isCreating}
              size="sm"
              className="gap-2"
            >
              <Plus className="h-4 w-4" />
              New Script
            </Button>
          )}
        </div> */}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {canCreate === false && (
          <Card className="border-amber-200 bg-amber-50">
            <CardContent className="p-3">
              <div className="flex items-start gap-2 text-amber-800">
                <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-sm">Limited Access</p>
                  <p className="text-xs">
                    {canCreateResult?.reason === "anonymous_user"
                      ? "Anonymous users can only view shared scripts."
                      : canCreateResult?.reason === "not_authenticated"
                        ? "Please sign in to create and manage scripts."
                        : "You can view shared scripts but cannot create new ones."
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {scripts.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <FolderOpen size={48} className="mx-auto mb-4 opacity-50" />
            <p className="text-sm">No scripts available</p>
            {canCreate && (
              <p className="text-xs mt-2">Create your first script to get started</p>
            )}
          </div>
        ) : (
          <div className="space-y-3">
            {scripts.map((script) => {
              const isSelected = selectedScriptId === script._id;
              
              return (
                <a
                  key={script._id}
                  href={getScriptUrl(script._id)}
                  onClick={(e) => {
                    e.preventDefault();
                    onSelectScript(script._id);
                  }}
                  className={`block p-4 rounded-lg border transition-all duration-200 group hover:shadow-md ${
                    isSelected
                      ? 'border-blue-500 bg-blue-50 shadow-sm'
                      : 'border-gray-200 bg-white hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <h3 className={`text-sm font-medium line-clamp-2 flex-1 mr-2 ${
                      isSelected ? 'text-blue-900' : 'text-gray-900'
                    }`}>
                      {script.title}
                    </h3>
                    {script.permission === "owner" && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setScriptToDelete(script._id);
                          setShowDeleteDialog(true);
                        }}
                        className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0 text-gray-400 hover:text-red-600"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    )}
                  </div>

                  {script.description && (
                    <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                      {script.description}
                    </p>
                  )}

                  <div className="flex items-center justify-between">
                    <Badge
                      variant={getPermissionColor(script.permission) as any}
                      className="gap-1 text-xs"
                    >
                      {getPermissionIcon(script.permission)}
                      {script.permission}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {new Date(script._creationTime).toLocaleDateString()}
                    </span>
                  </div>
                </a>
              );
            })}
          </div>
        )}
      </div>

      {/* Create Script Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Script</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label htmlFor="script-title" className="text-sm font-medium">
                Title *
              </label>
              <Input
                id="script-title"
                value={newScriptTitle}
                onChange={(e) => setNewScriptTitle(e.target.value)}
                placeholder="Enter script title..."
                className="mt-1"
              />
            </div>
            <div>
              <label htmlFor="script-description" className="text-sm font-medium">
                Description
              </label>
              <Textarea
                id="script-description"
                value={newScriptDescription}
                onChange={(e) => setNewScriptDescription(e.target.value)}
                placeholder="Enter script description..."
                className="mt-1"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
              disabled={isCreating}
            >
              Cancel
            </Button>
            <Button onClick={handleCreateScript} disabled={isCreating}>
              {isCreating ? "Creating..." : "Create Script"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Script Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Script</DialogTitle>
          </DialogHeader>
          <p className="text-sm text-gray-600">
            Are you sure you want to delete this script? This will also delete all documents within the script. This action cannot be undone.
          </p>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteScript}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete Script"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
