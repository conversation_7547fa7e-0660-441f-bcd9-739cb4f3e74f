/**
 * Comment thread component showing a comment and its replies
 */

import React, { useState } from "react";
import { Card, CardContent } from "./ui/card";
import { <PERSON><PERSON> } from "./ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { Badge } from "./ui/badge";
import { Separator } from "./ui/separator";
import { CommentInput } from "./CommentInput";
import { QuotedText, CommentContent } from "./ui/expandable-text";
import { getUserColor } from "../lib/userColors";
import { 
  MoreHorizontal, 
  Reply, 
  Edit2, 
  Trash2, 
  Check, 
  RotateCcw,
  Quote
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
// Helper function to format relative time
function formatDistanceToNow(date: Date, options?: { addSuffix?: boolean }) {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return options?.addSuffix ? "just now" : "now";
  if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return options?.addSuffix ? `${minutes} minute${minutes > 1 ? 's' : ''} ago` : `${minutes}m`;
  }
  if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return options?.addSuffix ? `${hours} hour${hours > 1 ? 's' : ''} ago` : `${hours}h`;
  }
  const days = Math.floor(diffInSeconds / 86400);
  return options?.addSuffix ? `${days} day${days > 1 ? 's' : ''} ago` : `${days}d`;
}
import { Id } from "../../convex/_generated/dataModel";

interface CommentUser {
  _id: string;
  name?: string;
  email?: string;
  image?: string;
}

interface CommentReply {
  _id: string;
  commentId: string;
  userId: string;
  content: string;
  timestamp: number;
  user?: CommentUser | null;
}

interface Comment {
  _id: string;
  documentId: string;
  userId: string;
  content: string;
  position: number;
  selection: {
    from: number;
    to: number;
  };
  selectedText: string;
  isResolved?: boolean;
  timestamp: number;
  user?: CommentUser | null;
  replies?: CommentReply[];
}

interface CommentThreadProps {
  comment: Comment;
  currentUserId?: string;
  isSelected?: boolean;
  canResolve?: boolean;
  onUpdateComment: (commentId: Id<"comments">, content: string) => Promise<boolean>;
  onDeleteComment: (commentId: Id<"comments">) => Promise<boolean>;
  onCreateReply: (commentId: Id<"comments">, content: string) => Promise<any>;
  onUpdateReply: (replyId: Id<"commentReplies">, content: string) => Promise<boolean>;
  onDeleteReply: (replyId: Id<"commentReplies">) => Promise<boolean>;
  onResolveComment: (commentId: Id<"comments">, isResolved: boolean) => Promise<boolean>;
  onSelectComment: (commentId: string) => void;
}

export function CommentThread({
  comment,
  currentUserId,
  isSelected = false,
  canResolve = false,
  onUpdateComment,
  onDeleteComment,
  onCreateReply,
  onUpdateReply,
  onDeleteReply,
  onResolveComment,
  onSelectComment,
}: CommentThreadProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [showReplyInput, setShowReplyInput] = useState(false);
  const [editingReplyId, setEditingReplyId] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isOwner = currentUserId === comment.userId;
  const userName = comment.user?.name || comment.user?.email || "Unknown User";
  const userInitials = userName.split(" ").map(n => n[0]).join("").toUpperCase().slice(0, 2);
  const userColor = getUserColor(comment.userId);

  const handleEditComment = async (content: string) => {
    setIsSubmitting(true);
    try {
      const success = await onUpdateComment(comment._id as Id<"comments">, content);
      if (success) {
        setIsEditing(false);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteComment = async () => {
    if (window.confirm("Are you sure you want to delete this comment?")) {
      await onDeleteComment(comment._id as Id<"comments">);
    }
  };

  const handleCreateReply = async (content: string) => {
    setIsSubmitting(true);
    try {
      const success = await onCreateReply(comment._id as Id<"comments">, content);
      if (success) {
        setShowReplyInput(false);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditReply = async (content: string) => {
    if (!editingReplyId) return;
    
    setIsSubmitting(true);
    try {
      const success = await onUpdateReply(editingReplyId as Id<"commentReplies">, content);
      if (success) {
        setEditingReplyId(null);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteReply = async (replyId: string) => {
    if (window.confirm("Are you sure you want to delete this reply?")) {
      await onDeleteReply(replyId as Id<"commentReplies">);
    }
  };

  const handleResolveToggle = async () => {
    await onResolveComment(comment._id as Id<"comments">, !comment.isResolved);
  };

  return (
    <Card
      data-testid="comment-thread"
      data-comment-id={comment._id}
      className={`w-full transition-all duration-200 ${
        isSelected ? "ring-2 shadow-md" : ""
      } ${comment.isResolved ? "opacity-75" : ""}`}
      style={{
        borderLeftColor: `rgb(${userColor.rgb})`,
        borderLeftWidth: isSelected ? '4px' : '2px',
        ...(isSelected && {
          ringColor: `rgba(${userColor.rgb}, 0.3)`,
        })
      }}
      onClick={() => onSelectComment(comment._id)}
    >
      <CardContent className="p-4">
        {/* Comment Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={comment.user?.image} />
              <AvatarFallback
                className="text-xs"
                style={{
                  backgroundColor: `rgb(${userColor.rgb})`,
                  color: userColor.text === 'text-white' ? 'white' : 'black'
                }}
              >
                {userInitials}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="flex items-center gap-2">
                <span className="font-medium text-sm">{userName}</span>
                {comment.isResolved && (
                  <Badge variant="secondary" className="text-xs">
                    <Check className="h-3 w-3 mr-1" />
                    Resolved
                  </Badge>
                )}
              </div>
              <span className="text-xs text-muted-foreground">
                {formatDistanceToNow(new Date(comment.timestamp), { addSuffix: true })}
              </span>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                data-testid="comment-options"
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setShowReplyInput(true)}>
                <Reply className="h-4 w-4 mr-2" />
                Reply
              </DropdownMenuItem>
              {isOwner && (
                <>
                  <DropdownMenuItem onClick={() => setIsEditing(true)}>
                    <Edit2 className="h-4 w-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={handleDeleteComment}
                    className="text-destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}
              {canResolve && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleResolveToggle}>
                    {comment.isResolved ? (
                      <>
                        <RotateCcw className="h-4 w-4 mr-2" />
                        Reopen
                      </>
                    ) : (
                      <>
                        <Check className="h-4 w-4 mr-2" />
                        Resolve
                      </>
                    )}
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Original Text Quote */}
        <div className="mb-3 p-2 bg-muted rounded-md border-l-4 border-muted-foreground/20">
          <div className="flex items-center gap-1 mb-1">
            <Quote className="h-3 w-3 text-muted-foreground" />
            <span className="text-xs text-muted-foreground">Original text:</span>
          </div>
          <QuotedText maxLines={2}>
            {comment.selectedText}
          </QuotedText>
        </div>

        {/* Comment Content */}
        {isEditing ? (
          <CommentInput
            initialValue={comment.content}
            onSubmit={handleEditComment}
            onCancel={() => setIsEditing(false)}
            isSubmitting={isSubmitting}
            autoFocus
            showCancel
            submitLabel="Update"
            placeholder="Edit your comment..."
          />
        ) : (
          <div className="mb-3">
            <CommentContent maxLines={4} enableScrolling={true}>
              {comment.content}
            </CommentContent>
          </div>
        )}

        {/* Replies */}
        {comment.replies && comment.replies.length > 0 && (
          <>
            <Separator className="my-3" />
            <div className="space-y-3">
              {comment.replies.map((reply) => (
                <div key={reply._id} className="flex gap-3">
                  <Avatar className="h-6 w-6 mt-1">
                    <AvatarImage src={reply.user?.image} />
                    <AvatarFallback className="text-xs">
                      {(reply.user?.name || reply.user?.email || "U").slice(0, 1).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-xs">
                          {reply.user?.name || reply.user?.email || "Unknown User"}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {formatDistanceToNow(new Date(reply.timestamp), { addSuffix: true })}
                        </span>
                      </div>
                      {currentUserId === reply.userId && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                              <MoreHorizontal className="h-3 w-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => setEditingReplyId(reply._id)}>
                              <Edit2 className="h-3 w-3 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleDeleteReply(reply._id)}
                              className="text-destructive"
                            >
                              <Trash2 className="h-3 w-3 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                    
                    {editingReplyId === reply._id ? (
                      <div className="mt-2">
                        <CommentInput
                          initialValue={reply.content}
                          onSubmit={handleEditReply}
                          onCancel={() => setEditingReplyId(null)}
                          isSubmitting={isSubmitting}
                          autoFocus
                          showCancel
                          submitLabel="Update"
                          placeholder="Edit your reply..."
                        />
                      </div>
                    ) : (
                      <div className="mt-1">
                        <CommentContent maxLines={3} enableScrolling={true}>
                          {reply.content}
                        </CommentContent>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </>
        )}

        {/* Reply Input */}
        {showReplyInput && (
          <>
            <Separator className="my-3" />
            <CommentInput
              placeholder="Write a reply..."
              onSubmit={handleCreateReply}
              onCancel={() => setShowReplyInput(false)}
              isSubmitting={isSubmitting}
              autoFocus
              showCancel
              submitLabel="Reply"
            />
          </>
        )}
      </CardContent>
    </Card>
  );
}
