/**
 * Paginated editor component for screenplay formatting
 * Provides page-based layout with proper page breaks and formatting
 */

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { PAGE_DIMENSIONS_PX, CONTENT_AREA } from '../lib/screenplayFormatting';

interface PaginatedEditorProps {
  children: React.ReactNode;
  className?: string;
  onPageChange?: (pageNumber: number, totalPages: number) => void;
}

interface PageInfo {
  pageNumber: number;
  startOffset: number;
  endOffset: number;
  height: number;
}

export function PaginatedEditor({ 
  children, 
  className = '', 
  onPageChange 
}: PaginatedEditorProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [pages, setPages] = useState<PageInfo[]>([]);
  const [currentPage, setCurrentPage] = useState(1);

  // Calculate page breaks based on content
  const calculatePageBreaks = useCallback(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    const elements = Array.from(container.querySelectorAll('[data-screenplay-element]'));
    
    const newPages: PageInfo[] = [];
    let currentPageHeight = 0;
    let currentPageStart = 0;
    let pageNumber = 1;

    elements.forEach((element, index) => {
      const elementHeight = element.getBoundingClientRect().height;
      const elementType = element.getAttribute('data-screenplay-element');
      
      // Check if we need a page break
      if (currentPageHeight + elementHeight > CONTENT_AREA.height && currentPageHeight > 0) {
        // Finish current page
        newPages.push({
          pageNumber,
          startOffset: currentPageStart,
          endOffset: index - 1,
          height: currentPageHeight,
        });
        
        // Start new page
        pageNumber++;
        currentPageStart = index;
        currentPageHeight = elementHeight;
      } else {
        currentPageHeight += elementHeight;
      }
    });

    // Add the last page
    if (elements.length > 0) {
      newPages.push({
        pageNumber,
        startOffset: currentPageStart,
        endOffset: elements.length - 1,
        height: currentPageHeight,
      });
    }

    setPages(newPages);
    onPageChange?.(currentPage, newPages.length);
  }, [currentPage, onPageChange]);

  // Recalculate page breaks when content changes
  useEffect(() => {
    const observer = new MutationObserver(calculatePageBreaks);
    const resizeObserver = new ResizeObserver(calculatePageBreaks);
    
    if (containerRef.current) {
      observer.observe(containerRef.current, {
        childList: true,
        subtree: true,
        characterData: true,
      });
      resizeObserver.observe(containerRef.current);
    }

    // Initial calculation
    setTimeout(calculatePageBreaks, 100);

    return () => {
      observer.disconnect();
      resizeObserver.disconnect();
    };
  }, [calculatePageBreaks]);

  return (
    <div className="paginated-editor-container">
      {/* Page Navigation */}
      <div className="page-navigation sticky top-0 z-10 bg-white border-b px-4 py-2 flex items-center justify-between text-sm text-gray-600">
        <div className="flex items-center gap-4">
          <span>Page {currentPage} of {pages.length}</span>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage <= 1}
              className="px-2 py-1 border rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(Math.min(pages.length, currentPage + 1))}
              disabled={currentPage >= pages.length}
              className="px-2 py-1 border rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Next
            </button>
          </div>
        </div>
        <div className="text-xs text-gray-500">
          Industry Standard Format
        </div>
      </div>

      {/* Pages Container */}
      <div className="pages-container bg-gray-100 min-h-screen p-8">
        <div className="pages-wrapper max-w-none mx-auto">
          {pages.map((page) => (
            <div
              key={page.pageNumber}
              className={`page ${currentPage === page.pageNumber ? 'current-page' : ''}`}
              style={{
                width: `${PAGE_DIMENSIONS_PX.width}px`,
                minHeight: `${PAGE_DIMENSIONS_PX.height}px`,
                margin: '0 auto 2rem auto',
                backgroundColor: 'white',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                position: 'relative',
                padding: `${PAGE_DIMENSIONS_PX.marginTop}px ${PAGE_DIMENSIONS_PX.marginRight}px ${PAGE_DIMENSIONS_PX.marginBottom}px ${PAGE_DIMENSIONS_PX.marginLeft}px`,
              }}
            >
              {/* Page Number */}
              <div 
                className="page-number absolute text-xs text-gray-500"
                style={{
                  top: `${PAGE_DIMENSIONS_PX.marginTop / 2}px`,
                  right: `${PAGE_DIMENSIONS_PX.marginRight}px`,
                }}
              >
                {page.pageNumber}
              </div>

              {/* Page Content */}
              <div 
                className="page-content"
                style={{
                  minHeight: `${CONTENT_AREA.height}px`,
                  fontFamily: '"Courier New", monospace',
                  fontSize: '12pt',
                  lineHeight: '1.2',
                }}
              >
                {/* This will be populated by the editor content */}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Hidden content container for measurement */}
      <div
        ref={containerRef}
        className={`screenplay-content-measurer ${className}`}
        style={{
          position: 'absolute',
          left: '-9999px',
          top: '-9999px',
          width: `${CONTENT_AREA.width}px`,
          fontFamily: '"Courier New", monospace',
          fontSize: '12pt',
          lineHeight: '1.2',
          visibility: 'hidden',
        }}
      >
        {children}
      </div>

      <style jsx>{`
        .paginated-editor-container {
          height: 100%;
          overflow-y: auto;
        }

        .page {
          transition: opacity 0.2s ease;
        }

        .page:not(.current-page) {
          opacity: 0.7;
        }

        .current-page {
          opacity: 1;
          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .page-content {
          position: relative;
        }

        /* Print styles */
        @media print {
          .page-navigation {
            display: none;
          }
          
          .pages-container {
            background: white;
            padding: 0;
          }
          
          .page {
            box-shadow: none;
            margin: 0;
            page-break-after: always;
          }
          
          .page:last-child {
            page-break-after: auto;
          }
        }
      `}</style>
    </div>
  );
}

export default PaginatedEditor;
