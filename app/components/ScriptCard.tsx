import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Button } from "./ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";
import { Badge } from "./ui/badge";
import {
  Trash2,
  MoreVertical,
  Calendar,
  Users,
  FileText,
  Film
} from "lucide-react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "./ui/dialog";
import { useToast } from "../hooks/use-toast";
import { Id } from "../../convex/_generated/dataModel";

interface Script {
  _id: Id<"scripts">;
  _creationTime: number;
  title: string;
  description?: string;
  permission: "owner" | "read" | "write";
  sharedBy?: Id<"users">;
}

interface ScriptCardProps {
  script: Script;
  viewMode: "grid" | "list";
  onSelect: () => void;
  getPermissionIcon: (permission: string) => React.ReactNode;
  getPermissionColor: (permission: string) => string;
}

export function ScriptCard({
  script,
  viewMode,
  onSelect,
  getPermissionIcon,
  getPermissionColor,
}: ScriptCardProps) {
  const deleteScript = useMutation(api.scripts.deleteScript);
  const { toast } = useToast();

  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDeleteScript = async () => {
    setIsDeleting(true);
    try {
      await deleteScript({ id: script._id });
      toast({
        title: "Success",
        description: "Script deleted successfully",
      });
      setShowDeleteDialog(false);
    } catch (error) {
      console.error("Failed to delete script:", error);
      toast({
        title: "Error",
        description: "Failed to delete script. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return "Today";
    } else if (diffInDays === 1) {
      return "Yesterday";
    } else if (diffInDays < 7) {
      return `${diffInDays} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  if (viewMode === "list") {
    return (
      <>
        <Card className="hover:shadow-md transition-all duration-200 cursor-pointer group">
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3 sm:gap-4 flex-1 min-w-0" onClick={onSelect}>
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Film className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                  </div>
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-semibold text-gray-900 truncate">{script.title}</h3>
                    <Badge
                      variant={getPermissionColor(script.permission) as any}
                      className="gap-1 text-xs flex-shrink-0"
                    >
                      {getPermissionIcon(script.permission)}
                      {script.permission}
                    </Badge>
                  </div>
                  
                  {script.description && (
                    <p className="text-sm text-gray-600 line-clamp-1 mb-1">
                      {script.description}
                    </p>
                  )}
                  
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {formatDate(script._creationTime)}
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      Collaborative
                    </div>
                  </div>
                </div>
              </div>
              
              {script.permission === "owner" && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowDeleteDialog(true);
                  }}
                  className="opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0 text-gray-400 hover:text-red-600 flex-shrink-0"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Delete Dialog */}
        <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Script</DialogTitle>
            </DialogHeader>
            <p className="text-sm text-gray-600">
              Are you sure you want to delete "{script.title}"? This will also delete all documents within the script. This action cannot be undone.
            </p>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowDeleteDialog(false)}
                disabled={isDeleting}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteScript}
                disabled={isDeleting}
              >
                {isDeleting ? "Deleting..." : "Delete Script"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </>
    );
  }

  // Grid view
  return (
    <>
      <Card className="hover:shadow-lg transition-all duration-200 cursor-pointer group h-full">
        <CardHeader className="pb-2 sm:pb-3 p-4 sm:p-6">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0" onClick={onSelect}>
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-2 sm:mb-3">
                <Film className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
              </div>
              <CardTitle className="text-base sm:text-lg line-clamp-2 mb-2">{script.title}</CardTitle>
            </div>
            {script.permission === "owner" && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowDeleteDialog(true);
                }}
                className="opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0 text-gray-400 hover:text-red-600 flex-shrink-0"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        </CardHeader>

        <CardContent className="pt-0 p-4 sm:p-6" onClick={onSelect}>
          {script.description && (
            <p className="text-sm text-gray-600 line-clamp-3 mb-4">
              {script.description}
            </p>
          )}
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Badge
                variant={getPermissionColor(script.permission) as any}
                className="gap-1 text-xs"
              >
                {getPermissionIcon(script.permission)}
                {script.permission}
              </Badge>
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <Users className="h-3 w-3" />
                <span>Collaborative</span>
              </div>
            </div>
            
            <div className="flex items-center gap-1 text-xs text-gray-500">
              <Calendar className="h-3 w-3" />
              <span>{formatDate(script._creationTime)}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Delete Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Script</DialogTitle>
          </DialogHeader>
          <p className="text-sm text-gray-600">
            Are you sure you want to delete "{script.title}"? This will also delete all documents within the script. This action cannot be undone.
          </p>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteScript}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete Script"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
