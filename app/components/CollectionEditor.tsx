import { useQuery } from 'convex/react'
import { useBlockNoteSync } from "@convex-dev/prosemirror-sync/blocknote";
import { BlockNoteView } from "@blocknote/mantine";
import "@blocknote/core/fonts/inter.css";
import "@blocknote/mantine/style.css";
import {
  BasicTextStyleButton,
  BlockTypeSelect,
  ColorStyleButton,
  CreateLinkButton,
  FileCaptionButton,
  FileReplaceButton,
  FormattingToolbar,
  FormattingToolbarController,
  NestBlockButton,
  TextAlignButton,
  UnnestBlockButton,
  SideMenuController,
  SideMenu,
  AddBlockButton,
  DragHandleButton,
} from "@blocknote/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { useState, useEffect, useCallback } from "react";
import { useCollaborativeCursors } from "../hooks/useCollaborativeCursors";
import { createCollaborativeCursorsPlugin } from "../lib/collaborativeCursors";
import { useComments } from "../hooks/useComments";
import { createCommentPlugin } from "../lib/commentPlugin";
import { CommentSidebar } from "./CommentSidebar";
import { CommentPopover } from "./CommentPopover";
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Heading1,
  Heading2,
  Heading3,
  Link,
  Undo,
  Redo,
  Lock,
  MessageSquare,
  MessageSquarePlus,
  FolderOpen,
  Tag,
  Archive
} from "lucide-react";
import { DocumentHeader } from "./DocumentHeader";
import { Button } from "./ui/button";
import { ScrollArea } from "./ui/scroll-area";

interface CollectionEditorProps {
  documentId: Id<"documents">;
}

export function CollectionEditor({ documentId }: CollectionEditorProps) {
  const sync = useBlockNoteSync(api.prosemirror, documentId);
  const permission = useQuery(api.sharing.getDocumentPermission, { documentId });
  const canCreate = useQuery(api.documents.canCreateDocuments);
  const currentUser = useQuery(api.auth.loggedInUser);

  // Check if user has write permission for the editor
  const isReadOnly = permission?.permission === "read";

  // Comment system state
  const [showCommentSidebar, setShowCommentSidebar] = useState(false);
  const [commentPopover, setCommentPopover] = useState<{
    isVisible: boolean;
    position: { x: number; y: number };
    selectedText: string;
  }>({
    isVisible: false,
    position: { x: 0, y: 0 },
    selectedText: "",
  });

  // Collaborative cursors
  const { cursors } = useCollaborativeCursors({
    documentId,
    editor: sync.editor,
    isReadOnly,
  });

  // Comments
  const {
    comments,
    createComment,
    updateComment,
    deleteComment,
    createReply,
    updateReply,
    deleteReply,
    resolveComment,
  } = useComments({
    documentId,
    editor: sync.editor,
    isReadOnly,
  });

  // Handle text selection for comments
  const handleTextSelection = useCallback((selectedText: string, position: { x: number; y: number }) => {
    if (selectedText.trim() && !isReadOnly) {
      setCommentPopover({
        isVisible: true,
        position,
        selectedText: selectedText.trim(),
      });
    } else {
      setCommentPopover(prev => ({ ...prev, isVisible: false }));
    }
  }, [isReadOnly]);

  // Handle comment creation
  const handleCreateComment = useCallback(async (content: string) => {
    if (!sync.editor || !currentUser) return;

    const view = sync.editor._tiptapEditor.view;
    const { from, to } = view.state.selection;
    const selectedText = view.state.doc.textBetween(from, to);

    try {
      await createComment({
        content,
        position: from,
        selection: { from, to },
        selectedText,
      });

      setCommentPopover(prev => ({ ...prev, isVisible: false }));
    } catch (error) {
      console.error("Failed to create comment:", error);
    }
  }, [sync.editor, currentUser, createComment]);

  // Setup collaborative cursors plugin
  useEffect(() => {
    if (sync.editor && cursors.length > 0) {
      const plugin = createCollaborativeCursorsPlugin(cursors, currentUser?._id);
      // Note: Plugin integration would need to be added to BlockNote configuration
    }
  }, [sync.editor, cursors, currentUser]);

  // Setup comment plugin
  useEffect(() => {
    if (sync.editor && comments.length > 0) {
      const plugin = createCommentPlugin(
        comments,
        currentUser?._id,
        handleTextSelection,
        () => setShowCommentSidebar(true)
      );
      // Note: Plugin integration would need to be added to BlockNote configuration
    }
  }, [sync.editor, comments, currentUser, handleTextSelection]);

  if (!sync.editor) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col h-full relative">
      <DocumentHeader 
        documentId={documentId}
        permission={permission}
        canCreate={canCreate}
      />

      <div className="flex-1 flex relative">
        {/* Main Editor Area */}
        <div className="flex-1 flex flex-col">
          {/* Collection-specific Toolbar */}
          <div className="border-b bg-white px-6 py-3">
            <div className="flex items-center gap-2 text-sm">
              <span className="font-medium text-gray-700">Collection Tools:</span>
              <div className="flex items-center gap-4">
                <Button variant="ghost" size="sm" className="gap-2">
                  <Tag className="h-4 w-4" />
                  Add Tag
                </Button>
                <Button variant="ghost" size="sm" className="gap-2">
                  <Archive className="h-4 w-4" />
                  Archive
                </Button>
                <Button variant="ghost" size="sm" className="gap-2">
                  <FolderOpen className="h-4 w-4" />
                  Organize
                </Button>
              </div>
              
              {/* Comment Toggle */}
              <div className="ml-auto">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowCommentSidebar(!showCommentSidebar)}
                  className="gap-2"
                >
                  <MessageSquare className="h-4 w-4" />
                  Comments ({comments.length})
                </Button>
              </div>
            </div>
          </div>

          {/* Editor */}
          <ScrollArea className="h-[calc(100vh-300px)] min-h-[500px] editor-scroll-area">
            <div className="p-6">
              <BlockNoteView
                editor={sync.editor}
                theme="light"
                className="collection-editor prose prose-sm max-w-none"
                editable={!isReadOnly}
                formattingToolbar={false}
                sideMenu={isReadOnly}
              >
                {!isReadOnly && sync.editor && (
                  <>
                    <FormattingToolbarController
                      key="collection-formatting-toolbar"
                      formattingToolbar={() => (
                        <FormattingToolbar>
                          <BlockTypeSelect key={"blockTypeSelect"} />

                          <FileCaptionButton key={"fileCaptionButton"} />
                          <FileReplaceButton key={"replaceFileButton"} />

                          <BasicTextStyleButton
                            key={"boldStyleButton"}
                            basicTextStyle={"bold"}
                          >
                            <Bold className="h-4 w-4" />
                          </BasicTextStyleButton>
                          <BasicTextStyleButton
                            key={"italicStyleButton"}
                            basicTextStyle={"italic"}
                          >
                            <Italic className="h-4 w-4" />
                          </BasicTextStyleButton>
                          <BasicTextStyleButton
                            key={"underlineStyleButton"}
                            basicTextStyle={"underline"}
                          >
                            <Underline className="h-4 w-4" />
                          </BasicTextStyleButton>

                          <TextAlignButton
                            key={"textAlignButton"}
                            textAlignment={"left"}
                          />

                          <ColorStyleButton key={"colorStyleButton"} />

                          <NestBlockButton key={"nestBlockButton"}>
                            <List className="h-4 w-4" />
                          </NestBlockButton>
                          <UnnestBlockButton key={"unnestBlockButton"}>
                            <ListOrdered className="h-4 w-4" />
                          </UnnestBlockButton>

                          <CreateLinkButton key={"createLinkButton"}>
                            <Link className="h-4 w-4" />
                          </CreateLinkButton>

                          {/* Comment button for selected text */}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              const view = sync.editor._tiptapEditor.view;
                              const { from, to } = view.state.selection;
                              if (from !== to) {
                                const selectedText = view.state.doc.textBetween(from, to);
                                const coords = view.coordsAtPos(from);
                                handleTextSelection(selectedText, { x: coords.left, y: coords.top });
                              }
                            }}
                            className="gap-2"
                          >
                            <MessageSquarePlus className="h-4 w-4" />
                          </Button>
                        </FormattingToolbar>
                      )}
                    />

                    <SideMenuController
                      key="collection-side-menu"
                      sideMenu={(props) => (
                        <SideMenu {...props}>
                          <AddBlockButton {...props} />
                          <DragHandleButton {...props} />
                        </SideMenu>
                      )}
                    />
                  </>
                )}
              </BlockNoteView>
            </div>
          </ScrollArea>
        </div>

        {/* Comment Sidebar */}
        {showCommentSidebar && (
          <CommentSidebar
            documentId={documentId}
            comments={comments}
            currentUser={currentUser}
            onClose={() => setShowCommentSidebar(false)}
            onCreateComment={createComment}
            onUpdateComment={updateComment}
            onDeleteComment={deleteComment}
            onCreateReply={createReply}
            onUpdateReply={updateReply}
            onDeleteReply={deleteReply}
            onResolveComment={resolveComment}
          />
        )}

        {/* Comment Popover */}
        {commentPopover.isVisible && (
          <CommentPopover
            position={commentPopover.position}
            selectedText={commentPopover.selectedText}
            onCreateComment={handleCreateComment}
            onClose={() => setCommentPopover(prev => ({ ...prev, isVisible: false }))}
          />
        )}
      </div>

      <style jsx>{`
        .collection-editor {
          /* Collection-specific styling */
        }
        
        .collection-editor .note-item {
          border: 1px solid #e5e7eb;
          border-radius: 0.5rem;
          padding: 1rem;
          margin: 0.5rem 0;
          background: #f9fafb;
        }
        
        .collection-editor .tag {
          display: inline-block;
          background: #dbeafe;
          color: #1e40af;
          padding: 0.25rem 0.5rem;
          border-radius: 0.25rem;
          font-size: 0.75rem;
          margin: 0.125rem;
        }
      `}</style>
    </div>
  );
}
