'use client';

import { useState } from "react";
import * as React from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Button } from "./ui/button";
import { Card, CardContent } from "./ui/card";
import { Badge } from "./ui/badge";
import { Input } from "./ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import {
  Plus,
  FileText,
  Trash2,
  Crown,
  Eye,
  Edit,
  Lock,
  AlertCircle,
  Film,
  BookOpen,
  FolderOpen
} from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "./ui/dialog";
import { useToast } from "../hooks/use-toast";
import { useScriptUrls } from "../hooks/useScriptRouting";
import { Id } from "../../convex/_generated/dataModel";

interface ScriptDocumentListProps {
  scriptId: Id<"scripts">;
  onSelectDocument: (scriptId: Id<"scripts">, documentId: Id<"documents">) => void;
  selectedDocumentId: Id<"documents"> | null;
  onDocumentCountChange?: (count: number) => void;
}

type DocumentType = "research" | "screenplay" | "collection";

export function ScriptDocumentList({ 
  scriptId, 
  onSelectDocument, 
  selectedDocumentId, 
  onDocumentCountChange 
}: ScriptDocumentListProps) {
  const documents = useQuery(api.documents.getScriptDocuments, { scriptId });
  const scriptPermission = useQuery(api.scriptSharing.getScriptPermission, { scriptId });
  const createDocument = useMutation(api.documents.createDocument);
  const deleteDocument = useMutation(api.documents.deleteDocument);
  const { getScriptDocumentUrl } = useScriptUrls();

  // Check if user can create documents in this script
  const canCreate = scriptPermission?.permission === "owner" || scriptPermission?.permission === "write";

  // State for create dialog
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newDocumentTitle, setNewDocumentTitle] = useState("");
  const [newDocumentType, setNewDocumentType] = useState<DocumentType>("research");
  const [isCreating, setIsCreating] = useState(false);

  // State for delete dialog
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<Id<"documents"> | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const { toast } = useToast();

  // Notify parent component of document count changes
  React.useEffect(() => {
    if (documents && onDocumentCountChange) {
      onDocumentCountChange(documents.length);
    }
  }, [documents, onDocumentCountChange]);

  const handleShowCreateDialog = () => {
    setNewDocumentTitle("");
    setNewDocumentType("research");
    setShowCreateDialog(true);
  };

  const handleCreateDocument = async () => {
    if (!newDocumentTitle.trim()) {
      toast({
        title: "Error",
        description: "Please enter a document title",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);
    try {
      const documentId = await createDocument({
        title: newDocumentTitle.trim(),
        scriptId,
        documentType: newDocumentType,
      });

      toast({
        title: "Success",
        description: "Document created successfully",
      });

      setShowCreateDialog(false);
      setNewDocumentTitle("");
      setNewDocumentType("research");

      // Navigate to the new document
      onSelectDocument(scriptId, documentId);
    } catch (error) {
      console.error("Failed to create document:", error);
      toast({
        title: "Error",
        description: "Failed to create document. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteDocument = async () => {
    if (!documentToDelete) return;

    setIsDeleting(true);
    try {
      await deleteDocument({ id: documentToDelete });
      
      toast({
        title: "Success",
        description: "Document deleted successfully",
      });

      setShowDeleteDialog(false);
      setDocumentToDelete(null);

      // If the deleted document was selected, clear selection
      if (selectedDocumentId === documentToDelete) {
        // Navigate to first available document or script
        if (documents && documents.length > 1) {
          const remainingDocuments = documents.filter(d => d._id !== documentToDelete);
          if (remainingDocuments.length > 0) {
            onSelectDocument(scriptId, remainingDocuments[0]._id);
          }
        }
      }
    } catch (error) {
      console.error("Failed to delete document:", error);
      toast({
        title: "Error",
        description: "Failed to delete document. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const getDocumentTypeIcon = (type: string) => {
    switch (type) {
      case "screenplay":
        return <Film className="h-3 w-3" />;
      case "collection":
        return <FolderOpen className="h-3 w-3" />;
      case "research":
      default:
        return <BookOpen className="h-3 w-3" />;
    }
  };

  const getDocumentTypeColor = (type: string) => {
    switch (type) {
      case "screenplay":
        return "default";
      case "collection":
        return "secondary";
      case "research":
      default:
        return "outline";
    }
  };

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case "owner":
        return <Crown className="h-3 w-3" />;
      case "write":
        return <Edit className="h-3 w-3" />;
      case "read":
        return <Eye className="h-3 w-3" />;
      default:
        return <Lock className="h-3 w-3" />;
    }
  };

  const getPermissionColor = (permission: string) => {
    switch (permission) {
      case "owner":
        return "default";
      case "write":
        return "secondary";
      case "read":
        return "outline";
      default:
        return "destructive";
    }
  };

  if (!documents) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-gray-200 bg-white">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">Documents</h2>
          {canCreate && (
            <Button
              onClick={handleShowCreateDialog}
              disabled={isCreating}
              size="sm"
              className="gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Document
            </Button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        {!canCreate && (
          <Card className="border-amber-200 bg-amber-50 mb-4">
            <CardContent className="p-3">
              <div className="flex items-start gap-2 text-amber-800">
                <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-sm">Read-Only Access</p>
                  <p className="text-xs">
                    You can view documents but cannot create or modify them.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {documents.length === 0 ? (
          <div className="text-center py-12 text-gray-500">
            <FileText size={48} className="mx-auto mb-4 opacity-50" />
            <p className="text-sm">No documents in this script</p>
            {canCreate && (
              <p className="text-xs mt-2">Add your first document to get started</p>
            )}
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {documents.map((document) => {
              const isSelected = selectedDocumentId === document._id;
              
              return (
                <a
                  key={document._id}
                  href={getScriptDocumentUrl(scriptId, document._id)}
                  onClick={(e) => {
                    e.preventDefault();
                    onSelectDocument(scriptId, document._id);
                  }}
                  className={`block p-4 rounded-lg border transition-all duration-200 group hover:shadow-md ${
                    isSelected
                      ? 'border-blue-500 bg-blue-50 shadow-sm'
                      : 'border-gray-200 bg-white hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start justify-between mb-3">
                    <h3 className={`text-sm font-medium line-clamp-2 flex-1 mr-2 ${
                      isSelected ? 'text-blue-900' : 'text-gray-900'
                    }`}>
                      {document.title}
                    </h3>
                    {document.permission === "owner" && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setDocumentToDelete(document._id);
                          setShowDeleteDialog(true);
                        }}
                        className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0 text-gray-400 hover:text-red-600"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    )}
                  </div>

                  <div className="flex items-center justify-between mb-2">
                    <Badge
                      variant={getDocumentTypeColor(document.documentType) as any}
                      className="gap-1 text-xs"
                    >
                      {getDocumentTypeIcon(document.documentType)}
                      {document.documentType}
                    </Badge>
                    <Badge
                      variant={getPermissionColor(document.permission) as any}
                      className="gap-1 text-xs"
                    >
                      {getPermissionIcon(document.permission)}
                      {document.permission}
                    </Badge>
                  </div>

                  <div className="text-xs text-muted-foreground">
                    {new Date(document._creationTime).toLocaleDateString()}
                  </div>
                </a>
              );
            })}
          </div>
        )}
      </div>

      {/* Create Document Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Document</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label htmlFor="document-title" className="text-sm font-medium">
                Title *
              </label>
              <Input
                id="document-title"
                value={newDocumentTitle}
                onChange={(e) => setNewDocumentTitle(e.target.value)}
                placeholder="Enter document title..."
                className="mt-1"
              />
            </div>
            <div>
              <label htmlFor="document-type" className="text-sm font-medium">
                Document Type *
              </label>
              <Select value={newDocumentType} onValueChange={(value: DocumentType) => setNewDocumentType(value)}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select document type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="research">
                    <div className="flex items-center gap-2">
                      <BookOpen className="h-4 w-4" />
                      Research - Rich text editor
                    </div>
                  </SelectItem>
                  <SelectItem value="screenplay">
                    <div className="flex items-center gap-2">
                      <Film className="h-4 w-4" />
                      Screenplay - Industry format
                    </div>
                  </SelectItem>
                  <SelectItem value="collection">
                    <div className="flex items-center gap-2">
                      <FolderOpen className="h-4 w-4" />
                      Collection - Note organizer
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
              disabled={isCreating}
            >
              Cancel
            </Button>
            <Button onClick={handleCreateDocument} disabled={isCreating}>
              {isCreating ? "Creating..." : "Create Document"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Document Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Document</DialogTitle>
          </DialogHeader>
          <p className="text-sm text-gray-600">
            Are you sure you want to delete this document? This action cannot be undone.
          </p>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteDocument}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete Document"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
