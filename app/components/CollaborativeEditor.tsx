import { useQuery } from 'convex/react'
import { useBlockNoteSync } from "@convex-dev/prosemirror-sync/blocknote";
import { BlockNoteView } from "@blocknote/mantine";
import "@blocknote/core/fonts/inter.css";
import "@blocknote/mantine/style.css";
import {
  BasicTextStyleButton,
  BlockTypeSelect,
  ColorStyleButton,
  CreateLinkButton,
  FileCaptionButton,
  FileReplaceButton,
  FormattingToolbar,
  FormattingToolbarController,
  NestBlockButton,
  TextAlignButton,
  UnnestBlockButton,
  useComponentsContext,
  SideMenuController,
  SideMenu,
  AddBlockButton,
  DragHandleButton,
} from "@blocknote/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { useState, useEffect, useCallback } from "react";
import { useCollaborativeCursors } from "../hooks/useCollaborativeCursors";
import { createCollaborativeCursorsPlugin } from "../lib/collaborativeCursors";
import { useComments } from "../hooks/useComments";
import { createCommentPlugin } from "../lib/commentPlugin";
import { CommentSidebar } from "./CommentSidebar";
import { CommentPopover } from "./CommentPopover";
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Heading1,
  Heading2,
  Heading3,
  Link,
  Undo,
  Redo,
  Lock,
  MessageSquare,
  MessageSquarePlus
} from "lucide-react";
import { DocumentHeader } from "./DocumentHeader";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "./ui/dialog";
import { Separator } from "./ui/separator";
import { Card } from "./ui/card";
import { ScrollArea } from "./ui/scroll-area";

interface CollaborativeEditorProps {
  documentId: Id<"documents">;
}

export function CollaborativeEditor({ documentId }: CollaborativeEditorProps) {
  const sync = useBlockNoteSync(api.prosemirror, documentId);
  const permission = useQuery(api.sharing.getDocumentPermission, { documentId });
  const canCreate = useQuery(api.documents.canCreateDocuments);
  const currentUser = useQuery(api.auth.loggedInUser);

  // Check if user has write permission for the editor
  const isReadOnly = permission?.permission === "read";

  // Comment system state
  const [showCommentSidebar, setShowCommentSidebar] = useState(false);
  const [commentPopover, setCommentPopover] = useState<{
    isVisible: boolean;
    position: { x: number; y: number };
    selectedText: string;
  }>({
    isVisible: false,
    position: { x: 0, y: 0 },
    selectedText: "",
  });

  // Initialize collaborative cursors
  useCollaborativeCursors({
    documentId,
    editor: sync.editor,
    isReadOnly,
  });

  // Initialize comment system
  const comments = useComments({
    documentId,
    editor: sync.editor,
    isReadOnly,
  });

  // Expose debug functions to window for testing (development only)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      (window as any).debugComments = {
        forceRefresh: comments.forceRefreshDecorations,
        debugState: comments.debugCommentState,
        getState: comments.getPluginState,
      };
    }
  }, [comments]);

  // Add collaborative cursors and comment plugins to the editor
  useEffect(() => {
    if (!sync.editor?.prosemirrorView) return;

    const view = sync.editor.prosemirrorView;
    const state = view.state;

    // Check if plugins are already added
    const hasCollaborativeCursorsPlugin = state.plugins.some(
      plugin => (plugin as any).key?.toString().includes("collaborative-cursors")
    );
    const hasCommentPlugin = state.plugins.some(
      plugin => (plugin as any).key?.toString().includes("comments")
    );

    const pluginsToAdd = [];

    if (!hasCollaborativeCursorsPlugin) {
      pluginsToAdd.push(createCollaborativeCursorsPlugin());
    }

    if (!hasCommentPlugin) {
      pluginsToAdd.push(createCommentPlugin());
    }

    if (pluginsToAdd.length > 0) {
      const newState = state.reconfigure({
        plugins: [...state.plugins, ...pluginsToAdd]
      });
      view.updateState(newState);
    }
  }, [sync.editor]);



  // Handle adding comment from toolbar button
  const handleAddComment = useCallback(() => {
    if (isReadOnly || !sync.editor?.prosemirrorView) return;

    const selection = sync.editor.prosemirrorView.state.selection;
    if (!selection || selection.empty) return;

    const selectedText = sync.editor.prosemirrorView.state.doc.textBetween(
      selection.from,
      selection.to
    );

    // Position the popover near the editor but not covering the selected text
    const editorElement = sync.editor.prosemirrorView.dom;
    const editorRect = editorElement.getBoundingClientRect();

    setCommentPopover({
      isVisible: true,
      position: {
        x: editorRect.right - 400, // Position to the right of the editor
        y: editorRect.top + 100    // A bit down from the top
      },
      selectedText,
    });
  }, [isReadOnly, sync.editor]);

  const handleCloseCommentPopover = () => {
    setCommentPopover(prev => ({ ...prev, isVisible: false }));
  };

  // Custom Add Comment Button for the formatting toolbar
  function AddCommentButton() {
    const Components = useComponentsContext()!;

    return (
      <Components.FormattingToolbar.Button
        className="bg-blue-600 hover:bg-blue-700 text-white"
        onClick={handleAddComment}
        mainTooltip="Add Comment to Selection"
      >
        <MessageSquarePlus size={16} />
      </Components.FormattingToolbar.Button>
    );
  }

  if (sync.isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-2 text-muted-foreground">Loading editor...</span>
      </div>
    );
  }

  if (!sync.editor) {
    // Check if user has permission to access this specific document
    if (permission === null) {
      return (
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <div className="flex items-center gap-2 text-muted-foreground">
            <Lock className="h-6 w-6" />
            <span className="text-lg">Document Not Found</span>
          </div>
          <div className="text-center space-y-2">
            <p className="text-muted-foreground">
              This document doesn't exist or you don't have permission to access it.
            </p>
          </div>
        </div>
      );
    }

    // Check if user has permission to create/edit documents
    if (canCreate?.canCreate === false) {
      return (
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <div className="flex items-center gap-2 text-muted-foreground">
            <Lock className="h-6 w-6" />
            <span className="text-lg">Access Restricted</span>
          </div>
          <div className="text-center space-y-2">
            <p className="text-muted-foreground">
              You don't have permission to create documents.
            </p>
            <p className="text-sm text-muted-foreground">
              Anonymous users cannot create documents. Please sign up for an account to create and edit documents.
            </p>
          </div>
        </div>
      );
    }

    // Auto-initialize the document content if user has permission
    if (permission && (permission.permission === "owner" || permission.permission === "write")) {
      // Create the initial document content automatically
      sync.create({
        type: "doc",
        content: []
      });

      // Show loading state while the document is being initialized
      return (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2 text-muted-foreground">Initializing document...</span>
        </div>
      );
    }

    // Show message for read-only users when document content doesn't exist
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <div className="flex items-center gap-2 text-muted-foreground">
          <Lock className="h-6 w-6" />
          <span className="text-lg">Document Empty</span>
        </div>
        <div className="text-center space-y-2">
          <p className="text-muted-foreground">
            This document has no content yet and you have read-only access.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full space-y-4">
      <DocumentHeader documentId={documentId} />
      <Card>
        {!isReadOnly && (
          <EditorToolbar
            editor={sync.editor}
            showCommentSidebar={showCommentSidebar}
            onToggleCommentSidebar={() => setShowCommentSidebar(!showCommentSidebar)}
          />
        )}
        {isReadOnly && (
          <div className="flex items-center gap-2 p-3 border-b bg-muted/30">
            <Lock className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              You have read-only access to this document
            </span>
          </div>
        )}
        <ScrollArea className="h-[calc(100vh-300px)] min-h-[500px] editor-scroll-area">
          <div className="p-6">
            <BlockNoteView
              editor={sync.editor}
              theme="light"
              className="prose prose-sm max-w-none"
              editable={!isReadOnly}
              formattingToolbar={false}
              sideMenu={isReadOnly}
            >
              {!isReadOnly && sync.editor && (
                <>
                  <FormattingToolbarController
                    key="custom-formatting-toolbar"
                    formattingToolbar={() => (
                      <FormattingToolbar>
                        <BlockTypeSelect key={"blockTypeSelect"} />

                        <FileCaptionButton key={"fileCaptionButton"} />
                        <FileReplaceButton key={"replaceFileButton"} />

                        <BasicTextStyleButton
                          basicTextStyle={"bold"}
                          key={"boldStyleButton"}
                        />
                        <BasicTextStyleButton
                          basicTextStyle={"italic"}
                          key={"italicStyleButton"}
                        />
                        <BasicTextStyleButton
                          basicTextStyle={"underline"}
                          key={"underlineStyleButton"}
                        />
                        <BasicTextStyleButton
                          basicTextStyle={"strike"}
                          key={"strikeStyleButton"}
                        />
                        <BasicTextStyleButton
                          basicTextStyle={"code"}
                          key={"codeStyleButton"}
                        />

                        <TextAlignButton
                          textAlignment={"left"}
                          key={"textAlignLeftButton"}
                        />
                        <TextAlignButton
                          textAlignment={"center"}
                          key={"textAlignCenterButton"}
                        />
                        <TextAlignButton
                          textAlignment={"right"}
                          key={"textAlignRightButton"}
                        />

                        <ColorStyleButton key={"colorStyleButton"} />

                        <NestBlockButton key={"nestBlockButton"} />
                        <UnnestBlockButton key={"unnestBlockButton"} />

                        <CreateLinkButton key={"createLinkButton"} />

                        {/* Custom Add Comment Button */}
                        <AddCommentButton key={"addCommentButton"} />
                      </FormattingToolbar>
                    )}
                  />
                  <SideMenuController
                    key="custom-side-menu"
                    sideMenu={(props) => (
                      <SideMenu {...props}>
                        <AddBlockButton {...props} />
                        <DragHandleButton {...props} />
                      </SideMenu>
                    )}
                  />
                </>
              )}
            </BlockNoteView>
          </div>
        </ScrollArea>
      </Card>

      {/* Comment Popover */}
      <CommentPopover
        isVisible={commentPopover.isVisible}
        position={commentPopover.position}
        selectedText={commentPopover.selectedText}
        onCreateComment={comments.createComment}
        onClose={handleCloseCommentPopover}
      />

      {/* Comment Sidebar */}
      <CommentSidebar
        comments={comments.comments}
        currentUserId={currentUser?._id}
        selectedCommentId={comments.getPluginState()?.selectedCommentId}
        isVisible={showCommentSidebar}
        canResolve={!isReadOnly}
        onClose={() => setShowCommentSidebar(false)}
        onCreateComment={comments.createComment}
        onUpdateComment={comments.updateComment}
        onDeleteComment={comments.deleteComment}
        onCreateReply={comments.createReply}
        onUpdateReply={comments.updateReply}
        onDeleteReply={comments.deleteReply}
        onResolveComment={comments.resolveComment}
        onSelectComment={comments.selectComment}
      />
    </div>
  );
}

function EditorToolbar({
  editor,
  showCommentSidebar,
  onToggleCommentSidebar
}: {
  editor: any;
  showCommentSidebar: boolean;
  onToggleCommentSidebar: () => void;
}) {
  const [showLinkDialog, setShowLinkDialog] = useState(false);
  const [linkUrl, setLinkUrl] = useState("");

  const handleBold = () => {
    editor.focus();
    editor.commands.toggleBold();
  };

  const handleItalic = () => {
    editor.focus();
    editor.commands.toggleItalic();
  };

  const handleUnderline = () => {
    editor.focus();
    editor.commands.toggleUnderline();
  };

  const handleBulletList = () => {
    editor.focus();
    editor.commands.toggleBulletList();
  };

  const handleOrderedList = () => {
    editor.focus();
    editor.commands.toggleOrderedList();
  };

  const handleHeading = (level: 1 | 2 | 3) => {
    editor.focus();
    editor.commands.toggleHeading({ level });
  };

  const handleLink = () => {
    const selection = editor.state.selection;
    const selectedText = editor.state.doc.textBetween(selection.from, selection.to);

    if (selectedText) {
      setShowLinkDialog(true);
    } else {
      alert("Please select text first to create a link");
    }
  };

  const insertLink = () => {
    if (linkUrl) {
      editor.commands.setLink({ href: linkUrl });
      setShowLinkDialog(false);
      setLinkUrl("");
    }
  };

  const handleUndo = () => {
    editor.commands.undo();
  };

  const handleRedo = () => {
    editor.commands.redo();
  };

  return (
    <>
      <div className="flex items-center gap-1 p-3 border-b bg-muted/50">
        <Button variant="ghost" size="sm" onClick={handleUndo} title="Undo">
          <Undo className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={handleRedo} title="Redo">
          <Redo className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="mx-2 h-6" />

        <Button variant="ghost" size="sm" onClick={handleBold} title="Bold">
          <Bold className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={handleItalic} title="Italic">
          <Italic className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={handleUnderline} title="Underline">
          <Underline className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="mx-2 h-6" />

        <Button variant="ghost" size="sm" onClick={() => handleHeading(1)} title="Heading 1">
          <Heading1 className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={() => handleHeading(2)} title="Heading 2">
          <Heading2 className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={() => handleHeading(3)} title="Heading 3">
          <Heading3 className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="mx-2 h-6" />

        <Button variant="ghost" size="sm" onClick={handleBulletList} title="Bullet List">
          <List className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={handleOrderedList} title="Numbered List">
          <ListOrdered className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="mx-2 h-6" />

        <Button variant="ghost" size="sm" onClick={handleLink} title="Insert Link">
          <Link className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="mx-2 h-6" />

        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleCommentSidebar}
          title="Toggle Comments"
          className={showCommentSidebar ? "bg-accent" : ""}
        >
          <MessageSquare className="h-4 w-4" />
        </Button>
      </div>

      <Dialog open={showLinkDialog} onOpenChange={setShowLinkDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Insert Link</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <Input
              type="url"
              value={linkUrl}
              onChange={(e) => setLinkUrl(e.target.value)}
              placeholder="Enter URL..."
              autoFocus
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowLinkDialog(false)}>
              Cancel
            </Button>
            <Button onClick={insertLink}>
              Insert
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
