import { useState } from "react";
import { X, UserPlus, Crown } from "lucide-react";

interface PermissionRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (message?: string) => void;
  ownerName: string;
}

export function PermissionRequestModal({ 
  isOpen, 
  onClose, 
  onSubmit, 
  ownerName 
}: PermissionRequestModalProps) {
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      await onSubmit(message.trim() || undefined);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <UserPlus size={20} className="text-green-600" />
            <h2 className="text-lg font-semibold">Request Edit Access</h2>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-4 space-y-4">
          <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <Crown size={16} className="text-yellow-600" />
            <div>
              <p className="text-sm font-medium text-yellow-800">
                Requesting permission from
              </p>
              <p className="text-sm text-yellow-700">{ownerName}</p>
            </div>
          </div>

          <div>
            <p className="text-sm text-gray-600 mb-3">
              You currently have view-only access to this document. Send a request to the owner to upgrade your permissions to edit access.
            </p>
            
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Message (optional)
            </label>
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Explain why you need edit access..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 text-sm resize-none"
              rows={3}
              maxLength={500}
            />
            <p className="text-xs text-gray-500 mt-1">
              {message.length}/500 characters
            </p>
          </div>

          <div className="flex gap-2">
            <button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              <UserPlus size={16} />
              {isSubmitting ? "Sending..." : "Send Request"}
            </button>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-sm"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
