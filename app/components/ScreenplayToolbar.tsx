/**
 * Screenplay-specific toolbar component
 * Provides formatting buttons for screenplay elements
 */

import React from 'react';
import { But<PERSON> } from './ui/button';
import { 
  MapPin, 
  Type, 
  MessageSquare, 
  ArrowRight, 
  Camera, 
  FileText,
  MessageSquare as MessageSquareIcon
} from 'lucide-react';
import { ScreenplayElementType } from '../lib/screenplayFormatting';

interface ScreenplayToolbarProps {
  onFormatElement: (elementType: ScreenplayElementType) => void;
  onToggleComments: () => void;
  commentsCount: number;
  showComments: boolean;
  currentElementType?: ScreenplayElementType;
  disabled?: boolean;
}

export function ScreenplayToolbar({
  onFormatElement,
  onToggleComments,
  commentsCount,
  showComments,
  currentElementType,
  disabled = false,
}: ScreenplayToolbarProps) {
  const formatButtons = [
    {
      type: ScreenplayElementType.SCENE_HEADING,
      label: 'Scene Heading',
      icon: MapPin,
      description: 'INT./EXT. Location',
      shortcut: 'Ctrl+1',
    },
    {
      type: ScreenplayElementType.ACTION,
      label: 'Action',
      icon: FileText,
      description: 'Action description',
      shortcut: 'Ctrl+2',
    },
    {
      type: ScreenplayElementType.CHARACTER,
      label: 'Character',
      icon: Type,
      description: 'Character name',
      shortcut: 'Ctrl+3',
    },
    {
      type: ScreenplayElementType.DIALOGUE,
      label: 'Dialogue',
      icon: MessageSquare,
      description: 'Character dialogue',
      shortcut: 'Ctrl+4',
    },
    {
      type: ScreenplayElementType.PARENTHETICAL,
      label: 'Parenthetical',
      icon: MessageSquareIcon,
      description: '(direction)',
      shortcut: 'Ctrl+5',
    },
    {
      type: ScreenplayElementType.TRANSITION,
      label: 'Transition',
      icon: ArrowRight,
      description: 'CUT TO:',
      shortcut: 'Ctrl+6',
    },
  ];

  return (
    <div className="screenplay-toolbar border-b bg-white px-6 py-3">
      <div className="flex items-center justify-between">
        {/* Format Buttons */}
        <div className="flex items-center gap-1">
          <span className="text-sm font-medium text-gray-700 mr-3">
            Screenplay Format:
          </span>
          
          {formatButtons.map((button) => {
            const Icon = button.icon;
            const isActive = currentElementType === button.type;
            
            return (
              <Button
                key={button.type}
                variant={isActive ? "default" : "ghost"}
                size="sm"
                onClick={() => onFormatElement(button.type)}
                disabled={disabled}
                className={`gap-2 ${isActive ? 'bg-blue-600 text-white' : ''}`}
                title={`${button.description} (${button.shortcut})`}
              >
                <Icon className="h-4 w-4" />
                <span className="hidden sm:inline">{button.label}</span>
              </Button>
            );
          })}
        </div>

        {/* Right side controls */}
        <div className="flex items-center gap-3">
          {/* Current Element Indicator */}
          {currentElementType && (
            <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
              {formatButtons.find(b => b.type === currentElementType)?.label || 'Unknown'}
            </div>
          )}
          
          {/* Comments Toggle */}
          <Button
            variant={showComments ? "default" : "ghost"}
            size="sm"
            onClick={onToggleComments}
            className={`gap-2 ${showComments ? 'bg-blue-600 text-white' : ''}`}
            title="Toggle Comments Sidebar"
          >
            <MessageSquareIcon className="h-4 w-4" />
            <span className="hidden sm:inline">Comments</span>
            {commentsCount > 0 && (
              <span className="bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[1.25rem] h-5 flex items-center justify-center">
                {commentsCount}
              </span>
            )}
          </Button>
        </div>
      </div>

      {/* Keyboard Shortcuts Help */}
      <div className="mt-2 text-xs text-gray-500 flex items-center gap-4">
        <span>Keyboard shortcuts:</span>
        <div className="flex items-center gap-3">
          <span>Ctrl+1-6: Format elements</span>
          <span>Tab: Next element</span>
          <span>Enter: New line/element</span>
        </div>
      </div>


    </div>
  );
}

export default ScreenplayToolbar;
