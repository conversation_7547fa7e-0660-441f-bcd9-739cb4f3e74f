'use client';

import { Film } from 'lucide-react';
import { SignOutButton } from '../auth/SignOutButton';

interface HeaderProps {
  title?: string;
  showSidebar?: boolean;
}

export function Header({ title = 'Scripty', showSidebar = true }: HeaderProps) {
  return (
    <header className="sticky top-0 z-10 py-3 bg-white/80 backdrop-blur-sm h-16 flex justify-between items-center border-b shadow-sm px-4">
      <div className="flex items-center gap-2">
        {!showSidebar && (
          <>
            <Film className="text-blue-600" size={24} />
            <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
          </>
        )}
      </div>
      <SignOutButton />
    </header>
  );
}
