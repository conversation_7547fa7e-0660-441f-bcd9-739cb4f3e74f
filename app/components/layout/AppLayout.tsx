'use client';

import { ReactNode } from 'react';
import { Authenticated, Unauthenticated } from 'convex/react';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { SignInForm } from '../auth/SignInForm';
import { FileText } from 'lucide-react';

interface AppLayoutProps {
  children: ReactNode;
  showSidebar?: boolean;
}

export function AppLayout({ children, showSidebar = true }: AppLayoutProps) {
  return (
    <div className="h-screen flex flex-col bg-gray-50 overflow-hidden" data-testid="app-loaded">
      <Unauthenticated>
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="w-full max-w-md mx-auto text-center">
            <div className="mb-8">
              <FileText size={64} className="mx-auto mb-4 text-blue-600" />
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Scripty
              </h1>
              <p className="text-gray-600">
                Create and edit scripts and screenplays together in real-time
              </p>
            </div>
            <SignInForm />
          </div>
        </div>
      </Unauthenticated>

      <Authenticated>
        <div className="flex-1 flex h-full overflow-hidden">
          {showSidebar && <Sidebar />}
          <div className="flex-1 flex flex-col">
            <Header showSidebar={showSidebar} />
            <main className="flex-1 overflow-hidden">
              {children}
            </main>
          </div>
        </div>
      </Authenticated>
    </div>
  );
}
