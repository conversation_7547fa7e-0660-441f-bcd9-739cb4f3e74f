/**
 * Comment input component for creating and editing comments
 */

import React, { useState, useRef, useEffect } from "react";
import { Button } from "./ui/button";
import { Textarea } from "./ui/textarea";
import { Card, CardContent } from "./ui/card";
import { Send, X } from "lucide-react";

interface CommentInputProps {
  placeholder?: string;
  initialValue?: string;
  onSubmit: (content: string) => Promise<any>;
  onCancel?: () => void;
  isSubmitting?: boolean;
  autoFocus?: boolean;
  showCancel?: boolean;
  submitLabel?: string;
  className?: string;
}

export function CommentInput({
  placeholder = "Add a comment...",
  initialValue = "",
  onSubmit,
  onCancel,
  isSubmitting = false,
  autoFocus = false,
  showCancel = false,
  submitLabel = "Comment",
  className = "",
}: CommentInputProps) {
  const [content, setContent] = useState(initialValue);
  const [isExpanded, setIsExpanded] = useState(!!initialValue || autoFocus);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (autoFocus && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [autoFocus]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const trimmedContent = content.trim();
    if (!trimmedContent) return;
    
    try {
      await onSubmit(trimmedContent);
      setContent("");
      setIsExpanded(false);
    } catch (error) {
      // Error handling is done in the parent component
      console.error("Error submitting comment:", error);
    }
  };

  const handleCancel = () => {
    setContent(initialValue);
    setIsExpanded(!!initialValue);
    onCancel?.();
  };

  const handleFocus = () => {
    setIsExpanded(true);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSubmit(e);
    } else if (e.key === "Escape") {
      e.preventDefault();
      handleCancel();
    }
  };

  return (
    <Card className={`w-full ${className}`}>
      <CardContent className="p-3">
        <form onSubmit={handleSubmit} className="space-y-3">
          <Textarea
            ref={textareaRef}
            value={content}
            onChange={(e) => setContent(e.target.value)}
            onFocus={handleFocus}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className={`min-h-[80px] max-h-[200px] resize-none text-expandable ${
              isExpanded ? "min-h-[100px]" : "min-h-[40px]"
            }`}
            disabled={isSubmitting}
            style={{
              wordWrap: 'break-word',
              wordBreak: 'break-word',
              overflowWrap: 'break-word',
            }}
          />
          
          {(isExpanded || content.trim()) && (
            <div className="flex items-center justify-between">
              <div className="text-xs text-muted-foreground">
                Press Cmd+Enter to submit, Esc to cancel
              </div>
              <div className="flex items-center gap-2">
                {showCancel && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handleCancel}
                    disabled={isSubmitting}
                  >
                    <X className="h-4 w-4 mr-1" />
                    Cancel
                  </Button>
                )}
                <Button
                  type="submit"
                  size="sm"
                  disabled={!content.trim() || isSubmitting}
                >
                  <Send className="h-4 w-4 mr-1" />
                  {isSubmitting ? "Submitting..." : submitLabel}
                </Button>
              </div>
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  );
}
