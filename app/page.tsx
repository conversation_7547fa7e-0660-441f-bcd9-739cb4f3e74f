'use client';

import { useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { AppLayout } from "./components/layout/AppLayout";
import { ScriptDashboard } from "./components/ScriptDashboard";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function HomePage() {
  const router = useRouter();
  const [scriptCount, setScriptCount] = useState<number>(0);
  const loggedInUser = useQuery(api.auth.loggedInUser);

  const navigateToScript = (scriptId: string) => {
    router.push(`/script/${scriptId}`);
  };

  if (loggedInUser === undefined) {
    return (
      <AppLayout>
        <div className="flex-1 flex justify-center items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <ScriptDashboard
        onSelectScript={navigateToScript}
        onScriptCountChange={setScriptCount}
      />
    </AppLayout>
  );
}
