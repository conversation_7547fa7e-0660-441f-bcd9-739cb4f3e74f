'use client';

import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { AppLayout } from "../../components/layout/AppLayout";
import { DocumentEditor } from "../../components/DocumentEditor";
import { PresenceIndicator } from "../../components/PresenceIndicator";
import { useParams } from "next/navigation";
import { Id } from "../../../convex/_generated/dataModel";

export default function DocumentPage() {
  const params = useParams();
  const documentId = params.documentId as Id<"documents">;
  const loggedInUser = useQuery(api.auth.loggedInUser);

  if (loggedInUser === undefined) {
    return (
      <AppLayout>
        <div className="flex-1 flex justify-center items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="flex-1 flex flex-col bg-gray-50">
        <PresenceIndicator roomId={documentId} />
        <DocumentEditor documentId={documentId} />
      </div>
    </AppLayout>
  );
}
