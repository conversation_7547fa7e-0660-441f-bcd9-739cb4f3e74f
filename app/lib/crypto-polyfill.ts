/**
 * Comprehensive crypto polyfill for both server-side and client-side compatibility
 * Provides crypto.randomUUID() and crypto.getRandomValues() functionality when not available
 */

// UUID v4 implementation
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// getRandomValues polyfill implementation
function getRandomValuesPolyfill(array: any): any {
  if (array instanceof Uint8Array) {
    for (let i = 0; i < array.length; i++) {
      array[i] = Math.floor(Math.random() * 256);
    }
  } else if (array instanceof Uint16Array) {
    for (let i = 0; i < array.length; i++) {
      array[i] = Math.floor(Math.random() * 65536);
    }
  } else if (array instanceof Uint32Array) {
    for (let i = 0; i < array.length; i++) {
      array[i] = Math.floor(Math.random() * 4294967296);
    }
  } else if (array instanceof Int8Array) {
    for (let i = 0; i < array.length; i++) {
      array[i] = Math.floor(Math.random() * 256) - 128;
    }
  } else if (array instanceof Int16Array) {
    for (let i = 0; i < array.length; i++) {
      array[i] = Math.floor(Math.random() * 65536) - 32768;
    }
  } else if (array instanceof Int32Array) {
    for (let i = 0; i < array.length; i++) {
      array[i] = Math.floor(Math.random() * 4294967296) - 2147483648;
    }
  }
  return array;
}

// Client-side polyfill (browser environment)
if (typeof window !== 'undefined') {
  // Ensure crypto object exists
  if (!window.crypto) {
    (window as any).crypto = {};
  }

  // Add randomUUID if it doesn't exist
  if (!window.crypto.randomUUID) {
    (window.crypto as any).randomUUID = generateUUID;
  }

  // Add getRandomValues if it doesn't exist
  if (!window.crypto.getRandomValues) {
    (window.crypto as any).getRandomValues = getRandomValuesPolyfill;
  }

  // Also polyfill the global crypto if it exists but lacks functions
  if (typeof crypto !== 'undefined') {
    if (!crypto.randomUUID) {
      (crypto as any).randomUUID = generateUUID;
    }
    if (!crypto.getRandomValues) {
      (crypto as any).getRandomValues = getRandomValuesPolyfill;
    }
  }
}

// Server-side polyfill (Node.js environment)
if (typeof globalThis !== 'undefined') {
  // Ensure crypto object exists
  if (!globalThis.crypto) {
    globalThis.crypto = {} as Crypto;
  }

  // Add randomUUID if it doesn't exist
  if (!globalThis.crypto.randomUUID) {
    (globalThis.crypto as any).randomUUID = generateUUID;
  }

  // Add getRandomValues if it doesn't exist
  if (!globalThis.crypto.getRandomValues) {
    (globalThis.crypto as any).getRandomValues = getRandomValuesPolyfill;
  }
}

// Additional fallback for environments where crypto exists but functions don't
if (typeof crypto !== 'undefined') {
  if (!crypto.randomUUID) {
    (crypto as any).randomUUID = generateUUID;
  }
  if (!crypto.getRandomValues) {
    (crypto as any).getRandomValues = getRandomValuesPolyfill;
  }
}

export {};
