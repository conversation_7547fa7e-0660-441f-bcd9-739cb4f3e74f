/**
 * Collaborative cursor and selection plugin for ProseMirror
 * Handles real-time cursor positioning and selection highlighting
 */

import { Plugin, Plugin<PERSON>ey } from "prosemirror-state";
import { Decoration, DecorationSet } from "prosemirror-view";
import { getUserColor } from "./userColors";

export interface CursorData {
  position: number;
  selection?: {
    from: number;
    to: number;
  };
  timestamp: number;
}

export interface CollaboratorCursor {
  userId: string;
  sessionId: string;
  name: string;
  image?: string;
  cursor: CursorData;
}

export interface CollaborativeCursorsState {
  cursors: CollaboratorCursor[];
  decorations: DecorationSet;
  lastCursorHash: string; // Track cursor changes to prevent unnecessary updates
}

export const collaborativeCursorsKey = new PluginKey<CollaborativeCursorsState>("collaborative-cursors");

/**
 * Create a cursor decoration element
 */
function createCursorElement(collaborator: CollaboratorCursor): HTMLElement {
  const color = getUserColor(collaborator.userId);
  
  const cursor = document.createElement("div");
  cursor.className = "collaborative-cursor";
  cursor.style.cssText = `
    position: absolute;
    width: 2px;
    height: 1.2em;
    background-color: rgb(${color.rgb});
    pointer-events: none;
    z-index: 10;
    animation: cursor-blink 1s infinite;
  `;

  // Create cursor label
  const label = document.createElement("div");
  label.className = "collaborative-cursor-label";
  label.textContent = collaborator.name;
  label.style.cssText = `
    position: absolute;
    top: -24px;
    left: 0;
    background-color: rgb(${color.rgb});
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
    z-index: 11;
  `;

  // Show label on hover
  cursor.addEventListener("mouseenter", () => {
    label.style.opacity = "1";
  });
  
  cursor.addEventListener("mouseleave", () => {
    label.style.opacity = "0";
  });

  cursor.appendChild(label);
  return cursor;
}

/**
 * Create a selection decoration
 */
function createSelectionDecoration(from: number, to: number, collaborator: CollaboratorCursor) {
  const color = getUserColor(collaborator.userId);

  return Decoration.inline(from, to, {
    class: "collaborative-selection",
    style: `background-color: rgba(${color.rgb}, 0.3); border-radius: 2px;`,
    title: `${collaborator.name}'s selection`,
    // Add stable key to prevent unnecessary DOM recreations
    key: `selection-${collaborator.userId}`,
  });
}

/**
 * Create a cursor decoration
 */
function createCursorDecoration(position: number, collaborator: CollaboratorCursor) {
  const cursorElement = createCursorElement(collaborator);

  return Decoration.widget(position, cursorElement, {
    side: 1,
    // Use stable key based only on userId to prevent recreation
    key: `cursor-${collaborator.userId}`,
  });
}

/**
 * Generate a hash of cursor data to detect changes
 */
function generateCursorHash(cursors: CollaboratorCursor[]): string {
  return cursors
    .map(c => `${c.userId}:${c.cursor.position}:${c.cursor.selection?.from || ''}:${c.cursor.selection?.to || ''}`)
    .sort()
    .join('|');
}

/**
 * Update decorations based on current collaborators with optimization
 */
function updateDecorations(
  doc: any,
  cursors: CollaboratorCursor[],
  currentDecorations: DecorationSet,
  forceUpdate: boolean = false
): DecorationSet {
  // Generate hash to check if cursors actually changed
  const newHash = generateCursorHash(cursors);

  // If cursors haven't changed and we're not forcing an update, return existing decorations
  if (!forceUpdate && currentDecorations && currentDecorations.find().length > 0) {
    // We'll check this in the plugin state
  }

  const decorations: Decoration[] = [];

  cursors.forEach((collaborator) => {
    const { cursor } = collaborator;

    // Add selection decoration if there's a selection
    if (cursor.selection && cursor.selection.from !== cursor.selection.to) {
      const { from, to } = cursor.selection;

      // Ensure positions are within document bounds
      if (from >= 0 && to <= doc.content.size && from < to) {
        decorations.push(createSelectionDecoration(from, to, collaborator));
      }
    }

    // Add cursor decoration (only if no selection or selection is empty)
    if (cursor.position >= 0 && cursor.position <= doc.content.size) {
      // Only show cursor if there's no selection
      if (!cursor.selection || cursor.selection.from === cursor.selection.to) {
        decorations.push(createCursorDecoration(cursor.position, collaborator));
      }
    }
  });

  return DecorationSet.create(doc, decorations);
}

/**
 * Create the collaborative cursors plugin
 */
export function createCollaborativeCursorsPlugin() {
  return new Plugin<CollaborativeCursorsState>({
    key: collaborativeCursorsKey,
    
    state: {
      init() {
        return {
          cursors: [],
          decorations: DecorationSet.empty,
          lastCursorHash: '',
        };
      },

      apply(tr, state, oldState, newState) {
        let { cursors, decorations, lastCursorHash } = state;

        // Check for cursor updates from meta
        const cursorUpdate = tr.getMeta(collaborativeCursorsKey);
        if (cursorUpdate) {
          cursors = cursorUpdate.cursors || cursors;
        }

        // Generate new hash to check if cursors actually changed
        const newCursorHash = generateCursorHash(cursors);
        const cursorsChanged = newCursorHash !== lastCursorHash;

        // Update decorations only if document changed or cursors actually changed
        if (tr.docChanged || cursorsChanged) {
          decorations = updateDecorations(
            newState.doc,
            cursors,
            decorations,
            cursorsChanged || tr.docChanged
          );
          lastCursorHash = newCursorHash;
        } else {
          // Map existing decorations through the transaction
          decorations = decorations.map(tr.mapping, tr.doc);
        }

        return {
          cursors,
          decorations,
          lastCursorHash,
        };
      },
    },
    
    props: {
      decorations(state) {
        return this.getState(state)?.decorations;
      },
    },
  });
}

/**
 * Update cursors in the plugin state
 */
export function updateCursors(view: any, cursors: CollaboratorCursor[]) {
  const tr = view.state.tr;
  tr.setMeta(collaborativeCursorsKey, { cursors });
  view.dispatch(tr);
}

/**
 * Get current cursor position and selection from editor state
 */
export function getCurrentCursorData(state: any): CursorData {
  const { selection } = state;
  
  return {
    position: selection.head,
    selection: selection.from !== selection.to ? {
      from: selection.from,
      to: selection.to,
    } : undefined,
    timestamp: Date.now(),
  };
}
