/**
 * Screenplay formatting utilities and constants
 * Based on industry-standard screenplay formatting rules
 */

// Standard screenplay page dimensions (in inches)
export const PAGE_DIMENSIONS = {
  width: 8.5,
  height: 11,
  marginTop: 1,
  marginBottom: 1,
  marginLeft: 1.5,
  marginRight: 1,
} as const;

// Convert inches to pixels (assuming 96 DPI)
export const PAGE_DIMENSIONS_PX = {
  width: PAGE_DIMENSIONS.width * 96,
  height: PAGE_DIMENSIONS.height * 96,
  marginTop: PAGE_DIMENSIONS.marginTop * 96,
  marginBottom: PAGE_DIMENSIONS.marginBottom * 96,
  marginLeft: PAGE_DIMENSIONS.marginLeft * 96,
  marginRight: PAGE_DIMENSIONS.marginRight * 96,
} as const;

// Calculate content area
export const CONTENT_AREA = {
  width: PAGE_DIMENSIONS_PX.width - PAGE_DIMENSIONS_PX.marginLeft - PAGE_DIMENSIONS_PX.marginRight,
  height: PAGE_DIMENSIONS_PX.height - PAGE_DIMENSIONS_PX.marginTop - PAGE_DIMENSIONS_PX.marginBottom,
} as const;

// Screenplay element types
export enum ScreenplayElementType {
  SCENE_HEADING = 'scene_heading',
  ACTION = 'action',
  CHARACTER = 'character',
  DIALOGUE = 'dialogue',
  PARENTHETICAL = 'parenthetical',
  TRANSITION = 'transition',
  SHOT = 'shot',
  GENERAL = 'general',
}

// Formatting rules for each element type
export const ELEMENT_FORMATTING = {
  [ScreenplayElementType.SCENE_HEADING]: {
    marginLeft: 0,
    marginRight: 0,
    textTransform: 'uppercase' as const,
    fontWeight: 'bold' as const,
    marginTop: 24,
    marginBottom: 12,
    lineHeight: 1.2,
  },
  [ScreenplayElementType.ACTION]: {
    marginLeft: 0,
    marginRight: 0,
    textTransform: 'none' as const,
    fontWeight: 'normal' as const,
    marginTop: 12,
    marginBottom: 12,
    lineHeight: 1.2,
  },
  [ScreenplayElementType.CHARACTER]: {
    marginLeft: 220, // ~3.7 inches from left
    marginRight: 0,
    textTransform: 'uppercase' as const,
    fontWeight: 'normal' as const,
    marginTop: 12,
    marginBottom: 0,
    lineHeight: 1.2,
  },
  [ScreenplayElementType.DIALOGUE]: {
    marginLeft: 100, // ~1.5 inches from left
    marginRight: 100, // ~1.5 inches from right
    textTransform: 'none' as const,
    fontWeight: 'normal' as const,
    marginTop: 0,
    marginBottom: 12,
    lineHeight: 1.2,
  },
  [ScreenplayElementType.PARENTHETICAL]: {
    marginLeft: 160, // ~2.5 inches from left
    marginRight: 120, // ~2 inches from right
    textTransform: 'none' as const,
    fontWeight: 'normal' as const,
    fontStyle: 'italic' as const,
    marginTop: 0,
    marginBottom: 0,
    lineHeight: 1.2,
  },
  [ScreenplayElementType.TRANSITION]: {
    marginLeft: 400, // Right-aligned
    marginRight: 0,
    textTransform: 'uppercase' as const,
    fontWeight: 'normal' as const,
    marginTop: 12,
    marginBottom: 24,
    lineHeight: 1.2,
    textAlign: 'right' as const,
  },
  [ScreenplayElementType.SHOT]: {
    marginLeft: 0,
    marginRight: 0,
    textTransform: 'uppercase' as const,
    fontWeight: 'normal' as const,
    marginTop: 12,
    marginBottom: 12,
    lineHeight: 1.2,
  },
  [ScreenplayElementType.GENERAL]: {
    marginLeft: 0,
    marginRight: 0,
    textTransform: 'none' as const,
    fontWeight: 'normal' as const,
    marginTop: 12,
    marginBottom: 12,
    lineHeight: 1.2,
  },
} as const;

// Auto-detection patterns for screenplay elements
export const ELEMENT_PATTERNS = {
  [ScreenplayElementType.SCENE_HEADING]: /^(INT\.|EXT\.|FADE IN:|FADE OUT\.|CUT TO:)/i,
  [ScreenplayElementType.TRANSITION]: /^(FADE IN:|FADE OUT\.|CUT TO:|DISSOLVE TO:|SMASH CUT TO:)$/i,
  [ScreenplayElementType.CHARACTER]: /^[A-Z][A-Z\s]+(\(.*\))?$/,
  [ScreenplayElementType.PARENTHETICAL]: /^\(.*\)$/,
} as const;

// Page break rules
export const PAGE_BREAK_RULES = {
  // Don't break these elements
  keepTogether: [
    ScreenplayElementType.CHARACTER,
    ScreenplayElementType.PARENTHETICAL,
  ],
  // Minimum lines to keep together
  minLinesBeforeBreak: 2,
  minLinesAfterBreak: 2,
  // Orphan/widow control
  preventOrphans: true,
  preventWidows: true,
} as const;

/**
 * Detect screenplay element type from text content
 */
export function detectElementType(text: string): ScreenplayElementType {
  const trimmedText = text.trim();
  
  if (!trimmedText) {
    return ScreenplayElementType.ACTION;
  }
  
  // Check patterns in order of specificity
  if (ELEMENT_PATTERNS[ScreenplayElementType.SCENE_HEADING].test(trimmedText)) {
    return ScreenplayElementType.SCENE_HEADING;
  }
  
  if (ELEMENT_PATTERNS[ScreenplayElementType.TRANSITION].test(trimmedText)) {
    return ScreenplayElementType.TRANSITION;
  }
  
  if (ELEMENT_PATTERNS[ScreenplayElementType.PARENTHETICAL].test(trimmedText)) {
    return ScreenplayElementType.PARENTHETICAL;
  }
  
  if (ELEMENT_PATTERNS[ScreenplayElementType.CHARACTER].test(trimmedText)) {
    return ScreenplayElementType.CHARACTER;
  }
  
  return ScreenplayElementType.ACTION;
}

/**
 * Get CSS styles for a screenplay element type
 */
export function getElementStyles(elementType: ScreenplayElementType) {
  const formatting = ELEMENT_FORMATTING[elementType];
  
  return {
    marginLeft: `${formatting.marginLeft}px`,
    marginRight: `${formatting.marginRight}px`,
    marginTop: `${formatting.marginTop}px`,
    marginBottom: `${formatting.marginBottom}px`,
    textTransform: formatting.textTransform,
    fontWeight: formatting.fontWeight,
    fontStyle: (formatting as any).fontStyle || 'normal',
    lineHeight: formatting.lineHeight,
    textAlign: (formatting as any).textAlign || 'left',
  };
}

/**
 * Calculate if a page break should occur
 */
export function shouldBreakPage(
  currentPageHeight: number,
  elementHeight: number,
  elementType: ScreenplayElementType,
  nextElementType?: ScreenplayElementType
): boolean {
  const availableHeight = CONTENT_AREA.height - currentPageHeight;
  
  // If element doesn't fit, break
  if (elementHeight > availableHeight) {
    return true;
  }
  
  // Keep character names with dialogue
  if (elementType === ScreenplayElementType.CHARACTER && 
      nextElementType === ScreenplayElementType.DIALOGUE) {
    // Estimate dialogue height (rough calculation)
    const estimatedDialogueHeight = 60; // ~3 lines
    if (elementHeight + estimatedDialogueHeight > availableHeight) {
      return true;
    }
  }
  
  // Keep parentheticals with dialogue
  if (elementType === ScreenplayElementType.PARENTHETICAL &&
      nextElementType === ScreenplayElementType.DIALOGUE) {
    const estimatedDialogueHeight = 40; // ~2 lines
    if (elementHeight + estimatedDialogueHeight > availableHeight) {
      return true;
    }
  }
  
  return false;
}

/**
 * Format text according to screenplay element type
 */
export function formatScreenplayText(text: string, elementType: ScreenplayElementType): string {
  const formatting = ELEMENT_FORMATTING[elementType];
  
  if (formatting.textTransform === 'uppercase') {
    return text.toUpperCase();
  }
  
  return text;
}
