/**
 * Unit tests for URL routing utilities
 * Tests document ID parsing, validation, and URL generation
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
  isValidDocumentId,
  extractDocumentIdFromPath,
  extractDocumentIdFromSearch,
  extractDocumentIdFromUrl,
  generateDocumentUrl,
  generateHomeUrl,
  isDocumentRoute,
  navigationUtils
} from '../routing';
import { Id } from '../../../convex/_generated/dataModel';

// Mock window.location for URL tests
const mockLocation = {
  origin: 'http://localhost:3000'
};

Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true
});

// Mock window.history for navigation tests
const mockHistory = {
  pushState: vi.fn(),
  replaceState: vi.fn()
};

Object.defineProperty(window, 'history', {
  value: mockHistory,
  writable: true
});

describe('URL Routing Utilities', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('isValidDocumentId', () => {
    it('should return true for valid document ID strings', () => {
      expect(isValidDocumentId('j57abc123def456')).toBe(true);
      expect(isValidDocumentId('doc123')).toBe(true);
      expect(isValidDocumentId('a')).toBe(true);
    });

    it('should return false for invalid document IDs', () => {
      expect(isValidDocumentId('')).toBe(false);
      expect(isValidDocumentId('doc/123')).toBe(false);
      expect(isValidDocumentId('doc?123')).toBe(false);
    });

    it('should return false for excluded route names', () => {
      expect(isValidDocumentId('other')).toBe(false);
      expect(isValidDocumentId('about')).toBe(false);
      expect(isValidDocumentId('settings')).toBe(false);
    });

    it('should return false for non-string values', () => {
      expect(isValidDocumentId(null as any)).toBe(false);
      expect(isValidDocumentId(undefined as any)).toBe(false);
      expect(isValidDocumentId(123 as any)).toBe(false);
    });
  });

  describe('extractDocumentIdFromPath', () => {
    it('should extract document ID from /document/{id} pattern', () => {
      expect(extractDocumentIdFromPath('/document/abc123')).toBe('abc123');
      expect(extractDocumentIdFromPath('document/xyz789')).toBe('xyz789');
    });

    it('should return null for non-document paths', () => {
      expect(extractDocumentIdFromPath('/abc123')).toBe(null);
      expect(extractDocumentIdFromPath('xyz789')).toBe(null);
      expect(extractDocumentIdFromPath('/other')).toBe(null);
    });

    it('should return null for invalid paths', () => {
      expect(extractDocumentIdFromPath('/')).toBe(null);
      expect(extractDocumentIdFromPath('')).toBe(null);
      expect(extractDocumentIdFromPath('/document/')).toBe(null);
      expect(extractDocumentIdFromPath('/document/abc/def')).toBe(null);
      expect(extractDocumentIdFromPath('/other/path')).toBe(null);
    });

    it('should return null for paths with invalid document IDs', () => {
      expect(extractDocumentIdFromPath('/document/doc?123')).toBe(null);
      expect(extractDocumentIdFromPath('/document/other')).toBe(null);
    });
  });

  describe('extractDocumentIdFromSearch', () => {
    it('should extract document ID from ?doc={id} parameter', () => {
      expect(extractDocumentIdFromSearch('?doc=abc123')).toBe('abc123');
      expect(extractDocumentIdFromSearch('?doc=xyz789&other=value')).toBe('xyz789');
      expect(extractDocumentIdFromSearch('?other=value&doc=def456')).toBe('def456');
    });

    it('should return null when doc parameter is missing', () => {
      expect(extractDocumentIdFromSearch('')).toBe(null);
      expect(extractDocumentIdFromSearch('?other=value')).toBe(null);
      expect(extractDocumentIdFromSearch('?doc=')).toBe(null);
    });

    it('should return null for invalid document IDs in search params', () => {
      expect(extractDocumentIdFromSearch('?doc=doc?123')).toBe(null);
      expect(extractDocumentIdFromSearch('?doc=doc/123')).toBe(null);
    });
  });

  describe('extractDocumentIdFromUrl', () => {
    it('should extract document ID from pathname first', () => {
      expect(extractDocumentIdFromUrl('http://localhost:3000/document/abc123')).toBe('abc123');
      expect(extractDocumentIdFromUrl('/document/xyz789')).toBe('xyz789');
    });

    it('should fall back to search params when pathname fails', () => {
      expect(extractDocumentIdFromUrl('http://localhost:3000/?doc=abc123')).toBe('abc123');
      expect(extractDocumentIdFromUrl('http://localhost:3000/other?doc=xyz789')).toBe('xyz789');
    });

    it('should handle relative URLs', () => {
      expect(extractDocumentIdFromUrl('/document/abc123')).toBe('abc123');
      expect(extractDocumentIdFromUrl('/?doc=xyz789')).toBe('xyz789');
    });

    it('should return null for URLs without document IDs', () => {
      expect(extractDocumentIdFromUrl('http://localhost:3000/')).toBe(null);
      expect(extractDocumentIdFromUrl('http://localhost:3000/other')).toBe(null);
      expect(extractDocumentIdFromUrl('/')).toBe(null);
    });
  });

  describe('generateDocumentUrl', () => {
    it('should generate correct document URLs', () => {
      expect(generateDocumentUrl('abc123' as Id<"documents">)).toBe('/document/abc123');
      expect(generateDocumentUrl('xyz789' as Id<"documents">)).toBe('/document/xyz789');
    });
  });

  describe('generateHomeUrl', () => {
    it('should generate home URL', () => {
      expect(generateHomeUrl()).toBe('/');
    });
  });

  describe('isDocumentRoute', () => {
    it('should return true for document routes', () => {
      expect(isDocumentRoute('/document/abc123')).toBe(true);
      expect(isDocumentRoute('document/xyz789')).toBe(true);
    });

    it('should return false for non-document routes', () => {
      expect(isDocumentRoute('/')).toBe(false);
      expect(isDocumentRoute('/other')).toBe(false);
      expect(isDocumentRoute('/xyz789')).toBe(false);
      expect(isDocumentRoute('/document/')).toBe(false);
    });
  });

  describe('navigationUtils', () => {
    it('should navigate to document using pushState', () => {
      navigationUtils.navigateToDocument('abc123' as Id<"documents">);
      expect(mockHistory.pushState).toHaveBeenCalledWith(null, '', '/document/abc123');
    });

    it('should navigate to home using pushState', () => {
      navigationUtils.navigateToHome();
      expect(mockHistory.pushState).toHaveBeenCalledWith(null, '', '/');
    });

    it('should replace URL using replaceState', () => {
      navigationUtils.replaceUrl('/new-url');
      expect(mockHistory.replaceState).toHaveBeenCalledWith(null, '', '/new-url');
    });
  });
});
