'use client';

import { useQuery } from "convex/react";
import { api } from "../../../../../convex/_generated/api";
import { AppLayout } from "../../../../components/layout/AppLayout";
import { ScriptList } from "../../../../components/ScriptList";
import { ScriptDocumentList } from "../../../../components/ScriptDocumentList";
import { DocumentEditor } from "../../../../components/DocumentEditor";
import { PresenceIndicator } from "../../../../components/PresenceIndicator";
import { useRouter, useParams } from "next/navigation";
import { useState } from "react";
import { Id } from "../../../../../convex/_generated/dataModel";

export default function ScriptDocumentPage() {
  const router = useRouter();
  const params = useParams();
  const scriptId = params.scriptId as Id<"scripts">;
  const documentId = params.documentId as Id<"documents">;
  
  const [documentCount, setDocumentCount] = useState<number>(0);
  const [scriptCount, setScriptCount] = useState<number>(0);
  const loggedInUser = useQuery(api.auth.loggedInUser);

  const navigateToScript = (newScriptId: Id<"scripts">) => {
    router.push(`/script/${newScriptId}`);
  };

  const navigateToScriptDocument = (scriptId: Id<"scripts">, documentId: Id<"documents">) => {
    router.push(`/script/${scriptId}/document/${documentId}`);
  };

  if (loggedInUser === undefined) {
    return (
      <AppLayout>
        <div className="flex-1 flex justify-center items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout showSidebar={false}>
      <div className="flex-1 flex h-full overflow-hidden">
        {/* Left Sidebar - Scripts */}
        <ScriptList
          onSelectScript={navigateToScript}
          selectedScriptId={scriptId}
          onScriptCountChange={setScriptCount}
        />
        
        {/* Middle Panel - Documents */}
        <ScriptDocumentList
          scriptId={scriptId}
          onSelectDocument={navigateToScriptDocument}
          selectedDocumentId={documentId}
          onDocumentCountChange={setDocumentCount}
        />

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col bg-gray-50">
          <PresenceIndicator roomId={documentId} />
          <DocumentEditor documentId={documentId} />
        </div>
      </div>
    </AppLayout>
  );
}
