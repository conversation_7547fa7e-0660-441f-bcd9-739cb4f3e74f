'use client';

import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { AppLayout } from "../../components/layout/AppLayout";
import { ScriptList } from "../../components/ScriptList";
import { ScriptDocumentList } from "../../components/ScriptDocumentList";
import { useRouter, useParams } from "next/navigation";
import { useState } from "react";
import { Film, Users } from "lucide-react";
import { Id } from "../../../convex/_generated/dataModel";

export default function ScriptPage() {
  const router = useRouter();
  const params = useParams();
  const scriptId = params.scriptId as Id<"scripts">;
  
  const [documentCount, setDocumentCount] = useState<number>(0);
  const [scriptCount, setScriptCount] = useState<number>(0);
  const loggedInUser = useQuery(api.auth.loggedInUser);

  const navigateToScript = (newScriptId: Id<"scripts">) => {
    router.push(`/script/${newScriptId}`);
  };

  const navigateToScriptDocument = (scriptId: Id<"scripts">, documentId: Id<"documents">) => {
    router.push(`/script/${scriptId}/document/${documentId}`);
  };

  if (loggedInUser === undefined) {
    return (
      <AppLayout>
        <div className="flex-1 flex justify-center items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout showSidebar={false}>
      <div className="flex-1 flex h-full overflow-hidden">
        {/* Left Sidebar - Scripts */}
        {/* <ScriptList
          onSelectScript={navigateToScript}
          selectedScriptId={scriptId}
          onScriptCountChange={setScriptCount}
        /> */}
        
        {/* Left Panel - Documents */}
        <ScriptDocumentList
          scriptId={scriptId}
          onSelectDocument={navigateToScriptDocument}
          selectedDocumentId={null}
          onDocumentCountChange={setDocumentCount}
        />

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col bg-gray-50">
          <div className="flex-1 flex items-center justify-center p-8">
            <div className="text-center text-gray-500 max-w-md">
              <Film size={64} className="mx-auto mb-6 opacity-50" />
              <h2 className="text-2xl font-semibold mb-3 text-gray-900">Script Selected</h2>
              <p className="mb-6 text-gray-600">
                {documentCount === 0
                  ? "Add your first document to this script to get started"
                  : "Select a document from the script to start editing"
                }
              </p>
              <div className="flex items-center justify-center gap-6 text-sm">
                <div className="flex items-center gap-2">
                  <Users size={18} className="text-blue-600" />
                  <span>Real-time collaboration</span>
                </div>
                <div className="flex items-center gap-2">
                  <Film size={18} className="text-blue-600" />
                  <span>Screenplay formatting</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
