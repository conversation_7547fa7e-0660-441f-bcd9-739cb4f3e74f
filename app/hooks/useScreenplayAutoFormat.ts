/**
 * Hook for auto-formatting screenplay elements in BlockNote editor
 * Applies screenplay formatting rules and CSS classes based on content
 */

import { useEffect, useCallback } from "react";
import { ScreenplayElementType } from "../lib/screenplayFormatting";
import { autoFormatScreenplayBlock } from "../lib/screenplaySchema";

interface UseScreenplayAutoFormatProps {
  editor: any; // BlockNote editor instance
  isReadOnly?: boolean;
}

export function useScreenplayAutoFormat({
  editor,
  isReadOnly = false
}: UseScreenplayAutoFormatProps) {
  
  // Auto-format blocks based on content
  const handleAutoFormat = useCallback((block: any) => {
    if (!block || !block.content || isReadOnly) return;

    const text = block.content.map((item: any) => item.text || '').join('');
    const currentBlockType = block.type || 'paragraph';
    
    const { blockType, formattedText } = autoFormatScreenplayBlock(text, currentBlockType);
    
    // Apply CSS class based on detected screenplay element
    const cssClass = getScreenplayElementClass(blockType);
    
    // Update block properties if needed
    if (blockType !== currentBlockType || text !== formattedText) {
      try {
        // Update the block with new formatting
        editor.updateBlock(block.id, {
          type: 'paragraph', // Keep as paragraph but add CSS class
          props: {
            ...block.props,
            className: cssClass,
            screenplayType: blockType,
          },
          content: formattedText ? [{ type: 'text', text: formattedText }] : block.content
        });
      } catch (error) {
        console.warn('Failed to auto-format screenplay block:', error);
      }
    }
  }, [editor, isReadOnly]);

  // Listen for content changes and apply auto-formatting
  useEffect(() => {
    if (!editor || isReadOnly) return;

    const handleContentChange = () => {
      try {
        const blocks = editor.document;
        if (blocks && Array.isArray(blocks)) {
          blocks.forEach(handleAutoFormat);
        }
      } catch (error) {
        console.warn('Error during auto-formatting:', error);
      }
    };

    // Listen for editor changes
    const unsubscribe = editor.onChange?.(handleContentChange);

    return () => {
      if (typeof unsubscribe === 'function') {
        unsubscribe();
      }
    };
  }, [editor, isReadOnly, handleAutoFormat]);

  // Manual formatting function for toolbar
  const formatAsScreenplayElement = useCallback((elementType: ScreenplayElementType) => {
    if (!editor || isReadOnly) return;

    try {
      const selection = editor.getTextCursorPosition();
      const currentBlock = editor.getBlock(selection.block);
      
      if (currentBlock) {
        const cssClass = getScreenplayElementClass(getBlockTypeFromElementType(elementType));
        
        editor.updateBlock(currentBlock.id, {
          type: 'paragraph',
          props: {
            ...currentBlock.props,
            className: cssClass,
            screenplayType: getBlockTypeFromElementType(elementType),
          }
        });
      }
    } catch (error) {
      console.warn('Failed to format as screenplay element:', error);
    }
  }, [editor, isReadOnly]);

  return {
    formatAsScreenplayElement,
    handleAutoFormat,
  };
}

// Helper function to get CSS class for screenplay element
function getScreenplayElementClass(blockType: string): string {
  const classMap: Record<string, string> = {
    'sceneHeading': 'screenplay-scene-heading',
    'character': 'screenplay-character',
    'dialogue': 'screenplay-dialogue',
    'parenthetical': 'screenplay-parenthetical',
    'transition': 'screenplay-transition',
    'action': 'screenplay-action',
  };
  
  return classMap[blockType] || 'screenplay-action';
}

// Helper function to convert screenplay element type to block type
function getBlockTypeFromElementType(elementType: ScreenplayElementType): string {
  switch (elementType) {
    case ScreenplayElementType.SCENE_HEADING:
      return 'sceneHeading';
    case ScreenplayElementType.CHARACTER:
      return 'character';
    case ScreenplayElementType.DIALOGUE:
      return 'dialogue';
    case ScreenplayElementType.PARENTHETICAL:
      return 'parenthetical';
    case ScreenplayElementType.TRANSITION:
      return 'transition';
    case ScreenplayElementType.ACTION:
    default:
      return 'action';
  }
}

// Helper function to convert block type to screenplay element type
function getElementTypeFromBlockType(blockType: string): ScreenplayElementType {
  switch (blockType) {
    case 'sceneHeading':
      return ScreenplayElementType.SCENE_HEADING;
    case 'character':
      return ScreenplayElementType.CHARACTER;
    case 'dialogue':
      return ScreenplayElementType.DIALOGUE;
    case 'parenthetical':
      return ScreenplayElementType.PARENTHETICAL;
    case 'transition':
      return ScreenplayElementType.TRANSITION;
    case 'action':
    default:
      return ScreenplayElementType.ACTION;
  }
}
