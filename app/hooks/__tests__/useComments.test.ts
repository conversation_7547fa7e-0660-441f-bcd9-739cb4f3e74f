/**
 * Tests for useComments hook
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { useComments } from '../useComments';

// Mock Convex hooks
vi.mock('convex/react', () => ({
  useQuery: vi.fn(),
  useMutation: vi.fn(),
}));

// Mock toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock comment plugin helpers
vi.mock('../lib/commentPlugin', () => ({
  commentPluginHelpers: {
    setComments: vi.fn(),
    getSelectionInfo: vi.fn(),
    selectComment: vi.fn(),
    setSidebar: vi.fn(),
    toggleSidebar: vi.fn(),
    getState: vi.fn(),
  },
}));

describe('useComments', () => {
  const mockDocumentId = 'doc123' as any;
  const mockEditor = {
    prosemirrorView: {
      state: {
        selection: { from: 0, to: 10 },
        doc: { textBetween: vi.fn(() => 'selected text') },
      },
    },
  };

  const mockComments = [
    {
      _id: 'comment1',
      documentId: mockDocumentId,
      userId: 'user1',
      content: 'Test comment',
      position: 0,
      selection: { from: 0, to: 10 },
      selectedText: 'test text',
      isResolved: false,
      timestamp: Date.now(),
      user: { _id: 'user1', name: 'Test User', email: '<EMAIL>' },
      replies: [],
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mock returns
    mockUseQuery.mockReturnValue(mockComments);
    mockUseMutation.mockReturnValue(vi.fn());
    mockCommentPluginHelpers.getSelectionInfo.mockReturnValue({
      from: 0,
      to: 10,
      selectedText: 'selected text',
      position: 0,
    });
  });

  it('should initialize with comments from query', () => {
    const { result } = renderHook(() =>
      useComments({
        documentId: mockDocumentId,
        editor: mockEditor,
        isReadOnly: false,
      })
    );

    expect(result.current.comments).toEqual(mockComments);
    expect(result.current.isLoading).toBe(false);
  });

  it('should handle loading state', () => {
    mockUseQuery.mockReturnValue(undefined);

    const { result } = renderHook(() =>
      useComments({
        documentId: mockDocumentId,
        editor: mockEditor,
        isReadOnly: false,
      })
    );

    expect(result.current.comments).toEqual([]);
    expect(result.current.isLoading).toBe(true);
  });

  it('should create comment with valid selection', async () => {
    const mockCreateComment = vi.fn().mockResolvedValue('comment123');
    mockUseMutation.mockReturnValue(mockCreateComment);

    const { result } = renderHook(() =>
      useComments({
        documentId: mockDocumentId,
        editor: mockEditor,
        isReadOnly: false,
      })
    );

    const commentId = await result.current.createComment('Test comment content');

    expect(mockCreateComment).toHaveBeenCalledWith({
      documentId: mockDocumentId,
      content: 'Test comment content',
      position: 0,
      selection: { from: 0, to: 10 },
      selectedText: 'selected text',
    });
    expect(commentId).toBe('comment123');
  });

  it('should not create comment without selection', async () => {
    mockCommentPluginHelpers.getSelectionInfo.mockReturnValue(null);

    const { result } = renderHook(() =>
      useComments({
        documentId: mockDocumentId,
        editor: mockEditor,
        isReadOnly: false,
      })
    );

    const commentId = await result.current.createComment('Test comment content');

    expect(commentId).toBe(null);
  });

  it('should not create comment in read-only mode', async () => {
    const { result } = renderHook(() =>
      useComments({
        documentId: mockDocumentId,
        editor: mockEditor,
        isReadOnly: true,
      })
    );

    const commentId = await result.current.createComment('Test comment content');

    expect(commentId).toBe(null);
  });

  it('should update comment', async () => {
    const mockUpdateComment = vi.fn().mockResolvedValue(true);
    mockUseMutation.mockReturnValue(mockUpdateComment);

    const { result } = renderHook(() =>
      useComments({
        documentId: mockDocumentId,
        editor: mockEditor,
        isReadOnly: false,
      })
    );

    const success = await result.current.updateComment('comment1' as any, 'Updated content');

    expect(mockUpdateComment).toHaveBeenCalledWith({
      commentId: 'comment1',
      content: 'Updated content',
    });
    expect(success).toBe(true);
  });

  it('should delete comment', async () => {
    const mockDeleteComment = vi.fn().mockResolvedValue(true);
    mockUseMutation.mockReturnValue(mockDeleteComment);

    const { result } = renderHook(() =>
      useComments({
        documentId: mockDocumentId,
        editor: mockEditor,
        isReadOnly: false,
      })
    );

    const success = await result.current.deleteComment('comment1' as any);

    expect(mockDeleteComment).toHaveBeenCalledWith({
      commentId: 'comment1',
    });
    expect(success).toBe(true);
  });

  it('should create reply', async () => {
    const mockCreateReply = vi.fn().mockResolvedValue('reply123');
    mockUseMutation.mockReturnValue(mockCreateReply);

    const { result } = renderHook(() =>
      useComments({
        documentId: mockDocumentId,
        editor: mockEditor,
        isReadOnly: false,
      })
    );

    const replyId = await result.current.createReply('comment1' as any, 'Reply content');

    expect(mockCreateReply).toHaveBeenCalledWith({
      commentId: 'comment1',
      content: 'Reply content',
    });
    expect(replyId).toBe('reply123');
  });

  it('should resolve comment', async () => {
    const mockResolveComment = vi.fn().mockResolvedValue(true);
    mockUseMutation.mockReturnValue(mockResolveComment);

    const { result } = renderHook(() =>
      useComments({
        documentId: mockDocumentId,
        editor: mockEditor,
        isReadOnly: false,
      })
    );

    const success = await result.current.resolveComment('comment1' as any, true);

    expect(mockResolveComment).toHaveBeenCalledWith({
      commentId: 'comment1',
      isResolved: true,
    });
    expect(success).toBe(true);
  });

  it('should not resolve comment in read-only mode', async () => {
    const { result } = renderHook(() =>
      useComments({
        documentId: mockDocumentId,
        editor: mockEditor,
        isReadOnly: true,
      })
    );

    const success = await result.current.resolveComment('comment1' as any, true);

    expect(success).toBe(false);
  });

  it('should select comment', () => {
    const { result } = renderHook(() =>
      useComments({
        documentId: mockDocumentId,
        editor: mockEditor,
        isReadOnly: false,
      })
    );

    result.current.selectComment('comment1');

    expect(mockCommentPluginHelpers.selectComment).toHaveBeenCalledWith(
      mockEditor.prosemirrorView,
      'comment1'
    );
    expect(mockCommentPluginHelpers.setSidebar).toHaveBeenCalledWith(
      mockEditor.prosemirrorView,
      true
    );
  });

  it('should toggle sidebar', () => {
    const { result } = renderHook(() =>
      useComments({
        documentId: mockDocumentId,
        editor: mockEditor,
        isReadOnly: false,
      })
    );

    result.current.toggleSidebar();

    expect(mockCommentPluginHelpers.toggleSidebar).toHaveBeenCalledWith(
      mockEditor.prosemirrorView
    );
  });
});
