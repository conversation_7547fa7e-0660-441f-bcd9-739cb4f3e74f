/**
 * React hook for URL-based document routing
 * Manages document selection via URL synchronization
 * Updated for Next.js App Router
 */

import { useState, useEffect, useCallback } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Id } from '../../convex/_generated/dataModel';
import {
  extractDocumentIdFromUrl,
  generateDocumentUrl,
  generateHomeUrl
} from '../lib/routing';

interface UseDocumentRoutingReturn {
  selectedDocumentId: Id<"documents"> | null;
  navigateToDocument: (documentId: Id<"documents">) => void;
  navigateToHome: () => void;
  isLoading: boolean;
}

/**
 * Hook for managing document routing via Next.js App Router
 */
export function useDocumentRouting(): UseDocumentRoutingReturn {
  const router = useRouter();
  const pathname = usePathname();
  const [selectedDocumentId, setSelectedDocumentId] = useState<Id<"documents"> | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Extract document ID from current pathname
  useEffect(() => {
    const documentId = extractDocumentIdFromUrl(pathname);

    if (documentId) {
      setSelectedDocumentId(documentId as Id<"documents">);
    } else {
      setSelectedDocumentId(null);
    }

    setIsLoading(false);
  }, [pathname]);

  // Navigate to a specific document
  const navigateToDocument = useCallback((documentId: Id<"documents">) => {
    const url = generateDocumentUrl(documentId);
    router.push(url);
  }, [router]);

  // Navigate to home (no document selected)
  const navigateToHome = useCallback(() => {
    const url = generateHomeUrl();
    router.push(url);
  }, [router]);

  return {
    selectedDocumentId,
    navigateToDocument,
    navigateToHome,
    isLoading
  };
}

/**
 * Hook for document URL generation
 * Provides utilities for creating document links
 */
export function useDocumentUrls() {
  const getDocumentUrl = useCallback((documentId: Id<"documents">) => {
    return generateDocumentUrl(documentId);
  }, []);

  const getHomeUrl = useCallback(() => {
    return generateHomeUrl();
  }, []);

  return {
    getDocumentUrl,
    getHomeUrl
  };
}
