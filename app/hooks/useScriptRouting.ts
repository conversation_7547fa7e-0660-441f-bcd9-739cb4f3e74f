/**
 * React hook for URL-based script and document routing
 * Manages script and document selection via URL synchronization
 * Updated for Next.js App Router
 */

import { useState, useEffect, useCallback } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Id } from '../../convex/_generated/dataModel';
import {
  extractIdsFromPath,
  generateScriptUrl,
  generateScriptDocumentUrl,
  generateDocumentUrl,
  generateHomeUrl
} from '../lib/routing';

interface UseScriptRoutingReturn {
  selectedScriptId: Id<"scripts"> | null;
  selectedDocumentId: Id<"documents"> | null;
  navigateToScript: (scriptId: Id<"scripts">) => void;
  navigateToScriptDocument: (scriptId: Id<"scripts">, documentId: Id<"documents">) => void;
  navigateToDocument: (documentId: Id<"documents">) => void;
  navigateToHome: () => void;
  isLoading: boolean;
}

/**
 * Hook for managing script routing via Next.js App Router
 */
export function useScriptRouting(): UseScriptRoutingReturn {
  const router = useRouter();
  const pathname = usePathname();
  const [selectedScriptId, setSelectedScriptId] = useState<Id<"scripts"> | null>(null);
  const [selectedDocumentId, setSelectedDocumentId] = useState<Id<"documents"> | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Extract IDs from current pathname
  useEffect(() => {
    const { scriptId, documentId } = extractIdsFromPath(pathname);

    if (scriptId) {
      setSelectedScriptId(scriptId as Id<"scripts">);
    }

    if (documentId) {
      setSelectedDocumentId(documentId as Id<"documents">);
    }

    setIsLoading(false);
  }, [pathname]);

  // Navigate to a specific script
  const navigateToScript = useCallback((scriptId: Id<"scripts">) => {
    const url = generateScriptUrl(scriptId);
    router.push(url);
  }, [router]);

  // Navigate to a document within a script
  const navigateToScriptDocument = useCallback((scriptId: Id<"scripts">, documentId: Id<"documents">) => {
    const url = generateScriptDocumentUrl(scriptId, documentId);
    router.push(url);
  }, [router]);

  // Navigate to a standalone document (legacy)
  const navigateToDocument = useCallback((documentId: Id<"documents">) => {
    const url = generateDocumentUrl(documentId);
    router.push(url);
  }, [router]);

  // Navigate to home
  const navigateToHome = useCallback(() => {
    const url = generateHomeUrl();
    router.push(url);
  }, [router]);

  return {
    selectedScriptId,
    selectedDocumentId,
    navigateToScript,
    navigateToScriptDocument,
    navigateToDocument,
    navigateToHome,
    isLoading
  };
}



/**
 * Hook for URL generation utilities
 * Provides utilities for creating script and document links
 */
export function useScriptUrls() {
  const getScriptUrl = useCallback((scriptId: Id<"scripts">) => {
    return generateScriptUrl(scriptId);
  }, []);

  const getScriptDocumentUrl = useCallback((scriptId: Id<"scripts">, documentId: Id<"documents">) => {
    return generateScriptDocumentUrl(scriptId, documentId);
  }, []);

  const getDocumentUrl = useCallback((documentId: Id<"documents">) => {
    return generateDocumentUrl(documentId);
  }, []);

  const getHomeUrl = useCallback(() => {
    return generateHomeUrl();
  }, []);

  return {
    getScriptUrl,
    getScriptDocumentUrl,
    getDocumentUrl,
    getHomeUrl
  };
}
