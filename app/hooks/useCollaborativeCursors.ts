'use client';

/**
 * React hook for managing collaborative cursors and selections
 */

import { useEffect, useRef, useCallback } from "react";
import { useQuery, useMutation } from "convex/react";
import usePresence from "@convex-dev/presence/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import {
  updateCursors,
  getCurrentCursorData,
  type CollaboratorCursor,
  type CursorData
} from "../lib/collaborativeCursors";

interface UseCollaborativeCursorsProps {
  documentId: Id<"documents">;
  editor: any; // BlockNote/ProseMirror editor instance
  isReadOnly?: boolean;
}

export function useCollaborativeCursors({
  documentId,
  editor,
  isReadOnly = false
}: UseCollaborativeCursorsProps) {
  const userId = useQuery(api.presence.getUserId);
  const updateCursorMutation = useMutation(api.presence.updateCursor);

  // Use presence system for the document room (for general presence)
  const roomId = `document-${documentId}`;
  const presenceState = usePresence(api.presence, roomId, userId || "");

  // Get cursor data directly from the cursors table
  const cursors = useQuery(api.presence.getCursors, { documentId });
  
  const lastCursorUpdate = useRef<number>(0);
  const updateThrottle = useRef<NodeJS.Timeout | null>(null);
  const lastCursorData = useRef<CursorData | null>(null);
  const pendingUpdate = useRef<boolean>(false);

  // Improved throttled cursor update function with debouncing and deduplication
  const throttledUpdateCursor = useCallback((cursorData: CursorData) => {
    if (isReadOnly || !userId) return;

    // Check if cursor data actually changed to prevent unnecessary updates
    const lastData = lastCursorData.current;
    if (lastData &&
        lastData.position === cursorData.position &&
        lastData.selection?.from === cursorData.selection?.from &&
        lastData.selection?.to === cursorData.selection?.to) {
      return; // No change, skip update
    }

    lastCursorData.current = cursorData;

    const now = Date.now();
    const timeSinceLastUpdate = now - lastCursorUpdate.current;

    // Clear any pending timeout
    if (updateThrottle.current) {
      clearTimeout(updateThrottle.current);
      updateThrottle.current = null;
    }

    // If enough time has passed and no update is pending, send immediately
    if (timeSinceLastUpdate >= 150 && !pendingUpdate.current) {
      pendingUpdate.current = true;
      updateCursorMutation({
        documentId,
        cursorData,
      }).catch((error) => {
        console.warn('Failed to update cursor position:', error);
      }).finally(() => {
        pendingUpdate.current = false;
        lastCursorUpdate.current = Date.now();
      });
      return;
    }

    // Otherwise, schedule a debounced update
    updateThrottle.current = setTimeout(() => {
      if (!pendingUpdate.current && lastCursorData.current) {
        pendingUpdate.current = true;
        updateCursorMutation({
          documentId,
          cursorData: lastCursorData.current,
        }).catch((error) => {
          console.warn('Failed to update cursor position (debounced):', error);
        }).finally(() => {
          pendingUpdate.current = false;
          lastCursorUpdate.current = Date.now();
        });
      }
      updateThrottle.current = null;
    }, 150); // Increased to 150ms for better stability
  }, [documentId, updateCursorMutation, isReadOnly, userId]);

  // Set up editor event listeners
  useEffect(() => {
    if (!editor || isReadOnly) return;

    const handleSelectionChange = () => {
      if (!editor.prosemirrorView) return;
      
      const cursorData = getCurrentCursorData(editor.prosemirrorView.state);
      throttledUpdateCursor(cursorData);
    };

    const handleTransaction = () => {
      handleSelectionChange();
    };

    // Listen to editor changes
    if (editor.prosemirrorView) {
      const view = editor.prosemirrorView;
      
      // Add transaction listener
      const originalDispatch = view.dispatch;
      view.dispatch = (tr: any) => {
        originalDispatch.call(view, tr);
        handleTransaction();
      };

      // Listen to focus/blur events
      view.dom.addEventListener("focus", handleSelectionChange);
      view.dom.addEventListener("blur", handleSelectionChange);

      return () => {
        view.dom.removeEventListener("focus", handleSelectionChange);
        view.dom.removeEventListener("blur", handleSelectionChange);
        view.dispatch = originalDispatch;
      };
    }
  }, [editor, throttledUpdateCursor, isReadOnly]);

  // Update cursor decorations when cursors change
  useEffect(() => {
    if (!editor?.prosemirrorView || !cursors) return;

    // Filter out current user's cursor
    const otherCursors = cursors.filter((cursor: CollaboratorCursor) => 
      cursor.userId !== userId
    );

    updateCursors(editor.prosemirrorView, otherCursors);
  }, [editor, cursors, userId]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (updateThrottle.current) {
        clearTimeout(updateThrottle.current);
      }
    };
  }, []);

  return {
    cursors: cursors || [],
    isConnected: !!presenceState?.roomToken,
    collaboratorCount: cursors?.length || 0,
    presenceUsers: presenceState || [],
  };
}
