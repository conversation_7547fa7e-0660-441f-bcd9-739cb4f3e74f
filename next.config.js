/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    // Enable experimental features if needed
  },
  
  // Configure webpack for Convex and ProseMirror compatibility
  webpack: (config, { isServer }) => {
    // Handle Convex client-side modules
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };

      // Ensure crypto polyfill is loaded first on client-side
      config.entry = async () => {
        const entries = await config.entry();
        if (entries['main.js'] && !entries['main.js'].includes('./app/lib/crypto-polyfill.ts')) {
          entries['main.js'].unshift('./app/lib/crypto-polyfill.ts');
        }
        return entries;
      };
    }

    // Add polyfill for crypto.randomUUID on server-side
    if (isServer) {
      config.resolve.alias = {
        ...config.resolve.alias,
      };

      // Inject crypto polyfill for server-side rendering
      config.plugins = config.plugins || [];
      config.plugins.push(
        new config.webpack.DefinePlugin({
          'global.crypto': JSON.stringify({}),
        })
      );
    }

    return config;
  },

  // Environment variables
  env: {
    CONVEX_URL: process.env.CONVEX_URL,
    NEXT_PUBLIC_CONVEX_URL: process.env.NEXT_PUBLIC_CONVEX_URL,
  },

  // Transpile packages that need it
  transpilePackages: [
    '@convex-dev/auth',
    '@convex-dev/presence',
    '@convex-dev/prosemirror-sync',
  ],

  // Image optimization
  images: {
    domains: [],
  },

  // Redirects for backward compatibility
  async redirects() {
    return [
      // Add any necessary redirects here
    ];
  },

  // Headers for security and performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
